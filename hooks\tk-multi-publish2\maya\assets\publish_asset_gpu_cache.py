# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

import os
import sys
import sgtk
import maya.cmds as cmds
import maya.mel as mel


HookBaseClass = sgtk.get_hook_baseclass()


class MayaAssetGpuCachePublishPlugin(HookBaseClass):
    """
    Plugin for publish Scene playblast Publish

    """

    @property
    def icon(self):
        """
        Path to an png icon on disk
        """

        # look for icon one level up from this hook's folder in "icons" folder
        return os.path.join(
            self.disk_location,
            os.pardir,
            os.pardir,
            "icons",
            "publish_alembic.png"
        )

    @property
    def name(self):
        """
        One line display name describing the plugin
        """
        return "Publish GPU Cache"

    @property
    def description(self):
        """
        Verbose, multi-line description of what the plugin does. This can
        contain simple html for formatting.
        """

        loader_url = "https://support.shotgunsoftware.com/hc/en-us/articles/219033078"

        return """
        <p>This plugin publish an alembic with dynamic attributes baked on every frame.</p>

        """

    @property
    def settings(self):
        """
        Dictionary defining the settings that this plugin expects to recieve
        through the settings parameter in the accept, validate, publish and
        finalize methods.

        A dictionary on the following form::

            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }

        The type string should be one of the data types that toolkit accepts
        as part of its environment configuration.
        """
        # inherit the settings from the base publish plugin
        plugin_settings = super(MayaAssetGpuCachePublishPlugin, self).settings or {}

        # settings specific to this class
        maya_publish_settings = {
            "Work Template": {
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "Primary Publish Template": {
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "Publish Template": {
                "type": "template",
                "default": None,
                "description": "Template path for published Scene playblast. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "Publish type": {
                "type": "string",
                "default": "Animation Data",
                "description": "The published file type to register.",
            },
            "Allowed asset types": {
                "type": "list",
                "default": [],
                "description": "Allowed asset type to export dyn attrs alembic.",
            },
            "Allowed steps": {
                "type": "list",
                "default": [],
                "description": "Allowed asset steps for the plugin.",
            },
            "Allowed tasks": {
                "type": "list",
                "default": [],
                "description": "Allowed asset tasks for the plugin.",
            },
            "Allowed groups": {
                "type": "list",
                "default": [],
                "description": "The valid secondary groups.",
            },
        }

        # update the base settings
        plugin_settings.update(maya_publish_settings)

        return plugin_settings

    @property
    def item_filters(self):
        """
        List of item types that this plugin is interested in.

        Only items matching entries in this list will be presented to the
        accept() method. Strings can contain glob patters such as *, for example
        ["maya.*", "file.maya"]
        """

        return ["maya.session.asset.gpu_cache"]

    def accept(self, settings, item):

        step_name = self.parent.context.step['name'].lower()
        task_name = self.parent.context.task['name'].lower()

        allowed_steps = settings['Allowed steps'].value
        allowed_tasks = settings['Allowed tasks'].value

        if step_name in allowed_steps and task_name in allowed_tasks:
            allowed_types = settings['Allowed asset types'].value
            current_asset = self.parent.engine.shotgun.find_one(
                "Asset",
                [["id", "is", self.parent.context.entity["id"]]],
                ["sg_asset_type"])
            current_asset_type = current_asset["sg_asset_type"].lower()
            valid_asset_type = current_asset_type in allowed_types
            if valid_asset_type:
                if current_asset_type == "prop" or current_asset_type == "envprop":
                    return {"accepted": True, "checked": False}
                else:
                    return {"accepted": True, "checked": True}

        return {"accepted": False, "checked": True}


    def get_root_node(self, settings, ref_obj=None):
        """
        From a reference object collects the top root node name.
        returns: A string corresponding to the top root node name.
        """
        model = None

        model_high_root_list = cmds.ls("model_high_root", long=True)

        for model in model_high_root_list:
            if cmds.referenceQuery(model, isNodeReferenced=True):
                continue
            if not cmds.listRelatives(model, parent=True):
                return model

        # ----------------------------------------------------------------------
        # Unnecessary chunk because when we publish asset gpucaches, we don't
        # check if references to exist in scene. At least no at root level. We only
        # search for the |model_high_root node.

        # if ref_obj is None:
        #     print("return last PARENT")
        #     return lastParent

        # ref_nodes = cmds.referenceQuery(ref_obj, nodes=True, dagPath=True)
        # transform_list = cmds.ls(ref_nodes, type="transform", long=True)

        # # get main parent of current reference's transform
        # if len(transform_list) == 0:
        #     return None

        # lastParent = transform_list[0]

        # while True:
        #     newParent = cmds.listRelatives(
        #         lastParent, parent=True, fullPath=True)
        #     if newParent == None:
        #         break
        #     lastParent = newParent[0]
        # ----------------------------------------------------------------------

        return model

    def read_hierarchy_gen(self, obj_name):
        """
        Reads the hierarchy for a node and yields the name of the group,
        is a recursive generator for each children node contained.
        """
        yield obj_name
        secondary_groups = cmds.listRelatives(obj_name, children=True, fullPath=True)
        if secondary_groups:
            for sec_grp in secondary_groups:
                yield sec_grp
                if cmds.listRelatives(sec_grp, children=True, fullPath=True):
                    for val in self.read_hierarchy_gen(sec_grp):
                        yield val

    def validate_groups(self, node, allowed):

        for val in self.read_hierarchy_gen(node):
            # Get the last name group and remove namespace
            secondary_node_name = val.split('|')[-1]
            if str(secondary_node_name) in allowed:
                return val


    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish. Returns a
        boolean to indicate validity.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        :returns: True if item is valid and not in proxy mode, False otherwise.
        """

        # []  #
        allowed_grps = settings["Allowed groups"].value
        allowed_groups_found = None

        root_object = self.get_root_node(settings)
        # Validate secondary groups to have at least any of the valid groups within
        if root_object:
            allowed_groups_found = self.validate_groups(root_object, allowed_grps)
        if not allowed_groups_found:
            raise Exception("Root allowed group name is missing, \
                Valid secondary groups are: {}.".format(allowed_grps))
        item.properties['root_object'] = root_object
        item.properties['export_group'] = allowed_groups_found

        return True

    def get_root_item(self, item):
        """ A handy recursive function to get an item's root parent
            This is mostly useful because the root is the only one
            where we can store extra data
        """
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root

    def export_gpu_cache(self, export_items, gpu_publish_path, export_in_frame, export_out_frame):

        dir_path = os.path.dirname(gpu_publish_path)
        gpu_file_name, file_extension = os.path.splitext(os.path.basename(gpu_publish_path))
        cmds.gpuCache(
            export_items,
            startTime=export_in_frame,
            endTime=export_out_frame,
            fileName=gpu_file_name,
            directory=dir_path,
            optimize=True,
            writeMaterials=True,
            dataFormat="ogawa")

    def register_gpu_publishes(self, asset_name, item, fields, sg_type, publish_path, prim_pub_path):

        root_item = self.get_root_item(item)
        root_item.properties.setdefault('sg_publish_extra_data', [])
        publish_extra_data = root_item.properties['sg_publish_extra_data']

        gpu_dependencies = [prim_pub_path]
        self.parent.log_info("Publish gpu cache for:  {}".format(asset_name))

        # and a publish name
        publish_name = self.parent.util.get_publish_name(publish_path)

        # Register the publish of the gpu cache File:
        args = {
            "tk": self.parent.engine.sgtk,
            "context": item.context,
            "comment": item.description,
            "path": publish_path,
            "name": publish_name,
            "version_number": fields["version"],
            "thumbnail_path": item.get_thumbnail_as_path(),
            "task": self.parent.engine.context.task,
            "dependency_paths": gpu_dependencies,
            "published_file_type": sg_type.replace("{step_code}", fields['step_code'])
        }
        sg_publish = sgtk.util.register_publish(**args)

        publish_extra_data.append(sg_publish)

        self.parent.log_info("Published file registered.")

        return sg_publish

    def publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        engine = self.parent.engine

        # Settings information
        publish_type = settings["Publish type"].value
        work_template_name = settings["Work Template"].value
        gpu_pub_template_name = settings["Publish Template"].value
        scene_pub_template_name = settings["Primary Publish Template"].value

        # Template references
        work_template = engine.get_template_by_name(work_template_name)
        gpu_pub_template = engine.get_template_by_name(gpu_pub_template_name)
        scene_pub_template = engine.get_template_by_name(scene_pub_template_name)

        # Path building
        scene_path = cmds.file(q=True, sn=True)
        fields = work_template.get_fields(scene_path)
        primary_publish_path = scene_pub_template.apply_fields(fields)
        gpu_published_path = gpu_pub_template.apply_fields(fields)

        # Other attributes required
        start_value = 1
        top_value = 1
        export_group = item.properties['export_group']
        root_object = item.properties['root_object']

        self.export_gpu_cache(
            export_group, gpu_published_path, start_value, top_value
        )

        self.register_gpu_publishes(
            root_object,
            item,
            fields,
            publish_type,
            gpu_published_path,
            primary_publish_path
        )

        return []

    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        pass
