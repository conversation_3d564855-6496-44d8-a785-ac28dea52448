########################################################################################
#
# Copyright (c) 2024 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
########################################################################################

"""
An app that replaces all proxy backgrounds with non-proxy backgrounds versions. It
doesn't really replace the proxies, but adds the hires versions trying to mantain the
current backgrounds transformations so they are visually identical. It inserts a
composite node between the existing backgrounds output connections.

"""

from tank import Hook
from tank import TankError
from tank.platform import Application
from tank.platform.qt import QtCore, QtGui
import tank
import sgtk

import os
import re
import json
import time
import pprint
import tempfile


pp = pprint.pprint
pf = pprint.pformat


QFont = QtGui.QFont
QColor = QtGui.QColor
QDialog = QtGui.QDialog
QHeaderView = QtGui.QHeaderView
QPushButton = QtGui.QPushButton
QVBoxLayout = QtGui.QVBoxLayout
QTableWidget = QtGui.QTableWidget
QApplication = QtGui.QApplication
QTableWidgetItem = QtGui.QTableWidgetItem


META_SHOTGUN_PATH = "meta.shotgun.path"


class ProcessItemsHook(Hook):
    def execute(self, **kwargs):
        self.parent.engine.logger.info(
            "execute action replace_proxies_with_hires start. {}".format("-" * 80)
        )

        # Ensure the scene is saved
        self.parent.engine.app.save_project()

        node_type_list = ["READ"]
        proxies_dict = self.collect_nodes_of_type(node_type_list) or {}

        if proxies_dict:
            # using the published proxy paths, the proxy and hires templates, resolve
            # the published hires paths. We are not yet checking if the paths are valid,
            # just resolving them. The check happens in the find_hires_publishes_in_SG
            # method
            proxies_dict = self.resolve_hires_publish_paths(proxies_dict)

        if not proxies_dict:
            error_msg = "Couldn't find any proxy files in the current scene."
            self.show_message(error_msg, icon="Warning")
            return {"succes": [], "messages": [error_msg], "errors": [1]}

        # find hires publishes in SG witht he resolved hires paths
        hires_publishes = self.find_hires_publishes_in_SG(proxies_dict)
        if not hires_publishes:
            error_msg = "Couldn't find any related hires publishes in SG"
            self.show_message(error_msg, icon="Warning")
            return {"succes": [], "messages": [error_msg], "errors": [1]}

        # Using the proxy and hires paths, find a corelation and add the relevant
        # information to the proxies_dict
        proxies_dict = self.relate_hires_to_proxies(proxies_dict, hires_publishes)
        self.parent.engine.logger.info("proxies_dict:\n{}".format(pf(proxies_dict)))

        selected_nodes = self.create_replace_dialog(proxies_dict)
        if not selected_nodes:
            error_msg = "Nothing was selected, nothing to replace..."
            self.show_message(error_msg, icon="Warning")
            return {"succes": [], "messages": [error_msg], "errors": [1]}
        self.parent.engine.logger.info(
            "selected_nodes:\n{}".format(pf(selected_nodes))
        )

        proxies_dict = self.check_if_hires_should_be_imported(
            proxies_dict, selected_nodes
        )

        # Download the hires published files
        self.parent.engine.logger.info("Downloading files...")
        self.download_hires_published_files(proxies_dict)

        # Import the hires published files and create all needed connections and
        # offsets
        proxies_dict = self.import_and_connect_hires_to_proxies(proxies_dict)

        self.parent.engine.logger.info("proxies_dict:\n{}".format(pf(proxies_dict)))


        # # Ensure the scene is saved
        # self.parent.engine.app.save_project()

        self.parent.engine.logger.info(
            "execute action replace_proxies_with_hires end. {}".format("-" * 80)
        )

        msg = "Successfully imported hires files for nodes:\n\n{}\n".format(
            pf(selected_nodes)
        )
        self.show_message(msg, "Information")

        return {"succes": [1], "messages": [msg], "errors": []}

    def collect_nodes_of_type(self, node_type_list):
        """
        Collects all nodes of types included in the node_type_list
        """

        result = None

        file_path = self.create_tmp_json_file("proxy_files")
        self.parent.logger.info("file_path: {}".format(file_path))

        collect_nodes_cmd = """
include("harmony_utility_functions.js");

function get_nodes_list(node_type_list) {
    var nodes_list = node.getNodes(node_type_list);
    return nodes_list;
}

function filter_nodes_by_attr_dict(nodes_list, attr_name) {
    var array_of_filtered_nodes = {};
    for (var i = 0; i < nodes_list.length; ++i) {
        log("----------------------------");
        var node_path = nodes_list[i];
        log("checking node: " + node_path);
        var attr = node.getAttr(node_path, 1.0, attr_name);

        var path = attr.textValue();
        if (path.length > 0) {
            log("path: " + path);
            var basename = path.split("/").pop();
            if (basename.indexOf("_proxy_") !== -1) {
                if (!("Images" in array_of_filtered_nodes)) {
                    array_of_filtered_nodes["Images"] = {};
                }

                var version_match = basename.match(/_v(\d{3})/);
                var version = version_match ? version_match[1] : null;

                array_of_filtered_nodes["Images"][node_path] = {
                    "file_name": basename,
                    "version": version,
                    "published_path": path,
                    "hires_found": false
                };
            }
        }
    }
    return array_of_filtered_nodes
}

var node_type_list = %s;
// META_SHOTGUN_PATH comes from the harmoyn_utility_functions.js
var attr = META_SHOTGUN_PATH;
var file_path = "%s"

var nodes_list = get_all_nodes_by_type(node_type_list);
// MessageLog.trace(pp(nodes_list, null, 4));

var array_of_filtered_nodes = filter_nodes_by_attr_dict(nodes_list, attr);

file = new File(file_path);
file.open(2);
file.write(JSON.stringify(array_of_filtered_nodes, null, 4));
file.close();

""" % (
            node_type_list,
            file_path,
        )
        self.parent.engine.logger.debug(collect_nodes_cmd)
        # asset_versions_dict = self.parent.engine.app.execute(collect_nodes_cmd)
        self.parent.engine.app.execute(collect_nodes_cmd)

        asset_versions_dict = {}
        counter = 0
        while not asset_versions_dict and counter < 20:
            time.sleep(3)
            counter += 1
            self.parent.engine.logger.info("sleep round: {}".format(counter))
            try:
                with open(file_path, "r") as json_file:
                    asset_versions_dict = json.load(json_file)
            except:
                self.parent.engine.logger.info(
                    "file not ready yet: {}".format(file_path)
                )

        if not asset_versions_dict:
            self.parent.engine.logger.error("Couldn't get asset_versions_dict from file.")
            return result

        else:
            return asset_versions_dict

    def add_version_token_to_dict(self, asset_versions_dict):
        pattern = re.compile(r"(?P<head>.+)(?P<version>v\d{3})(?P<tail>.*)")
        for category in asset_versions_dict.keys():
            for node, node_dict in asset_versions_dict[category].items():
                file_name = node_dict["file_name"]
                pattern_match = re.match(pattern, file_name)
                if pattern_match:
                    version = pattern_match.groupdict()["version"]
                    asset_versions_dict[category][node]["version"] = version

        return asset_versions_dict

    def resolve_hires_publish_paths(self, proxies_dict):
        images_dict = proxies_dict.get("Images", {})

        publish_asset_ps_layer_group_proxy_template = self.parent.engine.get_template_by_name(
            "photoshop_asset_layergroup_proxy_publish"
        )
        publish_asset_ps_layer_group_template = self.parent.engine.get_template_by_name(
            "photoshop_asset_layergroup_publish"
        )

        for node in images_dict.keys():
            file_name = images_dict[node].get("file_name", "")
            proxy_path = images_dict[node].get("published_path", "")

            self.parent.logger.info("working with file: {}".format(file_name))

            proxy_fields = publish_asset_ps_layer_group_proxy_template.get_fields(
                proxy_path
            )
            self.parent.logger.info("proxy_fields:\n{}".format(pf(proxy_fields)))

            hires_published_path = publish_asset_ps_layer_group_template.apply_fields(
                proxy_fields
            )

            if not hires_published_path:
                self.parent.logger.info("No hires path found!")
                continue

            hires_published_path = self.fix_path(hires_published_path)
            self.parent.logger.info(
                "hires_published_path: {}".format(hires_published_path)
            )

            images_dict[node]["hires_published_path"] = hires_published_path

        return proxies_dict

    def find_hires_publishes_in_SG(self, proxies_dict):
        images_dict = proxies_dict.get("Images", {})

        hires_published_paths = [
            images_dict[node_path].get("hires_published_path")
            for node_path in images_dict.keys()
        ]

        tk = self.parent.tank
        fields = ["code", "path"]

        hires_publishes = sgtk.util.find_publish(
            tk, hires_published_paths, fields=fields
        )

        return hires_publishes

    def relate_hires_to_proxies(self, proxies_dict, hires_publishes):
        images_dict = proxies_dict.get("Images", {})

        for node_path in images_dict.keys():
            node_dict = images_dict[node_path]
            hires_published_path = node_dict.get("hires_published_path", "")

            if not hires_published_path in hires_publishes:
                node_dict["import_file"] = False
                continue

            node_dict["hires_found"] = True
            node_dict["publish_entity"] = {
                "type": hires_publishes.get(hires_published_path, {}).get("type"),
                "id": hires_publishes.get(hires_published_path, {}).get("id"),
                "code": hires_publishes.get(hires_published_path, {}).get("code"),
            }
            node_dict["import_file"] = False

        return proxies_dict

    def check_if_hires_should_be_imported(self, proxies_dict, selected_nodes):
        images_dict = proxies_dict.get("Images", {})

        for node in selected_nodes:
            node_dict = images_dict[node]

            if not node_dict["hires_found"]:
                node_dict["import_file"] = False
                continue

            node_dict["import_file"] = True

        return proxies_dict

    def create_tmp_json_file(self, basename):
        """
        Creates a tmp file based on the input basename, in the current project folder,
        inside a tmpfiles folder and returns the full file path
        """

        result = None

        # xstage_path = self._session_path()
        tmp_dir = tempfile.mkdtemp()
        tmpfiles_folder = os.path.join(tmp_dir, "tmpfiles")

        if not os.path.exists(tmpfiles_folder):
            try:
                os.makedirs(tmpfiles_folder)
            except:
                error_msg = "Couldn't create tmpfiles folder: {}".format(
                    tmpfiles_folder
                )
                raise Exception(error_msg)

        json_filename = "{}.json".format(basename)
        json_fullpath = os.path.join(tmpfiles_folder, json_filename)
        json_fullpath = json_fullpath.replace("\\", "/")

        try:
            f = open(json_fullpath, "w+")
            f.close()
        except:
            error_msg = "Couldn't create tmpfile '{}''.".format(json_fullpath)
            raise Exception(error_msg)
            # pass

        if os.path.exists(json_fullpath):
            return json_fullpath
        else:
            return result

    def download_hires_published_files(self, proxies_dict):
        images_dict = proxies_dict.get("Images", {})

        # load metasync framework and init transfers manager
        metasync = (
            self.parent.engine.custom_frameworks.get("mty-framework-metasync")
            or
            self.load_framework("mty-framework-metasync")
        )

        # load the transfersManager from metasync framework
        transfersManager = metasync.transfersManager

        # iterate over images_dict and find the publish_entities for each existing entry
        # of the selected_publishes (nodes where import_file is set to True).
        # Try to download them using the transfersManager
        for node in images_dict:
            import_file = images_dict[node].get("import_file", False)
            if not import_file:
                continue

            publish_entity = images_dict[node].get("publish_entity")
            publish_path = images_dict[node].get("hires_published_path")

            self.parent.engine.logger.info(f"{publish_entity = }")
            self.parent.engine.logger.info(f"{publish_path = }")

            if os.path.exists(publish_path):
                continue

            counter = 0
            while counter < 5:
                try:
                    self.parent.logger.info(
                        "downloading file: {}, try: {}".format(publish_path, counter +1)
                    )
                    transfersManager.ensure_file_is_local(
                        publish_path, publish_entity, show_busy=True,
                        report_progress=False,
                    )
                    break
                except:
                    counter += 1

    def import_and_connect_hires_to_proxies(self, proxies_dict):
        images_dict = proxies_dict.get("Images", {})

        failed_to_import = []

        # first add scale multiplier for each hires image dict
        for node in images_dict:
            node_dict = images_dict[node]
            hires_published_path = node_dict.get("hires_published_path", "")
            hires_found = node_dict.get("hires_found", False)
            import_file = node_dict.get("import_file", False)

            if not hires_found or not import_file:
                continue

            # use loader harmony actions to import the files as project resolution
            image_scale_multiplier = self.parent.execute_hook_expression(
                "{config}/tk-multi-loader2/tk-harmony_actions.py",
                '_get_image_scale_multiplier',
                image_path=hires_published_path
            )

            node_dict["image_scale_multiplier"] = image_scale_multiplier

        # Create the command
        import_hires_cmd = """
include("harmony_utility_functions.js");
include("loader_import_images.js");


var proxies_dict = %s;

function add_hires_scale_offset_peg(hires_node_path, proxy_node_path) {
    var peg_name = "scale_offset_PEG";
    var offset_peg_pos_x = node.coordX(hires_node_path);
    var offset_peg_pos_y = node.coordY(hires_node_path);
    var offset_peg_width = node.width(hires_node_path);

    var hires_offset_peg_node = node.add(
        node.parentNode(hires_node_path),
        peg_name,
        "PEG",
        offset_peg_pos_x + (offset_peg_width / 2),
        offset_peg_pos_y - 25,
        0
    );

    // center the offset peg
    var peg_node_pos_x = node.coordX(hires_offset_peg_node);
    var peg_node_pos_y = node.coordY(hires_offset_peg_node);
    var peg_node_width = node.width(hires_offset_peg_node);
    node.setCoord(hires_offset_peg_node, peg_node_pos_x - (peg_node_width / 2), peg_node_pos_y);

    // connect the offset peg to the hires node
    node.link(
        hires_offset_peg_node,
        0,
        hires_node_path,
        0
    );

    // get the scale attributes from the proxy and hires nodes
    var attr = node.getAttr(proxy_node_path, frame.current(), "scale.x");
    var proxy_scale_x = attr.doubleValue();
    attr = node.getAttr(proxy_node_path, frame.current(), "scale.y");
    var proxy_scale_y = attr.doubleValue();
    attr = node.getAttr(hires_node_path, frame.current(), "scale.x");
    var hires_scale_x = attr.doubleValue();
    attr = node.getAttr(hires_node_path, frame.current(), "scale.y");
    var hires_scale_y = attr.doubleValue();

    // set the offset scale values in the offset peg
    attr = node.getAttr(hires_offset_peg_node, frame.current(), "scale.x");
    attr.setValue(proxy_scale_x / hires_scale_x);
    attr = node.getAttr(hires_offset_peg_node, frame.current(), "scale.y");
    attr.setValue(proxy_scale_y / hires_scale_y);

    return hires_offset_peg_node;
}


for (var current_proxy_node in proxies_dict.Images) {
    var current_proxy_dict = proxies_dict.Images[current_proxy_node];

    // import hires image as project resolution
    var image_scale_multiplier = current_proxy_dict["image_scale_multiplier"];
    var hires_published_path = current_proxy_dict["hires_published_path"];
    var import_file = current_proxy_dict["import_file"];

    // skip file if it doesn't need to be imported (not selected in the dialog)
    if (!import_file) {
        continue;
    };

    var hires_node_path = custom_import_image(
      hires_published_path, "Top", true, "As Is", null, null, image_scale_multiplier
    );
    current_proxy_dict["hires_node_path"] = hires_node_path

    // move hires image to correct position
    var proxy_good_position = get_node_good_position(current_proxy_node, 0, 0);

    // move hires_node to correct position
    var hires_width = node.width(hires_node_path);
    var hires_x_pos = proxy_good_position.x - hires_width - 10;
    var hires_y_pos = proxy_good_position.y;
    node.setCoord(hires_node_path, hires_x_pos, hires_y_pos);

    // create a new composite node and insert it between the current proxy node and the
    // its existing connections
    var node_name = "hires_COMPOSITE";

    var composite_node = node.add(
        node.parentNode(current_proxy_node),
        node_name,
        "COMPOSITE",
        0,
        0,
        0
    );
    current_proxy_dict["new_composite_node"] = composite_node

    // once the node has been created, move it to a good position and insert it between
    // the existing output connections of the proxy node
    insert_node_below(current_proxy_node, composite_node, 0, 30);

    // finally connect the hires node to this new composite node in the last input port
    // left most of the composite node
    node.link(
        hires_node_path,
        0,
        composite_node,
        node.numberOfInputPorts(composite_node)
    );

    // match the visual scale of the proxy and hires images by adding a PEG to the
    // hires node and offset it's scale based on the scale of the proxy node
    var hires_offset_peg_node = add_hires_scale_offset_peg(hires_node_path, current_proxy_node);


    // if the current proxy node has a peg connected to its input port, also connect
    // that peg to the hires node
    if (node.isLinked(current_proxy_node, 0)) {
        var parent_node_path = node.srcNode(current_proxy_node, 0);
        if (node.type(parent_node_path) == "PEG") {

            node.link(parent_node_path, 0, hires_offset_peg_node, 0);
        }
    }
}

""" % proxies_dict

        # Execute the command
        self.parent.engine.app.execute(import_hires_cmd)

        return proxies_dict

    def fix_path(self, path):
        """Replace all backward slashes with forward slashes."""

        path = path.replace("\\\\", "/")
        path = path.replace("\\", "/")

        return path

    def create_replace_dialog(self, proxies_dict):
        # Create the dialog
        dialog = QtGui.QDialog()
        dialog.setWindowTitle("Select Proxies to Replace")

        # Main layout for the dialog
        main_layout = QtGui.QVBoxLayout(dialog)

        # Add warning label
        warning_label = QtGui.QLabel(
            (
            "Warning: If an item is disabled, it's because a hires version of the "
            "file couldn't be found in SG."
            )
        )
        warning_label.setWordWrap(True)  # Enable wrapping for long text

        # Set font size and bold style for the warning label
        font = warning_label.font()
        font.setPointSize(10)  # Increase font size
        font.setBold(True)  # Set font to bold
        warning_label.setFont(font)

        main_layout.addWidget(warning_label)

        # Add a separator line
        separator = QtGui.QFrame()
        separator.setFrameShape(QtGui.QFrame.HLine)
        separator.setFrameShadow(QtGui.QFrame.Sunken)
        main_layout.addWidget(separator)

        # Group box for checkboxes
        group_box = QtGui.QGroupBox("Available replacements:")
        group_layout = QtGui.QVBoxLayout(group_box)

        # Dictionary to store checkboxes with associated file names
        self.checkboxes = {}

        # Process each file path entry in the "Images" dictionary
        images_dict = proxies_dict.get("Images", {})
        for path, file_data in images_dict.items():
            # Create a checkbox for each file, labeled with "file_name"
            file_name = file_data.get("file_name", "Unknown")
            hires_found = file_data.get("hires_found", False)

            checkbox = QtGui.QCheckBox(file_name)
            checkbox.setChecked(hires_found)
            checkbox.setEnabled(hires_found)

            # Store checkbox with its path as the key for easy access
            self.checkboxes[path] = checkbox
            group_layout.addWidget(checkbox)

        # Add the group box to the main layout
        main_layout.addWidget(group_box)

        # Create buttons for Replace and Cancel
        button_layout = QtGui.QHBoxLayout()
        replace_button = QtGui.QPushButton("Replace")
        cancel_button = QtGui.QPushButton("Cancel")

        # Connect buttons to dialog actions
        replace_button.clicked.connect(lambda: dialog.done(QtGui.QDialog.Accepted))
        cancel_button.clicked.connect(lambda: dialog.done(QtGui.QDialog.Rejected))

        button_layout.addWidget(replace_button)
        button_layout.addWidget(cancel_button)
        main_layout.addLayout(button_layout)

        # Execute the dialog
        result = dialog.exec_()

        # Process the dialog result
        if result == QtGui.QDialog.Accepted:
            # Collect all checked and enabled checkboxes' paths
            selected_items = [
                path for path, checkbox in self.checkboxes.items()
                if checkbox.isChecked() and checkbox.isEnabled()
            ]
            return selected_items
        else:
            return None


    def show_message(self, msg, icon=None):
        """
        Shows a message box with the input message
        """

        icons_dict = {
            "NoIcon": QtGui.QMessageBox.NoIcon,
            "Question": QtGui.QMessageBox.Question,
            "Information": QtGui.QMessageBox.Information,
            "Warning": QtGui.QMessageBox.Warning,
            "Critical": QtGui.QMessageBox.Critical,
        }

        if not icon or icon not in icons_dict.keys():
            icon_selection = icons_dict["NoIcon"]
        else:
            icon_selection = icons_dict.get(icon, QtGui.QMessageBox.Information)

        msg_error = QtGui.QMessageBox()
        msg_error.setWindowTitle("Finished execution.")
        msg_error.setText(msg)
        msg_error.setIcon(icon_selection)
        msg_error.exec_()
