#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

# import mimetypes
import os
import re
import json
import sgtk
import pprint


HookBaseClass = sgtk.get_hook_baseclass()


class LayerGroupsProxiesCollector(HookBaseClass):
    """ """

    @property
    def settings(self):
        # grab any base class settings
        collector_settings = super(LayerGroupsProxiesCollector, self).settings or {}

        photoshop_settings = {
            "Primary Publish Template": {
                "type": "template",
                "default": None,
                "description": "A work file template required by this collector.",
            },
            "Work Template": {
                "type": "template",
                "default": None,
                "description": "A work file template required by this collector.",
            },
            "Publish Layer Group Image Template": {
                "type": "template",
                "default": None,
                "description": "A publish file template required by layergroups_image_item.",
            },
            "Publish Layer Group Proxy Image Template": {
                "type": "template",
                "default": None,
                "description": "A work file template required by layergroups_proxy_image_item.",
            },
            "Publish Image Review Template": {
                "type": "template",
                "default": None,
                "description": "A publish file template required by layergroups_image_item.",
            },
            "Publish Video Template": {
                "type": "template",
                "default": None,
                "description": "A work file template required by layergroups_proxy_image_item.",
            },
            "Proxy Image File Type": {
                "type": "string",
                "default": None,
                "description": "Published file type for group layer proxy images",
            },
            "Image Sequence Review Type": {
                "type": "string",
                "default": None,
                "description": "Published file type for group layer images",
            },
            "Video File Type": {
                "type": "string",
                "default": None,
                "description": "Published file type for group layer video (media review)",
            },
            "Publish Photoshop Proxy Template": {
                "type": "template",
                "default": None,
                "description": "A publish file template required by ps_proxy_image_item.",
            },
        }

        # update the base settings with these settings
        collector_settings.update(photoshop_settings)

        return collector_settings

    def process_current_session(self, settings, parent_item):
        """
        Analyzes the current scene open in a DCC and parents a subtree of items
        under the parent_item passed in.

        :param dict settings: Configured settings for this collector
        :param parent_item: Root item instance
        """

        # let the parent collector do its own job
        super(LayerGroupsProxiesCollector, self).process_current_session(settings, parent_item)


        self._collect_layergroups_proxies(parent_item, settings)

    def _collect_layergroups_proxies(self, item, settings):
        """
        Creates just one item for all the output layer group in the document root.
        Only layer groups which start with [Oo]utput[_- ] are taken in account.

        """

        engine = self.parent.engine
        photoshop = engine.adobe

        # ------------------------------------------------------------------------------
        # Get templates and file types from settings
        primary_publish_template = settings.get("Primary Publish Template").value
        primary_work_template = settings.get("Work Template").value
        layer_group_image_seq_pub_template = settings.get(
            "Publish Image Review Template"
        ).value
        layer_group_image_pub_template = settings.get(
            "Publish Layer Group Image Template"
        ).value
        layer_group_proxy_image_pub_template = settings.get(
            "Publish Layer Group Proxy Image Template"
        ).value
        video_pub_template = settings.get("Publish Video Template").value
        layer_group_proxy_image_file_type = settings.get("Proxy Image File Type").value
        layer_group_image_seq_file_type = settings.get(
            "Image Sequence Review Type"
        ).value
        layer_group_video_file_type = settings.get("Video File Type").value
        ps_proxy_image_pub_template = settings.get(
            "Publish Photoshop Proxy Template"
        ).value

        # ------------------------------------------------------------------------------

        # Load value overrides framework
        valueoverrides = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        ) or self.load_framework("mty-framework-valueoverrides")

        secondary_outputs_on_local = False
        if valueoverrides:
            value_code = (
                "mty.engine.photoshop.multi-publish.process_secondary_outputs_on_local"
            )
            value_secondary_outputs_on_local = valueoverrides.get_value(value_code)
            self.parent.logger.info(
                "photoshop.multi-publish.process_secondary_outputs_on_local: {}".format(
                value_secondary_outputs_on_local
                )
            )
            dict_secondary_outputs_on_local = json.loads(value_secondary_outputs_on_local)
            secondary_outputs_on_local = dict_secondary_outputs_on_local.get(
                "photoshop.layergroupsproxies", {}
            ).get(
                "value", True
            )


        self.parent.logger.info(
            "secondary_outputs_on_local [photoshop.layergroupsproxies]: {}".format(secondary_outputs_on_local)
        )

        PS_document = photoshop.app.activeDocument
        PS_document_path = PS_document.fullName.fsName or None


        # ------------------------------------------------------------------------------
        root_item = self.get_root_item(item)
        sorted_layer_group_data = root_item.properties.get("all_output_layer_groups", None)
        all_layers_visibility_state = root_item.properties.get(
            "all_layers_visibility_state", None
        )

        if sorted_layer_group_data and all_layers_visibility_state:
            layergroups_image_item = item.create_item(
                "photoshop.layergroupsproxies",
                "Photoshop Layer Group Proxy Images",
                "Layer Group Proxy Images",
            )

            # get the icon path to display for this item
            icon_path = os.path.join(
                self.disk_location, os.pardir, "icons", "image_publish.png"
            )
            layergroups_image_item.set_icon_from_path(icon_path)
            # layergroups_image_item.properties["layer_group_data"] = {}
        else:
            return


        layergroups_image_item.properties["layer_group_data"] = sorted_layer_group_data
        layergroups_image_item.properties["all_layers_visibility_state"] = all_layers_visibility_state

        # Create the dictionary that will be used in all sibling hooks. This
        # contains all of the relevant templates and file types, as well as
        # basic date of the PS document.
        if not layergroups_image_item.properties.get(
            "templates_and_file_types"
        ):
            layergroups_image_item.properties["templates_and_file_types"] = {
                "primary_publish_template_name": primary_publish_template,
                "primary_publish_template": engine.get_template_by_name(
                    primary_publish_template
                ),
                "primary_work_template_name": primary_work_template,
                "primary_work_template": engine.get_template_by_name(
                    primary_work_template
                ),
                "image_publish_template_name": layer_group_image_pub_template,
                "image_publish_template": engine.get_template_by_name(
                    layer_group_image_pub_template
                ),
                "proxy_image_publish_template_name": layer_group_proxy_image_pub_template,
                "proxy_image_publish_template": engine.get_template_by_name(
                    layer_group_proxy_image_pub_template
                ),
                "proxy_image_file_type": layer_group_proxy_image_file_type,
                "image_seq_publish_template_name": layer_group_image_seq_pub_template,
                "image_seq_publish_template": engine.get_template_by_name(
                    layer_group_image_seq_pub_template
                ),
                "image_seq_file_type": layer_group_image_seq_file_type,
                "video_publish_template_name": video_pub_template,
                "video_publish_template": engine.get_template_by_name(
                    video_pub_template
                ),
                "video_file_type": layer_group_video_file_type,
                "PS_document_path": PS_document_path,
                "PS_document": PS_document,
                "publish_photoshop_proxy_template_name": ps_proxy_image_pub_template,
                "publish_photoshop_proxy_template": engine.get_template_by_name(
                    ps_proxy_image_pub_template
                ),
            }

        if not secondary_outputs_on_local:
            layergroups_image_item.properties["output_to_farm"] = True
            # root_item = self.get_root_item(item)
            second_outputs_to_farm = root_item.properties.get(
                "secondary_outputs_to_farm", {}
            )

            dit_to_primary ={
                "photoshop.layergroupsproxies":{
                    "layer_group_data" : sorted_layer_group_data,
                    "templates_and_file_types": {
                        "primary_publish_template_name": primary_publish_template,
                        "primary_work_template_name": primary_work_template,
                        "image_publish_template_name": layer_group_image_pub_template,
                        "proxy_image_publish_template_name": layer_group_proxy_image_pub_template,
                        "proxy_image_file_type": layer_group_proxy_image_file_type,
                        "image_seq_publish_template_name": layer_group_image_seq_pub_template,
                        "image_seq_file_type": layer_group_image_seq_file_type,
                        "video_publish_template_name": video_pub_template,
                        "video_file_type": layer_group_video_file_type,
                        "publish_photoshop_proxy_template_name": ps_proxy_image_pub_template,
                    }
                }
            }
            second_outputs_to_farm.update(dit_to_primary)

            root_item.properties["secondary_outputs_to_farm"] = second_outputs_to_farm

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root
