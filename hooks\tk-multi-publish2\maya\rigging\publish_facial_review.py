# -*- coding: utf-8 -*-
# Standard library:
import os
import json
import subprocess

import sgtk
import maya.cmds as cmds
import pymel.core as pm
import maya.mel as mel
import pymel.core as pymel
from sgtk.platform.qt import QtCore

HookBakeClass= sgtk.get_hook_baseclass()

class FacialReviewPublishPlugin(HookBakeClass):
    def __init__(self,parent):
        super(FacialReviewPublishPlugin, self).__init__(parent)
        engine = self.parent.engine

        # Apps . . . . . . . . . . . . . . . . . . . . . .
        #app_playblast = engine.apps.get("mty-maya-playblast")
        #print "App:", app_playblast

        #module_playblast_tools = app_playblast.import_module("playblast_tools")
        #self.playblast_ops = module_playblast_tools.playblast_operations(app_playblast)

        # frameworks   . . . . . . . . . . . . . . . . . . . . . .
        self.coremayatools = self.load_framework("mty-framework-coremayatools")
        self.mayacapture = self.load_framework("mty-framework-mayacapture")
        self.ffmpeg = self.load_framework("mty-framework-ffmpeg")

    @property
    def icon(self):
        return os.path.join(
        self.disk_location,
        os.pardir,
        "icons",
        "review.png"
        )

    @property
    def name(self):
        return "Facial Media Review"


    @property
    def description(self):
        return """
        <p>This plugin publish a playblast 2 views from the current facial Rig.</p>

        """

    @property
    def item_filters(self):
        return ["maya.session.facial.review"]



    @property
    def settings(self):
        plugin_settings= super(
             FacialReviewPublishPlugin, self).settings or {}

        # Settings specific to this class
        review_publish_settings={
        "Work Template":{
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                               "correspond to a template defined in "
                               "templates.yml."
                               },

        "Primary Publish Template": {
                "type": "template",
                "default": None,
                "description": "Maya publish session"
                },

        "Asset Review Work Template":{
                "type": "template",
                "default": None,
                "description":"Template path where the image sequence will be storaged"
                },

        "Asset Review Publish Template":{
                "type": "template",
                "default": None,
                "description": ""

                },

        "Head joint name":{
                "type": "list",
                "default": None,
                "description": ""

                }
        }
        # update the base settings
        plugin_settings.update(review_publish_settings)

        return plugin_settings


    def accept(self, settings, item):
        """
        Method called by the publisher to determine if an item is of any
        interest to this plugin. Only items matching the filters defined via the
        item_filters property will be presented to this method.

        A submit for review task will be generated for each item accepted here. Returns a
        dictionary with the following booleans:

            - accepted: Indicates if the plugin is interested in this value at
                all. Required.
            - enabled: If True, the plugin will be enabled in the UI, otherwise
                it will be disabled. Optional, True by default.
            - visible: If True, the plugin will be visible in the UI, otherwise
                it will be hidden. Optional, True by default.
            - checked: If True, the plugin will be checked in the UI, otherwise
                it will be unchecked. Optional, True by default.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process

        :returns: dictionary with boolean keys accepted, required and enabled.
            This plugin makes use of the tk-multi-reviewsubmission app; if this
            app is not available then the item will not be accepted, this method
            will return a dictionary with the accepted field that has a value of
            False.

            Several properties on the item must also be present for it to be
            accepted. The properties are 'path', 'publish_name', 'color_space',
            'first_frame' and 'last_frame'
        """
        publisher = self.parent
        engine    = publisher.engine
        sg        = engine.shotgun
        context   = engine.context

        entity    = context.entity
        step      = context.step["name"]
        task      = context.task["name"]


        asset= sg.find_one("Asset",[["code", "is", entity["name"]]], ["sg_asset_type"])


        if step.lower() == "rig" and task.lower() == "fullrig" and asset["sg_asset_type"]== "Character":
            return {"accepted": True,
                    "chcecked": True}

        else:
            return {"accepted": False }

    def validate(self, settings, item):

        ctx       = self.parent.context
        publisher = self.parent
        path= cmds.file(q=True, sn=True)

        #---- ensure session has been saved
        if not path:
            # the session still requires saving. provide a save button.
            # validation fails.
            error_msg = "The Maya session has not been saved."
            raise Exception(error_msg)

        #Validate Head JNT
        head_joint_ls  = settings.get("Head joint name").value
        child_jnt      = False
        jnt_exist      = False

        joints_ls      = cmds.ls(type= "joint")

        for jnt in head_joint_ls :
            if cmds.objExists(jnt):
                jnt_exist      = True
                head_jnt= jnt
                children_ls=cmds.listRelatives(head_jnt, c=1)

                if children_ls:
                    for c in children_ls:
                        if cmds.nodeType(c) == "joint" :
                            child_jnt= True

                if not child_jnt:
                    error_msg = "{0} should have at least one child joint".format(head_jnt )
                    raise Exception(error_msg)
                    return False

        if not jnt_exist :
            error_msg = "You need name the head joint, like one of the following options {0}".format(head_joint_ls )
            raise Exception(error_msg)
            return False

        return True



    def publish(self, settings, item):

        # Create the review videos
        #Get the publisg path form settings template
        engine           = sgtk.platform.current_engine()
        publisher        = self.parent
        template_by_name = engine.get_template_by_name

        work_path= cmds.file(q=1,sn=1)

        # template paths
        work_template_setting                  = settings.get("Work Template").value
        publish_template_settings              = settings.get("Primary Publish Template").value
        work_asset_review_template_setting     = settings.get("Asset Review Work Template").value
        publish_asset_review_template_setting  = settings.get("Asset Review Publish Template").value
        head_joint_ls                          = settings.get("Head joint name").value


        # templates
        work_template                 = template_by_name(work_template_setting)
        publish_template              = template_by_name(publish_template_settings )
        work_asset_review_template    = template_by_name(work_asset_review_template_setting)
        publish_asset_review_template = template_by_name(publish_asset_review_template_setting)


        for jnt in  head_joint_ls :
            if cmds.objExists(jnt):
                head_joint_name= jnt
                break

        #fields
        work_fields= work_template.get_fields(work_path)

        #Facial Review Paths
        work_facial_review_path    = work_asset_review_template.apply_fields(work_fields)
        publish_session_path       = publish_template.apply_fields(work_fields)
        publish_facial_review_path = publish_asset_review_template.apply_fields(work_fields)

        publish_name = os.path.basename(publish_facial_review_path)

        #Create playablast
        start_frame, end_frame           = self.create_facial_animation()
        real_name                        = work_fields["name"]
        work_path_directory              = os.path.dirname(work_facial_review_path)

        camera, camera_grp= self.create_camera(head_joint_name)

        #Camera metadata
        camera_matrix_data= {}

        cam_matrix= cmds.xform('facial_camera1', matrix =1, q=1 , ws=1)
        camera_matrix_data["front_camera"]= cam_matrix


        tmp_path= os.path.join( work_path_directory,"front_camera")
        fornt_images_path = self.create_playblast(camera[0], start_frame, end_frame, tmp_path, publish_facial_review_path)
        fornt_images_path = os.path.join( fornt_images_path,"front_camera") + ".%4d.jpg"
        fornt_images_path = fornt_images_path.replace("\\" , "//")
        print(fornt_images_path)

        #Perspective_position
        cmds.setAttr("{0}.rotateY".format(camera_grp), 20)

        cam_matrix= cmds.xform("facial_camera1", matrix =1, q=1 , ws=1)
        camera_matrix_data['perspective_camera']= cam_matrix

        tmp_path= os.path.join( work_path_directory,"perespective_camera")
        perspective_images_path = self.create_playblast(camera[0], start_frame, end_frame, tmp_path, publish_facial_review_path)
        perspective_images_path = os.path.join(perspective_images_path,"perespective_camera") + ".%4d.jpg"
        perspective_images_path = perspective_images_path.replace("\\" , "//")
        print(perspective_images_path)


        camera_matrix_json= json.dumps(camera_matrix_data)


        #ffmpeg
        #create list
        txt_path= os.path.join(work_path_directory, "list_.txt").replace("\\" , "//")
        files_= [fornt_images_path, perspective_images_path ]

        with open (txt_path, "w") as f:
            for file_ in files_:
                f.write("file "+ file_ +'\n')

        ffmpeg_cmd=["-f",
                    "concat",
                    "-safe",
                    "0",
                    "-i",
                    str(txt_path),
                    "-y",
                    str(publish_facial_review_path)]
        print(ffmpeg_cmd)

        _err, _info = self.ffmpeg.ffmpegCore.execute_command(ffmpeg_cmd)

        if _err:
            raise Exception(
                "Failed to convert playblast to video: %s" % (_info))



        # Publish video process
        args= {
                "tk":                  self.parent.engine.sgtk,
                "context":             item.context,
                "comment":             item.description,
                "path":                publish_facial_review_path,
                "name":                publish_name,
                "version_number":      work_fields['version'],
                "thumbnail_path":      item.get_thumbnail_as_path(),
                "task":                self.parent.engine.context.task,
                "dependency_paths":    [publish_session_path],
                "sg_fields":           {"sg_status_list": "rev"},
                "published_file_type": "Media Review"

        }
        sg_publishes = sgtk.util.register_publish(**args)
        sg_task= self.parent.engine.context.task
        comment= item.description

        if os.access(publish_facial_review_path,os.F_OK):
            store_on_disk= True
        else:
            store_on_disk= False

        work_status = 'rev'
        self.parent.log_debug(
            "Adding existing publish "+
            "(id:{0})".format(sg_publishes['id'])+
            "as extra data for item: {0}".format(item)
        )

        item.properties.sg_publish_data = sg_publishes
        item.properties.sg_publish_path = sg_publishes["path"]

        #Adding metadata
        data={"sg_metadata": camera_matrix_json}
        self.parent.engine.shotgun.update('PublishedFile',sg_publishes['id'] , data)

        print(sg_publishes)
        sg_version= self.submit_version(publish_facial_review_path, [sg_publishes], sg_task,
                                        comment, store_on_disk, start_frame, end_frame, work_status)


        event_loop = QtCore.QEventLoop()
        thread = UploaderThread(
            self.parent, sg_version,
            publish_facial_review_path,
            item.get_thumbnail_as_path(),
            True
        )

        thread.finished.connect(event_loop.quit)
        thread.start()
        event_loop.exec_()

        # Update task status for revision
        sg_task = self.parent.context.task
        self.parent.log_info(
            "End of Publish and updating task with id: " + str(
                sg_task['id']) + " to status: " + str('rev'))
        self.parent.engine.shotgun.update("Task", sg_task["id"],
                                          {"sg_status_list": 'rev'})

        # log any errors generated in the thread
        for e in thread.get_errors():
            self.parent.log_error(e)


    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        engine           = sgtk.platform.current_engine()
        publisher        = self.parent
        template_by_name = engine.get_template_by_name
        work_path        = cmds.file(q=1,sn=1)


        # template paths
        work_template_setting                  = settings.get("Work Template").value
        publish_template_settings              = settings.get("Primary Publish Template").value
        work_asset_review_template_setting     = settings.get("Asset Review Work Template").value
        publish_asset_review_template_setting  = settings.get("Asset Review Publish Template").value
        head_joint_ls                          = settings.get("Head joint name").value


        # templates
        work_template                 = template_by_name(work_template_setting)
        publish_template              = template_by_name(publish_template_settings )
        work_asset_review_template    = template_by_name(work_asset_review_template_setting)
        publish_asset_review_template = template_by_name(publish_asset_review_template_setting)


        #fields
        work_fields= work_template.get_fields(work_path)

        #Facial Review Paths
        work_facial_review_path          = work_asset_review_template.apply_fields(work_fields)
        work_path_directory              = os.path.dirname(work_facial_review_path)


        image_files=os.listdir(work_path_directory)
        for file in image_files:
            file_path=os.path.join(work_path_directory,file)
            os.remove(file_path)

        self.logger.debug('Item video successfully published')

        self.clean_animation()
        self.clean_camera_nodes()
        cmds.file(s=1)


    def create_playblast(self, camera, start_frame, last_frame, work_path, publish_path):
        mayacapture = self.mayacapture
        # General playblast data
        engine = sgtk.platform.current_engine()

        playblast_folder = os.path.dirname(work_path)
        if not os.path.exists(playblast_folder):
            os.makedirs(playblast_folder)

        playblast_filename, _ = os.path.splitext(publish_path)

        movie_folder = os.path.dirname(publish_path)
        if not os.access(movie_folder, os.F_OK):
            os.makedirs(movie_folder, 0777)

        view_opts = mayacapture.ViewportOptions.copy()
        view_opts['grid'] = False
        view_opts['polymeshes'] = True
        view_opts['displayAppearance'] = "smoothShaded"
        view_opts['nurbsCurves'] = False
        view_opts['locators'] = False
        view_opts['joints'] = False
        view_opts['pluginShapes'] = True
        view_opts['pluginObjects'] = ("gpuCacheDisplayFilter", True)
        view_opts['ikHandles'] = False
        view_opts['textures'] = True
        view_opts['textures'] = True
        # Enable viewport2 AmbientOclusion
        view2_opts = mayacapture.Viewport2Options.copy()
        view2_opts['ssaoEnable'] = True
        # camera
        cam_opts = mayacapture.CameraOptions.copy()
        cam_opts['displayResolution'] = True

        # Enable Color Management
        color_management_enabled = cmds.colorManagementPrefs(q=True, cmEnabled=True)
        if color_management_enabled is False:
            cmds.colorManagementPrefs(e=True, cmEnabled=True)

        mayacapture.capture(camera=camera,
                            width=1920,
                            height=1080,
                            filename=work_path,
                            frames=None,
                            format="image",
                            compression="jpg",
                            # format='avi',
                            # compression='none',
                            viewer=False,
                            overwrite=True,
                            viewport_options=view_opts,
                            viewport2_options=view2_opts,
                            camera_options=cam_opts,
                            sound=None)

        return playblast_folder

    def create_facial_animation(self):
        """ This function creates the animation keyframes
            and returns the star and end frame value """

        start_frame = 1
        end_frame   = 0
        frame       = start_frame
        trfm_ls     = cmds.ls(type='transform')

        cmds.currentTime( start_frame, edit=True )
        self.clean_animation()

        facial_controls = []
        eyeBrows_ls     = []
        eyes_ls         = []
        pupils_ls       = []
        extra_ls        = []

        for tn in trfm_ls :
            if cmds.attributeQuery('ANI', node=tn, exists=True) and "Ex" not in tn:
                if  "EyeBrow" in tn or "Brow" in tn:
                    eyeBrows_ls.append(tn)
                elif "Eye" in tn:
                    eyes_ls.append(tn)
                elif "Pupil" in tn:
                    pupils_ls.append(tn)
                else:
                    extra_ls.append(tn)

        facial_controls= [eyeBrows_ls, eyes_ls, pupils_ls, extra_ls]

        for ctrl_grp in facial_controls:
            if ctrl_grp != facial_controls[-1]:

                for ctrl in ctrl_grp:
                    frame= start_frame
                    maxValue = cmds.attributeQuery('ANI', node=ctrl, max=True)[0]
                    minValue = cmds.attributeQuery('ANI', node=ctrl, min=True)[0]

                    for val in range(int(minValue),int(maxValue)+1):
                        frame +=1
                        cmds.setKeyframe(ctrl , v=val, at='ANI', t=[frame])
                        end_frame= frame

            else:
                for ctrl in ctrl_grp:
                    maxValue = cmds.attributeQuery('ANI', node=ctrl, max=True)[0]
                    minValue = cmds.attributeQuery('ANI', node=ctrl, min=True)[0]

                    for val in range(int(minValue),int(maxValue)+1):
                        frame +=1
                        cmds.setKeyframe(ctrl , v=val, at='ANI', t=[frame])
                        end_frame= frame

            start_frame= end_frame

            cmds.playbackOptions(min=1)
            cmds.playbackOptions(max=end_frame)


        print(start_frame, end_frame)


        return [1, end_frame]


    def clean_animation(self):
        valid_curve_types= ["UL", "UA", "UT", "UU"]
        animCurves= pm.ls(type='animCurve')
        animCurves=[crv for crv in  animCurves  if  crv.animCurveType() not in valid_curve_types]
        pm.currentTime(1)

        for crv in animCurves:
            pm.delete(crv)



    def create_camera(self, joint_name):
        #Create Camera
        import maya.cmds as cmds
        joints_ls=cmds.ls(typ= "joint")

        for jnt in joints_ls:
           if joint_name == jnt:
                head_jnt = jnt
                children_jnt= [ch for ch in cmds.listRelatives(head_jnt, c=1) if cmds.nodeType(ch) == "joint" ]

                locator = cmds.spaceLocator(n= "facial_camera_loc")

                cmds.select(head_jnt)

                for ch in children_jnt:
                    cmds.select(ch, add=1)

                cmds.select(locator,add=1)

                point_constraint=cmds.pointConstraint(n= "facial_camwe_pc", mo=0)
                cmds.delete(point_constraint)

                cmds.select(cl=1)

                facial_camera_grp= cmds.group(n= "facial_camera_grp", em=1, w=1)
                facial_camera= cmds.camera(n= "facial_camera")
                cmds.parent(facial_camera[0], facial_camera_grp)

                pc=cmds.pointConstraint(locator,facial_camera_grp, mo=0  )
                cmds.delete(pc)


        #Front_position
        cmds.setAttr("{0}.translateZ".format(facial_camera[0]), 10)

        return facial_camera, facial_camera_grp

        #Perspective_position
        cmds.setAttr("{0}.rotateY".format(facial_camera_grp), 20)

    def clean_camera_nodes(self):
        camera_nodes= [node  for node in cmds.ls(type= "transform") if "facial_camera" in node]
        for n in camera_nodes:
            cmds.delete(n)

    def submit_version(self, path_to_movie, sg_publishes,
                       sg_task, comment, store_on_disk, first_frame, last_frame,
                       work_status= 'rev', override_entity=False):
        """
        Create a version in Shotgun for this path and linked to this publish.
        """

        # get current shotgun user
        current_user = self.parent.engine.context.user

        # create a name for the version based on the file name
        # grab the file name, strip off extension
        name = os.path.splitext(os.path.basename(path_to_movie))[0]
        # do some replacements
        name = name.replace("_", " ")
        # and capitalize
        name = name.capitalize()


        if override_entity != False:
            entity = override_entity
        else:
            entity = self.parent.engine.context.entity

        # Create the version in Shotgun
        data = {
            "code": name,
            "sg_status_list": work_status,
            "entity": entity,
            "sg_task": sg_task,
            "sg_first_frame": first_frame,
            "sg_last_frame": last_frame,
            "frame_count": (last_frame - first_frame + 1),
            "frame_range": "%s-%s" % (first_frame, last_frame),
            "sg_frames_have_slate": False,
            "published_files": sg_publishes,
            "created_by": current_user,
            "description": comment,
            "sg_movie_has_slate": False,
            "project": self.parent.engine.context.project,
            "user": current_user
        }

        if store_on_disk:
            data["sg_path_to_movie"] = path_to_movie

        sg_version = self.parent.engine.shotgun.create("Version", data)
        self.parent.log_debug("Created version in shotgun: %s" % str(data))

        return sg_version

class UploaderThread(QtCore.QThread):
    """
    Simple worker thread that encapsulates uploading to shotgun.
    Broken out of the main loop so that the UI can remain responsive
    even though an upload is happening
    """

    def __init__(self, app, version, path_to_movie, thumbnail_path,
                 upload_to_shotgun):
        QtCore.QThread.__init__(self)
        self._app = app
        self._version = version
        self._path_to_movie = path_to_movie
        self._thumbnail_path = thumbnail_path
        self._upload_to_shotgun = upload_to_shotgun
        self._errors = []

    def get_errors(self):
        """
        can be called after execution to retrieve a list of errors
        """
        return self._errors

    def run(self):
        """
        Thread loop
        """
        upload_error = False

        if self._upload_to_shotgun:
            try:
                self._app.tank.shotgun.upload(
                    "Version", self._version["id"], self._path_to_movie,
                    "sg_uploaded_movie")
            except Exception as e:
                self._errors.append("Movie upload to Shotgun failed: %s" % e)
                upload_error = True

        if not self._upload_to_shotgun or upload_error:
            try:
                self._app.tank.shotgun.upload_thumbnail(
                    "Version", self._version["id"], self._thumbnail_path)
            except Exception as e:
                self._errors.append(
                    "Thumbnail upload to Shotgun failed: %s" % e)





