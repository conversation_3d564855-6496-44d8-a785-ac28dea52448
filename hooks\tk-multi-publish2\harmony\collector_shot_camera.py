#####################################################################################
#
# Copyright (c) 2022 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio.
#####################################################################################

import os
import sys
import time
import pprint
import shutil

import sgtk

HookBaseClass = sgtk.get_hook_baseclass()


pf = pprint.pformat

class HarmonyCameraCollector(HookBaseClass):
    """
    Collector that operates on the Toon Boom Harmony session. Should inherit
    from the basic collector hook.
    """

    @property
    def settings(self):
        """
        Dictionary defining the settings that this collector expects to receive
        through the settings parameter in the process_current_session and
        process_file methods.
        A dictionary on the following form::
        {
            "Settings Name": {
                "type": "settings_type",
                "default": "default_value",
                "description": "One line description of the setting"
        }
        The type string should be one of the data types that toolkit accepts as
        part of its environment configuration.
        """

        # grab any base class settings
        collector_settings = super(HarmonyCameraCollector, self).settings or {}

        # settings specific to this collector
        harmony_session_settings = {
            "Publish Template": {
                "type": "template",
                "default": None,
                "description": "Publish path template",
            },
            "Work Template": {
                "type": "template",
                "default": None,
                "description": "Publish path template",
            },
            "Publish Camera Template": {
                "type": "template",
                "default": None,
                "description": "Template path where the files will be published",
            },
            "camera_attr_name": {
                "type": "string",
                "default": None,
                "description": "Camera attribute name",
            },
            "camera_attr_type": {
                "type": "string",
                "default": None,
                "description": "Camera attribute type",
            },
        }

        # update the base settings with these settings
        collector_settings.update(harmony_session_settings)

        return collector_settings

    def process_current_session(self, settings, parent_item):
        """
        Analyzes the current session open in Toon Boom Harmony and parents a
        subtree of items under the parent_item passed in.
        :param dict settings: Configured settings for this collector
        :param parent_item: Root item instance
        """

        super(HarmonyCameraCollector, self).process_current_session(
            settings, parent_item
        )

        item = next((i for i in parent_item.descendants if i.type_spec.endswith('.session')), None)
        if item is None:
            item = self.collect_current_harmony_session(settings, parent_item)

        camera_item = self._collect_camera(settings, item)
        if not camera_item:
            self.parent.logger.info("No camera item has been created.")

    def _collect_camera(self, settings, parent_item):

        self.logger.debug("Collecting camera...")

        ctx = self.parent.context
        task = ctx.task

        # load overrides framework
        overrides_framework = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides", None
        )
        if not overrides_framework:
            overrides_framework = self.load_framework("mty-framework-valueoverrides")

        default_value_cam_validation = "mty.publisher.harmony.validate_pipeline_camera"
        override_value_cam_validation = overrides_framework.get_value(
            default_value_cam_validation, link=task
        )
        self.parent.logger.info(
            "override value for '{}': {}".format(
                default_value_cam_validation, override_value_cam_validation
            )
        )

        # camera validation ------------------------------------------------------------
        # validate camera depending of there's an override or if we are using the
        # default value True (mty.publisher.harmony.validate_pipeline_camera)
        if default_value_cam_validation:
            validated_camera = False
            found_cameras = []

            shot_camera_nodes_list = self._get_shot_camera_node(settings)

            # Sometimes the cameras list is not correctly retured. The following block uses
            # various retries to ensure the cameras list can be correctly retrieved
            if not shot_camera_nodes_list:
                counter = 0
                while counter < 5 and not shot_camera_nodes_list:
                    time.sleep(1)
                    shot_camera_nodes_list = self._get_shot_camera_node(settings)
                    counter += 1
                    self.parent.engine.logger.info("------------")
                    self.parent.engine.logger.info(
                        "get_shot_camera_node, try: {}".format(counter + 1)
                    )
                    self.parent.engine.logger.info(
                        "Current shot_camera_nodes_list:\n{}".format(
                            pf(shot_camera_nodes_list)
                        )
                    )

            if not shot_camera_nodes_list:
                validated_camera = False
                found_cameras = []
            elif len(shot_camera_nodes_list) != 1:
                validated_camera = False
                found_cameras = shot_camera_nodes_list
            else:
                validated_camera = True
                found_cameras = shot_camera_nodes_list[0]

        # camera collector and export camera -------------------------------------------
        default_value_cam_collector = "mty.publisher.harmony_camera_shot_collector"
        default_value_skip_cam_export = "mty.publisher.harmony.export_harmony_camera"

        override_value_cam_collector = overrides_framework.get_value(
            default_value_cam_collector, link=task
        )
        self.parent.logger.info(
            "override value for '{}': {}".format(
                default_value_cam_collector, override_value_cam_collector
            )
        )

        link_project = {"type": "Project", "id": self.parent.context.project["id"]}
        override_value_cam_export = overrides_framework.get_value(
            default_value_skip_cam_export, link=link_project
        )
        self.parent.logger.info(
            "override value for '{}': {}".format(
                default_value_skip_cam_export, override_value_cam_export
            )
        )

        # skip if both overrides (or default values) are set to False
        if not override_value_cam_collector and not override_value_cam_export:
            self.parent.logger.info(
                (
                    "Override value exist for camera collector and camera export. "
                    "Skipping camera collector"
                )
            )
            return None

        # continue witht the camera collector if the the camera collector override (or
        # default value) is set to True, usually this is set at project level.
        if override_value_cam_collector:
            self.parent.logger.info(
                "Override value doesn't exists. Continuing with camera collector"
            )

        config_path = os.path.dirname(os.path.dirname(self.disk_location))

        engine = self.parent.engine
        # Harmony Work Session path
        current_project_path = engine.app.get_current_project_path()
        publisher = self.parent
        template_by_name = engine.get_template_by_name

        # get templates from settings
        work_template_setting = settings.get("Work Template").value
        publish_session_setting = settings.get("Publish Template").value

        publish_camera_template_setting = settings.get("Publish Camera Template").value

        # Templates
        work_template = template_by_name(work_template_setting)
        publish_session_template = template_by_name(publish_session_setting)
        publish_camera_template = template_by_name(publish_camera_template_setting)

        # Adding extra element in work_fields
        work_fields = work_template.get_fields(current_project_path)
        publish_camera_path = publish_camera_template.apply_fields(work_fields)
        self.parent.logger.info("publish_camera_path: {}".format(publish_camera_path))

        display_name = "Harmony Shot Camera File"
        session_item = parent_item.create_item(
            "harmony.camera", "Collects shot camera in scene", display_name
        )
        self.parent.logger.info("created cam item")

        publish_icons = os.path.join(config_path, "tk-multi-publish2", "icons")
        icon_path = os.path.join(publish_icons, "camera.png")
        session_item.set_icon_from_path(icon_path)

        session_item.properties["work_fields"] = work_fields
        session_item.properties["template_session_publish"] = publish_session_template
        session_item.properties["publish_path"] = publish_camera_path

        # Sub template for every plugin item
        session_item.properties["template_camera_publish"] = publish_camera_template

        # Add validated_camera property to fail the validations in the publish plugin
        # even if the collector is not accepted
        session_item.properties["validated_camera"] = validated_camera
        session_item.properties["found_cameras"] = found_cameras

        session_item.properties["enabled"] = False

        return [session_item]

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root

    def _get_shot_camera_node(self, settings):

        camera_attr_name = settings.get("camera_attr_name").value
        camera_attr_type = settings.get("camera_attr_type").value

        context = self.parent.engine.context

        if context.entity["type"] == "Shot":
            shot_name = context.entity["name"]
            camera_node_name = "{}-Camera".format(shot_name)
        else:
            camera_node_name = None

        self.parent.engine.logger.debug("context:\n{}".format(pprint.pformat(context)))
        self.parent.logger.info("camera_attr_name: {}".format(camera_attr_name))
        self.parent.logger.info("camera_attr_type: {}".format(camera_attr_type))
        self.parent.logger.info("camera_node_name: {}".format(camera_node_name))

        collect_shot_camera_node_list_cmd = """

function collect_shot_camera_node(nodeName)
{
    // Get the camera nodes by attr
    var array_of_node_types = ["CAMERA"];
    var array_of_nodes = node.getNodes(array_of_node_types);

    var array_of_shot_camera_nodes = [];
    for (var i = 0; i < array_of_nodes.length; ++i)
    {
        var node_path = array_of_nodes[i];
        MessageLog.trace("checking node: " + node_path);
        // var attr = node.getAttr(node_path, 1.0, "mtyShotCamera");
        var attr = node.getAttr(node_path, 1.0, "%s");
        // MessageLog.trace(attr.name());
        // MessageLog.trace(attr.typeName());
        // MessageLog.trace(attr.boolValue());
        if (attr != null)
        {
            // if (attr.keyword() != "" && attr.typeName() == "BOOL")
            if (attr.keyword() != "" && attr.typeName() == "%s")
            {
                if (attr.boolValue() == true)
                {
                    if (node.getName(node_path) == nodeName)
                    {
                        MessageLog.trace("Found shot camera node: " + node_path);
                        array_of_shot_camera_nodes.push(node_path);
                    };
                };
            };
        };
    };
    return array_of_shot_camera_nodes;
};

collect_shot_camera_node("%s");
// var shot_camera_nodes = collect_shot_camera_node("s");
// MessageLog.trace(JSON.stringify(shot_camera_nodes));
""" % (
    camera_attr_name,
    camera_attr_type,
    camera_node_name,
)

        self.parent.engine.logger.debug(
            "collect_shot_camera_node_list_cmd:\n{}".format(
                collect_shot_camera_node_list_cmd
            )
        )
        shot_camera_nodes_list = self.parent.engine.app.execute(collect_shot_camera_node_list_cmd)
        self.parent.engine.logger.info(
            "shot_camera_nodes_list: {}".format(shot_camera_nodes_list)
        )

        return shot_camera_nodes_list
