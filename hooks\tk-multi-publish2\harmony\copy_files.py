#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

"""
An app that syncs the frame range between a scene and a shot in Shotgun.
Plus adding and modifiying the scene Render Settings

"""

import sys
import os
import sgtk
from tank import Hook


class CopyFilesHook(Hook):

    def fix_path(self, path):
        # we need to account that this local path can be a folder
        # being a folder means that it can have multiple nested folders and
        # files inside it, which can lead some file paths to be really long
        # and in the case of windows and python 2 there is a limitation
        # in the path length to be 256 characters, the limit is only
        # unlocked properly on python 3 and windows 10 and only if a registry
        # key exist, more information can be found here:
        # https://docs.microsoft.com/en-us/windows/win32/fileio/maximum-file-path-limitation
        # https://docs.microsoft.com/en-us/windows/win32/fileio/naming-a-file#win32-file-namespaces
        # https://stackoverflow.com/a/********/2133436
        # https://stackoverflow.com/a/********/2133436

        new_path = path
        # we took the safest path and use file namespace on such cases
        if sys.platform == 'win32' and sys.version_info.major == 2:
            # make sure that we use the windows file namespace
            # needs to be unicode and properly scaping backslashes
            new_path = u'\\\\?\\%s' % path

        return new_path


    def execute(self, source, destination, **kwargs):
        """
        Copy a file or a folder using sgtk.utils

        :param source:
        :param destination:
        :param kwargs:
        :return:
        """

        source_path = self.fix_path(source)
        destination_path = self.fix_path(destination)

        if os.path.isfile(source):
            sgtk.util.filesystem.copy_file(source_path, destination_path)
        else:
            sgtk.util.filesystem.copy_folder(source_path, destination_path)


        return {'succes': [1], 'messages': [], 'errors': []}

