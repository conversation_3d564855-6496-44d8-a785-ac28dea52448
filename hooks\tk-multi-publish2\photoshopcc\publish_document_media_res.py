# Copyright (c) 2019 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

import os
import sgtk

HookBaseClass = sgtk.get_hook_baseclass()


class PhotoshopCCDocumentPublishPlugin(HookBaseClass):
    """
    Plugin for publishing a photoshop document. It uses as base the engine hook, just
    adds the media resolution to the item properties.

    This hook relies on functionality found in the base file publisher hook in
    the publish2 app and should inherit from it in the configuration. The hook
    setting for this plugin should look something like this::

        hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_document.py"

    """



    @property
    def item_filters(self):
        """
        List of item types that this plugin is interested in.

        Only items matching entries in this list will be presented to the
        accept() method. Strings can contain glob patters such as *, for example
        ["maya.*", "file.maya"]
        """
        return ["photoshop.document"]


    def publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """

        publisher = self.parent
        engine = publisher.engine
        document = item.properties["document"]
        path = _document_path(document)

        # get the path in a normalized state. no trailing separator, separators
        # are appropriate for current os, no double separators, etc.
        path = sgtk.util.ShotgunPath.normalize(path)

        # ensure the document is saved
        engine.save(document)

        # get document dimensions
        doc_width = document.width.value
        doc_height = document.height.value
        if doc_width and doc_height:
            doc_resolution = "{}x{}".format(doc_width, doc_height)
        else:
            doc_resolution = None
        self.parent.logger.info(
            "PS document resolution: {}".format(doc_resolution)
        )
        if doc_resolution:
            # Add resolution to publish publish_fields
            publish_fields = item.properties.get("publish_fields", {})
            publish_fields.update({"sg_media_resolution": doc_resolution})
            item.properties["publish_fields"] = publish_fields

        # update the item with the saved document path
        item.properties["path"] = path

        # let the base class register the publish
        super(PhotoshopCCDocumentPublishPlugin, self).publish(settings, item)


def _document_path(document):
    """
    Returns the path on disk to the supplied document. May be ``None`` if the
    document has not been saved.
    """

    try:
        path = document.fullName.fsName
    except Exception:
        path = None

    return path
