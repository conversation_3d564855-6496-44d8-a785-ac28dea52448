# Copyright (c) 2015 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

import sgtk

HookClass = sgtk.get_hook_baseclass()


class FilterPublishes(HookClass):
    """
    Hook that can be used to filter the list of publishes returned from Shotgun for the current
    Work area
    """

    def execute(self, publishes, **kwargs):
        """
        Main hook entry point

        :param publishes:    List of dictionaries
                             A list of  dictionaries for the current work area within the app.  Each
                             item in the list is a Dictionary of the form:

                             {
                                 "sg_publish" : {Shotgun entity dictionary for a Published File entity}
                             }


        :returns:            The filtered list of dictionaries of the same form as the input 'publishes'
                             list
        """
        app = self.parent

        #metasync = self.load_framework("mty-framework-metasync")
        #self.parent.log_debug("metasync: %s" % metasync)
        #self.parent.log_debug("dir: %s" % dir(metasync))
        #host_location = metasync.locationManager.get_host_location()
        #client_location = metasync.locationManager.get_client_location()
        #publishes = metasync.syncLogsManager.update_fields_on_publishes([publishes]) 
        #local = []
        #remote = []
        # we only want to display the local ones
        #for publish in publishes:
        #    source_location = publish['sg_source_location']
        #    if source_location != host_location:
        #        remote.append(publish)
        #    else:
        #        local.append(publish)
        # and also those that can be downloaded, from the action options
        #downloadable = metasync.syncLogsManager.filter_downloadable_publishes(remote)
        #publishes = local + downloadable

        return publishes
