# Copyright (c) 2015 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

"""
Hook that defines and executes custom actions that can operate on a file (and it's versions).
"""

import os
import re
import sys
import time
import pprint
import shutil
import traceback
from datetime import datetime

import sgtk
from tank.platform.qt import QtCore, QtGui

QApplication = QtGui.QApplication
QLabel = QtGui.QLabel
QHBoxLayout = QtGui.QHBoxLayout
QVBoxLayout = QtGui.QVBoxLayout
QComboBox = QtGui.QComboBox
QPushButton = QtGui.QPushButton
QTextEdit = QtGui.QTextEdit
QGroupBox = QtGui.QGroupBox

pp = pprint.pprint
pf = pprint.pformat


HookBaseClass = sgtk.get_hook_baseclass()


class CustomActions(HookBaseClass):
    """
    Implementation of the CustomActions class
    """

    def generate_actions(
        self, file, work_versions, publish_versions, context, **kwargs
    ):
        """
        Generate a list of actions that should be made available via the right-click context menu on a work file or
        publish in the main file view.

        Each action returned should be a dictionary containing the name (should be unique) and the UI caption to use
        in the menu.

        :param file:                Information about the currently selected file.  This is a dictionary containing
                                    the following fields:

                                    - name          - The Toolkit name for the file (e.g. the value for the name
                                                      template key)
                                    - path          - The path of the file
                                    - version       - The version of the file
                                    - type          - The type of the file - this can be either 'work' for work files
                                                      or 'publish' for publishes.

                                    Additionally, for work files (type is 'work'), the dictionary will also contain:

                                    - modified_at   - The date/time the file was last modified on disk
                                    - modified_by   - A Shotgun user dictionary representing the last person to modify
                                                      the file.
                                    - read_only     - True if the work file is deemed read-only.  This can be
                                                      customised by overriding the filter_work_files hook

                                    For publishes (type is 'publish'), the dictionary will also contain:
                                    - published_at  - The date/time the file was published
                                    - published_by  - A Shotgun user dictionary representing the user that published
                                                      this file
        :param work_versions:       A list of all the work file versions that exist for this file.  Each entry in
                                    the list is a dictionary with the same form as that for the 'file' parameter
        :param publish_versions:    A list of all the publish versions that exist for this file.  Each entry in
                                    the list is a dictionary with the same form as that for the 'file' parameter
        :param context:             The context/work area this file and all it's versions exist in.
        :returns:                   A list of custom actions that are available for this file.  Each action in the
                                    list should be a dictionary containing:

                                    - name      - A unique name to identify the action by.  This will be passed back to
                                                  'execute_action' to identify the action to be executed
                                    - caption   - The caption to use in the UI (e.g. on the Menu) for the action
        """
        # default implementation returns an empty list!
        # return [{"name":"do_something", "caption":"Do Something"}]

        actions = super(CustomActions, self).generate_actions(
            file, work_versions, publish_versions, context, **kwargs
        )

        hook_actions = [
            {
                "name": "save_work_file_change_context",
                "caption": "Save work file from Publish, change Task",
            },
        ]

        actions.extend(hook_actions)

        return actions

    def execute_action(
        self, action, file, work_versions, publish_versions, context, **kwargs
    ):
        """
        Execute the specified action on the specified file/file versions.

        :param action:              The name of the action to execute.  This will be the name of one of the actions
                                    returned by the 'generate_actions' method.
        :param file:                Information about the currently selected file.  This is a dictionary containing
                                    the following fields:

                                    - name          - The Toolkit name for the file (e.g. the value for the name
                                                      template key)
                                    - path          - The path of the file
                                    - version       - The version of the file
                                    - type          - The type of the file - this can be either 'work' for work files
                                                      or 'publish' for publishes.

                                    Additionally, for work files (type is 'work'), the dictionary will also contain:

                                    - modified_at   - The date/time the file was last modified on disk
                                    - modified_by   - A Shotgun user dictionary representing the last person to modify
                                                      the file.
                                    - read_only     - True if the work file is deemed read-only.  This can be
                                                      customised by overriding the filter_work_files hook

                                    For publishes (type is 'publish'), the dictionary will also contain:
                                    - published_at  - The date/time the file was published
                                    - published_by  - A Shotgun user dictionary representing the user that published
                                                      this file
        :param work_versions:       A list of all the work file versions that exist for this file.  Each entry in
                                    the list is a dictionary with the same form as that for the 'file' parameter
        :param publish_versions:    A list of all the publish versions that exist for this file.  Each entry in
                                    the list is a dictionary with the same form as that for the 'file' parameter
        :param context:             The context/work area this file and all it's versions exist in.
        :returns:                   True if the action should close the main UI, otherwise False to keep it open.
        """

        super(CustomActions, self).execute_action(
            action, file, work_versions, publish_versions, context, **kwargs
        )

        if action == "save_work_file_change_context":
            self.save_work_file_change_context(file)

        return False

    def ensure_file_is_local(self, file):
        # method inherited from the hook {config}/tk-multi-workfiles2/custom_actions.py
        return super(CustomActions, self).ensure_file_is_local(file)

    def _collect_sequenced_files(self, sequence_path):
        # method inherited from the hook {config}/tk-multi-workfiles2/custom_actions.py
        return super(CustomActions, self)._collect_sequenced_files(sequence_path)

    def show_message(self, msg, icon=None):
        """
        Displays a message box with a specified message and icon.

        This method creates a QMessageBox to display a message to the user.
        The appearance of the message box can be customized using the 'icon' parameter.

        Args:
            msg (str): The message to be displayed in the message box.
            icon (str, optional): The icon to be displayed in the message box.
                Valid options are "NoIcon", "Question", "Information", "Warning", and "Critical".
                Defaults to "NoIcon" if not provided or invalid.
        """

        from sgtk.platform.qt import QtCore, QtGui

        icons_dict = {
            "NoIcon": QtGui.QMessageBox.NoIcon,
            "Question": QtGui.QMessageBox.Question,
            "Information": QtGui.QMessageBox.Information,
            "Warning": QtGui.QMessageBox.Warning,
            "Critical": QtGui.QMessageBox.Critical,
        }

        if not icon or icon not in icons_dict.keys():
            icon_selection = icons_dict["NoIcon"]
        else:
            icon_selection = icons_dict[icon]

        msg_error = QtGui.QMessageBox()
        msg_error.setWindowTitle("Finished execution.")
        msg_error.setText(msg)
        msg_error.setIcon(icon_selection)
        msg_error.raise_()
        msg_error.activateWindow()
        msg_error.exec_()

    def save_work_file_change_context(self, file_dict):
        """From a given publish, save it in the work area under a different context
        selected by the user using a qt interface, and finally open that file"""

        shotgun = self.parent.engine.shotgun
        context = self.parent.engine.context
        engine = self.parent.engine
        tk = self.parent.tank

        self.parent.engine.logger.info(
            "\n{}\nStarted custom workfiles action: save_work_file_change_context".format(
                "-" * 88
            )
        )

        self.parent.engine.logger.debug("file_dict: {}".format(file_dict))

        metasync = engine.custom_frameworks.get("mty-framework-metasync")

        if not metasync:
            metasync = self.load_framework("mty-framework-metasync")

        # load the transfersManager from metasync framework to have the tar related
        # methods available
        transfersManager = metasync.transfersManager

        def fix_path(path):
            """Replace all backward slashes with forward slashes. Also adds the P: drive if it's
            no pressent"""

            path = path.replace("\\\\", "/")
            path = path.replace("\\", "/")

            return path

        def get_published_file_data_from_sg(published_file_id):
            """From a published file id, get published file relevant data"""

            filters = [
                ["project", "is", context.project],  # type: ignore
                ["id", "is", published_file_id],
            ]
            fields = ["entity", "task.Task.step", "task", "path", "code"]

            result = shotgun.find_one("PublishedFile", filters, fields)  # type: ignore

            return result

        def get_entity_tasks(sg_entity):
            """From a given entity_id, get shot task entities"""

            filters = [
                ["project", "is", context.project],  # type: ignore
                ["entity", "is", sg_entity],
            ]
            fields = [
                "content",
                "sg_status_list",
                "entity",
                "step",
                "step.Step.short_name",
                "sg_sort_order",
            ]

            result = shotgun.find("Task", filters, fields)  # type: ignore

            return result

        def get_sorted_entity_tasks(parent_entity_tasks):
            sorted_entity_tasks = {}
            for task in parent_entity_tasks:
                step_name = task.get("step", {}).get("name", None)
                task_name = task.get("content", None)
                status = task.get("sg_status_list", None)

                # skip error if a task without name or pipeline step is found
                if not step_name or not task_name or (not status or status == "na"):
                    continue

                if step_name not in sorted_entity_tasks.keys():
                    sorted_entity_tasks[step_name] = {task_name: task}
                else:
                    sorted_entity_tasks[step_name].update({task_name: task})

            return sorted_entity_tasks

        def get_task_publishes_from_sg(main_entity, task_entity):
            filters = [
                ["entity", "is", main_entity],
                ["task", "is", task_entity],
            ]

            fields = ["path", "version_number"]

            order = [{"field_name": "version_number", "direction": "desc"}]

            publishes = shotgun.find("PublishedFile", filters, fields, order=order)

            return publishes

        def get_next_available_published_version_number(
            publishes_list, publish_template, published_file_path
        ):
            published_files_list = []
            input_publish_fields = publish_template.get_fields(published_file_path)
            if not input_publish_fields:
                self.parent.engine.logger.error(
                    "Couldn't get published file fields for path: {}".format(
                        published_file_path
                    )
                )
                published_name_field = None
            else:
                self.parent.engine.logger.info(
                    "input_publish_fields:\n{}".format(pf(input_publish_fields))
                )
                published_name_field = selected_keys.get("selected name", None)

            for publish in publishes_list:
                publish_path = publish.get("path", {}).get("local_path", None)
                if publish_path:
                    if publish_template.validate(publish_path):
                        fields_dict = publish_template.get_fields(publish_path)
                        current_publish_name_field = fields_dict.get("name", None)
                        if (
                            published_name_field
                            and published_name_field == current_publish_name_field
                        ):
                            current_publish_version_number = publish.get(
                                "version_number", None
                            )
                            if current_publish_version_number:
                                published_files_list.append(
                                    current_publish_version_number
                                )

            if published_files_list:
                next_available_version = max(published_files_list) + 1
            else:
                next_available_version = 1

            return next_available_version

        def get_next_available_work_version_number(
            entity,
            fields_dict,
            work_template,
            work_template_notask=None,
        ):
            """
            get all existing versions of the existing files in the context workarea
            """
            tk = self.parent.tank
            # tk = sgtk.sgtk_from_entity(entity["type"], entity["id"])
            field_to_search = fields_dict.pop("version")
            try:
                self.parent.engine.logger.debug("fields:{}".format(fields_dict))
                self.parent.engine.logger.debug(
                    "work_template:{}".format(work_template)
                )
                work_files = tk.paths_from_template(work_template, fields_dict)
            except:
                if work_template_notask:
                    work_files = tk.paths_from_template(
                        work_template_notask, fields_dict
                    )
                else:
                    return None

            if not work_files:
                self.parent.engine.logger.debug("cant get a work_files")
                return None

            self.parent.engine.logger.debug("work_files: {}".format(work_files))

            pattern = re.compile(
                r"(?P<head>.*[_\.-])(?P<version>v(?P<version_number>\d{3,}))"
            )
            work_file_versions = []

            name_field = fields_dict.get("name", None)

            # usually workfiles will be a list with one single element, because we are
            # using the fields dict that contain one single version. From there we need
            # to search for existing files on disk
            tmp_work_file = work_files[0]
            path_dirname = os.path.dirname(tmp_work_file)
            base_path, ext = os.path.splitext(path_dirname)

            # Get all files in the directory
            all_dir_files = os.listdir(path_dirname)

            # Filter files that match the extension
            work_files = [
                file
                for file in all_dir_files
                if file.endswith(ext) and name_field in file
            ]

            for work_file in work_files:
                # file_basename = os.path.basename(base_path)
                match = re.match(pattern, work_file)
                if match:
                    version_number = match.groupdict().get("version_number", None)
                    if version_number and version_number not in work_file_versions:
                        work_file_versions.append(int(version_number))

            self.parent.engine.logger.debug(
                "work_file_versions: {}".format(work_file_versions)
            )

            if work_file_versions:
                return max(work_file_versions) + 1
            else:
                return None

        def get_work_path(
            publish_path,
            publish_template,
            work_template,
            selected_keys,
            published_version,
            publish_template_notask=None,
            work_template_notask=None,
        ):
            """get workarea path from published template"""

            engine = self.parent.engine

            task_entity = selected_keys["selected task entity"]

            try:
                fields_dict = publish_template.get_fields(publish_path)
            except:
                if publish_template_notask:
                    fields_dict = publish_template_notask.get_fields(publish_path)
                else:
                    return None

            # build dictionary for get_next_available_work_version_number function
            fields_dict["Step"] = task_entity.get("step", {}).get("name", "")
            fields_dict["Task"] = task_entity.get("content", "")
            fields_dict["step_code"] = task_entity.get("step.Step.short_name", "")
            fields_dict["name"] = selected_keys["selected name"]

            work_version = get_next_available_work_version_number(
                task_entity,
                fields_dict,
                work_template,
                work_template_notask=work_template_notask,
            )

            self.parent.engine.logger.info(
                "work version: {}, publish version: {}, highest version: {}".format(
                    work_version,
                    published_version,
                    max([work_version or published_version, published_version]),
                )
            )

            # replace version with the next available number, either work or published
            if not work_version:
                fields_dict["version"] = published_version
            else:
                fields_dict["version"] = max([work_version, published_version])
            self.parent.engine.logger.debug("fields_dict:\n{}".format(pf(fields_dict)))

            workarea_path = work_template.apply_fields(
                fields_dict
            ) or work_template_notask.apply_fields(fields_dict)
            workarea_path = fix_path(workarea_path)
            validate = work_template.validate(
                workarea_path
            ) or work_template_notask.validate(workarea_path)

            if validate == True:
                return workarea_path
            else:
                return None

        def unpack_tar_file_to_workarea(tar_file_path, workarea_dirname):
            logger = self.parent.engine.logger

            if not os.path.exists(workarea_dirname):
                os.makedirs(workarea_dirname)

            # uncompress the tar file in the workarea
            if os.path.exists(tar_file_path):
                try:
                    transfersManager.unpack_compressed_tar(
                        tar_file_path, workarea_dirname, logger=logger
                    )
                    return True
                except:
                    logger.info(
                        "Couldn't unpack tar file {}".format(tar_file_path)
                    )
                    return False
            else:
                logger.info(
                    (
                        "tar file doesn't exist in the publish area, "
                        "proceeding with legacy copy.\nfile path: {}"
                    ).format(tar_file_path)
                )
                return False

        def copy_files(publish_path, workarea_path):
            """copy files from publish to workarea"""

            publish_path = fix_path(publish_path)
            workarea_path = fix_path(workarea_path)

            msg = (
                "Copying file(s) from:\n\n{}\n\nto:\n\n{}\n\nPlease wait until you get a "
                "confirmation message box. Depending on the size of the original "
                "file, this might take a couple of minutes."
            ).format(publish_path, workarea_path)
            self.parent.engine.logger.info(msg)
            # self.show_message(msg, icon="Information")
            self.parent.engine.show_busy("Copying files...", msg)

            if ".xstage" in workarea_path:
                # create template folder -----------------------------------------------
                publisharea_dirname = os.path.dirname(publish_path)
                workarea_dirname = os.path.dirname(workarea_path)

                workfolder_exist = os.path.exists(workarea_dirname)
                if workfolder_exist:
                    history_folder = workarea_dirname.replace("scenes", "history")
                    history_exist = os.path.exists(history_folder)

                    # delete old history folder
                    if history_exist == True:
                        self.parent.engine.logger.info(
                            "Deleting old history folder:\n{}".format(history_folder)
                        )
                        shutil.rmtree(history_folder)

                    # create new history from scenes folder
                    self.parent.engine.logger.info(
                        "Moving scenes folder contents to history folder."
                    )
                    os.rename(workarea_dirname, history_folder)

                # pack temporary tar file to copy one single file instead of several
                (
                    tar_file_path,
                    source_directory,
                    output_directory,
                ) = transfersManager.get_tar_path(publish_path)

                if not os.path.exists(tar_file_path):
                    transfersManager.create_compressed_tar(
                        tar_file_path, source_directory, output_directory
                    )

                # File copy ------------------------------------------------------------

                tar_file_unpacked = False
                # if tar file exists, unpack it to the workarea
                if os.path.exists(tar_file_path):
                    tar_file_unpacked = unpack_tar_file_to_workarea(
                        tar_file_path, workarea_dirname
                    )

                # tar file doesn't exist, so we use the legacy copy
                if not tar_file_unpacked:
                    try:
                        self.parent.engine.logger.info(
                            "Copying contents of folder:\nfrom {}\nto   {}".format(
                                publisharea_dirname, workarea_dirname
                            )
                        )
                        shutil.copy2 = shutil.copy
                        shutil.copytree(
                            publisharea_dirname,
                            workarea_dirname,
                            # copy_function=shutil.copy,  # only compatible with python 3
                        )
                        self.parent.engine.logger.info("Finished copying files")
                    except:
                        msg = ("Couldn't copy files from\n\n{}\n\nto\n\n{}").format(
                            publisharea_dirname, workarea_dirname
                        )
                        self.parent.engine.logger.info(msg)
                        self.parent.engine.clear_busy()
                        self.show_message(msg, icon="Critical")
                        return None
                    finally:
                        self.parent.engine.logger.info("Restoring copy2 method")
                        from shutil import copy2 as orig_copy2

                        shutil.copy2 = orig_copy2

                publish_basename = os.path.basename(publish_path)
                # work_basename = os.path.basename(workarea_path)

                # resolve the xstage file path -------------------------------------
                # resolve the xstage file path, using the published basename,
                # but now aims to the workarea, as we copied in the previous lines.
                xstage_path_to_rename = os.path.join(workarea_dirname, publish_basename)
                # this should be the path of the original xstage file,
                # but in the workarea
                xstage_path_to_rename = fix_path(xstage_path_to_rename)

                # resolve the aux file path ----------------------------------------
                # this should be the path of the original aux file,
                # but in the workarea
                aux_path_to_rename = xstage_path_to_rename.replace(".xstage", ".aux")

                # this should be the path of the aux file with the correct name for
                # the workarea (using the workarea template
                workarea_aux_path = workarea_path.replace(".xstage", ".aux")

                self.parent.engine.logger.info("xstage file renaming:")
                self.parent.engine.logger.info(
                    "\nxstage_path_to_rename:\n{}\nnew_path:\n{}".format(
                        xstage_path_to_rename, workarea_path
                    )
                )

                self.parent.engine.logger.info("aux file renaming:")
                self.parent.engine.logger.info(
                    "\naux_path_to_rename:\n{}\nnew_path:\n{}".format(
                        aux_path_to_rename, workarea_aux_path
                    )
                )

                # rename xstage and aux files in the workarea to match the new step
                # and task. Also uses the new version ----------------------------------

                xstage_file_exists = os.path.exists(xstage_path_to_rename)
                self.parent.engine.logger.info(
                    "xstage_file_exists: {}".format(xstage_file_exists)
                )
                if xstage_file_exists == True:
                    self.parent.engine.logger.info("Renaming xstage file")
                    try:
                        os.rename(xstage_path_to_rename, workarea_path)
                        # os.rename causes the creation date of the file to be reset,
                        # which is fine, but unfortunately it leaves the modified date
                        # intact, which is actually older now than the creation date, so
                        # we copy the creation date to the modified date to have more
                        # consistency, but also because the SG's work files app displays
                        # the modified date and this might lead to confusion
                        creation_time = os.path.getctime(workarea_path)
                        os.utime(workarea_path, (creation_time, creation_time))
                        # self.parent.engine.clear_busy()
                    except:
                        try:
                            shutil.move(xstage_path_to_rename, workarea_path)
                            # self.parent.engine.clear_busy()
                        except:
                            msg = ("Couldn't rename xstage file from {} to {}.").format(
                                xstage_path_to_rename, workarea_path
                            )
                            self.parent.engine.logger.info(msg)
                            self.parent.engine.clear_busy()
                            self.show_message(msg, icon="Critical")
                            return None
                else:
                    self.parent.engine.logger.error(
                        "Renaming xstage file skipped as it doesn't exist on disk."
                    )

                aux_file_exists = os.path.exists(aux_path_to_rename)
                self.parent.engine.logger.info(
                    "aux_file_exists: {}".format(aux_file_exists)
                )
                if aux_file_exists == True:
                    self.parent.engine.logger.info("Renaming aux file")
                    try:
                        os.rename(aux_path_to_rename, workarea_aux_path)
                        # os.rename causes the creation date of the file to be reset,
                        # which is fine, but unfortunately it leaves the modified date
                        # intact, which is actually older now than the creation date, so
                        # we copy the creation date to the modified date to have more
                        # consistency, but also because the SG's work files app displays
                        # the modified date and this might lead to confusion
                        creation_time = os.path.getctime(workarea_aux_path)
                        os.utime(workarea_aux_path, (creation_time, creation_time))
                        self.parent.engine.clear_busy()
                        return True
                    except:
                        try:
                            shutil.move(aux_path_to_rename, workarea_aux_path)
                            self.parent.engine.clear_busy()
                            return True
                        except:
                            msg = ("Couldn't rename aux file from {} to {}.").format(
                                aux_path_to_rename, workarea_aux_path
                            )
                            self.parent.engine.logger.info(msg)
                            self.parent.engine.clear_busy()
                            self.show_message(msg, icon="Critical")
                            return None
                else:
                    self.parent.engine.logger.warning(
                        "Renaming aux file skipped as it doesn't exist on disk."
                    )
                    self.parent.engine.clear_busy()
                    # we still return tru because the aux file is not mandatory
                    return True

            else:
                workarea_dirname = os.path.dirname(workarea_path)
                if not os.path.exists(workarea_dirname):
                    sgtk.util.filesystem.ensure_folder_exists(workarea_dirname)
                try:
                    # copy with new version
                    self.parent.engine.logger.info(
                        "Copying file {}".format(os.path.basename(workarea_path))
                    )
                    shutil.copy(publish_path, workarea_path)
                    self.parent.engine.clear_busy()
                    return True
                except:
                    msg = ("Couldn't copy file from {} to {}.").format(
                        publish_path, workarea_path
                    )
                    self.parent.engine.logger.info(msg)
                    self.parent.engine.clear_busy()
                    self.show_message(msg, icon="Critical")
                    return None

        # ==============================================================================

        app = self.parent
        engine_name = self.parent.engine.name
        self.parent.engine.logger.info("engine_name: {}".format(engine_name))

        file_path = file_dict.get("path", None)
        self.parent.engine.logger.info("file_path: {}".format(file_path))
        if not file_path:
            msg = (
                "Couldn't get path for selected file. Please make sure you are "
                "trying to run this action from the Publishes tab. If you are not, "
                "then please switch to that tab and try again."
            )
            self.show_message(msg, icon="Critical")
            return None

        # get file path info

        current_template = tk.template_from_path(file_path)
        self.current_info = current_template.get_fields(file_path)

        self.parent.engine.logger.info("current_info: {}".format(self.current_info))
        # get published file entity ----------------------------------------------------
        published_file = sgtk.util.find_publish(self.parent.tank, [file_path])
        if not published_file:
            self.parent.engine.logger.error("Couldn't not get published file.")
            msg = (
                "Couldn't not get published file. Please make sure you are "
                "trying to run this action from the Publishes tab. If you are not, "
                "then please switch to that tab and try again."
            )
            self.show_message(msg, icon="Critical")
            return None
        # self.parent.engine.logger.info(
        #     "published_file:\n{}".format(pf(published_file))
        # )

        # we need to download the file before continue, because it is required for the
        # file to exist before we can get tk from path.
        download_start = datetime.now()
        if not os.path.exists(file_path):
            try:
                local_path = self.ensure_file_is_local(file_dict)
            except:
                msg = "Couldn't download file:\n\n{}".format(traceback.format_exc())
                self.parent.engine.logger.error(msg)
                self.show_message(msg, icon="Critical")
                return None
        else:
            local_path = file_path
        download_end = datetime.now()
        download_time_diff = download_end - download_start

        # after trying to download we check if the file actually existst on disk
        # before continuing
        if not os.path.exists(local_path):
            self.parent.engine.logger.error(
                "Published file was not downloaded correctly."
            )
            msg = (
                "Published file was not downloaded correctly. Please make sure you "
                "are trying to run this action from the Publishes tab. If you are "
                "not, then please switch to that tab and try again."
            )
            self.show_message(msg, icon="Critical")
            return None

        published_file_data = get_published_file_data_from_sg(
            published_file.get(file_path, {}).get("id")
        )
        if not published_file_data:
            self.parent.engine.logger.error("Couldn't not get published_file_data.")
            msg = (
                "Couldn't not get published_file_data. Please make "
                "sure you are trying to run this action from the Publishes tab. "
                "If you are not, then please switch to that tab and try again."
            )
            self.show_message(msg, icon="Critical")
            return None
        self.parent.engine.logger.debug(
            "published_file_data:\n{}".format(pf(published_file_data))
        )

        # get parent (main) entity tasks -----------------------------------------------
        parent_entity_tasks = get_entity_tasks(published_file_data.get("entity", None))
        if not parent_entity_tasks:
            self.parent.engine.logger.error("Couldn't not get parent_entity_tasks.")
            msg = (
                "Couldn't not get parent_entity_tasks. Please make "
                "sure you are trying to run this action from the Publishes tab. "
                "If you are not, then please switch to that tab and try again."
            )
            self.show_message(msg, icon="Critical")
            return None

        # get sorted entity tasks dictionary -------------------------------------------
        sorted_entity_tasks = get_sorted_entity_tasks(parent_entity_tasks)
        self.parent.engine.logger.debug(
            "sorted_entity_tasks:\n{}".format(pf(sorted_entity_tasks))
        )

        # ask user for a new pipeline step and task ------------------------------------
        # Show window to select the target pipeline step and task
        if not QApplication.instance():
            qt_app = QApplication(sys.argv)
        else:
            qt_app = QApplication.instance()

        # in some multitask templates, there's is no task field, so we need to ensure
        # this field exists in the self.current_info dictionary before passing it
        #  through to the ChangeContextWindow
        if not "Task" in self.current_info.keys():
            self.current_info["Task"] = published_file_data.get("task", {}).get(
                "name", ""
            )
            self.parent.engine.logger.info(
                "Added task field to current_info from published_file_data"
            )

        window = ChangeContextWindow(sorted_entity_tasks, self.current_info)
        window.raise_()
        window.activateWindow()
        window.exec_()
        self.parent.engine.logger.debug(
            "global selected_keys:\n{}".format(pf(selected_keys))
        )
        if not selected_keys:
            msg = (
                "Couldn't get target pipeline step and task. Please make sure you "
                "are trying to run this action from the Publishes tab. If you are "
                "not, then please switch to that tab and try again."
            ).format(engine_name)
            self.show_message(msg, icon="Critical")
            return None

        # Get templates ----------------------------------------------------------------
        main_entity_type = published_file_data.get("entity", {}).get("type", "").lower()
        templates_per_engine = app.get_setting("templates_per_engine")
        if not templates_per_engine or not engine_name in templates_per_engine.keys():
            self.parent.engine.logger.error(
                "templates_per_engine:\n{}".format(templates_per_engine)
            )
            msg = (
                "Couldn't get templates for engine {}. Please make sure you are "
                "trying to run this action from the Publishes tab. If you are not, "
                "then please switch to that tab and try again."
            ).format(engine_name)
            self.show_message(msg, icon="Critical")
            return None

        work_template_name = (
            templates_per_engine.get(engine_name, {})
            .get(main_entity_type, {})
            .get("template_work_area", None)
        )
        work_template_notask_name = (
            templates_per_engine.get(engine_name, {})
            .get(main_entity_type, {})
            .get("template_work_area_notask", None)
        )

        publish_template_name = (
            templates_per_engine.get(engine_name, {})
            .get(main_entity_type, {})
            .get("template_publish_area", None)
        )
        publish_template_notask_name = (
            templates_per_engine.get(engine_name, {})
            .get(main_entity_type, {})
            .get("template_publish_area_notask", None)
        )

        work_template = self.parent.engine.get_template_by_name(work_template_name)
        self.parent.engine.logger.info(
            "work_template_name: {}".format(work_template_name)
        )

        if work_template_notask_name:
            self.parent.engine.logger.info(
                "work_template_notask_name: {}".format(work_template_notask_name)
            )
            work_template_notask = self.parent.engine.get_template_by_name(
                work_template_notask_name
            )
        else:
            work_template_notask = None

        publish_template = self.parent.engine.get_template_by_name(
            publish_template_name
        )
        self.parent.engine.logger.info(
            "publish_template_name: {}".format(publish_template_name)
        )

        if publish_template_notask_name:
            self.parent.engine.logger.info(
                "publish_template_notask_name: {}".format(publish_template_notask_name)
            )
            publish_template_notask = self.parent.engine.get_template_by_name(
                publish_template_notask_name
            )
        else:
            publish_template_notask = None

        if not work_template or not publish_template:
            msg = (
                "Couldn't get templates:\n\nwork template: {}\npublish template: {}"
            ).format(work_template.name, publish_template.name)
            self.show_message(msg, icon="Critical")
            return None

        # get task publishes and next available publish version ------------------------
        main_entity = published_file_data.get("entity", {})
        task_entity = selected_keys[
            "selected task entity"
        ]  # last element should be the task entity
        task_publishes = get_task_publishes_from_sg(main_entity, task_entity)
        next_available_pub_version = get_next_available_published_version_number(
            task_publishes, publish_template, file_path
        )

        # get next available work version ----------------------------------------------
        work_path = get_work_path(
            file_path,
            publish_template,
            work_template,
            selected_keys,
            next_available_pub_version,
            publish_template_notask=publish_template_notask,
            work_template_notask=work_template_notask,
        )
        self.parent.engine.logger.info("target work_path: {}".format(work_path))
        if not work_path:
            msg = "Couldn't get work area path."
            self.show_message(msg, icon="Critical")
            return None

        # copy files to new context ----------------------------------------------------
        if not os.path.exists(os.path.dirname(work_path)):
            os.makedirs(os.path.dirname(work_path))

        copy_files_start = datetime.now()
        successfully_copied = copy_files(file_path, work_path)
        copy_files_end = datetime.now()
        copy_files_time_diff = copy_files_end - copy_files_start

        total_execution_time = copy_files_end - download_start

        if not successfully_copied:
            # play sound from engine attribute (set in engine_init)
            self.parent.engine.error_sound()

            # show message
            msg = "Copying files failed"
            self.parent.logger.error(msg)
            self.parent.engine.clear_busy()
            self.show_message(msg, icon="Critical")
            return None
        else:
            # play sound from engine attribute (set in engine_init)
            self.parent.engine.success_sound()

            # show message
            msg = (
                "Successfully copied file to new context. Please change to the {} task "
                "of the {} pipeline step (of this same shot), and open the available "
                "work file from the 'Working' files tab.\n\nDownload time: {}"
                "\nCopy time: {}\n\nTotal execution time: {}"
            ).format(
                task_entity["content"],
                task_entity.get("step", {}).get("name", ""),
                download_time_diff,
                copy_files_time_diff,
                total_execution_time,
            )
            self.parent.engine.logger.info(msg)
            self.parent.engine.clear_busy()
            self.show_message(msg, icon="Information")
            return True


class ChangeContextWindow(QtGui.QDialog):
    def __init__(self, entity_tasks_dict, current_info):
        super(ChangeContextWindow, self).__init__()

        # Store the input dictionary
        self.entity_tasks_dict = entity_tasks_dict
        self.current_info = current_info

        self.style_grp = """
        QGroupBox {
            border: 1px solid #787878;
            border-radius: 6px;
            margin-top: 6px;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 7px;
            padding: 0px 5px 0px 5px;
        }
        """

        # Initialize selected keys
        self.selected_pipeline_step = None
        self.selected_pipeline_step_short_code = None
        self.selected_task = None
        self.selected_task_entity = None

        self.setWindowTitle("Select target task context")
        self.resize(350, 100)

        main_layout = QVBoxLayout(self)

        # Create combo boxes
        context_group = QGroupBox("Select new context")
        context_group.setStyleSheet(self.style_grp)
        layout_combos = QHBoxLayout()

        # step
        steps_label = QLabel("Select a pipeline step:")
        self.steps_combo = QComboBox(self)
        self.steps_combo.currentIndexChanged.connect(self.update_tasks_combo)
        layout_combos.addWidget(steps_label)
        layout_combos.addWidget(self.steps_combo)

        # task
        tasks_label = QLabel("Select a task:")
        self.tasks_combo = QComboBox(self)
        self.tasks_combo.currentIndexChanged.connect(self.update_selected_task)
        layout_combos.addWidget(tasks_label)
        layout_combos.addWidget(self.tasks_combo)

        context_group.setLayout(layout_combos)
        main_layout.addWidget(context_group)

        # Name
        name_group = QGroupBox("Do you want to change the name field?")
        name_group.setStyleSheet(self.style_grp)
        layout_name = QHBoxLayout()

        name_label = QLabel("New name:")
        self.name_field = QTextEdit(self)

        self.name_field.setMaximumHeight(name_label.sizeHint().height() + 6)
        self.name_field.insertPlainText(self.current_info.get("name"))
        self.name_field.setVerticalScrollBarPolicy(QtGui.Qt.ScrollBarAlwaysOff)
        layout_name.addWidget(name_label)
        layout_name.addWidget(self.name_field)

        name_group.setLayout(layout_name)
        main_layout.addWidget(name_group)

        # Create buttons
        layout_buttons = QHBoxLayout()
        self.accept_button = QPushButton("Accept", self)
        self.accept_button.clicked.connect(self.accept_selection)
        self.cancel_button = QPushButton("Cancel", self)
        self.cancel_button.clicked.connect(self.reject_selection)
        layout_buttons.addWidget(self.accept_button)
        layout_buttons.addWidget(self.cancel_button)
        main_layout.addLayout(layout_buttons)

        self.setLayout(main_layout)

        # Populate the main combo box with the main dictionary keys
        self.steps_combo.addItems(self.entity_tasks_dict.keys())
        self.steps_combo.setCurrentIndex(
            list(self.entity_tasks_dict.keys()).index(self.current_info.get("Step"))
        )
        self.tasks_combo.setCurrentIndex(
            list(
                self.entity_tasks_dict.get(
                    self.current_info.get("Step", {}), {}
                ).keys()).index(
                self.current_info.get("Task")
            )
        )

    def validation_name_field(self, name):
        if name == "" or name == None:
            return False
        patron = re.compile(r'^[a-zA-Z0-9]+$')
        return bool(patron.match(name))


    def update_tasks_combo(self):
        self.selected_pipeline_step = self.steps_combo.currentText()
        nested_dict = self.entity_tasks_dict.get(self.selected_pipeline_step, {})
        nested_keys = nested_dict.keys()

        self.tasks_combo.clear()
        if nested_keys:
            self.tasks_combo.addItems(nested_keys)
        else:
            self.selected_pipeline_step = None
        self.selected_task = self.tasks_combo.currentText()
        self.selected_pipeline_step_short_code = (
            self.entity_tasks_dict.get(self.selected_pipeline_step, {})
            .get(self.selected_task, {})
            .get("step.Step.short_name", "")
        )

    def update_selected_task(self):
        self.selected_task = self.tasks_combo.currentText()
        self.selected_pipeline_step_short_code = (
            self.entity_tasks_dict.get(self.selected_pipeline_step, {})
            .get(self.selected_task, {})
            .get("step.Step.short_name", "")
        )
        self.selected_task_entity = self.entity_tasks_dict.get(
            self.selected_pipeline_step, {}
        ).get(self.selected_task, {})

    def accept_selection(self):
        # get and validate name
        self.selected_name = self.name_field.toPlainText()
        self.selected_name = self.selected_name.strip()
        validate_name = self.validation_name_field(self.selected_name)
        # only if the name is correct will the script continue
        if not validate_name:
            msg = (
                "The New Name field cannot be empty and cannot have symbols or "
                "characters that are not letters or numbers.\n\n"
                "then please close this windows and try again."
            )
            msg_error = QtGui.QMessageBox()
            msg_error.setWindowTitle("Wrong name error.")
            msg_error.setText(msg)
            msg_error.setIcon(QtGui.QMessageBox.Warning)
            msg_error.raise_()
            msg_error.activateWindow()
            msg_error.exec_()
            return

        global selected_keys
        selected_keys = {}
        self.selected_pipeline_step = self.steps_combo.currentText()
        self.selected_task = self.tasks_combo.currentText()
        self.selected_pipeline_step_short_code = self.entity_tasks_dict[
            self.selected_pipeline_step
        ][self.selected_task]["step.Step.short_name"]

        # selected_keys.append(self.selected_pipeline_step_short_code)
        # selected_keys.append(self.selected_task)
        # selected_keys.append(self.selected_task_entity)
        # selected_keys.append(self.selected_name)
        selected_keys = {
            "selected pipeline step short code": self.selected_pipeline_step_short_code,
            "selected task": self.selected_task,
            "selected task entity": self.selected_task_entity,
            "selected name": self.selected_name,
        }

        self.close()

    def reject_selection(self):
        global selected_keys
        selected_keys = {}
        self.reject()
