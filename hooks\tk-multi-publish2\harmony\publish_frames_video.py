#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

import os
import re
import six
import shutil
import traceback

from os import listdir
from os.path import isfile, join

import sgtk
from sgtk.platform.qt import QtGui, QtCore

HookBaseClass = sgtk.get_hook_baseclass()


class HarmonyPublishReviewPlugin(HookBaseClass):
    """
    Plugin for publish rendered sequence

    """

    @property
    def icon(self):
        """
        Path to an png icon on disk
        """

        # look for icon one level up from this hook's folder in "icons" folder
        return os.path.join(
            self.disk_location,
            os.pardir,
            "icons",
            "review.png"
        )

    @property
    def name(self):
        """
        One line display name describing the plugin
        """
        return "Publish sequence files"

    @property
    def description(self):
        """
        Copy/upload the render sequence to publish area
        """

        loader_url = "https://support.shotgunsoftware.com/hc/en-us/articles/219033078"

        return """
        <p>This plugin upload the video as a version in shotgun.</p>
        """

    @property
    def settings(self):
        """
        Dictionary defining the settings that this plugin expects to recieve
        through the settings parameter in the accept, validate, publish and
        finalize methods.

        A dictionary on the following form::

            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }

        The type string should be one of the data types that toolkit accepts
        as part of its environment configuration.
        """

        # settings specific to this class
        harmony_publish_settings = {

            "Work Template": {
                "type": "template",
                "default": "harmony_shot_work",
                "description": "Harmony work session"},

            "Primary Publish Template": {
                "type": "template",
                "default": "harmony_shot_publish",
                "description": "Harmony publish session"},

            "Publish Render Template": {
                "type": "template",
                "default": "shot_flat_render_publish_exr",
                "description": "Template where render sequence files got published"},

            "Publish Review Template":{
                "type": "template",
                "default": "shot_flat_render_publish_mov",
                "description": "Template to create a mov file"},

            "Publish Review Type": {
                "type": "str",
                "default": "Media Review",
                "description": "Published File Type name to use for the published movie"}

        }

        # inherit the settings from the base publish plugin
        plugin_settings = super(HarmonyPublishReviewPlugin, self).settings or {}

        # update the base settings
        plugin_settings.update(harmony_publish_settings)
        return plugin_settings

    @property
    def item_filters(self):
        """
        List of item types that this plugin is interested in.

        Only items matching entries in this list will be presented to the
        accept() method. Strings can contain glob patters such as *, for example
        ["fusion.*", "file.fusion"]
        """
        return ["harmony.frames_sequence"]

    def accept(self, settings, item):
        """
        Method called by the publisher to determine if an item is of any
        interest to this plugin. Only items matching the filters defined via the
        item_filters property will be presented to this method.

        A submit for review task will be generated for each item accepted here. Returns a
        dictionary with the following booleans:

            - accepted: Indicates if the plugin is interested in this value at
                all. Required.
            - enabled: If True, the plugin will be enabled in the UI, otherwise
                it will be disabled. Optional, True by default.
            - visible: If True, the plugin will be visible in the UI, otherwise
                it will be hidden. Optional, True by default.
            - checked: If True, the plugin will be checked in the UI, otherwise
                it will be unchecked. Optional, True by default.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process

        :returns: dictionary with boolean keys accepted, required and enabled.
            This plugin makes use of the tk-multi-reviewsubmission app; if this
            app is not available then the item will not be accepted, this method
            will return a dictionary with the accepted field that has a value of
            False.

            Several properties on the item must also be present for it to be
            accepted. The properties are 'path', 'publish_name', 'color_space',
            'first_frame' and 'last_frame'
        """

        publisher = self.parent
        engine = publisher.engine
        context = engine.context

        # Verify entity type SHOT
        entity_type = str(context).split(' ')[1]
        if entity_type != "Shot":
            return {'accepted': False}

        return {"accepted": True,
                "checked": True}


    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish. Returns a
        boolean to indicate validity.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        :returns: True if item is valid and not in proxy mode, False otherwise.
        """

        sg_shot = self.get_sg_shot_info(['sg_cut_in', 'sg_cut_out'])

        sequence = item.properties['sequence']

        # Harmony fames can only start at 1
        # we can only validate if the number of frames matches shot
        sequence_frames = len(sequence)
        shot_frames = sg_shot['sg_cut_out'] - sg_shot['sg_cut_in'] + 1

        if sequence_frames != shot_frames:
            error_msg = "Shot lenght and render frames don't match: {0} - {1}"
            raise Exception(error_msg.format(shot_frames, sequence_frames))

        # Other known issue is if the render naming convention uses "-"
        # that will cause conflict with the image sequence recognition
        # and will make the numbers look like negative, which will cause problems
        if sequence.start() < 0 or sequence.end() < 0:
            error_msg = ("It looks like you have negative numbers!\n"
                         "This is mostly because of your frames naming "
                         "convention, just make sure that you are not using '-'")
            raise Exception(error_msg)

        return True


    def publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """

        #   Video process
        engine      = self.parent.engine
        path        = engine.app.get_current_project_path()
        sequence    = item.properties['sequence']
        tpl_by_name = engine.get_template_by_name

        # Gettiings settings
        work_template_setting            = settings.get("Work Template")
        primary_publish_template_setting = settings.get("Primary Publish Template")
        render_publish_template_setting  = settings.get("Publish Render Template")
        review_publish_template_setting  = settings.get("Publish Review Template")
        review_publish_type_setting      = settings.get("Publish Review Type")

        # Getting templates
        work_template            = tpl_by_name(work_template_setting.value)
        primary_publish_template = tpl_by_name(primary_publish_template_setting.value)
        render_publish_template  = tpl_by_name(render_publish_template_setting.value)
        review_publish_template  = tpl_by_name(review_publish_template_setting.value)

        # Work area fields
        fields                = work_template.get_fields(path)

        # Dynamic paths or elements from templates
        primary_publish_path     = primary_publish_template.apply_fields(fields)
        render_publish_path      = render_publish_template.apply_fields(fields)
        review_publish_path      = review_publish_template.apply_fields(fields)
        review_publish_type_name = review_publish_type_setting.value

        publish_name    = fields["name"]
        publish_version = fields["version"]
        padding         = len(sequence[0].split('.')[1])
        sg_proj         = self.get_sg_project_info(['sg_working_color_space', 'sg_fps'])

        # source sequence to convert
        source_seq      = '.'.join([sequence[0].split('.')[0],
                                 '%0{}d'.format(padding),
                                 sequence[0].split('.')[2]])


        sg_shot      = self.get_sg_shot_info(['sg_cut_in', 'sg_cut_out'])

        # Converting exr to png files
        # Conversion to other colorspace
        seq_first_f = sequence.start()
        png_seq = self.convert_seq_to_png(settings, item, source_seq, sg_proj, sg_shot, seq_first_f)
        if not png_seq:
            # Second try
            png_seq = self.convert_seq_to_png(settings, item, source_seq, sg_proj, sg_shot, seq_first_f)
        if not png_seq:
            message = "Failed to png sequence, check sequence: %s"
            raise Exception(message % (source_seq))

        # Audio section
        audio_path = engine.app.custom_script("sound.getSoundtrackAll().path();")
        audio_cmd_1  = ""
        audio_cmd_2  = ""
        if os.path.exists(audio_path):
            audio_cmd_1  = ' -i "{}"'.format(audio_path)
            """
            audio_cmd_2  = ' -c:a aac'
            audio_cmd_2  = ' -acodec pcm_s16le'
            audio_cmd_2 += ' -aframes 0'
            audio_cmd_2 += ' -map 1:a'
            """
            audio_cmd_2  = ' -acodec pcm_s16le'


        # Check if video folder exists
        self.parent.engine.ensure_folder_exists(os.path.dirname(review_publish_path))

        self.logger.info('Creating video from png files: {}'.format(review_publish_path))

        # its important that the video filter goes
        # last to preserve the color matrix
        ffmpeg_tpl  = "-start_number {FRAME}"
        ffmpeg_tpl  += " -r {FRAME_RATE}"
        ffmpeg_tpl  += " -i {SEQ}"
        ffmpeg_tpl  += audio_cmd_1
        # ffmpeg_tpl  += " -s 1920x1080"
        ffmpeg_tpl  += " -vcodec libx264"
        ffmpeg_tpl  += " -pix_fmt yuv422p"
        ffmpeg_tpl  += audio_cmd_2
        #ffmpeg_tpl  += " -map 0:v"
        ffmpeg_tpl  += " -crf 5"
        ffmpeg_tpl  += " -r {FRAME_RATE}"
        # ffmpeg_tpl  += ' -vf "colorspace=bt709:iall=bt601-6-625:fast=1"'
        ffmpeg_tpl  +=  " -y"
        ffmpeg_tpl  += " {VIDEO}"

        ffmpeg_cmd   = ffmpeg_tpl.format(
            FRAME=str(sequence.start()),
            SEQ=png_seq,
            FRAME_RATE=sg_proj['sg_fps'],
            VIDEO=review_publish_path
        )

        ffmpeg_framework = self.load_framework("mty-framework-ffmpeg")
        ffmpeg_framework.ffmpegCore.set_binary(binary='convert')

        _err, _info = ffmpeg_framework.ffmpegCore.execute_command(ffmpeg_cmd)
        if _err:
            message = "Failed to create video: %s\ncommand: %s"
            raise Exception(message % (_info, ffmpeg_cmd))

        # Cleaning pngs
        offset = sg_shot['sg_cut_in'] - sequence.start()
        last_f = sg_shot['sg_cut_out'] - offset
        self.logger.info('Cleaning temporal files')
        for i in range(sequence.start(), last_f+1):
            cur_frame = png_seq % i
            if os.path.exists(cur_frame):
                os.remove(cur_frame)

        #   Publish video process
        args = {
            "tk":               self.parent.engine.sgtk,
            "context":          item.context,
            "comment":          item.description,
            "path":             review_publish_path,
            "name":             publish_name,
            "version_number":   publish_version,
            "thumbnail_path":   item.get_thumbnail_as_path(),
            "task":             self.parent.engine.context.task,
            "dependency_paths": [render_publish_path],
            "sg_fields":        {"sg_status_list":   "rev"},
            "published_file_type": review_publish_type_name
        }

        sg_publishes = sgtk.util.register_publish(**args)

        item.properties.sg_publish_data = sg_publishes
        item.properties.sg_publish_path = sg_publishes["path"]["local_path"]

        self.parent.log_debug(
            "Adding existing publish " +
            "(id:{}) ".format(sg_publishes['id']) +
            "as extra data for item: {}".format(item)
        )

        #   Version process Ref publish_shot_media_review.py from maya/layout
        sg_version = self.submit_version(
            render_publish_path,     review_publish_path,
            [sg_publishes],     self.parent.engine.context.task,
            item.description,   int(sg_shot['sg_cut_in']),
            int(sg_shot['sg_cut_out']),
        )

        # Upload in a new thread and make our own event loop
        # to wait for the thread to finish.
        event_loop = QtCore.QEventLoop()
        thread = UploaderThread(
            self.parent, sg_version,
            review_publish_path,
            item.get_thumbnail_as_path(),
            True
        )

        thread.finished.connect(event_loop.quit)
        thread.start()
        event_loop.exec_()

        # log any errors generated in the thread
        for e in thread.get_errors():
            self.parent.log_error(e)

        # Update task status for revision
        sg_task = self.parent.context.task
        self.parent.logger.info(
            "End of Publish and updating task with id: {} to status: 'rev'".format(
                str(sg_task['id'])
            )
        )

        try:
            self.parent.engine.shotgun.update(
                "Task", sg_task["id"], {"sg_status_list": 'rev'}
            )
        except:
            pass


    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        self.logger.debug('Item video successfully published')

    def get_sg_shot_info(self, shot_fields):
        engine         = self.parent.engine
        sg             = engine.shotgun
        sg_proj        = engine.context.project

        context_tokens = str(engine.context).split(' ')
        entity_name    = context_tokens[2]
        shot_filter    = [['project', 'is', sg_proj],
                          ['code', 'is', entity_name]]

        sg_shot = sg.find_one('Shot', shot_filter, shot_fields)
        return sg_shot

    def get_root_item(self, item):

        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root

    def submit_version(self, path_to_frames, path_to_movie, sg_publishes,
                       sg_task, comment, first_frame, last_frame):
        """
        Create a version in Shotgun for this path and linked to this publish.
        """

        # get current shotgun user
        current_user = self.parent.engine.context.user

        # create a name for the version based on the file name
        # grab the file name, strip off extension
        name = os.path.splitext(os.path.basename(path_to_movie))[0]
        # do some replacements and capitalize
        name = name.replace("_", " ").capitalize()

        LinkFolder = {'local_path': os.path.dirname(path_to_frames) + os.sep,
                      'content_type': None,
                      'link_type': 'local',
                      'name': name}

        LinkFile   = {'local_path': path_to_movie,
                      'content_type': None,
                      'link_type': 'local',
                      'name': name}

        entity = self.parent.engine.context.entity
        proj   = self.parent.engine.context.project
        # Create the version in Shotgun
        data = {
            "code":                 name,
            "sg_status_list":       "rev",
            "entity":               entity,
            "sg_task":              sg_task,
            "sg_version_type":      "Production",
            "sg_first_frame":       first_frame,
            "sg_last_frame":        last_frame,
            "frame_count":          (last_frame - first_frame + 1),
            "frame_range":          "%s-%s" % (first_frame, last_frame),
            "sg_frames_have_slate": False,
            "published_files":      sg_publishes,
            "created_by":           current_user,
            "description":          comment,
            "sg_path_to_frames":    path_to_frames,
            "sg_path_to_movie":     path_to_movie,
            "sg_movie_has_slate":   False,
            "project":              proj,
            "user":                 current_user
        }

        sg_version = self.parent.engine.shotgun.create("Version", data)
        self.parent.log_debug("Created version in shotgun: %s" % str(data))

        return sg_version

    def convert_seq_to_png(self, settings, item, source_seq, sg_proj, sg_shot, seq_first_f):
        # Element used to convert exr to png for colorconverting
        ocio         = self.load_framework("mty-framework-opencolorio")
        oiio         = self.load_framework("mty-framework-openimageio")
        oiio_tool    = oiio.openImageIOCore
        ocio_path    = ocio.disk_location + "/version/aces_1_2/config.ocio"
        frames_limit = 15

        oiio_tool.set_binary('oiiotool')
        oiio_exe        = oiio_tool.get_bin_path()
        all_colorspaces = {'sRGB': 'Output - sRGB',
                           'rec709': 'Output - Rec.709',
                           'aces_cg': 'ACES - ACEScg'}

        fr_start     = int(sg_shot['sg_cut_in'])
        fr_end       = int(sg_shot['sg_cut_out'])
        total_frames = fr_end - fr_start
        oiio_jobs    = int(total_frames / frames_limit) +1

        # Offset frames to sequence files range
        offset   = fr_start - seq_first_f
        fr_start = seq_first_f
        fr_end   = fr_end - offset

        # png with the color already converted
        temp_seq        = '.'.join(["{}_tmp".format(source_seq.split('.')[0]),
                                    source_seq.split('.')[1],
                                    'png'])

        # Using valid color space name
        proj_colorspace = sg_proj['sg_working_color_space']
        if proj_colorspace in all_colorspaces.keys():
            proj_colorspace = all_colorspaces[proj_colorspace]

        # Cleaning revious files
        folder_container = os.path.dirname(temp_seq)

        all_elements = os.listdir(folder_container)
        for sub_element in all_elements:
            if len(sub_element.split('.')) <=3:
                continue
            if sub_element.endswith('.temp.png'):
                full_el_path = os.path.join(folder_container, sub_element)
                os.remove(full_el_path)

        # Split the process in parts because it fails after frame 32
        for i in range(0, oiio_jobs):
            current_first_f = str(fr_start + (frames_limit * i))
            if i == oiio_jobs -1:
                current_last_f  = str(fr_end)
            else:
                current_last_f  = str(fr_start + (frames_limit * (i+1))-1)

            # oiiotool command
            if current_first_f != current_last_f:
                oiio_cmds  = "--frames {}-{}".format(current_first_f, current_last_f)
            else:
                oiio_cmds  = "--frames {}".format(current_first_f)

            oiio_cmds += " {}".format(source_seq)
            #oiio_cmds += " -ch R,G,B"
            oiio_cmds += " -colorconfig"
            oiio_cmds += ' "{}"'.format(ocio_path)
            oiio_cmds += " -colorconvert"
            oiio_cmds += ' "{}"'.format(proj_colorspace) # Input colorspace
            oiio_cmds += ' "Output - sRGB"' # output colorspace
            oiio_cmds += " -croptofull"
            oiio_cmds += " -o {}".format(temp_seq)

            info_msg = 'Temp png  Job: {}/{}  Frames: {}-{}'
            info_msg = info_msg.format(str(i+1), str(oiio_jobs+1),
                                       current_first_f,
                                       current_last_f)
            self.logger.info(info_msg)

            if not ' '  in oiio_exe:
                _err, _info, _cmd = oiio_tool.execute_command_str(oiio_cmds)

            else:
                # Problems with user with spaces
                # Using subprocess instead of the framework
                new_oiio_cmds = '"{}" {}'.format(oiio_exe, oiio_cmds)
                process2      = subprocess.Popen(new_oiio_cmds, shell=True,
                                                 stdout=subprocess.PIPE,
                                                 stderr=subprocess.STDOUT)
                _info, _err   = process2.communicate()

                _info = six.ensure_str(_info)
                _err = six.ensure_str(_err)

            if _err:
                war_msg = '{}/{} {}-{}: Error creating temp files'
                war_msg = war_msg.format(str(i+1), str(oiio_jobs+1), current_first_f, current_last_f)
                self.logger.error(war_msg)
                raise Exception("Failed to create png aux sequence:\n {}".format(_info))

        # Validating all the frames
        for i in range(fr_start, fr_end+1):
            cur_file = temp_seq % i
            if not os.path.exists(cur_file):
                return False

        return temp_seq

    def get_sg_project_info(self, proj_fields):
        engine         = self.parent.engine
        sg             = engine.shotgun
        sg_proj        = engine.context.project

        proj_filter    = [['id', 'is', sg_proj['id']]]

        sg_proj = sg.find_one('Project', proj_filter, proj_fields)
        return sg_proj


class UploaderThread(QtCore.QThread):
    """
    Simple worker thread that encapsulates uploading to shotgun.
    Broken out of the main loop so that the UI can remain responsive
    even though an upload is happening
    """
    def __init__(self, app, version, path_to_movie, thumbnail_path, upload_to_shotgun):
        QtCore.QThread.__init__(self)
        self._app               = app
        self._version           = version
        self._path_to_movie     = path_to_movie
        self._thumbnail_path    = thumbnail_path
        self._upload_to_shotgun = upload_to_shotgun
        self._errors            = []

    def get_errors(self):
        """
        can be called after execution to retrieve a list of errors
        """
        return self._errors

    def run(self):
        """
        Thread loop
        """
        upload_error = False

        if self._upload_to_shotgun:
            try:
                self._app.tank.shotgun.upload("Version", self._version["id"], self._path_to_movie, "sg_uploaded_movie")
            except:
                self._errors.append(
                    "Movie upload to Shotgun failed: {}".format(traceback.print_exc())
                )
                upload_error = True

        if not self._upload_to_shotgun or upload_error:
            try:
                self._app.tank.shotgun.upload_thumbnail("Version", self._version["id"], self._thumbnail_path)
            except:
                self._errors.append(
                    "Thumbnail upload to Shotgun failed: {}".format(traceback.print_exc())
                )
