# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

################################################################################

includes:
- ../app_locations.yml
- ../engine_locations.yml
- ./tk-multi-breakdown.yml
- ./tk-multi-loader2.yml
- ./tk-multi-publish2.yml
- ./tk-multi-shotgunpanel.yml
- ./tk-multi-snapshot.yml
- ./tk-multi-workfiles2.yml

################################################################################

# asset
settings.tk-vred.asset:
  apps:
    tk-multi-about:
      location: '@apps.tk-multi-about.location'
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel'
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2.vred'
  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  location: '@engines.tk-vred.location'
# asset_step
settings.tk-vred.asset_step:
  apps:
    tk-multi-about:
      location: '@apps.tk-multi-about.location'
    tk-multi-breakdown: '@settings.tk-multi-breakdown.vred'
    tk-multi-loader2: '@settings.tk-multi-loader2.vred'
    tk-multi-publish2: '@settings.tk-multi-publish2.vred.asset_step'
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel'
    tk-multi-snapshot: '@settings.tk-multi-snapshot.vred.asset_step'
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2.vred.asset_step'
  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-snapshot, name: Snapshot...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  - {app_instance: tk-multi-publish2, name: Publish...}
  render_template: vred_asset_render_work
  location: '@engines.tk-vred.location'
# project
settings.tk-vred.project:
  apps:
    tk-multi-about:
      location: '@apps.tk-multi-about.location'
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel'
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2.vred'
    mty-executeaction-ensure-folders:
      location: '@apps.tk-shotgun-executeaction.location'
      display_name: Ensure Tasks Folders
      action_hook: '{config}/tk-shotgun-executeaction/create_folders_for_assigned_tasks.py'
      sg_extended_fields: {}
      allowed_entities: [Shot, Sequence, Asset, Project]
  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  location: '@engines.tk-vred.location'
frameworks:
  tk-framework-lmv_v1.x.x:
    location:
      type: app_store
      name: tk-framework-lmv
      version: v1.1.0
