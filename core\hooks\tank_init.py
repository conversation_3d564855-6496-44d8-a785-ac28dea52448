################################################################################
#
# Copyright (c) 2024 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
################################################################################
"""
Hook that gets executed every time a new Toolkit API instance is created.
"""
import os
from tank import Hook

class TankInit(Hook):

    def execute(self, **kwargs):
        """
        Executed when a new Toolkit API instance is initialized.
        You can access the Toolkit API instance through ``self.parent``.

        """
        
        token = "****************************************"
        os.environ['SG_GITHUB_TOKEN_MIGHTYANIMATION'] = token