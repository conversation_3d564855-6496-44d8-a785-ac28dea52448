# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

description: Apps and engines loaded when a Shot is loaded. Since std VFX config
  template has a file system structure which is centered around pipeline steps,
  this environment is largely empty. Most of the work takes place on a level in
  the file system where both a shot and a pipeline step is available - e.g Shot
  ABC, modeling, so all apps for loading, publishing etc. are located in the
  shot_step environment. This environment mostly contains utility apps and the
  tank work files app, which lets you choose a task to work on and load
  associated content into an application.

################################################################################

includes:
- ./includes/frameworks.yml
- ./includes/settings/tk-3dsmax.yml
- ./includes/settings/tk-flame.yml
- ./includes/settings/tk-houdini.yml
- ./includes/settings/tk-maya.yml
- ./includes/settings/tk-motionbuilder.yml
- ./includes/settings/tk-nuke.yml
- ./includes/settings/tk-photoshopcc.yml
- ./includes/settings/tk-aftereffects.yml
- ./includes/settings/tk-shell.yml
- ./includes/settings/tk-shotgun.yml
- ./includes/settings/tk-harmony.yml
- ./includes/settings/tk-fusion.yml
- ./includes/settings/tk-krita.yml
- ./includes/settings/tk-blender.yml

################################################################################
# configuration for all engines to load in a shot context

engines:
  tk-3dsmax: "@settings.tk-3dsmax.shot"
  tk-flame: "@settings.tk-flame.shot"
  tk-houdini: "@settings.tk-houdini.shot"
  tk-maya: "@settings.tk-maya.shot"
  tk-motionbuilder: "@settings.tk-motionbuilder.shot"
  tk-nuke: "@settings.tk-nuke.shot"
  tk-photoshopcc: "@settings.tk-photoshopcc.shot"
  tk-aftereffects: "@settings.tk-aftereffects.shot"
  tk-shell: "@settings.tk-shell.shot"
  tk-shotgun: "@settings.tk-shotgun.shot"
  tk-harmony: "@settings.tk-harmony.shot"
  tk-fusion: "@settings.tk-fusion.shot"
  tk-krita: "@settings.tk-krita.shot"
  tk-blender: "@settings.tk-blender.shot"

################################################################################
# reference all of the common frameworks

frameworks: "@frameworks"
