# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

import os
import pprint

import sgtk
from sgtk.util.filesystem import ensure_folder_exists


__author__ = "Diego Garcia Hu<PERSON>"
__contact__ = "https://www.linkedin.com/in/diegogh/"


HookBaseClass = sgtk.get_hook_baseclass()


class SubstancePainterTexturesPublishPlugin(HookBaseClass):
    """
    Plugin for publishing an open Substance Painter session.

    This hook relies on functionality found in the base file publisher hook in
    the publish2 app and should inherit from it in the configuration. The hook
    setting for this plugin should look something like this::

        hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py"

    """

    # this is just a copy of the original hook from th eengine
    # we only need to patch teh way the fields are extracted
    # the default hook get them from context and we need them from file

    @property
    def settings(self):
        """
        Dictionary defining the settings that this plugin expects to receive
        through the settings parameter in the accept, validate, publish and
        finalize methods.
        A dictionary on the following form::
            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }
        The type string should be one of the data types that toolkit accepts as
        part of its environment configuration.
        """

        # inherit the settings from the base publish plugin
        base_settings = (
            super(SubstancePainterTexturesPublishPlugin, self).settings or {}
        )

        # settings specific to this class
        substancepainter_publish_settings = {
            "Publish Template": {
                "type": "template",
                "default": None,
                "description": "Template path for published texture files. Should"
                "correspond to a template defined in "
                "templates.yml.",
            },
            "Scene Work Template": {
                "type": "template",
                "default": None,
                "description": "Template path for the main scene work. Should"
                "correspond to a template defined in "
                "templates.yml.",
            }
        }

        # update the base settings
        base_settings.update(substancepainter_publish_settings)

        return base_settings

    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish. Returns a
        boolean to indicate validity.
        :param settings: Dictionary of Settings. The keys are strings, matching
                         the keys returned in the settings property. The values
                         are `Setting` instances.
        :param item: Item to process
        :returns: True if item is valid, False otherwise.
        """

        publisher = self.parent

        # populate the publish template on the item if found
        publish_template_setting = settings.get("Publish Template")
        publish_template = publisher.engine.get_template_by_name(
            publish_template_setting.value
        )
        if publish_template:
            item.properties["publish_template"] = publish_template
        else:
            error_msg = "Validation failed. Publish template not found"
            self.logger.error(error_msg)
            raise Exception(error_msg)

        path = item.properties["path"]

        if not item.properties["is_udim"] and not os.path.isfile(path):
            error_msg = (
                "Validation failed. Texture path does not exist on disk. %s" % path
            )
            self.logger.error(error_msg)
            raise Exception(error_msg)

        return True


    def publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.

        :param settings: Dictionary of Settings. The keys are strings, matching
                         the keys returned in the settings property.
                         The values are `Setting` instances.
        :param item: Item to process
        """

        publisher = self.parent

        publish_template = item.properties["publish_template"]
        publish_type = item.properties["publish_type"]

        src = item.properties["path"]

        filename = os.path.basename(src)
        filenamefile, extension = os.path.splitext(filename)

        # Get fields from the current scene file
        fields = {}
        scene_publish_template = publisher.engine.get_template_by_name(
            settings.get("Scene Work Template").value
        )
        # get the path to the current file
        scene_path = publisher.engine.app.get_current_project_path()
        scene_path_fields = scene_publish_template.get_fields(scene_path)
        fields.update(scene_path_fields)

        fields["extension"] = extension[1:]  # no dot

        if item.properties["is_udim"]:
            fields['UDIM'] = '<UDIM>'
            fields['channel'] = filenamefile.replace('.$udim', '')
        else:
            fields['channel'] = filenamefile

        publish_path = publish_template.apply_fields(fields)
        publish_path = sgtk.util.ShotgunPath.normalize(publish_path)

        publish_dir, filenamefile = os.path.split(publish_path)

        # make sure destination folder exists
        ensure_folder_exists(publish_dir)

        if item.properties["is_udim"]:
            for udim_number, udim_path in iter(item.properties["udims"].items()):
                fields['UDIM'] = udim_number
                udim_publish_path = publish_template.apply_fields(fields)
                udim_publish_path = sgtk.util.ShotgunPath.normalize(udim_publish_path)
                publisher.log_debug('Copying: {0} to {1}'.format(udim_path, udim_publish_path))
                sgtk.util.filesystem.copy_file(udim_path, udim_publish_path)
        else:
            sgtk.util.filesystem.copy_file(src, publish_path)

        self.logger.info("A Publish will be created in Shotgun and linked to:")
        self.logger.info("  %s" % (publish_path,))

        # arguments for publish registration

        # add dependencies
        dependency_paths = []
        if "sg_publish_path" in item.parent.properties:
            self.logger.debug(
                "Added dependency: %s" % item.parent.properties.sg_publish_path
            )
            dependency_paths.append(item.parent.properties.sg_publish_path)

        self.logger.info("Registering publish...")

        publish_name = publisher.util.get_publish_name(publish_path)

        publish_data = {
            "tk": publisher.sgtk,
            "context": item.context,
            "comment": item.description,
            "path": publish_path,
            "name": publish_name,
            "version_number": fields['version'],
            "thumbnail_path": item.get_thumbnail_as_path(),
            "published_file_type": publish_type,
            "dependency_paths": dependency_paths,
        }

        # log the publish data for debugging
        self.logger.debug(
            "Populated Publish data...",
            extra={
                "action_show_more_info": {
                    "label": "Publish Data",
                    "tooltip": "Show the complete Publish data dictionary",
                    "text": "<pre>%s</pre>" % (pprint.pformat(publish_data),),
                }
            },
        )

        # create the publish and stash it in the item properties for other
        # plugins to use.
        item.properties["sg_publish_data"] = sgtk.util.register_publish(**publish_data)

        # inject the publish path such that children can refer to it when
        # updating dependency information
        item.properties["sg_publish_path"] = publish_path

        self.logger.info("Publish registered!")

        # now that we've published. keep a handle on the path that was published
        item.properties["path"] = publish_path

