from tank import Hook
from tank.platform.qt import QtCore, QtGui

QDialog = QtGui.QDialog
QGridLayout = QtGui.QGridLayout
QLabel = QtGui.QLabel
QPushButton = QtGui.QPushButton
QVBoxLayout = QtGui.QVBoxLayout
QDoubleSpinBox = QtGui.QDoubleSpinBox
QGroupBox = QtGui.QGroupBox


class ProcessItemsHook(Hook):
    def execute(self, **kwargs):
        result = {"succes": [1], "messages": [], "errors": []}

        # define the function that will be called from Qt
        def change_line_thickness(number):
            cmd = """
include("harmony_utility_functions.js");

var original_line_thickness_attr = "mtyOriginalLineThickness"
var original_line_thickness_enabled_attr = "mtyOriginalLineThicknessEnabled"

function changeLineThickness() {
    var selectedNodesPropagated = get_nodes_and_subnodes("READ", get_selected_nodes());

    var numberOfNodesSelected = selection.numberOfNodesSelected();

    var number = %s;

    for (var i = 0; i < selectedNodesPropagated.length; i++) {
        log("Working on node: " + selectedNodesPropagated[i]);

        // first save the state of the ADJUST_PENCIL_THICKNESS attr to be able to reset it back to its original value
        var thickess_enabled_attr = node.getAttr(selectedNodesPropagated[i], frame.current(), original_line_thickness_enabled_attr);
        var thickess_enabled_attr_name = thickess_enabled_attr.name();
        if (thickess_enabled_attr_name == "") {
            // create the attribute and store the original value
            var new_attr = create_attr(selectedNodesPropagated[i], original_line_thickness_enabled_attr, "BOOL")
            var thickess_enabled_attr_value = get_attr_value(selectedNodesPropagated[i], "ADJUST_PENCIL_THICKNESS");
            // set the new attr value, which is the original thickness value
            node.setTextAttr(selectedNodesPropagated[i], original_line_thickness_enabled_attr, frame.current(), thickess_enabled_attr_value);
        } else {
            var thickess_enabled_attr_value = get_attr_value(selectedNodesPropagated[i], original_line_thickness_enabled_attr);
        }
        log("original thickness enabled value: " + thickess_enabled_attr_value);

        // then we need to ensure that the line thickness is tuned on
        node.setTextAttr(selectedNodesPropagated[i], "ADJUST_PENCIL_THICKNESS", frame.current(), "true");

        var thickess_attr = node.getAttr(selectedNodesPropagated[i], frame.current(), original_line_thickness_attr);
        var thickness_attr_name = thickess_attr.name();
        if (thickness_attr_name == "") {
            // create the attribute and store the original value
            var new_attr = create_attr(selectedNodesPropagated[i], original_line_thickness_attr, "DOUBLE")
            var thickess_attr_value = get_attr_value(selectedNodesPropagated[i], "MULT_LINE_ART_THICKNESS");
            // set the new attr value, which is the original thickness value
            node.setTextAttr(selectedNodesPropagated[i], original_line_thickness_attr, frame.current(), thickess_attr_value.toString());
        } else {
            var thickess_attr_value = get_attr_value(selectedNodesPropagated[i], original_line_thickness_attr);
        }
        log("original thickness value: " + thickess_attr_value);

        // multiply original thickness value by number
        var current_thickess_attr_value = get_attr_value(selectedNodesPropagated[i], "MULT_LINE_ART_THICKNESS");
        var new_thickness_value = current_thickess_attr_value * number;
        log("new thickness value: " + new_thickness_value);
        node.setTextAttr(selectedNodesPropagated[i], "MULT_LINE_ART_THICKNESS", frame.current(), new_thickness_value.toString());

        // set the scale dependent value to avoid size changes when a camera exists in the scene and its
        // Z value is other than zero
        node.setTextAttr(selectedNodesPropagated[i], "ZOOM_INDEPENDENT_LINE_ART_THICKNESS", frame.current(), "Scale Dependent");
    }

    return "";
}

changeLineThickness();
""" % number

            # execute the script in the engine
            self.parent.engine.app.execute(cmd)


        def reset_line_thickness():
            cmd = """
include("harmony_utility_functions.js");

var original_line_thickness_attr = "mtyOriginalLineThickness"
var original_line_thickness_enabled_attr = "mtyOriginalLineThicknessEnabled"

function resetLineThickness() {
    var selectedNodesPropagated = get_nodes_and_subnodes("READ", get_selected_nodes());

    log("selectedNodesPropagated:");
    log(JSON.stringify(selectedNodesPropagated));

    for (var i = 0; i < selectedNodesPropagated.length; i++) {
        var orig_thickness_attr_value = get_attr_value(selectedNodesPropagated[i], original_line_thickness_attr);
        if (orig_thickness_attr_value == null || orig_thickness_attr_value == "") {
            orig_thickness_attr_value = 1;
        }
        var orig_thickness_enabled_attr_value = get_attr_value(selectedNodesPropagated[i], original_line_thickness_enabled_attr);
        if (orig_thickness_enabled_attr_value == null || orig_thickness_enabled_attr_value == "") {
            orig_thickness_enabled_attr_value = false;
        }
        node.setTextAttr(selectedNodesPropagated[i], "MULT_LINE_ART_THICKNESS", frame.current(), orig_thickness_attr_value.toString());

        var node_attr = node.getAttr(selectedNodesPropagated[i], frame.current(), "ADJUST_PENCIL_THICKNESS");
        node_attr.setValue(orig_thickness_enabled_attr_value);
        // node.setTextAttr(selectedNodesPropagated[i], "ADJUST_PENCIL_THICKNESS", frame.current(), orig_thickness_enabled_attr_value);
    }

    // modify_attr(selectedNodesPropagated, "ADJUST_PENCIL_THICKNESS", false);
    // modify_attr(selectedNodesPropagated, "MULT_LINE_ART_THICKNESS", 1);
    return "";
}

resetLineThickness();
"""

            # execute the script in the engine
            self.parent.engine.app.execute(cmd)


        # create the dialog
        dialog = QDialog()
        dialog.setWindowTitle("Multiply Line Thickness Value")
        layout = QVBoxLayout()
        dialog.setLayout(layout)

        # Instructions group
        instructions_group = QGroupBox("Instructions")
        instructions_layout = QVBoxLayout()
        instructions_msg = (
            "The input value will be used as multiplier for the\n"
            "current line thickness value.\n\n"
            "If you want to reset line thickness attributes on\n"
            "all selected nodes, click on the 'Reset' button."
        )
        instructions_label = QLabel(instructions_msg)
        instructions_layout.addWidget(instructions_label)
        instructions_group.setLayout(instructions_layout)

        # Add Instructions group to the main layout
        layout.addWidget(instructions_group)

        # create the input field
        input_label = QLabel(
            "\nNew Line Thickness Multiplier\n\n(current line thickness value "
            "will be multiplied by this value):")
        input_field = QDoubleSpinBox()
        input_field.setRange(0, 10)
        input_field.setSingleStep(0.5)
        input_field.setValue(1)
        layout.addWidget(input_label)
        layout.addWidget(input_field)

        # create the buttons
        button_layout = QGridLayout()
        layout.addLayout(button_layout)
        update_button = QPushButton("Update")
        update_button.clicked.connect(lambda: change_line_thickness(input_field.text()))
        reset_button = QPushButton("Reset")
        reset_button.clicked.connect(lambda: reset_line_thickness())
        close_button = QPushButton("Close")
        close_button.clicked.connect(dialog.close)
        button_layout.addWidget(update_button, 0, 0)
        button_layout.addWidget(reset_button, 0, 1)
        button_layout.addWidget(close_button, 0, 2)

        # show the dialog
        dialog.exec_()

        return result
