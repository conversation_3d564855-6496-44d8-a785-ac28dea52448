import json
import socket
import getpass
from pprint import pformat as pf

import sgtk

HookBaseClass = sgtk.get_hook_baseclass()


class ResolveTransferBackends(HookBaseClass):

    def collect_backends(self):
        engine = self.parent

        overrides = engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        ) or engine.load_framework("mty-framework-valueoverrides")

        presto = engine.custom_frameworks.get(
            "mty-framework-presto"
        ) or engine.load_framework("mty-framework-presto")
        presto.prestoCore.set_binary(binary='cli')

        ftp = engine.custom_frameworks.get(
            "mty-framework-ftp"
        ) or engine.load_framework("mty-framework-ftp")

        login = engine.custom_frameworks.get(
            "mty-framework-login"
        ) or engine.load_framework("mty-framework-login")

        backends = {
            'presto': presto.prestoCore,
            'ftp': ftp.FTPCore,
            'login': login,
            'overrides': overrides
        }

        engine.logger.info("available_backends:\n{}".format(pf(backends)))

        return backends


    def resolve_backend_overrides(self, available_backends):
        engine = self.parent

        override_backend = None
        override_domain = None
        override_root = None

        if not "overrides" in available_backends:
            engine.logger.warning(
                'An "overrides" backend is not present, can\'t '
                "resolve overrides for transfer backend nor host!"
            )
            return override_backend, override_domain, override_root

        overrides_framework = available_backends["overrides"]

        # get per project backend override ---------------------------------------------
        project_backend_overrides = overrides_framework.get_value(
            "mty.metasync.transfer.backend_per_project"
        )
        engine.logger.info(
            "mty.metasync.transfer.backend_per_project value:\n{}".format(
                project_backend_overrides
            )
        )
        if project_backend_overrides:
            project_backend_overrides = json.loads(project_backend_overrides)
            override_backend = project_backend_overrides.get("backend", None)
            override_domain = project_backend_overrides.get("host", None)
            override_root = project_backend_overrides.get("root", None)

        # get per user backend override ------------------------------------------------
        user_backend_overrides = overrides_framework.get_value(
            "mty.metasync.transfer.backend_per_user"
        )
        if user_backend_overrides:
            user_backend_overrides = json.loads(user_backend_overrides)
        engine.logger.info(
            "mty.metasync.transfer.backend_per_user value:\n{}".format(
                user_backend_overrides
            )
        )

        current_host = socket.gethostname()
        current_user = getpass.getuser()

        if (
            isinstance(user_backend_overrides, dict)
            and current_user in user_backend_overrides.keys()
        ):
            user_overrides = user_backend_overrides[current_user]
            if current_host in user_overrides:
                override_backend = user_overrides.get(current_host, {}).get("backend", None)
                override_domain = user_overrides.get(current_host, {}).get("host", None)
                override_root = user_overrides.get(current_host, {}).get("root", None)
                message = (
                    "Resolved '{}' transfer backend "
                    "and '{}' host with root '{}', from user overrides!"
                )
                engine.logger.info(
                    message.format(override_backend, override_domain, override_root)
                )

        return override_backend, override_domain, override_root

    def resolve_project_transfer_backend(self, available_backends):
        backend_overrides = self.resolve_backend_overrides(available_backends)
        override_backend = backend_overrides[0]
        override_domain = backend_overrides[1]
        override_root = backend_overrides[2]

        project_location_info = self.get_project_location_info()

        if (
            override_backend
            and override_domain
            and override_root
            ):
            host_backend = override_backend
            host_domain = override_domain
            host_root = override_root
        else:
            host_backend = project_location_info.get("sg_transfers_backend")
            host_domain = project_location_info.get("sg_host_domain")
            host_root = project_location_info.get("sg_host_root")

        return host_backend, host_domain, host_root

    def get_project_location_info(self):
        """
        Defines a method getinfo that retrieves information from Shotgrid.
        """
        engine = self.parent

        location_entity_type = "CustomNonProjectEntity12"

        location_fields = [
            "sg_system_id",
            "sg_ip_address",
            "sg_admin",
            "code",
            "sg_auth_type",
            "sg_admin.HumanUser.login",
            "sg_status_list",
            "sg_host_domain",
            "sg_access_port",
            "sg_location_icon",
            "sg_location_image",
            "sg_toolengines",
            "sg_force_order_by_size",
            "sg_ignore_publishes_checksum",
            "sg_ignore_tools_checksum",
            "sg_default_pulling_task",
            "sg_require_nearline",
            "sg_type",
            "sg_ignore_types",
            "sg_limitation",
            "sg_before_task",
            "sg_after_task",
            "sg_data_access_login",
            "sg_upload_production_status",
            "sg_upload_direction_status",
            "sg_validation_for_local_files",
            "sg_user_domain",
            "sg_host_root",
            "sg_transfers_backend",
        ]

        result = engine.shotgun.find_one(
            location_entity_type,
            [["sg_projects", "is", engine.context.project]],
            location_fields,
        )

        return result
