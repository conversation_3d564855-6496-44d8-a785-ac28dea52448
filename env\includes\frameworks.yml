# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

################################################################################
# A single, top-level setting is defined: "@frameworks". This setting can
# be included and referenced wherever shared frameworks are required.
includes:
- ./settings/validations.yml

frameworks:
  mty-framework-valueoverrides:
    location:
      type: github_release
      organization: mightyanimation
      repository: mty-framework-valueoverrides
      version: v0.3.2
      private: true
      # type: dev
      # path: Q:\mty-framework-valueoverrides

  mty-framework-credentials:
    location:
      type: github_release
      organization: mightyanimation
      repository: mty-framework-credentials
      version: v0.0.1-4
      private: true
      # type: dev
      # path: Q:\mty-framework-credentials

  mty-framework-timelogs:
    location:
      type: github_release
      organization: mightyanimation
      repository: mty-framework-timelogs
      version: v0.1.6
      private: true
      # type: dev
      # path: Q:\mty-framework-timelogs

  mty-framework-coremayatools:
    location:
      type: github_release
      organization: mightyanimation
      repository: mty-framework-coremayatools
      version: v0.6.5-0
      private: true
      # type: dev
      # path: Q:\mty-framework-coremayatools

  mty-framework-coremayatools_v704:
    location:
      type: github_release
      organization: mightyanimation
      repository: mty-framework-coremayatools
      version: v0.6.5-0
      private: true

  mty-framework-autorig:
    location:
      type: github_release
      organization: mightyanimation
      repository: mty-framework-autorig
      version: v0.2.0
      private: true
      # type: dev
      # path: C:/Autorig/Mighty/mty-framework-autorig

  mty-framework-psdutils:
    location:
      type: github_release
      organization: mightyanimation
      repository: mty-framework-psdutils
      version: v0.0.3
      private: true
      # type: dev
      # path: Q:\mty-framework-psdutils
    override_version_name: "mty.framework.psdutils.verisonPsdTools"

  mty-framework-validations:
    location:
      type: github_release
      organization: mightyanimation
      repository: mty-framework-validations
      version: v0.0.7
      private: true
      # type: dev
      # path: Q:\mty-framework-validations
    context_validations: '@validations'

  mty-framework-aws:
    location:
      type: github_release
      organization: mightyanimation
      repository: mty-framework-aws
      version: v0.0.1-1
      private: true

  mty-framework-deliveries:
    location:
      type: github_release
      organization: mightyanimation
      repository: mty-framework-deliveries
      version: v0.0.4-4
      private: true
      # type: dev
      # path: Q:\mty-framework-deliveries
    mappings_hook: '{config}/mty-deliveries/mappings.py'
    transfer_backends_hook: '{config}/mty-deliveries/transfer_backends.py'
    temporal_folder_template: deliveries_temporal_root
    ingest_plugins:
    - name: IngestionTestPlugin
      hook: '{self}/ingest_plugin.py:{config}/mty-deliveries/ingest_test_plugin.py'
      settings:
        supported_entity_identifiers: [Rig High]
        supported_entity_types: {Asset: Asset}
        supported_task_names: {Rig High: FullRig}
        required_metadata_keys: [asset_type]
        published_file_templates:
          Main File: maya_asset_publish
        published_file_types:
          Main File: Maya Scene
    outgest_plugins:
    - name: OutgestionTestPlugin
      hook: '{self}/outgest_plugin.py:{config}/mty-deliveries/outgest_test_plugin.py'
      settings:
        supported_entity_identifiers: [Media Review]
        supported_entity_types: {Asset: Asset, Shot: Shot}
        supported_task_names:
          Compositing: cmpComp
          Color: bgColor
          Rig: rigRig
          designColor: artDesign
          mdlModeling: mxMdlModeling
          sfcShading: mxSfcShading
          rig2dRigging: mxRig2dRigging
          bgRough: mxBgRough
          bgClean: mxbgClean
          bgColor: mxBgColor
          chlClean: mxChlClean
          plsRough: mxPlsRough
          plsClean: mxPlsClean
          plsColor: mxPlsColor
          plsRetake: mxPlsRetake
          plsFix: mxPlsFix
          cutPosing: mxCutPosing
          cutInBetween: mxCutInBetween
          cutRetake: mxCutRetake
          cutFix: mxCutFix
          lgtFills: mxLgtFills
          fx2dFinish: mxFx2dFinish
          fx2dRetake: mxFxRetake
          fx2dFix: mxFxFix
          cmpComp: mxCmpComp
          cmpRetake: mxCmpRetake
          cmpFix: mxCmpFix
        replace_studio_name_with_client_name: false
    - name: OutgestionTestPlugin
      hook: '{self}/outgest_plugin.py:{config}/mty-deliveries/outgest_package_plugin.py'
      settings:
        supported_entity_identifiers:
        - After Effects Project
        - Toon Boom Harmony Project File
        - Harmony TPL
        - Photoshop Image
        - Photoshop Proxy Image
        - Photoshop Layer Group Image
        - Rendered Image
        supported_entity_types: {Asset: Asset, Shot: Shot}
        supported_task_names:
          Compositing: cmpComp
          Color: bgColor
          Rig: rigRig
          designColor: artDesign
          mdlModeling: mxMdlModeling
          sfcShading: mxSfcShading
          rig2dRigging: mxRig2dRigging
          bgRough: mxBgRough
          bgClean: mxbgClean
          bgColor: mxBgColor
          chlClean: mxChlClean
          plsRough: mxPlsRough
          plsClean: mxPlsClean
          plsColor: mxPlsColor
          plsRetake: mxPlsRetake
          plsFix: mxPlsFix
          cutPosing: mxCutPosing
          cutInBetween: mxCutInBetween
          cutRetake: mxCutRetake
          cutFix: mxCutFix
          lgtFills: mxLgtFills
          fx2dFinish: mxFx2dFinish
          fx2dRetake: mxFxRetake
          fx2dFix: mxFxFix
          cmpComp: mxCmpComp
          cmpRetake: mxCmpRetake
          cmpFix: mxCmpFix
        replace_studio_name_with_client_name: false
        update_client_task_status: false
        desired_client_task_status: rev

  mty-framework-ffmpeg:
    location:
      type: github_release
      organization: mightyanimation
      repository: mty-framework-ffmpeg
      version: v0.0.8
      private: true
      # type: dev
      # path: Q:\mty-framework-ffmpeg

  mty-framework-mediatools:
    location:
      type: github_release
      organization: mightyanimation
      repository: mty-framework-mediatools
      version: v0.0.2-6
      private: true

  mty-framework-mayacapture:
    location:
      type: github_release
      organization: mightyanimation
      repository: mty-framework-mayacapture
      version: v0.0.3
      private: true

  mty-framework-metasync:
    location:
      type: github_release
      organization: mightyanimation
      repository: mty-framework-metasync
      version: v0.0.14-14
      private: true
      # type: dev
      # path: Q:\mty-framework-metasync

  mty-framework-opencolorio:
    location:
      type: github_release
      organization: mightyanimation
      repository: mty-framework-opencolorio
      version: v0.0.6-0
      private: true
      # type: dev
      # path: Q:\mty-framework-opencolorio

  # mty-framework-djv:
  #   location:
  #     type: github_release
  #     organization: mightyanimation
  #     repository: mty-framework-djv
  #     version: v0.0.3-0
  #     private: true

  mty-framework-deadline:
    location:
      type: github_release
      organization: mightyanimation
      repository: mty-framework-deadline
      version: v0.1.2
      private: true

  mty-framework-imagemagick:
    location:
      type: github_release
      organization: mightyanimation
      repository: mty-framework-imagemagick
      version: v0.0.5-6
      private: true

  mty-framework-openimageio:
    location:
      type: github_release
      organization: mightyanimation
      repository: mty-framework-openimageio
      version: v0.0.4-2
      private: true

  mty-framework-openexr:
    location:
      type: github_release
      organization: mightyanimation
      repository: mty-framework-openexr
      version: v0.0.1-1
      private: true

  mty-framework-ftp:
    location:
      type: github_release
      organization: mightyanimation
      repository: mty-framework-ftp
      version: v0.0.2-1
      private: true
    ftp_options:
      timeout: 20
      max_threads: 10

  mty-framework-presto:
    location:
      type: github_release
      organization: mightyanimation
      repository: mty-framework-presto
      version: v0.0.4-1
      private: true
    common_flags:
    - --overwrite=newer
    - --encryption
    - --verbose
    - --auto-retry=5
    - --concurrent=10
    - --allow-untrust
    #  - "--compression"

  mty-framework-login:
    location:
      type: github_release
      organization: mightyanimation
      repository: mty-framework-login
      version: v0.0.5-1
      private: true
      # type: dev
      # path: Q:\mty-framework-login

  # mty-framework-opentimelineio:
  #   location:
  #     type: github_release
  #     organization: mightyanimation
  #     repository: mty-framework-opentimelineio
  #     version: v0.0.1-0
  #     private: true

  # mty-framework-exocortexcreate:
  #   location:
  #     field: sg_payload
  #     version: 4688
  #     type: shotgun
  #     name: mty-framework-exocortexcreate
  #     entity_type: CustomNonProjectEntity10
  #     type: dev
  #     path: C:\Development\Mighty\repo\apps\mty-framework-exocortexcreate

  #mty-framework-externalpython:
  #  location:
  #    type: dev
  #    path: C:\Development\Mighty\repo\apps\mty-framework-externalpython

  tk-framework-remotestorage_v1.x.x:
    provider_hook: '{config}/example_local_provider.py'
    location:
      type: github_release
      organization: shotgunsoftware
      repository: tk-framework-remotestorageexample
      version: v1.0.0

  # adobe - Common functionality for adobe products
  tk-framework-adobe_v1.x.x:
    location:
      type: app_store
      name: tk-framework-adobe
      version: v1.2.10
  # adminui - provides GUIs for administrative commands
  tk-framework-adminui_v0.x.x:
    location:
      type: app_store
      name: tk-framework-adminui
      version: v0.8.0
  # aliastranslations - provides Alias translation tools
  tk-framework-aliastranslations_v0.x.x:
    location:
      type: app_store
      name: tk-framework-aliastranslations
      version: v0.3.0
  # desktopserver - provides a server to run local commands.
  tk-framework-desktopserver_v1.x.x:
    location:
      type: app_store
      name: tk-framework-desktopserver
      version: v1.8.2
  # qtwidgets - collection of Toolkit related QT Widgets
  tk-framework-qtwidgets_v2.x.x:
    location:
      type: app_store
      name: tk-framework-qtwidgets
      version: v2.12.6
  # shotgunutils v4 - Shotgun Related Utilities
  tk-framework-shotgunutils_v4.x.x:
    location:
      version: v4.4.15
      type: app_store
      name: tk-framework-shotgunutils

  # shotgunutils v5 - Shotgun Related Utilities
  tk-framework-shotgunutils_v5.x.x:
    location:
      type: app_store
      name: tk-framework-shotgunutils
      version: v5.10.2
  # widget - QT Widget Framework for Tank Apps (qtwidgets fw predecessor)
  tk-framework-widget_v0.2.x:
    location:
      version: v0.2.8
      type: app_store
      name: tk-framework-widget

  # desktopclient - A client for Shotgun Create
  tk-framework-desktopclient_v0.x.x:
    location:
      type: app_store
      name: tk-framework-desktopclient
      version: v0.4.0

  tk-framework-editorial_v1.x.x:
    location:
      version: v1.4.1
      type: app_store
      name: tk-framework-editorial

  # unrealqt - PySide build for Unreal (Windows-only)
  # also usefull for dccs that does not include python qt bindings
  tk-framework-unrealqt_v1.x.x:
    location:
      path: https://github.com/shotgunsoftware/tk-framework-unrealqt.git
      version: c092ea92e87fa50e6108f6e103ea06811bb8f416
      type: git_branch
      branch: master

  # Adobe Framework patched to support Adobe Premiere Pro CC
  tk-framework-adobe-ppro_v1.x.x:
    location:
      type: github_release
      organization: Vintata
      repository: tk-framework-adobe-ppro
      # path: https://github.com/Vintata/tk-framework-adobe-ppro.git
      # important here that version doesn't have "v" because
      # the taging in the original repo doesn't have it ether
      version: 1.0.5
