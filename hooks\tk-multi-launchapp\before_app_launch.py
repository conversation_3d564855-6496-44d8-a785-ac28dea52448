# Copyright (c) 2013 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

"""
Before App Launch Hook

This hook is executed prior to application launch and is useful if you need
to set environment variables or run scripts as part of the app initialization.
"""

import os
import re
import sys
import json
import socket
import pprint
import pathlib
import getpass
import subprocess

import tank
import sgtk

# -- -- -- -- -- -- -- -- -- -- -- -- -- --
# project:
hook_baseclass = sgtk.get_hook_baseclass()
# =============================================================================
pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)
pf = pprint.pformat


class BeforeAppLaunch(tank.Hook):
    """
    Hook to set up the system prior to app launch.
    """

    def execute(
        self, app_path, app_args, version, engine_name, software_entity=None, **kwargs
    ):
        """
        The execute function of the hook will be called prior to starting the required application

        :param app_path: (str) The path of the application executable
        :param app_args: (str) Any arguments the application may require
        :param version: (str) version of the application being run if set in the
            "versions" settings of the Launcher instance, otherwise None
        :param engine_name (str) The name of the engine associated with the
            software about to be launched.
        :param software_entity: (dict) If set, this is the Software entity that is
            associated with this launch command.
        """

        # save current software path for later
        os.environ["CURRENT_SOFTWARE_PATH"] = app_path

        # accessing the current context (current shot, etc)
        # can be done via the parent object
        #
        # > multi_launchapp = self.parent
        # > current_entity = multi_launchapp.context.entity

        # you can set environment variables like this:
        # os.environ["MY_SETTING"] = "foo bar"

        current_paths = os.environ["PATH"].split(os.pathsep)
        paths = []

        pythonpath_fallbacks = []

        # ==============================================================================
        # tk-blender
        # ==============================================================================
        if engine_name == "tk-blender":

            # we need to make sure proper pyside version for blender version
            # first check blender version
            self.parent.logger.info("version: {}".format(version))
            if float(version) >= 5.0:
                # we need PySide6, but we know it doesnt work yet
                # show error dialog
                msg = (
                    "Blender version {} needs PySide6 but current "
                    "Integration does not suport it yet"
                ).format(version)
                message_box = self.show_version_error_message("Blender", msg)
                self.parent.logger.error(msg)

                # stop launch execution
                raise Exception(msg)
            else:
                # proceed to verify PySide2 location or download it
                python_version = "3.10"
                studio_install_root = pathlib.Path(
                    self.parent.engine.sgtk.pipeline_configuration.get_install_location()
                ).parent.parent

                PYSIDE_PYTHONPATH = os.path.join(
                    studio_install_root, "pip-packages", 
                    "PySide2", "py{}".format(python_version)
                )

                if not os.path.exists(PYSIDE_PYTHONPATH):
                    self.parent.logger.info(
                        "creating directory: {}".format(PYSIDE_PYTHONPATH)
                    )
                    os.makedirs(PYSIDE_PYTHONPATH)

                # verify contents
                contents = os.listdir(PYSIDE_PYTHONPATH)
                if not "PySide2" in contents or not "shiboken2" in contents:
                    # proceed with the download and install
                    blender_path = os.path.dirname(app_path)
                    python_path = os.path.join(
                        blender_path, version, "python", "bin", "python.exe"
                    )

                    # run install command
                    command = [
                        python_path, "-m", "pip", "install", 
                        "PySide2", "--target", PYSIDE_PYTHONPATH
                    ]
                    self.parent.logger.info("Running: {}".format(command))
                    self.parent.engine.show_busy(
                        "Installing PySide", 
                        "This can take a while, please wait..."
                    )
                    process = subprocess.Popen(
                        command, 
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE
                    )
                    stdout, stderr = process.communicate()
                    self.parent.engine.clear_busy()
                    if process.returncode != 0:
                        self.parent.logger.info(stdout)
                        self.parent.logger.error(stderr)
                        msg = (
                            "PySide failed to install.\n"
                            "Blender pipeline integration needs PySide to work properly.\n"
                            "Please Contact support and share your log files."
                        )
                        message_box = self.show_version_error_message("Blender", msg)
                        self.parent.logger.error(msg)
                        # stop launch execution
                        raise Exception(msg)

            # Official tk-blender custom pyside2 path
            os.environ["PYSIDE2_PYTHONPATH"] = PYSIDE_PYTHONPATH
            # current engine version is explicitly expecting PySide2
            # which means that it will not work with PySide6 on Python 3.11+
            # last Blender with 3.10 is 4.0

            # temporal patch to ignore compatibility test
            # if imported when qt is not avilable it will crash
            os.environ["SGTK_COMPATIBILITY_DIALOG_SHOWN"] = "0"
            pass
        # ==============================================================================
        # tk-maya
        # ==============================================================================

        if engine_name == "tk-maya":

            """
            exocortex.exocortexCore.build_config_paths(version)
            exocortex.exocortexCore.set_maya_envs()
            # we also need to save a pointer to the plugin
            # so we can later use it to verify if its loaded or not
            # plugin_path = exocortex.exocortexCore.get_config_paths()[
            #     'MAYA_PLUG_IN_PATH']
            # os.environ['MAYA_EXOCORTEX_PLUGIN'] = os.path.join(
            #     plugin_path, 'MayaExocortexAlembic.mll')
            # self.parent.logger.debug("MAYA_EXOCORTEX_PLUGIN: {0}".format(
            #     os.environ['MAYA_EXOCORTEX_PLUGIN']))

            # set maya not to use color management
            # https://knowledge.autodesk.com/support/maya/learn-explore/caas/CloudHelp/cloudhelp/2018/ENU/Maya-Rendering/files/GUID-2A714F4D-7768-41D0-89F4-9C0062E6CA6D-htm.html
            hooks_folder = os.path.dirname(self.disk_location)
            color_management_policy_file_path = os.path.join(
                hooks_folder, 'maya_color_management', 'defaultColorManagementPolicy.xml')
            os.environ['MAYA_COLOR_MANAGEMENT_POLICY_FILE'] = color_management_policy_file_path
            os.environ['MAYA_COLOR_MANAGEMENT_POLICY_LOCK'] = '1'
            """

            def get_maya_version_from_mayapy(mayapy_path):
                try:
                    # Run mayapy and use maya.standalone to initialize the environment
                    cmd = [
                        mayapy_path,
                        "-c",
                        "import maya.standalone; maya.standalone.initialize(name='python'); "
                        "import maya.cmds as cmds; print(cmds.about(version=True))"
                    ]
                    maya_version_process = subprocess.Popen(
                        cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        shell=True,
                        encoding="utf-8"
                    )
                    stdout, stderr = maya_version_process.communicate()
                    # self.parent.logger.info("mayapy stdout: {}".format(stdout))
                    # self.parent.logger.info("mayapy stderr: {}".format(stderr))

                    stdout = stdout.strip()
                    stdout_split = re.split(r"[\s\r\n]+", stdout)
                    version = stdout_split[-1]

                    self.parent.logger.info("stdout_split: {}".format(stdout_split))
                    self.parent.logger.info("Maya version: {}".format(version))

                    return version
                except Exception as e:
                    return f"Error: {e}"


            # Validate maya version
            import sgtk

            engine = sgtk.platform.current_engine()

            # --------------------------------------------------------------------------
            # Check for restricted maya versions per project ---------------------------

            # get per project allowed maya version from the software entities
            restricted_maya_versions = self.get_project_versions(engine_name)
            self.parent.logger.info(
                "Restricted {} versions for project {}: {}".format(
                    engine_name,
                    engine.context.project.get("name"),
                    restricted_maya_versions
                )
            )

            # TODO: find a way to do this without pymel
            # # only check the current Harmony version if restrictions could be found in
            # # the software entities
            # if restricted_maya_versions:
            #     # get executable to check current harmony version
            #     software_path = os.environ.get("CURRENT_SOFTWARE_PATH")
            #     executable_path = os.path.join(
            #         os.path.dirname(software_path), "mayapy"
            #     )

            #     # check maya version using command line
            #     maya_version = get_maya_version_from_mayapy(executable_path)

            #     if "Error" not in maya_version:
            #         self.parent.logger.info(
            #             "Current Maya version: {}".format(maya_version)
            #         )
            #     else:
            #         self.parent.logger.error(maya_version)

            #     if maya_version not in restricted_maya_versions:
            #         msg = (
            #             "Current maya version {} is not allowed for this project. "
            #             "Allowed versions: {}"
            #         ).format(maya_version, restricted_maya_versions)
            #         # self.show_version_error_message(engine.name, msg)

            #         # stop launch execution
            #         raise Exception(msg)


        # ==============================================================================
        # tk-harmony
        # ==============================================================================

        if engine_name == "tk-harmony":
            import sgtk

            engine = sgtk.platform.current_engine()

            # --------------------------------------------------------------------------
            # Check for restricted harmony versions per project ------------------------

            # get per project allowed harmony version from the software entities
            restricted_harmony_versions = self.get_project_versions("tk-harmony")
            self.parent.logger.info(
                "Restricted Harmony versions for project {}: {}".format(
                    engine.context.project.get("name"), restricted_harmony_versions
                )
            )

            def validate_or_not(user, host_name, allowed_users_dict):
                if user in allowed_users_dict.keys():
                    user_hosts_list = allowed_users_dict.get(user)
                    if host_name in user_hosts_list:
                        return True

                return False

            allowed_users_dict = {
                # "Luisa": ["MLSV"],
                "render": ["MightyWS53"],
                # "micro": ["DESKTOP-A4DUFDO"],
                # "kurok": ["LAPTOP-HT09R44B"],
                "orlando": ["DESKTOP-LS2J6OB"],
                "Orlando": ["microxx-lap"],
                "Chavador": ["DESKTOP-2KVF9HG"],
                "garabatoons": ["MacBook-Air-de-Guillermo.local"],
                "Hiram": ["DESKTOP-0CCTK5C"],
            }

            # get curent pc user
            user = getpass.getuser()
            # get curent pc host
            host_name = socket.gethostname()

            proceed_without_version_validation = validate_or_not(
                user, host_name, allowed_users_dict
            )
            # proceed_without_version_validation = False

            if proceed_without_version_validation:
                restricted_harmony_versions = []

            # only check the current Harmony version if restrictions could be found in
            # the software entities
            if restricted_harmony_versions:
                # get executable to check current harmony version
                software_path = os.environ.get("CURRENT_SOFTWARE_PATH")
                executable_path = os.path.join(
                    os.path.dirname(software_path), "uinfo.exe"
                )

                # build the command to get the harmony version
                command = [executable_path, "--version"]

                # check harmony version using command line
                harmony_version = subprocess.Popen(
                    command,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    shell=True,
                    encoding="utf-8"
                )
                stderr, stdout = harmony_version.communicate()
                self.parent.logger.debug("uinfo stdout: {}".format(stdout))
                self.parent.logger.debug("uinfo stderr: {}".format(stderr))

                version_pattern = re.compile(
                    r".+\s(?P<version>\d+\.\d\.\d).+\s(?P<build>\d+)\s"
                )
                version_match = re.search(version_pattern, stdout)
                if version_match:
                    current_harmony_version = version_match.groupdict().get("version")

                    if current_harmony_version not in restricted_harmony_versions:
                        if len(restricted_harmony_versions) == 1:
                            msg = (
                                "You are running Harmony version {}, but only "
                                "version {} is allowed in this project."
                            ).format(
                                current_harmony_version, restricted_harmony_versions[0]
                            )
                        else:
                            msg = (
                                "You are running Harmony version {}, but only "
                                "versions {} are allowed in this project."
                            ).format(
                                current_harmony_version, restricted_harmony_versions
                            )

                        # show error dialog
                        message_box = self.show_version_error_message("Harmony", msg)

                        self.parent.logger.error(msg)

                        # stop launch execution
                        raise Exception(msg)
                    else:
                        self.parent.logger.info(
                            (
                                "Harmony version matches the allowed project version "
                                "or there are no Harmony version restrictions for "
                                "this project."
                            )
                        )
                else:
                    self.parent.logger.error(
                        "Couldn't get a match for a harmony version in stdout:\n{}".format(
                            stdout
                        )
                    )
            else:
                self.parent.logger.info(
                    (
                        "Harmony version matches the allowed project version "
                        "or there are no Harmony version restrictions for "
                        "this project."
                    )
                )

            # finished checking for restricted Harmony versions ------------------------
            # --------------------------------------------------------------------------

            # set a generic config driven folder for harmony scripts

            hooks_location = os.path.dirname(os.path.dirname(self.disk_location))

            harmony_scripts = os.path.join(
                hooks_location, "hooks", "tk-harmony", "scripts"
            )

            os.environ["TOONBOOM_GLOBAL_SCRIPT_LOCATION"] = harmony_scripts

        # ==============================================================================
        # tk-aftereffects
        # ==============================================================================

        # ==============================================================================
        # All engines
        # ==============================================================================

        # Disable unnecessary apps in PATH ---------------------------------------------
        # # FFMPEG
        # self.parent.logger.debug("ingecting ffmpeg bin in path")
        # ffmpeg = self.load_framework("mty-framework-ffmpeg")
        # ffmpeg_path = os.path.dirname(ffmpeg.ffmpegCore.get_bin_path())
        # self.parent.logger.debug("ffmpeg path: %s" % ffmpeg_path)
        # paths.append(ffmpeg_path)
        # # DJV
        # self.parent.logger.debug("injecting djv bin in path")
        # djv = self.load_framework("mty-framework-djv")
        # djv_path = os.path.dirname(djv.djvCore.get_bin_path())
        # self.parent.logger.debug("djv path: %s" % djv_path)
        # paths.append(djv_path)

        # # OpenImageIO
        # self.parent.logger.debug("injecting OpenImageIO bin in path")
        # oiio = self.load_framework("mty-framework-openimageio")
        # oiio_path = os.path.dirname(oiio.openImageIOCore.get_bin_path())
        # self.parent.logger.debug("oiio path: %s" % oiio_path)
        # paths.append(oiio_path)

        # # ImageMagick
        # self.parent.logger.debug("injecting imagemagick bin in path")
        # imagemagick = self.load_framework("mty-framework-imagemagick")
        # imagemagick_path = os.path.dirname(imagemagick.imageMagickCore.get_bin_path())
        # self.parent.logger.debug("imagemagick path: %s" % imagemagick_path)
        # # We know that on windows there is already a "convert" command
        # # and that we can't replace System PATH order, so we need a custom var
        # os.environ["IMAGE_MAGICK"] = imagemagick_path

        # # Presto
        # self.parent.logger.debug("injecting presto bin in path")
        # presto = self.load_framework("mty-framework-presto")
        # presto_path = os.path.dirname(presto.prestoCore.get_bin_path())
        # self.parent.logger.debug("presto path: %s" % presto_path)
        # paths.append(presto_path)

        # # Update PATH with new paths
        # new = os.pathsep.join(current_paths + paths).replace(os.pathsep * 2, os.pathsep)
        # os.environ["PATH"] = new

        # OCIO, only for final render engines
        # import sgtk
        # engine= sgtk.platform.current_engine()
        import sgtk

        engine = sgtk.platform.current_engine()
        proyect_id = engine.context.project["id"]
        sg = engine.shotgun

        fields = ["sg_output_color_space", "sg_working_color_space"]
        project_sg = sg.find_one(
            "Project", [["id", "is", proyect_id]], fields
        )
        working_colourspace = project_sg["sg_working_color_space"]
        output_colourspace = project_sg["sg_output_color_space"]
        self.parent.logger.info(
            "Project working colour space: {}".format(working_colourspace)
        )
        self.parent.logger.info(
            "Project output colour space: {}".format(output_colourspace)
        )

        OCIO_valid = self.parent.get_setting("Init_OCIO")
        self.parent.logger.info(
            "Apply OCIO: {0}".format(self.parent.get_setting("Init_OCIO"))
        )

        if OCIO_valid:
            harmony_colourspaces = [
                "linear",
                "displayp3",
                "rec709",
                "Rec. 709 2.4",
                "rec2020",
                "Rec. 2020 2.4",
                "Rec. 2020 Linear",
                "sRGB",
            ]

            # invalid_engines = ["tk-maya"]
            invalid_engines = []

            if working_colourspace in harmony_colourspaces:
                invalid_engines.append("tk-harmony")

            if engine_name not in invalid_engines:
                self.parent.logger.info("Injecting ocio bin in path")
                ocio = self.load_framework("mty-framework-opencolorio")
                ocio.ocioCore.set_ocio_env()
                self.parent.logger.info(
                    "OCIO env variable: {}".format(dict(os.environ).get("OCIO"))
                )

        # Disable more unnecessary apps in PATH ----------------------------------------
        # # MetaSync
        # self.parent.logger.debug("injecting metasync in pythonpath")
        # metasync = self.load_framework("mty-framework-metasync")
        # metasync_path = os.path.join(metasync.current_path(), "python")
        # self.parent.logger.debug("metasync path: %s" % metasync_path)
        # pythonpath_fallbacks.append(metasync_path)
        # current = os.environ["PYTHONPATH"].split(os.pathsep)
        # new = os.pathsep.join(current + [metasync_path]).replace(
        #     os.pathsep * 2, os.pathsep
        # )
        # os.environ["PYTHONPATH"] = new

        # Queue Manager
        self.parent.logger.debug("injecting queue manager in pythonpath")
        if "mty-multi-queue" in self.parent.engine.apps:
            app = self.parent.engine.apps["mty-multi-queue"]
            queueclient_path = os.path.join(app.disk_location, "python")
            pythonpath_fallbacks.append(queueclient_path)
            current = os.environ["PYTHONPATH"].split(os.pathsep)
            new = os.pathsep.join(current + [queueclient_path]).replace(
                os.pathsep * 2, os.pathsep
            )
            os.environ["PYTHONPATH"] = new
        else:
            self.parent.log_warning("Queue Manager app is not present!")

        # OpenTimelineIO
        # self.parent.logger.debug("Configuring OTIO Adapters")
        # opentimelineio = self.load_framework("mty-framework-opentimelineio")
        # self.parent.logger.debug("otio: %s" % opentimelineio.otio)
        # self.parent.logger.debug("otioview: %s" % opentimelineio.otioview)
        # opentimelineio.setup_env()

        # ValueOverrides ---------------------------------------------------------------
        self.parent.logger.debug("injecting ValueOverrides in pythonpath")
        valueoverrides = self.load_framework("mty-framework-valueoverrides")
        valueoverrides_path = os.path.join(valueoverrides.disk_location, "python")
        self.parent.logger.debug("valueoverrides path: {}".format(valueoverrides_path))
        pythonpath_fallbacks.append(valueoverrides_path)
        current = os.environ["PYTHONPATH"].split(os.pathsep)
        new = os.pathsep.join(current + [valueoverrides_path]).replace(
            os.pathsep * 2, os.pathsep
        )
        self.parent.logger.info("PYTHONPATH: {}".format(new))
        os.environ["PYTHONPATH"] = new
        # The ValueOverrides framework have a set of settings that can
        # be configured at the config level on the framework
        # but since this setup in the before_app_launch hook is
        # designed to allow other contexts like core hooks or non toolkit
        # code to just import the ValueOverrides module, we collect the
        # corresponding settings and store them in an environment variable
        setting_values = {
            "default_entity_name": valueoverrides.get_setting(
                "default_value_entity_name"
            ),
            "default_value_name_field": valueoverrides.get_setting(
                "default_value_name_field"
            ),
            "default_selected_type_field_name": valueoverrides.get_setting(
                "default_value_selected_type_field_name"
            ),
            "default_types_fields_map": valueoverrides.get_setting(
                "default_types_fields_map"
            ),
            "override_default_code_field": valueoverrides.get_setting(
                "override_default_code_field"
            ),
            "override_default_type_field": valueoverrides.get_setting(
                "override_default_type_field"
            ),
            "override_entity_name": valueoverrides.get_setting("override_entity_name"),
            "override_name_field": valueoverrides.get_setting("override_name_field"),
            "override_task_field": valueoverrides.get_setting("override_task_field"),
            "override_entity_field": valueoverrides.get_setting(
                "override_entity_field"
            ),
            "overrides_default_reference_field_name": valueoverrides.get_setting(
                "overrides_default_reference_field_name"
            ),
            "overrides_types_fields_map": valueoverrides.get_setting(
                "overrides_types_fields_map"
            ),
        }
        setting_values_str = json.dumps(setting_values)
        os.environ["ValueOverridesSettings"] = setting_values_str
        # with all this, the ValueOverrides module can be used as follows:
        # import os
        # import json
        # import ValueOverrides
        # setting_values_str = os.environ["ValueOverridesSettings"]
        # setting_values = json.loads(setting_values_str)
        # CoreOverrides = ValueOverrides.CoreValueOverrides(
        #     setting_values, engine.context.project,
        #     engine.shotgun, logger=engine.logger
        # )
        # value_code = 'mty.pipeline.testing.overrides'
        # data = CoreOverrides.get_values(
        #  value_code, link=engine.context.entity # or engine.context.task
        # )
        # value = data.get(value_code)

        # Generic pythonpath_fallbacks, for cases when the app that is launched
        # reset the PYTHONPATH, and in that case we need an alternative
        os.environ["PYTHONPATH_FALLBACKS"] = os.pathsep.join(pythonpath_fallbacks)

    def get_project_versions(self, engine_name):
        """
        Get a list of software versions from the software entities linked
        to the current project
        """

        import sgtk

        engine = sgtk.platform.current_engine()

        restricted_versions = []

        # filter version in software entities per project, engine and status.
        # Only Active (act) software entities are taken in account
        filters = [
            ["projects", "is", engine.context.project],
            ["engine", "is", engine_name],
            ["sg_status_list", "is", "act"],
        ]

        fields = ["code", "engine", "sg_status_list", "projects", "version_names"]

        # get project software entities
        restricted_software_entities = engine.shotgun.find("Software", filters, fields)

        # Generate a list of unique elements with the restricted software versions
        if restricted_software_entities:
            [
                restricted_versions.extend(software["version_names"].split(", "))
                for software in restricted_software_entities
            ]

        restricted_versions = list(set(restricted_versions))
        restricted_versions.sort()

        return restricted_versions

    # TODO: fix QMessageBox not showing, it only emits a sound
    def show_version_error_message(self, software, msg):
        """
        Shows a message box explainig why the software didn't run
        """

        # from sgtk.platform.qt import QtCore, QtGui

        # app = QtGui.QApplication.instance()
        # msg_error = QtGui.QMessageBox()
        # msg_error.setWindowTitle("Wrong {} version".format(software))
        # msg_error.setText(msg)
        # msg_error.setIcon(QtGui.QMessageBox.Critical)
        # # msg_error.exec_()
        # msg_error.show()

        # return msg_error

        # temporary workaround using tkinter
        import tkinter as tk
        from tkinter import messagebox

        # Create the root window and hide it immediately
        root = tk.Tk()
        root.withdraw()  # Hide the main window

        # Show the error message box
        messagebox.showerror(
            title="Wrong {} version".format(software),
            message=msg
        )

        # Ensure the hidden main window is destroyed after the message box is closed
        root.destroy()
