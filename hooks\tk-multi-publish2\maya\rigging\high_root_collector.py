# -*- coding: utf-8 -*-
# Standard library:
import mimetypes
import os
#   .   .   .   .   .   .   .   .   .   .   .
# Third party:
import sgtk
from tank_vendor import six
import pymel.core as pm
#   .   .   .   .   .   .   .   .   .   .   .
# Project:
# ================================================================


HookBaseClass = sgtk.get_hook_baseclass()


class RootCollector(HookBaseClass):

    def __init__(self, parent):
        super(RootCollector, self).__init__(parent)


    # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

    def process_current_session(
        self, settings, parent_item
    ):

        self.hi_roots(parent_item)

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def hi_roots(self, parent_item):

        icon_path = os.path.join(
            self.disk_location,
            os.pardir,
            os.pardir,
            "icons",
            "root.png"
        )

        step_name = \
            self.parent.context.step['name'].lower()

        if self.ASSET["sg_asset_type"] != "Camera":
            root_nodes_item = \
                parent_item.create_item(
                    "maya.session.high_roots",
                    "high_roots in session",
                    "high_roots"
                )

            self.parent.engine.execute_hook_expression(
                "{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py",
                "use_farm_or_local_processing",
                item = root_nodes_item
            )

            root_nodes_item.set_icon_from_path(icon_path)

            if step_name == "model":
                root_nodes_item.properties["model"] = \
                    "model_high_root"
                self.logger.debug(
                    "Collected high_roots: {0}".format(
                        str(root_nodes_item.properties["model"])
                    )
                )

            else:
                root_nodes_item.properties["model"] = \
                    "model_high_root"

                root_nodes_item.properties["rig"] = \
                    "rig_high_root"

                self.logger.debug(
                    "Collected high_roots: {0} - {1}".format(
                        str(root_nodes_item.properties["model"]),
                        str(root_nodes_item.properties["rig"])
                    )
                )
