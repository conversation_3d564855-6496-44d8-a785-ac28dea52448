# -*- coding: utf-8 -*-
# Standard library:
import os
import sys
from pprint import pformat
import pprint
import json
import traceback
# ___   ___   ___   ___   ___   ___  ___
# Third party:
from tank.platform.qt import Qt<PERSON><PERSON>, QtGui
from tank import Hook

# ___   ___   ___   ___   ___   ___  ___
# Project:
# ====================================================================
pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)


class ScanSceneHook(Hook):

    def __init__(self, parent):
        super(ScanSceneHook, self).__init__(parent)
        self.query = self.parent.create_hook_instance(
            self.parent.get_setting('queries_hook')
        )

        self.transfers = self.parent.create_hook_instance(
            self.parent.get_setting('transfers_hook')
        )
        #  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
        self.utility_hooks = self.parent.get_setting('map_of_utilities_hooks')

        self.asset_scanner = self.parent.create_hook_instance(
            self.utility_hooks['assets']['scanner']
        )
        #  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -


    # ---------------------------------------------------------------------------


    @property
    def map_of_published_types(self):
        return self.parent.get_setting('map_of_publish_types')


    @property
    def entity_task(self):
        filters = [['id', 'is', self.parent.context.task['id']]]
        fields = ['code', 'step', 'content']
        entity = self.parent.engine.shotgun.find_one('Task', filters, fields)
        return entity


    # ---------------------------------------------------------------------------

    def scan_scene(self):
        result = None
        try:
            result = self.asset_scanner.find()
            result['list_of_overrides'] = self._scan_for_overrides()
            result['list_of_items'] = []

            self._handle_list_of_errors(result)

            self.transfers.download_publish_and_dependencies(
                result['list_of_rigs'] +
                result['list_of_environments'] +
                result['list_of_overrides']
            )

            for rig in result['list_of_rigs']:
                result['list_of_items'].append(
                    {'node': rig['name'],
                     'type': 'Rig File',
                     'path': rig['path'],
                     'process_hook': 'batchload_reference_rigs'}
                )

            for env in result['list_of_environments']:
                result['list_of_items'].append(
                    {'node': env['name'],
                     'type': 'Env Proxy',
                     'path': env['path'],
                     'process_hook': 'batchload_reference_rigs'
                     }
                )

            for override in result['list_of_overrides']:
                result['list_of_items'].append(
                    {'node': override['name'],
                     'type': 'Env Overrides',
                     'path': override['path'],
                     'process_hook': 'batchload_reference_rigs'}
                )

            return result
        except:
            error_str = traceback.format_exc()
            self.parent.engine.execute_in_main_thread(
                QtGui.QMessageBox.critical,
                None,
                'Something went wrong',
                error_str
            )

        return result

    # ---------------------------------------------------------------------------

    def _handle_list_of_errors(self, scan_results):
        if self.query.current_pipeline_step_name == "Animation":
            if scan_results['list_of_errors']:
                message = str(scan_results['list_of_errors'])
                self.parent.engine.execute_in_main_thread(
                    QtGui.QMessageBox.critical,
                    None,
                    "There was an issue with scene assets!",
                    message
                )

    # ---------------------------------------------------------------------------
    def _scan_for_overrides(self):
        result = []

        list_of_publishes = self.query.shotgun_for_shot_published_types(
            shot_entity=self.parent.context.entity,
            filters=[
                [
                    'published_file_type.PublishedFileType.code',
                    'is',
                    self.parent.get_setting('map_of_publish_types')['overrides']
                ]
            ]
        )

        latest_version = self.query.shotgun_for_latest_version_by_type(
            publish_type=self.map_of_published_types['overrides']
        )

        for _p in list_of_publishes:
            pp(_p)
            if _p['version_number'] == latest_version:
                result.append({
                    'name': _p['name'],
                    'path': _p['path']['local_path'],
                    'publish': _p
                })

        return result
