# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
from __future__ import division

# from concurrent.futures import process

import os
import re
import six
import glob
import copy
import json
import time
import pprint
import shutil
import datetime
import tempfile
#import threading
import traceback


import sgtk
from sgtk.util.filesystem import ensure_folder_exists
from tank.platform.qt import QtCore, QtGui


__author__ = "Diego <PERSON> Huert<PERSON>"
__contact__ = "https://www.linkedin.com/in/diegogh/"


pp = pprint.pprint
pf = pprint.pformat

#THREAD_CACHE = threading.local()


HookBaseClass = sgtk.get_hook_baseclass()


class HarmonySessionPublishPlugin(HookBaseClass):
    # a class level cache for scene node names
    node_names_cache = set()

    """
    Plugin for publishing an open Harmony session.

    This hook relies on functionality found in the base file publisher hook in
    the publish2 app and should inherit from it in the configuration. The hook
    setting for this plugin should look something like this::

        hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py"

    """

    # NOTE: The plugin icon and name are defined by the base file plugin.

    @property
    def description(self):
        """
        Verbose, multi-line description of what the plugin does. This can
        contain simple html for formatting.
        """

        loader_url = "https://support.shotgunsoftware.com/hc/en-us/articles/219033078"

        return """
        Publishes the file to Shotgun. A <b>Publish</b> entry will be
        created in Shotgun which will include a reference to the file's current
        path on disk. If a publish template is configured, a copy of the
        current session will be copied to the publish template path which
        will be the file that is published. Other users will be able to access
        the published file via the <b><a href='%s'>Loader</a></b> so long as
        they have access to the file's location on disk.

        If the session has not been saved, validation will fail and a button
        will be provided in the logging output to save the file.

        <h3>File versioning</h3>
        If the filename contains a version number, the process will bump the
        file to the next version after publishing.

        The <code>version</code> field of the resulting <b>Publish</b> in
        Shotgun will also reflect the version number identified in the filename
        The basic worklfow recognizes the following version formats by default:

        <ul>
        <li><code>filename.v###.ext</code></li>
        <li><code>filename_v###.ext</code></li>
        <li><code>filename-v###.ext</code></li>
        </ul>

        After publishing, if a version number is detected in the work file, the
        work file will automatically be saved to the next incremental version
        number. For example, <code>filename.v001.ext</code> will be published
        and copied to <code>filename.v002.ext</code>

        If the next incremental version of the file already exists on disk, the
        validation step will produce a warning, and a button will be provided
        in the logging output which will allow saving the session to the next
        available version number prior to publishing.

        <br><br><i>NOTE: any amount of version number padding is supported. for
        non-template based workflows.</i>

        <h3>Overwriting an existing publish</h3>
        In non-template workflows, a file can be published multiple times,
        however only the most recent publish will be available to other users.
        Warnings will be provided during validation if there are previous
        publishes.
        """ % (
            loader_url,
        )

    @property
    def settings(self):
        """
        Dictionary defining the settings that this plugin expects to receive
        through the settings parameter in the accept, validate, publish and
        finalize methods.

        A dictionary on the following form::

            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }

        The type string should be one of the data types that toolkit accepts as
        part of its environment configuration.
        """

        # inherit the settings from the base publish plugin
        base_settings = super(HarmonySessionPublishPlugin, self).settings or {}

        base_settings["File Types"]["default"].append(
            ["Harmony Project File", "xstage"]
        )

        # settings specific to this class
        harmony_publish_settings = {
            "Publish Template": {
                "type": "template",
                "default": None,
                "description": "Template path for published work files. Should"
                "correspond to a template defined in "
                "templates.yml.",
            },
            "Verification elements": {
                "type": "list",
                "default": ["sg_working_color_space"],
                "description": "Elements to verify from the current context."
                "This list will be required from the project info in SG.",
            },
            "Copy to Publish folders": {
                "type": "list",
                "default": [
                    "audio",
                    "elements",
                    "environments",
                    "jobs",
                    "palette-library",
                    "scripts",
                ],
                "description": "Folders that will be copied from work area to publish",
            },
            "Color Spaces": {
                "type": "dict",
                "default": {},
                "description": "ColorSpace mapping between SG and Harmony",
            },
            # "No Resolution Validation Tasks": {
            #     "type": "list",
            #     "default": [],
            #     "description": "List of tasks where resolution won't be validated",
            # },
            "Audio validations": {
                "type": "list",
                "default": [],
                "description": "Audio validations available: Exists, Published, Latest",
            },
            "Timechart validation": {
                "type": "boolean",
                "default": False,
                "description": "Validate if the scene will verify if "
                "one or more layers exists with the TC_ in the name.",
            },
            "Thumbnail files": {
                "type": "list",
                "default": [],
                "description": "Thumbnails files inside xstage dependencies.",
            },
            "id_nodes_dict file name": {
                "type": "string",
                "default": "id_nodes_dict_tmp",
                "description": "Name of the temporary json file to store Harmony queried data.",
            },
            "id_elements_mapping file name": {
                "type": "string",
                "default": "id_elements_mapping_tmp",
                "description": "Name of the temporary json file to store Harmony queried data.",
            },
        }

        # update the base settings
        base_settings.update(harmony_publish_settings)

        return base_settings

    @property
    def item_filters(self):
        """
        List of item types that this plugin is interested in.

        Only items matching entries in this list will be presented to the
        accept() method. Strings can contain glob patters such as *, for
        example ["harmony.*", "file.harmony"]
        """
        return ["harmony.session"]

    def accept(self, settings, item):
        """
        Method called by the publisher to determine if an item is of any
        interest to this plugin. Only items matching the filters defined via
        the item_filters property will be presented to this method.

        A publish task will be generated for each item accepted here. Returns a
        dictionary with the following booleans:

            - accepted: Indicates if the plugin is interested in this value at
                all. Required.
            - enabled: If True, the plugin will be enabled in the UI, otherwise
                it will be disabled. Optional, True by default.
            - visible: If True, the plugin will be visible in the UI, otherwise
                it will be hidden. Optional, True by default.
            - checked: If True, the plugin will be checked in the UI, otherwise
                it will be unchecked. Optional, True by default.

        :param settings: Dictionary of Settings. The keys are strings, matching
                         the keys returned in the settings property. The values
                         are `Setting` instances.
        :param item: Item to process

        :returns: dictionary with boolean keys accepted, required and enabled
        """

        # if a publish template is configured, disable context change. This
        # is a temporary measure until the publisher handles context switching
        # natively.
        if settings.get("Publish Template").value:
            item.context_change_allowed = False

        path = _session_path()

        if not path:
            # the session has not been saved before (no path determined).
            # provide a save button. the session will need to be saved before
            # validation will succeed.
            self.logger.warn(
                "The Harmony session has not been saved.", extra=_get_save_as_action()
            )

        self.logger.info(
            "Harmony '%s' plugin accepted the current session." % (self.name,)
        )
        return {"accepted": True, "checked": True}

    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish. Returns a
        boolean to indicate validity.

        :param settings: Dictionary of Settings. The keys are strings, matching
                         the keys returned in the settings property. The values
                         are `Setting` instances.
        :param item: Item to process
        :returns: True if item is valid, False otherwise.
        """

        # everytime we use this validate method, the THREAD_CACHE gets deleted
        # if hasattr(THREAD_CACHE, "id_elements_mapping"):
        #     delattr(THREAD_CACHE, "id_elements_mapping")

        publisher = self.parent
        path = _session_path()

        # ---- ensure the session has been saved

        if not path:
            # the session still requires saving. provide a save button.
            # validation fails.
            error_msg = "The Harmony session has not been saved."
            self.logger.error(error_msg, extra=_get_save_as_action())
            raise Exception(error_msg)

        # ---- check the session against any attached work template

        # get the path in a normalized state. no trailing separator,
        # separators are appropriate for current os, no double separators,
        # etc.
        path = sgtk.util.ShotgunPath.normalize(path)

        # if the session item has a known work template, see if the path
        # matches. if not, warn the user and provide a way to save the file to
        # a different path
        work_template = item.properties.get("work_template")
        if work_template:
            if not work_template.validate(path):
                self.logger.warning(
                    "The current session does not match the configured work "
                    "file template.",
                    extra={
                        "action_button": {
                            "label": "Save File",
                            "tooltip": "Save the current session to a "
                            "different file name",
                            # will launch wf2 if configured
                            "callback": _get_save_as_action,
                        }
                    },
                )
            else:
                self.logger.debug("Work template configured and matches session file.")
        else:
            self.logger.debug("No work template configured.")

        # ---- see if the version can be bumped post-publish

        # check to see if the next version of the work file already exists on
        # disk. if so, warn the user and provide the ability to jump to save
        # to that version now
        (next_version_path, version) = self._get_next_version_info(path, item)
        if next_version_path and os.path.exists(next_version_path):
            # determine the next available version_number. just keep asking for
            # the next one until we get one that doesn't exist.
            while os.path.exists(next_version_path):
                (next_version_path, version) = self._get_next_version_info(
                    next_version_path, item
                )

            error_msg = "The next version of this file already exists on disk."
            self.logger.error(
                error_msg,
                extra={
                    "action_button": {
                        "label": "Save to v%s" % (version,),
                        "tooltip": "Save to the next available version number, "
                        "v%s" % (version,),
                        "callback": lambda: _save_session(next_version_path),
                    }
                },
            )
            raise Exception(error_msg)

        # ---- populate the necessary properties and call base class validation

        # populate the publish template on the item if found
        publish_template_setting = settings.get("Publish Template")
        publish_template = publisher.engine.get_template_by_name(
            publish_template_setting.value
        )
        if publish_template:
            item.properties["publish_template"] = publish_template

        # set the session path on the item for use by the base plugin
        # validation
        # step. NOTE: this path could change prior to the publish phase.
        item.properties["path"] = path

        # run the base class validation
        # return super(HarmonySessionPublishPlugin, self).validate(settings, item)
        baseclass_validation = super(
            HarmonySessionPublishPlugin, self
        ).validate(settings, item)

        if not baseclass_validation:
            return False

        return True

    def create_tmp_json_file(self, basename):
        """
        Creates a tmp file based on the input basename, in the current project folder,
        inside a tmpfiles folder and returns the full file path
        """

        xstage_path = _session_path()
        xstage_dirname = os.path.dirname(xstage_path)
        tmpfiles_folder = os.path.join(xstage_dirname, "tmpfiles")

        tmpfiles_folder = fix_path(tmpfiles_folder)

        if not os.path.exists(tmpfiles_folder):
            try:
                os.makedirs(tmpfiles_folder)
            except:
                error_msg = "Couldn't create tmpfiles folder: {}".format(
                    tmpfiles_folder
                )
                raise Exception(error_msg)

        json_filename = "{}.json".format(basename)
        json_fullpath = os.path.join(tmpfiles_folder, json_filename)
        # json_fullpath = json_fullpath.replace("\\", "/")
        json_fullpath = fix_path(json_fullpath)
        self.parent.logger.info("json_fullpath: {}".format(json_fullpath))

        # If file already exists, delete it first
        if os.path.exists(json_fullpath):
            try:
                os.remove(json_fullpath)
            except:
                error_msg = "Couldn't delete existing tmpfile '{}''.".format(
                    json_fullpath
                )
                raise Exception(error_msg)

        try:
            f = open(json_fullpath, "w+")
            f.close()
        except:
            error_msg = "Couldn't create tmpfile '{}''.".format(json_fullpath)
            raise Exception(error_msg)
            # pass

        if os.path.exists(json_fullpath):
            return json_fullpath
        else:
            return None

    def get_id_nodes_mapping(self, file_path):
        """
        Collects all read nodes from the scene.

        returns a json file path which contains a dictionary of the form:
            {
                str(element_id): node_path,
                str(element_id): node_path,
                ...
            }
        """

        engine = self.parent.engine

        id_nodes_mapping = {}

        engine.app.custom_script(
            'include("long_names_validation_utils.js"); '
            'get_nodes_elements_ids_mapping("{}");'.format(file_path)
        )

        time_limit = 100
        # read json file from disk created by the 'get_id_nodes_mapping' function
        if file_path and os.path.exists(file_path):
            # try:
            #     with open(file_path) as json_file:
            #         id_nodes_mapping = json.load(json_file)
            # except:
            id_nodes_dict = None
            i = 0
            while not id_nodes_dict and i < time_limit:
                time.sleep(5)
                try:
                    with open(file_path) as json_file:
                        id_nodes_dict = json.load(json_file)
                except:
                    i += 1
        else:
            id_nodes_dict = None

        self.parent.logger.debug("id_nodes_dict:\n{}".format(pf(id_nodes_dict)))

        if id_nodes_dict:
            for elem_id in id_nodes_dict.keys():
                # element_id = id_nodes_dict[elem_id]["element_id"]
                element_id = int(elem_id)
                node_path = id_nodes_dict[elem_id]
                # node_path = id_nodes_dict[elem_id]["node_path"]
                node_name = node_path.split("/")
                node_root = node_name[0]
                node_name = node_name[-1]
                id_nodes_mapping[elem_id] = {
                    "node_path": node_path,
                    "node_name": node_name,
                    "element_id": element_id,
                }

            return id_nodes_mapping

    def get_id_elements_mapping(self):
        """
        Collects all elements in scene and creates a json file which contains
        a dictionary of dictionaries the form:
            {
                column_element_id: {
                    column_element_name,
                    element_vector_type
                },
                column_element_id: {
                    column_element_name,
                    element_vector_type
                },
                ...
            }
        """

        id_elements_mapping_basename = self.settings.get(
            "id_elements_mapping file name"
        ).get("default")
        self.parent.logger.info(
            "id_elements_mapping_basename: {}".format(id_elements_mapping_basename)
        )
        id_elements_mapping_json_path = self.create_tmp_json_file(
            id_elements_mapping_basename
        )
        self.parent.logger.info(
            "id_elements_mapping_json_path: {}".format(id_elements_mapping_json_path)
        )

        # get id_elements_mapping dict either from the thread_cache or by analizing the
        # scene using the js script inside of Harmpny
        if getattr(THREAD_CACHE, "id_elements_mapping", None):
            id_elements_mapping = getattr(THREAD_CACHE, "id_elements_mapping", None)
            self.parent.logger.info("Read id_elements_mapping from THREAD_CACHE")
        else:
            self.parent.logger.warning(
                "id_elements_mapping doesn't exist in THREAD_CACHE yet"
            )

            engine = self.parent.engine
            engine.app.custom_script(
                'include("long_names_validation_utils.js"); '
                'get_elements_mapping("{}");'.format(id_elements_mapping_json_path)
            )

            time_limit = 180
            # read json file from disk created by the 'get_id_elements_mapping' function
            if id_elements_mapping_json_path and os.path.exists(
                id_elements_mapping_json_path
            ):
                id_elements_mapping = None
                i = 0
                while not id_elements_mapping and i < time_limit:
                    time.sleep(5)
                    try:
                        with open(id_elements_mapping_json_path) as json_file:
                            id_elements_mapping = json.load(json_file)
                            # store the dictionary in the thread_cache
                            setattr(
                                THREAD_CACHE, "id_elements_mapping", id_elements_mapping
                            )
                            self.parent.logger.info(
                                "Added id_elements_mapping attr to THREAD_CACHE"
                            )

                    except:
                        i += 1
            else:
                id_elements_mapping = None

        # remove temp files and folder
        tmpfiles_folder = os.path.dirname(id_elements_mapping_json_path)

        if os.path.exists(tmpfiles_folder):
            try:
                shutil.rmtree(tmpfiles_folder, ignore_errors=True)
            except:
                self.parent.logger.info(
                    "Couldn't delete tmp json files folder '{}'".format(tmpfiles_folder)
                )


        return id_elements_mapping


    def get_long_named_elements(self, validation):
        """
        Collects a list of nodes in scene which names
        are longer than 20 characters

        returns a dictionary of the form:
        element_name: {
            node_path,
            node_name,
            node_new_name,
            element_name,
            new_element_name,
            element_id
        }
        """

        long_named_elements = {}
        wrongly_named_elements = {}

        # ------------------------------------------------------------------------------
        id_elements_mapping = self.get_id_elements_mapping()

        self.parent.logger.debug(
            "id_elements_mapping, {}\n{}".format(
                type(id_elements_mapping), pf(id_elements_mapping)
            )
        )

        # ------------------------------------------------------------------------------
        # if id_elements_mapping and id_nodes_mapping:
        if id_elements_mapping:
            for elem_id in id_elements_mapping.keys():
                elem_vector_type = id_elements_mapping[elem_id]["element_vector_type"]
                element_name = id_elements_mapping[elem_id]["element_name"]
                element_id = id_elements_mapping[elem_id]["element_id"]
                element_physical_name = id_elements_mapping[elem_id][
                    "element_physical_name"
                ]
                node_name = id_elements_mapping[elem_id].get("node_name", "")
                node_path = id_elements_mapping[elem_id].get("node_path", "")

                # elem_vector_type:
                #   0 means it's an image
                #   1 means it's an obsolete vector (.pnt)
                #   2 means it's a vector (.tvg)
                check_elem_type_list = [0, 1, 2]

                # fill in long named elements dict
                if elem_vector_type in check_elem_type_list or "_sgtk" in element_name:
                    if len(element_name) > 20 and node_name and node_path:
                        # node_name = id_nodes_mapping.get(elem_id, {}).get("node_name")
                        # node_path = id_nodes_mapping.get(elem_id, {}).get("node_path")
                        new_elem_name = self.compute_shorter_node_name(
                            element_name, validation
                        )
                        if node_name:
                            new_node_name = self.compute_shorter_node_name(
                                node_name, validation
                            )
                        else:
                            new_node_name = element_name

                        # long_named_elements[element_name] = {
                        long_named_elements[node_path] = {
                            "node_path": node_path,
                            "node_name": node_name,
                            "new_node_name": new_node_name,
                            "element_name": element_name,
                            "new_element_name": new_elem_name,
                            "element_id": element_id,
                        }

        return long_named_elements

    def compute_shorter_node_name(self, node_name, validation):
        """
        Calculate a shorter version of an element name
        """

        new_name = node_name

        # start by extracting any version token and getting rid of it
        version_token = re.match(r"^.*(?P<version>v[0-9]+).*$", node_name)
        if version_token:
            new_name = node_name.replace(
                version_token.groupdict().get("version", ""), ""
            )

        # also separate any tokens separated by underscores
        name_tokens = new_name.split("_")

        # we will title them to join them later
        # but preserving existing upercase characetrs
        name_tokens = [(t[:1].capitalize() + t[1:]) for t in name_tokens]

        # try to only keep the first 4 characters of each token
        # in the case the token is 8 characters len or shorter, but
        # if its longer, then keep 4 from start and 4 from end, or
        # the corresponding amount to not duplicate characters
        copy_tokens = copy.copy(name_tokens)
        name_tokens = []
        for t in copy_tokens:
            token_len = len(t)
            if token_len <= 8:
                name_tokens.append(t[:4])
            else:
                keep = token_len // 2
                token_start = t[:4]
                token_end = t[-5:]
                token_end = token_end[:1].capitalize() + token_end[1:]
                new_token = token_start + token_end
                name_tokens.append(new_token)

        # build the new name after the previous removals
        new_name = "".join(name_tokens)

        if validation:
            self.parent.logger.info("Is validation: {}".format(validation))
            if len(new_name) > 20:
                remove = len(new_name) - 20
                middle = len(new_name) // 2
                starting = new_name[: middle - (remove // 2)]
                ending = new_name[middle + (remove // 2) :]
                new_name = starting + ending[:1].capitalize() + ending[1:]
        elif not validation:
            self.parent.logger.info("Is not validation: {}".format(validation))
            # and if the length is still too much, remove from the middle
            # while loop to ensure name is not bigger than 20 characters
            while len(new_name) > 20:
                self.parent.logger.info("new_name: {}".format(new_name))
                remove = len(new_name) - 10
                middle = len(new_name) // 2
                starting = new_name[: middle - (remove // 2)]
                ending = new_name[middle + (remove // 2) :]
                new_name = starting + ending[:1].capitalize() + ending[1:]

        # and return the version token
        if version_token:
            new_name += version_token.group(1)

        # finally using a cache, make sure that there are no duplicates
        current = 2
        base_name = copy.copy(new_name)
        while new_name in self.node_names_cache:
            new_name = base_name + "_{0}".format(current)
            current += 1
        self.node_names_cache.add(new_name)

        return new_name

    def auto_rename_long_named_nodes_and_elements(self):
        """Collect long named nodes and elements and auto rename them"""

        self.logger.info("Auto renaming long named nodes and elements...")
        self.parent.logger.info("Auto renaming long named nodes and elements...")

        renamed = 0
        issues = []

        long_named_elements = self.get_long_named_elements(False)

        # at this point id_elements_mapping should exist as attr, so we get it to update
        # it at the end of each element operation, and finally set the attribute again
        # with the updated dictionary
        id_elements_mapping = getattr(THREAD_CACHE, "id_elements_mapping", None)
        if id_elements_mapping:
            self.parent.logger.info("Read id_elements_mapping from THREAD_CACHE")
        else:
            self.parent.logger.warning(
                "id_elements_mapping doesn't exist in THREAD_CACHE"
            )

        if long_named_elements:
            # start by clearing the cache
            self.node_names_cache = set()

            for element in long_named_elements.keys():
                element_id = str(long_named_elements[element]["element_id"])
                node_path = long_named_elements[element]["node_path"]
                node_name = long_named_elements[element]["node_name"]
                new_node_name = long_named_elements[element]["new_node_name"]
                element_name = long_named_elements[element]["element_name"]
                new_element_name = long_named_elements[element]["new_element_name"]

                node_rename_command = 'node.rename("{}", "{}");'.format(
                    node_path, new_element_name
                )
                element_rename_command = 'element.renameById("{}", "{}");'.format(
                    element_id, new_element_name
                )
                self.parent.logger.info("-" * 80)
                self.parent.logger.info(
                    "element: {}, node: {}".format(element_name, node_name)
                )
                self.parent.logger.info(
                    "node_rename_command:\n{}".format(node_rename_command)
                )
                self.parent.logger.info(
                    "element_rename_command:\n{}".format(element_rename_command)
                )

                # renameById doesn't update node displayname, so need to rename
                # display name first then renameById
                node_rename = self.parent.engine.app.custom_script(node_rename_command)
                element_rename = self.parent.engine.app.custom_script(
                    element_rename_command
                )
                # self.parent.logger.info("node_rename: {}".format(node_rename))
                # self.parent.logger.info("element_rename: {}".format(element_rename))
                if node_rename and element_rename:
                    self.parent.logger.info(
                        "node_rename and element_rename: {}".format(
                            node_rename and element_rename
                        )
                    )
                    renamed += 1
                    if id_elements_mapping:
                        id_elements_mapping[element_id]["element_name"] = new_element_name
                        id_elements_mapping[element_id]["element_physical_name"] = new_element_name
                        id_elements_mapping[element_id]["node_name"] = new_node_name

                        # build the new node path
                        node_dirname = os.path.dirname(
                            id_elements_mapping[element_id]["node_path"]
                        )
                        new_node_path = "{}/{}".format(node_dirname, new_node_name)
                        id_elements_mapping[element_id]["node_path"] = new_node_path

                        # finally we update the thread_cache with the updated data
                        setattr(THREAD_CACHE, "id_elements_mapping", id_elements_mapping)
                        self.parent.logger.info("Updated THREAD_CACHE")

                else:
                    issues.append((node_rename_command, node_rename))
                    issues.append((element_rename_command, element_rename))

        self.parent.engine.app.save_project()

        self.logger.info(
            "Auto renamed {0} long named nodes and elements.".format(renamed)
        )
        self.parent.logger.info(
            "Auto renamed {0} long named nodes and elements.".format(renamed)
        )

        sg_step = self.parent.context.step
        sg_step_name = sg_step.get("name")

        if sg_step_name.lower() in ["rig", "cycles"]:
            self.logger.warning("Deleting existing TPL from pipeline library.")
            self.parent.logger.warning("Deleting existing TPL from pipeline library.")
        # Remove existing tpl
        # harmony library app
        engine = sgtk.platform.current_engine()
        app_harmonylib = engine.apps.get("mty-harmony-library")
        container_folder = app_harmonylib.ensure_library_folder(engine.context)
        self.parent.logger.info("container_folder: {}".format(container_folder))

        for filename in os.listdir(container_folder):
            file_path = os.path.join(container_folder, filename)
            self.parent.logger.info("file_path: {}".format(file_path))
            try:
                if os.path.isfile(file_path) or os.path.islink(file_path):
                    try:
                        os.unlink(file_path)
                    except:
                        os.remove(file_path)
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
            except Exception as e:
                self.parent.engine.logger.error(
                    "Failed to delete {}. Reason: {}".format(file_path, e)
                )

        msg = (
            "The TPL in your pipeline library has been removed because it "
            "most likely also contained the nodes with long names.\n\n"
            "You MUST first close the publisher, then refresh your library (f5) "
            "add the tpl to the library again, and finally open the publisher again."
        )

        sg_step = self.parent.context.step
        sg_step_name = sg_step.get("name")

        if sg_step_name.lower() in ["rig", "cycles"]:
            # self.parent.engine.show_message(tpl_msg)
            QtGui.QMessageBox.warning(None, "TPL has been removed", msg)

        if issues:
            self.logger.warning(
                "Some auto renames failed:\n{0}".format("\n".join(issues))
            )
            self.parent.logger.warning(
                "Some auto renames failed:\n{0}".format("\n".join(issues))
            )

        # tpl_msg = (
        #     "If you are piblishing a TPL, the TPL in your pipeline library will "
        #     "be removed because it most likely contains also the nodes with long names.\n\n"
        #     "You MUST first close the publisher, then refresh your library (f5) "
        #     "and finally, add the tpl to the library again."
        # )

    def get_wrongly_named_elements(self):
        """
        Collects a list of nodes in scene which names
        include - (hyphen)

        returns a dictionary of the form:
        element_name: {
            node_path,
            node_name,
            node_new_name,
            element_name,
            new_element_name,
            element_id
        }
        """

        wrongly_named_elements = {}

        # ------------------------------------------------------------------------------
        id_elements_mapping = self.get_id_elements_mapping()

        self.parent.logger.debug(
            "id_elements_mapping, {}\n{}".format(
                type(id_elements_mapping), pf(id_elements_mapping)
            )
        )

        # ------------------------------------------------------------------------------
        # if id_elements_mapping and id_nodes_mapping:
        if id_elements_mapping:
            for elem_id in id_elements_mapping.keys():
                elem_vector_type = id_elements_mapping[elem_id]["element_vector_type"]
                element_name = id_elements_mapping[elem_id]["element_name"]
                element_id = id_elements_mapping[elem_id]["element_id"]
                element_physical_name = id_elements_mapping[elem_id][
                    "element_physical_name"
                ]
                node_name = id_elements_mapping[elem_id].get("node_name", "")
                node_path = id_elements_mapping[elem_id].get("node_path", "")

                # elem_vector_type:
                #   0 means it's an image
                #   1 means it's an obsolete vector (.pnt)
                #   2 means it's a vector (.tvg)
                check_elem_type_list = [0, 1, 2]

                # fill in long named elements dict
                if elem_vector_type in check_elem_type_list or "_sgtk" in element_name:
                    if "-" in element_name and node_name and node_path:
                        # node_name = id_nodes_mapping.get(elem_id, {}).get("node_name")
                        # node_path = id_nodes_mapping.get(elem_id, {}).get("node_path")
                        new_elem_name = self.fix_node_name(element_name)
                        if node_name:
                            new_node_name = self.fix_node_name(node_name)
                        else:
                            new_node_name = new_elem_name

                        wrongly_named_elements[node_path] = {
                            "node_path": node_path,
                            "node_name": node_name,
                            "new_node_name": new_node_name,
                            "element_name": element_name,
                            "new_element_name": new_elem_name,
                            "element_id": element_id,
                        }

        return wrongly_named_elements

    def fix_node_name(self, node_name):
        """
        Replace all - (hyphens) in element name by _ (underscores)
        """

        pattern =  r"-| "
        new_node_name = re.sub(pattern, "_", node_name) or node_name

        return new_node_name

    def auto_rename_wrongly_named_nodes_and_elements(self):
        """Collect wrongly named nodes and elements and auto rename them"""

        self.logger.info("Auto renaming wrongly named nodes and elements...")
        self.parent.logger.info("Auto renaming wrongly named nodes and elements...")

        renamed = 0
        issues = []

        wrongly_named_elements = self.get_wrongly_named_elements()

        # at this point id_elements_mapping should exist as attr, so we get it to update
        # it at the end of each element operation, and finally set the attribute again
        # with the updated dictionary
        id_elements_mapping = getattr(THREAD_CACHE, "id_elements_mapping", None)
        if id_elements_mapping:
            self.parent.logger.info("Read id_elements_mapping from THREAD_CACHE")
        else:
            self.parent.logger.warning(
                "id_elements_mapping doesn't exist in THREAD_CACHE"
            )

        if wrongly_named_elements:
            # start by clearing the cache
            self.node_names_cache = set()

            for element in wrongly_named_elements.keys():
                element_id = str(wrongly_named_elements[element]["element_id"])
                node_path = wrongly_named_elements[element]["node_path"]
                node_name = wrongly_named_elements[element]["node_name"]
                new_node_name = wrongly_named_elements[element]["new_node_name"]
                element_name = wrongly_named_elements[element]["element_name"]
                new_element_name = wrongly_named_elements[element]["new_element_name"]

                node_rename_command = 'node.rename("{}", "{}");'.format(
                    node_path, new_node_name
                )
                element_rename_command = 'element.renameById("{}", "{}");'.format(
                    element_id, new_element_name
                )
                self.parent.logger.info("-" * 80)
                self.parent.logger.info(
                    "element: {}, node: {}".format(element_name, node_name)
                )
                self.parent.logger.info(
                    "node_rename_command:\n{}".format(node_rename_command)
                )
                self.parent.logger.info(
                    "element_rename_command:\n{}".format(element_rename_command)
                )

                # renameById doesn't update node displayname, so need to rename
                # display name first then renameById
                node_rename = self.parent.engine.app.custom_script(node_rename_command)
                element_rename = self.parent.engine.app.custom_script(
                    element_rename_command
                )
                # self.parent.logger.info("node_rename: {}".format(node_rename))
                # self.parent.logger.info("element_rename: {}".format(element_rename))
                if node_rename and element_rename:
                    self.parent.logger.info(
                        "node_rename and element_rename: {}".format(
                            node_rename and element_rename
                        )
                    )
                    renamed += 1
                    # self.parent.logger.info("renamed: {}".format(renamed))
                    # self.parent.logger.info(
                    #     "id_elements_mapping:\n{}".format(pf(id_elements_mapping))
                    # )
                    if id_elements_mapping:
                        # self.parent.logger.info(
                        #     "element_data before updating:\n{}".format(
                        #         pf(id_elements_mapping[element_id])
                        #     )
                        # )
                        self.parent.logger.info("Preparing to update THREAD_CACHE")
                        id_elements_mapping[element_id]["element_name"] = new_element_name
                        id_elements_mapping[element_id]["element_physical_name"] = new_element_name
                        id_elements_mapping[element_id]["node_name"] = new_node_name

                        # build the new node path
                        node_dirname = os.path.dirname(
                            id_elements_mapping[element_id]["node_path"]
                        )
                        new_node_path = "{}/{}".format(node_dirname, new_node_name)
                        id_elements_mapping[element_id]["node_path"] = new_node_path

                        self.parent.logger.info("About to update THREAD_CACHE")
                        # self.parent.logger.info(
                        #     "element_data after updating:\n{}".format(
                        #         pf(id_elements_mapping[element_id])
                        #     )
                        # )
                        # finally we update the thread_cache with the updated data
                        setattr(THREAD_CACHE, "id_elements_mapping", id_elements_mapping)
                        self.parent.logger.info("Updated THREAD_CACHE")
                else:
                    issues.append((node_rename_command, node_rename))
                    issues.append((element_rename_command, element_rename))

        self.parent.engine.app.save_project()

        self.logger.info(
            "Auto renamed {0} wrongly named nodes and elements.".format(renamed)
        )
        self.parent.logger.info(
            "Auto renamed {0} wrongly named nodes and elements.".format(renamed)
        )

        sg_step = self.parent.context.step
        sg_step_name = sg_step.get("name")

        if sg_step_name.lower() in ["rig", "cycles"]:
            self.logger.warning("Deleting existing TPL from pipeline library.")
            self.parent.logger.warning("Deleting existing TPL from pipeline library.")
        # Remove existing tpl
        # harmony library app
        engine = sgtk.platform.current_engine()
        app_harmonylib = engine.apps.get("mty-harmony-library")
        container_folder = app_harmonylib.ensure_library_folder(engine.context)
        self.parent.logger.info("container_folder: {}".format(container_folder))

        for filename in os.listdir(container_folder):
            file_path = os.path.join(container_folder, filename)
            self.parent.logger.info("file_path: {}".format(file_path))
            try:
                if os.path.isfile(file_path) or os.path.islink(file_path):
                    try:
                        os.unlink(file_path)
                    except:
                        os.remove(file_path)
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
            except Exception as e:
                self.parent.engine.logger.error(
                    "Failed to delete {}. Reason: {}".format(file_path, e)
                )

        msg = (
            "The TPL in your pipeline library has been removed because it "
            "most likely also contained the nodes with long names.\n\n"
            "You MUST first close the publisher, then refresh your library (f5) "
            "add the tpl to the library again, and finally open the publisher again."
        )

        sg_step = self.parent.context.step
        sg_step_name = sg_step.get("name")

        if sg_step_name.lower() in ["rig", "cycles"]:
            # self.parent.engine.show_message(tpl_msg)
            QtGui.QMessageBox.warning(None, "TPL has been removed", msg)

        if issues:
            self.logger.warning(
                "Some auto renames failed:\n{0}".format("\n".join(issues))
            )
            self.parent.logger.warning(
                "Some auto renames failed:\n{0}".format("\n".join(issues))
            )

        # tpl_msg = (
        #     "If you are piblishing a TPL, the TPL in your pipeline library will "
        #     "be removed because it most likely contains also the nodes with long names.\n\n"
        #     "You MUST first close the publisher, then refresh your library (f5) "
        #     "and finally, add the tpl to the library again."
        # )

    def get_problematic_files(self):
        def select_lines_based_on_start_and_end_strings(file_path):
            selected_lines = []

            start_last_index = None
            end_last_index = None

            if not os.path.exists(file_path):
                self.parent.logger.error("File doesn't exist: {}".format(file_path))
                return None

            start_string = "***---*** start"
            end_string = "***---*** end"

            with open(file_path, "r") as file:
                lines = file.readlines()
                for i, line in enumerate(lines):
                    if start_string in line:
                        start_last_index = i + 1
                    elif end_string in line:
                        end_last_index = i + 1

                # print("start_last_index: {}".format(start_last_index))
                # print("end_last_index: {}".format(end_last_index))

            if start_last_index and end_last_index:
                selected_lines = lines[start_last_index - 1 : end_last_index]

            return selected_lines

        def get_time_obj_from_string(hour_str):
            hour_split = re.split("[:|.]", hour_str)
            hour_split_int = [int(i) for i in hour_split]
            time_obj = datetime.time(
                hour_split_int[0],
                hour_split_int[1],
                hour_split_int[2],
                hour_split_int[3],
            )
            time_obj = datetime.datetime.combine(datetime.datetime.today(), time_obj)

            return time_obj

        def get_time_difference_from_now(time_obj):
            now = datetime.datetime.now()
            time_diff = now - time_obj
            diff_in_seconds = time_diff.total_seconds()

            return diff_in_seconds

        def filter_lines(lines_list, pattern):
            result = []

            # The Harmony log file gets deleted when Harmony is closed, but most of the
            # time the artists keep working in the same session, thus the same Harmony
            # log will be read by our script.
            # We need to define a threshold in seconds to allow the validation to read the lines.
            # This is because a single harmony session may contain several blocks of
            # validations and comparing the hour when the lines were written to the
            # current hour is the only way to ensure we are reading the right block, and
            # not one from a previous check, perhaps a different scene or even a different
            # project.
            max_time_in_secs = 300

            for line in lines_list:
                match = re.match(pattern, line)
                if not match:
                    continue
                file_path = match.groupdict().get("file_path")

                # Compare the hour read from the file to the current hour
                hour_str = match.groupdict().get("hour")
                time_obj = get_time_obj_from_string(hour_str)
                diff_in_seconds = get_time_difference_from_now(time_obj)

                # Only if the diff is equal or less than our threshold, we will append
                # the path to the result list.
                if diff_in_seconds <= max_time_in_secs:
                    result.append(file_path)

            return result

        # Define match patterns for the different cases
        FILE_DOESNT_EXIST_PATTERN = re.compile(
            (
                r"(?P<head>^[A-Z]{1}: (?P<hour>\d{1,2}:\d{1,2}:\d{1,2}.\d{1,3}) )"
                r"(?P<tag>file does not exists )"
                r"\("
                r"(?P<file_path>.+)"
                r"\)$"
            )
        )

        FILE_UNABLE_TO_READ_PATTERN = re.compile(
            (
                r"(?P<head>^[A-Z]{1}: (?P<hour>\d{1,2}:\d{1,2}:\d{1,2}.\d{1,3}) )"
                r"(?P<tag>unable to read )"
                r"(?P<file_path>.+\.[a-zA-Z]+)"
            )
        )

        ILLEGAL_FILE_PATTERN = re.compile(
            (
                r"(?P<head>^[A-Z]{1}: (?P<hour>\d{1,2}:\d{1,2}:\d{1,2}.\d{1,3}) )"
                r"(?P<tag>### SECURITY WARNING)"
                r":.+\("
                r"(?P<file_path>.+)"
                r"\).+$"
            )
        )

        # create a new temp file to write data to
        temp_dir = tempfile.gettempdir()
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)
        file_path = os.path.join(temp_dir, "check_file_integrity.txt")

        # fix path
        file_path = file_path.replace("\\\\", "/")
        file_path = file_path.replace("\\", "/")

        # if file_path already exists, remove it so we can start from scratch and to
        # avoid inconsistencies (if the file already exists, it may return the results
        # of a previous scene instead of the current one)
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
            except:
                self.parent.logger.info(
                    "Couldn't remove file at the beginning of 'get_problematic_files': {}".format(
                        file_path
                    )
                )

        # build harmony script
        script_str = (
            """
function check_files_integrity()
{
    var separator = "***---***";

    var log = MessageLog.trace

    // ---------------------------------------------------------------------------------
    // run command
    log(separator + " start");
    var check_files = scene.checkFiles(
        {
            allFiles: false,
            colors: true
        }
    );
    log(separator + " end");
    // ---------------------------------------------------------------------------------
    // get log
    var msg_log = MessageLog.getLog();

    return msg_log;
}

function write_log_to_file(file_path, msg_log)
{
    var file = new File(file_path);
    file.open(2);
    file.write(msg_log);
    file.close();
}

var msg_log = check_files_integrity();
write_log_to_file("%s", msg_log)
"""
            % file_path
        )

        self.parent.logger.debug("files_integrity_script_str:\n{}".format(script_str))

        engine = self.parent.engine
        engine.app.custom_script(script_str)

        # safe belt to ensure the custom script finished writing the file
        while not os.path.exists(file_path):
            self.parent.logger.warning(
                "File '{}' doesn't exist yet. Waiting 2 more seconds.".format(file_path)
            )
            time.sleep(2)

        self.parent.logger.info(
            "File '{}' finally exists, continuing.".format(file_path)
        )

        # TODO: compare the lines date to the current date, to avoid readin reports from
        # different sessions
        selected_lines = select_lines_based_on_start_and_end_strings(file_path)
        self.parent.logger.info("selected_lines: {}".format(selected_lines))

        # non_existent_files_list = []
        # unable_to_read_files_list = []
        # illegal_files_list = []

        problematic_files = {
            "non_existent_files": [],
            "unable_to_read_files": [],
            "illegal_files": [],
        }

        if not selected_lines:
            self.parent.logger.error(
                "Couldn't get any selected line (regex not working?)"
            )
            return None

        non_existent_files_list = filter_lines(
            selected_lines, FILE_DOESNT_EXIST_PATTERN
        )
        unable_to_read_files_list = filter_lines(
            selected_lines, FILE_UNABLE_TO_READ_PATTERN
        )
        illegal_files_list = filter_lines(selected_lines, ILLEGAL_FILE_PATTERN)

        if non_existent_files_list:
            msg = "Found {} non-existent files:\n{}\n".format(
                len(non_existent_files_list), pf(non_existent_files_list)
            )
            problematic_files["non_existent_files"] = non_existent_files_list

        if unable_to_read_files_list:
            msg = "Found {} unable to read files:\n{}\n".format(
                len(unable_to_read_files_list), pf(unable_to_read_files_list)
            )
            problematic_files["unable_to_read_files"] = unable_to_read_files_list

        if illegal_files_list:
            msg = "Found {} illegal files:\n{}\n".format(
                len(illegal_files_list), pf(illegal_files_list)
            )
            problematic_files["illegal_files"] = illegal_files_list

        self.parent.logger.info(
            "File exists after getting all data: {}".format(os.path.exists(file_path))
        )

        # Before leaving the function, try to delete the json file written by Harmony
        # to ensure the next time we run the validation, a new file will be created.
        try:
            os.remove(file_path)
        except:
            self.parent.logger.info(
                "Couldn't remove file at the end of 'get_problematic_files': {}".format(
                    file_path
                )
            )

        return problematic_files

    def publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.

        :param settings: Dictionary of Settings. The keys are strings, matching
                         the keys returned in the settings property.
                         The values are `Setting` instances.
        :param item: Item to process
        """

        # get the path in a normalized state. no trailing separator, separators
        # are appropriate for current os, no double separators, etc.
        path = sgtk.util.ShotgunPath.normalize(_session_path())

        # ensure the session is saved
        # _save_session()

        # update the item with the saved session path
        item.properties["path"] = path

        # add dependencies for the base class to register when publishing
        item.properties[
            "publish_dependencies"
        ] = _harmony_find_additional_session_dependencies()

        # let the base class register the publish
        super(HarmonySessionPublishPlugin, self).publish(settings, item)
        item.properties.sg_publish_path = item.properties.sg_publish_data["path"][
            "local_path"
        ]

        # Update task status for revision
        sg_task = self.parent.context.task
        self.parent.logger.info(
            "End of Publish and updating task with id: {} to status: 'rev'".format(
                str(sg_task["id"])
            )
        )

        try:
            self.parent.engine.shotgun.update(
                "Task", sg_task["id"], {"sg_status_list": "rev"}
            )
        except Exception as e:
            msg = (
                "Couldn't update task with id: {} to status: 'rev', error: {}, "
                "full tracekback:\n{}"
            ).format(sg_task["id"], str(e), traceback.format_exc())
            self.parent.logger.error(msg)

    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed, and can for example be used to version up files.

        :param settings: Dictionary of Settings. The keys are strings, matching
                         the keys returned in the settings property.
                         The values are `Setting` instances.
        :param item: Item to process
        """

        # do the base class finalization
        super(HarmonySessionPublishPlugin, self).finalize(settings, item)

        # bump the session file to the next version
        self._save_to_next_version(item.properties["path"], item, _save_session)

    def _copy_work_to_publish(self, settings, item):
        """
        This method handles copying work file path(s) to a designated publish
        location.

        This method requires a "work_template" and a "publish_template" be set
        on the supplied item.

        The method will handle copying the "path" property to the corresponding
        publish location assuming the path corresponds to the "work_template"
        and the fields extracted from the "work_template" are sufficient to
        satisfy the "publish_template".

        The method will not attempt to copy files if any of the above
        requirements are not met. If the requirements are met, the file will
        ensure the publish path folder exists and then copy the file to that
        location.

        If the item has "sequence_paths" set, it will attempt to copy all paths
        assuming they meet the required criteria with respect to the templates.

        """
        publisher = self.parent
        dcc_app = publisher.engine.app

        # ---- ensure templates are available
        work_template = item.properties.get("work_template")
        if not work_template:
            self.logger.debug(
                "No work template set on the item. "
                "Skipping copy file to publish location."
            )
            return

        publish_template = self.get_publish_template(settings, item)
        if not publish_template:
            self.logger.debug(
                "No publish template set on the item. "
                "Skipping copying file to publish location."
            )
            return

        # ---- get a list of files to be copied

        # by default, the path that was collected for publishing
        work_file = item.properties.path

        # ---- copy the work files to the publish location
        if not work_template.validate(work_file):
            self.logger.warning(
                "Work file '%s' did not match work template '%s'. "
                "Publishing in place." % (work_file, work_template)
            )
            return

        work_fields = work_template.get_fields(work_file)

        missing_keys = publish_template.missing_keys(work_fields)

        if missing_keys:
            self.logger.warning(
                "Work file '%s' missing keys required for the publish "
                "template: %s" % (work_file, missing_keys)
            )
            return

        publish_file = publish_template.apply_fields(work_fields)

        keep_folders = settings.get("Copy to Publish folders").value

        self.logger.info("Copy xstage dependencies to publish area... ")
        dcc_app.save_project_as(
            source_file=work_file,
            target_file=publish_file,
            open_project=False,
            keep_folders=keep_folders,
        )

        (
            collected_directories,
            collected_files,
        ) = self._collecting_unwanted_files_inside_xstage_dependencies(
            harmony_files_path=publish_file
        )

        self._delete_files_inside_xstage_dependecies(
            list_of_directories=collected_directories, list_of_files=collected_files
        )

    def _collecting_long_file_paths(self, session_path):
        self.logger.info("Collecting long file paths...")

        long_paths = []
        limit_character_number = 240

        session_path_base_dir = os.path.dirname(session_path)

        for dirpath, dirnames, filenames in os.walk(session_path_base_dir):
            for file in filenames:
                path = os.path.join(dirpath, file)

                if len(path) >= limit_character_number:
                    if not ".thumbnails" in path and not "-small.tga" in path:
                        long_paths.append(path)

        return long_paths

    def _collecting_unwanted_files_inside_xstage_dependencies(self, harmony_files_path):
        """
        Unwanted files are files that are not necessary for the publish and that might
        cause issues when transferring the files to the server.

        At the moment we collect:
            .thumbnails
            -small.tga
            -any .xstage, .aux, .xstage~ or .aux~ file that is not the main file
        """

        def collect_unwanted_xstage_and_aux_files(basename, target_dir):
            """
            Clean up Harmony folder: delete all other xstage and aux folders that are not
            the published one
            """

            collected_files = []

            # we are only interested in delete files with these extensions, all others will
            # be kept regardless of theirs names
            filter_extensions = [".xstage", ".aux", r".xstage~", r".aux~", ".thumbnails"]

            # basename = os.path.basename(source_path)
            basename, ext = os.path.splitext(basename)

            # get possible names to keep list
            keep_files_names = ["{}{}".format(basename, ext) for ext in filter_extensions]

            # add thumbnails file for the current scene to the keep_files_names list
            for file_ in keep_files_names:
                if basename in file_ and file_.endswith(".xstage"):
                    keep_files_names.append("{}.thumbnails".format(file_))
                    break

            self.parent.logger.info("keep_files_names: {}".format(keep_files_names))

            # iterate through files in target_dir
            for file_name in os.listdir(target_dir):
                file_path = os.path.join(target_dir, file_name)

                # check if the file has the specified extension and not in keep_files_names
                if (
                    any(file_name.endswith(ext) for ext in filter_extensions)
                    and file_name not in keep_files_names
                    and os.path.isfile(file_path)
                ):
                    collected_files.append(file_path)

            return collected_files

        self.logger.info(
            "Collecting thumbnail and -small.tga files inside xstage dependencies."
        )
        self.parent.logger.info(
            "Collecting thumbnail and -small.tga files inside xstage dependencies."
        )
        self.parent.logger.debug("harmony_files_path: {}".format(harmony_files_path))

        harmony_base_dir = os.path.dirname(harmony_files_path)
        harmony_elements_dir = os.path.join(harmony_base_dir, "elements")
        self.parent.logger.info(
            "Collecting files in harmony_elements_dir: {}".format(harmony_elements_dir)
        )

        thumbnail_files = []
        small_tga_files = []
        collected_files = []
        collected_directories = []

        for dirpath, dirnames, filenames in os.walk(harmony_elements_dir):
            if ".thumbnails" in dirpath:
                thumbnail_files.extend([os.path.join(dirpath, f) for f in filenames])
                collected_files.extend([os.path.join(dirpath, f) for f in filenames])
                thumbnail_files.append(dirpath)
                collected_directories.append(dirpath)

            for file_ in filenames:
                if "-small.tga" in file_:
                    if os.path.join(dirpath, file_) not in small_tga_files:
                        small_tga_files.append(os.path.join(dirpath, file_))
                        collected_files.append(os.path.join(dirpath, file_))

        self.logger.info("Collected thumbnail files.")
        self.parent.logger.info(
            "Collected {} thumbnail files.".format(len(thumbnail_files))
        )
        self.parent.logger.debug(
            "Collected thumbnail files:\n{}".format(pf(thumbnail_files))
        )
        self.logger.info("Collected -small.tga files.")
        self.parent.logger.info(
            "Collected {} -small.tga files.".format(len(small_tga_files))
        )
        self.parent.logger.debug(
            "Collected -small.tga files:\n{}".format(pf(small_tga_files))
        )

        # collect any .xstage, .aux, .xstage~ or .aux~ files that are not the main file

        self.logger.info(
            "Collecting unnecessary xstage and aux files."
        )
        self.parent.logger.info(
            "Collecting unnecessary xstage and aux files."
        )

        basename = os.path.basename(harmony_files_path)
        basename, ext = os.path.splitext(basename)
        self.parent.logger.info(
            "harmony_base_dir: {}".format(harmony_base_dir)
        )
        collected_xstage_aux_files = collect_unwanted_xstage_and_aux_files(
            basename, harmony_base_dir
        )
        self.parent.logger.info(
            "Collected {} unwanted xstage and aux files.".format(
                len(collected_xstage_aux_files)
            )
        )
        self.parent.logger.info(
            "Collected unwanted xstage and aux files:\n{}".format(
                pf(collected_xstage_aux_files)
            )
        )
        collected_files.extend(collected_xstage_aux_files)

        return collected_directories, collected_files

    def _delete_files_inside_xstage_dependecies(
        self, list_of_directories=[], list_of_files=[]
    ):
        if list_of_directories:
            self.logger.info("Deleting folders inside xstage dependencies.")
            self.parent.logger.info("Deleting folders inside xstage dependencies.")

            for dir_path in list_of_directories:
                if os.path.exists(dir_path):
                    try:
                        shutil.rmtree(dir_path, ignore_errors=True)
                    except:
                        self.parent.logger.info(
                            "Couldn't delete folder '{}'".format(dir_path)
                        )

            self.logger.info("Finished deleting folders. ")

        # ------------------------------------------------------------------------------

        if list_of_files:
            self.logger.info("Deleting files inside xstage dependencies.")
            self.parent.logger.info("Deleting files inside xstage dependencies.")

            for file_path in list_of_files:
                if os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                    except:
                        self.parent.logger.info(
                            "Couldn't delete file '{}'".format(file_path)
                        )

            self.logger.info("Finished deleting -small.tga files. ")

    def get_active_display(self):
        dcc_app = self.parent.engine.app
        cmds = 'scene.getDefaultDisplay();'
        return dcc_app.custom_script(cmds)

    def get_main_display(self):
        dcc_app = self.parent.engine.app
        cmds  = """

include("harmony_utility_functions.js");

get_main_display_node();

    """
        return dcc_app.custom_script(cmds)

    def set_active_display(self, main_display_node):
        dcc_app = self.parent.engine.app
        cmds = 'node.setAsGlobalDisplay("{}");'.format(main_display_node)
        dcc_app.custom_script(cmds)



def _harmony_find_additional_session_dependencies():
    """
    Find additional dependencies from the session
    """

    return []


def _session_path():
    """
    Return the path to the current session
    :return:
    """
    engine = sgtk.platform.current_engine()

    # get the path to the current file
    path = engine.app.get_current_project_path()

    engine.logger.info("Current project path: {}".format(path))

    # while loop to ensure that the path is returned correctly
    wrong_paths = [None, "", "Unknown"]
    counter = 0
    while counter < 5 and path in wrong_paths:
        time.sleep(1)
        path = engine.app.get_current_project_path()
        counter += 1
        engine.logger.info("------------")
        engine.logger.info("get_current_project_path, try: {}".format(counter + 1))
        engine.logger.info("Current project path: {}".format(path))

    if path in wrong_paths:
        raise RuntimeError("Could not get the current project path.")

    if isinstance(path, six.text_type):
        path = six.ensure_str(path)

    return path


def _save_session(path=None):
    """
    Save the current session to the supplied path.
    """

    engine = sgtk.platform.current_engine()
    if path is None:
        engine.app.save_project()
    else:
        # Ensure that the folder is created when saving
        folder = os.path.dirname(path)
        ensure_folder_exists(folder)

        # we are saving a new version, so we only need the name of the file
        _, filename = os.path.split(path)
        filename_file, _ = os.path.splitext(filename)
        engine.app.save_new_version(filename_file)


# TODO: method duplicated in all the Harmony hooks
def _get_save_as_action():
    """
    Simple helper for returning a log action dict for saving the session
    """

    engine = sgtk.platform.current_engine()

    callback = _save_as

    # if workfiles2 is configured, use that for file save
    if "tk-multi-workfiles2" in engine.apps:
        app = engine.apps["tk-multi-workfiles2"]
        if hasattr(app, "show_file_save_dlg"):
            callback = app.show_file_save_dlg

    return {
        "action_button": {
            "label": "Save As...",
            "tooltip": "Save the current session",
            "callback": callback,
        }
    }


def _save_as():
    engine = sgtk.platform.current_engine()
    engine.app.save_new_version_action()


def fix_path(path):
    path = path.replace("\\\\", "/")
    path = path.replace("\\", "/")

    return path
