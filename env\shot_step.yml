# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

description: Apps and Engines related to Shot based work.

################################################################################

includes:
- ./includes/frameworks.yml
- ./includes/settings/tk-desktop2.yml
- ./includes/settings/tk-3dsmax.yml
- ./includes/settings/tk-houdini.yml
- ./includes/settings/tk-maya.yml
- ./includes/settings/tk-motionbuilder.yml
- ./includes/settings/tk-nuke.yml
- ./includes/settings/tk-photoshopcc.yml
- ./includes/settings/tk-aftereffects.yml
- ./includes/settings/tk-shell.yml
- ./includes/settings/tk-shotgun.yml
- ./includes/settings/tk-harmony.yml
- ./includes/settings/tk-fusion.yml
- ./includes/settings/tk-krita.yml
- ./includes/settings/tk-blender.yml

################################################################################
# configuration for all engines to load in a shot step context

engines:
  # referencing the DCC-specific shot_step environments included above
  tk-desktop2: "@settings.tk-desktop2.all"
  tk-3dsmax: "@settings.tk-3dsmax.shot_step"
  tk-houdini: "@settings.tk-houdini.shot_step"
  tk-maya: "@settings.tk-maya.shot_step"
  tk-motionbuilder: "@settings.tk-motionbuilder.shot_step"
  tk-nuke: "@settings.tk-nuke.shot_step"
  tk-nukestudio: "@settings.tk-nuke.nukestudio.shot_step"
  tk-photoshopcc: "@settings.tk-photoshopcc.shot_step"
  tk-aftereffects: "@settings.tk-aftereffects.shot_step"
  tk-shell: "@settings.tk-shell.shot_step"
  tk-shotgun: "@settings.tk-shotgun.shot_step"
  tk-harmony: "@settings.tk-harmony.shot_step"
  tk-fusion: "@settings.tk-fusion.shot_step"
  tk-krita: "@settings.tk-krita.shot_step"
  tk-blender: "@settings.tk-blender.shot_step"

################################################################################
# reference all of the common frameworks

frameworks: "@frameworks"
