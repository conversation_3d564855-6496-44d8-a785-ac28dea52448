# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

import os

import sgtk


__author__ = "Diego Garcia Huert<PERSON>"
__contact__ = "https://www.linkedin.com/in/diegogh/"


HookBaseClass = sgtk.get_hook_baseclass()


SESSION_PUBLISHED_TYPE = "Toon Boom Harmony Project File"


class HarmonySessionCollector(HookBaseClass):
    """
    Collector that operates on the Toon Boom Harmony session. Should inherit
    from the basic collector hook.
    """

    @property
    def settings(self):
        """
        Dictionary defining the settings that this collector expects to receive
        through the settings parameter in the process_current_session and
        process_file methods.

        A dictionary on the following form::

            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }

        The type string should be one of the data types that toolkit accepts as
        part of its environment configuration.
        """

        # grab any base class settings
        collector_settings = super(HarmonySessionCollector, self).settings or {}

        # settings specific to this collector
        harmony_session_settings = {
            "Work Template": {
                "type": "template",
                "default": None,
                "description": "Template path for artist work files. Should "
                "correspond to a template defined in "
                "templates.yml. If configured, is made available"
                "to publish plugins via the collected item's "
                "properties. ",
            }
        }

        # update the base settings with these settings
        collector_settings.update(harmony_session_settings)

        return collector_settings

    def process_current_session(self, settings, parent_item):
        """
        Analyzes the current session open in Toon Boom Harmony and parents a
        subtree of items under the parent_item passed in.

        :param dict settings: Configured settings for this collector
        :param parent_item: Root item instance

        """

        # create an item representing the current Toon Boom Harmony session
        item = self.collect_current_harmony_session(settings, parent_item)

    def get_export_path(self, settings):
        publisher = self.parent

        work_template = None
        work_template_setting = settings.get("Work Template")
        if work_template_setting:
            work_template = publisher.engine.get_template_by_name(work_template_setting.value)

            self.logger.debug("Work template defined for Toon Boom Harmony collection.")

        work_export_template = None
        work_export_template_setting = settings.get("Work Export Template")
        if work_export_template_setting:
            self.logger.debug(
                "Work Export template settings: %s" % work_export_template_setting
            )

            work_export_template = publisher.engine.get_template_by_name(
                work_export_template_setting.value
            )

            self.logger.debug("Work Export template defined for Toon Boom Harmony collection.")

        if work_export_template and work_template:
            path = publisher.engine.app.get_current_project_path()
            fields = work_template.get_fields(path)
            export_path = work_export_template.apply_fields(fields)

            self.logger.debug("Work Export Path is: %s " % export_path)

            return export_path

    def collect_current_harmony_session(self, settings, parent_item):
        """
        Creates an item that represents the current Toon Boom Harmony session.

        :param parent_item: Parent Item instance

        :returns: Item of type harmony.session
        """

        publisher = self.parent
        engine = sgtk.platform.current_engine()

        # get the path to the current file
        path = engine.app.get_current_project_path()

        # determine the display name for the item
        if path:
            file_info = publisher.util.get_file_path_components(path)
            display_name = file_info["filename"]
        else:
            display_name = "Current Toon Boom Harmony Session"

        # create the session item for the publish hierarchy
        session_item = parent_item.create_item(
            "harmony.session", "Toon Boom Harmony Session", display_name
        )

        # get the icon path to display for this item
        icon_path = os.path.join(self.disk_location, os.pardir, "icons", "session.png")
        session_item.set_icon_from_path(icon_path)

        # if a work template is defined, add it to the item properties so
        # that it can be used by attached publish plugins
        work_template_setting = settings.get("Work Template")
        if work_template_setting:

            work_template = publisher.engine.get_template_by_name(work_template_setting.value)

            # store the template on the item for use by publish plugins. we
            # can't evaluate the fields here because there's no guarantee the
            # current session path won't change once the item has been created.
            # the attached publish plugins will need to resolve the fields at
            # execution time.
            session_item.properties["work_template"] = work_template
            session_item.properties["publish_type"] = SESSION_PUBLISHED_TYPE

            self.logger.debug("Work template defined for session.")

        # set scene_node burnin and scene_peg attributes if they exist in the scene
        self.set_scene_burnIn_attrs()

        self.logger.info("Collected current Toon Boom Harmony session")

        return session_item

    def set_scene_burnIn_attrs(self):
        """
        Set attributes to scene_node and scene_peg if they exist in the scene
        """

        self.parent.logger.info("About to set scene_node and scene_peg attributes")

        script_str = """
function get_scene_burnin_node()
{
    var nodes = node.subNodes("Top");
    log(nodes.length);
    for (var i= 0; i<nodes.length; ++i)
    {
        // log(node.type(nodes[i]))
        if (node.type(nodes[i]) == "BurnIn")
        {
            var node_name = node.getName(nodes[i]);
            if (node_name.toLowerCase().search("scene") != -1)
            {
                log(nodes[i]);
                return nodes[i];
            };
        };
    };
    // if scene_node couldn't be found
    return null;
};


function set_scene_node_attr(scene_node)
{
    var scene_name = scene.currentVersionName();
    var scene_attr_str = "Scene: " + scene_name;
    log(scene_attr_str);

    var attr = node.getAttr(scene_node, 1, "printinfo");
    node.setTextAttr(scene_node, attr.keyword(), frame.current(), scene_attr_str);
};


function set_scene_peg_attrs(scene_peg)
{
    var attr_x = node.getAttr(scene_peg, 1, "position.x");
    // var attr_y = node.getAttr(scene_peg, 1, "position.y");

    attr_x.setValue(0);
    // attr_y.setValue(0);
};


var log = MessageLog.trace;
// Object.getOwnPropertyNames(String.prototype)

var scene_node = get_scene_burnin_node();
var scene_peg = node.srcNode(scene_node, 0);

set_scene_node_attr(scene_node);
set_scene_peg_attrs(scene_peg);
"""

        engine = self.parent.engine
        engine.app.custom_script(script_str)
