#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

"""
An app that syncs the frame range between a scene and a shot in Shotgun.
Plus adding and modifiying the scene Ren<PERSON> Settings

"""

import sys
import os

from tank import Hook
from tank import TankError
from tank.platform import Application
from tank.platform.qt import QtCore, QtGui
import tank

import os
import re


class ProcessItemsHook(Hook):

    def execute(self, entity_type, entities, other_params, **kwargs):

        print("Loading framework...")
        mty_rigBeast = self.load_framework("mty-framework-autorig")
        mty_rigBeast.mtyMayaAutorig.get_output()

        return {'succes': [1], 'messages': [], 'errors': []}
