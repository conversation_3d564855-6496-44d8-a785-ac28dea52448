import os
import sgtk

HookBaseClass = sgtk.get_hook_baseclass()

class BasicActions(HookBaseClass):
    def generate_actions(self, sg_publish_data, actions, ui_area):
        """
        Returns a list of action instances for a particular publish.

        :param sg_publish_data: Shotgun data dictionary with all the standard publish fields.
        :param actions: List of action strings which have been defined in the app configuration.
        :param ui_area: String denoting the UI Area.
        :returns List of dictionaries, each with keys name, params, caption and description
        """
        action_instances = []

        if "download_publish" in actions:
            action_instances.append({
                "name": "download_publish",
                "params": None,
                "caption": "Download File",
                "description": "Download the file to your local machine."
            })

        return action_instances

    def execute_multiple_actions(self, actions):
        """
        Executes the specified action on a list of items.

        :param actions: List of dictionaries containing the actions to execute.
                       Each entry will have the following values:
                           name: Name of the action to execute
                           sg_publish_data: Publish information coming from Shotgun
                           params: Parameters passed down from the generate_actions hook.
        """
        for single_action in actions:
            name = single_action["name"]
            sg_publish_data = single_action["sg_publish_data"]
            params = single_action["params"]
            self.execute_action(name, params, sg_publish_data)

    def execute_action(self, name, params, sg_publish_data):
        """
        Execute a given action.

        :param name: Action name string representing one of the items returned by generate_actions.
        :param params: Params data, as specified by generate_actions.
        :param sg_publish_data: Shotgun data dictionary with all the standard publish fields.
        """
        if name == "download_publish":
            path = self.get_publish_path(sg_publish_data)
            self.ensure_file_is_local(path, sg_publish_data)

    def ensure_file_is_local(self, path, publish):
        """
        Ensures that the file is available locally.

        :param path: Path to the file.
        :param publish: The publish entity dictionary.
        :returns: The path to the local file.
        """
        if not hasattr(self, 'metasync'):
            self.metasync = self.parent.engine.custom_frameworks.get(
                "mty-framework-metasync"
            )
            if not self.metasync:
                self.metasync = self.load_framework("mty-framework-metasync")

        transfersManager = self.metasync.transfersManager

        if "%" in path:
            dirname = os.path.dirname(path)
            if os.path.exists(dirname) and self._collect_sequenced_files(path):
                transfersManager.ensure_local_dependencies(publish)
                return path
        else:
            if os.path.exists(path):
                transfersManager.ensure_local_dependencies(publish)
                return path

        transfersManager.ensure_file_is_local(path, publish)
        transfersManager.ensure_local_dependencies(publish)

        return path