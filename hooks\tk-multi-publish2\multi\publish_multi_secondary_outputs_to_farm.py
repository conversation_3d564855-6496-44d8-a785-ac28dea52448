import os
import sys
import json
import pprint

import sgtk


HookBaseClass = sgtk.get_hook_baseclass()
pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)
pf = pprint.pformat


class SendSecondaryOutputsToFarm(HookBaseClass):
    @property
    def name(self):
        return "SendSecondaryOutputsToFarm"

    def post_finalize(self, publish_tree):
        # creat info to write in metada field
        # to create secondary output in farm\

        root_item = publish_tree.root_item

        # get from root item dict with info for jobs
        dict_secondary_outputs = root_item.properties.get(
            "secondary_outputs_to_farm", {}
        )

        if dict_secondary_outputs:

            primary_item = None
            primary_item_data = None

            # Allways the fist item is primary
            for item in publish_tree:
                sg_publishes_data = self.get_published_file_data(item)

                if not primary_item and not primary_item_data:
                    primary_item = item
                    primary_item_data = sg_publishes_data

                items_checked = []
                for _item in publish_tree:
                    if _item.checked:
                        self.parent.engine.logger.info(
                            f"item checked: {_item.name} checked: {_item.checked}"
                        )
                        items_checked.append(_item.name)
                    else:
                        self.parent.engine.logger.info(
                            f"item checked: {_item.name} checked: {_item.checked}"
                        )

                if items_checked:
                    dict_secondary_outputs["items_checked"] = items_checked

                if primary_item:
                    break

            self.resolve_metadata(
                dict_secondary_outputs,
                primary_item,
                primary_item_data
            )

        super(SendSecondaryOutputsToFarm, self).post_finalize(publish_tree)

    def resolve_metadata(self, dict_secondary_outputs, primary_item, primary_item_data):
        engine_name = self.parent.engine.name

        # condition for engine
        # ====================================================================
        ### Maya condition
        if engine_name == "tk-maya":
            maya_version = self.parent.engine.execute_hook_expression(
                "{config}/tk-multi-publish2/maya/collector_alembic.py",
                "get_maya_version",
            )
            dict_secondary_outputs["maya_version"] = maya_version
            dict_secondary_outputs["work_template"] = primary_item.properties.get(
                "work_template", ""
            ).name
            dict_secondary_outputs["publish_template"] = primary_item.properties.get(
                "publish_template", ""
            ).name
        # ====================================================================

        # selecto Hook to execute on farm
        hook_expression_dict = {
            "tk-photoshopcc": {
                "hook_expression": "{config}/mty-framework-deadline/photoshop_publish_secondary_outputs.py",
                "method_name": "publish_secondary_outputs",
            },
            "tk-maya": {
                "hook_expression": "{config}/mty-framework-deadline/boostrap_secundary_outputs_maya.py",
                "method_name": "process_secondary_outputs",
            },
        }

        if engine_name in hook_expression_dict:
            hook_expression = hook_expression_dict[engine_name]["hook_expression"]
            method_name = hook_expression_dict[engine_name]["method_name"]
        else:
            hook_expression = (
                "{config}/mty-framework-deadline/testing_and_example_job.py"
            )
            method_name = "testing_job"

        # self.parent.engine.logger.info("primary_item.properties: {}".format(primary_item.properties.to_dict()))

        if not dict_secondary_outputs:
            # end the process if there is no secondary outputs
            return

        # get current info data of the config
        framework_deadline = self.load_framework("mty-framework-deadline")
        deadline_utils = framework_deadline.deadlineSubmitJobUtils
        config_data = deadline_utils.get_current_config_info(self.parent.engine)

        # get jobs values
        # deafult values
        jobs_values = {
        "OverrideJobFailureDetection": True,
        "FailureDetectionJobErrors": 1,
        }

        farm_group = self.get_farm_group_for_engine(engine_name)
        if farm_group:
            jobs_values["Group"] = farm_group


        # create the dictionary to put in metadata
        base_info = {
            "secondary_publish_on_farm": "pending",
            "job_settings": {
                "Action": "ExecuteHook",
                "HookExpression": hook_expression,
                "MethodName": method_name,
                "HookKwargs": {
                    "engine_name": engine_name,
                    "secondary_outputs_to_farm": dict_secondary_outputs
                },
            },
            "jobs_values": jobs_values,
            "config_info": config_data,
        }
        format_job_info = json.dumps(base_info)
        self.parent.shotgun.update(
            "PublishedFile",
            primary_item_data["id"],
            {"sg_metadata": format_job_info},
        )


    def get_farm_group_for_engine(self, engine_name):
        """
        Get the custom group for the engine
        from overide value, if not exist return None
        """

        result = None
        valueoverrides = self.parent.engine.custom_frameworks.get(
        "mty-framework-valueoverrides"
        ) or self.load_framework("mty-framework-valueoverrides")

        # Get excluded tasks from value override in case it exists
        if valueoverrides:
            default_value_code = "mty.multi.deadline.select_group_for_engine"

            procress_grps = valueoverrides.get_value(
                default_value_code)

            self.parent.engine.logger.info("Overide Farm Group: {}".format(procress_grps))

            if procress_grps:
                dict_process_grps = json.loads(procress_grps)
                engine_grp = dict_process_grps.get(engine_name)
                if engine_grp:
                    result = engine_grp

        return result
