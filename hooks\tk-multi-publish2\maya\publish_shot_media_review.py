# -*- coding: utf-8 -*-

# HOOK NOT IN USE, DISCARDED I COMMIT a1a893eae2bce7ca0e850135c2b3eb5ad01ca4a2

# Standard library:
import os
import re
import six
import json
import pprint
import traceback
import subprocess
#   .   .   .   .   .   .   .   .   .   .   .
# Third party:
import sgtk
import maya.cmds as cmds
import maya.mel as mel
import pymel.core as pm
from sgtk.platform.qt import QtCore

#   .   .   .   .   .   .   .   .   .   .   .
# Project:
# ================================================================

pp = pprint.pprint
pf = pprint.pformat

HookBaseClass = sgtk.get_hook_baseclass()


class MayaShotReviewPublishPlugin(HookBaseClass):
    def __init__(self, parent):
        super(MayaShotReviewPublishPlugin, self).__init__(parent)

        engine = self.parent.engine

        # Apps . . . . . . . . . . . . . . . . . . . . . .
        app_playblast = engine.apps.get("mty-maya-playblast")

        module_playblast_tools = app_playblast.import_module("playblast_tools")
        self.playblast_ops = module_playblast_tools.playblast_operations(app_playblast)

        module_convert_to_video = app_playblast.import_module("convert_to_video")
        self.build_ffmpeg_cmd = module_convert_to_video.convert_to_video(app_playblast)

        # frameworks   . . . . . . . . . . . . . . . . . . . . . .

        self.coremayatools = self.load_framework("mty-framework-coremayatools")

        self.mayacapture = self.load_framework("mty-framework-mayacapture")

        self.ffmpeg = self.load_framework("mty-framework-ffmpeg")

        #   . . . . . . . . . . . . . . . . . . . . . .
        if parent.engine.context.step['name'] == 'Animation':
            self.setup_viewport()

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    @property
    def icon(self):

        return os.path.join(
            self.disk_location,
            os.pardir,
            "icons",
            "review.png"
        )

    @property
    def name(self):

        return "Publish Media Review"

    @property
    def description(self):

        return """
        <p>This plugin publish a playblast from the current shot.</p>

        """

    @property
    def settings(self):

        # inherit the settings from the base publish plugin
        plugin_settings = super(
            MayaShotReviewPublishPlugin, self).settings or {}

        # settings specific to this class
        review_publish_settings = {
            "Work Template": {
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "Primary Publish Template": {
                "type": "template",
                "default": None,
                "description": "Template path for main scene publish. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "Review Publish Template": {
                "type": "template",
                "default": None,
                "description": "Template path for published shot playblast. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "Publish type": {
                "type": "string",
                "default": "Media Review",
                "description": "The published file type to register.",
            },
            "Camera Pattern": {
                "type": "list",
                "description": "The camera shape name pattern to look for."
                               "It should contain a '%s:' at the begining which will be replaced with the shot namespace",
            },
            "Camera Export Review": {
                "type": "string",
                "default": None,
                "description": "List of camera type select for review",
            },
            "sg_in_frame_field": {
                "type": "string",
                "default": "sg_cut_out",
                "description": "The Shotgun Shot field for the last frame.",
            },
            "sg_out_frame_field": {
                "type": "string",
                "default": "sg_cut_in",
                "description": "The Shotgun Shot field for the first frame.",
            },
            "width": {
                "type": "int",
                "default": 1920,
                "description": "The pixel With for the review media.",
            },
            "height": {
                "type": "int",
                "default": 1080,
                "description": "The pixel Height for the review media.",
            },
            'tasks_to_find_audio': {
                'type': 'string',
                'default': 'Editorial',
                'description': 'Task to validate last published audio',
            },
        }

        # update the base settings
        plugin_settings.update(review_publish_settings)

        return plugin_settings

    @property
    def item_filters(self):
        return ["maya.session.shot.review"]

    def accept(self, settings, item):
        return {"accepted": True, "checked": True}

    # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

    def validate_frame_range(self, sg_cut_in, sg_cut_out):
        # Set frame range for shot
        # framerange_app = self.parent.engine.apps.get("tk-multi-setframerange")
        # sg_in, sg_out = framerange_app.get_frame_range_from_shotgun()

        current_in = cmds.playbackOptions(query=True, minTime=True)
        current_out = cmds.playbackOptions(query=True, maxTime=True)

        if sg_cut_in != current_in or sg_cut_out != current_out:
            return False, sg_cut_in, sg_cut_out

        return True, sg_cut_in, sg_cut_out

    # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

    def validate_last_published_audio(self, settings):

        version_number = None

        audio_result = cmds.ls(type='audio')

        if not audio_result:
            self.parent.engine.logger.error(
                "No audio found in scene"
            )

            return False, version_number

        audio_result = audio_result[0]
        sound_path = cmds.sound(audio_result, file=True, q=True)
        pb_fields = ['entity', "version_number"]

        tasks_to_find = settings.get('tasks_to_find_audio').value
        self.parent.engine.logger.info(
            "Got tasks_to_find (audio validation) from hook settings"
        )
        valueoverrides = self.parent.engine.custom_frameworks.get(
            'mty-framework-valueoverrides'
        ) or self.load_framework('mty-framework-valueoverrides')
        override_code = 'mty.maya.validate_last_published_audio.tasks'
        data = valueoverrides.get_value(override_code)
        self.parent.engine.logger.info('data before json.loads: '.format(data))
        if data is not None:
            tasks_to_find = json.loads(data)
            self.parent.engine.logger.info(
                "Got tasks_to_find (audio validation) from overrides"
            )

        self.parent.engine.logger.info(
            'Tasks to validate last published audio: {}'.format(tasks_to_find)
        )

        ref_publish = sgtk.util.find_publish(
            self.parent.engine.context.sgtk,
            [sound_path],
            fields=pb_fields)
        #
        filters = [
            ['entity', 'is', self.parent.context.entity],
            ['task.Task.content', 'in', tasks_to_find],
            ['project', 'is', self.parent.context.project],
            ['published_file_type.PublishedFileType.code', 'is', 'Editorial Audio']
        ]

        fields = ['version_number', 'path']
        order = [{'field_name': 'version_number', 'direction': 'desc'}]

        publish_audio = self.parent.engine.shotgun.find_one(
            'PublishedFile', filters, fields, order
        )

        if publish_audio:
            version_number = publish_audio['version_number']
            if publish_audio['version_number'] != ref_publish[sound_path][
                "version_number"]:
                self.parent.engine.logger.error(
                    "Audio version is not the same as the latest published audio version"
                )
                return False, version_number
            else:
                self.parent.engine.logger.info(
                    "Audio version matches the latest published audio version"
                )
                return True, version_number

        return False, version_number


    # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

    def validate(self, settings, item):

        published_audio = self.shot_published_audio(settings)
        item.properties["audio"] = published_audio

        camera_shape = self.shot_camera(settings)
        self.parent.logger.info(
            "validate camera_shape (should be the camera shape name): {}".format(
                camera_shape
            )
        )
        item.properties["camera"] = camera_shape

        shot_range = self.shot_range(settings)
        item.properties["start_frame"] = shot_range[0]
        item.properties["end_frame"] = shot_range[1]

        item.properties["shot_name"] = self.parent.context.entity['name']

        # Validate shot frame range

        frame_resut, sg_in, sg_out = self.validate_frame_range(
            shot_range[0], shot_range[1]
        )
        if not frame_resut:
            raise Exception(
                (
                    "Scene frame range does not match shotgun defined range: {} - {}, "
                    "use Sync Fram Range app to fix this."
                ).format(sg_in, sg_out)
            )

        # Validate las audio file
        validate_result, audio_version = self.validate_last_published_audio(settings)
        if not validate_result:
            raise Exception(
                'Audio version file must be updated to version number: {}'.format(
                    audio_version))

        return True

    # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

    def publish(self, settings, item):

        # create a pointer to the parent root item to store
        # any extra publishes apart form the primary one
        root_item = self.get_root_item(item)
        root_item.properties.setdefault('sg_publish_extra_data', [])
        self.parent.log_debug(
            'Storing extra publish data on root item: %s' % root_item)
        publish_extra_data = root_item.properties['sg_publish_extra_data']

        # Create the review videos
        # Get the publish path from settings template
        work_template_name = settings["Work Template"].value
        work_template = self.parent.engine.get_template_by_name(
            work_template_name)
        scene_path = os.path.abspath(cmds.file(query=True, sn=True))
        fields = work_template.get_fields(scene_path)
        publish_version = fields["version"]
        publish_template_name = settings["Review Publish Template"].value

        publish_template = self.parent.engine.get_template_by_name(
            publish_template_name
        )
        self.parent.engine.logger.info(
            "publish_template_name: {}".format(publish_template_name)
        )
        self.parent.engine.logger.info(
            "publish_template definition: {}".format(publish_template.definition)
        )
        self.parent.engine.logger.info(
            "work_template_fields:\n{}".format(pf(fields))
        )
        publish_path = publish_template.apply_fields(fields)
        self.parent.engine.logger.info(
            "publish_path: {}".format(publish_path)
        )

        scene_pub_template_name = settings["Primary Publish Template"].value

        scene_pub_template = self.parent.engine.get_template_by_name(
            scene_pub_template_name
        )

        primary_publish_path = scene_pub_template.apply_fields(fields)

        self.parent.engine.logger.info(
            "scene_pub_template_name: {}".format(scene_pub_template_name)
        )
        self.parent.engine.logger.info(
            "scene_pub_template definition: {}".format(scene_pub_template.definition)
        )
        self.parent.engine.logger.info(
            "primary_publish_path: {}".format(primary_publish_path)
        )

        # determine the publish name:
        publish_name = None

        version_pattern = re.compile(
            r"(?P<head>.+)"
            r"(?P<version_token>_v\d{3})"
            r"(?P<tail>.+)"
        )
        version_match = re.match(
            version_pattern, os.path.basename(publish_path)
        )
        if version_match:
            publish_name = "{}{}".format(
                version_match.groupdict().get("head"),
                version_match.groupdict().get("tail")
            )

        # fallback to use file name as publish name
        if not publish_name:
            publish_name = os.path.basename(publish_path)

        # showStates, currentPanel = self.enable_maya_states()

        # Find additional info from the scene:
        images_path = self.create_playblast(settings, item, publish_path, fields)

        # reference the path of teh specific audio used fo rthe playblast
        audio_node = self.shot_published_audio(settings)
        if audio_node:
            audio_path = cmds.getAttr("{}.filename".format(audio_node))
            audio_path = audio_path.replace("/", os.path.sep)

        dependencies = [primary_publish_path]
        if audio_node:
            dependencies.append(audio_path)

        sg_fields_to_update = {
            "sg_status_list": "rev",
            "task.Task.sg_status_list": "rev",
        }

        # register the publish:
        publish_data = {
            "tk": self.parent.engine.sgtk,
            "context": item.context,
            "comment": item.description,
            "path": publish_path,
            "name": publish_name,
            "version_number": publish_version,
            "thumbnail_path": item.get_thumbnail_as_path(),
            "task": self.parent.engine.context.task,
            "sg_fields": sg_fields_to_update,
            "published_file_type": settings["Publish type"].value,
            "dependency_paths": dependencies,
        }
        sg_publishes = sgtk.util.register_publish(**publish_data)

        self.parent.log_debug(
            "Adding existing publish " +
            "(id:{0}) ".format(sg_publishes['id']) +
            "as extra data for item: {0}".format(item)
        )

        publish_extra_data.append(sg_publishes)

        work_status = 'rev'

        self.parent.shotgun.update(
            "PublishedFile", sg_publishes["id"],
            {
                "sg_status_list": work_status
            }
        )

        sg_version = self.submit_version(
            images_path,
            publish_path,
            [sg_publishes],
            self.parent.engine.context.task,
            item.description,
            True,
            item.properties['start_frame'],
            item.properties['end_frame'],
            work_status
        )

        # Upload in a new thread and make our own event loop
        # to wait for the thread to finish.

        event_loop = QtCore.QEventLoop()
        thread = UploaderThread(
            self.parent, sg_version,
            publish_path,
            item.get_thumbnail_as_path(),
            True
        )

        thread.finished.connect(event_loop.quit)
        thread.start()
        event_loop.exec_()

        # Update task status for revision
        sg_task = self.parent.context.task
        self.parent.log_info(
            "End of Publish and updating task with id: " + str(
                sg_task['id']) + " to status: " + str('rev'))
        self.parent.engine.shotgun.update("Task", sg_task["id"],
                                          {"sg_status_list": 'rev'})

        # log any errors generated in the thread
        for e in thread.get_errors():
            self.parent.log_error(e)

    # ================================================================

    def setup_viewport(self):
        burnins = self.coremayatools.display.Burnins()
        burnins.change_all_visibility(enabled=False)

        pm.headsUpDisplay(
            "HUDCurrentFrame",
            visible=True,
            edit=True
        )

        burnins.HUD_shotcode(
            app=self.parent,
            HUD_name="HUDShotCode",
            enabled=True,
            event=None,
            is_sequence=False,
            block=1,
            section=5
        )

        burnins.predefined_context_HUD(
            app=self.parent,
            name="HUDUser",
            enabled=True,
            data=self.parent.context.user["name"],
            block=2,
            section=5
        )

        burnins.predefined_context_HUD(
            app=self.parent,
            name="HUDStep",
            enabled=True,
            data=self.parent.context.step["name"],
            block=3,
            section=5
        )

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def shot_camera(self, settings):
        result = None

        context_name = \
            self.parent.context.entity['name'] \
                .replace('.', '_').replace('-', '_')

        context_name_first_character = context_name[0]
        if context_name_first_character.isdigit():
            context_name = "x" + context_name

        valid_camera_names = []

        for camera_name in settings['Camera Pattern'].value:
            valid_camera_names.append(
                "{0}:{1}".format(context_name, camera_name)
            )
        self.parent.logger.info("valid_camera_names: {}".format(valid_camera_names))

        camera_shape = cmds.ls(valid_camera_names, type="camera")

        export_camera_name = settings["Camera Export Review"].value

        if export_camera_name:
            for cam in camera_shape:
                if export_camera_name in cam:
                    return cam

        # if len(camera_shape) == 0:
        #     message = \
        #         "A camera with any of the following names: " + \
        #         "\"{0}\" was not found.".format(
        #             valid_camera_names
        #         )

        #     solution = \
        #         "Solution: Reference layout alembic camera."

        #     self.logger.error(message)
        #     self.logger.warning(solution)

        #     raise Exception(message + "\n" + solution)

        elif len(camera_shape) > 1:
            message = \
                "Error: There are more than one " + \
                "shot camera in the scene " + \
                "- {0}".format(str(camera_shape))

            solution = \
                "Solution: Keep only one camera with the following " + \
                "namespace: \"{0}:\"".format(context_name)

            self.logger.error(message)
            self.logger.warning(solution)
            self.logger.warning(
                "Tip: Sequence might need to be splitted again."
            )
            raise Exception(message)
        else:
            result = camera_shape[0]

        return result

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def shot_published_audio(self, settings):
        """
        Check the audio nodes in scene to verify that they are published
        """

        try:
            aPlayBackSliderPython = mel.eval("$tmpVar=$gPlayBackSlider")
            audio_node = cmds.timeControl(aPlayBackSliderPython, query=True, sound=True)
            if not audio_node:
                raise Exception("Audio not found in timeline")
        # fallback in case we are running maya batch
        except Exception as e:
            self.parent.engine.logger.error(
                "Couldn't get audio from timeline. Error: {}".format(e)
            )
            self.parent.engine.logger.error(
                "Full traceback:\n{}".format(traceback.format_exc())
            )
            audio_node = cmds.ls(type="audio", long=True)
            if audio_node:
                if len(audio_node) == 1:
                    audio_node = audio_node[0]
                else:
                    audio_node = ""

        if not audio_node:
            message = (
                "Couldn't get sound name from timeline.\nPlease make sure you loaded "
                "the audio file usin SG Loader app and that the file exists locally in "
                "your hard drive (that the file has been downloaded properly).\n"
            )

            self.logger.error(message)
            self.parent.logger.error(message)
            raise Exception(message)

        sound_path = cmds.getAttr("{}.filename".format(audio_node))
        published_sound = sgtk.util.find_publish(self.sgtk, [sound_path])

        if not published_sound:
            message = "Sound file is not correctly published, please use a publishes audio, or not audio at all\n"
            message += "Audio path: %s" % sound_path

            self.logger.error(message)
            self.parent.logger.error(message)
            raise Exception(message)

        if not os.path.exists(sound_path):
            message = "Sound file doesn't exist in local drive. Please download it first and try again.\n"
            message += "Audio path: %s" % sound_path

            self.logger.error(message)
            self.parent.logger.error(message)
            raise Exception(message)

        return audio_node

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def shot_range(self, settings):
        """
        """

        entity = self.parent.context.entity
        sg_entity_type = entity["type"]
        sg_filters = [["id", "is", entity["id"]]]
        sg_in_field = settings["sg_in_frame_field"].value
        sg_out_field = settings["sg_out_frame_field"].value
        fields = [sg_in_field, sg_out_field]

        data = self.parent.shotgun.find_one(
            sg_entity_type, filters=sg_filters, fields=fields)

        self.logger.debug(data)

        if not data:
            message = "Cant find a valid shotgun entity for: %s" % entity
            self.logger.error(message)
            raise Exception(message)

        first_frame = data.get(sg_in_field)
        last_frame = data.get(sg_out_field)

        if not first_frame or not last_frame:
            message = "Cant find a valid frame range from: %s" % entity
            self.logger.error(message)
            raise Exception(message)

        return first_frame, last_frame

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def enable_maya_states(self):

        cmds.select(clear=True)
        thisPanel = cmds.getPanel(withFocus=1)
        panelType = cmds.getPanel(typeOf=thisPanel)

        """
        if not panelType == "modelPanel":
            error_msg = "You need to select one viewport panel."
            self.logger.error(error_msg)
            raise Exception(error_msg)
        """

        cmds.modelEditor(thisPanel, e=1, dtx=1)
        states = {}
        states['cameras'] = cmds.modelEditor(thisPanel, query=True, cameras=1)
        states['joints'] = cmds.modelEditor(thisPanel, query=True, joints=1)
        states['follicles'] = cmds.modelEditor(
            thisPanel, query=True, follicles=1)
        states['deformers'] = cmds.modelEditor(
            thisPanel, query=True, deformers=1)
        states['nurbsCurves'] = cmds.modelEditor(
            thisPanel, query=True, nurbsCurves=1)
        states['ikHandles'] = cmds.modelEditor(
            thisPanel, query=True, ikHandles=1)
        states['dynamics'] = cmds.modelEditor(
            thisPanel, query=True, dynamics=1)
        states['locators'] = cmds.modelEditor(
            thisPanel, query=True, locators=1)

        for typeOfObject in states:
            eval('cmds.modelEditor(thisPanel, e=1, ' + typeOfObject + '=0)')

        return states, thisPanel

    def convert_to_video(self, animation_exposition, fps, source_file,
                         dest_file, shot_name, first_frame, version,
                         sound_offset, sound_path, ffpeg_frwrk):

        """

        codec = 'libx264'
        options = ['-pix_fmt', 'yuv420p', '-preset', 'slow', '-crf', '17']

        convert_cmd = [
            '-r',
            '25',
            '-probesize', '16M',
            '-i',
            str(source_file),
            '-max_muxing_queue_size', '1024',
            '-vcodec', codec,
            '-acodec', 'aac',
        ]

        convert_cmd.extend(options)

        convert_cmd.extend(['-r', '25', '-y', str(dest_file)])

        """

        convert_cmd = self.build_ffmpeg_cmd.build_command(animation_exposition,
                                                          first_frame, fps,
                                                          sound_offset,
                                                          str(sound_path),
                                                          str(source_file),
                                                          str(dest_file))

        # self.parent.logger.info("convert_command: {}".format(convert_cmd))

        convert_cmd = " ".join(convert_cmd)
        self.parent.logger.info("convert_command str: {}".format(convert_cmd))

        #set binary to ensure ffmpeg is used
        ffpeg_frwrk.ffmpegCore.set_binary(binary="convert")

        _err, _info = ffpeg_frwrk.ffmpegCore.execute_command(convert_cmd)

        if _err:
            raise Exception(
                "Failed to convert playblast to video: %s" % (_info))

        # delete the playblast file, we only want to keep the transcoded one
        try:
            image_tmp_directory = os.path.dirname(source_file)

            files = os.listdir(image_tmp_directory)
            if files:
                for f in files:
                    file = os.path.join(image_tmp_directory, f)

                    # os.remove(file)

        except:
            pass

    # Returns de video dimension from ffmepg video metadata

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def getVideoDims(self, video_path):
        pattern = re.compile(r'Stream.*Video.* ([0-9]{3,})x([0-9]{3,})')

        p = subprocess.Popen(['ffmpeg', '-i', video_path],
                             stdout=subprocess.PIPE,
                             stderr=subprocess.PIPE)
        stdout, stderr = p.communicate()
        stdout = six.ensure_str(stdout)
        stderr = six.ensure_str(stderr)

        match = pattern.search(stderr)
        if match:
            x, y = map(int, match.groups())
        else:
            x = y = 0

        return x, y

    def set_smooth_geometry(self, smooth_level):

        geometry = cmds.ls(geometry=True)
        transforms = cmds.listRelatives(geometry, p=True, path=True)
        cmds.displaySmoothness(transforms, polygonObject=smooth_level)

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----
    def create_playblast(self, settings, item, publish_path, sg_fields):

        # the following lines are not needed as in the plugin initialization we are
        # already loading all three frameworks: coremayatools, mayacapture and ffmpeg
        # Loading FFMPEG framework
        # ffmpeg = self.load_framework("mty-framework-ffmpeg")
        # Loading maya capture framework
        # mayacapture = self.playblast_ops.mayaCapture_framework
        # mayacapture = self.load_framework("mty-framework-mayacapture")

        # General playblast data
        engine = sgtk.platform.current_engine()
        playblast_data = self.playblast_ops.get_playblast_settings(engine)

        start_frame = playblast_data["start_frame"]
        fps = playblast_data["frame_rate"]
        animation_exposition = playblast_data["exposition"]
        frames = playblast_data["frames"]
        format_ = playblast_data["format"]
        compression_ = playblast_data["compression"]
        images_path = playblast_data["tmp_path"]

        # Sound data
        sound_data = self.playblast_ops.get_sound_info(start_frame)
        sound_name = sound_data["name"]
        sound_path = sound_data["path"]
        sound_offset = sound_data["offset"]

        images_path = None

        shots_videos = []

        try:
            cmds.setAttr(item.properties['camera'] + '.overscan', 1)
        except:
            pass

        try:
            cmds.setAttr(item.properties['camera'] + '.depthOfField', 0)
        except:
            pass

        playblast_folder = os.path.dirname(publish_path)
        if not os.path.exists(playblast_folder):
            os.makedirs(playblast_folder)

        playblast_filename, _ = os.path.splitext(publish_path)

        # Create TMP File
        import tempfile
        tmp_root = tempfile.gettempdir()

        shot_name = engine.context.entity["name"]
        version = 'v{0}'.format(str(sg_fields['version']).zfill(3))

        playblast_file_name = os.path.basename(playblast_filename)
        tmp_dir = os.path.join(tmp_root, "playblast_images", shot_name, version)
        playblast_tmp_path = os.path.join(tmp_dir, playblast_file_name)

        self.parent.logger.info("Playblast Tmp Dir: {}".format(tmp_dir))

        if not os.access(tmp_dir, os.F_OK):
            os.makedirs(tmp_dir, 0o777)
        else:
            import shutil
            shutil.rmtree(tmp_dir)
            os.makedirs(tmp_dir, 0o777)

        # set the audio properly
        audio = item.properties['audio']
        if audio:
            sound_name = audio
        else:
            sound_name = None

        # Launch capture with custom viewport settings
        view_opts = self.mayacapture.ViewportOptions.copy()
        view_opts['grid'] = False
        view_opts['polymeshes'] = True
        view_opts['displayAppearance'] = "smoothShaded"
        view_opts['nurbsCurves'] = False
        view_opts['locators'] = False
        view_opts['joints'] = False
        view_opts['pluginShapes'] = True
        view_opts['pluginObjects'] = ("gpuCacheDisplayFilter", True)
        view_opts['ikHandles'] = False
        view_opts['displayTextures'] = True
        view_opts['textures'] = True
        # Enable viewport2 AmbientOclusion
        view2_opts = self.mayacapture.Viewport2Options.copy()
        view2_opts['ssaoEnable'] = True
        # camera
        cam_opts = self.mayacapture.CameraOptions.copy()
        cam_opts['displayResolution'] = True

        # Set the smoothView to level 3
        # self.set_smooth_geometry(3)

        self.mayacapture.capture(
            camera=item.properties["camera"],
            width=settings['width'].value,
            height=settings['height'].value,
            filename=playblast_tmp_path,
            frames=frames,
            format=format_,
            compression=compression_,
            # format='avi',
            # compression='none',
            viewer=False,
            overwrite=True,
            viewport_options=view_opts,
            viewport2_options=view2_opts,
            camera_options=cam_opts,
            sound=sound_name
        )

        # playblast_filename += '.avi'

        directory = os.path.dirname(playblast_tmp_path)

        self.playblast_ops.rename_image_frames(
            animation_exposition, playblast_tmp_path, frames
        )

        playblast_tmp_path = playblast_tmp_path.replace(".{0}".format(version), "")

        self.convert_to_video(
            animation_exposition,
            fps,
            playblast_tmp_path,
            publish_path,
            item.properties['shot_name'],
            item.properties['start_frame'],
            version,
            sound_offset,
            sound_path,
            self.ffmpeg
        )

        # restore smooth level to 1
        # self.set_smooth_geometry(1)

        return publish_path

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def submit_version(self, path_to_frames, path_to_movie, sg_publishes,
                       sg_task, comment, store_on_disk, first_frame, last_frame,
                       work_status, override_entity=False):
        """
        Create a version in Shotgun for this path and linked to this publish.
        """

        # get current shotgun user
        current_user = self.parent.engine.context.user

        # create a name for the version based on the file name
        # grab the file name, strip off extension
        name = os.path.splitext(os.path.basename(path_to_movie))[0]
        # do some replacements
        name = name.replace("_", " ")
        # and capitalize
        name = name.capitalize()

        print
        '--------------', path_to_frames
        LinkFolder = {'local_path': os.path.dirname(path_to_frames) + os.sep,
                      'content_type': None,
                      'link_type': 'local',
                      'name': name}

        LinkFile = {'local_path': path_to_movie,
                    'content_type': None,
                    'link_type': 'local',
                    'name': name}

        if override_entity != False:
            entity = override_entity
        else:
            entity = self.parent.engine.context.entity

        # Create the version in Shotgun
        data = {
            "code": name,
            "sg_status_list": work_status,
            "entity": entity,
            "sg_task": sg_task,
            "sg_version_type": "Production",
            "sg_first_frame": first_frame,
            "sg_last_frame": last_frame,
            "frame_count": (last_frame - first_frame + 1),
            "frame_range": "%s-%s" % (first_frame, last_frame),
            "sg_frames_have_slate": False,
            "published_files": sg_publishes,
            "created_by": current_user,
            "description": comment,
            "sg_path_to_frames": path_to_frames,
            "sg_movie_has_slate": True,
            "project": self.parent.engine.context.project,
            "user": current_user
        }

        if store_on_disk:
            data["sg_path_to_movie"] = path_to_movie

        sg_version = self.parent.engine.shotgun.create("Version", data)
        self.parent.log_debug("Created version in shotgun: %s" % str(data))

        return sg_version

    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        pass


class UploaderThread(QtCore.QThread):
    """
    Simple worker thread that encapsulates uploading to shotgun.
    Broken out of the main loop so that the UI can remain responsive
    even though an upload is happening
    """

    def __init__(self, app, version, path_to_movie, thumbnail_path,
                 upload_to_shotgun):
        QtCore.QThread.__init__(self)
        self._app = app
        self._version = version
        self._path_to_movie = path_to_movie
        self._thumbnail_path = thumbnail_path
        self._upload_to_shotgun = upload_to_shotgun
        self._errors = []

    def get_errors(self):
        """
        can be called after execution to retrieve a list of errors
        """
        return self._errors

    def run(self):
        """
        Thread loop
        """
        upload_error = False

        if self._upload_to_shotgun:
            try:
                self._app.tank.shotgun.upload(
                    "Version", self._version["id"], self._path_to_movie,
                    "sg_uploaded_movie")
            except:
                self._errors.append(
                    "Movie upload to Shotgun failed: {}".format(traceback.print_exc())
                )
                upload_error = True

        if not self._upload_to_shotgun or upload_error:
            try:
                self._app.tank.shotgun.upload_thumbnail(
                    "Version", self._version["id"], self._thumbnail_path)
            except:
                self._errors.append(
                    "Thumbnail upload to Shotgun failed: {}".format(traceback.print_exc())
                )
