#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

"""
An app that creates a "WRITE" node for each "COMPOSITE" (passthrough) node found
in the scene. Passthrough nodes are created using a different execute action, which
sets custom color on the nodes, so we can collect them in this hook.
The name of each found passthrough node will be used for the name
of each output sequence and write node created:

     ____________________
    |                    |
    |  OutputRenderName  |  Composite node named after the desired output sequence name
    | (passhtrough node) |
    |____________________|
              |
              |
     _________|__________
    |                    |
    |  OutputRenderName  |  Write node (created by a different hook) that will take the
    | (passhtrough node) |  name of the parent passthrough node for the sequence name
    |____________________|  and path:
                            root_scene_folder/frame/{OutputRenderName}/{OutputRenderName}

If a passthrough node already has a write node connected, the hook won't create a new
write node, but will modify the attributes on the existing node.

It creates a boolean attribute in each write node called 'mtyWriteExport' set to true. This
allows us to collect specific write nodes for the publisher. This attribute is not exposed
in the interface so it can be modified only via scripting.

"""

from tank import Hook


class ProcessItemsHook(Hook):
    def execute(self, **kwargs):

        self.parent.engine.logger.info(
            "execute action create_write_nodes start. {}".format("-" * 80)
        )

        result = self.create_write_nodes()

        if not result:
            result = {"succes": [1], "messages": [], "errors": []}

        self.parent.engine.logger.info(
            "execute action create_write_nodes end. {}".format("-" * 80)
        )

        return result

    def create_write_nodes(self):

        result = {"succes": [1], "messages": [], "errors": []}

        # get settings previously set in tk-harmony.yml
        passthrough_node_color = self.parent.get_setting("passthrough_node_color")
        write_node_color = self.parent.get_setting("write_node_color")
        color_depth = self.parent.get_setting("color_depth")
        write_attr_name = self.parent.get_setting("write_attr_name")
        write_attr_type = self.parent.get_setting("write_attr_type")
        passthrough_attr_name = self.parent.get_setting("passthrough_attr_name")
        passthrough_attr_type = self.parent.get_setting("passthrough_attr_type")

        # convert values to strings in the right format
        passthrough_node_color_str = str(passthrough_node_color)
        write_node_color_str = str(write_node_color)[1:-1]
        color_depth_str = str(color_depth)

        create_write_nodes_cmd = """

function createNode(nodeType, prevNode, color, nodeName, xOffset, yOffset)
{
    var prevNodeParent = node.parentNode(prevNode);

    if (nodeType == "WRITE")
    {
        var prevNodeXpos = node.coordX(prevNode);
        var prevNodeYpos = node.coordY(prevNode);
        var prevNodeZpos = node.coordZ(prevNode);
        var newNode = node.add(
            prevNodeParent,
            nodeName,
            nodeType,
            prevNodeXpos - xOffset,
            prevNodeYpos + yOffset,
            prevNodeZpos
        );
    } else if (nodeType == "COMPOSITE")
    {
        var newNode = node.add(
            prevNodeParent,
            nodeName,
            nodeType,
            -xOffset,
            yOffset,
            prevNodeZpos
        );
    };

    node.link(prevNode, 0, newNode, 0);
    node.setColor(newNode, color);
    MessageLog.trace("Created node: " + node.getName(newNode));

    return newNode;
};


function setWriteNodeAttrs(writeNode, name, bit_depth)
{
    var startFrame = scene.getStartFrame();

    var attrs = node.getAttrList(writeNode, startFrame);

    // get bit depth from settings and set the drawing_type variable accordingly
    // if the variable is different than 8, 16 or 32, the fallback to PNG4 (8 bits)
    if (bit_depth == 8)
    {
        var drawing_type = "PNG4";
    } else if (bit_depth == 16)
    {
        var drawing_type = "PNGDP4";
    } else if (bit_depth == 32)
    {
        var drawing_type = "EXR";
    } else
    {
        var drawing_type = "PNG4";
    };

    drawingNameAttr = node.getAttr(writeNode, startFrame, "DRAWING_NAME");
    drawingTypeAttr = node.getAttr(writeNode, startFrame, "DRAWING_TYPE");
    if (name == "final")
    {
        drawingNameAttr.setValue("frames/" + name + ".");
        drawingTypeAttr.setValue("PNG");
    } else
    {
        drawingNameAttr.setValue("frames/" + name + "/" + name + ".");
        drawingTypeAttr.setValue(drawing_type);
    };
};


function create_attr(nodeName, attrName, attrType)
{
    var attr = node.getAttr(nodeName, 1.0, attrName);
    if (attr.keyword() == "")
    {
        var visualAttrName = attrName;
        if (node.createDynamicAttr(nodeName, attrType, attrName, visualAttrName, false))
        {
            attr = node.getAttr(nodeName, 1.0, attrName);
        };
        if (attr.keyword() != "")
        {
            node.setTextAttr(nodeName, attrName, 1.0, "true");
        };
    };
    return attr
};


function createWriteNodes(passthrough_nodes)
{
    var writeNodesArray = [];
    if(passthrough_nodes.length > 0)
    {
        for(var n = 0; n < passthrough_nodes.length; n++)
        {
            var parentNode = passthrough_nodes[n];
            var parentNodeName = node.getName(parentNode);
            var destWriteNode = node.dstNode(parentNode, 0, 0);

            var writeNodeColor = new ColorRGBA(%s);
            var nodeName = "Write_" + parentNodeName;

            var writeNode = destWriteNode;
            if (writeNode.length != 0)
            {
                if (node.getName(writeNode) != nodeName)
                {
                    node.rename(writeNode, nodeName);
                    var writeNode = node.dstNode(parentNode, 0, 0);
                };
            };
            if (writeNode.length == 0)
            {
                var writeNode = createNode(
                    "WRITE",
                    parentNode,
                    writeNodeColor,
                    nodeName,
                    0,
                    100
                );
            };

            writeNodesArray.push(writeNode);
            // set node attributes
            setWriteNodeAttrs(writeNode, parentNodeName, %s);
            // create node metadata (attribute)
            create_attr(writeNode, "%s", "%s")
        };
    } else
    {
        MessageBox.information(
            "Couldn't find any passthrough node. Please create at least one passthrough node and try again.");
    };
    return writeNodesArray;
};


function collect_passthrough_nodes()
{
    // Get the passthrough nodes by color
    var array_of_node_types = ["COMPOSITE"];
    var array_of_nodes = node.getNodes(array_of_node_types);
    var predefined_passthrough_color = %s;

    var array_of_passthough_nodes = [];
    for (var i = 0; i < array_of_nodes.length; ++i)
    {
        var node_path = array_of_nodes[i];
        MessageLog.trace("checking node: " + node_path)
        var attr = node.getAttr(node_path, 1.0, "%s");
        if (attr != null)
        {
            if (attr.keyword() != "" && attr.typeName() == "%s")
            {
                if (attr.boolValue() == true)
                {
                    MessageLog.trace("Found passthrough node: " + node_path);
                    array_of_passthough_nodes.push(node_path);
                };
            };
        };

        /*
        var node_name = node.getName(node_path);
        var node_color = [
            node.getColor(node_path).r,
            node.getColor(node_path).g,
            node.getColor(node_path).b,
            node.getColor(node_path).a
        ];

        if (
            node_color[0] == predefined_passthrough_color[0] &&
            node_color[1] == predefined_passthrough_color[1] &&
            node_color[2] == predefined_passthrough_color[2] &&
            node_color[3] == predefined_passthrough_color[3]
        {
            MessageLog.trace(node_path)
            array_of_passthough_nodes.push(node_path);
        };
        )*/
    };
    return array_of_passthough_nodes;
};


var passthrough_nodes = collect_passthrough_nodes();
var writeNodesArray = createWriteNodes(passthrough_nodes);

MessageBox.information(writeNodesArray.length + " write nodes were created or modified");
""" % (
    write_node_color_str,
    color_depth_str,
    write_attr_name,
    write_attr_type,
    passthrough_node_color_str,
    passthrough_attr_name,
    passthrough_attr_type
)

        self.parent.engine.logger.debug(create_write_nodes_cmd)
        self.parent.engine.app.execute(create_write_nodes_cmd)

        return result
