#####################################################################################
#
# Copyright (c) 2021 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

import os
import time
import pprint

import sgtk
from sgtk.util.filesystem import ensure_folder_exists

HookBaseClass = sgtk.get_hook_baseclass()

pf = pprint.pformat

class HarmonyShotCameraPublishPlugin(HookBaseClass):
    """
    Plugin for publishing an open Harmony session.
    This hook relies on functionality found in the base file publisher hook in
    the publish2 app and should inherit from it in the configuration. The hook
    setting for this plugin should look something like this::
        hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py"
    """

    # NOTE: The plugin icon and name are defined by the base file plugin.
    @property
    def icon(self):
        """
        Path to an png icon on disk
        """
        # look for icon one level up from this hook's folder in "icons" folder
        return os.path.join(
            self.disk_location,
            os.pardir,
            "icons",
            "camera_publish.png"
        )

    @property
    def description(self):
        """
        Verbose, multi-line description of what the plugin does. This can
        contain simple html for formatting.
        """

        loader_url = "https://support.shotgunsoftware.com/hc/en-us/articles/219033078"

        return """
        Publishes the file to Shotgun. A <b>Publish</b> entry will be
        created in Shotgun which will include a reference to the file's current
        path on disk. If a publish template is configured, a copy of the
        current session will be copied to the publish template path which
        will be the file that is published. Other users will be able to access
        the published file via the <b><a href='%s'>Loader</a></b> so long as
        they have access to the file's location on disk.
        If the session has not been saved, validation will fail and a button
        will be provided in the logging output to save the file.
        <h3>File versioning</h3>
        If the filename contains a version number, the process will bump the
        file to the next version after publishing.
        The <code>version</code> field of the resulting <b>Publish</b> in
        Shotgun will also reflect the version number identified in the filename
        The basic worklfow recognizes the following version formats by default:
        <ul>
        <li><code>filename.v###.ext</code></li>
        <li><code>filename_v###.ext</code></li>
        <li><code>filename-v###.ext</code></li>
        </ul>
        After publishing, if a version number is detected in the work file, the
        work file will automatically be saved to the next incremental version
        number. For example, <code>filename.v001.ext</code> will be published
        and copied to <code>filename.v002.ext</code>
        If the next incremental version of the file already exists on disk, the
        validation step will produce a warning, and a button will be provided
        in the logging output which will allow saving the session to the next
        available version number prior to publishing.
        <br><br><i>NOTE: any amount of version number padding is supported. for
        non-template based workflows.</i>
        <h3>Overwriting an existing publish</h3>
        In non-template workflows, a file can be published multiple times,
        however only the most recent publish will be available to other users.
        Warnings will be provided during validation if there are previous
        publishes.
        """ % (
            loader_url,
        )

    @property
    def item_filters(self):
        """
        List of item types that this plugin is interested in.
        Only items matching entries in this list will be presented to the
        accept() method. Strings can contain glob patters such as *, for
        example ["harmony.*", "file.harmony"]
        """
        return ["harmony.camera"]

    @property
    def settings(self):
        """
        Dictionary defining the settings that this plugin expects to receive
        through the settings parameter in the accept, validate, publish and
        finalize methods.
        A dictionary on the following form::
            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }
        The type string should be one of the data types that toolkit accepts as
        part of its environment configuration.
        """

        # inherit the settings from the base publish plugin
        base_settings = super(HarmonyShotCameraPublishPlugin, self).settings or {}

        # settings specific to this class
        shot_camera_publish_settings = {
            "camera_attr_name": {
                "type": "string",
                "default": None,
                "description": "Camera attribute name",
            },
            "camera_attr_type": {
                "type": "string",
                "default": None,
                "description": "Camera attribute type",
            },
            # "Allowed Tasks": {
            #     "type": "list",
            #     "default": None,
            #     "description": "List of allowed tasks",
            # },
        }

        # update the base settings
        base_settings.update(shot_camera_publish_settings)

        return base_settings

    def accept(self, settings, item):
        result = {"accepted": True, "checked": True}

        context = self.parent.context
        task = context.task

        # load overrides framework -----------------------------------------------------
        overrides_framework = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        )
        if not overrides_framework:
            overrides_framework = self.load_framework("mty-framework-valueoverrides")
        # ------------------------------------------------------------------------------

        # get validated_camera property from item to check if there's actually a camera
        # in the scene, regardless of if it's going to be exported in the current task
        validated_camera = item.properties["validated_camera"]
        found_cameras = item.properties["found_cameras"]

        default_value_cam_validation = "mty.publisher.harmony.validate_pipeline_camera"
        override_value_cam_validation = overrides_framework.get_value(
            default_value_cam_validation, link=task
        )

        # if the value of the camera validation override or default value is set to
        # False, then we completely skip the camera validation. Usually this should be
        # set at project level
        if not override_value_cam_validation:
            return  {
                "accepted": False,
                "checked": False,
                "visible": False,
                "enabled": False
            }

        if not validated_camera:
            # no camera was found or more than one camera was found:
            if not found_cameras or len(found_cameras) > 1:
                return  {
                    "accepted": True,
                    "checked": True,
                    "visible": False,
                    "enabled": False
                }


        # get allowed tasks from value overrides framework
        allowed_tasks = []
        default_value = "mty.engine.harmony.export_camera_tasks"
        # link = {"type": "Task", "id": context.task["id"]}
        link = {"type": "Project", "id": context.project["id"]}
        allowed_tasks = overrides_framework.get_value(
            default_value, link=link
        )
        self.parent.logger.debug(
            "export_camera_tasks: {}, type: {}".format(
                allowed_tasks, type(allowed_tasks)
            )
        )

        if allowed_tasks:
            # Convert string to list
            allowed_tasks = eval(allowed_tasks)

        self.parent.engine.logger.info("export_camera_tasks: {}".format(allowed_tasks))


        if context.task["name"] not in allowed_tasks:
            result = {"accepted": False, "checked": False}

        return result


    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish. Returns a
        boolean to indicate validity.
        :param settings: Dictionary of Settings. The keys are strings, matching
                         the keys returned in the settings property. The values
                         are `Setting` instances.
        :param item: Item to process
        :returns: True if item is valid, False otherwise.
        """

        return item.properties["validated_camera"]

        context = self.parent.context

        # Vaildate camera
        self.logger.info("Validating shot camera...")
        self.parent.engine.logger.info("Validating shot camera...".ljust(88, "-"))
        shot_camera_nodes_list = self._get_shot_camera_node(settings)
        self.parent.engine.logger.info(
            "Current shot_camera_nodes_list:\n{}".format(pf(shot_camera_nodes_list))
        )

        # Sometimes the cameras list is not correctly retured. The following block uses
        # various retries to ensure the cameras list can be correctly retrieved
        if not shot_camera_nodes_list:
            counter = 0
            while counter < 5 and not shot_camera_nodes_list:
                time.sleep(1)
                shot_camera_nodes_list = self._get_shot_camera_node(settings)
                counter += 1
                self.parent.engine.logger.info("------------")
                self.parent.engine.logger.info(
                    "get_shot_camera_node, try: {}".format(counter + 1)
                )
                self.parent.engine.logger.info(
                    "Current shot_camera_nodes_list:\n{}".format(
                        pf(shot_camera_nodes_list)
                    )
                )

        if not shot_camera_nodes_list:
            error_msg = "Couldn't find a shot camera in the scene."
            self.logger.error(
                error_msg,
                extra={
                    "action_show_more_info": {
                        "label": "How to fix it",
                        "tooltip": "Error info.",
                        "text": "<pre>%s</pre>" % (
                            "Create a new shot camera using the corresponding execute "
                            "action from the SG menu:\n\nSG > SG Execute Actions > "
                            "Shot Assembly > 1 Create shot camera node...\n\n"
                            "This camera will be exported to the next tasks, so this "
                            "must be your animated\ncamera. If you already have an "
                            "animated camera, you could simply reconnect the\n(perhaps "
                            "already animated) peg to this new camera.\n\nRemember you "
                            "should NEVER animate the camera itsef, but its parent peg(s).\n\n"
                            "Also, please make sure you switch to the new camera (Scene > "
                            "Camera > your_new_camera)\nand that everyting looks correct "
                            "in your Camera View. If you need to adjust the camera, \n"
                            "use the new camera peg to position it (or animate it)"
                        )
                    }
                }
            )
            raise Exception(error_msg)
        elif len(shot_camera_nodes_list) != 1:
            error_msg = "Found more than one shot camera in the scene."
            self.logger.error(
                error_msg,
                extra={
                    "action_show_more_info": {
                        "label": "How to fix it",
                        "tooltip": "Error info.",
                        "text": "<pre>%s</pre>" % (
                            "You must have just one shot camera in your scene.\n\n"
                            "More than one camera was found in your scene: \n\n{}"
                        ).format(shot_camera_nodes_list)
                    }
                }
            )
            raise Exception(error_msg)

        return True

    def publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.
        :param settings: Dictionary of Settings. The keys are strings, matching
                         the keys returned in the settings property.
                         The values are `Setting` instances.
        :param item: Item to process
        """
        self.logger.info("Publishing Shot Camera.")
        self.parent.logger.info("Publishing Shot Camera.")

        # get the path in a normalized state. no trailing separator, separators
        # are appropriate for current os, no double separators, etc.
        engine    = self.parent.engine

        # Get item properties
        work_fields = item.properties["work_fields"]
        publish_session_template = item.properties["template_session_publish"]
        primary_publish_path = publish_session_template.apply_fields(work_fields)
        publish_path = item.properties["publish_path"]
        publish_folder = os.path.dirname(publish_path)

        # Ensure folder exists
        if not os.path.exists(publish_folder):
            engine.ensure_folder_exists(publish_folder)

        # Export the file
        file_correctly_exported = self._export_camera_json_file(
            settings, publish_path, item
        )
        self.parent.logger.info(
            "file_correctly_exported: {}".format(file_correctly_exported)
        )

        if file_correctly_exported:
            publish_name = self.parent.util.get_publish_name(publish_path)

            self.parent.logger.info("Shot camera publish name: {}".format(publish_name))

            publish_data = {
                "tk":                   self.parent.sgtk,
                "context":              item.context,
                "comment":              item.description,
                "path":                 publish_path,
                "name":                 publish_name,
                "version_number":       work_fields['version'],
                "thumbnail_path":       item.get_thumbnail_as_path(),
                "published_file_type":  "Harmony Shot Camera",
                "dependency_paths":     [primary_publish_path],
                "dependency_ids":       [],
                "sg_fields":            {"sg_status_list": "rev"},
            }

            # Register the publish of the original PNG, so that we can have the pointer
            sg_publishes = sgtk.util.register_publish(**publish_data)

            item.properties.sg_publish_data = sg_publishes
            item.properties.sg_publish_path = sg_publishes["path"]["local_path"]

            # finally just store the publish data for later retrieval
            # and upload to the host storage location
            root_item = self.get_root_item(item)
            root_item.properties.setdefault('sg_publish_extra_data', [])

            self.parent.logger.debug(
                'Storing extra publish data on root item: {}'.format(root_item)
            )
            publish_extra_data = root_item.properties['sg_publish_extra_data']
            root_item.properties['sg_publish_extra_data'].append(sg_publishes)
            self.parent.logger.debug(
                "Already {} elements in extra publish data".format(
                    len(publish_extra_data)
                )
            )

            # Update task status for revision
            sg_task = self.parent.context.task
            self.parent.logger.info(
                "End of Publish and updating task with id: {} to status: 'rev'".format(
                    str(sg_task['id'])
                )
            )

            try:
                self.parent.engine.shotgun.update(
                    "Task", sg_task["id"], {"sg_status_list": 'rev'}
                )
            except:
                pass

    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed, and can for example be used to version up files.
        :param settings: Dictionary of Settings. The keys are strings, matching
                         the keys returned in the settings property.
                         The values are `Setting` instances.
        :param item: Item to process
        """

        # do the base class finalization
        #super(HarmonySessionPublishPlugin, self).finalize(settings, item)
        self.logger.debug('Item camera successfully published')
        # bump the session file to the next version
        #self._save_to_next_version(item.properties["path"], item, _save_session)

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item
        return root

    def _get_shot_camera_node(self, settings):

        camera_attr_name = settings.get("camera_attr_name").value
        camera_attr_type = settings.get("camera_attr_type").value

        context = self.parent.engine.context

        if context.entity["type"] == "Shot":
            shot_name = context.entity["name"]
            camera_node_name = "{}-Camera".format(shot_name)
        else:
            camera_node_name = None

        self.parent.engine.logger.debug("context:\n{}".format(pprint.pformat(context)))
        self.parent.logger.info("camera_attr_name: {}".format(camera_attr_name))
        self.parent.logger.info("camera_attr_type: {}".format(camera_attr_type))
        self.parent.logger.info("camera_node_name: {}".format(camera_node_name))

        collect_shot_camera_node_list_cmd = """

function collect_shot_camera_node(nodeName)
{
    // Get the camera nodes by attr
    var array_of_node_types = ["CAMERA"];
    var array_of_nodes = node.getNodes(array_of_node_types);

    var array_of_shot_camera_nodes = [];
    for (var i = 0; i < array_of_nodes.length; ++i)
    {
        var node_path = array_of_nodes[i];
        MessageLog.trace("checking node: " + node_path);
        // var attr = node.getAttr(node_path, 1.0, "mtyShotCamera");
        var attr = node.getAttr(node_path, 1.0, "%s");
        // MessageLog.trace(attr.name());
        // MessageLog.trace(attr.typeName());
        // MessageLog.trace(attr.boolValue());
        if (attr != null)
        {
            // if (attr.keyword() != "" && attr.typeName() == "BOOL")
            if (attr.keyword() != "" && attr.typeName() == "%s")
            {
                if (attr.boolValue() == true)
                {
                    if (node.getName(node_path) == nodeName)
                    {
                        MessageLog.trace("Found shot camera node: " + node_path);
                        array_of_shot_camera_nodes.push(node_path);
                    };
                };
            };
        };
    };
    return array_of_shot_camera_nodes;
};

collect_shot_camera_node("%s");
// var shot_camera_nodes = collect_shot_camera_node("s");
// MessageLog.trace(JSON.stringify(shot_camera_nodes));
""" % (
    camera_attr_name,
    camera_attr_type,
    camera_node_name,
)

        self.parent.engine.logger.debug(
            "collect_shot_camera_node_list_cmd:\n{}".format(
                collect_shot_camera_node_list_cmd
            )
        )
        shot_camera_nodes_list = self.parent.engine.app.execute(
            collect_shot_camera_node_list_cmd
        )
        self.parent.engine.logger.info(
            "shot_camera_nodes_list: {}".format(shot_camera_nodes_list)
        )

        return shot_camera_nodes_list


    def _export_camera_json_file(self, settings, publish_path, item):
        """
        Export the scene camera using a global matrix. Saves a jsonx file in the Publish Area
        """
        engine = self.parent.engine

        camera_node_path = item.properties.get("found_cameras", None)

        if not camera_node_path:
            # retry for camera collector. Sometimes when the scene is too heavy, this
            # method will return None. This loop tries to ensure it returns something
            counter = 0
            shot_cameras_list = None
            while not shot_cameras_list and counter < 3:
                shot_cameras_list = self._get_shot_camera_node(settings)
                if not shot_cameras_list:
                    engine.logger.info(
                        "Couldn't get shot_cameras_list, try {}".format(counter + 1)
                    )
                counter += 1
                time.sleep(3)


            if shot_cameras_list:
                camera_node_path = shot_cameras_list[0]

        if isinstance(camera_node_path, list):
            camera_node_path = camera_node_path[0]

        engine.logger.info("camera_node_path: {}".format(camera_node_path))

        # publish_path = publish_path.replace(os.path.sep, "/")
        publish_path = self.fix_path(publish_path)
        engine.logger.info("publish_path after fix_path: {}".format(publish_path))

        export_shot_camera_cmd = """

// Export current camera.
// Harmony version.

function TB_ExportCamera()
{
  this.trace = function(m)
  {
     MessageLog.trace(m);
  }
  this.createExportCameraObject = function(nodePath)
  {
     var obj = {
        type : "CAMERA",
        name : node.getName(nodePath),
        position : [],
        scale : [],
        rotation : [],
        is3D : false
     };

     trace("exporting " + frame.numberOf() + " frames");
     var previousPosition;
     var previousScale;
     var previousRotation;
     var holdingPosition = false;
     var holdingScale = false;
     var holdingRotation = false;

     for(var i=0 ; i< frame.numberOf() ; ++i)
     {
        var frameNumber = 1+i;
        var matrix = scene.getCameraMatrix(frameNumber);

        var position = matrix.extractPosition();
        var scale = matrix.extractScale();
        var rotation = matrix.extractRotation();

        if (!i || position.isNotEqual(previousPosition))
        {
            if (holdingPosition)
            {
               obj.position.push([i-1, previousPosition.x, previousPosition.y, previousPosition.z]);
            }
             obj.position.push([i, position.x, position.y, position.z]);
            holdingPosition = false;
        }
        else
        {
            holdingPosition = true;
        }

        if (!i || scale.isNotEqual(previousScale))
        {
            if (holdingScale)
            {
               obj.scale.push([i-1, previousScale.x, previousScale.y, previousScale.z]);
            }
             obj.scale.push([i, scale.x, scale.y, scale.z]);
            holdingScale = false;
        }
        else
        {
            holdingScale = true;
        }

        if (!i || rotation.isNotEqual(previousRotation))
        {
            if (holdingRotation)
            {
               obj.rotation.push([i-1, previousRotation.x, previousRotation.y, previousRotation.z]);
            }
             obj.rotation.push([i, rotation.x, rotation.y, rotation.z]);
            holdingRotation = false;
            if (rotation.x < -0.00001 || rotation.x > 0.00001 || rotation.y < -0.00001 || rotation.y > 0.00001)
            {
               obj.is3D = true;
            }
        }
        else
        {
            holdingRotation = true;
        }

        previousPosition = position;
        previousScale = scale;
        previousRotation = rotation;
     }

    return obj;
  }
  this.createSettingsObject = function()
  {
    return { type: "Settings",
            frameRate : scene.getFrameRate(),
            unitsAspectRatioX : scene.unitsAspectRatioX(),
            unitsAspectRatioY : scene.unitsAspectRatioY(),
            unitsZ : scene.numberOfUnitsZ(),
            resolutionX : scene.currentResolutionX(),
            resolutionY: scene.currentResolutionY(),
            defaultResolutionFOV : scene.defaultResolutionFOV()
      };
  }

  var defaultCamera = "%s";

  trace("Exporting Camera: " + defaultCamera);

  var filename = "%s";
  if (!filename)
  {
     trace("No filename specified. Aborting.");
     return;
  }
  var file = new File( filename );
   trace("Exporting in file: " + filename);
   file.open(FileAccess.WriteOnly);
   var data=[createSettingsObject(), createExportCameraObject(defaultCamera)];
   file.write(JSON.stringify(data, null, 2));
   file.close();
}

TB_ExportCamera();

""" % (
    camera_node_path,
    publish_path
)
        self.parent.engine.logger.debug(
            "export_shot_camera_cmd:\n{}".format(
                export_shot_camera_cmd
            )
        )
        self.parent.engine.app.execute(export_shot_camera_cmd)

        # Check if the file was created (if it exists on disk)
        file_exists = os.path.exists(publish_path)

        return file_exists

    def fix_path(self, path):
        path = path.replace('\\', '/')
        path = path.replace('\\\\', '/')

        return path
