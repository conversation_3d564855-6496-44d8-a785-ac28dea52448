#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

"""
An app that loads a template specific to the project and sets all relevant
project settings found in SG:
    shot settings:
        - shot sg_cut_in (first shot frame)
        - shot sg_cut_out (last shot frame)
    project settings:
        - frame rate
        - resolution
        - working color space

"""

from tank import Hook
from tank import TankError
from tank.platform import Application
from tank.platform.qt import QtCore, QtGui
import tank

import os
import re
import pprint


class ProcessItemsHook(Hook):
    def execute(self, **kwargs):
        self.parent.engine.logger.info(
            "execute action get_job_info start. {}".format("-" * 80)
        )

        result = self.get_job_info()

        self.parent.engine.logger.info(
            "execute action get_job_info end. {}".format("-" * 80)
        )

        return result

    def get_job_info(self):
        adobe = self.parent.engine.adobe

        result = {"succes": [1], "messages": [], "errors": []}

        # shot_data = self.get_shot_data_from_SG()
        shot_data = {}
        # self.parent.engine.logger.info(
        #     "shot_data:\n{}".format(pprint.pformat(shot_data))
        # )

        compositions = self.collect_all_compositions_in_project()
        self.parent.engine.logger.info(
            "found {} compositions".format(len(compositions))
        )

        # set shot settings
        if not compositions:
            return result

        # filter output compositions
        list_of_output_comps = self.filter_output_compositions(compositions)
        self.parent.engine.logger.info(
            "found {} output compositions".format(len(list_of_output_comps))
        )

        if not list_of_output_comps:
            msg = (
                "Couldn't find any AFX Output Comp.\n(TIP: your output comp might be "
                "missing the 'AFX Output Comp' comment.)"
            )
            self.show_message(msg, icon="Question")

            return result

        # get the current project path
        fsName = adobe.app.project.file.fsName
        # fullName = adobe.app.project.file.fullName
        # path = adobe.app.project.file.path
        # absoluteURI = adobe.app.project.file.absoluteURI
        # relativeURI = adobe.app.project.file.relativeURI

        self.parent.engine.logger.info("fsName: {}".format(fsName))
        # self.parent.engine.logger.info("fullName: {}".format(fullName))
        # self.parent.engine.logger.info("path: {}".format(path))
        # self.parent.engine.logger.info("absoluteURI: {}".format(absoluteURI))
        # self.parent.engine.logger.info("relativeURI: {}".format(relativeURI))

        current_project_path = "Current project path:\n{}\n".format(fsName)

        out_comp_info_list = [current_project_path]
        # iterate through output comps and get their relevant values
        for comp in list_of_output_comps:
            comp_data = self.get_composition_info(comp)
            self.parent.engine.logger.info(
                "comp {} data:\n{}".format(comp.name, pprint.pformat(comp_data))
            )
            # out_comp_info_list.append("Output comps:")
            out_comp_info_list.append("{}".format(comp_data["name"]))

            out_comp_info_list.append(
                "\tstart frame: {}".format(comp_data["start_frame"])
            )
            out_comp_info_list.append(
                "\tend frame:  {}\n".format(comp_data["end_frame"])
            )

        # # append shot cut in and cut out from SG
        # out_comp_info_list.append("\nShot data from SG:")
        # out_comp_info_list.append("\tstart frame: {}".format(shot_data["sg_cut_in"]))
        # out_comp_info_list.append("\tend frame: {}\n".format(shot_data["sg_cut_out"]))

        msg = "\n".join(out_comp_info_list)
        self.show_message(msg, icon="Information")
        # alert_box = adobe.alert(msg)

        return result

    def collect_all_compositions_in_project(self):
        compositions = []

        adobe = self.parent.engine.adobe

        project_items = adobe.app.project.items

        for i, item in enumerate(self.parent.engine.iter_collection(project_items)):
            if item.typeName == "Composition":
                if item not in compositions:
                    compositions.append(item)

        return compositions

    def filter_output_compositions(self, list_of_comps):
        """
        Filters a list of comps to get only comps which name start with 'Output'
        It is NOT case sensitive so either 'Output' or 'output' should work.
        """

        output_comps = []

        for comp in list_of_comps:
            original_comp_name = comp.name
            match_pattern = re.compile(r"(?P<output_tag>^[Oo]utput)")
            match_name = re.match(match_pattern, original_comp_name)

            # Match output comp comment (metadata)
            original_comp_comment = comp.comment
            output_comp_pattern = re.compile(
                (
                    r"(?P<head>.+)?"
                    r"(?P<AFX_comp>[afxAFX]{3} [Oo]ut(put)? [Cc]omp([ositne]+)?)"
                    r"(?P<tail>.+)?"
                )
            )
            match_output_comp = re.match(output_comp_pattern, original_comp_comment)

            self.parent.engine.logger.debug(
                "original_comp_name: {}".format(original_comp_name)
            )
            self.parent.engine.logger.debug(
                "original_comp_comment: {}".format(original_comp_comment)
            )

            if match_name and match_output_comp:
                if comp not in output_comps:
                    output_comps.append(comp)

        return output_comps

    # def get_shot_data_from_SG(self):
    #     """
    #     Returns a dictionary of shot data from SG if the current context
    #     is a shot of the following form:

    #     {
    #         'id': (int),
    #          'sg_cut_duration': (int),
    #          'sg_cut_duration_in_seconds': (float),
    #          'sg_cut_in': (int),
    #          'sg_cut_out': (int),
    #          'type': (str),
    #      }
    #     """

    #     shot_data = {}

    #     if self.parent.engine.context.entity["type"] == "Shot":
    #         filters = [
    #             ["id", "is", self.parent.engine.context.entity["id"]],
    #         ]
    #         fields = [
    #             "sg_cut_in",
    #             "sg_cut_out",
    #             "sg_cut_duration",
    #             "sg_cut_duration_in_seconds",
    #         ]

    #         shot_data = self.parent.engine.shotgun.find_one(
    #             entity_type="Shot", filters=filters, fields=fields
    #         )

    #     return shot_data

    def get_composition_info(self, comp):
        """
        Returns a dictionary containing relevant comp info, like name, frame in,
        frame out, etc
        """

        result = {}
        adobe = self.parent.engine.adobe

        result["name"] = comp.name
        result["duration"] = comp.duration
        # result["start_frame"] = shot_data.get("sg_cut_in", None)
        # result["end_frame"] = shot_data.get("sg_cut_out", None)
        result["start_frame"] = comp.displayStartFrame
        # duration_in_frames = adobe.app.timeToCurrentFormat(
        duration_in_frames = adobe.timeToCurrentFormat(
            result["duration"], comp.frameRate, True
        )
        result["end_frame"] = result["start_frame"] + int(duration_in_frames)

        return result

    def show_message(self, msg, icon=None):
        """
        Shows a message box with the input message
        """

        from sgtk.platform.qt import QtCore, QtGui

        icons_dict = {
            "NoIcon": QtGui.QMessageBox.NoIcon,
            "Question": QtGui.QMessageBox.Question,
            "Information": QtGui.QMessageBox.Information,
            "Warning": QtGui.QMessageBox.Warning,
            "Critical": QtGui.QMessageBox.Critical,
        }

        if not icon or icon not in icons_dict.keys():
            icon_selection = icons_dict["NoIcon"]
        else:
            icon_selection = icons_dict[icon]

        msg_error = QtGui.QMessageBox()
        msg_error.setWindowTitle("Finished execution.")
        msg_error.setText(msg)
        msg_error.setIcon(icon_selection)
        msg_error.exec_()
