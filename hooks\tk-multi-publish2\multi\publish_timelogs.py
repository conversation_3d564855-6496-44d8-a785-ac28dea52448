################################################################################
#
# Copyright (c) 2022 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty Animation Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
################################################################################

import sgtk
import sys
import os

HookBaseClass = sgtk.get_hook_baseclass()


class TimeLogs(HookBaseClass):
    @property
    def name(self):
        return "TimeLogs"

    def post_finalize(self, publish_tree):
        super(TimeLogs, self).post_finalize(publish_tree)

        if os.environ.get("SG_PROCESSING_SECONDARY_OUTPUTS", False):
            return

        if not self.parent.engine.has_ui:
            msg = (
                "The current engine '{}' does not have a UI. "
                "Skipping Timelogs creation."
            ).format(self.parent.engine.name)
            self.parent.logger.warning(msg)
            return

        framework = self.load_framework("mty-framework-timelogs")
        framework.run_timelogs(self.parent.context, self.parent)
