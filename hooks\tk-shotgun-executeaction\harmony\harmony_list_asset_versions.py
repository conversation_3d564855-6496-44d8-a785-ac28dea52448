#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

"""
An app that fixes amn image (read) node's scale and proportions, by setting the right
parameters in the node properties windows. Basically replicates the import options
when using "Project Resolution" in the alignment rules when importing image as bitmap.
Also adds the proper scaling in the transformation tab

"""

from tank import Hook
from tank import TankError
from tank.platform import Application
from tank.platform.qt import QtCore, QtGui
import tank

import os
import re
import json
import time
import pprint
import tempfile


pp = pprint.pprint
pf = pprint.pformat


QFont = QtGui.QFont
QColor = QtGui.QColor
QDialog = QtGui.QDialog
QHeaderView = QtGui.QHeaderView
QPushButton = QtGui.QPushButton
QVBoxLayout = QtGui.QVBoxLayout
QTableWidget = QtGui.QTableWidget
QApplication = QtGui.QApplication
QTableWidgetItem = QtGui.QTableWidgetItem


META_SHOTGUN_PATH = "meta.shotgun.path"


class ProcessItemsHook(Hook):
    def execute(self, **kwargs):
        self.parent.engine.logger.info(
            "execute action list_asset_versions start. {}".format("-" * 80)
        )

        # Ensure the scene is saved
        self.parent.engine.app.save_project()

        node_type_list = ["READ", "CAMERA", "MULTIPORT_IN"]
        asset_versions_dict = self.collect_nodes_of_type(node_type_list) or {}

        if asset_versions_dict:
            asset_versions_dict = self.add_version_token_to_dict(asset_versions_dict)

        self.parent.engine.logger.info(
            "asset_versions_dict:\n{}".format(pf(asset_versions_dict))
        )

        self.show_asset_versions_dialog(asset_versions_dict)

        if not asset_versions_dict:
            error_msg = "Couldn't find any asset versions in the current scene."
            result = {"succes": [], "messages": [error_msg], "errors": [1]}

        # # Ensure the scene is saved
        # self.parent.engine.app.save_project()

        self.parent.engine.logger.info(
            "execute action list_asset_versions end. {}".format("-" * 80)
        )

        return {"succes": [1], "messages": [], "errors": []}

    def collect_nodes_of_type(self, node_type_list):
        """
        Collects all nodes of types included in the node_type_list
        """

        result = None

        file_path = self.create_tmp_json_file("asset_versions")
        self.parent.logger.info("file_path: {}".format(file_path))

        collect_nodes_cmd = """
var log = MessageLog.trace;

function get_nodes_list(node_type_list) {
    var nodes_list = node.getNodes(node_type_list);
    return nodes_list;
}

function filter_nodes_by_attribute(nodes_list, attr_name) {
    var array_of_filtered_nodes = {};
    for (var i = 0; i < nodes_list.length; ++i) {
        var node_path = nodes_list[i];
        if (node.type(node_path) == "MULTIPORT_IN") {
            node_path = node.dstNode(node_path, 0, 0);
        }
        log("----------------------------");
        log("checking node: " + node_path);
        var attr = node.getAttr(node_path, 1.0, attr_name);

        var path = attr.textValue();
        if (path.length > 0) {
            log("path: " + path);
            var basename = path.split("/").pop();
            if (basename.indexOf(".jsonx") !== -1) {
                if (!("Cameras" in array_of_filtered_nodes)) {
                    array_of_filtered_nodes["Cameras"] = {};
                }
                array_of_filtered_nodes["Cameras"][node_path] = {"file_name": basename, "version": null};
            } else if (basename.indexOf(".tpl") !== -1) {
                if (!("TPLs" in array_of_filtered_nodes)) {
                    array_of_filtered_nodes["TPLs"] = {};
                }
                // For tpls we get the metadata from its main peg inside of the tpl
                // group, but we add the main group to the display message to make it
                // clear to which group we are referring to. This is the parent of the
                // parent of the main tpl peg.
                array_of_filtered_nodes["TPLs"][node.parentNode(node_path)] = {"file_name": basename, "version": null};
            } else if (basename.indexOf(".mov") !== -1) {
                    if (!("Movies" in array_of_filtered_nodes)) {
                        array_of_filtered_nodes["Movies"] = {};
                    }
                    array_of_filtered_nodes["Movies"][node_path] = {"file_name": basename, "version": null};
            } else if (basename.indexOf(".obj") !== -1 || basename.indexOf(".fbx") !== -1 || basename.indexOf(".abc") !== -1) {
                    if (!("3d Meshes" in array_of_filtered_nodes)) {
                        array_of_filtered_nodes["3d Meshes"] = {};
                    }
                    array_of_filtered_nodes["3d Meshes"][node_path] = {"file_name": basename, "version": null};
            } else {
                if (!("Images" in array_of_filtered_nodes)) {
                    array_of_filtered_nodes["Images"] = {};
                }
                array_of_filtered_nodes["Images"][node_path] = {"file_name": basename, "version": null};
            }
        }
    }
    return array_of_filtered_nodes
}

var node_type_list = %s;
var attr = "%s"
var file_path = "%s"

var nodes_list = get_nodes_list(node_type_list);
// MessageLog.trace(JSON.stringify(nodes_list, null, 4));

var array_of_filtered_nodes = filter_nodes_by_attribute(nodes_list, attr);

file = new File(file_path);
file.open(2);
file.write(JSON.stringify(array_of_filtered_nodes, null, 4));
file.close();

""" % (
            node_type_list,
            META_SHOTGUN_PATH,
            file_path,
        )
        self.parent.engine.logger.debug(collect_nodes_cmd)
        # asset_versions_dict = self.parent.engine.app.execute(collect_nodes_cmd)
        self.parent.engine.app.execute(collect_nodes_cmd)

        asset_versions_dict = {}
        counter = 0
        while not asset_versions_dict and counter < 20:
            time.sleep(5)
            counter += 1
            self.parent.engine.logger.info("sleep round: {}".format(counter))
            try:
                with open(file_path, "r") as json_file:
                    asset_versions_dict = json.load(json_file)
            except:
                pass

        if not asset_versions_dict:
            self.parent.engine.logger.info("Couldn't get asset_versions_dict.")
            return result

        else:
            return asset_versions_dict

        # msg = ""
        # # # Add cameras
        # # if "Cameras" in asset_versions_dict:
        # #     msg += "\nCameras:\n"
        # #     for cam in asset_versions_dict["Cameras"]:
        # #         msg += "    {}:  -->  {}\n".format(cam, asset_versions_dict["Cameras"][cam])

        # # # Add Images
        # # if "Images" in asset_versions_dict:
        # #     msg += "\nImages:\n"
        # #     for img in asset_versions_dict["Images"]:
        # #         msg += "    {}:  -->  {}\n".format(img, asset_versions_dict["Images"][img])

        # # # Add TPLs
        # # if "TPLs" in asset_versions_dict:
        # #     msg += "\nTPLs:\n"
        # #     for tpl in asset_versions_dict["TPLs"]:
        # #         msg += "    {}:  -->  {}\n".format(tpl, asset_versions_dict["TPLs"][tpl])

        # # dynamically add whatever categories were found
        # for key in asset_versions_dict.keys():
        #     msg += "\n\n{}:".format(key)
        #     for value in asset_versions_dict[key]:
        #         msg += "\n    {}:  -->  {}\n".format(value, asset_versions_dict[key][value])

        # self.parent.engine.logger.info("asset versions msg:\n{}".format(msg))
        # # self.parent.engine.show_message(msg)
        # QtGui.QMessageBox.information(
        #     None, "Asset versions", msg
        # )

        # return msg

    def add_version_token_to_dict(self, asset_versions_dict):
        pattern = re.compile(r"(?P<head>.+)(?P<version>v\d{3})(?P<tail>.*)")
        for category in asset_versions_dict.keys():
            for node, node_dict in asset_versions_dict[category].items():
                file_name = node_dict["file_name"]
                pattern_match = re.match(pattern, file_name)
                if pattern_match:
                    version = pattern_match.groupdict()["version"]
                    asset_versions_dict[category][node]["version"] = version

        return asset_versions_dict

    def create_tmp_json_file(self, basename):
        """
        Creates a tmp file based on the input basename, in the current project folder,
        inside a tmpfiles folder and returns the full file path
        """

        result = None

        # xstage_path = self._session_path()
        tmp_dir = tempfile.mkdtemp()
        tmpfiles_folder = os.path.join(tmp_dir, "tmpfiles")

        if not os.path.exists(tmpfiles_folder):
            try:
                os.makedirs(tmpfiles_folder)
            except:
                error_msg = "Couldn't create tmpfiles folder: {}".format(
                    tmpfiles_folder
                )
                raise Exception(error_msg)

        json_filename = "{}.json".format(basename)
        json_fullpath = os.path.join(tmpfiles_folder, json_filename)
        json_fullpath = json_fullpath.replace("\\", "/")

        try:
            f = open(json_fullpath, "w+")
            f.close()
        except:
            error_msg = "Couldn't create tmpfile '{}''.".format(json_fullpath)
            raise Exception(error_msg)
            # pass

        if os.path.exists(json_fullpath):
            return json_fullpath
        else:
            return result

    def show_asset_versions_dialog(self, asset_versions_dict):
        """Shows a dialog with the asset versions found in the current scene."""

        dialog = QDialog()
        layout = QVBoxLayout()
        table = QTableWidget()

        table.setColumnCount(4)  # Adjusted for 4 columns now
        table.setHorizontalHeaderLabels(["Type", "Node", "Version", "File Name"])

        row = 0

        for category, values in asset_versions_dict.items():
            table.insertRow(row)
            category_item = QTableWidgetItem(category)
            # Making the category uneditable

            # Qt 5.x code (PySide2)
            if QtCore.__version__.startswith("5."):
                category_item.setFlags(
                    category_item.flags() ^ 0x0004
                )
            # Qt 6.x code (PySide6)
            elif QtCore.__version__.startswith("6."):
                category_item.setFlags(
                    category_item.flags() & ~QtCore.Qt.ItemFlag.ItemIsEditable
                )


            # Create a font object for the "Type" column
            category_item_font = QFont()
            category_item_font.setBold(True)
            category_item_font.setPointSize(11)  # Set the font size

            category_item.setFont(category_item_font)  # Apply the font to the item

            # Set background color for 'Type' column
            category_item.setBackground(QColor(60, 60, 60))  # Adjust color as needed

            table.setItem(row, 0, category_item)
            table.setSpan(row, 0, 1, 4)  # Span the category item across four columns

            row += 1

            for key, value in values.items():
                table.insertRow(row)

                # Qt 5.x code (PySide2)
                if QtCore.__version__.startswith("5."):
                    # Adjusting for the nested structure
                    key_item = QTableWidgetItem(key)
                    # Making the key uneditable
                    key_item.setFlags(
                        key_item.flags() ^ 0x0004
                    )

                    version_item = QTableWidgetItem(value["version"])
                    # Making the version uneditable
                    version_item.setFlags(
                        version_item.flags() ^ 0x0004
                    )

                    file_name_item = QTableWidgetItem(value["file_name"])
                    # Making the file name uneditable
                    file_name_item.setFlags(
                        file_name_item.flags() ^ 0x0004
                    )

                # Qt 6.x code (PySide6)
                elif QtCore.__version__.startswith("6."):
                    # Adjusting for the nested structure
                    key_item = QTableWidgetItem(key)
                    # Making the key uneditable
                    key_item.setFlags(
                        key_item.flags() & ~QtCore.Qt.ItemFlag.ItemIsEditable
                    )

                    version_item = QTableWidgetItem(value["version"])
                    # Making the version uneditable
                    version_item.setFlags(
                        version_item.flags() & ~QtCore.Qt.ItemFlag.ItemIsEditable
                    )

                    file_name_item = QTableWidgetItem(value["file_name"])
                    # Making the file name uneditable
                    file_name_item.setFlags(
                        file_name_item.flags() & ~QtCore.Qt.ItemFlag.ItemIsEditable
                    )


                table.setItem(row, 1, key_item)
                table.setItem(row, 2, version_item)
                table.setItem(row, 3, file_name_item)

                row += 1

        table.resizeColumnsToContents()
        table.horizontalHeader().setStretchLastSection(
            True
        )  # Resize last column to fill remaining space

        # Auto-resizing vertically
        table.verticalHeader().setSectionResizeMode(QHeaderView.ResizeToContents)
        table.verticalHeader().setMaximumSectionSize(1000)  # Limiting maximum height

        # Adjusting widget size
        table.setMinimumWidth(750)  # Minimum width
        table.setMinimumHeight(250)  # Minimum height
        table.setMaximumWidth(1900)  # Maximum width
        table.setMaximumHeight(1000)  # Maximum height

        ok_button = QPushButton("OK")
        ok_button.clicked.connect(dialog.accept)

        layout.addWidget(table)
        layout.addWidget(ok_button)

        dialog.setLayout(layout)
        dialog.adjustSize()
        dialog.setWindowTitle("Asset versions")

        dialog.exec_()
