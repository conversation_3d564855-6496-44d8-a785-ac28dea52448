#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

import os
import re
import shutil
import traceback
import subprocess

from os import listdir
from os.path import isfile, join

import hou

import sgtk
from sgtk.platform.qt import QtGui, QtCore

HookBaseClass = sgtk.get_hook_baseclass()


class HoudiniPublishReviewPlugin(HookBaseClass):
    """
    Plugin for publish rendered sequence

    """

    @property
    def icon(self):
        """
        Path to an png icon on disk
        """

        # look for icon one level up from this hook's folder in "icons" folder
        return os.path.join(
            self.disk_location,
            os.pardir,
            "icons",
            "review.png"
        )

    @property
    def name(self):
        """
        One line display name describing the plugin
        """
        return "Publish sequence files"

    @property
    def description(self):
        """
        Copy/upload the render sequence to publish area
        """

        loader_url = "https://support.shotgunsoftware.com/hc/en-us/articles/219033078"

        return """
        <p>This plugin upload the video as a version in shotgun.</p>
        """

    @property
    def settings(self):
        """
        Dictionary defining the settings that this plugin expects to recieve
        through the settings parameter in the accept, validate, publish and
        finalize methods.

        A dictionary on the following form::

            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }

        The type string should be one of the data types that toolkit accepts
        as part of its environment configuration.
        """

        # settings specific to this class
        publish_settings = {

            "Work Template": {
                "type": "template",
                "default": "harmony_shot_work",
                "description": "Harmony work session"},

            "Primary Publish Template": {
                "type": "template",
                "default": "harmony_shot_publish",
                "description": "Harmony publish session"},

            "Publish Render Template": {
                "type": "template",
                "default": "shot_flat_render_publish_exr",
                "description": "Template where render sequence files got published"},

            "Publish Review Template":{
                "type": "template",
                "default": "shot_flat_render_publish_mov",
                "description": "Template to create a mov file"},

            "Publish Review Type": {
                "type": "str",
                "default": "Media Review",
                "description": "Published File Type name to use for the published movie"}

        }

        # inherit the settings from the base publish plugin
        plugin_settings = super(HoudiniPublishReviewPlugin, self).settings or {}

        # update the base settings
        plugin_settings.update(publish_settings)
        return plugin_settings

    @property
    def item_filters(self):
        """
        List of item types that this plugin is interested in.

        Only items matching entries in this list will be presented to the
        accept() method. Strings can contain glob patters such as *, for example
        ["fusion.*", "file.fusion"]
        """
        return ["houdini.frames_sequence"]

    def accept(self, settings, item):
        """
        Method called by the publisher to determine if an item is of any
        interest to this plugin. Only items matching the filters defined via the
        item_filters property will be presented to this method.

        A submit for review task will be generated for each item accepted here. Returns a
        dictionary with the following booleans:

            - accepted: Indicates if the plugin is interested in this value at
                all. Required.
            - enabled: If True, the plugin will be enabled in the UI, otherwise
                it will be disabled. Optional, True by default.
            - visible: If True, the plugin will be visible in the UI, otherwise
                it will be hidden. Optional, True by default.
            - checked: If True, the plugin will be checked in the UI, otherwise
                it will be unchecked. Optional, True by default.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process

        :returns: dictionary with boolean keys accepted, required and enabled.
            This plugin makes use of the tk-multi-reviewsubmission app; if this
            app is not available then the item will not be accepted, this method
            will return a dictionary with the accepted field that has a value of
            False.

            Several properties on the item must also be present for it to be
            accepted. The properties are 'path', 'publish_name', 'color_space',
            'first_frame' and 'last_frame'
        """

        publisher = self.parent
        engine = publisher.engine
        context = engine.context

        # Verify entity type SHOT
        entity_type = str(context).split(' ')[1]
        if entity_type != "Shot":
            return {'accepted': False}

        return {"accepted": True,
                "checked": True}

    def get_sg_shot_info(self):

        sg_proj = self.parent.engine.context.project
        shotgun = self.parent.engine.shotgun
        current_entity = self.parent.engine.context.entity

        order = [{'field_name':'version_number', 'direction':'desc'}]
        sg_qry = shotgun.find_one("PublishedFile",
                            filters=[
                                ['project', 'is', sg_proj],
                                ["published_file_type.PublishedFileType.code", "is", "Editorial Audio"],
                                ["entity.Shot.id", "is", current_entity["id"]]
                            ],
                            fields=[
                                "path",
                                "entity.Shot.sg_cut_in",
                                "entity.Shot.sg_cut_out",
                                "entity",
                                "version_number"],
                            order=order)

        return sg_qry

    def ensure_file_is_local(self, path, publish):

        if not hasattr(self, 'metasync'):
            self.metasync = self.load_framework("mty-framework-metasync")

        transfersManager = self.metasync.transfersManager
        if "%" in path:
            dirname = os.path.dirname(path)
            if os.path.exists(dirname) and self._collect_sequenced_files(path):
                transfersManager.ensure_local_dependencies(publish)
                return path
        else:
            if os.path.exists(path):
                transfersManager.ensure_local_dependencies(publish)
                return path

        transfersManager.ensure_file_is_local(path, publish)
        transfersManager.ensure_local_dependencies(publish)

        return path

    def getShiftNode(self, chopnet_node, audio_file_node):
        """Get the shift node to offset the audio file, or create a new one if it
        doesn't exist"""

        shift_node_comment = "Shift node to offset the SG audio file. DON'T DELETE"

        # get all shift instances
        shift_node_instances = self.getNodeInstances(hou.chopNodeTypeCategory(), "shift")

        # find the (first) node with the "shift_node_comment"
        shift_node = None
        if len(shift_node_instances) > 0:
            for sh in shift_node_instances:
                if sh.comment() == shift_node_comment:
                    shift_node = sh
                    break

        # if there's chopnet node with the comment, then create one
        if shift_node is None:
            shift_node = audio_file_node.createOutputNode("shift")
            shift_node.setName("audio_offset",
                               unique_name=True)
            shift_node.setComment(shift_node_comment)
            shift_node.moveToGoodPosition()

        # colorize the node
        shift_node.setColor(hou.Color(0.8, 0.15, 0.15))

        return shift_node

    def fixPath(self, path):
        """removes backslashes and replace them with forwardd slashes"""

        path = path.replace("\\", "/")
        path = path.replace("//", "/")

        return path

    def getStartFrame(self):

        try:
            engine = self.parent.engine
            shotgun = engine.shotgun
            data = shotgun.find_one(entity_type="Shot",
                                           filters=[["id",
                                                     "is",
                                                     engine.context.entity["id"]]],
                                           fields=["sg_cut_in",
                                                   "sg_cut_out"])

            shot_start_frame = data["sg_cut_in"]
            shot_end_frame = data["sg_cut_out"]

            return shot_start_frame

        except Exception:
            scene_frame_range = hou.playbar.frameRange()
            shot_frame_range = (scene_frame_range[0], scene_frame_range[1])
            print("shot_frame_range from scene: {}".format(shot_frame_range))

            shot_start_frame = shot_frame_range[0]
            shot_end_frame = shot_frame_range[1]

            return shot_start_frame

    def createContextOptions(self, audio_path):
        """ Create context options (variables) at scene level so various nodes resolve
        their paths using these variables, eg shot_root, shot, etc. These should be
        created using SG queries. These oprtions can be accessed by the nodes using
        @contextOption or `@contextOption` if it's inside a string, eg @shot_root"""

        audio_file_path = self.fixPath(audio_path)

        shot_start_frame = self.getStartFrame()

        hou.setContextOption("audio_file_path", audio_file_path)
        hou.setContextOption("shot_start_frame", shot_start_frame)

        audio_file_path_options = ('{"label": "Audio File Path", "type": "file_path", "order": 0}')
        shot_start_frame_options = ('{"label": "Shot Start Frame", "type": "number", "order": 1}')

        hou.setContextOptionConfig("audio_file_path", audio_file_path_options)
        hou.setContextOptionConfig("shot_start_frame", shot_start_frame_options)

    def setChopParameters(self, node, path):
        """Set the Chop values"""

        file_node = self.getAudioFileNode(node)

        file_node.setParms({"file": "`@audio_file_path`",
                            "export": "."})

        shift_node = self.getShiftNode(node, file_node)

        self.createContextOptions(path)

        shift_node.setParmExpressions({"start": "@shot_start_frame"})
        shift_node.setParms({"units": 0,
                             "export": "."})

        shift_node.setDisplayFlag(True)
        shift_node.setAudioFlag(True)
        shift_node.setExportFlag(True)

        self.parent.log_debug("node: {}".format(node))
        self.parent.log_debug("File node: {}".format(file_node))

        return shift_node

    def setSceneAudio(self, shift_node):
        """ Set the audio panel options to point to the audio chopnet"""

        hou.audio.useTimeLineMode()
        hou.audio.setScrubRepeat(False)
        hou.audio.setScrubSustain(10)
        hou.audio.setScrubRate(10)
        hou.audio.useChops()
        hou.audio.setChopPath(shift_node.path())

    def add_shot_audio(self, path):


        chopnet_node = self.getAudioChopnetNode()
        self.parent.log_debug("Set chop audio node parameters...")
        # Set Chopnet parameters
        audio_shift = self.setChopParameters(chopnet_node, path)

        # Enable use of file audio
        self.setSceneAudio(audio_shift)


    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish. Returns a
        boolean to indicate validity.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        :returns: True if item is valid and not in proxy mode, False otherwise.
        """

        sg_audio_data = self.get_sg_shot_info()
        shot_cutin = sg_audio_data.get("entity.Shot.sg_cut_in", False)
        shot_cutout = sg_audio_data.get("entity.Shot.sg_cut_out", False)
        last_published_audio = sg_audio_data.get("path", False)
        last_published_audio_path = None
        last_audio_version_number = sg_audio_data.get("version_number", None)

        if last_published_audio:
            last_published_audio_path = last_published_audio.get("local_path", None)
            last_published_audio_path = last_published_audio_path.replace("\\", "/")
        else:
            # Editorial audio si missing
            raise Exception("Missing a published Editorial Audio for the Shot.")

        if not hou.hasContextOption("audio_file_path"):
            if not os.path.exists(last_published_audio_path):
                self.ensure_file_is_local(last_published_audio_path, sg_audio_data)

            self.add_shot_audio(last_published_audio_path)

        if not shot_cutin or not shot_cutout:
            raise Exception("Missing required Shot cut-in or cut-out values in Shotgrid.")

        sequence = item.properties['sequence']

        # TODO: Expand validation for proper frame range too
        # we can only validate if the number of frames matches shot
        sequence_frames = len(sequence)
        shot_frames = shot_cutout - shot_cutin + 1

        if sequence_frames != shot_frames:
            error_msg = "Shot lenght and render frames don't match: {0} - {1}"
            raise Exception(error_msg.format(shot_frames, sequence_frames))

        # Other known issue is if the render naming convention uses "-"
        # that will cause conflict with the image sequence recognition
        # and will make the numbers look like negative, which will cause problems
        if sequence.start() < 0 or sequence.end() < 0:
            error_msg = ("It looks like you have negative numbers!\n"
                         "This is mostly because of your frames naming "
                         "convention, just make sure that you are not using '-'")
            raise Exception(error_msg)


        # Get scene audio
        audio_path = hou.contextOption("audio_file_path")

        if audio_path:
            if audio_path != last_published_audio_path:
                self.parent.log_debug("1, {}".format(audio_path))
                self.parent.log_debug("2, {}".format(last_published_audio_path))
                raise Exception("Scene audio is missing or must be updated to last published version number: {}.".format(last_audio_version_number))
        else:
            raise Exception("Editorial Audio is missing, please load last published audio.")

        """ VALIDATION FALLBACK TO GET SG AUDIO
        else:
            # If no audio track in scene, Fallback to sg audio
            audio_path = last_published_audio_path
            self.parent.log_debug("Validating local audio path... {}".format(audio_path))
            if not os.path.exists(last_published_audio_path):
                self.parent.log_debug("Downloading audio path...")
                self.ensure_file_is_local(last_published_audio_path, sg_audio_data)

        # If there was no audio path even after sg falback, send validation error
        if not audio_path:
            raise Exception("Missing a published Editorial Audio for the Shot.")
        """



        item.properties["cutin"] = shot_cutin
        item.properties["cutout"] = shot_cutout
        item.properties["cutin"] = shot_cutout
        item.properties["audio_path"] = audio_path

        return True


    def getNodeInstances(self, category, node_type):
        """Find all instances of the specified node type. Returns a list.

        category must be one of the following:

        hou.chopNetNodeTypeCategory()
        hou.chopNodeTypeCategory()
        hou.cop2NetNodeTypeCategory()
        hou.cop2NodeTypeCategory()
        hou.dopNodeTypeCategory()
        hou.lopNodeTypeCategory()
        hou.managerNodeTypeCategory()
        hou.nodeTypeCategories()
        hou.objNodeTypeCategory()
        hou.rootNodeTypeCategory()
        hou.ropNodeTypeCategory()
        hou.shopNodeTypeCategory()
        hou.sopNodeTypeCategory()
        hou.topNodeTypeCategory()
        hou.vopNetNodeTypeCategory()
        hou.vopNodeTypeCategory()


        node_type must be a string describing the node type (node.type().name())
        """

        # print(category.name(), node_type)
        nodeType = hou.nodeType(category, node_type)
        nodeInstances = nodeType.instances()

        return nodeInstances

    def getAudioChopnetNode(self):
        """Get the audio chopnet node containing the audio file, or create a new one
        if it doesn't exist"""

        self.parent.log_debug("Get audio chot node...")

        ch_context = hou.node("/ch")  # CHOPs context
        audio_chop_node_comment = "ChopNet to load the SG shot audio. DON'T DELETE"

        # get all chopnet instances
        chopnet_node_instances = self.getNodeInstances(hou.chopNetNodeTypeCategory(), "ch")

        # find the (first) node with the "audio_chop_node_comment"
        chopnet_node = None
        if len(chopnet_node_instances) > 0:
            for chop in chopnet_node_instances:
                if chop.comment() == audio_chop_node_comment:
                    chopnet_node = chop
                    break

        # if there's chopnet node with the comment, then create one
        if chopnet_node is None:
            chopnet_node = ch_context.createNode("ch")
            chopnet_node.setName("audio",
                                 unique_name=True)
            chopnet_node.setComment(audio_chop_node_comment)
            chopnet_node.moveToGoodPosition()

        # colorize the node
        chopnet_node.setColor(hou.Color(0.8, 0.15, 0.15))

        return chopnet_node

    def getAudioFileNode(self, chopnet_node):
        """Get the file node containing the audio file, or create a new one if it
        doesn't exist"""

        audio_file_node_comment = "File node to load the shot audio. DON'T DELETE"

        # get all file instances
        audio_file_node_instances = self.getNodeInstances(hou.chopNodeTypeCategory(), "file")

        # find the (first) node with the "audio_file_node_comment"
        audio_file_node = None
        if len(audio_file_node_instances) > 0:
            for afile in audio_file_node_instances:
                if afile.comment() == audio_file_node_comment:
                    audio_file_node = afile
                    break

        # if there's chopnet node with the comment, then create one
        if audio_file_node is None:
            audio_file_node = chopnet_node.createNode("file")
            audio_file_node.setName("shot_audio",
                                    unique_name=True)
            audio_file_node.setComment(audio_file_node_comment)
            audio_file_node.moveToGoodPosition()

        # colorize the node
        audio_file_node.setColor(hou.Color(0.8, 0.15, 0.15))

        return audio_file_node

    def getAudioPathFromNode(self, audio_file_node):
        """Get audio path from file node"""

        parm = audio_file_node.parm("file")
        audio_path = parm.unexpandedString()

        return audio_path

    def getSceneAudio(self):
        """Main function to add the audio file to the shot"""

        self.parent.log_debug("About to collect sound node from scene...")
        chopnet_node = self.getAudioChopnetNode()
        audio_file_node = None
        self.parent.log_debug("Collecting sound node from chop...")
        if chopnet_node is not None:
            self.parent.log_debug("Collecting sound file from chop node...")
            audio_file_node = self.getAudioFileNode(chopnet_node)
            self.parent.log_debug("File path from chop node: {}".format(audio_file_node))

            if audio_file_node is not None:
                audio_path = self.getAudioPathFromNode(audio_file_node)

                # validate is a published file path


                return audio_path, audio_file_node

        else:
            return None, None

    def publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """

        #   Video process
        engine = self.parent.engine

        # TODO: Find a generic way to resolve current dcc file path
        path = hou.hipFile.path()
        #path = engine.app.get_current_project_path()

        sequence = item.properties['sequence']
        tpl_by_name = engine.get_template_by_name

        # Gettiings settings
        work_template_setting = settings.get("Work Template")
        primary_publish_template_setting = settings.get("Primary Publish Template")
        render_publish_template_setting = settings.get("Publish Render Template")
        review_publish_template_setting = settings.get("Publish Review Template")
        review_publish_type_setting = settings.get("Publish Review Type")

        # Getting templates
        work_template = tpl_by_name(work_template_setting.value)
        primary_publish_template = tpl_by_name(primary_publish_template_setting.value)
        render_publish_template = tpl_by_name(render_publish_template_setting.value)
        review_publish_template = tpl_by_name(review_publish_template_setting.value)

        # Work area fields
        fields = work_template.get_fields(path)

        # Dynamic paths or elements from templates
        primary_publish_path = primary_publish_template.apply_fields(fields)
        render_publish_path  = render_publish_template.apply_fields(fields)
        review_publish_path = review_publish_template.apply_fields(fields)
        review_publish_type_name = review_publish_type_setting.value

        publish_name = fields["name"]
        publish_version = fields["version"]
        padding = len(sequence[0].split('.')[1])
        sg_proj = self.get_sg_project_info(['sg_working_color_space', 'sg_fps'])

        # source sequence to convert
        source_seq = os.path.normpath(
            ''.join(
                [
                    sequence.dirname(),
                    sequence.basename(),
                    '%0{}d'.format(sequence._zfill),
                    sequence.extension()
                ]
            )
        )


        # Converting exr to png files
        # Conversion to other colorspace
        seq_first_f = sequence.start()
        srgb_seq = self.convert_seq_to_srgb(
            settings, item, source_seq, sg_proj, seq_first_f
        )
        if not srgb_seq:
            # Second try
            srgb_seq = self.convert_seq_to_srgb(
                settings, item, source_seq, sg_proj, seq_first_f
            )
        if not srgb_seq:
            message = "Failed to png sequence, check sequence: %s"
            raise Exception(message % (source_seq))

        # Audio section
        audio_cmd_1 = ""
        audio_cmd_2 = ""

        #command = "sound.getSoundtrackAll().path();"
        #audio_path = engine.app.custom_script(command)
        audio_path = item.properties["audio_path"]
        if os.path.exists(audio_path):
            audio_cmd_1 = ' -i "{}"'.format(audio_path)
            """
            audio_cmd_2  = ' -c:a aac'
            audio_cmd_2  = ' -acodec pcm_s16le'
            audio_cmd_2 += ' -aframes 0'
            audio_cmd_2 += ' -map 1:a'
            """
            audio_cmd_2 = ' -acodec pcm_s16le'

        # Check if video folder exists
        self.parent.engine.ensure_folder_exists(
            os.path.dirname(review_publish_path)
        )

        message = 'Creating video from png files: {}'
        self.logger.info(message.format(review_publish_path))

        # its important that the video filter goes
        # last to preserve the color matrix
        ffmpeg_tpl = "-y"
        ffmpeg_tpl += " -start_number {FRAME}"
        ffmpeg_tpl += " -i {SEQ}"

        if audio_cmd_1:
            ffmpeg_tpl += audio_cmd_1

        ffmpeg_tpl += " -s 1920x1080"

        if audio_cmd_2:
            ffmpeg_tpl  += audio_cmd_2

        ffmpeg_tpl += " -vcodec libx264"
        ffmpeg_tpl += " -pix_fmt yuv422p"
        ffmpeg_tpl += " -r {FRAME_RATE}"
        #ffmpeg_tpl  += " -map 0:v"
        ffmpeg_tpl += " -crf 5"
        ffmpeg_tpl += ' -vf "colorspace=bt709:iall=bt601-6-625:fast=1"'
        ffmpeg_tpl += " {VIDEO}"

        ffmpeg_cmd = ffmpeg_tpl.format(FRAME=str(sequence.start()),
                                         SEQ=srgb_seq,
                                         FRAME_RATE=sg_proj['sg_fps'],
                                         VIDEO=review_publish_path)

        ffmpeg_framework = self.load_framework("mty-framework-ffmpeg")

        _err, _info = ffmpeg_framework.ffmpegCore.execute_command(ffmpeg_cmd)
        if _err:
            message = "Failed to create video: %s\ncommand: %s"
            raise Exception(message % (_info, ffmpeg_cmd))

        # Cleaning pngs
        offset = item.properties["cutin"] - sequence.start()
        last_f = item.properties["cutout"] - offset

        self.logger.info('Cleaning temporal files')
        for i in range(sequence.start(), last_f+1):
            cur_frame = srgb_seq % i
            if os.path.exists(cur_frame):
                os.remove(cur_frame)

        #   Publish video process
        args = {
            "tk":               self.parent.engine.sgtk,
            "context":          item.context,
            "comment":          item.description,
            "path":             review_publish_path,
            "name":             publish_name,
            "version_number":   publish_version,
            "thumbnail_path":   item.get_thumbnail_as_path(),
            "task":             self.parent.engine.context.task,
            "dependency_paths": [render_publish_path],
            "sg_fields":        {"sg_status_list":   "rev"},
            "published_file_type": review_publish_type_name
        }

        sg_publishes = sgtk.util.register_publish(**args)

        item.properties.sg_publish_data = sg_publishes
        item.properties.sg_publish_path = sg_publishes["path"]["local_path"]

        self.parent.log_debug(
            "Adding existing publish " +
            "(id:{}) ".format(sg_publishes['id']) +
            "as extra data for item: {}".format(item)
        )

        #   Version process Ref publish_shot_media_review.py from maya/layout
        sg_version = self.submit_version(
            render_publish_path,
            review_publish_path,
            [sg_publishes],
            self.parent.engine.context.task,
            item.description,
            int(item.properties["cutin"]),
            int(item.properties["cutout"]),
        )

        # Upload in a new thread and make our own event loop
        # to wait for the thread to finish.
        event_loop = QtCore.QEventLoop()
        thread = UploaderThread(
            self.parent, sg_version,
            review_publish_path,
            item.get_thumbnail_as_path(),
            True
        )

        thread.finished.connect(event_loop.quit)
        thread.start()
        event_loop.exec_()

        # log any errors generated in the thread
        for e in thread.get_errors():
            self.parent.log_error(e)


    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        self.logger.debug('Item video successfully published')



    def get_root_item(self, item):

        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root

    def submit_version(self, path_to_frames, path_to_movie, sg_publishes,
                       sg_task, comment, first_frame, last_frame):
        """
        Create a version in Shotgun for this path and linked to this publish.
        """

        # get current shotgun user
        current_user = self.parent.engine.context.user

        # create a name for the version based on the file name
        # grab the file name, strip off extension
        name = os.path.splitext(os.path.basename(path_to_movie))[0]
        # do some replacements and capitalize
        name = name.replace("_", " ").capitalize()

        LinkFolder = {'local_path': os.path.dirname(path_to_frames) + os.sep,
                      'content_type': None,
                      'link_type': 'local',
                      'name': name}

        LinkFile   = {'local_path': path_to_movie,
                      'content_type': None,
                      'link_type': 'local',
                      'name': name}

        entity = self.parent.engine.context.entity
        proj   = self.parent.engine.context.project
        # Create the version in Shotgun
        data = {
            "code":                 name,
            "sg_status_list":       "rev",
            "entity":               entity,
            "sg_task":              sg_task,
            "sg_first_frame":       first_frame,
            "sg_last_frame":        last_frame,
            "frame_count":          (last_frame - first_frame + 1),
            "frame_range":          "%s-%s" % (first_frame, last_frame),
            "sg_frames_have_slate": False,
            "published_files":      sg_publishes,
            "created_by":           current_user,
            "description":          comment,
            "sg_path_to_frames":    path_to_frames,
            "sg_path_to_movie":     path_to_movie,
            "sg_movie_has_slate":   False,
            "project":              proj,
            "user":                 current_user
        }

        sg_version = self.parent.engine.shotgun.create("Version", data)
        self.parent.log_debug("Created version in shotgun: %s" % str(data))

        return sg_version

    def convert_seq_to_srgb(self, settings, item, source_seq, sg_proj, seq_first_f):

        _, source_ext = os.path.splitext(source_seq.lower())
        if source_ext in ['.png', '.jpg']:
            return source_seq

        # Element used to convert exr to png for colorconverting
        ocio = self.load_framework("mty-framework-opencolorio")
        oiio = self.load_framework("mty-framework-openimageio")
        oiio_tool = oiio.openImageIOCore
        ocio_path = ocio.disk_location + "/version/aces_1_2/config.ocio"
        frames_limit = 15

        oiio_tool.set_binary('oiiotool')
        oiio_exe = oiio_tool.get_bin_path()
        all_colorspaces = {'sRGB': 'Output - sRGB',
                           'rec709': 'Output - Rec.709',
                           'aces_cg': 'ACES - ACEScg'}

        fr_start = int(item.properties['cutin'])
        fr_end = int(item.properties['cutout'])
        total_frames = fr_end - fr_start
        oiio_jobs = int(total_frames / frames_limit) +1

        # Offset frames to sequence files range
        offset = fr_start - seq_first_f
        fr_start = seq_first_f
        fr_end = fr_end - offset

        # png with the color already converted
        temp_seq = '.'.join(["{}_tmp".format(source_seq.split('.')[0]),
                                    source_seq.split('.')[1],
                                    'png'])

        # Using valid color space name
        proj_colorspace = sg_proj['sg_working_color_space']
        if proj_colorspace in all_colorspaces.keys():
            proj_colorspace = all_colorspaces[proj_colorspace]

        # Cleaning revious files
        folder_container = os.path.dirname(temp_seq)

        all_elements = os.listdir(folder_container)
        for sub_element in all_elements:
            if len(sub_element.split('.')) <=3:
                continue
            if sub_element.endswith('.temp.png'):
                full_el_path = os.path.join(folder_container, sub_element)
                os.remove(full_el_path)

        # Split the process in parts because it fails after frame 32
        for i in range(0, oiio_jobs):
            current_first_f = str(fr_start + (frames_limit * i))
            if i == oiio_jobs -1:
                current_last_f  = str(fr_end)
            else:
                current_last_f  = str(fr_start + (frames_limit * (i+1))-1)

            # oiiotool command
            if current_first_f != current_last_f:
                oiio_cmds  = "--frames {}-{}".format(current_first_f, current_last_f)
            else:
                oiio_cmds  = "--frames {}".format(current_first_f)

            oiio_cmds += " {}".format(source_seq)
            #oiio_cmds += " -ch R,G,B"
            oiio_cmds += " -colorconfig"
            oiio_cmds += ' "{}"'.format(ocio_path)
            oiio_cmds += " -colorconvert"
            oiio_cmds += ' "{}"'.format(proj_colorspace) # Input colorspace
            oiio_cmds += ' "Output - sRGB"' # output colorspace
            oiio_cmds += " -croptofull"
            oiio_cmds += " -o {}".format(temp_seq)

            info_msg = 'Temp png  Job: {}/{}  Frames: {}-{}'
            info_msg = info_msg.format(str(i+1), str(oiio_jobs+1),
                                       current_first_f,
                                       current_last_f)
            self.logger.info(info_msg)

            if not ' '  in oiio_exe:
                # _err is boolean for error in the command exec
                _err, _info, _cmd = oiio_tool.execute_command_str(oiio_cmds)

            if _err:
                war_msg = '{}/{} {}-{}: Error creating temp files'
                war_msg = war_msg.format(str(i+1), str(oiio_jobs+1), current_first_f, current_last_f)
                self.logger.error(war_msg)
                raise Exception("Failed to create png aux sequence:\n {}".format(_info))

        # Validating all the frames
        for i in range(fr_start, fr_end+1):
            cur_file = temp_seq % i
            if not os.path.exists(cur_file):
                return False

        return temp_seq

    def get_sg_project_info(self, proj_fields):
        engine         = self.parent.engine
        sg             = engine.shotgun
        sg_proj        = engine.context.project

        proj_filter    = [['id', 'is', sg_proj['id']]]

        sg_proj = sg.find_one('Project', proj_filter, proj_fields)
        return sg_proj


class UploaderThread(QtCore.QThread):
    """
    Simple worker thread that encapsulates uploading to shotgun.
    Broken out of the main loop so that the UI can remain responsive
    even though an upload is happening
    """
    def __init__(self, app, version, path_to_movie, thumbnail_path, upload_to_shotgun):
        QtCore.QThread.__init__(self)
        self._app               = app
        self._version           = version
        self._path_to_movie     = path_to_movie
        self._thumbnail_path    = thumbnail_path
        self._upload_to_shotgun = upload_to_shotgun
        self._errors            = []

    def get_errors(self):
        """
        can be called after execution to retrieve a list of errors
        """
        return self._errors

    def run(self):
        """
        Thread loop
        """
        upload_error = False

        if self._upload_to_shotgun:
            try:
                self._app.tank.shotgun.upload("Version", self._version["id"], self._path_to_movie, "sg_uploaded_movie")
            except Exception as e:
                self._errors.append("Movie upload to Shotgun failed: %s" % e)
                upload_error = True

        if not self._upload_to_shotgun or upload_error:
            try:
                self._app.tank.shotgun.upload_thumbnail("Version", self._version["id"], self._thumbnail_path)
            except Exception as e:
                self._errors.append("Thumbnail upload to Shotgun failed: %s" % e)
