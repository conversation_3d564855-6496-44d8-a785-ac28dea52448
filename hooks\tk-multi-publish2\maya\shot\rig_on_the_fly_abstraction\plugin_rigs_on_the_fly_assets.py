# -*- coding: utf-8 -*-
# Standard library:
import os
import json
import pprint
import traceback

# -- -- -- -- -- -- -- -- -- -- -- -- -- --
# Third party:
import sgtk
import maya.cmds as cmds
from sgtk.util.filesystem import copy_file, ensure_folder_exists

# -- -- -- -- -- -- -- -- -- -- -- -- -- --
# project:
HookBaseClass = sgtk.get_hook_baseclass()
# =============================================================================
pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)
pf = pprint.pformat


class RigOnTheFlyJsonPublishPlugin(HookBaseClass):
    @property
    def icon(self):
        """
        Path to an png icon on disk
        """
        # look for icon one level up from this hook's folder in "icons" folder
        return os.path.join(
            self.disk_location, os.pardir, os.pardir, os.pardir, "icons", "document.png"
        )

    @property
    def name(self):
        return "Publish Rigs On The Fly Json Abstraction to Shotgun"

    @property
    def description(self):

        return "Export Rigs on the Fly Json Abstractions"

    @property
    def settings(self):
        plugin_settings = super(RigOnTheFlyJsonPublishPlugin, self).settings or {}

        # settings specific to this class
        maya_publish_settings = {
            "Work Template": {
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                "correspond to a template defined in "
                "templates.yml.",
            },
            "Primary Publish Template": {
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                "correspond to a template defined in "
                "templates.yml.",
            },
            "Publish Template": {
                "type": "template",
                "default": None,
                "description": "Template path for published Scene playblast. Should"
                "correspond to a template defined in "
                "templates.yml.",
            },
            "Publish Type": {
                "type": "string",
                "default": None,
                "description": "The published file type to register.",
            },
            "supported_tasks": {
                "type": "list",
                "default": [],
                "description": "List of supported tasks.",
            },
            "supported_pipeline_steps": {
                "type": "list",
                "default": [],
                "description": "List of supported pipeline steps.",
            },
        }

        # update the base settings
        plugin_settings.update(maya_publish_settings)

        # self.parent.logger.info("plugin settings:\n{}".format(pf(plugin_settings)))

        return plugin_settings

    @property
    def item_filters(self):
        return ["maya.session.rigsOnTheFlyAbstraction"]

    # ----------------------------------------------------------------------------------
    # standard publish plugin methods

    def accept(self, settings, item):
        # item.properties["accepted"] = False

        # get the main scene
        scene_name = scene_path = item.properties.get("scene_path")
        if not scene_name:
            raise Exception("Please Save your file before Publishing")

        supported_pipeline_steps = settings.get("supported_pipeline_steps").value
        if self.parent.engine.context.step.get("name") not in supported_pipeline_steps:
            return {"accepted": False}

        supported_tasks = settings.get("supported_tasks").value
        if self.parent.engine.context.task.get("name") not in supported_tasks:
            return {"accepted": False}

        abstraction_dictionary = item.properties.get("abstraction_dictionary")
        if not abstraction_dictionary.get("data"):
            return {"accepted": False}
        # self.parent.logger.info(
        #     "abstraction_dictionary:\n{}".format(pf(abstraction_dictionary))
        # )

        return {"accepted": True}

    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish.

        Returns a boolean to indicate validity.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process

        :returns: True if item is valid, False otherwise.
        """

        publisher = self.parent
        scene_path = item.properties.get("scene_path")
        if not scene_path:
            return False

        work_template_name = settings.get("Work Template").value
        work_template = self.parent.engine.get_template_by_name(work_template_name)
        if not work_template:
            return False

        fields = work_template.get_fields(scene_path)
        publish_version = fields.get("version")

        publish_template_name = settings.get("Publish Template").value
        publish_template = self.parent.engine.get_template_by_name(
            publish_template_name
        )
        if not publish_template:
            return False
        publish_path = publish_template.apply_fields(fields)
        if not publish_path:
            return False
        # self.parent.logger.info(
        #     "rigs on the fly abstraction publish_path: {}".format(publish_path)
        # )

        publish_name = fields.get("name")
        if not publish_name:
            publish_name = os.path.basename(publish_path)

        item.properties["publish_version"] = publish_version
        item.properties["publish_name"] = publish_name
        item.properties["publish_path"] = publish_path
        item.properties["publish_fields"] = fields

        publishes = publisher.util.get_conflicting_publishes(
            item.context,
            publish_path,
            publish_name,
            filters=["sg_status_list", "is_not", None],
        )

        return True

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root

    def publish(self, settings, item):

        # create a pointer to the parent root item to store
        # any extra publishes apart form the primary one
        root_item = self.get_root_item(item)
        root_item.properties.setdefault("sg_publish_extra_data", [])
        self.parent.logger.info(
            "Storing extra publish data on root item: {}".format(root_item)
        )
        publish_extra_data = root_item.properties.get("sg_publish_extra_data")

        publish_version = item.properties.get("publish_version")
        publish_name = item.properties.get("publish_name")
        publish_path = item.properties.get("publish_path")
        fields = item.properties.get("publish_fields")

        scene_pub_template_name = settings.get("Primary Publish Template").value
        scene_pub_template = self.parent.engine.get_template_by_name(
            scene_pub_template_name
        )
        primary_publish_path = scene_pub_template.apply_fields(fields)

        self.parent.engine.ensure_folder_exists(os.path.dirname(publish_path))

        abstraction_dictionary = item.properties.get("abstraction_dictionary")
        with open(publish_path, "w") as publish_file:
            json.dump(abstraction_dictionary, publish_file, indent=4, sort_keys=True)

        publisher = self.parent

        publish_dependencies_ids = []
        if "sg_publish_data" in item.parent.properties:
            publish_dependencies_ids.append(
                item.parent.properties.sg_publish_data["id"]
            )

        dependencies = [primary_publish_path]

        sg_fields_to_update = {
            "sg_status_list": "rev",
            "task.Task.sg_status_list": "rev",
        }

        publish_data = {
            "tk": self.parent.engine.sgtk,
            "context": item.context,
            "comment": item.description,
            "path": publish_path,
            "name": publish_name,
            "version_number": publish_version,
            "thumbnail_path": item.get_thumbnail_as_path(),
            "task": self.parent.engine.context.task,
            "sg_fields": sg_fields_to_update,
            "published_file_type": settings.get("Publish Type").value,
            "dependency_paths": dependencies,
        }
        sg_publishes = sgtk.util.register_publish(**publish_data)

        # self.parent.logger.info(
        #     "Adding existing publish "
        #     + "(id:{0}) ".format(sg_publishes["id"])
        #     + "as extra data for item: {0}".format(item)
        # )

        publish_extra_data.append(sg_publishes)

    def finalize(self, settings, item):
        pass
