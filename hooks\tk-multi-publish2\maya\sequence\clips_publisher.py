# -*- coding: utf-8 -*-

# HOOK NOT IN USE, DISCARDED I COMMIT a1a893eae2bce7ca0e850135c2b3eb5ad01ca4a2

# Standard library:
import sys
import collections
import os
import re
import traceback
import subprocess
from pprint import pprint
import math
from decimal import Decimal, ROUND_HALF_UP
import pprint
import copy
# ___   ___   ___   ___   ___   ___  ___
# Third party:
import sgtk
import pymel.core as pm

# ___   ___   ___   ___   ___   ___  ___
# Project:
hook_baseclass = sgtk.get_hook_baseclass()
# ====================================================================
pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)
pf = pprint.pformat


def validate_required_settings(fn):
    list_of_required_settings = [
        'framerate',
        'clip_publish_template',
        'sequence_start_frame',
        'work_template',
        'primary_publish_template',
        'publish_type',
        'media_review_publish_template',
    ]

    def inner(self, settings, **kwargs):
        if all(key in settings.keys() for key in list_of_required_settings):
            return fn(self, settings, **kwargs)
        else:
            raise ValueError(
                'Missing settings for hook: {0}'.format(
                    '\n - '.join(list_of_required_settings)
                )
            )

    return inner


# =============================================================================

class ClipPublisherHook(hook_baseclass):

    def __init__(self, parent):
        super(ClipPublisherHook, self).__init__(parent)
        self.__register_external_modules_path()

    @property
    def name(self):
        return 'ClipPublisherHook'

    @property
    def media_review_file_type(self):
        return 'Media Review'

    def _frames_to_timecode(self, frames, framerate):
        h = int(frames / 86400)
        m = int(frames / 1440) % 60
        s = int((frames % 1440) / framerate)
        f = frames % 1440 % framerate
        return ("%02d:%02d:%02d:%02d" % (h, m, s, f))

    def __register_external_modules_path(self):
        path_to_hooks = re.match(r'^.*?hooks', self.disk_location).group()
        path_to_external_modules = os.path.join(
            path_to_hooks,
            'external_python_modules'
        )

        if path_to_external_modules not in sys.path:
            sys.path.insert(0, path_to_external_modules)

    def fields_from_scene(self, settings):
        primary_publish_template = self.parent.get_template_by_name(
            settings['work_template'].value
        )
        return primary_publish_template.get_fields(pm.sceneName())

    def _publish_name(self, PublishModel):
        publish_name = self._regex_replace(
            regex=r'_v\d{3}',
            source_string=os.path.basename(PublishModel.path_to_publish),
            replace_string=''
        )
        return publish_name

    def _regex_replace(self, regex, source_string, replace_string):
        result = ''
        pattern = re.compile(regex)
        result = pattern.sub(repl=replace_string, string=source_string)
        return result

    # __ __ __ __ __ __ __ __ __ __ __ __ __ __ __ __ __ __ __ __ __ __ __ __ __

    @validate_required_settings
    def publish_clips(self, settings, **kwargs):
        print("\n" + (">" * 120))
        pp('{0}.publish_clips'.format(self.name))
        pp(kwargs)
        if not kwargs.get('item'):
            raise MissingArgument('item')

        if not kwargs.get('path_to_source'):
            raise MissingArgument('path_to_source')
        if not os.path.isfile(kwargs['path_to_source']):
            raise ValueError('Argument <path_to_source> is not a file.')

        path_to_source = kwargs['path_to_source']
        item = kwargs['item']

        list_of_publish_models = (
            self.list_of_publish_models(settings, path_to_source)
        )

        dry_run = kwargs['dry_run']

        list_of_publishes = []

        if not dry_run:
            for model in list_of_publish_models:
                self._ffmpeg_command(model)
                p = self.publish_from_model(settings, item, model)
                list_of_publishes.append(p)

        return list_of_publishes

    # -    -    -    -    -    -    -    -    -    -
    def publish_from_model(self, settings, item, model):
        __publish = sgtk.util.register_publish(
            tk=self.parent.sgtk,
            context=self.parent.context,
            comment=item.description,
            path=model.path_to_publish,
            name=self._publish_name(model),
            version_number=model.fields['version'],
            thumbnail_path=item.get_thumbnail_as_path(),
            dependency_paths=[model.path_to_source],
            task=self.parent.context.task,
            published_file_type=settings['publish_type'].value,
            sg_fields={
                'sg_status_list': 'rev',
            },
        )

        proxy_tag = {
            'type': 'Tag',
            'id': 2588
        }

        code = os.path.basename(model.path_to_publish).replace('_', ' ')
        code = code.replace('.mov', '')
        frame_range = "{0}-{1}".format(
            int(model.edit.shot.start_frame),
            int(model.edit.shot.end_frame)
        )

        version = self.parent.shotgun.create('Version', {
            'sg_first_frame': int(model.edit.shot.start_frame),
            'sg_last_frame': int(model.edit.shot.end_frame),
            'description': "Automatically splitted clip from Sequence Review",
            'frame_range': frame_range,
            'frame_count': int(model.edit.shot.duration),
            'sg_path_to_movie': model.path_to_publish,
            'sg_path_to_frames': None,
            'published_files': [__publish],
            'code': code,
            'project': self.parent.context.project,
            'entity': self.parent.context.entity,
            'sg_task': self.parent.context.task,
            'sg_version_type': 'Reference',
            'tags': [proxy_tag]
        })

        return __publish

    def _ffmpeg_command(self, PublishModel):
        dir_to_publish = os.path.dirname(PublishModel.path_to_publish)
        if not os.path.exists(dir_to_publish):
            os.makedirs(dir_to_publish, )

        commands = [
            'ffmpeg',
            '-y',
            '-r', str(PublishModel.edit.framerate),
            '-i', PublishModel.path_to_source,
            '-ss', repr(PublishModel.edit.start),
            '-to', repr(PublishModel.edit.end),
            '-r', str(PublishModel.edit.framerate),
            PublishModel.path_to_publish
        ]

        process_clips = subprocess.Popen(
            commands, stdout=subprocess.PIPE, stderr=subprocess.PIPE
        )

        stdout, stderr = process_clips.communicate()

        print(stdout)
        print(stderr)

        #  -    -    -    -    -    -    -    -    -    -

    def list_of_publish_models(self, settings, path_to_source):
        # What's Model?
        # A Model in this context its been used as a data structure that
        # contains inmmutable data build with a namedtuple in order to avoid
        # common errors with dictionaries.

        PublishModel = collections.namedtuple(
            'PublishModel',
            [
                'path_to_publish',
                'path_to_source',
                'fields',
                'version',
                'edit'
            ]
        )
        list_of_edits = self._list_of_edit_models(settings)
        fields = self.fields_from_scene(settings)
        clips_template = self.parent.get_template_by_name(
            settings['clip_publish_template'].value
        )
        framerate = settings['framerate'].value
        list_of_publish_models = []
        for e in list_of_edits:
            f = copy.deepcopy(fields)
            f['maya.shotnode.name'] = e.shot.shotname
            list_of_publish_models.append(
                PublishModel(
                    path_to_publish=clips_template.apply_fields(f),
                    path_to_source=path_to_source,
                    fields=fields,
                    version=fields['version'],
                    edit=e
                )
            )
        return list_of_publish_models

        # -    -    -    -    -    -    -    -    -    -

    @validate_required_settings
    def _list_of_edit_models(self, settings, **kwargs):
        from timecode import Timecode

        Edits = collections.namedtuple(
            'Edits', 'start end shot framerate'
        )

        framerate = settings['framerate'].value
        sequence_start_frame = settings['sequence_start_frame'].value

        list_of_shots = self._list_of_shots_models()
        list_of_edits = []
        for s in list_of_shots:
            __start = s.start_frame - sequence_start_frame
            estart = (
                Timecode(
                    framerate,
                    self._frames_to_timecode(__start, framerate)
                )
            )
            estart.set_fractional(True)

            __end = s.end_frame - sequence_start_frame + 1
            eend = Timecode(
                framerate,
                self._frames_to_timecode(__end, framerate)
            )
            eend.set_fractional(True)

            list_of_edits.append(
                Edits(
                    framerate=framerate,
                    start=estart,
                    end=eend,
                    shot=s
                )
            )

        return list_of_edits

        #  -    -    -    -    -    -    -    -    -    -

    def _list_of_shots_models(self):
        Shot = collections.namedtuple(
            'Shot',
            'shotname start_frame end_frame duration'
        )
        return [
            Shot(
                shotname=s.shotName.get(),
                start_frame=s.sequenceStartFrame.get(),
                end_frame=s.sequenceEndFrame.get(),
                duration=s.sequenceEndFrame.get() - s.sequenceStartFrame.get()
            )
            for s in pm.ls(type='shot')
        ]


# ===============================================================================


class MissingArgument(Exception):
    pass
