########################################################################################
#
# Copyright (c) 2024 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio.
########################################################################################

import os
import re
import pprint

import sgtk

HookBaseClass = sgtk.get_hook_baseclass()


class HarmonyPegsCollector(HookBaseClass):
    """
    Collector that operates on the Toon Boom Harmony session. Should inherit
    from the basic collector hook.
    """

    @property
    def settings(self):
        """
        Dictionary defining the settings that this collector expects to receive
        through the settings parameter in the process_current_session and
        process_file methods.
        A dictionary on the following form::
        {
            "Settings Name": {
                "type": "settings_type",
                "default": "default_value",
                "description": "One line description of the setting"
        }
        The type string should be one of the data types that toolkit accepts as
        part of its environment configuration.
        """

        # grab any base class settings
        collector_settings = super(HarmonyPegsCollector, self).settings or {}

        # settings specific to this collector
        harmony_session_settings = {
            "Publish Template": {
                "type": "template",
                "default": None,
                "description": "Harmony session publish path template",
            },
            "Work Template": {
                "type": "template",
                "default": None,
                "description": "Hamrony session work template",
            },
            "Publish Peg Template": {
                "type": "template",
                "default": None,
                "description": "Template path where the files will be published",
            },
            "peg_attr_name": {
                "type": "string",
                "default": None,
                "description": "Peg attribute name",
            },
            "peg_attr_type": {
                "type": "string",
                "default": None,
                "description": "Peg attribute type",
            },
        }

        # update the base settings with these settings
        collector_settings.update(harmony_session_settings)

        return collector_settings

    def process_current_session(self, settings, parent_item):
        """
        Analyzes the current session open in Toon Boom Harmony and parents a
        subtree of items under the parent_item passed in.
        :param dict settings: Configured settings for this collector
        :param parent_item: Root item instance
        """

        super(HarmonyPegsCollector, self).process_current_session(
            settings, parent_item
        )

        item = next((i for i in parent_item.descendants if i.type_spec.endswith('.session')), None)
        if item is None:
            item = self.collect_current_harmony_session(settings, parent_item)

        tracker_pegs_item = self._collect_tracker_pegs(settings, item)
        if not tracker_pegs_item:
            self.parent.logger.info("No pegs item has been created.")

    def _collect_tracker_pegs(self, settings, parent_item):

        self.parent.logger.info("Collecting tracker pegs...")

        ctx = self.parent.context
        task = ctx.task

        # load overrides framework
        overrides_framework = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides", None
        ) or self.load_framework("mty-framework-valueoverrides")

        shot_tracker_pegs_list = self._get_shot_tracker_pegs(settings)

        # skip the rest of the collector if there are no tracker peg nodes
        if not shot_tracker_pegs_list:
            return None

        self.parent.logger.info(f"Got shot tracker pegs: {shot_tracker_pegs_list}")

        # camera collector and export camera -------------------------------------------
        default_value_tracker_pegs_collector = "mty.publisher.harmony_shot_tracker_pegs_collector"
        default_value_skip_tracker_pegs_export = "mty.publisher.harmony.export_harmony_tracker_pegs"

        override_value_tracker_pegs_collector = overrides_framework.get_value(
            default_value_tracker_pegs_collector, link=task
        )
        self.parent.logger.info(
            "override value for '{}': {}".format(
                default_value_tracker_pegs_collector, override_value_tracker_pegs_collector
            )
        )

        link_project = {"type": "Project", "id": self.parent.context.project["id"]}
        override_value_tracker_pegs_export = overrides_framework.get_value(
            default_value_skip_tracker_pegs_export, link=link_project
        )
        self.parent.logger.info(
            "override value for '{}': {}".format(
                default_value_skip_tracker_pegs_export, override_value_tracker_pegs_export
            )
        )

        # skip if both overrides (or default values) are set to False
        if (
            not override_value_tracker_pegs_collector
            and not override_value_tracker_pegs_export
        ):
            self.parent.logger.info(
                (
                    "Override value exist for tracker pegs collector and tracker "
                    "pegs export. Skipping tracker pegs collector"
                )
            )
            return None

        # continue with the tracker pegs collector if the the tracker pegs collector
        # override (or # default value) is set to True, usually this is set at
        # project level.
        if override_value_tracker_pegs_collector:
            self.parent.logger.info(
                "Override value doesn't exists. Continuing with tracker pegs collector"
            )

        config_path = os.path.dirname(os.path.dirname(self.disk_location))

        engine = self.parent.engine
        # Harmony Work Session path
        current_project_path = engine.app.get_current_project_path()
        publisher = self.parent
        template_by_name = engine.get_template_by_name

        # get templates from settings
        work_template_setting = settings.get("Work Template").value
        publish_session_setting = settings.get("Publish Template").value

        publish_peg_template_setting = settings.get("Publish Peg Template").value

        # Templates
        work_template = template_by_name(work_template_setting)
        publish_session_template = template_by_name(publish_session_setting)
        publish_peg_template = template_by_name(publish_peg_template_setting)
        display_name = "Harmony Shot Tracker Peg File"

        # Adding extra element in work_fields
        work_fields = work_template.get_fields(current_project_path)

        tracker_peg_items = []
        for peg in shot_tracker_pegs_list:
            # tracker_basename = os.path.basename(peg).split("-")[0]
            tracker_basename = self.reformat_tracker_name(os.path.basename(peg))
            work_fields["tracker_name"] = tracker_basename
            publish_peg_path = publish_peg_template.apply_fields(work_fields)
            self.parent.logger.info("publish_peg_path: {}".format(publish_peg_path))

            tracker_peg_item = parent_item.create_item(
                "harmony.tracker_peg",
                "Collects shot tracker pegs in scene",
                f"{tracker_basename} {display_name}",
            )
            self.parent.logger.info("created peg item")

            publish_icons = os.path.join(config_path, "tk-multi-publish2", "icons")
            icon_path = os.path.join(publish_icons, "tracker_publish.png")
            tracker_peg_item.set_icon_from_path(icon_path)

            tracker_peg_item.properties["work_fields"] = work_fields
            tracker_peg_item.properties["template_session_publish"] = publish_session_template
            tracker_peg_item.properties["publish_path"] = publish_peg_path
            tracker_peg_item.properties["peg_node_path"] = peg

            # Sub template for every plugin item
            tracker_peg_item.properties["template_peg_publish"] = publish_peg_template

            tracker_peg_item.properties["found_tracker_pegs"] = shot_tracker_pegs_list

            tracker_peg_item.properties["enabled"] = False

            self.parent.logger.info(f"peg item name: {tracker_peg_item.name}")
            self.parent.logger.info(
                f"peg item properties:\n{pprint.pformat(tracker_peg_item.properties.to_dict())}"
            )

            tracker_peg_items.append(tracker_peg_item)

        return tracker_peg_items

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root

    def _get_shot_tracker_pegs(self, settings):

        peg_attr_name = settings.get("peg_attr_name").value
        peg_attr_type = settings.get("peg_attr_type").value

        context = self.parent.engine.context

        # if context.entity["type"] == "Shot":
        #     shot_name = context.entity["name"]
        #     camera_node_name = "{}-Camera".format(shot_name)
        # else:
        #     camera_node_name = None

        self.parent.engine.logger.debug("context:\n{}".format(pprint.pformat(context)))
        self.parent.logger.info("peg_attr_name: {}".format(peg_attr_name))
        self.parent.logger.info("peg_attr_type: {}".format(peg_attr_type))
        # self.parent.logger.info("camera_node_name: {}".format(camera_node_name))

        collect_shot_tracker_pegs_list_cmd = """
var log = MessageLog.trace;

function collect_shot_tracker_pegs()
{
    // Get the camera nodes by attr
    var array_of_node_types = ["PEG"];
    var array_of_nodes = node.getNodes(array_of_node_types);
    // log(JSON.stringify(array_of_nodes, null, 4));

    var array_of_shot_tracker_pegs = [];
    for (var i = 0; i < array_of_nodes.length; ++i)
    {
        var node_path = array_of_nodes[i];
        log("checking node: " + node_path);
        // var attr = node.getAttr(node_path, 1.0, "mtyShotTracker");
        var attr = node.getAttr(node_path, 1.0, "%s");
        // log(attr.name());
        // log(attr.typeName());
        // log(attr.boolValue());
        if (attr != null)
        {
            // if (attr.keyword() != "" && attr.typeName() == "BOOL")
            if (attr.keyword() != "" && attr.typeName() == "%s")
            {
                if (attr.boolValue() == true)
                {
                    log("-- Found shot tracker peg: " + node_path);
                    array_of_shot_tracker_pegs.push(node_path);
                };
            };
        };
    };
    return array_of_shot_tracker_pegs;
};

collect_shot_tracker_pegs();
// var shot_tracker_pegs = collect_shot_tracker_pegs();
// log(JSON.stringify(shot_tracker_pegs, null, 4));

""" % (
    peg_attr_name,
    peg_attr_type,
)

        self.parent.engine.logger.debug(
            f"collect_shot_tracker_pegs_list_cmd:\n{collect_shot_tracker_pegs_list_cmd}"
        )
        shot_tracker_pegs_list = self.parent.engine.app.execute(
            collect_shot_tracker_pegs_list_cmd
        )
        self.parent.engine.logger.debug(
            f"shot_tracker_pegss_list: {shot_tracker_pegs_list}"
        )

        return shot_tracker_pegs_list

    def reformat_tracker_name(self, tracker_name):
        # if the name starts with "P-" remove it
        if tracker_name.startswith("P-"):
            tracker_name = tracker_name[2:]

        # split the resulting string whener a character [ -_] is found
        tracker_name_parts = re.split(r"[-_]", tracker_name)

        # iterate over the splitted elements and for all of them, except the first
        # one, capitalize the first letter
        for i, part in enumerate(tracker_name_parts):
            if i != 0:
                tracker_name_parts[i] = part[0].upper() + part[1:]

        # the resulting string is returned
        reformated_tracker_name = "".join(tracker_name_parts)

        return reformated_tracker_name
