########################################################################################
#
# Copyright (c) 2023 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty Animation Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
########################################################################################

import os
import sgtk
import subprocess

HookBaseClass = sgtk.get_hook_baseclass()


class PlaySounds(HookBaseClass):
    """Class to play sounds using ffplay. Usage: the class is already initialized in
    the engine_init hook, so you should call the desired method from the initialized
    class instance"""

    def play_sound(self, sound="success"):
        """
        Plays a sound file using ffplay

        Args:
            logger: engine logger
            sound (str): basename without extension of the sound file.
                The default sound  is the 'success' sound, but also 'error' and
                'download' could be valid options. More sounds can be added to
                {config}/assets/sounds. The only requirements are: no spaces in the
                name, and file must be an mp3
        """

        ffmpeg = self.parent.custom_frameworks.get(
            "mty-framework-ffmpeg"
        ) or self.load_framework("mty-framework-ffmpeg")

        FFmpegCoreTools = ffmpeg.ffmpegCore
        FFmpegCoreTools.set_binary("play")
        ffplay_path = FFmpegCoreTools.get_bin_path()

        tk = self.parent.tank
        config_location = tk.configuration_descriptor.get_path()

        sound_basename = "{}.mp3".format(sound)
        path_to_sound = os.path.join(
            config_location, "assets", "sounds", sound_basename
        )

        self.parent.logger.debug("ffplay_path: {}".format(ffplay_path))
        self.parent.logger.debug("path_to_sound: {}".format(path_to_sound))

        if os.path.exists(path_to_sound):
            subprocess.call(
                [ffplay_path, "-autoexit", "-nodisp", path_to_sound]
            )
        else:
            self.parent.logger.error(
                "File doesn't exist on disk: {}".format(path_to_sound)
            )

    def success_sound(self):
        self.play_sound(sound="success")

    def error_sound(self):
        self.play_sound(sound="error")

    def download_sound(self):
        self.play_sound(sound="download")

    def confirm_sound(self):
        self.play_sound(sound="confirm")

    def dual_confirm_sound(self):
        self.play_sound(sound="dual_confirm")

    def desktop_init_sound(self):
        self.play_sound(sound="desktop_init")
