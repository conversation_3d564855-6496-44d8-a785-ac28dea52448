import os
import re
import six
import pprint
import fileseq
import traceback
import subprocess
from imp import reload
from tank.util import sgre as re

import sgtk

HookBaseClass = sgtk.get_hook_baseclass()

class ResolutionFromPathInfo(HookBaseClass):
    """
    Methods for basic file path parsing.
    """

    def __init__(self, parent):
        super(ResolutionFromPathInfo, self).__init__(parent)

    def is_file_sequence(self, path):
        """
        Return True if the path is a file sequence
        """
        pattern = re.compile(
            r"(?P<head>.+)"
            r"(?P<printf>%\d{2}d)"
            r"(?P<tail>.+)"
        )

        match = re.match(pattern, path)
        if match:
            return True
        else:
            return False

    def get_first_sequence_frame(self, path):
        """
        Return the first frame of the first sequence in the path
        """
        first_seq_frame = None

        seq = fileseq.findSequenceOnDisk(path)
        if seq:
            first_seq_frame = list(seq)[0]

        return first_seq_frame


    def media_resolution_value(self, path):

        """ this method return the resolution of media, only need the path
        retun type(str): "{width}x{height}"
        """
        media_resolution = None

        is_sequence = self.is_file_sequence(path)
        if is_sequence:
            path = self.get_first_sequence_frame(path)

        mediatools = self.load_framework("mty-framework-mediatools")
        imagemagick = self.load_framework("mty-framework-imagemagick")

        if not path:
            return None

        # try to get media resolution using mediatools framework
        try:
            self.parent.engine.logger.info(
                "Trying to get media resolution using mediatools framework"
            )
            width, height = mediatools.Describer.get_media_resolution(path)
            media_resolution = "{}x{}".format(width,height)
        except Exception as e:
            self.parent.engine.log_error(
                (
                    "Failed to get media resolution using mediatools framework:"
                    "\n{}\nFull traceback:\n{}"
                ).format(e, traceback.format_exc())
            )

            # try to get media resolution using mediatools framework
            self.parent.engine.logger.info(
                "Trying to get media resolution using imagemagick framework"
            )
            imagemagick.imageMagickCore.set_binary("identify")
            imagemagick_bin_path = imagemagick.imageMagickCore.get_bin_path()

            if not imagemagick_bin_path:
                error_msg = (
                    "Couldn't get imagemagick identify path from mty-framework-"
                    "imagemagick while trying to validate image resolution."
                )
                self.parent.engine.logger.error(error_msg)
                return None

            imagemagick_cmd = [
                imagemagick_bin_path,
                "-format",
                "%wx%h",
                path,
            ]
            imagemagick_cmd_str = " ".join(imagemagick_cmd)

            extract = subprocess.Popen(
                imagemagick_cmd_str,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                shell=True
            )

            stdout, stderr = extract.communicate()
            if hasattr(six, "ensure_str"):
                stdout = six.ensure_str(stdout)
                stderr = six.ensure_str(stderr)
            else:
                stdout = self.ensure_str(stdout)
                stderr = self.ensure_str(stderr)

            if stderr:
                src_width = None
                src_height = None
                msg = "Couldn't retrieve source image width or height from {}.\nError: {}\ncmd: {}".format(
                    path, stderr, " ".join(imagemagick_cmd)
                )
                msg = "{}{}".format(
                    msg, "\nSource image resolution set to None"
                )
                self.parent.engine.logger.error(msg)

                return None

            res_split = stdout.split("x")
            src_width = int(res_split[0])
            src_height = int(res_split[1])

            media_resolution = "{}x{}".format(src_width,src_height)

        self.parent.engine.logger.info("media_resolution: {}".format(media_resolution))

        return media_resolution

    def ensure_str(self,s, encoding="utf-8", errors="strict"):
        """Coerce *s* to `str`.

        For Python 2:
            - `unicode` -> encoded to `str`
            - `str` -> `str`

        For Python 3:
            - `str` -> `str`
            - `bytes` -> decoded to `str`
        """

        # SG's tk-framework-adobe includes six, but it's missing this method. This is a
        # workaround when working with Adobe apps (PS and AFX tested)

        # Optimization: Fast return for the common case.
        if type(s) is str:
            return s
        if six.PY2 and isinstance(s, six.text_type):
            return s.encode(encoding, errors)
        elif six.PY3 and isinstance(s, six.binary_type):
            return s.decode(encoding, errors)
        elif not isinstance(s, (six.text_type, six.binary_type)):
            raise TypeError("not expecting type '%s'" % type(s))
        return s
