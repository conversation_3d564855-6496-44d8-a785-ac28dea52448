#####################################################################################
#
# Copyright (c) 2021 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################
import os
import json
import pprint
import shutil

import six

import sgtk
from sgtk.util.filesystem import ensure_folder_exists
from tank.platform.qt import QtCore, QtGui


HookBaseClass = sgtk.get_hook_baseclass()

pp = pprint.pprint
pf = pprint.pformat


class HarmonyTPLPublishPlugin(HookBaseClass):
    """
    Plugin for publishing an open Harmony session.
    This hook relies on functionality found in the base file publisher hook in
    the publish2 app and should inherit from it in the configuration. The hook
    setting for this plugin should look something like this::
        hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py"
    """

    # NOTE: The plugin icon and name are defined by the base file plugin.
    @property
    def icon(self):
        """
        Path to an png icon on disk
        """
        # look for icon one level up from this hook's folder in "icons" folder
        return os.path.join(self.disk_location, os.pardir, "icons", "nodes.png")

    @property
    def description(self):
        """
        Verbose, multi-line description of what the plugin does. This can
        contain simple html for formatting.
        """

        loader_url = "https://support.shotgunsoftware.com/hc/en-us/articles/219033078"

        return """
        Publishes the file to Shotgun. A <b>Publish</b> entry will be
        created in Shotgun which will include a reference to the file's current
        path on disk. If a publish template is configured, a copy of the
        current session will be copied to the publish template path which
        will be the file that is published. Other users will be able to access
        the published file via the <b><a href='%s'>Loader</a></b> so long as
        they have access to the file's location on disk.
        If the session has not been saved, validation will fail and a button
        will be provided in the logging output to save the file.
        <h3>File versioning</h3>
        If the filename contains a version number, the process will bump the
        file to the next version after publishing.
        The <code>version</code> field of the resulting <b>Publish</b> in
        Shotgun will also reflect the version number identified in the filename
        The basic worklfow recognizes the following version formats by default:
        <ul>
        <li><code>filename.v###.ext</code></li>
        <li><code>filename_v###.ext</code></li>
        <li><code>filename-v###.ext</code></li>
        </ul>
        After publishing, if a version number is detected in the work file, the
        work file will automatically be saved to the next incremental version
        number. For example, <code>filename.v001.ext</code> will be published
        and copied to <code>filename.v002.ext</code>
        If the next incremental version of the file already exists on disk, the
        validation step will produce a warning, and a button will be provided
        in the logging output which will allow saving the session to the next
        available version number prior to publishing.
        <br><br><i>NOTE: any amount of version number padding is supported. for
        non-template based workflows.</i>
        <h3>Overwriting an existing publish</h3>
        In non-template workflows, a file can be published multiple times,
        however only the most recent publish will be available to other users.
        Warnings will be provided during validation if there are previous
        publishes.
        """ % (
            loader_url,
        )

    @property
    def item_filters(self):
        """
        List of item types that this plugin is interested in.
        Only items matching entries in this list will be presented to the
        accept() method. Strings can contain glob patters such as *, for
        example ["harmony.*", "file.harmony"]
        """
        return ["harmony.tpl"]

    @property
    def settings(self):
        """
        Dictionary defining the settings that this plugin expects to receive
        through the settings parameter in the accept, validate, publish and
        finalize methods.
        A dictionary on the following form::
            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }
        The type string should be one of the data types that toolkit accepts as
        part of its environment configuration.
        """

        # inherit the settings from the base publish plugin
        base_settings = super(HarmonyTPLPublishPlugin, self).settings or {}

        # settings specific to this class
        tpl_publish_settings = {
            "Ignored TPL Palettes": {
                "type": "list",
                "default": None,
                "description": "List of color palette names that will be ignored by the validation process.",
            },
        }

        # update the base settings
        base_settings.update(tpl_publish_settings)

        return base_settings

    def accept(self, settings, item):
        return {"accepted": True, "checked": True}

    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish. Returns a
        boolean to indicate validity.
        :param settings: Dictionary of Settings. The keys are strings, matching
                         the keys returned in the settings property. The values
                         are `Setting` instances.
        :param item: Item to process
        :returns: True if item is valid, False otherwise.
        """

        ctx = self.parent.context
        self.engine = self.parent.engine
        # publisher = self.parent
        path = _session_path()

        if not path:
            # the session still requires saving. provide a save button.
            # validation fails.
            error_msg = "The Harmony session has not been saved."
            raise Exception(error_msg)

        # asset_id = ctx.entity["id"]
        # asset_name = ctx.entity["name"]
        path = sgtk.util.ShotgunPath.normalize(path)

        # validating templates
        if "template_tpl_publish" not in item.properties.keys():
            raise Exception("Missing Harmony tpl templates, publish and work.")

        # Checking work area info
        if "work_fields" not in item.properties.keys():
            raise Exception("Invalid work area info. Check collector")
        elif not len(item.properties["work_fields"]):
            raise Exception("Invalid work area info.")

        asset_name = self.engine.context.entity["name"]
        tpl_basename = "{}.tpl".format(asset_name)
        self.parent.engine.logger.info("Expected tpl name: {}".format(tpl_basename))

        # Validates if a tpl exists on the pipelineLibrary
        tpl_validate_exist = item.properties["TPL_validate_exist"]
        if not tpl_validate_exist:
            msg = (
                "The expected TPL does not exist in the pipelineLibrary folder.\n"
                "Please add a TPL to the 'Harmony Premium Library > pipelineLibrary' "
                "to be able to publish it."
                "\n\nThe correct name for the TPL is:    {}"
            ).format(tpl_basename)
            # self.parent.engine.show_message(msg)
            self.show_message("TPL not found", msg, "Critical")

            raise Exception(msg)

        # Template
        directory = item.properties["TPL_directory"]
        tpl_list_directory = os.listdir(directory)

        tpl_path = None
        tpl_exist = False
        for file_ in tpl_list_directory:
            if (
                file_.lower() == tpl_basename.lower()
                and os.path.isdir(os.path.join(directory, file_))
            ):
                tpl_path = os.path.join(directory, file_)
                tpl_exist = True

                break

        self.parent.engine.logger.info(
            "Does the expected tpl exist in the pipeline library?: {}".format(tpl_exist)
        )

        if not tpl_exist or not tpl_path:
            msg = (
                "The expected TPL does not exist in the pipelineLibrary folder.\n"
                "Please add a TPL to the 'Harmony Premium Library > pipelineLibrary' "
                "to be able to publish it."
                "\n\nThe correct name for the TPL is:    {}"
            ).format(tpl_basename)

            # self.parent.engine.show_message(msg)
            self.parent.engine.logger.error(msg)
            self.show_message("TPL not found", msg, icon="Critical")

            raise Exception(msg)

        self.parent.engine.logger.info(
            "About to validate color palettes in TPL directory: {}".format(tpl_path)
        )
        color_palette_validation_msg = self.validate_color_palettes(tpl_path, settings)
        if color_palette_validation_msg:
            self.show_message(
                "Invalid color palette(s)", color_palette_validation_msg, icon="Critical"
            )
            raise Exception(color_palette_validation_msg)

        return True

    def publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.
        :param settings: Dictionary of Settings. The keys are strings, matching
                         the keys returned in the settings property.
                         The values are `Setting` instances.
        :param item: Item to process
        """
        self.logger.info("Publishing TPL Files")
        self.parent.log_info("Publishing TPL Files.")

        # get the path in a normalized state. no trailing separator, separators
        # are appropriate for current os, no double separators, etc.
        path = sgtk.util.ShotgunPath.normalize(_session_path())
        engine = sgtk.platform.current_engine()
        ctx = self.parent.context
        publisher = self.parent

        asset_name = engine.context.entity["name"]
        tpl_basename = "{}.tpl".format(asset_name)

        # Get item properties -------------------------------------
        publish_session_template = item.properties["template_session_publish"]
        work_tpl_templates = item.properties["template_tpl_work"]
        publish_tpl_template = item.properties["template_tpl_publish"]
        work_fields = item.properties["work_fields"]
        tpl_library_path = item.properties["TPL_directory"]

        # Ensure folder exist
        tpl_publish_path = publish_tpl_template.apply_fields(work_fields)
        publish_path = publish_session_template.apply_fields(work_fields)
        publish_tpl_directory = os.path.dirname(tpl_publish_path)

        if not os.path.exists(publish_tpl_directory):
            self.parent.engine.ensure_folder_exists(publish_tpl_directory)

        # Move TPL FILES  to publish area
        tpl_files_ls = os.listdir(tpl_library_path)

        for tpl in tpl_files_ls:
            if tpl.lower() == tpl_basename.lower():
                name = os.path.splitext(tpl)[0]

        work_fields["tokenName"] = name
        # work_fields["name"]= name
        # tpl_work_path= work_tpl_templates.apply_fields(work_fields)
        tpl_work_path = os.path.join(tpl_library_path, name + ".tpl")
        tpl_publish_path = publish_tpl_template.apply_fields(work_fields)
        shutil.move(tpl_work_path, tpl_publish_path)

        # dependency Path
        publish_session_path = publish_session_template.apply_fields(work_fields)

        asset_id = ctx.entity["id"]
        asset_name = ctx.entity["name"]
        scene_name = work_fields.get("name", "")
        # ver_str   = 'v{}'.format(str(work_fields['version']).zfill(3))
        # item_name = '_'.join([asset_name, ver_str])
        if scene_name and scene_name.lower() not in ["scene", "master", "rig"]:
            item_name = f"{asset_name}_{scene_name}_tpl"
        else:
            item_name = f"{asset_name}_tpl"

        publish_data = {
            "tk": publisher.sgtk,
            "context": item.context,
            "comment": item.description,
            "path": tpl_publish_path,
            "name": item_name,
            "created_by": self.get_publish_user(settings, item),
            "version_number": work_fields["version"],
            "thumbnail_path": item.get_thumbnail_as_path(),
            "published_file_type": "Harmony TPL",
            "dependency_paths": [publish_path],
            "dependency_ids": [],
            "sg_fields": {"sg_status_list": "rev"},
        }

        # Register the tpl file, so that we can have the pointer
        item.properties.sg_publish_data = sgtk.util.register_publish(**publish_data)
        item.properties.sg_publish_path = item.properties.sg_publish_data["path"][
            "local_path"
        ]

        # finally just store the publish data for later retrieval
        # and upload to the host storage location
        root_item = self.get_root_item(item)
        root_item.properties.setdefault("sg_publish_extra_data", [])

        self.parent.log_debug("Storing extra publish data on root item: %s" % root_item)
        publish_extra_data = root_item.properties["sg_publish_extra_data"]

        self.parent.log_debug(
            "Already %s elements in extra publish data" % len(publish_extra_data)
        )

        # Update task status for revision
        sg_task = self.parent.context.task
        self.parent.logger.info(
            "End of Publish and updating task with id: {} to status: 'rev'".format(
                str(sg_task["id"])
            )
        )

        try:
            self.parent.engine.shotgun.update(
                "Task", sg_task["id"], {"sg_status_list": "rev"}
            )
        except:
            pass

    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed, and can for example be used to version up files.
        :param settings: Dictionary of Settings. The keys are strings, matching
                         the keys returned in the settings property.
                         The values are `Setting` instances.
        :param item: Item to process
        """

        # do the base class finalization
        # super(HarmonySessionPublishPlugin, self).finalize(settings, item)
        self.logger.debug("Item sequence successfully published")
        # bump the session file to the next version
        # self._save_to_next_version(item.properties["path"], item, _save_session)

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item
        return root

    def validate_color_palettes(self, tpl_path, settings):
        asset_name = self.engine.context.entity["name"]
        self.parent.engine.logger.info("Validating color palettes...")
        self.parent.engine.logger.info("asset name: {}".format(asset_name))

        palette_dir_path = os.path.join(tpl_path, "palette-library")
        palette_dir_path = self.fix_path(palette_dir_path)
        palette_files = os.listdir(palette_dir_path)

        # First we try to get the list of ignored color palettes from the  value
        # overrides framework, or its default value. These palettes will not be
        # validated
        # load overrides framework -----------------------------------------------------
        overrides_framework = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        ) or self.load_framework("mty-framework-valueoverrides")
        # ------------------------------------------------------------------------------
        default_value = "mty.publisher.harmony.ignored_tpl_palettes"
        link = self.parent.context.task or self.parent.context.project or {}
        override_ignored_palettes = overrides_framework.get_value(
            default_value, link=link
        )

        if override_ignored_palettes:
            ignore_palettes_validation_list = json.loads(override_ignored_palettes)
            self.parent.engine.logger.info("Got ignored palettes list from overrides")
        else:
            ignore_palettes_validation_list = []

        # In case there's no default value or override, we get the list from the
        # settings as fallback
        if not ignore_palettes_validation_list:
            ignore_palettes_validation_list = settings.get("Ignored TPL Palettes").value
            self.parent.engine.logger.info("Got ignored palettes list from settings")

        self.parent.engine.logger.info(
            "ignore_palettes_validation_list: {}".format(
                ignore_palettes_validation_list
            )
        )

        asset_palette_name = "{0}_color_palette.plt".format(asset_name.replace(" ", ""))
        asset_palette_prefix = "{0}_color_palette".format(asset_name.replace(" ", ""))

        template_palette = False
        asset_palettes = []
        invalid_palette_list = []

        template_palette_default = """
ToonBoomAnimationInc PaletteFile 2
Solid    Black                      0x09e1298841d01055   0   0   0 255
Solid    White                      0x09e1298841d01057 255 255 255 255
Solid    Red                        0x09e1298841d01059 255   0   0 255
Solid    Green                      0x09e1298841d0105b   0 255   0 255
Solid    Blue                       0x09e1298841d0105d   0   0 255 255
Solid    "Vectorized Line"          0x0000000000000003   0   0   0 255
"""
        msg = ""
        for palette in palette_files:
            if palette.lower().endswith(".plt"):
                palette_name = palette.replace(" ", "")
                self.parent.engine.logger.info("----------")
                self.parent.engine.logger.info("Checking palette: {}".format(palette))
                self.parent.engine.logger.info("palette_name: {}".format(palette_name))
                self.parent.engine.logger.info(
                    "palette full path: {}".format(
                        os.path.join(palette_dir_path, palette_name)
                    )
                )
                self.parent.engine.logger.info(
                    "asset_palette_prefix: {}".format(asset_palette_prefix)
                )

                if palette == "template.plt":
                    template_palette_path = os.path.join(
                        tpl_path, "palette-library", palette
                    )
                    template_palette_path = self.fix_path(template_palette_path)

                    f = open(template_palette_path, "r")
                    template_palette = f.read()
                    f.close()
                    if template_palette == template_palette_default:
                        template_palette = True
                        self.parent.engine.logger.info(
                            "Found unmodified template palette: {}".format(palette)
                        )

                # elif asset_palette_name == palette_name:
                elif palette_name.startswith(asset_palette_prefix):
                    asset_palettes.append(palette)
                    self.parent.engine.logger.info(
                        "Found asset palette: {}".format(palette)
                    )

                elif (
                    "texture" not in palette
                    and palette not in ignore_palettes_validation_list
                ):
                    invalid_palette_list.append(palette)
                    self.parent.engine.logger.error(
                        "Found invalid palette: {}".format(palette)
                    )
            else:
                pass
                # palette_files.remove(palette)

        if not template_palette:
            msg = (
                "The Template palette (template.plt) "
                "in the TPL has been removed or modified"
            )
            return msg

        if not asset_palettes:
            msg = (
                "{0} doesn't have its own color palette. "
                "The palette name must be: {1}, and additional palettes are supported "
                "if the share the same prefix: {1}_somethingElse"
            ).format(asset_name, asset_palette_name)

            return msg
        else:
            self.parent.engine.logger.info(
                "Found the following asset palettes:\n{}".format(pf(asset_palettes))
            )

        if invalid_palette_list:
            msg = (
                "The TPL has the following invalid palettes: {0}, "
                "please remove them (ideally from both, the TPL/palette-library and "
                "the current_scene/palette-library folders) in order to continiue."
            ).format(invalid_palette_list)
            return msg

        return msg

    def show_message(self, title, msg, icon=None):
        """
        Shows a message box with the input message
        """

        icons_dict = {
            "NoIcon": QtGui.QMessageBox.NoIcon,
            "Question": QtGui.QMessageBox.Question,
            "Information": QtGui.QMessageBox.Information,
            "Warning": QtGui.QMessageBox.Warning,
            "Critical": QtGui.QMessageBox.Critical,
        }

        if not icon or icon not in icons_dict.keys():
            icon_selection = icons_dict["NoIcon"]
        else:
            icon_selection = icons_dict[icon]

        msg_error = QtGui.QMessageBox()
        msg_error.setWindowTitle(title)
        msg_error.setText(msg)
        msg_error.setIcon(icon_selection)
        msg_error.raise_()
        msg_error.activateWindow()
        msg_error.exec_()

    def fix_path(self, path):
        """Replace all backward slashes with forward slashes."""

        path = path.replace("\\\\", "/")
        path = path.replace("\\", "/")

        return path


def _session_path():
    """
    Return the path to the current session
    :return:
    """
    engine = sgtk.platform.current_engine()

    # get the path to the current file
    path = engine.app.get_current_project_path()

    path = six.ensure_str(path)

    return path


# def _save_as():
#    engine = sgtk.platform.current_engine()
#    engine.app.save_new_version_action()
