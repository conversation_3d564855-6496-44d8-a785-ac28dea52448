# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

# location descriptors for engines used in this configuration

################################################################################

# 3dsMax
engines.tk-3dsmax.location:
  type: app_store
  name: tk-3dsmax
  version: v1.4.1

# After Effects
engines.tk-aftereffects.location:
  type: app_store
  name: tk-aftereffects
  version: v0.4.5

# Alias
engines.tk-alias.location:
  type: app_store
  name: tk-alias
  version: v4.1.5

# # Desktop fork
# engines.tk-desktop.location:
#   type: github_release
#   organization: mightyanimation
#   repository: tk-desktop
#   version: v2.7.5
#   private: true

# Desktop
engines.tk-desktop.location:
  type: app_store
  name: tk-desktop
  version: v2.7.8

# Desktop2
engines.tk-desktop2.location:
  type: app_store
  name: tk-desktop2
  version: v1.6.0

# Flame
engines.tk-flame.location:
  type: app_store
  name: tk-flame
  version: v1.19.5

# Houdini
engines.tk-houdini.location:
  type: app_store
  name: tk-houdini
  version: v1.9.3

# Mari
engines.tk-mari.location:
  type: app_store
  name: tk-mari
  version: v1.4.1

# Maya
engines.tk-maya.location:
  type: app_store
  name: tk-maya
  version: v0.13.3

# Motion Builder
engines.tk-motionbuilder.location:
  type: app_store
  name: tk-motionbuilder
  version: v0.8.2

# Nuke
engines.tk-nuke.location:
  type: app_store
  name: tk-nuke
  version: v0.15.4

# Photoshop
engines.tk-photoshopcc.location:
  type: app_store
  name: tk-photoshopcc
  version: v1.11.5

# Shell
engines.tk-shell.location:
  type: app_store
  name: tk-shell
  version: v0.10.1

# Shotgun
engines.tk-shotgun.location:
  type: app_store
  name: tk-shotgun
  version: v0.11.1

# VRED
engines.tk-vred.location:
  type: app_store
  name: tk-vred
  version: v3.5.0

# Toon Boom Harmony
engines.tk-harmony.location:
  type: github_release
  organization: mightyanimation
  repository: tk-harmony
  version: v2.1.1-0
  private: true
  # type: dev
  # path: Q:\tk-harmony

# Fusion
engines.tk-fusion.location:
  type: github_release
  organization: mightyanimation
  repository: tk-fusion
  version: v1.1.0-9
  private: true
  # type: dev
  # path: Q:\tk-fusion

# Krita
engines.tk-krita.location:
  # type: git
  # path: https://github.com/diegogarciahuerta/tk-krita.git
  # version: v1.0.3
  type: github_release
  organization: mightyanimation
  repository: tk-krita
  version: v1.0.4
  private: true
  # type: dev
  # path: Q:\tk-krita

# engines.tk-blender.location:
#   type: git
#   path: https://github.com/diegogarciahuerta/tk-blender.git
#   version: v1.1.1

engines.tk-blender.location:
  type: git
  # branch: master
  path: https://github.com/icentric-dev/tk-blender.git
  version: v2.0.0

# Substance Painter
engines.tk-substancepainter.location:
  type: git
  path: https://github.com/diegogarciahuerta/tk-substancepainter.git
  version: v1.2.2
  # type: github_release
  # organization: diegogarciahuerta
  # repository: tk-substancepainter
  # version: v1.2.2
  # private: false

# Adobe Premiere CC
engines.tk-premiere.location:
  type: git
  path: https://github.com/Vintata/tk-premiere.git
  version: v1.0.8
  # type: github_release
  # organization: Vintata
  # repository: tk-premiere
  # version: "catched error if clip has no source"
  # private: false
