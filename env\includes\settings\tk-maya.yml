# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

################################################################################

includes:
  - ../app_locations.yml
  - ../engine_locations.yml
  - ./tk-multi-loader2.yml
  - ./tk-multi-publish2.yml
  - ./tk-multi-reviewsubmission.yml
  - ./tk-multi-screeningroom.yml
  - ./tk-multi-shotgunpanel.yml
  - ./tk-multi-snapshot.yml
  - ./tk-multi-workfiles2.yml
  - ./tk-multi-breakdown.yml
  - ./mty-multi-batchloader.yml

################################################################################

# asset
settings.tk-maya.asset:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.launch_at_startup"
    mty-multi-scenesetup:
      location: "@apps.mty-multi-scenesetup.location"
  menu_favourites:
    - { app_instance: tk-multi-workfiles2, name: File Open... }
  debug_logging: false
  location: "@engines.tk-maya.location"

# asset_step
settings.tk-maya.asset_step:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-breakdown: "@settings.tk-multi-breakdown.maya"
    tk-multi-loader2: "@settings.tk-multi-loader2.maya"
    tk-multi-publish2: "@settings.tk-multi-publish2.maya.asset_step"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel.maya"
    tk-multi-snapshot: "@settings.tk-multi-snapshot.maya.asset_step"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.maya.asset_step"
    #tk-multi-reviewsubmission: "@settings.tk-multi-reviewsubmission.maya"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'
    mty-multi-scenesetup:
      location: "@apps.mty-multi-scenesetup.location"

    mty-executeaction-gpu-update:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Update latest GPU..."
      action_hook: "{config}/tk-shotgun-executeaction/maya/maya_latest_gpu.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset" ]

    mty-executeaction-autorig-loader:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Open RigBeast mighty..."
      action_hook: "{config}/mty-maya-autorig/maya_autorig.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset" ]

    mty-executeaction-create-rom:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Create ROM..."
      action_hook: "{config}/tk-multi-publish2/maya/rigging/create_rom_atom.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset" ]

    mty-executeaction-prepare-model:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Prepare Model..."
      action_hook: "{config}/tk-shotgun-executeaction/maya/maya_prepare_model.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset" ]

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset" ]

  menu_favourites:
    - { app_instance: tk-multi-workfiles2, name: File Open... }
    - { app_instance: tk-multi-snapshot, name: Snapshot... }
    - { app_instance: tk-multi-workfiles2, name: File Save... }
    - { app_instance: tk-multi-publish2, name: Publish... }
  template_project: asset_work_area_maya
  location: "@engines.tk-maya.location"


# asset_step_notask
settings.tk-maya.asset_step_notask:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-breakdown: "@settings.tk-multi-breakdown.maya"
    tk-multi-loader2: "@settings.tk-multi-loader2.maya"
    tk-multi-publish2: "@settings.tk-multi-publish2.maya.asset_step_notask"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel.maya"
    tk-multi-snapshot: "@settings.tk-multi-snapshot.maya.asset_step_notask"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.maya.asset_step_notask"
    #tk-multi-reviewsubmission: "@settings.tk-multi-reviewsubmission.maya"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'
    mty-multi-scenesetup:
      location: "@apps.mty-multi-scenesetup.location"

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset" ]

  menu_favourites:
    - { app_instance: tk-multi-workfiles2, name: File Open... }
    - { app_instance: tk-multi-snapshot, name: Snapshot... }
    - { app_instance: tk-multi-workfiles2, name: File Save... }
    - { app_instance: tk-multi-publish2, name: Publish... }
  template_project: asset_work_area_maya_notask
  location: "@engines.tk-maya.location"


# environment_step
settings.tk-maya.environment_step:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-breakdown: "@settings.tk-multi-breakdown.maya"
    tk-multi-loader2: "@settings.tk-multi-loader2.maya"
    tk-multi-publish2: "@settings.tk-multi-publish2.maya.environment_step"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel.maya"
    tk-multi-snapshot: "@settings.tk-multi-snapshot.maya.environment_step"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.maya.environment_step"
    #tk-multi-reviewsubmission: "@settings.tk-multi-reviewsubmission.maya"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'
    mty-multi-scenesetup:
      location: "@apps.mty-multi-scenesetup.location"

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Environment" ]

  menu_favourites:
    - { app_instance: tk-multi-workfiles2, name: File Open... }
    - { app_instance: tk-multi-snapshot, name: Snapshot... }
    - { app_instance: tk-multi-workfiles2, name: File Save... }
    - { app_instance: tk-multi-publish2, name: Publish... }
  template_project: environment_work_area_maya
  location: "@engines.tk-maya.location"

# environment_step_notask
settings.tk-maya.environment_step_notask:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-breakdown: "@settings.tk-multi-breakdown.maya"
    tk-multi-loader2: "@settings.tk-multi-loader2.maya"
    tk-multi-publish2: "@settings.tk-multi-publish2.maya.environment_step_notask"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel.maya"
    tk-multi-snapshot: "@settings.tk-multi-snapshot.maya.environment_step_notask"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.maya.environment_step_notask"
    #tk-multi-reviewsubmission: "@settings.tk-multi-reviewsubmission.maya"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'
    mty-multi-scenesetup:
      location: "@apps.mty-multi-scenesetup.location"

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Environment" ]

  menu_favourites:
    - { app_instance: tk-multi-workfiles2, name: File Open... }
    - { app_instance: tk-multi-snapshot, name: Snapshot... }
    - { app_instance: tk-multi-workfiles2, name: File Save... }
    - { app_instance: tk-multi-publish2, name: Publish... }
  template_project: environment_work_area_maya_notask
  location: "@engines.tk-maya.location"


# project
settings.tk-maya.project:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.maya.project"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    mty-executeaction-ensure-folders:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Ensure Tasks Folders"
      action_hook: "{config}/tk-shotgun-executeaction/create_folders_for_assigned_tasks.py"
      sg_extended_fields: { }
      allowed_entities: [ "Shot", "Sequence", "Asset", "Project" ]
    mty-multi-scenesetup:
      location: "@apps.mty-multi-scenesetup.location"
  menu_favourites:
    - { app_instance: tk-multi-workfiles2, name: File Open... }
  location: "@engines.tk-maya.location"

# sequence
settings.tk-maya.sequence:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-setframerange:
      location: "@apps.tk-multi-setframerange.location"
    tk-multi-loader2: "@settings.tk-multi-loader2.maya"
    tk-multi-publish2: "@settings.tk-multi-publish2.maya.sequence_step"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.launch_at_startup"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    mty-executeaction-sequence-shot-splitter:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Sequence Shot Splitter"
      action_hook: "{config}/extract_shot_from_sequence.py"
      sg_extended_fields: { }
      allowed_entities: [ "Sequence", "Project" ]
      step_name: "Setup"
      task_name: "Layout"
      asset_alembic_template_name: "maya_shot_animation_alembic_publish"
      rigging_tasks: ["FullRig", "BodyRig", "rig3DRigging"]
      layout_tasks: ["Layout", "lyt3dLayout", "lyt3dBlocking", "lyt3dPolish"]
    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'
    mty-multi-scenesetup:
      location: "@apps.mty-multi-scenesetup.location"
    mty-multi-queue:
      location: '@apps.mty-multi-queue.location'

  menu_favourites:
    - { app_instance: tk-multi-workfiles2, name: File Open... }
  location: "@engines.tk-maya.location"

# sequence_step
settings.tk-maya.sequence_step:
  apps:
    mty-maya-layouttoolkit:
      location: "@apps.mty-maya-layouttoolkit.location"
      template_work: maya_sequence_work
      template_sequence: maya_sequence_playblast_work_mov
      # template_shot: maya_sequence_playblast_shot_work_mov
      template_shot: maya_sequence_playblast_shot_work
      template_maya_asset: maya_asset_publish
      template_gpu_cache_asset: asset_gpu_cache
      actions_hook: "{self}/tk-maya_actions.py:{config}/tk-multi-loader2/tk-maya_actions.py"
      camera_asset_settings:
        entity_type: Asset
        entity_filters:
          - [ "entity.Asset.sg_asset_type", "is", "Camera" ]
        publish_file_type: Maya Scene
        action_name: reference_with_custom_namespace
    # mty-maya-layoutsequencer:
    #   location: "@apps.mty-maya-layoutsequencer.location"
    #   template_work: maya_sequence_work
    #   template_sequence: maya_sequence_playblast_work_mov
    #   template_shot: maya_sequence_playblast_shot_work_mov
    #   template_maya_asset: maya_asset_publish
    #   template_gpu_cache_asset: asset_gpu_cache
    #   actions_hook: "{self}/tk-maya_actions.py:{config}/tk-multi-loader2_transfer_actions.py"
    #   camera_asset_settings:
    #     entity_type: Asset
    #     entity_filters:
    #       - [ "entity.Asset.sg_asset_type", "is", "Camera" ]
    #     publish_file_type: Maya Scene
    #     action_name: reference_with_custom_namespace
    # mty-maya-playblast:
    #   location: "@apps.mty-maya-playblast.location"
    #   template_work: maya_sequence_work
    #   template_sequence: maya_sequence_playblast_work_mov
    #   template_shot: maya_sequence_playblast_shot_work_mov
    #   display_name: "Create Playblast"
    #   width: 1280
    #   height: 720
    #   current_scene_template: maya_sequence_work
    #   playblast_template: maya_sequence_playblast_work_mov
    #   sg_in_frame_field: sg_cut_in
    #   sg_out_frame_field: sg_cut_out
    #   camera_patern: "%s:rigged_CAMShape"
    #   software_to_review: "RV"
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-breakdown: "@settings.tk-multi-breakdown.maya"
    tk-multi-setframerange:
      location: "@apps.tk-multi-setframerange.location"
    tk-multi-loader2: "@settings.tk-multi-loader2.maya"
    tk-multi-publish2: "@settings.tk-multi-publish2.maya.sequence_step"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel.maya"
    tk-multi-snapshot: "@settings.tk-multi-snapshot.maya.sequence_step"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.maya.sequence_step"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'
    mty-multi-scenesetup:
      location: "@apps.mty-multi-scenesetup.location"
    mty-multi-queue:
      location: '@apps.mty-multi-queue.location'

    mty-executeaction-sequence-shot-splitter:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Sequence Shot Splitter"
      action_hook: "{config}/extract_shot_from_sequence.py"
      sg_extended_fields: { }
      allowed_entities: [ "Sequence", "Project" ]
      step_name: "Setup"
      task_name: "Layout"
      asset_alembic_template_name: "maya_shot_animation_alembic_publish"
      rigging_tasks: ["FullRig", "BodyRig", "rig3DRigging"]
      layout_tasks: ["Layout", "lyt3dLayout", "lyt3dBlocking", "lyt3dPolish"]

    mty-executeaction-create-TopCam:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Create sequence TopCam"
      action_hook: "{config}/tk-shotgun-executeaction/maya/maya_create_TopCam.py"
      sg_extended_fields: { }
      allowed_entities: [ "Sequence" ]

    mty-executeaction-create-distance-cycle_locator:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Create Distance Cycle Locator"
      action_hook: "{config}/tk-shotgun-executeaction/maya/maya_create_distance_cycle_locator.py"
      sg_extended_fields: { }
      allowed_entities: [ "Sequence" ]

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Sequence" ]

  menu_favourites:
    - { app_instance: tk-multi-workfiles2, name: File Open... }
    - { app_instance: tk-multi-snapshot, name: Snapshot... }
    - { app_instance: tk-multi-workfiles2, name: File Save... }
    - { app_instance: tk-multi-publish2, name: Publish... }
  template_project: sequence_work_area_maya
  location: "@engines.tk-maya.location"

# shot
settings.tk-maya.shot:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.launch_at_startup"
    mty-multi-scenesetup:
      location: "@apps.mty-multi-scenesetup.location"
    tk-multi-setframerange:
      location: "@apps.tk-multi-setframerange.location"
    # mty-app-anitools:
    #   location: "@apps.mty-app-anitools.location"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
      frameworks: "@frameworks"
    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'

  menu_favourites:
    - { app_instance: tk-multi-workfiles2, name: File Open... }
  location: "@engines.tk-maya.location"

# shot_step
settings.tk-maya.shot_step:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-breakdown: "@settings.tk-multi-breakdown.maya"
    mty-multi-scenesetup:
      location: "@apps.mty-multi-scenesetup.location"
    #mty-app-anitools:
    #  location: "@apps.mty-app-anitools.location"

    # mty-executeaction-facial-texture-loader:
    #   location: "@apps.tk-shotgun-executeaction.location"
    #   display_name: "Facial Rig Texture Loader"
    #   action_hook: facial_texture_loader
    #   sg_extended_fields: { }
    #   allowed_entities: [ "Shot" ]

    mty-executeaction-show-rendered-outputs:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Show Rendered Outputs"
      action_hook: "{config}/tk-shotgun-executeaction/show_rendered_outputs.py"
      sg_extended_fields: { }
      allowed_entities: [ "Shot" ]

    mty-executeaction-set_shot_main_render_settings:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Set Shot Main Render Settings..."
      action_hook: "{config}/tk-shotgun-executeaction/maya/maya_set_shot_main_render_settings.py"
      sg_extended_fields: { }
      allowed_entities: [ "Shot" ]

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Shot" ]

    tk-multi-setframerange:
      location: "@apps.tk-multi-setframerange.location"
    # mty-maya-playblast:
    #   display_name: "Create Playblast"
    #   width: 1280
    #   height: 720
    #   current_scene_template: maya_shot_work
    #   playblast_template: maya_shot_playblast
    #   sg_in_frame_field: sg_cut_in
    #   sg_out_frame_field: sg_cut_out
    #   camera_patern: "%s:rigged_CAMShape"
    #   software_to_review: "RV"
    #   location: "@apps.mty-maya-playblast.location"
    tk-multi-batchloader: "@settings.mty-multi-batchloader.maya.shot_step"
    tk-multi-loader2: "@settings.tk-multi-loader2.maya"
    tk-multi-publish2: "@settings.tk-multi-publish2.maya.shot_step"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel.maya"
    tk-multi-snapshot: "@settings.tk-multi-snapshot.maya.shot_step"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.maya.shot_step"
    #tk-multi-reviewsubmission: "@settings.tk-multi-reviewsubmission.maya"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
      frameworks: "@frameworks"
    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'

  menu_favourites:
    - { app_instance: tk-multi-workfiles2, name: File Open... }
    - { app_instance: tk-multi-snapshot, name: Snapshot... }
    - { app_instance: tk-multi-workfiles2, name: File Save... }
    - { app_instance: tk-multi-publish2, name: Publish... }
  template_project: shot_work_area_maya
  location: "@engines.tk-maya.location"
