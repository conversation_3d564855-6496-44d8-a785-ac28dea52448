# -*- coding: utf-8 -*-
# Standard library:
#   .   .   .   .   .   .   .   .   .   .   .
# Third party:
import sgtk
import pymel.core as pm
#   .   .   .   .   .   .   .   .   .   .   .
# Project:
# ================================================================

HookBaseClass = sgtk.get_hook_baseclass()


class ModelHighRootValidationPlugin(HookBaseClass):

    def __init__(self, parent):
        super(
            ModelHighRootValidationPlugin,
            self
        ).__init__(parent)

    @property
    def description(self):
        return """
            <p>
            This plugin will validate high_root elements in the asset.
            </p>
        """

    # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

    @property
    def item_filters(self):
        return["maya.session.high_roots"]

    # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

    def accept(self, settings, item):
        return{
            "accepted": True,
            "enabled": True,
            "visible": True,
            "checked": True
        }

    def validate(self, settings, item):
        step_name = self.parent.context.step['name'].lower()

        fbCache_Dyn_GlobalScale = None

        state = {
            "errors": 0,
            "messages": {
                "errors": [],
                "debugs": []
            }
        }

        model_high_root = {
            "nodes": pm.ls(item.properties["model"], type="transform"),
            "name": item.properties["model"]
        }

        if step_name == "model":
            self.model_only_checks(model_high_root, state)
            self.evaluate(state)
            if state["errors"] == 0:
                return True

        rig_high_root = {
            "nodes": pm.ls(item.properties["rig"], type="transform"),
            "name": item.properties["model"]
        }

        for each in [model_high_root, rig_high_root]:
            if len(each["nodes"]) == 0:
                state["errors"] += 1
                state["messages"]["errors"].append(
                    "{0} ".format(each["name"]) +
                    "doesn't exists."
                )

            elif len(each["nodes"]) > 1:
                state["errors"] += 1
                state["messages"]["errors"] \
                    .append(
                        "There is more than " +
                        "one {0} node.".format(each["name"])
                )

        self.evaluate(state)

        fbCache_Dyn_GlobalScale = \
            model_high_root["nodes"][0] \
            .hasAttr("fbCache_Dyn_GlobalScale")

        if not fbCache_Dyn_GlobalScale:
            state["errors"] += 1
            message = \
                "Missing fbCache_Dyn_GlobalScale " + \
                "attribute in " + \
                model_high_root["name"]

            state["messages"]["errors"].append(message)

        self.evaluate(state)

        fbCache_Dyn_GlobalScale_type = \
            pm.attributeQuery(
                "fbCache_Dyn_GlobalScale",
                node=model_high_root["nodes"][0],
                attributeType=True
            )

        if fbCache_Dyn_GlobalScale_type != "double":
            state["errors"] += 1
            message = \
                "The fbCache_Dyn_GlobalScale " + \
                "attribute its not a double."
            state["messages"]["errors"].append(message)

        self.evaluate(state)

        return True

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def publish(self, settings, item):
        return True

    def finalize(self, settings, item):
        pass

    def model_only_checks(self, model_high_root, state):
        if len(model_high_root["nodes"]) == 0:
            state["errors"] += 1
            state["messages"]["errors"].append(
                "There is no model_high_root node."
            )
        elif len(model_high_root["nodes"]) > 1:
            state["errors"] += 1
            state["messages"]["errors"].append(
                "There is more than one model_high_root node."
            )
        else:
            state["messages"]["debugs"].append(
                "model_high_root node exists."
            )

    # ================================================================

    def evaluate(self, state):
        if state["errors"] == 0:
            for message in state["messages"]["debugs"]:
                self.logger.debug(message)

            return True

        else:
            self.logger.error(
                "{0} errors found.".format(state["errors"])
            )

            for message in state["messages"]["errors"]:
                self.logger.error(message)

            raise Exception("Session attributes checks fail.")
