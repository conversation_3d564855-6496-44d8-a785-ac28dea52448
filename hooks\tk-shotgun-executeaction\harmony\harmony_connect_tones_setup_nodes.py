########################################################################################
#
# Copyright (c) 2023 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty Animation Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
########################################################################################

"""
An app that helps to speed up the process of connecting nodes to the tones tpl. It
automates the connections once the user has selected the proper nodes
"""

from tank import Hook
from tank import TankError
from tank.platform import Application
from tank.platform.qt import QtCore, QtGui

import sgtk
import tank

import os
import re
import sys
import pprint
import tempfile


pp = pprint.pprint
pf = pprint.pformat


QFrame = QtGui.QFrame
QLabel = QtGui.QLabel
QWidget = QtGui.QWidget
QGridLayout = QtGui.QGridLayout
QMainWindow = QtGui.QMainWindow
QPushButton = QtGui.QPushButton
QVBoxLayout = QtGui.QVBoxLayout
QApplication = QtGui.QApplication


class ProcessItemsHook(Hook):
    def execute(self, **kwargs):

        self.parent.engine.logger.info(
            "execute action connect_tones_nodes start. {}".format("-" * 80)
        )

        result = {"succes": [1], "messages": [], "errors": []}

        nodes_dict = get_selected_nodes(self.parent.engine)
        self.parent.engine.logger.debug("nodes_dict:\n{}".format(pf(nodes_dict)))

        # if not QApplication.instance():
        #     app = QApplication(sys.argv)
        # else:
        #     app = QApplication.instance()
        app = QApplication.instance()

        logger = self.parent.engine.logger
        window = ConnectionWindow(nodes_dict, self.parent.engine, logger)
        window.show()

        self.parent.engine.logger.info(
            "execute action connect_tones_nodes end. {}".format("-" * 80)
        )

        return result


class ConnectionWindow(QWidget):
    def __init__(self, nodes_dict, engine, logger):
        QWidget.__init__(self, None, QtCore.Qt.WindowStaysOnTopHint)
        # super(ConnectionWindow, self).__init__()

        self.nodes_dict = nodes_dict
        self.engine = engine
        self.logger = logger

        # Set fixed horizontal size
        self.setMinimumWidth(1000)

        # Create vertical layout for main window
        main_layout = QVBoxLayout()

        # Add instructions label
        instructions_text = (
            "Select the corresponding node or peg for each part and then click on its "
            "button. This will add the name of the node/peg to the interface.\n"
            "If it is the wrong node/peg, select the correct one and click on its "
            "button again.\n"
        )
        instructions_label = QLabel(instructions_text)
        main_layout.addWidget(instructions_label)

        # Add a separator
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        main_layout.addWidget(separator)

        # Create grid layout for the widgets
        grid_layout = QGridLayout()

        # Create lists to store buttons and labels
        self.buttons_labels = []

        # Create three vertical groups per character part
        for i, part in enumerate(sorted(self.nodes_dict.keys())):
            # First column -------------------------------------------------------------
            # Add two labels and a button to each group
            label_part = QLabel("<b>{}</b>".format(part))
            label_target_node = QLabel("------")
            label_separator1 = QLabel(" --> ")
            button_node = QPushButton("Get selected node")

            grid_layout.addWidget(label_part, i, 0)
            grid_layout.addWidget(
                label_separator1, i, 1, alignment=QtCore.Qt.AlignCenter
            )
            grid_layout.addWidget(
                label_target_node, i, 2, alignment=QtCore.Qt.AlignCenter
            )
            grid_layout.addWidget(button_node, i, 3)

            # Set group colors ---------------------------------------------------------
            stylesheet_text_grp1 = "background-color: rgb(115, 152, 194); color: black"

            label_part.setStyleSheet(stylesheet_text_grp1)
            # label_separator1.setStyleSheet(stylesheet_text_grp1)
            label_target_node.setStyleSheet(stylesheet_text_grp1)
            button_node.setStyleSheet("color: rgb(151, 199, 255)")

            # Second column ------------------------------------------------------------
            # Add two labels and a button to each group
            label_part_peg = QLabel("<b>{} Peg</b>".format(part))
            label_target_peg = QLabel("------")
            label_separator2 = QLabel(" --> ")
            button_peg = QPushButton("Get selected peg")

            grid_layout.addWidget(label_part_peg, i, 6)
            grid_layout.addWidget(
                label_separator2, i, 7, alignment=QtCore.Qt.AlignCenter
            )
            grid_layout.addWidget(
                label_target_peg, i, 8, alignment=QtCore.Qt.AlignCenter
            )
            grid_layout.addWidget(button_peg, i, 9)

            # Set group colors ---------------------------------------------------------
            stylesheet_text_grp2 = "background-color: rgb(138, 189, 85); color: black"

            label_part_peg.setStyleSheet(stylesheet_text_grp2)
            # label_separator2.setStyleSheet(stylesheet_text_grp2)
            label_target_peg.setStyleSheet(stylesheet_text_grp2)
            button_peg.setStyleSheet("color: rgb(186, 255, 114)")

            # Store objs in the main dict for later reference
            self.nodes_dict[part].update(
                {
                    "node_button": button_node,
                    "node_selected_label": label_target_node,
                    "peg_button": button_peg,
                    "peg_selected_label": label_target_peg,
                }
            )

            # Add the labels and buttons to the list (as tuple)
            self.buttons_labels.append(
                (
                    button_node,
                    label_target_node,
                    button_peg,
                    label_target_peg,
                )
            )

            # set buttons indexes
            button1_index = 0
            button2_index = 2

            # Connect button to callback function with index as argument
            button_node.clicked.connect(
                lambda checked=False, tup_index=i, btn_index=button1_index: self.on_button_clicked(
                    tup_index, btn_index
                )
            )
            button_peg.clicked.connect(
                lambda checked=False, tup_index=i, btn_index=button2_index: self.on_button_clicked(
                    tup_index, btn_index
                )
            )

        # Set columns width ------------------------------------------------------------
        # separator columns
        grid_layout.setColumnMinimumWidth(1, 10)
        grid_layout.setColumnMinimumWidth(7, 10)

        # spacer column
        grid_layout.setColumnMinimumWidth(5, 40)

        # original nodes columns
        grid_layout.setColumnMinimumWidth(0, 40)
        grid_layout.setColumnMinimumWidth(6, 40)

        # Set columns stretch ----------------------------------------------------------
        # separator columns
        grid_layout.setColumnStretch(1, 1)
        grid_layout.setColumnStretch(7, 1)

        # spacer column
        grid_layout.setColumnStretch(5, 1)

        # original nodes columns
        grid_layout.setColumnStretch(0, 1)
        grid_layout.setColumnStretch(6, 1)

        # target nodes columns
        grid_layout.setColumnStretch(2, 2)
        grid_layout.setColumnStretch(8, 2)

        # Add grid layout to main layout
        main_layout.addLayout(grid_layout)

        # Add a separator
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        main_layout.addWidget(separator)

        # Add accept and cancel buttons
        accept_button = QPushButton("Accept")
        cancel_button = QPushButton("Cancel")

        # Main buttons callbacks
        accept_button.clicked.connect(self.on_accept_button_clicked)
        cancel_button.clicked.connect(self.on_cancel_button_clicked)

        main_layout.addWidget(accept_button)
        main_layout.addWidget(cancel_button)

        self.setLayout(main_layout)
        self.setWindowTitle("Select corresponding nodes")

    def on_button_clicked(self, tup_index, btn_index):
        # self.engine.logger.info("tup_index: {}".format(tup_index))
        # self.engine.logger.info("btn_index: {}".format(btn_index))
        if btn_index == 0:
            new_label = "Button {} clicked".format(tup_index * 2)
        elif btn_index == 2:
            new_label = "Button {} clicked".format((tup_index * 2) + 1)
        # self.engine.logger.info("new_label: {}".format(new_label))

        # Get selected node/peg from Harmony
        cmd = "selection.selectedNode(0);"
        new_label = self.engine.app.execute(cmd)
        if not new_label:
            new_label = "------"
        # self.engine.logger.info("new_label: {}".format(new_label))

        # Update label's text
        self.buttons_labels[tup_index][btn_index + 1].setText(new_label)

        # cmd = """MessageLog.trace("new_label: {}");""".format(new_label)
        # self.engine.app.execute(cmd)

    def on_accept_button_clicked(self):
        # self.engine.logger.debug("nodes_dict:\n{}".format(pf(self.nodes_dict)))

        for part in self.nodes_dict.keys():
            self.engine.logger.info("part: {} {}".format(part, "-" * 20))
            label_part = self.nodes_dict[part]["node_selected_label"].text()
            label_peg = self.nodes_dict[part]["peg_selected_label"].text()

            self.engine.logger.info("label_part: {}".format(label_part))
            self.engine.logger.info("label_peg: {}".format(label_peg))

            # We connect the part nodes one by one
            self.connect_nodes(part)

        # Finally, after we are done with the connections, we close the app
        self.close()

    def on_cancel_button_clicked(self):
        self.close()

    def connect_nodes(self, part):
        """
        Given a Harmony node_path, tries to connect it to its group_multi output.
        Only the first level is supported in case of nested groups, in other words,
        nodes inside a group that lives on the top level.

        return: the last (top level) group node. The last output should then be
                connected to the corresponding node or peg
        """

        # Get the relevant nodes and resolve the relevant names from the part dict
        hl_drawing = self.nodes_dict[part]["HL"]
        hl_cutter = self.nodes_dict[part]["HL_cutter"]
        shd_drawing = self.nodes_dict[part]["SHD"]
        shd_cutter = self.nodes_dict[part]["SHD_cutter"]

        for label in self.nodes_dict[part].keys():
            if "label" in label:
                node_path = self.nodes_dict[part][label].text()

                # Continue with next label if label wasn't changed
                if node_path == "------":
                    continue

                parent_group = os.path.dirname(node_path)

                # We need to make sure the parent group is not the top level as it
                # doesn't have multi output group
                if parent_group == "Top":
                    continue

                cmd = """
// =====================================================================================

log = MessageLog.trace;

log("--------------------------------------");

var hl_drawing = "%s"
var hl_cutter = "%s"
var shd_drawing = "%s"
var shd_cutter = "%s"

var node_ = "%s";
var parent_group = "%s";

// log("node_: " + node_);
// log("parent_group: " + parent_group);
// log("parent_group type: " + node.type(parent_group));

function connect_node_to_multiouput(node_, parent_group) {
    if (node.type(parent_group) != "GROUP") {
        log("is not group");
        return null;
    };

    multiout = node.getGroupOutputModule(parent_group, "", 0, 0, 0);
    multiout_inputs = node.numberOfInputPorts(multiout)
    node.link(node_, 0, multiout, multiout_inputs, true, true);

    if (node.type(node_) == "PEG") {
        return true;
    } else {
        return false;
    };
};

function connect_multiout_to_nodes(parent_group, hl_node, shd_node, port) {
    group_out_num = node.numberOfOutputPorts(parent_group)

    node.link(parent_group, group_out_num - 1, hl_node, port);
    node.link(parent_group, group_out_num - 1, shd_node, port);
};

var is_peg = connect_node_to_multiouput(node_, parent_group);
if (is_peg != null) {
    if (is_peg == true) {
        connect_multiout_to_nodes(parent_group, hl_drawing, shd_drawing, 0);
    } else {
        connect_multiout_to_nodes(parent_group, hl_cutter, shd_cutter, 1);
    };
};

""" % (
    hl_drawing,
    hl_cutter,
    shd_drawing,
    shd_cutter,
    node_path,
    parent_group,
)
                self.engine.app.execute(cmd)

                self.logger.debug("node_path: {}".format(node_path))
                self.logger.debug("cmd:\n{}".format(cmd))

                # save previous command to a tmp file to read it later in Harmony
                tmp_folder = tempfile.gettempdir()
                tmp_file_path = os.path.join(
                    tmp_folder, "{}_{}.txt".format(part, label)
                )
                tmp_file_path = fix_path(tmp_file_path)
                with open(tmp_file_path, 'w') as tmp_file:
                    tmp_file.write(cmd)

                self.logger.info("tmp_file_path: {}".format(tmp_file_path))

                cmd_script_node = """
log = MessageLog.trace;

function get_scripted_node(src_node, x_offset, y_offset) {
    var src_name = node.getName(src_node);
    var src_node_parent = node.parentNode(src_node);
    var new_node_name = "ScriptModule_" + src_name ;
    var new_node_full_path = src_node_parent + "/" + new_node_name;
    log("new_node_full_path: " + new_node_full_path);

    var src_node_X_pos = node.coordX(src_node);
    var src_node_Y_pos = node.coordY(src_node);
    var src_node_Z_pos = node.coordZ(src_node);

    var scripted_node_color = new ColorRGBA(0, 95, 115, 255);

    if (node.getName(new_node_full_path) === "") {
        var scripted_node = node.add(
            src_node_parent,
            new_node_name,
            "SCRIPT_MODULE",
            src_node_X_pos - x_offset,
            src_node_Y_pos + y_offset,
            src_node_Z_pos
        );
        node.setColor(scripted_node, scripted_node_color);
        // return scripted_node;
        return new_node_full_path;
    } else {
        node.setColor(new_node_full_path, scripted_node_color);
        return new_node_full_path;
    }
}

function set_scripted_attribute(scripted_node, cmd_str) {
    var editor_attr = node.getAttr(scripted_node, 1, "uiScript.editor");
    var current_attr_value = editor_attr.textValue();
    var extended_attr_value = current_attr_value + cmd_str;

    editor_attr.setValue(extended_attr_value);
}

var src_node = "%s";
log("src_node: " + src_node);

var tmp_file_path = "%s";
log("tmp_file_path: " + tmp_file_path);

file = new File(tmp_file_path);
file.open(FileAccess.ReadOnly);
var cmd_str = file.read();
file.close();

// var cmd_str = "";
// log(cmd_str);

var scripted_node = get_scripted_node(src_node, 100, 80);
set_scripted_attribute(scripted_node, cmd_str);

""" % (parent_group, tmp_file_path)
                self.logger.info(
                    "cmd_script_node:\n{}".format(cmd_script_node)
                )

                self.engine.app.execute(cmd_script_node)


def get_selected_nodes(engine):
    result = {}

    cmd = "selection.selectedNodes();"
    selected_nodes = engine.app.execute(cmd)

    pattern = re.compile(
        (
            r"(?P<cutter>[cC]utter)?"
            r"(?P<part_name>[a-zA-Z]+)_"
            r"(?P<tail>(?P<tone>HL|SHD)(.+)?)"
        )
    )

    for node in selected_nodes:
        dirname = os.path.dirname(node)
        basename = os.path.basename(node)
        orig_basename = basename

        match = re.match(pattern, basename)
        if not match:
            continue

        # engine.logger.info("match for {}:\n{}".format(basename, pf(match.groupdict())))
        # engine.logger.info("{} dirname: {}".format(basename, dirname))

        part_name = match.groupdict()["part_name"]
        cutter = match.groupdict()["cutter"]
        tone = match.groupdict()["tone"]
        tail = match.groupdict()["tail"]

        if part_name not in result.keys():
            result[part_name] = {}

        if not cutter and tone == "HL":
            result[part_name]["HL"] = "{}/{}_{}".format(dirname, part_name, tail)
        elif cutter and tone == "HL":
            result[part_name]["HL_cutter"] = "{}/{}{}_{}".format(
                dirname, cutter, part_name, tail
            )
        elif not cutter and tone == "SHD":
            result[part_name]["SHD"] = "{}/{}_{}".format(dirname, part_name, tail)
        elif cutter and tone == "SHD":
            result[part_name]["SHD_cutter"] = "{}/{}{}_{}".format(
                dirname, cutter, part_name, tail
            )

    return result

def fix_path(path):
    """Replace all backward slashes with forward slashes. Also adds the P: drive if it's
    no pressent"""

    path = path.replace("\\\\", "/")
    path = path.replace("\\", "/")

    return path
