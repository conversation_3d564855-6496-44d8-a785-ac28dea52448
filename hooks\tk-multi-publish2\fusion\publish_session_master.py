# Copyright (c) 2017 Shotgun Software Inc.
# 
# CONFIDENTIAL AND PROPRIETARY
# 
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit 
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your 
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights 
# not expressly granted therein are reserved by Shotgun Software Inc.

import os
import pprint
import sgtk
import traceback

from sgtk.util.filesystem import ensure_folder_exists

import BlackmagicFusion as bmd
fusion = bmd.scriptapp("Fusion")

HookBaseClass = sgtk.get_hook_baseclass()


class FusionSessionPublishMighty(HookBaseClass):
    """
    Plugin for publishing an open fusion session.

    This hook relies on functionality found in the base file publisher hook in
    the publish2 app and should inherit from it in the configuration. The hook
    setting for this plugin should look something like this::

        hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py"

    """

    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish. Returns a
        boolean to indicate validity.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        :returns: True if item is valid and not in proxy mode, False otherwise.
        """
        #publish_name = self.get_publish_name(settings, item)
        task_info = self.parent.engine.context.task
        sg = self.parent.engine.shotgun
        sg_task = sg.find_one('Task', [['id', 'is', task_info['id']]], ['sg_status_list'])
        
        # Approved validation
        if sg_task['sg_status_list'] == 'apr':
            error_msg  = "Task already approved\n"
            error_msg += "Invalid publish\n"
            error_msg += "This task status is already approved, "
            error_msg += "contact your supervisor."
            raise Exception(error_msg)

        # Hold validation
        if sg_task['sg_status_list'] == 'hld':
            error_msg  = "Your task status is is on hold\n"
            error_msg += "Contact your supervisor."
            raise Exception(error_msg)

        # do the base class validation
        return super(FusionSessionPublishMighty, self).validate(settings, item)



    def publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """

        # get the path in a normalized state. no trailing separator, separators
        # are appropriate for current os, no double separators, etc.
        path = sgtk.util.ShotgunPath.normalize(_session_path())

        # ensure the session is saved
        _save_session(path)

        # update the item with the saved session path
        item.properties["path"] = path

        # Unfortunately, it seems that the SSL certificate does not work
        # with the Fusion urlib2 library so we force it here
        ssl_cert_file = os.environ.get("SSL_CERT_FILE")

        try:
            import inspect
            import tank_vendor.shotgun_api3.lib.httplib2 as sapi3_httplib2
            httplib2_file = inspect.getfile(sapi3_httplib2)
            httplib2_dir = os.path.dirname(httplib2_file)
            cacerts_file = os.path.join(httplib2_dir, "cacerts.txt")
            os.environ["SSL_CERT_FILE"] = cacerts_file
        except ImportError:
            pass

        dependency_paths = self.get_fusion_dependencies()
        item.properties["publish_dependencies"] = dependency_paths


        # let the base class register the publish
        #super(FusionSessionPublishMighty, self).publish(settings, item)
        self.app_publish(settings, item)

        if ssl_cert_file is None:
            del os.environ["SSL_CERT_FILE"]
        else:
            os.environ["SSL_CERT_FILE"] = ssl_cert_file

    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed, and can for example be used to version up files.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """

        # do the base class finalization
        super(FusionSessionPublishMighty, self).finalize(settings, item)

        try:
            # Update saver nodes
            self.parent.engine._FusionEngine__update_nodes_version()
        except:
            self.parent.log_error(traceback.format_exc())


        # get the data for the publish that was just created in SG
        publish_data = item.properties.sg_publish_data

        dependency_paths = self.get_fusion_dependencies()

        for path in dependency_paths:
            self.parent.log_debug(
                'Finalize: Resolved dependency paths: %s' % path)

        dependencies = sgtk.util.find_publish(
            self.parent.sgtk, dependency_paths)
        dependencies = [dependencies[path] for path in dependencies]

        self.parent.log_debug(
            "Resolved dependency publishes:\n%s" % pprint.pformat(dependencies))

        if dependencies:
            filters = [['published_file', 'is', publish_data],
                       ['dependent_published_file', 'in', dependencies]]
            publish_dependencies = self.parent.shotgun.find(
                'PublishedFileDependency', filters)

            batch_data = []
            for publish in publish_dependencies:
                batch_data.append({'request_type': 'update',
                                   'entity_type': 'PublishedFileDependency',
                                   'entity_id': publish['id'],
                                   'data': {'sg_type': 'Hard'}})

            self.parent.shotgun.batch(batch_data)
        
    def get_fusion_dependencies(self):
        comp = fusion.GetCurrentComp()
        loaders = comp.GetToolList(False, "Loader").values()
        loader_dependencies = []
        
        for loader in loaders:
            clip_info = loader.GetAttrs()['TOOLST_Clip_Name']
            if clip_info is None:
                continue

            l_path = clip_info[1]
            base_template = self.parent.sgtk.template_from_path(l_path)
            if base_template is None:
                continue
            
            if base_template.name == 'shot_flat_render_publish_exr':
                continue
                
            loader_dependencies.append(l_path)
        
        return loader_dependencies

    # this app_publish method is litteraly a copy of the publish one from the base publish_file hook
    # https://github.com/shotgunsoftware/tk-multi-publish2/blob/master/hooks/publish_file.py

    def app_publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.
        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """

        publisher = self.parent

        # ---- determine the information required to publish

        # We allow the information to be pre-populated by the collector or a
        # base class plugin. They may have more information than is available
        # here such as custom type or template settings.

        publish_type = self.get_publish_type(settings, item)
        publish_name = self.get_publish_name(settings, item)
        publish_version = self.get_publish_version(settings, item)
        publish_path = self.get_publish_path(settings, item)
        publish_dependencies_paths = self.get_publish_dependencies(settings, item)
        publish_dependencies_paths.extend(item.properties["publish_dependencies"])
        publish_user = self.get_publish_user(settings, item)
        publish_fields = self.get_publish_fields(settings, item)
        # catch-all for any extra kwargs that should be passed to register_publish.
        publish_kwargs = self.get_publish_kwargs(settings, item)

        # if the parent item has publish data, get it id to include it in the list of
        # dependencies
        publish_dependencies_ids = []
        if "sg_publish_data" in item.parent.properties:
            publish_dependencies_ids.append(
                item.parent.properties.sg_publish_data["id"]
            )

        # handle copying of work to publish if templates are in play
        self._copy_work_to_publish(settings, item)

        # arguments for publish registration
        self.logger.info("Registering publish...")
        publish_data = {
            "tk":self.parent.engine.sgtk,
            "context": item.context,
            "comment": item.description,
            "path": publish_path,
            "name": publish_name,
            "created_by": publish_user,
            "version_number": publish_version,
            "thumbnail_path": item.get_thumbnail_as_path(),
            "published_file_type": publish_type,
            "dependency_paths": list(set(publish_dependencies_paths)),
            "dependency_ids": publish_dependencies_ids,
            "task": self.parent.engine.context.task,
            "sg_fields": publish_fields,
        }

        validation_tags = item.properties.get('checklist_validations')
        
        if validation_tags:
            publish_data["sg_checked_validations"] = validation_tags
            publish_data["tags"] = validation_tags

        # add extra kwargs
        publish_data.update(publish_kwargs)

        # log the publish data for debugging
        self.logger.debug(
            "Populated Publish data...",
            extra={
                "action_show_more_info": {
                    "label": "Publish Data",
                    "tooltip": "Show the complete Publish data dictionary",
                    "text": "<pre>%s</pre>" % (pprint.pformat(publish_data),),
                }
            },
        )


        # create the publish and stash it in the item properties for other
        # plugins to use. 
        item.properties.sg_publish_data = sgtk.util.register_publish(**publish_data)

        validation_tags = item.properties.get('checklist_validations')
        # update tags
        if validation_tags:
            self.parent.shotgun.update("PublishedFile",
                                       item.properties.sg_publish_data['id'],
                                       {'tags': validation_tags})

        self.logger.info("Publish registered!")
        self.logger.debug(
            "Shotgun Publish data...",
            extra={
                "action_show_more_info": {
                    "label": "Shotgun Publish Data",
                    "tooltip": "Show the complete Shotgun Publish Entity dictionary",
                    "text": "<pre>%s</pre>"
                    % (pprint.pformat(item.properties.sg_publish_data),),
                }
            },
        )

        
def _session_path():
    """
    Return the path to the current session
    :return:
    """
    comp = fusion.GetCurrentComp()
    path = comp.GetAttrs()['COMPS_FileName']

    if isinstance(path, unicode):
        path = path.encode("utf-8")

    return path


def _save_session(path):
    """
    Save the current session to the supplied path.
    """

    # Ensure that the folder is created when saving
    folder = os.path.dirname(path)
    ensure_folder_exists(folder)

    comp = fusion.GetCurrentComp()
    comp.Save(path)


# TODO: method duplicated in all the fusion hooks
def _get_save_as_action():
    """
    Simple helper for returning a log action dict for saving the session
    """

    engine = sgtk.platform.current_engine()

    callback = _save_as

    # if workfiles2 is configured, use that for file save
    if "tk-multi-workfiles2" in engine.apps:
        app = engine.apps["tk-multi-workfiles2"]
        if hasattr(app, "show_file_save_dlg"):
            callback = app.show_file_save_dlg

    return {
        "action_button": {
            "label": "Save As...",
            "tooltip": "Save the current session",
            "callback": callback
        }
    }


def _save_as():
    comp = fusion.GetCurrentComp()
    path = comp.GetAttrs()['COMPS_FileName']

    if isinstance(path, unicode):
        path = path.encode("utf-8")

    if path:
        comp.Save(path)
