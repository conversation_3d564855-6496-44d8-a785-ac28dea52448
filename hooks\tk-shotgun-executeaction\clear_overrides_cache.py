########################################################################################
#
# Copyright (c) 2024 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
########################################################################################

"""
An app that tries to clear the value overrides so new overrides can be read immediately
after creation. Useful when applying overrides to Tasks
"""
import os
import traceback

from tank import Hook
from tank.platform.qt import QtCore, QtGui

QMessageBox = QtGui.QMessageBox

class ProcessItemsHook(Hook):

    def execute(self, entity_type, entities, other_params, **kwargs):

        # load overrides framework -----------------------------------------------------
        overrides_framework = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        ) or self.load_framework("mty-framework-valueoverrides")
        if not overrides_framework:
            msg = "Couldn't load the oveerides framework. Caches were not cleared."
            self.show_message("Warning", msg, icon="Warning")
            return {'succes': [], 'messages': [msg], 'errors': [1]}

        try:
            overrides_framework.reset()
        except Exception as e:
            msg = "Clearing the caches failed:\n{}\nFull traceback:\n{}".format(
                e, traceback.format_exc()
            )
            self.show_message("Warning", msg, icon="Warning")
            return {'succes': [], 'messages': [msg], 'errors': [1]}

        msg = "Successfully cleared all overrides framework caches."
        self.show_message("Caches cleared", msg, icon="Information")
        return {'succes': [1], 'messages': [msg], 'errors': []}


    def show_message(self, title, msg, icon=None):
        """
        Displays a message box with a specified message and icon.

        This method creates a QMessageBox to display a message to the user.
        The appearance of the message box can be customized using the 'icon' parameter.

        Args:
            msg (str): The message to be displayed in the message box.
            icon (str, optional): The icon to be displayed in the message box.
                Valid options are "NoIcon", "Question", "Information", "Warning", and "Critical".
                Defaults to "NoIcon" if not provided or invalid.
        """

        icons_dict = {
            "NoIcon": QMessageBox.NoIcon,
            "Question": QMessageBox.Question,
            "Information": QMessageBox.Information,
            "Warning": QMessageBox.Warning,
            "Critical": QMessageBox.Critical,
        }

        if not icon or icon not in icons_dict.keys():
            icon_selection = icons_dict["NoIcon"]
        else:
            icon_selection = icons_dict[icon]

        msg_error = QMessageBox()
        msg_error.setWindowTitle(title)
        msg_error.setText(msg)
        msg_error.setIcon(icon_selection)
        msg_error.raise_()
        msg_error.activateWindow()
        msg_error.exec_()
