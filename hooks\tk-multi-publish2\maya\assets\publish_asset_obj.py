# -*- coding: utf-8 -*-
import os
import re
import json
import pprint
import traceback

# ___   ___   ___   ___   ___   ___  ___
# Third party:
import sgtk
import maya.mel as mel
import maya.cmds as cmds
import pymel.core as pm

HookBaseClass = sgtk.get_hook_baseclass()

pp = pprint.pprint
pf = pprint.pformat

class ObjAssetPublishPlugin(HookBaseClass):
    def __init__(self, parent):
        super(ObjAssetPublishPlugin, self).__init__(parent)
        if not pm.pluginInfo("objExport", loaded=True, query=True):
            pm.loadPlugin("objExport")

        self.parent.logger.info("obj publish start".ljust(88, "-"))

    @property
    def icon(self):
        return os.path.join(self.disk_location, os.pardir, "icons", "mesh.png")

    @property
    def name(self):
        return "Publish Asset OBJ File"

    @property
    def description(self):
        return "This plugin will save static obj for specific asset."

    @property
    def item_filters(self):
        return ["maya.session.asset.obj"]

    @property
    def settings(self):
        plugin_settings = super(ObjAssetPublishPlugin, self).settings or {}

        asset_obj_settings = {
            "Work Template": {
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                "correspond to a template defined in "
                "templates.yml.",
            },
            "Primary Publish Template": {
                "type": "template",
                "default": None,
                "description": "Maya publish session",
            },
            "OBJ Publish Type": {
                "type": "string",
                "default": "OBJ Mesh",
                "description": "Publish File Type",
            },
            "OBJ Publish Template": {
                "type": "template",
                "default": None,
                "description": "",
            },
            "Transform Node": {
                "type": "string",
                "default": "model_high_root",
                "description": "",
            },
            "Valid Steps": {
                "type": "list",
                "default": None,
                "description": "Valid steps for exporting obj meshes",
            },
        }
        # update the base settings
        plugin_settings.update(asset_obj_settings)
        return plugin_settings

    # ----------------------------------------------------------------------------------

    def accept(self, settings, item):
        item.properties["accepted"] = False
        engine = sgtk.platform.current_engine()
        ctx = self.parent.engine.context
        step = ctx.step["name"]
        task = ctx.task

        # load overrides framework -----------------------------------------------------
        overrides_framework = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        ) or self.load_framework("mty-framework-valueoverrides")

        # load valid steps for exporting obj meshes from overrides or from settings
        # It's using the same overrides that fbx uses. If needed in the future, we
        # create extra default values and overrides exclusive to obj meshes.
        default_value_fbx_export_steps = "mty.publisher.maya.fbx_export_steps"

        valid_steps = overrides_framework.get_value(
            default_value_fbx_export_steps, link=task
        )
        self.parent.logger.info(f"obj_export_steps: {valid_steps}")
        if valid_steps:
            valid_steps = json.loads(valid_steps)
        else:
            valid_steps = settings.get("Valid Steps").value

        if step.lower() in valid_steps:
            return {"accepted": True, "checked": True}
        else:

            return {"accepted": False}

    # ----------------------------------------------------------------------------------

    def validate(self, settings, item):

        transform_node = self.get_transform_node_name(settings)
        if not transform_node:
            message = (
                "Couldn't get transform node name from overrides or settings.\n"
                "Please report this issue to the pipeline team."
            )
            raise Exception(message)

        if cmds.objExists(transform_node):
            self.parent.logger.info(f"{transform_node} exists")
            result = True
        else:
            message = f"{transform_node} not exist in the scene. plese create this node and try again!"
            raise Exception(message)

        return result

    # ----------------------------------------------------------------------------------

    def publish(self, settings, item):
        # General Data
        publisher = self.parent
        ctx = publisher.context
        step = ctx.step["name"]

        # Settings
        work_template_name = settings["Work Template"].value
        publish_template_name = settings["Primary Publish Template"].value
        obj_template_name = settings["OBJ Publish Template"].value

        # Publish File Type
        publish_file_type = settings["OBJ Publish Type"].value

        # Extras
        root_transform_node_name = settings.get("Transform Node").value
        root_transform_node = self.get_pymel_node(root_transform_node_name)

        # Templates
        work_template = self.parent.engine.get_template_by_name(work_template_name)
        publish_template = self.parent.engine.get_template_by_name(
            publish_template_name
        )
        obj_template = self.parent.engine.get_template_by_name(
            obj_template_name
        )

        scene_path = os.path.abspath(cmds.file(query=True, sn=True))
        self.parent.logger.debug(f"scene_path: {scene_path}")
        fields = work_template.get_fields(scene_path)
        publish_version = fields["version"]

        self.parent.logger.debug(f"fields for primary publish:\n{pf(fields)}")
        publish_session_path = publish_template.apply_fields(fields)

        fields["extension"] = "obj"
        self.parent.logger.debug(f"fields for obj publish:\n{pf(fields)}")
        obj_path = obj_template.apply_fields(fields)

        if not os.path.exists(os.path.dirname(obj_path)):
            os.makedirs(os.path.dirname(obj_path), exist_ok=True)

        publish_name = self._publish_name(obj_path)

        # rigging_grp = self.get_rigging_geo_grp(root_transform_node)

        # save current selection
        original_selection = pm.selected()

        # select the root transform node
        pm.select(root_transform_node, replace=True)

        # export the obj file
        options = "groups=0;ptgroups=0;materials=0;smoothing=1;normals=1"
        cmds.file(
            obj_path,
            force=True,
            options=options,
            type="OBJexport",
            preserveReferences=False,
            exportSelected=True
        )

        # Restore the original selection
        pm.select(original_selection, replace=True)

        # check if the file was correctly exported
        if not os.path.exists(obj_path):
            self.parent.logger.error("Failed to export OBJ file")
            return False

        sg_fields_to_update = {
            "sg_status_list": "rev",
            "task.Task.sg_status_list": "rev",
        }

        # Publish the obj file
        args = {
            "tk": self.parent.engine.sgtk,
            "context": item.context,
            "comment": item.description,
            "path": obj_path,
            "name": publish_name,
            "version_number": publish_version,
            "thumbnail_path": item.get_thumbnail_as_path(),
            "task": self.parent.engine.context.task,
            "dependency_paths": [publish_session_path],
            "sg_fields": sg_fields_to_update,
            "published_file_type": publish_file_type,
        }

        sg_publishes = sgtk.util.register_publish(**args)
        item.properties.sg_publish_data = sg_publishes
        item.properties.sg_publish_path = sg_publishes["path"]

        root_item = self.get_root_item(item)
        root_item.properties.setdefault("sg_publish_extra_data", [])

        self.parent.log_debug("Storing extra publish data on root item: %s" % root_item)
        publish_extra_data = root_item.properties["sg_publish_extra_data"]

    def finalize(self, settings, item):
        self.logger.debug("OBJ successfully published")

    def _publish_name(self, path_to_publish):
        publish_name = self._regex_replace(
            regex=r"_v\d{3}",
            source_string=os.path.basename(path_to_publish),
            replace_string="",
        )
        return publish_name

    def _regex_replace(self, regex, source_string, replace_string):
        result = ""
        pattern = re.compile(regex)
        result = pattern.sub(repl=replace_string, string=source_string)
        return result

    def get_pymel_node(self, node_name):
        pm.select(node_name)
        selection_ls = pm.ls(selection=1)
        node = selection_ls[0]

        return node

    def get_transform_node_name(self, settings):
        ctx = self.parent.engine.context
        step = ctx.step["name"]
        task = ctx.task

        transform_node = None

        # load overrides framework -----------------------------------------------------
        overrides_framework = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        ) or self.load_framework("mty-framework-valueoverrides")

        # load valid steps for exporting fbx meshes from overrides or from settings
        default_value_transform_node_name = (
            "mty.publisher.maya.3d_mesh_transform_node_name"
        )

        transform_node = overrides_framework.get_value(
            default_value_transform_node_name, link=task
        )
        self.parent.logger.info(
            f"transform_node_name: {default_value_transform_node_name}"
        )
        if not transform_node:
            self.parent.logger.info("Transform Node Name obtained from settings")
            transform_node = settings.get("Transform Node").value
        else:
            self.parent.logger.info("Transform Node Name obtained from overrides")

        return transform_node

    # ----------------------------------------------------------------------------------

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item
        return root

    # ----------------------------------------------------------------------------------

    def get_rigging_geo_grp(self, root_node):
        for node in root_node.getChildren():
            if "C_rigging_proxy_grp_0" in node.name():
                pm.parent(node, w=1)
                return node
        return False

    def parent_rigging_grp(self, root_node, rigging_geo_grp):
        if rigging_geo_grp:
            pm.parent(rigging_geo_grp, root_node)
