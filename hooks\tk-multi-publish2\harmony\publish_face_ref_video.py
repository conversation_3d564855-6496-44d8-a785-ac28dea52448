#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

import os
import re
import shutil
import traceback

from os import listdir
from os.path import isfile, join

import sgtk
from sgtk.platform.qt import QtGui, QtCore

HookBaseClass = sgtk.get_hook_baseclass()


class HarmonyPublishReviewPlugin(HookBaseClass):
    """
    Plugin for publish rendered sequence

    """

    @property
    def icon(self):
        """
        Path to an png icon on disk
        """

        # look for icon one level up from this hook's folder in "icons" folder
        return os.path.join(
            self.disk_location,
            os.pardir,
            "icons",
            "review.png"
        )

    @property
    def name(self):
        """
        One line display name describing the plugin
        """
        return "Publish face reference video"

    @property
    def description(self):
        """
        Copy/upload the render sequence to publish area
        """

        loader_url = "https://support.shotgunsoftware.com/hc/en-us/articles/219033078"

        return """
        <p>This plugin upload the video as a version in shotgun.</p>
        """

    @property
    def item_filters(self):
        """
        List of item types that this plugin is interested in.

        Only items matching entries in this list will be presented to the
        accept() method. Strings can contain glob patters such as *, for example
        ["harmony.*", "file.harmony"]
        """
        return ["harmony.face_references"]

    def accept(self, settings, item):
        """
        Method called by the publisher to determine if an item is of any
        interest to this plugin. Only items matching the filters defined via the
        item_filters property will be presented to this method.

        A submit for review task will be generated for each item accepted here. Returns a
        dictionary with the following booleans:

            - accepted: Indicates if the plugin is interested in this value at
                all. Required.
            - enabled: If True, the plugin will be enabled in the UI, otherwise
                it will be disabled. Optional, True by default.
            - visible: If True, the plugin will be visible in the UI, otherwise
                it will be hidden. Optional, True by default.
            - checked: If True, the plugin will be checked in the UI, otherwise
                it will be unchecked. Optional, True by default.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process

        :returns: dictionary with boolean keys accepted, required and enabled.
            This plugin makes use of the tk-multi-reviewsubmission app; if this
            app is not available then the item will not be accepted, this method
            will return a dictionary with the accepted field that has a value of
            False.

            Several properties on the item must also be present for it to be
            accepted. The properties are 'path', 'publish_name', 'color_space',
            'first_frame' and 'last_frame'
        """

        publisher = self.parent
        engine = publisher.engine
        context = engine.context

        return {"accepted": True,
                "checked": True}


    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish. Returns a
        boolean to indicate validity.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        :returns: True if item is valid and not in proxy mode, False otherwise.
        """

        ctx       = self.parent.context
        publisher = self.parent
        path      = _session_path()

        # ---- ensure the session has been saved
        if not path:
            # the session still requires saving. provide a save button.
            # validation fails.
            error_msg = "The Harmony session has not been saved."
            #self.logger.error(error_msg, extra=_get_save_as_action())
            raise Exception(error_msg)

        asset_id   = ctx.entity['id']
        asset_name = ctx.entity['name']
        path       = sgtk.util.ShotgunPath.normalize(path)

        # Validating frames
        if 'face_reference_nums' not in item.properties.keys():
            raise Exception('Missing face references. Check collector')
        elif not len(item.properties['face_reference_nums']):
            raise Exception('Missing face references')

        # Validating templates
        for k in ['template_faceRef_video_publish', 'template_faceRef_work']:
            if k not in item.properties.keys():
                raise Exception('Missing Harmony face reference templates, publish and work.')

        # Checking work area info
        if 'work_fields' not in item.properties.keys():
            raise Exception('Invalid work area info. Check collector')
        elif not len(item.properties['work_fields']):
            raise Exception('Invalid work area info.')

        # Checking images found
        image_numbers = item.properties['face_reference_nums']
        if len(image_numbers) in [0, 1]:
            msg = 'Not enough frames to create a video. Total images: {}'
            raise Exception(msg.format(len(image_numbers)))

        for i in range(len(image_numbers)):
            if i == 0:
                continue
            current_num = image_numbers[i]
            if current_num != image_numbers[i-1]+1:
                msg = 'Missing reference frame: {}'
                raise Exception(msg.format(current_num))

        return True


    def publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        #   Video process
        self.logger.info("Publishing Face references.")
        self.parent.log_info("Publishing Face references.")

        engine    = sgtk.platform.current_engine()
        ctx       = self.parent.context
        publisher = self.parent

        # Get item properties -------------------------------------------
        face_ref_found           = item.properties['face_reference_nums']
        publish_faceRef_template = item.properties['template_faceRef_video_publish']
        publish_faceRef_seq_tpl  = item.properties['template_faceRef_seq_publish']
        work_faceRef_template    = item.properties['template_faceRef_work']
        work_fields              = item.properties['work_fields']
        render_publish_path      = ''

        # Assigning variables to video creation
        work_fields['shape.frame'] = 0
        review_publish_path = publish_faceRef_template.apply_fields(work_fields)

        padding        = int(work_faceRef_template.keys['shape.frame'].format_spec)
        str_num        = str(work_fields['shape.frame']).zfill(padding)
        work_face_path = work_faceRef_template.apply_fields(work_fields)
        work_face_path = work_face_path.replace(
            '_{}.'.format(str_num), '_%0{}d.'.format(padding))

        publish_seq_path = publish_faceRef_seq_tpl.apply_fields(work_fields)
        publish_seq_path = publish_seq_path.replace(
            '_{}.'.format(str_num), '_%0{}d.'.format(padding))

        # Ensure folder exists
        publish_folder = os.path.dirname(review_publish_path)
        if not os.path.exists(publish_folder):
            self.parent.engine.ensure_folder_exists(publish_folder)

        sg_proj = self.get_sg_project_info(
            [
                'sg_working_color_space',
                'sg_fps',
            ]
        )

        # its important that the video filter goes
        # last to preserve the color matrix
        ffmpeg_tpl   = "-start_number {FRAME}"
        ffmpeg_tpl  += " -r {FRAME_RATE}"
        ffmpeg_tpl  += " -i {SEQ}"
        ffmpeg_tpl  += " -qscale 0"
        ffmpeg_tpl  += " -vcodec mpeg4"
        ffmpeg_tpl  += " -r {FRAME_RATE}"
        ffmpeg_tpl  += " -y"
        ffmpeg_tpl  += " {VIDEO}"
        # ffmpeg -start_number n -i test_%d.jpg -vcodec mpeg4 test.avi

        ffmpeg_cmd   = ffmpeg_tpl.format(
            FRAME=face_ref_found[0],
            SEQ  =work_face_path,
            VIDEO=review_publish_path,
            FRAME_RATE=sg_proj.get("sg_fps", 24),
        )

        ffmpeg_framework = self.load_framework("mty-framework-ffmpeg")
        ffmpeg_framework.ffmpegCore.set_binary(binary='convert')

        _err, _info = ffmpeg_framework.ffmpegCore.execute_command(ffmpeg_cmd)
        if _err:
            message = "Failed to create video: {}\ncommand: {}"
            raise Exception(message.format(_info, ffmpeg_cmd))

        item_name = self.get_publish_name(settings, item)

        # Remove version token and sequence token
        pattern1 = re.compile(
            r"(?P<basename>.+)(?P<remove>_v\d{3}\.%\d{2}d)(?P<extension>\.\w{3})"
        )
        pattern2 = re.compile(
            r"(?P<basename>.+)(?P<remove>_v\d{3}\.\d{4})(?P<extension>\.\w{3})"
        )

        match = re.match(pattern1, item_name) or re.match(pattern2, item_name)

        if match:
            item_name = "{}{}".format(
                match.groupdict().get("basename"),
                ".mov",
            )


        #   Publish video process
        args = {
            "tk":               self.parent.engine.sgtk,
            "context":          item.context,
            "comment":          item.description,
            "path":             review_publish_path,
            "name":             item_name,
            "version_number":   work_fields['version'],
            "thumbnail_path":   item.get_thumbnail_as_path(),
            "task":             self.parent.engine.context.task,
            "dependency_paths": [publish_seq_path],
            "sg_fields":        {"sg_status_list": "rev"},
            "published_file_type": "Media Review"
        }

        sg_publishes = sgtk.util.register_publish(**args)

        item.properties.sg_publish_data = sg_publishes
        item.properties.sg_publish_path = sg_publishes["path"]["local_path"]

        self.parent.log_debug(
            "Adding existing publish " +
            "(id:{}) ".format(sg_publishes['id']) +
            "as extra data for item: {}".format(item)
        )

        #   Version process Ref publish_shot_media_review.py from maya/layout
        sg_version = self.submit_version(
            path_to_frames = publish_seq_path,
            path_to_movie  = review_publish_path,
            sg_publishes   = [sg_publishes],
            sg_task        = self.parent.engine.context.task,
            comment        = item.description,
            first_frame    = face_ref_found[0],
            last_frame     = face_ref_found[-1]
        )

        # Upload in a new thread and make our own event loop
        # to wait for the thread to finish.

        event_loop = QtCore.QEventLoop()
        thread = UploaderThread(
            self.parent, sg_version,
            review_publish_path,
            item.get_thumbnail_as_path(),
            True
        )

        thread.finished.connect(event_loop.quit)
        thread.start()
        event_loop.exec_()

        # log any errors generated in the thread
        for e in thread.get_errors():
            self.parent.log_error(e)

        # Update task status for revision
        sg_task = self.parent.context.task
        self.parent.logger.info(
            "End of Publish and updating task with id: {} to status: 'rev'".format(
                str(sg_task['id'])
            )
        )

        try:
            self.parent.engine.shotgun.update(
                "Task", sg_task["id"], {"sg_status_list": 'rev'}
            )
        except:
            pass


    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        self.logger.debug('Item video successfully published')

        # TODO- crate next version

    def get_sg_shot_info(self, shot_fields):
        engine         = self.parent.engine
        sg             = engine.shotgun
        sg_proj        = engine.context.project

        context_tokens = str(engine.context).split(' ')
        entity_name    = context_tokens[2]
        shot_filter    = [['project', 'is', sg_proj],
                          ['code', 'is', entity_name]]

        sg_shot = sg.find_one('Shot', shot_filter, shot_fields)
        return sg_shot

    def get_sg_project_info(self, proj_fields):
        engine = self.parent.engine
        sg = engine.shotgun
        sg_proj = engine.context.project

        proj_filter = [['id', 'is', sg_proj['id']]]

        sg_proj = sg.find_one('Project', proj_filter, proj_fields)
        return sg_proj

    def get_root_item(self, item):

        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root

    def submit_version(self, path_to_frames, path_to_movie, sg_publishes,
                       sg_task, comment, first_frame, last_frame):
        """
        Create a version in Shotgun for this path and linked to this publish.
        """

        # get current shotgun user
        current_user = self.parent.engine.context.user

        # create a name for the version based on the file name
        # grab the file name, strip off extension
        name = os.path.splitext(os.path.basename(path_to_movie))[0]
        # do some replacements and capitalize
        name = name.replace("_", " ").capitalize()

        LinkFolder = {'local_path': os.path.dirname(path_to_frames) + os.sep,
                      'content_type': None,
                      'link_type': 'local',
                      'name': name}

        LinkFile   = {'local_path': path_to_movie,
                      'content_type': None,
                      'link_type': 'local',
                      'name': name}

        entity = self.parent.engine.context.entity
        proj   = self.parent.engine.context.project
        # Create the version in Shotgun
        data = {
            "code":                 name,
            "sg_status_list":       "rev",
            "entity":               entity,
            "sg_task":              sg_task,
            "sg_version_type":      "Production",
            "sg_first_frame":       first_frame,
            "sg_last_frame":        last_frame,
            "frame_count":          (last_frame - first_frame + 1),
            "frame_range":          "%s-%s" % (first_frame, last_frame),
            "sg_frames_have_slate": False,
            "published_files":      sg_publishes,
            "created_by":           current_user,
            "description":          comment,
            "sg_path_to_frames":    path_to_frames,
            "sg_path_to_movie":     path_to_movie,
            "sg_movie_has_slate":   False,
            "project":              proj,
            "user":                 current_user
        }

        sg_version = self.parent.engine.shotgun.create("Version", data)
        self.parent.log_debug("Created version in shotgun: %s" % str(data))

        return sg_version



class UploaderThread(QtCore.QThread):
    """
    Simple worker thread that encapsulates uploading to shotgun.
    Broken out of the main loop so that the UI can remain responsive
    even though an upload is happening
    """
    def __init__(self, app, version, path_to_movie, thumbnail_path, upload_to_shotgun):
        QtCore.QThread.__init__(self)
        self._app               = app
        self._version           = version
        self._path_to_movie     = path_to_movie
        self._thumbnail_path    = thumbnail_path
        self._upload_to_shotgun = upload_to_shotgun
        self._errors            = []

    def get_errors(self):
        """
        can be called after execution to retrieve a list of errors
        """
        return self._errors

    def run(self):
        """
        Thread loop
        """
        upload_error = False

        if self._upload_to_shotgun:
            try:
                self._app.tank.shotgun.upload("Version", self._version["id"], self._path_to_movie, "sg_uploaded_movie")
            except:
                self._errors.append(
                    "Movie upload to Shotgun failed: {}".format(traceback.print_exc())
                )
                upload_error = True

        if not self._upload_to_shotgun or upload_error:
            try:
                self._app.tank.shotgun.upload_thumbnail("Version", self._version["id"], self._thumbnail_path)
            except:
                self._errors.append(
                    "Thumbnail upload to Shotgun failed: {}".format(traceback.print_exc())
                )


def _session_path():
    """
    Return the path to the current session
    :return:
    """
    engine = sgtk.platform.current_engine()

    # get the path to the current file
    path = engine.app.get_current_project_path()

    if isinstance(path, unicode):
        path = path.encode("utf-8")

    return path


