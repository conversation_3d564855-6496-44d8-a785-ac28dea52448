# -*- coding: utf-8 -*-
# Standard library:
import pprint
import os
import sys
import json
#   .   .   .   .   .   .   .   .   .   .   .
# Third party:
import sgtk
from tank_vendor import six
from tank.platform.qt import QtCore, QtGui
import maya.cmds as cmds
import maya.mel as mel

QApplication = QtGui.QApplication
QDialog = QtGui.QDialog
QVBoxLayout = QtGui.QVBoxLayout
QLabel = QtGui.QLabel
QLineEdit = QtGui.QLineEdit
QListWidget = QtGui.QListWidget
QDialogButtonBox = QtGui.QDialogButtonBox


#   .   .   .   .   .   .   .   .   .   .   .
# Project:
pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)
# ================================================================
HookBaseClass = sgtk.get_hook_baseclass()


class TransferActions(HookBaseClass):

    def generate_actions(
            self, sg_publish_data, actions, ui_area
    ):
        inherent = super(TransferActions, self)
        action_instances = \
            inherent.generate_actions(
                sg_publish_data, actions, ui_area
            )

        if "gpu_cache" in actions:
            action_instances.append(
                {
                    "name": "gpu_cache",
                    "params": None,
                    "caption": "Create GPU Cache Node",
                    "description":
                        "Creates an GPU Cache Node " +
                        "for the selected item..",
                }
            )

        if "environment_overrides" in actions:
            action_instances.append(
                {
                    "name": "environment_overrides",
                    "params": None,
                    "caption": "Load Environment Overrides",
                    "description":
                        "Override transforms for Environment GPU Cache Nodes",
                }
            )

        if "library_sequence_texture_node" in actions:
            action_instances.append(
                {
                    "name": "library_sequence_texture_node",
                    "params": None,
                    "caption": "Create Sequence Texture",
                    "description":
                        "Creates a Sequence Texture " +
                        "node for the entire Library..",
                }
            )

        if "audio" in actions:
            action_instances.append(
                {
                    "name": "audio",
                    "params": None,
                    "caption": "Create Audio Node",
                    "description":
                        "Creates an Audio Node for the " +
                        "selected item..",
                }
            )

        if "reference_without_namespace" in actions:
            action_instances.append(
                {
                    "name": "reference_without_namespace",
                    "params": None,
                    "caption":
                        "Reference without a namespace",
                    "description":
                        "Create an asset reference " +
                        "without a namespace."
                }
            )

        if "reference_with_custom_namespace" in actions:
            action_instances.append(
                {
                    "name":
                        "reference_with_custom_namespace",
                    "params":
                        None,
                    "caption":
                        "Reference with custom namespace",
                    "description":
                        "Create an asset reference " +
                        "with custom a namespace."
                }
            )

        if "download_publish" in actions:
            action_instances.append(
                {
                    "name":
                        "download_publish",
                    "params":
                        None,
                    "caption":
                        "Download publish",
                    "description":
                        "Ensure the file exists locally, " +
                        "downloading it if necesary...",
                }
            )

        return action_instances

    # ================================================================

    def execute_action(self, name, params, sg_publish_data):

        path = six.ensure_str(self.get_publish_path(sg_publish_data))
        path = self.ensure_file_is_local(path, sg_publish_data)

        super(TransferActions, self).execute_action(
            name, params, sg_publish_data)

        #        -----------------------------       #

        if name == "gpu_cache":
            self.load_gpu_cache(path, sg_publish_data)

        if name == "audio":
            self.load_audio(path, sg_publish_data)

        if name == "download_publish":
            self.ensure_file_is_local(path, sg_publish_data)

        if name == "reference_with_custom_namespace":
            namespace = params[0] if params else None
            self.create_reference_with_custom_namespace(
                path, sg_publish_data, namespace=namespace
            )

        if name == "reference_without_namespace":
            self.create_reference_without_namespace(
                path, sg_publish_data
            )

        if name == "library_sequence_texture_node":
            self.create_library_sequence_texture_node(
                path, sg_publish_data
            )

        if name == "environment_overrides":
            self.apply_environment_overrides(
                path, sg_publish_data
            )

    # ================================================================

    def ensure_file_is_local(self, path, publish):
        if not hasattr(self, 'metasync'):
            self.metasync = self.parent.engine.custom_frameworks.get(
                "mty-framework-metasync"
            )
            if not self.metasync:
                self.metasync = self.load_framework("mty-framework-metasync")

        transfersManager = self.metasync.transfersManager
        if "%" in path:
            dirname = os.path.dirname(path)
            if os.path.exists(dirname) and \
                    self._collect_sequenced_files(path):
                transfersManager \
                    .ensure_local_dependencies(publish)
                return path
        else:
            if os.path.exists(path):
                transfersManager \
                    .ensure_local_dependencies(publish)
                return path

        transfersManager.ensure_file_is_local(path, publish)
        transfersManager.ensure_local_dependencies(publish)

        return path

    # ================================================================

    def _create_reference(self, path, sg_publish_data):
        file_path = self.ensure_file_is_local(
            path, sg_publish_data
        )

        self.parent.log_debug("filepath: %s\npublish_data:\n%s" % (
            file_path, pprint.pformat(sg_publish_data)))

        namespace = sg_publish_data['entity']['name']
        namespace = namespace.replace(" ", "_").replace(".", "_")

        cmds.file(
            path,
            reference=True,
            loadReferenceDepth="all",
            mergeNamespacesOnClash=False,
            namespace=namespace,
        )

    # ================================================================

    def create_reference_with_custom_namespace(
            self, path, sg_publish_data, namespace=None
    ):
        """
        Creates a reference with a custom namespace.

        :param path: The path to the file to reference.
        :param sg_publish_data: The Shotgun data dictionary with all the standard
            publish fields.
        :param namespace: The desired namespace for the reference. If not provided,
            a dialog will be launched to ask for it.

        :raises Exception: If the file is not found on disk or if no namespace was
            provided.
        """
        if not namespace:
            # launch dialog to ask for the desired namespace in case it's not provided
            dialog = InputDialog()
            if dialog.exec_() == QDialog.Accepted:
                namespace = dialog.get_input_text()
                self.parent.engine.logger.info(
                    "Desired namespace: {}".format(namespace)
                )

            if not namespace:
                msg = "No namespace was provided."
                self.show_message(msg, icon="Warning")
                return
                # raise Exception("No namespace was provided.")

        file_path = self.ensure_file_is_local(path, sg_publish_data)

        if not os.path.exists(path):
            raise Exception("File not found on disk - '%s'" % path)

        namespace = namespace.replace(" ", "_").replace(".", "_")
        cmds.file(
            path,
            reference=True,
            loadReferenceDepth="all",
            mergeNamespacesOnClash=False,
            namespace=namespace,
            sharedNodes="displayLayers"
        )

    # ================================================================

    def create_reference_without_namespace(
            self, path, sg_publish_data
    ):
        file_path = self.ensure_file_is_local(path, sg_publish_data)

        if not os.path.exists(path):
            raise Exception("File not found on disk - '%s'" % path)

        # Now create the reference object in Maya.
        # attempting to not use a namespace
        cmds.file(
            path,
            reference=True,
            loadReferenceDepth="all",
            mergeNamespacesOnClash=True,
            namespace=":",
            sharedNodes="displayLayers"
        )

    # ================================================================

    def _do_import(self, path, sg_publish_data):
        file_path = self.ensure_file_is_local(path, sg_publish_data)

        super(TransferActions, self). \
            _do_import(file_path, sg_publish_data)

    # ================================================================

    def load_gpu_cache(self, path, sg_publish_data):
        file_path = self.ensure_file_is_local(path, sg_publish_data)
        message = "filepath: %s\npublish_data:\n%s"
        self.parent.log_debug(message % (
            file_path, pprint.pformat(sg_publish_data)))

        if 'gpuCache' not in \
                cmds.pluginInfo(query=True, listPlugins=True):
            cmds.loadPlugin('gpuCache')

        name = sg_publish_data['entity']['name']
        transform = cmds.createNode('transform', name=name)
        gpuShape = cmds.createNode(
            'gpuCache', name="%sShape" % name, parent=transform)

        cmds.setAttr(
            '%s.cacheFileName' % gpuShape,
            file_path, type='string'
        )

    # ================================================================

    def apply_environment_overrides(self, path, sg_publish_data):
        file_path = self.ensure_file_is_local(path, sg_publish_data)
        message = "filepath: %s\npublish_data:\n%s"
        self.parent.log_debug(message % (
            file_path, pprint.pformat(sg_publish_data)))

        # first load the json and store the data
        with open(path, 'r') as json_file:
            overrides_data = json.load(json_file)

            matrix = overrides_data['parameters']['xform']['matrix']
            rotatePivot = overrides_data['parameters']['xform']['rotatePivot']
            scalePivot = overrides_data['parameters']['xform']['scalePivot']
            metadata = overrides_data['metadata']['mighty']

            # now list all gpu cache nodes, and find the correct one

            gpu_caches = cmds.ls(type='gpuCache')

            for gpu_node in gpu_caches:
                cache_path = cmds.getAttr("%s.cacheFileName" % gpu_node)

                fields = ['entity.Asset.sg_asset_type',
                          'entity.Asset.code',
                          'published_file_type',
                          'published_file_type.PublishedFileType.code']

                publish_entities = sgtk.util.find_publish(self.parent.sgtk,
                                                          [cache_path],
                                                          fields=fields)

                if not publish_entities:
                    continue

                # we expect that only one result comes back
                publish_entity = publish_entities[cache_path]

                env_asset_type = metadata['asset_type']
                asset_type = publish_entity.get('entity.Asset.sg_asset_type')

                self.parent.log_debug('Asset type: %s' % asset_type)

                if not asset_type or asset_type != env_asset_type:
                    continue

                env_asset_name = metadata['asset_name']
                asset_name = publish_entity.get('entity.Asset.code')

                self.parent.log_debug('Asset name: %s' % asset_name)

                if not asset_name or asset_name != env_asset_name:
                    continue

                transform = cmds.listRelatives(gpu_node, parent=True)
                if cmds.nodeType(transform) != 'transform':
                    raise Exception("Incorrect parent type for GPU Cache node")

                # at this point, we should accept this node as the correct one

                cmds.xform(transform, worldSpace=False, matrix=matrix)
                cmds.xform(transform, worldSpace=False, rotatePivot=rotatePivot)
                cmds.xform(transform, worldSpace=False, scalePivot=scalePivot)

                break

    # ================================================================

    def create_library_sequence_texture_node(
            self, path, sg_publish_data
    ):
        self._ensure_sync_modules()
        node_name = None
        message = "Loading CustomShape library sequence for:\n%s"

        if sg_publish_data['entity']['type'] == 'Blendshape':

            library_publish_frames = \
                self._collect_customshape_library_publishes(
                    [sg_publish_data]
                )

            for lib_pub_frame in library_publish_frames:
                self.ensure_file_is_local(
                    lib_pub_frame['path']['local_path'],
                    lib_pub_frame
                )

        file_node = \
            cmds.shadingNode("file", name=node_name, asTexture=True)

        cmds.setAttr(
            "%s.fileTextureName" % file_node, path, type="string"
        )
        cmds.setAttr("%s.useFrameExtension" % file_node, 1)

        return file_node

    # ================================================================

    def load_audio(self, path, sg_publish_data):
        file_path = self.ensure_file_is_local(path, sg_publish_data)
        message = "filepath: {0}\npublish_data:\n{1}"
        # self.parent.logger.info(
        #     message.format(file_path, pf(sg_publish_data))
        # )
        name = "{}_audio".format(sg_publish_data['entity']['name'])

        # Delete existing audio nodes
        audio_nodes = cmds.ls(type="audio", long=True)
        if audio_nodes:
            cmds.delete(audio_nodes)

        audio = cmds.createNode('audio', name=name)
        cmds.setAttr('{}.filename'.format(audio), file_path, type='string')
        offset = None
        entity = sg_publish_data['entity']

        if entity['type'] == 'Sequence':

            #        -----------------------------       #

            python_modules_path = os.path.join(
                self.disk_location, "external_python_modules"
            )
            self.parent.logger.info(
                "python_modules_path: {}".format(python_modules_path)
            )
            sys.path.append(python_modules_path)
            from timecode import Timecode

            #        -----------------------------       #

            filters = [['entity', 'is', entity]]
            order = [{'field_name': 'id', 'direction': 'desc'}]
            fields = ['timecode_start_text', 'fps']
            cut = self.parent.shotgun.find_one('Cut', filters, fields, order=order)

            timecode_start = Timecode(cut['fps'], cut['timecode_start_text'])
            offset = timecode_start.frames

        elif entity['type'] == 'Shot':
            filters = [['id', 'is', entity['id']]]
            shot = self.parent.shotgun.find_one(
                'Shot', filters, ['sg_cut_in']
            )

            offset = shot['sg_cut_in']

        if offset:
            cmds.setAttr('{}.offset'.format(audio), offset)

        aPlayBackSliderPython = mel.eval('$tmpVar=$gPlayBackSlider')
        cmds.timeControl(
            aPlayBackSliderPython, e=True,
            sound=audio, displaySound=True
        )

    # ================================================================

    def select_camera_for_image_plane(self, path, sg_publish_data):
        """
        Open a dialog to select a camera for image plane import.

        :param path: Path to image file
        :param sg_publish_data: Shotgun publish data dictionary
        :returns: Selected camera name or None
        """
        app = self.parent

        # Ensure a QApplication exists
        qt_app = QApplication.instance()
        if not qt_app:
            qt_app = QApplication([])

        # Get all cameras in the scene with robust detection
        def get_camera_info():
            cameras = cmds.ls(type='camera')
            camera_info = []
            for cam in cameras:
                try:
                    cam_transform = cmds.listRelatives(cam, parent=True)[0]

                    # Additional camera diagnostics
                    cam_type = cmds.getAttr(f"{cam}.orthographic")
                    cam_type_str = "Orthographic" if cam_type else "Perspective"

                    camera_info.append({
                        'shape': cam,
                        'transform': cam_transform,
                        'display_name': cam_transform,
                        'type': cam_type_str
                    })

                except Exception:
                    camera_info.append({
                        'shape': cam,
                        'transform': cam,
                        'display_name': cam,
                        'type': 'Unknown'
                    })
            return camera_info

        camera_info_list = get_camera_info()

        # If no cameras, use perspective view
        if not camera_info_list:
            camera_info_list = [{
                'shape': 'perspShape',
                'transform': 'persp',
                'display_name': 'persp',
                'type': 'Perspective'
            }]

        # Camera Selection Dialog with modal blocking
        class CameraSelectionDialog(QDialog):
            def __init__(self, camera_info, parent=None):
                super().__init__(parent)
                self.setWindowTitle("Select Camera for Image Plane")
                self.setMinimumWidth(300)
                self.setMinimumHeight(400)

                self.camera_info = camera_info
                self.selected_camera = None

                layout = QVBoxLayout(self)

                label = QLabel("Please select a camera for the image plane:")
                layout.addWidget(label)

                self.camera_list = QListWidget()
                camera_display_items = [
                    f"{info['display_name']} ({info['type']})"
                    for info in camera_info
                ]
                self.camera_list.addItems(camera_display_items)
                self.camera_list.itemSelectionChanged.connect(self.validate_selection)
                layout.addWidget(self.camera_list)

                button_box = QDialogButtonBox(
                    QDialogButtonBox.Ok | QDialogButtonBox.Cancel
                )
                button_box.accepted.connect(self.accept)
                button_box.rejected.connect(self.reject)

                # Initially disable OK button
                button_box.button(QDialogButtonBox.Ok).setEnabled(False)
                layout.addWidget(button_box)

                self.button_box = button_box

            def validate_selection(self):
                # Enable OK button only when an item is selected
                selected_items = self.camera_list.selectedItems()
                self.button_box.button(QDialogButtonBox.Ok).setEnabled(bool(selected_items))

                if selected_items:
                    # Extract original display name without type
                    selected_name = selected_items[0].text().split(' (')[0]
                    self.selected_camera = selected_name

            def get_selected_camera(self):
                return self.selected_camera

        # Create and show dialog
        dialog = CameraSelectionDialog(camera_info_list)
        dialog.setModal(True)

        # Show dialog and wait for user interaction
        result = dialog.exec_()

        # Check dialog result
        if result == QDialog.Accepted:
            selected_camera = dialog.get_selected_camera()
            if selected_camera:
                app.logger.info(f"Camera selected for image plane: {selected_camera}")
                return selected_camera

        app.logger.info("Camera selection cancelled.")
        return None

    def import_image_plane(self, path, sg_publish_data, camera_name=None):
        """
        Create an image plane for a specific camera

        :param path: Path to image file or sequence
        :param sg_publish_data: Shotgun publish data dictionary
        :param camera_name: Optional camera name to import image plane on
        :returns: The newly created image plane
        """
        app = self.parent

        # Log initial state
        app.logger.info("Starting image plane import")
        app.logger.info(f"Input path: {path}")
        app.logger.info(f"Specified camera: {camera_name}")

        # Handle sequence path
        import os
        import glob

        # Check if path contains sequence marker
        if '%04d' in path:
            # Try to find an actual file in the sequence
            base_path = path.replace('%04d', '*')
            matching_files = glob.glob(base_path)

            if matching_files:
                # Use the first matching file
                path = matching_files[0]
                app.logger.info(f"Resolved sequence path to: {path}")
            else:
                app.logger.error(f"No files found matching sequence: {base_path}")
                return None

        # Validate path
        if not os.path.isfile(path):
            app.logger.error(f"Image file does not exist: {path}")
            return None

        # If no camera specified, use camera selection dialog
        if not camera_name:
            camera_name = self.select_camera_for_image_plane(path, sg_publish_data)
            if not camera_name:
                app.logger.info("No camera selected. Aborting image plane import.")
                return None

        # Validate camera exists
        if not cmds.objExists(camera_name):
            app.logger.error(f"Camera {camera_name} does not exist")
            return None

        # Find camera shape
        try:
            camera_shape = cmds.listRelatives(camera_name, shapes=True, type='camera')[0]
            app.logger.info(f"Found camera shape: {camera_shape}")
        except Exception as e:
            app.logger.error(f"Could not find camera shape: {e}")
            return None

        # Create image plane
        try:
            # Clear any existing image planes on this camera
            existing_image_planes = cmds.listConnections(f"{camera_shape}.imagePlane", source=True, destination=False) or []
            for ip in existing_image_planes:
                try:
                    cmds.delete(ip)
                except Exception:
                    pass

            # Create new image plane
            image_plane = cmds.imagePlane(
                name=f"{camera_shape}_imagePlane",
                camera=camera_shape,
                fileName=path
            )[0]

            app.logger.info(f"Created image plane {image_plane} on camera {camera_name}")

            # Image sequence
            cmds.setAttr(f"{image_plane}.useFrameExtension", 1)

            # Get the shot from the current context instead of the image plane entity
            if app.context.entity and app.context.entity['type'] == 'Shot':
                entity = app.context.entity

                filters = [['id', 'is', entity['id']],
                           ['project', 'is', app.context.project]]
                shot = app.shotgun.find_one('Shot', filters, ['project', 'sg_cut_in', 'sg_cut_out'])

                if shot and 'sg_cut_in' in shot:
                    # Calculate the offset to align the first frame of the image
                    # with the first frame of the shot
                    # If sg_cut_in is 1, the offset should be 0
                    offset = 1 - shot['sg_cut_in']

                    # Set the image plane offset
                    cmds.setAttr(f"{image_plane}.frameOffset", offset)
                    app.logger.info(f"Set image plane offset to: {offset}")

            # Ghost the selected objects
            cmds.ghosting(image_plane, action="ghost", preFrames=0, postFrames=0)

            return image_plane

        except Exception as e:
            app.logger.error(f"Failed to create image plane: {e}")
            return None

    # ================================================================

    def _create_image_plane(self, path, sg_publish_data):
        """
        Prevent automatic image plane creation.
        This method should now only return path and publish data.

        :param path: Path to image file
        :param sg_publish_data: Shotgun publish data dictionary
        :returns: Dictionary with path and publish data
        """
        app = self.parent

        # Log the attempt to create image plane
        app.logger.info("Preventing automatic image plane creation")
        app.logger.info(f"Path: {path}")

        # # Return path and publish data for later use
        # return {
        #     "path": path,
        #     "sg_publish_data": sg_publish_data
        # }

        app.logger.info(f"Wrapping to custom method...")
        self.import_image_plane(path, sg_publish_data)


    # ================================================================

    def _collect_sequenced_files(self, sequence_path):
        folder_path = os.path.dirname(sequence_path)
        name, ext = os.path.splitext(sequence_path)
        folder_files = os.listdir(folder_path)
        sequence_files = []

        if len(folder_files) > 0:
            for file in sorted(folder_files):
                if file.endswith(ext):
                    path = os.path.join(folder_path, file)
                    sequence_files.append(path)

        return sequence_files

    # ================================================================

    def _create_udim_texture_node(self, path, sg_publish_data):
        """
        Create a file texture node for a UDIM (Mari) texture
        :param path:             Path to file.
        :param sg_publish_data:  Shotgun data dictionary with all the standard publish fields.
        :returns:                The newly created file node
        """
        # create the normal file node:

        if "%04d" in path:
            path = path.replace("%04d", "<UDIM>")

        file_node = self._create_texture_node(path, sg_publish_data)
        if file_node:
            # path is a UDIM sequence so set the uv tiling mode to 3 ('UDIM (Mari)')
            cmds.setAttr("%s.uvTilingMode" % file_node, 3)
            # and generate a preview:
            mel.eval("generateUvTilePreview %s" % file_node)

        return file_node

    # ================================================================

    def show_message(self, msg, icon=None):
        """
        Displays a message box with a specified message and icon.

        This method creates a QMessageBox to display a message to the user.
        The appearance of the message box can be customized using the 'icon' parameter.

        Args:
            msg (str): The message to be displayed in the message box.
            icon (str, optional): The icon to be displayed in the message box.
                Valid options are "NoIcon", "Question", "Information", "Warning", and "Critical".
                Defaults to "NoIcon" if not provided or invalid.
        """

        from sgtk.platform.qt import QtCore, QtGui

        icons_dict = {
            "NoIcon": QtGui.QMessageBox.NoIcon,
            "Question": QtGui.QMessageBox.Question,
            "Information": QtGui.QMessageBox.Information,
            "Warning": QtGui.QMessageBox.Warning,
            "Critical": QtGui.QMessageBox.Critical,
        }

        if not icon or icon not in icons_dict.keys():
            icon_selection = icons_dict["NoIcon"]
        else:
            icon_selection = icons_dict[icon]

        msg_error = QtGui.QMessageBox()
        msg_error.setWindowTitle("Finished execution.")
        msg_error.setText(msg)
        msg_error.setIcon(icon_selection)
        msg_error.raise_()
        msg_error.activateWindow()
        msg_error.exec_()


# ======================================================================================


class InputDialog(QDialog):
    def __init__(self, parent=None):
        super(InputDialog, self).__init__(parent)
        self.setWindowTitle("Input Namespace")

        layout = QVBoxLayout(self)
        label = QLabel("Enter desired namespace:")
        self.text_input = QLineEdit()
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)

        layout.addWidget(label)
        layout.addWidget(self.text_input)
        layout.addWidget(button_box)

        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)

    def get_input_text(self):
        return self.text_input.text()
