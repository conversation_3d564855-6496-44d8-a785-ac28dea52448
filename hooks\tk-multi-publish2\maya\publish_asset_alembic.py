# -*- coding: utf-8 -*-
import os
import json
import re
import traceback

# ___   ___   ___   ___   ___   ___  ___
# Third party:
import sgtk
import maya.mel as mel
import maya.cmds as cmds
import pymel.core as pm

HookBaseClass = sgtk.get_hook_baseclass()

class AlembicAssetPublishPlugin(HookBaseClass):
    def __init__(self, parent):
        super(AlembicAssetPublishPlugin, self).__init__(parent)
        if not pm.pluginInfo("AbcExport", loaded=True, query=True):
            pm.loadPlugin("AbcExport")

        self.time = self._get_shot_time()
        self.development = False

    @property
    def icon(self):
        return os.path.join(
            self.disk_location,
            os.pardir,
            "icons",
            "publish_alembic.png"
        )

    @property
    def name(self):
        return "Publish Asset alembic File"


    @property
    def description(self):
        return (
            'This plugin will save alembic caches for specific asset.'
        )

    @property
    def item_filters(self):
        return ["maya.session.asset.alembic"]


    @property
    def settings(self):
        plugin_settings= super(AlembicAssetPublishPlugin,self).settings or {}

        asset_alembic_settings= {
        "Work Template":{
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                               "correspond to a template defined in "
                               "templates.yml."
                               },

        "Primary Publish Template": {
                "type": "template",
                "default": None,
                "description": "Maya publish session"
                },

        "Alembic Publish Type":{
                "type": "string",
                "default":  "Alembic Cache",
                "description": "Publish File Type"
                },


        "Alembic Publish Template":{
                "type": "template",
                "default": None,
                "description": ""
                },

        "Transform Node":{
                "type": "string",
                "default": "model_high_root",
                "description": ""
                },

        "Valid Steps":{
                "type": "list",
                "default": None,
                "description": ""
                }

        }
        # update the base settings
        plugin_settings.update(asset_alembic_settings)
        return plugin_settings

    # ---------------------------------------------------------------------------


    def accept(self, settings, item):
        item.properties['accepted'] = False
        engine= sgtk.platform.current_engine()
        ctx= engine.context
        step      =ctx.step["name"]

        valid_steps=  settings["Valid Steps"].value

        if step.lower() in valid_steps:
            return {'accepted': True, 'checked': True}
        else:

            return {'accepted': False}



    # ---------------------------------------------------------------------------

    def validate(self, settings, item):
        transform_node= settings.get("Transform Node").value

        if cmds.objExists(transform_node):
            self.parent.logger.info("{0} exists".format(transform_node))
            result = True

        else:
            message = " {0} not exist in the scene. plese create this node and try again!".format(transform_node)
            raise Exception(message)

        return result

    # ---------------------------------------------------------------------------


    def publish(self,settings, item):
        #General Data
        publisher = self.parent
        ctx       = publisher.context
        step      = ctx.step["name"]

        #Settings
        work_template_name     = settings["Work Template"].value
        publish_template_name  = settings["Primary Publish Template"].value
        alembic_template_name  = settings["Alembic Publish Template"].value

        #Publish File Type
        publish_file_type      = settings["Alembic Publish Type"].value

        #Extras
        model_high_root_node_name = settings.get("Transform Node").value
        model_high_root_node= self.get_pymel_node(model_high_root_node_name)


        #Templates
        work_template    = self.parent.engine.get_template_by_name(work_template_name)
        publish_template = self.parent.engine.get_template_by_name(publish_template_name)
        alembic_template = self.parent.engine.get_template_by_name(alembic_template_name)

        scene_path = os.path.abspath(cmds.file(query=True, sn=True))
        fields = work_template.get_fields(scene_path)
        publish_version = fields["version"]


        publish_session_path = publish_template .apply_fields(fields)
        alembic_path         = alembic_template.apply_fields(fields)

        if not os.path.exists(os.path.dirname(alembic_path)):
            os.makedirs(os.path.dirname(alembic_path), exist_ok=True)

        publish_name= self._publish_name(alembic_path)

        rigging_grp= self.get_rigging_geo_grp(model_high_root_node)

        alembic_command= self._abc_command(
            prefix= '{0}_model'.format(ctx.entity["name"]),
            path_to_publish= alembic_path,
            node= model_high_root_node
            )

        if not self.development:
                pm.AbcExport(jobArg=alembic_command)

        self.parent_rigging_grp(model_high_root_node, rigging_grp)

        sg_fields_to_update = {
            "sg_status_list": "rev",
            "task.Task.sg_status_list": "rev",
        }

        # Publish video process
        args = {
            "tk":                  self.parent.engine.sgtk,
            "context":             item.context,
            "comment":             item.description,
            "path":                alembic_path,
            "name":                publish_name,
            "version_number":      publish_version,
            "thumbnail_path":      item.get_thumbnail_as_path(),
            "task":                self.parent.engine.context.task,
            "dependency_paths":    [publish_session_path],
            "sg_fields":           sg_fields_to_update,
            "published_file_type": publish_file_type

        }

        sg_publishes = sgtk.util.register_publish(**args)
        item.properties.sg_publish_data = sg_publishes
        item.properties.sg_publish_path = sg_publishes["path"]


        root_item = self.get_root_item(item)
        root_item.properties.setdefault('sg_publish_extra_data', [])

        self.parent.log_debug('Storing extra publish data on root item: %s' % root_item)
        publish_extra_data = root_item.properties['sg_publish_extra_data']



    def finalize(self, settings, item):
        self.logger.debug('Alembic successfully published')

    def _publish_name(self, path_to_publish):
        publish_name = self._regex_replace(
            regex=r'_v\d{3}',
            source_string=os.path.basename(path_to_publish),
            replace_string=''
        )
        return publish_name

    def _regex_replace(self, regex, source_string, replace_string):
        result = ''
        pattern = re.compile(regex)
        result = pattern.sub(repl=replace_string, string=source_string)
        return result

    def get_pymel_node(self, node_name):
        pm.select(node_name)
        selection_ls=pm.ls(selection=1)
        node= selection_ls[0]

        return node

    # ---------------------------------------------------------------------------
    # Alembic Operations

    def abc_export_alembic(self):
        command_arguments = (
            self._abc_command(
                start=self.time['start'],
                end=self.time['end'],
                prefix='AlembicCache'
            )
        )

    def _get_shot_time(self):
        return {
            'start': pm.playbackOptions(animationStartTime=True, query=True),
             'end': pm.playbackOptions(animationEndTime=True, query=True)
        }

    #  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

    def _abc_command(self, prefix, node, path_to_publish):
        _start = self.time['start']
        _end = self.time['end']
        list_of_arguments = [
            '-frameRange {0} {1}'.format(_start, _end),
            '-attrPrefix {0}'.format(prefix),
            '-stripNamespaces',
            '-uvWrite',
            '-writeColorSets',
            '-writeFaceSets',
            '-worldSpace',
            '-writeVisibility',
            '-writeUVSets',
            '-dataFormat ogawa',
            '-eulerFilter',
            '-root {0}'.format(node.longName()),
            '-file {0}'.format(path_to_publish)
        ]
        result = ' '.join(list_of_arguments)
        return result



    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item
        return root

    #  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

    def get_rigging_geo_grp(self, root_node):
        for node in  root_node.getChildren():
            if "C_rigging_proxy_grp_0" in node.name():
                pm.parent(node,w=1)
                return node
        return False


    def parent_rigging_grp(self, root_node, rigging_geo_grp):
        if rigging_geo_grp:
            pm.parent(rigging_geo_grp, root_node)




