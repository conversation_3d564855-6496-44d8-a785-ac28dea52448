# -*- coding: utf-8 -*-
# Standard library:
import pprint
import functools
import time
import copy
import re
# ___   ___   ___   ___   ___
# Third party:
import sgtk
import pymel.core as pm
import maya.mel as mel
import maya.cmds as cmds

# ___   ___   ___   ___   ___
# Project:
# ====================================================================
HookBaseClass = sgtk.get_hook_baseclass()


def highlight_print(fn):
    @functools.wraps(fn)
    def inner(*args, **kwargs):
        print(">" * 100)
        print("Result:")
        fn(*args, **kwargs)

    return inner


@highlight_print
def pp(data):
    pprint.PrettyPrinter(indent=4).pprint(data)


def addTag(value, tag, color=None):
    map_of_colors = {
        'orange': 'rgb(240, 100, 50)',
        'yellow': 'rgb(230, 150, 50)',
        'red': 'rgb(240, 30, 30)',
        'blue': 'rgb(30, 30, 240)',
        'green': 'rgb(50, 240, 30)'
    }

    if color is not None and color not in map_of_colors.keys():
        raise ValueError('Invalid color name')

    if not color:
        return '<{0}>{1}</{0}>'.format(tag, value)
    else:
        return '<{0} style="color:{2}">{1}</{0}>'.format(
            tag, value, map_of_colors[color]
        )


#    ___  ___  ___  ___  ___  ___  ___  ___  ___  ___  ___  ___


def timed(fn):
    @functools.wraps(fn)
    def inner(*args, **kwargs):
        start = time.time()
        list_of_errors = fn(*args, **kwargs)
        end = time.time()
        total = end - start
        message = (
                '\n\n\n' + ('>' * 80) + '\n' +
                '\tPERFORMANCE ASSESMENT: <{0}>\n'.format(fn.__name__) +
                '\t\tfunction took {0} '.format(total) +
                'seconds to process.\n'
                '\t\tFound {0} errors.\n'.format(len(list_of_errors)) +
                ('>' * 80)
        )
        print(message)
        return list_of_errors

    return inner


# ====================================================================


class Utils(HookBaseClass):

    def __init__(self, parent):
        super(Utils, self).__init__(parent)
        self.core_maya_tools = \
            self.load_framework("mty-framework-coremayatools")
        self.composer = self.core_maya_tools.checks.composer
        self.palatte = {
            "orange": "rgb(220,120,30)"
        }
        self.limit = 350000

    # ________________________________________________________________
    @property
    def name(self):
        return 'FrienzSpaceValidation.Utils'

    def open_editor(self, name):
        mel.eval("{0};".format(name))

    @property
    def list_of_references(self):
        return pm.listReferences()

    @property
    def list_of_gpuCache(self):
        return pm.ls(type="gpuCache")

    # ____________________________________________________________________

    @timed
    def audio_in_timeline(self):
        result = []
        playback_slider = \
            mel.eval('$tmpVar=$gPlayBackSlider')
        audio_source = \
            pm.timeControl(playback_slider, s=True, query=True)

        if not audio_source:
            result.append("You need to import the audio source.")

        return result

    # ________________________________________________________________

    def context_latest_cut(self, **kwargs):
        result = None
        app = kwargs["app"]
        context = self.get_sequence_context(app=app)

        entity = "Cut"
        filters = [
            ["entity", "is", context]
        ]
        fields = ["revision_number"]
        order = [
            {
                "field_name": "revision_number",
                "direction": "desc"
            }
        ]

        result = app.shotgun.find_one(
            entity,
            filters,
            fields,
            order
        )
        return result

    def get_sequence_context(self, **kwargs):
        result = None
        app = kwargs["app"]
        context = app.context
        if context.entity["type"] == "Shot":
            entity = "Sequence"
            filters = [
                ["shots", "is", [context.entity]]
            ]
            result = app.shotgun.find_one(entity, filters)
        else:
            result = context.entity

        return result

    def context_latest_cut_items(self, **kwargs):
        result = {}
        app = kwargs["app"]

        latest_cut = self.context_latest_cut(app=app)

        entity = "CutItem"
        filter = [
            ["cut", "is", latest_cut]
        ]

        fields = [
            "cut_item_in",
            "cut_item_out",
            "code",
            "cut_item_duration"
        ]

        list_of_cutItems = app.shotgun.find(
            entity,
            filter,
            fields
        )

        for element in list_of_cutItems:
            result.update({
                element["code"]: {
                    "start_frame": element["cut_item_in"],
                    "end_frame": element["cut_item_out"],
                    "duration": element["cut_item_duration"]
                }
            })

        return result

    # ---------------------------------------------------------------------------
    # To be promoted to a framework
    @timed
    def list_of_not_loaded_nodes(self):
        result = []
        list_of_references = pm.ls(type='reference')

        for ref in list_of_references:
            try:
                if not ref.isLoaded():
                    result.append(addTag(ref.name(), 'h4', 'orange'))
            except:
                continue

        return result

    # To be promoted to a framework
    @timed
    def list_of_unknown_reference_nodes(self):
        result = []
        pattern = re.compile(r'.*unknown.*', re.IGNORECASE)
        list_of_unknown_nodes = pm.ls(regex=pattern)

        if list_of_unknown_nodes:
            for each in list_of_unknown_nodes:
                result.append(
                    addTag(' - ' + each.name(), 'h4', 'orange')
                )

        return result

    @timed
    def animation_publish_references(self, **kwargs):
        result = []
        list_of_references = pm.listReferences()
        map_of_references = {}

        #        ___   ___   ___   ___   ___

        def validate_reference_with_templates(path):
            result = False
            for template_name in kwargs["valid_templates"]:

                template = kwargs["app"] \
                    .get_template_by_name(template_name)

                if template.validate(path) == True:
                    result = True
                    return result

            return result

        #        ___   ___   ___   ___   ___

        for reference in list_of_references:
            reference_node_name = reference.refNode.name()
            map_of_references.update({
                reference_node_name: {
                    "valid": False,
                    "path": reference.path
                }
            })
        #        ___   ___   ___   ___   ___

        for reference in list_of_references:
            reference_node_name = reference.refNode.name()
            map_of_references[reference_node_name]["valid"] = \
                validate_reference_with_templates(reference.path)

        #        ___   ___   ___   ___   ___

        for key in map_of_references.keys():
            if map_of_references[key]["valid"] == False:
                result.append("{0} | {1}".format(
                    key,
                    map_of_references[key]["path"]
                ))

        return result

    # ____________________________________________________________________

    @timed
    def missing_animControls_set(self):
        result = []
        list_of_sets = pm.ls("animControls", type="objectSet")

        if len(list_of_sets) != 1:
            result.append(
                "You haven't created the animControls set."
            )

        return result

    @timed
    def missing_controllers_in_animControls_set(self):
        result = {
            "errors": [],
            "callback": None,
            "label": None
        }

        list_of_controllers = pm.ls(
            "*_CTL", exactType="transform"
        )
        list_of_controllers.extend(
            pm.ls("*_ctl_*", exactType="transform")
        )

        animControls_set = \
            pm.ls('animControls', exactType="objectSet")

        #          .     .     .     .     .

        def create_animControls_set():
            animControls_set = \
                pm.ls('animControls', exactType="objectSet")

            if len(animControls_set) != 1:
                pm.sets(name="animControls")
            else:
                print("Set already created.")

        #          .     .     .     .     .

        if len(animControls_set) != 1:
            result["errors"].append(
                "You haven't created the animControls set"
            )
            result["callback"] = create_animControls_set
            result["label"] = "Create animControls"
            return result

        #      _     _     _     _     _     _     _

        else:
            set_of_controllers = set()

            for controller in list_of_controllers:
                if controller.hasAttr("fbControl"):
                    set_of_controllers.add(controller)

            set_of_controllers_in_set = \
                set(pm.sets(animControls_set[0], query=True))

            difference = \
                set_of_controllers - set_of_controllers_in_set

            if difference:
                result["errors"].append(
                    "You need to add all the controllers " +
                    "to the animControls set"
                )

                partial_function = functools.partial(
                    self.add_controllers_to_animControls_set,
                    difference
                )

                result["callback"] = partial_function
                result["label"] = "Add animControls"

        return result

    #    _  _  _  _  _  _  _  _  _  _  _  _  _  _  _  _  _

    def add_controllers_to_animControls_set(
            self,
            list_of_controllers
    ):
        animControls_set = \
            pm.ls('animControls', exactType="objectSet")[0]

        for element in list_of_controllers:
            pm.sets(animControls_set, addElement=element)

    # ________________________________________________________________

    @timed
    def missing_controller_attributes(self):
        list_of_controllers_curves = pm.ls(exactType="nurbsCurve")
        result = []

        for curve in list_of_controllers_curves:
            curve_transform = curve.getParent()

            has_fbControl = curve_transform.hasAttr("fbControl")
            has_fbCurve = curve_transform.hasAttr("fbCurve")

            if has_fbControl or has_fbCurve:
                pass
            else:
                result.append(curve.getParent())

        return result

    #        ___  ___  ___  ___  ___  ___

    def select_controllers_without_attributes(self):
        controllers = self.missing_controller_attributes()
        pm.select(controllers)

    # ________________________________________________________________

    @timed
    def missing_cache_color_in_meshes(self):
        meshes = pm.ls(type='mesh')
        result = []

        for mesh in meshes:
            if 'Orig' in mesh.name() or 'Deformed' in mesh.name():
                pass
            else:
                has_noCacheColor = mesh.hasAttr("noCacheColor")
                has_cacheColor = mesh.hasAttr("cacheColor")
                if has_cacheColor or has_noCacheColor:
                    pass
                else:
                    result.append(mesh)

        return result

    #        ___  ___  ___  ___  ___  ___

    def select_missing_cache_color_nodes(self):
        list_of_meshes = self.missing_cache_color_in_meshes()
        pm.select(list_of_meshes)

    # ________________________________________________________________

    @timed
    def check_model_high_root(self):
        result = []
        model_high_root = \
            pm.ls("model_high_root", exactType="transform")

        if len(model_high_root) == 1:

            children = \
                model_high_root[0].listRelatives(allDescendents=True)

            if len(children) == 0:
                result.append(
                    "model_high_root node has no children."
                )

            has_globalScaleAttr = \
                model_high_root[0].hasAttr("fbCache_Dyn_GlobalScale")

            if has_globalScaleAttr:
                pass
            else:
                pass
                #result.append(
                #    "Missing fbCache_Dyn_GlobalScale attribute."
                #)

        elif len(model_high_root) == 0:
            result.append(
                "There is no model_high_root node in the scene."
            )

        else:
            result.append(
                "There is more than one model_high_root node."
            )

        return result

    # ________________________________________________________________

    @timed
    def check_rig_root_name(self, root_name):
        rig_root = pm.ls(root_name, exactType="transform")
        result = []

        if len(rig_root) == 0:
            result.append(
                "There is no {} node in the scene.".format(root_name)
            )

        if len(rig_root) == 1:
            pass

        if len(rig_root) > 1:
            result.append(
                "There is more than one {} node.".format(root_name)
            )

        return result

    # ________________________________________________________________

    @timed
    def list_of_shape_deformed_nodes(self):
        result = pm.ls("*hape*eformed*", type="mesh")
        return result

    def select_shape_deformed_nodes(self):
        shape_deformed = self.list_of_shape_deformed_nodes()
        pm.select(shape_deformed)

    # ________________________________________________________________

    def clear_edits_for_textures(self, **kwargs):

        list_of_references = pm.listReferences(namespaces=True, refNodes=True)
        texture_reference_edits = []
        for ref in list_of_references:
            for edit in ref[2].getReferenceEdits():
                if ".fileTextureName" in edit:
                    command, attr = edit.split(" ")[:2]

                    pm.referenceEdit(
                        attr,
                        editCommand=command,
                        removeEdits=True,
                        failedEdits=True,
                        successfulEdits=True
                    )
        return texture_reference_edits

    def no_reference_edits_for_texture_files(self, **kwargs):

        list_of_references = pm.listReferences(namespaces=True, refNodes=True)
        texture_reference_edits = []
        for ref in list_of_references:
            for edit in ref[2].getReferenceEdits():
                if ".fileTextureName" in edit:
                    texture_reference_edits.append(ref)

        return texture_reference_edits

    @timed
    def list_of_not_loaded_assets(self, **kwargs):
        result = []

        app = kwargs["app"]
        context = app.context
        breakdown_entity = "CustomEntity30"

        filters = [
            ["sg_shot", "is", context.entity]
        ]

        fields = ["code", "sg_asset"]

        list_of_breakdowns = \
            app.shotgun.find(
                breakdown_entity, filters, fields
            )

        set_of_breakdown_namespaces = set()

        for breakdown in list_of_breakdowns:
            set_of_breakdown_namespaces.add(breakdown["code"])

        set_of_assets = self.set_of_namespaces_in_scene()

        result = \
            set_of_breakdown_namespaces.difference(set_of_assets)

        return result

    # ________________________________________________________________

    @timed
    def set_of_namespaces_in_scene(self, **kwargs):
        result = set()

        for reference in self.list_of_references:
            result.add(reference.namespace)

        for gpuCache in self.list_of_gpuCache:
            result.add(gpuCache.getParent().name())

        return result

    # ________________________________________________________________

    @timed
    def list_of_shots_duration_errors(self, **kwargs):
        result = []
        app = kwargs["app"]
        list_of_shots = pm.ls(type="shot")
        map_of_cut_items = self.context_latest_cut_items(app=app)

        for shot in list_of_shots:
            shot_name = shot.getShotName()
            shot_start_frame = shot.startFrame.get()
            shot_end_frame = shot.endFrame.get()
            duration = shot_end_frame - shot_start_frame
            duration = duration + 1
            cut_item_duration = \
                map_of_cut_items[shot_name]["duration"]

            if duration != cut_item_duration:
                message = \
                    "{0} ".format(shot_name) + \
                    "- Does not match latest cut " + \
                    "items duration.\n" + \
                    "Shot duration: {0}\n".format(int(duration)) + \
                    "Cut item duration: {0}".format(
                        int(cut_item_duration)
                    )

                result.append(message)

        return result

    # ________________________________________________________________

    @timed
    def list_of_not_enabled_huds(self, **kwargs):
        result = []
        set_of_required_huds = kwargs["set_of_required_huds"]

        if not isinstance(set_of_required_huds, set):
            raise Exception(
                "Keyword should be of type set."
            )

        set_of_headsUpDisplays = \
            set(pm.headsUpDisplay(listHeadsUpDisplays=True))

        difference = \
            set_of_required_huds.difference(set_of_headsUpDisplays)

        if len(difference) != 0:
            for hud in difference:
                result.append(
                    "{0} has not beign created.".format(hud)
                )

        return result

    # ________________________________________________________________

    @timed
    def not_visible_hud(self, hud):
        result = []

        hud_exists = pm.headsUpDisplay(hud, exists=True)
        hud_is_visible = False

        if hud_exists:
            hud_is_visible = pm.headsUpDisplay(
                hud,
                visible=True,
                query=True
            )

            if hud_is_visible != True:
                result.append(
                    "You must set {0} to visible.".format(hud)
                )

        return result

    def set_hud_visible(self, hud):

        try:
            pm.headsUpDisplay(hud, visible=True, edit=True)
        except:
            pm.warning(
                "Unable to set visibility for {0}".format(hud)
            )

    # ________________________________________________________________

    @timed
    def list_of_not_required_huds(self, list_of_huds):
        result = []

        list_of_all_huds = \
            pm.headsUpDisplay(listHeadsUpDisplays=True)

        def is_hud_visible(hud):
            return pm.headsUpDisplay(
                hud, visible=True, query=True
            )

        visible_huds = \
            filter(is_hud_visible, list_of_all_huds)

        result = \
            filter(
                lambda x: x not in list_of_huds,
                visible_huds
            )

        return result

    # ________________________________________________________________

    @timed
    def list_of_not_updated_assets(self, app):
        result = []
        map_of_assets = {}
        list_of_paths = self.list_not_locked_versions_paths()
        TK = app.engine.sgtk

        if not list_of_paths:
            return result

        list_of_paths = \
            self.remove_cameras_references_by_episode(
                app=app, list_of_paths=list_of_paths
            )

        #          .     .     .     .     .

        map_of_assets.update(
            self.add_session_references(TK, list_of_paths)
        )

        map_of_assets = \
            self.compose_publishes(TK, list_of_paths, map_of_assets)

        #          .     .     .     .     .

        return self.latest_publishes(map_of_assets)

    #    _  _  _  _  _  _  _  _  _  _  _  _  _  _  _  _  _

    def list_not_locked_versions_paths(self):
        result = []
        list_of_references = pm.listReferences()
        list_of_gpuCaches = \
            [x.getParent() for x in pm.ls(type="gpuCache")]
        list_of_gpuCaches = []

        list_of_non_locked_references_paths = []
        for reference in list_of_references:
            top_node = reference.nodes()[0]
            if not top_node.hasAttr("lockedVersionEnabled"):
                continue
            if not top_node.lockedVersionEnabled.get():
                list_of_non_locked_references_paths.append(
                    str(reference.path)
                )

        list_of_non_locked_gpuCaches_paths = []
        for gpuCache in list_of_gpuCaches:
            if not gpuCache.hasAttr("lockedVersionEnabled"):
                continue
            if not gpuCache.lockedVersionEnabled.get():
                list_of_non_locked_gpuCaches_paths.append(
                    gpuCache.cacheFileName.get()
                )

        result.extend(list_of_non_locked_references_paths)
        result.extend(list_of_non_locked_gpuCaches_paths)

        return result

    #    _  _  _  _  _  _  _  _  _  _  _  _  _  _  _  _  _

    def latest_publishes(self, map_of_assets):
        result = []
        for key in map_of_assets.keys():
            asset = map_of_assets[key]
            version = "version_number"

            if not asset["latest"]:
                message = \
                    "- There is no version approved for " + \
                    "<b>{0}</b>".format(key) + \
                    ", supervision team has " + \
                    "to approve at least one version of " + \
                    "this asset before you can publish.\n"
                result.append(message)

            else:
                current_version = asset["current"][version]
                latest_version = asset["latest"][version]
                do_versions_match = \
                    current_version == latest_version

                if do_versions_match == False:
                    config = {
                        "name": key,
                        "name_style": (
                                "\""
                                "font-size:large;" +
                                "font-weight:bold;" +
                                "color: {0};".format(
                                    self.palatte.get("orange")
                                ) +
                                "\""
                        )
                    }

                    message = (
                        "You are not using the "
                        "latest approved version of "
                        "<span style={name_style}>{name}</span>.\n"
                        "<i>Tip: You might be using the "
                        "latest version of an"
                        "asset but not the "
                        "latest-approved version.</i>\n"
                        "<b>Solution: talk to your supervisor "
                        "about the approval of this element in "
                        "your scene.</b>\n"
                    )
                    result.append(message.format(**config))

        return result

    #    _  _  _  _  _  _  _  _  _  _  _  _  _  _  _  _  _

    def remove_cameras_references_by_episode(
            self, app, list_of_paths
    ):
        result = []
        context = app.context
        TK = app.engine.sgtk
        list_of_excluded_episodes = ["e105", "e108", "e110", "e112"]

        episode_code = context.entity["name"].split("_")[0]
        if episode_code in list_of_excluded_episodes:
            for path in list_of_paths:
                template = TK.template_from_path(path)

                if template:
                    fields = template.get_fields(path)
                    is_a_camera = False

                    #        ___   ___   ___   ___   ___

                    asset_type = fields.get("sg_asset_type")
                    if asset_type:
                        is_a_camera = asset_type.lower() == "camera"

                    if is_a_camera == False:
                        result.append(path)

                    #        ___   ___   ___   ___   ___

        else:
            result = list_of_paths[:]

        return result

    #    _  _  _  _  _  _  _  _  _  _  _  _  _  _  _  _  _

    def add_session_references(self, tk, list_of_paths):
        result = {}

        for path in list_of_paths:
            type_name = path
            template = tk.template_from_path(path)
            fields = template.get_fields(path)

            if fields is not None:
                step = fields["Step"]
                reference_name = None
                path_string = path

                if step.lower() in ["rig", "model"]:
                    reference_name = fields["Asset"]

                elif step.lower() in ["layout"]:
                    if template.name == "shot_camera_publish":
                        reference_name = \
                            fields["Shot"] + "_cameraAlembic"

                    elif template.name == "maya_shot_publish":
                        reference_name = \
                            fields["Shot"] + "_layoutReference"
                else:
                    raise Exception(
                        "Unable to idenfity reference: " +
                        "{0}".format(path)
                    )

                #        ___   ___   ___   ___   ___

                if reference_name in result.keys():
                    result[reference_name]["nodes"] \
                        .append(fields)

                else:
                    result.update({
                        reference_name: {
                            "nodes": [],
                            "path": path_string,
                            "step": step
                        }
                    })

                    result[reference_name]["nodes"] \
                        .append(fields)

        return result

    #    _  _  _  _  _  _  _  _  _  _  _  _  _  _  _  _  _

    def no_camera_rig_for_shot_animation(self, app):
        result = None

        context = app.context
        context_name = context.entity['name'].replace(
            '.', '_').replace('-', '_')

        context_name_first_character = context_name[0]
        if context_name_first_character.isdigit():
            context_name = "x" + context_name

        valid_camera_names = []

        for camera_name in ["rigged_CAMShape", "free_CAMShape"]:
            valid_camera_names.append(
                "{0}:{1}".format(context_name, camera_name)
            )

        camera_shape = cmds.ls(valid_camera_names, type="camera")

        export_camera_name = "rigged_CAM"

        camera_node = None

        if export_camera_name:
            for cam in camera_shape:
                if export_camera_name in cam:
                    camera_node = cam

        if len(camera_shape) == 1:
            camera_node = camera_shape[0]

            name_node = cmds.listRelatives(
                camera_node, allParents=True, fullPath=True)
            ref_file = cmds.referenceQuery(name_node, filename=True)

            if ref_file.endswith(".abc"):
                return []
        elif len(camera_shape) == 0:
            return ['No camera with name: {} '.format(export_camera_name)]
        else:
            return ['Multiple camera nodes found.']

        return camera_shape

    def check_blendshapes_textures(self):
        """
        Verify that there is not any CustomEntity BlendShape as a texture file.

        :return: A list of node name and paths for the BlendShapes found in the scene.
        """

        errors = []
        allFileNodes = cmds.ls(et="file")

        nodes_paths = {}
        allFilePaths = []
        for txtr_node in allFileNodes:
            ref_path = cmds.getAttr("%s.fileTextureName" % txtr_node)
            nodes_paths[ref_path] = txtr_node
            allFilePaths.append(ref_path)

        filters = [['entity', 'type_is', 'Blendshape']]
        fields = ['entity']
        publishes = sgtk.util.find_publish(self.parent.engine.sgtk,
                                           allFilePaths, filters=filters,
                                           fields=fields)
        if publishes:
            for path in publishes:
                msg = "The node {} have a Blendshape linked, this can not be published.\n{}"
                errors.append(msg.format(nodes_paths[path], path))

        return errors

    def get_locked_number(self, publishedFile):
        """
        This method seached for the locked version related to a brakedown entity if there is a
        locked publishedFile assigned.

        Returns version number for the locked published file in breakdown entity or None value.
        """

        # Get brakedown entity for context entity
        item_brakedown = self.engine.shotgun.find_one("CustomEntity30",
                                                      [['sg_shot', 'is',
                                                        self.engine.context.entity],
                                                       ['code', 'is',
                                                        publishedFile['entity'][
                                                            'name']]],

                                                      [
                                                          'sg_locked_version.PublishedFile.version_number']
                                                      )

        if item_brakedown and item_brakedown.get(
                'sg_locked_version.PublishedFile.version_number'):
            return item_brakedown.get(
                'sg_locked_version.PublishedFile.version_number')

        return None

    def compose_publishes(
            self, tk, list_of_paths, map_of_assets
    ):
        map_of_assets_clone = copy.deepcopy(map_of_assets)

        fields = [
            "entity",
            "published_file_type",
            "version_number",
            "name"
        ]

        map_of_publishes = sgtk.util.find_publish(
            tk, list_of_paths, filters=None, fields=fields
        )

        for key in map_of_assets_clone.keys():
            path_key = map_of_assets_clone[key]["path"]
            publish = map_of_publishes[path_key]
            map_of_assets_clone[key].update({
                "publish_data": map_of_publishes[path_key]
            })

            map_of_assets_clone[key].update({
                "current": {
                    "type":
                        map_of_publishes[path_key]["type"],
                    "version_number":
                        map_of_publishes[path_key]["version_number"],
                    "id":
                        map_of_publishes[path_key]["id"]
                }
            })

            published_file_type = \
                map_of_publishes[path_key]['published_file_type']

            publish_name = map_of_publishes[path_key]['name']

            # -------------
            # Return locked version number for breakdown entity
            locked_version_number = self.get_locked_number(
                map_of_publishes[path_key])

            filters = [
                ['entity', 'is', publish['entity']],
                ['published_file_type', 'is', published_file_type],
                ['name', 'is', publish_name]
            ]

            if locked_version_number:
                filters.append(['version_number', 'is', locked_version_number])
            else:
                filters.append(['sg_status_list', 'is', 'apr'])

            order = [
                {
                    'field_name': 'version_number',
                    'direction': 'desc'
                }
            ]

            latest = tk.shotgun.find_one(
                'PublishedFile',
                filters,
                ['version_number'],
                order=order
            )

            map_of_assets_clone[key].update({
                "latest": latest
            })

        return map_of_assets_clone

    # ____________________________________________________________________

    @timed
    def list_of_locked_versions_without_description(self):
        result = []
        list_of_references = \
            [x.nodes()[0] for x in pm.listReferences()]
        list_of_gpuCaches = \
            [x.getParent() for x in pm.ls(type="gpuCache")]

        list_of_all_nodes = []
        list_of_all_nodes.extend(list_of_references)
        list_of_all_nodes.extend(list_of_gpuCaches)

        for node in list_of_all_nodes:
            if not node.hasAttr("lockedVersionEnabled"):
                continue

            if node.getAttr("lockedVersionEnabled"):
                config = {
                    "node": node.name(),
                    "name_style":
                        "font-size:large;" +
                        "font-weight:bold;" +
                        "color: {0};".format(
                            self.palatte.get("orange")
                        )
                }

                if not node.getAttr("lockedVersionDescription"):
                    message = (
                        "<span style =\"{name_style}\">{node} </span>"
                        "is a <i>locked version</i> but it doesn't "
                        "have a description.\n"
                        "Tip: Use the Layout Toolkit to set its "
                        "description.\n"
                    )
                    message = message.format(**config)
                    result.append(message)

        return result

    # ________________________________________________________________

    @timed
    def list_of_objects_too_far_from_origin(self):
        result = []
        dict_of_animation_data = {}
        list_of_control_curves = \
            ["*:" + "*_ctl_*", "*:" + "*_CTL*", ]
        list_of_curves = \
            pm.ls(list_of_control_curves, type="nurbsCurve")

        self.cache_anim_data(
            list_of_curves,
            dict_of_animation_data
        )
        result = self.keyframe_positions_out_of_limit(
            dict_of_animation_data
        )

        return result

    # ___  ___  ___  ___  ___  ___  ___  ___  ___  ___  ___  ___  ___

    def keyframe_positions_out_of_limit(
            self, dict_of_animation_data
    ):
        result = []

        for key in dict_of_animation_data:
            list_of_animation_data = \
                dict_of_animation_data.get(key).get("data")

            result.extend(
                self.process_animation_data(
                    list_of_animation_data
                )
            )
        return result

    # ___  ___  ___  ___  ___  ___  ___  ___  ___  ___  ___  ___  ___

    def process_animation_data(self, list_of_animation_data):
        result = []
        list_of_cached_items = []

        for data in list_of_animation_data:
            curve_transform = data.get("transform")

            list_of_keyframes = sorted(data.get("keyframes"))

            if curve_transform.namespace() in list_of_cached_items:
                continue

            #        ___   ___   ___   ___   ___

            if not list_of_keyframes:
                world_matrix = \
                    curve_transform.getAttr("worldMatrix")
                distance = world_matrix.length()

                if distance >= self.limit:
                    result.append(
                        self.out_of_limit_object(
                            curve_transform=curve_transform,
                            distance=distance
                        )
                    )
                list_of_cached_items.append(
                    curve_transform.namespace()
                )
            else:
                for frame in list_of_keyframes:
                    world_matrix = curve_transform.getAttr(
                        "worldMatrix",
                        time=frame
                    )

                    distance = world_matrix.length()

                    # If it finds one node from an asset outside
                    # the the limit, just append the asset and
                    # stop the loop.
                    # this is for optimization purpouses:
                    if distance >= self.limit:
                        result.append(
                            self.out_of_limit_object(
                                curve_transform=curve_transform,
                                time=frame,
                                distance=distance
                            )
                        )

        return result

    # ___  ___  ___  ___  ___  ___  ___  ___  ___  ___  ___  ___  ___

    def cache_anim_data(
            self, list_of_curves, dict_of_animation_data
    ):
        for curve in list_of_curves:
            _list_of_anim_curves = curve.getParent() \
                .listConnections(type="animCurveTL")

            namespace = curve.namespace()

            if not dict_of_animation_data.get(namespace):
                dict_of_animation_data.update({
                    namespace: {
                        "data": [{
                            "animation_curves":
                                _list_of_anim_curves,
                            "node":
                                curve,
                            "transform":
                                curve.getParent(),
                            "keyframes":
                                self.set_of_timeline_keys(
                                    _list_of_anim_curves
                                )
                        }]
                    }
                })

            else:
                data = {
                    "animation_curves":
                        _list_of_anim_curves,
                    "node":
                        curve,
                    "transform":
                        curve.getParent(),
                    "keyframes":
                        self.set_of_timeline_keys(
                            _list_of_anim_curves
                        )
                }

                dict_of_animation_data \
                    .get(namespace) \
                    .get("data") \
                    .append(data)

    #    ___  ___  ___  ___  ___  ___  ___  ___  ___  ___  ___  ___

    def out_of_limit_object(
            self, curve_transform, distance, time=None
    ):
        result = None

        if time:
            config = {
                "node":
                    curve_transform.name(),
                "distance":
                    int(distance),
                "name_style":
                    "font-size:large;" +
                    "font-weight:bold;" +
                    "color: {0};".format(
                        self.palatte.get("orange")
                    ),
                "frame_style":
                    "font-size: large;font-weight: bold;" +
                    "background-color:rgb(255, 50, 50);" +
                    "color: rgb(30,30,30);" +
                    "padding: 5px;",
                "frame": int(time),
                "limit": self.limit
            }
            data = {
                "message": (
                    "<span style =\"{name_style}\">{node} </span>"
                    "is far beyond the {limit} unit limit.\n"
                    "Distance: {distance}\n"
                    "Frame: <span style=\"{frame_style}\">"
                    " {frame} "
                    "</span>\n"
                ),
                "node": curve_transform
            }

        else:
            config = {
                "frame":
                    int(pm.currentTime()),
                "style":
                    "font-size:large;" +
                    "font-weight:bold;" +
                    "color: {0};".format(
                        self.palatte.get("orange")
                    ),
                "node":
                    curve_transform,
                "distance":
                    int(distance),
                "limit":
                    self.limit,
                "namespace":
                    curve_transform.namespace().strip(":")
            }

            data = {
                "message": (
                    "<span style =\"{style}\">{namespace} </span>"
                    "has an element(s) beyond the {limit} unit "
                    "limit at the current frame {frame}.\n"
                    "Distance: {distance}\n"
                ),
                "node": curve_transform
            }

        data["message"] = data.get("message").format(**config)

        result = data

        return result

    #    ___  ___  ___  ___  ___  ___  ___  ___  ___  ___  ___  ___

    def set_of_timeline_keys(self, list_of_anim_curves):
        set_of_keyframes = set()
        for _curve in list_of_anim_curves:
            amount = _curve.numKeys()
            list_of_keys = \
                [_curve.getTime(i) for i in range(0, amount)]

            set_of_keyframes.update(set(list_of_keys))

        return set_of_keyframes

    # ________________________________________________________________

    @timed
    def list_of_non_reference_mesh_display_overrides(self):
        result = []
        list_of_meshes = pm.ls(type="mesh")
        list_of_transforms = [x.getParent() for x in list_of_meshes]

        list_of_all_nodes = []
        list_of_all_nodes.extend(list_of_meshes)
        list_of_all_nodes.extend(list_of_transforms)

        for node in list_of_all_nodes:

            overrides_enabled = node.overrideEnabled.get() and \
                                node.overrideDisplayType.get() == 2

            if not overrides_enabled:
                config = {
                    "name": node.name(),
                    "style":
                        "color:{0};".format(
                            self.palatte.get("orange")
                        )
                }

                message = (
                    "<span style=\"{style}\">{name}</span> "
                    "has no overrides enabled."
                )

                result.append({
                    "node": node,
                    "name": message.format(**config)
                })

        return result

    @timed
    def check_fps_time_unit(self, **kwargs):
        result = []

        app = kwargs['app']

        frame_range_data = { 2: "2fps",    3: "3fps",    4: "4fps",
                             5: "5fps",    6: "6fps",    8: "8fps",
                            10: "10fps",  12: "12fps",  15: "game",
                            16: "16fps",  20: "20fps",  24: "film",
                            25: "pal",    30: "ntsc",   40: "40fps",
                            48: "show",   50: "palf",   60: "ntscf",
                            75: "75fps",  80: "80fps", 100: "100fps",
                            120: "120fps"}

        filters = [['id', 'is', app.context.project['id']]]
        project_data = app.shotgun.find_one(
            'Project', filters, ['sg_fps']
        )

        frames_psec = int(project_data['sg_fps'])

        if frames_psec in frame_range_data:
            desired_unit = frame_range_data[frames_psec]
        else:
            desired_unit = 'film'

        time_unit = cmds.currentUnit(query=True, time=True)       

        if time_unit != desired_unit:
            result.append(time_unit)

        return result

    @timed
    def list_constraints_targeted_to_camera(self):

        result = []

        constraints_list = cmds.ls(type='constraint')
        namespaces_list = pm.listReferences(
            parentReference=None,
            recursive=False, namespaces=True,
            refNodes=False,
            references=False
        )

        constraints_non_referenced_list = []

        for constraint in constraints_list:

            if not constraint.split(":")[0] in namespaces_list:
                constraints_non_referenced_list.append(constraint)

        for constraint in constraints_non_referenced_list:

            constraint_node = pm.PyNode(constraint)
            target_list = pm.parentConstraint(constraint_node, q=True,
                                              targetList=True)

            for target in target_list:
                if target[-3:] == 'CAM':
                    result.append(constraint)

        return result

    def list_geometry_non_referenced(self):
        result = []

        geometry_list = cmds.ls(type='mesh')
        namespaces_list = pm.listReferences(
            parentReference=None,
            recursive=False, namespaces=True,
            refNodes=False,
            references=False
        )

        for geometry in geometry_list:
            parent_of_geometry = cmds.listRelatives(geometry, allParents=True)

            if parent_of_geometry:
                if not parent_of_geometry[0].split(":")[0] in namespaces_list:
                    result.append(geometry)
            else:
                if not geometry.split(":")[0] in namespaces_list:
                    result.append(geometry)

        return result

    @timed
    def list_scripts_nodes_jobs_vaccine(self):
        import os

        result = []

        script_nodes = cmds.ls("vaccine_gene", type="script")
        if script_nodes:
            cmds.delete(script_nodes)
            print('Deleted script_nodes: {}'.format(script_nodes))

        if cmds.objExists('breed_gene'):
            cmds.delete('breed_gene')
            print('Deleted from the scene "breed_gene"')

        if cmds.objExists('vaccine_gene'):
            cmds.delete('vaccine_gene')
            print('Deleted from the scene "vaccine_gene"')

        path = cmds.internalVar(userAppDir=True) + 'scripts'

        if os.path.exists(path + '/userSetup.py'):
            os.remove(path + '/userSetup.py')
            print('Deleted from the script folder "userSetup.py"')

        if os.path.exists(path + '/userSetup.pyc'):
            os.remove(path + '/userSetup.pyc')
            print('Deleted from the script folder "userSetup.pyc"')

        if os.path.exists(path + '/vaccine.py'):
            os.remove(path + '/vaccine.py')
            print('Deleted from the script folder "vaccine.py"')

        if os.path.exists(path + '/vaccine.pyc'):
            os.remove(path + '/vaccine.pyc')
            print('Deleted from the script folder "vaccine.pyc"')

        jobs = cmds.scriptJob(lj=True)

        for job in jobs:
            if "leukocyte.antivirus()" in job:
                id_ = job.split(":")[0]
                if id_.isdigit():
                    cmds.scriptJob(k=int(id_), f=True)

        return result

    @timed
    def list_unsupported_references_nodes(self, filter_function):
        result = []

        list_of_references = pm.listReferences()
        list_of_top_nodes = [x.nodes()[0] for x in list_of_references]

        list_of_top_nodes = filter(
            lambda x: len(x.longName().split('|')) == 2,
            list_of_top_nodes
        )

        result = filter(
            filter_function,
            list_of_top_nodes
        )

        if result:
            result.append(
                '<h3 style=\"color:rgb(200, 100, 100)\">'
                'There was an error with the references in your scene,<br>'
                'this can be caused for one of the following reasons:'
                '</h3>'
                '<ul>'
                '<li>'
                'You are not using a Asset reference contained inside a '
                ' \"rig_high_root\" named group node.\n'
                '</li>'
                '<li>'
                'Only references to Assets inside \"model_high_root\" group '
                'will be valid if the attribute \"assetType\" exists<br>'
                'and value set to either \"EnvLocation\" or \"EnvModule\"'
                '</li>'
                '</ul>'
            )

        return result

    # ---------------------------------------------------------------------------

    def _temporal_request_environment_for_episodes(
            self, app, list_of_episodes, list_of_environments, list_of_steps
    ):
        print("\n" + (">" * 120))
        print('{0}._temporal_request_environment_for_episodes'.format(
            self.name
        ))
        result = []
        list_of_gpu_caches = pm.ls(type='gpuCache')
        _ctx = app.context

        if _ctx.step['name'] not in list_of_steps:
            return []

        _entity = None
        if _ctx.entity['type'] == 'Sequence':
            _entity = app.shotgun.find_one(
                entity_type='Sequence',
                filters=[
                    ['id', 'is', _ctx.entity['id']],
                    ['project', 'is', _ctx.project]
                ],
                fields=['episode']
            )
        if _ctx.entity['type'] == 'Shot':
            _entity = app.shotgun.find_one(
                entity_type='Shot',
                filters=[
                    ['id', 'is', _ctx.entity['id']],
                    ['project', 'is', _ctx.project]
                ],
                fields=['sg_sequence.Sequence.episode']
            )
            _entity['episode'] = _entity['sg_sequence.Sequence.episode']

        template = app.get_template_by_name('asset_gpu_cache')
        list_of_assets = []
        for gpu in list_of_gpu_caches:
            _p = gpu.cacheFileName.get()
            _ln = gpu.longName()
            _fields = None
            if len(_ln.split('|')) == 3:
                _fields = template.get_fields(_p)
            _asset = None
            if _fields:
                list_of_assets.append(_fields['Asset'])

        styles = {
            'header': '\"color:rgb(250,140,30)\"',
            'env': '\"color:rgb(50,200,230)\"',
            'questions': '\"color:rgb(200,160,0)\"'
        }

        msg = (
            '<div>'
            'For this episode, it is required to use the .ma file from the '
            'modeling task for <span style={env_style}>{env}</span>'
            '</div>'
        )

        list_of_formatted_message = map(
            lambda x: msg.format(env=x, env_style=styles['env']),
            list_of_environments
        )

        header = (
                '<h3 style={0}>'.format(styles['header']) +
                'Hello!, this is temporal validation from your pipeline friends:'
                '</h3>'
        )

        why = (
            '''<div>
                <br>
                <h4 style={questions}>Why?</h4>
                This validation will push forward the use of the new Environments
                where you will be able to move and hide certain elements,
                we add this temporary check so that we can make sure we test
                with specific environment at specific episodes.
 
                So... if you see this message, it is because you are one of the
                choosen ones to use such environments, if you experience any
                issue please report it with the pipeline team.
                
                <h4 style={questions}>How can I fix it?</h4>
                Use the Loader to download and create a reference for this 
                environment, but instead of the GPUCache use the .ma from
                the modeling task.
                (Don't forget to remove the replaced GPUCache node)
            </div>'''.format(questions=styles['questions'])
        )

        _is_episode = _entity['episode']['name'] in list_of_episodes
        _is_environment = any(
            _a in list_of_environments for _a in list_of_assets
        )

        if _is_episode and _is_environment:
            result.append(header)
            result.append('{0}'.format(''.join(list_of_formatted_message)))
            result.append(why)

        return result
