# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

import os
import sgtk
import re
import traceback
import shutil
from sgtk.platform.qt import QtGui, QtCore
import BlackmagicFusion as bmd
from os import listdir
from os.path import isfile, join
import subprocess

HookBaseClass = sgtk.get_hook_baseclass()

fusion = bmd.scriptapp("Fusion")

class FusionPublishSaverSeqPlugin(HookBaseClass):
    """
    Plugin for publish rendered sequence

    """

    @property
    def icon(self):
        """
        Path to an png icon on disk
        """

        # look for icon one level up from this hook's folder in "icons" folder
        return os.path.join(
            self.disk_location,
            os.pardir,
            "icons",
            "video.png"
        )

    @property
    def name(self):
        """
        One line display name describing the plugin
        """
        return "Publish sequence files"

    @property
    def description(self):
        """
        Copy/upload the render sequence to publish area
        """

        loader_url = "https://support.shotgunsoftware.com/hc/en-us/articles/219033078"

        return """
        <p>This plugin upload the video as a version in shotgun.</p>
        """

    @property
    def settings(self):
        """
        Dictionary defining the settings that this plugin expects to recieve
        through the settings parameter in the accept, validate, publish and
        finalize methods.

        A dictionary on the following form::

            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }

        The type string should be one of the data types that toolkit accepts
        as part of its environment configuration.
        """

        # settings specific to this class
        fusion_publish_settings = {
            "Work render template": {
                "type": "template",
                "default": "shot_flat_render_work_exr",
                "description": "Original path from render"},
            "Publish render template":{
                "type": "template",
                "default": "shot_flat_render_publish_exr",
                "description": "Template to publish and copy sequence files"},

            "Publish render mov template":{
                "type": "template",
                "default": "shot_flat_render_publish_mov",
                "description": "Template to create a mov file"}
        }

        # inherit the settings from the base publish plugin
        plugin_settings = super(FusionPublishSaverSeqPlugin, self).settings or {}

        # update the base settings
        plugin_settings.update(fusion_publish_settings)
        return plugin_settings

    @property
    def item_filters(self):
        """
        List of item types that this plugin is interested in.

        Only items matching entries in this list will be presented to the
        accept() method. Strings can contain glob patters such as *, for example
        ["fusion.*", "file.fusion"]
        """
        return ["fusion.session.savers"]

    def accept(self, settings, item):
        """
        Method called by the publisher to determine if an item is of any
        interest to this plugin. Only items matching the filters defined via the
        item_filters property will be presented to this method.

        A submit for review task will be generated for each item accepted here. Returns a
        dictionary with the following booleans:

            - accepted: Indicates if the plugin is interested in this value at
                all. Required.
            - enabled: If True, the plugin will be enabled in the UI, otherwise
                it will be disabled. Optional, True by default.
            - visible: If True, the plugin will be visible in the UI, otherwise
                it will be hidden. Optional, True by default.
            - checked: If True, the plugin will be checked in the UI, otherwise
                it will be unchecked. Optional, True by default.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process

        :returns: dictionary with boolean keys accepted, required and enabled.
            This plugin makes use of the tk-multi-reviewsubmission app; if this
            app is not available then the item will not be accepted, this method
            will return a dictionary with the accepted field that has a value of
            False.

            Several properties on the item must also be present for it to be
            accepted. The properties are 'path', 'publish_name', 'color_space',
            'first_frame' and 'last_frame'
        """
        publisher  = self.parent
        engine     = publisher.engine
        context    = engine.context
        saver_path = item.properties['saver_node'].GetAttrs()['TOOLST_Clip_Name'].values()[0]

        template_saver = publisher.tank.templates_from_path(saver_path)

        # If the saver node is not used any template to render
        if not len(template_saver):
            return {'accepted': False}
        else:
            template_saver = template_saver[0]

        # If the saver node is using a different template than
        #    shot_flat_render_work_exr
        #if item.properties['work_template'] != template_saver:
        if template_saver.name != 'fusion_shot_render_aov':
            return {'accepted': False}

        # Only register the default nodes
        fields = template_saver.get_fields(saver_path)
        if fields['aov_name'] != 'default':
            return {'accepted': False}

        # Verify entity type SHOT
        entity_type = str(context).split(' ')[1]
        if entity_type != "Shot":
            return {'accepted': False}

        # Verify saver and work versions
        comp          = fusion.GetCurrentComp()
        path          = comp.GetAttrs()['COMPS_FileName']
        work_template = engine.sgtk.template_from_path(path)
        work_version  = work_template.get_fields(path).get('version')
        fields        = template_saver.get_fields(saver_path)

        template_version = fields.get('version')
        if template_version != work_version:
            return {'accepted': False}

        return {"accepted": True,
                "checked": True}


    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish. Returns a
        boolean to indicate validity.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        :returns: True if item is valid and not in proxy mode, False otherwise.
        """

        sg_shot    = self.get_sg_shot_info(['sg_cut_in', 'sg_cut_out'])
        saver_path = item.properties['saver_node'].GetAttrs()['TOOLST_Clip_Name'].values()[0]


        path_container = os.path.dirname(saver_path)
        file_base      = os.path.basename(saver_path)

        path_parts     = file_base.split('.')
        padding        = len(path_parts[1])

        error_frames   = []
        for i in xrange(sg_shot['sg_cut_in'], sg_shot['sg_cut_out']+1):
            frame_str       = str(i).zfill(padding)
            validating_file = '.'.join([path_parts[0], frame_str,
                                        path_parts[-1]])
            if not os.path.exists(join(path_container, validating_file)):
                error_frames.append(frame_str)

        if len(error_frames):
            error_msg = "Missing frames: {}".format(error_frames)
            raise Exception(error_msg)

        return True


    def publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        self.logger.info('Publish video process')
        #   Video process
        work_path_t       = item.properties['work_area_template']
        publish_path_t    = item.properties['publish_render_template']
        primary_publish_t = item.properties['primary_publish_template']
        mov_publish_t     = item.properties['publish_mov_template']
        saver_path        = item.properties['saver_node'].GetAttrs()['TOOLST_Clip_Name'].values()[0]

        comp            = fusion.GetCurrentComp()
        work_path       = comp.GetAttrs()['COMPS_FileName']
        fields          = work_path_t.get_fields(work_path)
        publish_name    = fields["name"]
        publish_version = fields["version"]
        publish_path    = publish_path_t.apply_fields(fields)
        mov_publish     = mov_publish_t.apply_fields(fields)

        publish_parts   = publish_path.split('.')
        source_parts    = saver_path.split('.')

        padding         = len(publish_parts[1])
        source_seq      = '.'.join([source_parts[0],
                                    '%0{}d'.format(padding),
                                    source_parts[-1]])
        temp_seq        = '.'.join(["{}_tmp".format(source_parts[0]),
                                    '%0{}d'.format(padding),
                                    'png'])
        multilayer_seq  = '.'.join([publish_parts[0],
                                    '%0{}d'.format(padding),
                                    publish_parts[-1]])

        self.parent.engine.ensure_folder_exists(os.path.dirname(temp_seq))

        ocio         = self.load_framework("mty-framework-opencolorio")
        sg_shot      = self.get_sg_shot_info(['sg_cut_in', 'sg_cut_out'])
        sg_proj      = self.get_sg_project_info(['sg_working_color_space', 'sg_fps'])
        all_colorspaces = {'sRGB': 'Output - sRGB',
                           'rec709': 'Output - Rec.709',
                           'aces_cg': 'ACES - ACEScg'}

        # Validating path
        first_f_path = source_seq % sg_shot['sg_cut_in']
        if not os.path.exists(first_f_path):
            raise Exception("Missing file: %s " % (first_f_path))


        # Using valid color space name
        proj_colorspace = sg_proj['sg_working_color_space']
        if proj_colorspace in all_colorspaces.keys():
            proj_colorspace = all_colorspaces[proj_colorspace]

        # Converting exr to png ------------------------------
        # Variables for oiitool command
        self.parent.log_debug('Creating auxiliar png files')
        self.logger.info('Creating auxiliar png files')

        ocio_path = ocio.disk_location + "/version/aces_1_2/config.ocio"
        oiio         = self.load_framework("mty-framework-openimageio")
        oiio_tool    = oiio.openImageIOCore
        oiio_tool.set_binary('oiiotool')
        fr_start     = int(sg_shot['sg_cut_in'])
        fr_end       = int(sg_shot['sg_cut_out'])
        total_frames = fr_end - fr_start
        #frames_str   = ','.join([str(i) for i in range(fr_start, fr_end+1)])
        frames_limit = 15
        oiio_subprocess = int(total_frames / frames_limit)+1

        # Cleaning revious files
        folder_container = os.path.dirname(temp_seq)
        all_elements     = os.listdir(folder_container)
        for sub_element in all_elements:
            if len(sub_element.split('.')) <=3:
                continue
            if sub_element.endswith('.temp.png'):
                full_el_path = os.path.join(folder_container, sub_element)
                os.remove(full_el_path)

        # Split the process in parts because it fails after frame 32
        for i in range(0, oiio_subprocess):
            current_first_f = str(fr_start + (frames_limit * i))
            if i == oiio_subprocess -1:
                current_last_f  = str(fr_end)
            else:
                current_last_f  = str(fr_start + (frames_limit * (i+1))-1)

            # oiiotool command
            if current_first_f != current_last_f:
                oiio_cmds  = "--frames {}-{}".format(current_first_f, current_last_f)
            else:
                oiio_cmds  = "--frames {}".format(current_first_f)

            oiio_cmds += " {}".format(source_seq)
            oiio_cmds += " -colorconfig"
            oiio_cmds += ' "{}"'.format(ocio_path)
            oiio_cmds += " -colorconvert"
            oiio_cmds += ' "{}"'.format(proj_colorspace) # Input colorspace
            oiio_cmds += ' "Output - sRGB"' # output colorspace
            #cmds += " -ociolook 0"
            oiio_cmds += " -croptofull"
            oiio_cmds += " -o {}".format(temp_seq)

            info_msg = '{}/{} {}-{}: Creating tmp png, 1st attempt'
            info_msg = info_msg.format(str(i+1), str(oiio_subprocess+1), current_first_f, current_last_f)
            self.logger.info(info_msg)
            _err, _info, _cmd = oiio_tool.execute_command_str(oiio_cmds)
            if _err:
                war_msg = '{}/{} {}-{}: Creating tmp png, 2nd attempt'
                war_msg = war_msg.format(str(i+1), str(oiio_subprocess+1), current_first_f, current_last_f)
                self.logger.warning(war_msg)
                #firts_attempt = "Failed to create png aux sequence: %s \n %s" % (_info, _cmd)
                try:
                    # Second try
                    oiio_exe = oiio_tool.get_bin_path()
                    new_oiio_cmds = '"{}" {}'.format(oiio_exe, oiio_cmds)
                    process2  = subprocess.Popen(new_oiio_cmds, shell=True, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)
                    err_2, out_2  = process2.communicate()
                    if err_2:
                        self.logger.error(err_2)
                        raise Exception("Failed to create png aux sequence: %s \n %s \nSecond try: %s" % (_info, _cmd, out_2))
                except:
                    raise Exception("Failed to create png aux sequence: {}".format(traceback.format_exc()))

            current_temp_first = temp_seq % int(current_first_f)
            self.logger.info(current_temp_first)
            if not os.path.exists(current_temp_first):
                new_oiio_cmds = '"{}" {}'.format(oiio_exe, oiio_cmds)

                error_msg  = "Failes to create tmp files, try the following command in terminal"
                error_msg += "\n{}".format(new_oiio_cmds)
                self.logger.error(error_msg)
                raise Exception(error_msg)

        # Converting png images to mov with ffmpeg
        # Back to string format becuase the list has issues with the flag lut3d
        self.parent.log_debug('Creating video from png files')
        ffmpeg_cmd  = "-y "
        ffmpeg_cmd += "-start_number {} ".format(int(sg_shot['sg_cut_in']))
        ffmpeg_cmd += "-i {} ".format(temp_seq)
        ffmpeg_cmd += "-s 1920x1080 "
        ffmpeg_cmd += " -vcodec libx264"
        ffmpeg_cmd += " -pix_fmt yuv422p"
        ffmpeg_cmd += " -r {}".format(sg_proj['sg_fps'])
        ffmpeg_cmd += " -map 0:v"
        ffmpeg_cmd += " -crf 5"
        ffmpeg_cmd += ' -vf "colorspace=bt709:iall=bt601-6-625:fast=1" '
        ffmpeg_cmd += mov_publish

        self.parent.engine.ensure_folder_exists(os.path.dirname(mov_publish))

        ffmpeg_framework = self.load_framework("mty-framework-ffmpeg")

        _err, _info = ffmpeg_framework.ffmpegCore.execute_command(ffmpeg_cmd)
        if _err:
            raise Exception(
                "Failed to create video: %s \n %s" % (_info, _err))

        # Cleaning pngs sequence
        self.parent.log_debug('Cleaning temporal files')
        fr_start     = int(sg_shot['sg_cut_in'])
        fr_end       = int(sg_shot['sg_cut_out'])
        for i in range(fr_start, fr_end+1):
            cur_frame = temp_seq % i
            if os.path.exists(cur_frame):
                os.remove(cur_frame)

        #   Publish video process
        self.parent.log_debug('Publish process')
        args = {
            "tk":               self.parent.engine.sgtk,
            "context":          item.context,
            "comment":          item.description,
            "path":             mov_publish,
            "name":             publish_name,
            "version_number":   publish_version,
            "thumbnail_path":   item.get_thumbnail_as_path(),
            "task":             self.parent.engine.context.task,
            "dependency_paths": [multilayer_seq],
            "sg_fields":        {"sg_status_list":   "rev"},
            "published_file_type": item.properties['publish_video_type']
        }

        sg_publishes = sgtk.util.register_publish(**args)

        root_item = self.get_root_item(item)
        root_item.properties.setdefault('sg_publish_extra_data', [])
        publish_extra_data = root_item.properties['sg_publish_extra_data']
        publish_extra_data.append(sg_publishes)

        item.properties.sg_publish_data = sg_publishes
        item.properties.sg_publish_path = sg_publishes["path"]["local_path"]

        self.parent.log_debug(
            "Adding existing publish " +
            "(id:{}) ".format(sg_publishes['id']) +
            "as extra data for item: {}".format(item)
        )

        #   Version process Ref publish_shot_media_review.py from maya/layout
        sg_version = self.submit_version(
            multilayer_seq,     mov_publish,
            [sg_publishes],     self.parent.engine.context.task,
            item.description,   int(sg_shot['sg_cut_in']),
            int(sg_shot['sg_cut_out']),
        )

        # Upload in a new thread and make our own event loop
        # to wait for the thread to finish.

        event_loop = QtCore.QEventLoop()
        thread = UploaderThread(
            self.parent, sg_version,
            mov_publish,
            item.get_thumbnail_as_path(),
            True
        )

        thread.finished.connect(event_loop.quit)
        thread.start()
        event_loop.exec_()

        # log any errors generated in the thread
        for e in thread.get_errors():
            self.parent.log_error(e)


    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        self.logger.debug('Item video successfully published')


    def get_sg_shot_info(self, shot_fields):
        engine         = self.parent.engine
        sg             = engine.shotgun
        sg_proj        = engine.context.project

        context_tokens = str(engine.context).split(' ')
        entity_name    = context_tokens[2]
        shot_filter    = [['project', 'is', sg_proj],
                          ['code', 'is', entity_name]]

        sg_shot = sg.find_one('Shot', shot_filter, shot_fields)
        return sg_shot

    def get_sg_project_info(self, proj_fields):
        engine         = self.parent.engine
        sg             = engine.shotgun
        sg_proj        = engine.context.project

        proj_filter    = [['id', 'is', sg_proj['id']]]

        sg_proj = sg.find_one('Project', proj_filter, proj_fields)
        return sg_proj

    def get_root_item(self, item):

        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root

    def submit_version(self, path_to_frames, path_to_movie, sg_publishes,
                       sg_task, comment, first_frame, last_frame):
        """
        Create a version in Shotgun for this path and linked to this publish.
        """

        # get current shotgun user
        current_user = self.parent.engine.context.user

        # create a name for the version based on the file name
        # grab the file name, strip off extension
        name = os.path.splitext(os.path.basename(path_to_movie))[0]
        # do some replacements and capitalize
        name = name.replace("_", " ").capitalize()

        LinkFolder = {'local_path': os.path.dirname(path_to_frames) + os.sep,
                      'content_type': None,
                      'link_type': 'local',
                      'name': name}

        LinkFile   = {'local_path': path_to_movie,
                      'content_type': None,
                      'link_type': 'local',
                      'name': name}

        entity = self.parent.engine.context.entity
        proj   = self.parent.engine.context.project
        # Create the version in Shotgun
        data = {
            "code":                 name,
            "sg_status_list":       "rev",
            "entity":               entity,
            "sg_task":              sg_task,
            "sg_version_type":      "Production",
            "sg_first_frame":       first_frame,
            "sg_last_frame":        last_frame,
            "frame_count":          (last_frame - first_frame + 1),
            "frame_range":          "%s-%s" % (first_frame, last_frame),
            "sg_frames_have_slate": False,
            "published_files":      sg_publishes,
            "created_by":           current_user,
            "description":          comment,
            "sg_path_to_frames":    path_to_frames,
            "sg_path_to_movie":     path_to_movie,
            "sg_movie_has_slate":   False,
            "project":              proj,
            "user":                 current_user
        }

        sg_version = self.parent.engine.shotgun.create("Version", data)
        self.parent.log_debug("Created version in shotgun: %s" % str(data))

        return sg_version



class UploaderThread(QtCore.QThread):
    """
    Simple worker thread that encapsulates uploading to shotgun.
    Broken out of the main loop so that the UI can remain responsive
    even though an upload is happening
    """
    def __init__(self, app, version, path_to_movie, thumbnail_path, upload_to_shotgun):
        QtCore.QThread.__init__(self)
        self._app               = app
        self._version           = version
        self._path_to_movie     = path_to_movie
        self._thumbnail_path    = thumbnail_path
        self._upload_to_shotgun = upload_to_shotgun
        self._errors            = []

    def get_errors(self):
        """
        can be called after execution to retrieve a list of errors
        """
        return self._errors

    def run(self):
        """
        Thread loop
        """
        upload_error = False

        if self._upload_to_shotgun:
            try:
                self._app.tank.shotgun.upload("Version", self._version["id"], self._path_to_movie, "sg_uploaded_movie")
            except:
                self._errors.append(
                    "Movie upload to Shotgun failed: {}".format(traceback.print_exc())
                )
                upload_error = True

        if not self._upload_to_shotgun or upload_error:
            try:
                self._app.tank.shotgun.upload_thumbnail("Version", self._version["id"], self._thumbnail_path)
            except:
                self._errors.append(
                    "Thumbnail upload to Shotgun failed: {}".format(traceback.print_exc())
                )

