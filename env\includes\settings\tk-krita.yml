# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

################################################################################

includes:
- ../app_locations.yml
- ../engine_locations.yml
- ./tk-multi-loader2.yml
- ./tk-multi-publish2.yml
- ./tk-multi-screeningroom.yml
- ./tk-multi-shotgunpanel.yml
- ./tk-multi-snapshot.yml
- ./tk-multi-workfiles2.yml
- ./tk-multi-breakdown.yml
- ./tk-multi-setframerange.yml

################################################################################


# ---- Krita

# project
settings.tk-krita.project:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.krita.project"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    mty-executeaction-ensure-folders:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Ensure Tasks Folders"
      action_hook: "{config}/tk-shotgun-executeaction/create_folders_for_assigned_tasks.py"
      sg_extended_fields: {}
      allowed_entities: ['Shot', 'Sequence', 'Asset', 'Project']
  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  location: "@engines.tk-krita.location"

# episode_step
settings.tk-krita.episode_step:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-breakdown: "@settings.tk-multi-breakdown.krita"
    tk-multi-setframerange: "@settings.tk-multi-setframerange.krita"
    tk-multi-loader2: "@settings.tk-multi-loader2.krita"
    tk-multi-publish2: "@settings.tk-multi-publish2.krita.episode_step"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel.krita"
    tk-multi-snapshot: "@settings.tk-multi-snapshot.krita.episode_step"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.krita.episode_step"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Episode" ]

  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-snapshot, name: Snapshot...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  - {app_instance: tk-multi-publish2, name: Publish...}
  template_project: episode_work_area_krita
  location: "@engines.tk-krita.location"


# asset
settings.tk-krita.asset:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.krita.project"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  location: "@engines.tk-krita.location"

# asset_step
settings.tk-krita.asset_step:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-breakdown: "@settings.tk-multi-breakdown.krita"
    tk-multi-loader2: "@settings.tk-multi-loader2.krita"
    tk-multi-publish2: "@settings.tk-multi-publish2.krita.asset_step"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel"
    tk-multi-snapshot: "@settings.tk-multi-snapshot.krita.asset_step"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.krita.asset_step"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset" ]

  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-snapshot, name: Snapshot...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  - {app_instance: tk-multi-publish2, name: Publish...}
  template_project: asset_work_area_krita
  location: "@engines.tk-krita.location"

# environment_step
settings.tk-krita.environment_step:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-breakdown: "@settings.tk-multi-breakdown.krita"
    tk-multi-loader2: "@settings.tk-multi-loader2.krita"
    tk-multi-publish2: "@settings.tk-multi-publish2.krita.environment_step"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel"
    tk-multi-snapshot: "@settings.tk-multi-snapshot.krita.environment_step"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.krita.environment_step"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Environment" ]

  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-snapshot, name: Snapshot...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  - {app_instance: tk-multi-publish2, name: Publish...}
  template_project: environment_work_area_krita
  location: "@engines.tk-krita.location"

# environment_step_notask
settings.tk-krita.environment_step_notask:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-breakdown: "@settings.tk-multi-breakdown.krita"
    tk-multi-loader2: "@settings.tk-multi-loader2.krita"
    tk-multi-publish2: "@settings.tk-multi-publish2.krita.environment_step_notask"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel"
    tk-multi-snapshot: "@settings.tk-multi-snapshot.krita.environment_step_notask"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.krita.environment_step_notask"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Environment" ]

  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-snapshot, name: Snapshot...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  - {app_instance: tk-multi-publish2, name: Publish...}
  template_project: environment_work_area_krita
  location: "@engines.tk-krita.location"

# sequence
settings.tk-krita.sequence:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.krita.project"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  location: "@engines.tk-krita.location"

# sequence_step
settings.tk-krita.sequence_step:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-breakdown: "@settings.tk-multi-breakdown.krita"
    tk-multi-setframerange: "@settings.tk-multi-setframerange.krita"
    tk-multi-loader2: "@settings.tk-multi-loader2.krita"
    tk-multi-publish2: "@settings.tk-multi-publish2.krita.sequence_step"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel.krita"
    tk-multi-snapshot: "@settings.tk-multi-snapshot.krita.sequence_step"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.krita.sequence_step"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Sequence" ]

  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-snapshot, name: Snapshot...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  - {app_instance: tk-multi-publish2, name: Publish...}
  template_project: shot_work_area_krita
  location: "@engines.tk-krita.location"


# shot
settings.tk-krita.shot:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.krita.project"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  location: "@engines.tk-krita.location"

# shot_step
settings.tk-krita.shot_step:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-breakdown: "@settings.tk-multi-breakdown.krita"
    tk-multi-setframerange: "@settings.tk-multi-setframerange.krita"
    tk-multi-loader2: "@settings.tk-multi-loader2.krita"
    tk-multi-publish2: "@settings.tk-multi-publish2.krita.shot_step"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel.krita"
    tk-multi-snapshot: "@settings.tk-multi-snapshot.krita.shot_step"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.krita.shot_step"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Shot" ]

  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-snapshot, name: Snapshot...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  - {app_instance: tk-multi-publish2, name: Publish...}
  template_project: shot_work_area_krita
  location: "@engines.tk-krita.location"
