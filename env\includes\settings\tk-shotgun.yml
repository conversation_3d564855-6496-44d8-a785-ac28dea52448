# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

################################################################################

includes:
- ../engine_locations.yml
- ../app_locations.yml
- ./tk-multi-launchapp.yml
- ./tk-multi-publish2.yml
- ./tk-multi-screeningroom.yml
- ./tk-shotgun-folders.yml
- ./tk-shotgun-launchfolder.yml
- ./tk-shotgun-launchpublish.yml

################################################################################

# asset
settings.tk-shotgun.asset:
  apps:
    tk-multi-launchapp: "@settings.tk-multi-launchapp"
    tk-multi-launchmari: "@settings.tk-multi-launchapp.mari"
    tk-multi-launchmotionbuilder: "@settings.tk-multi-launchapp.motionbuilder"
    tk-multi-publish2: "@settings.tk-multi-publish2.standalone"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-shotgun-folders: "@settings.tk-shotgun-folders"
    tk-shotgun-launchfolder: "@settings.tk-shotgun-launchfolder"
  location: "@engines.tk-shotgun.location"

# asset_step
settings.tk-shotgun.asset_step:
  apps:
    tk-multi-launchapp: "@settings.tk-multi-launchapp.shotgun"
    tk-multi-launchmari: "@settings.tk-multi-launchapp.mari"
    tk-multi-launchmotionbuilder: "@settings.tk-multi-launchapp.motionbuilder"
    tk-multi-publish2: "@settings.tk-multi-publish2.standalone"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-shotgun-launchfolder: "@settings.tk-shotgun-launchfolder"
  location: "@engines.tk-shotgun.location"

# asset_step_notask
settings.tk-shotgun.asset_step_notask:
  apps:
    tk-multi-launchapp: "@settings.tk-multi-launchapp.shotgun"
    tk-multi-launchmari: "@settings.tk-multi-launchapp.mari"
    tk-multi-launchmotionbuilder: "@settings.tk-multi-launchapp.motionbuilder"
    tk-multi-publish2: "@settings.tk-multi-publish2.standalone"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-shotgun-launchfolder: "@settings.tk-shotgun-launchfolder"
  location: "@engines.tk-shotgun.location"

# project
settings.tk-shotgun.project:
  apps:
    tk-multi-launchapp: "@settings.tk-multi-launchapp"
    tk-multi-launchhiero: "@settings.tk-multi-launchapp.hiero"
    tk-multi-launchmari: "@settings.tk-multi-launchapp.mari"
    tk-multi-launchmotionbuilder: "@settings.tk-multi-launchapp.motionbuilder"
    tk-multi-publish2: "@settings.tk-multi-publish2.standalone"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-shotgun-launchfolder: "@settings.tk-shotgun-launchfolder"
  location: "@engines.tk-shotgun.location"

# publishedfile_version
settings.tk-shotgun.version:
  apps:
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-shotgun-launchpublish: "@settings.tk-shotgun-launchpublish"
    mty-multi-queue:
      location: '@apps.mty-multi-queue.location'
    mty-executeaction-editorial-downloader:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Download for Editorial"
      action_hook: shotgun_ami_editorial_downloader
      sg_extended_fields: {}
      allowed_entities: ["Version"]
      supports_multiple_selection: True
  location: "@engines.tk-shotgun.location"

settings.tk-shotgun.publishedfile:
  apps:
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-shotgun-launchpublish: "@settings.tk-shotgun-launchpublish"
    tk-shotgun-launchfolder: "@settings.tk-shotgun-launchfolder"
    mty-multi-queue:
      location: '@apps.mty-multi-queue.location'
    mty-executeaction-ensure-local-publish:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Ensure Local Publish"
      action_hook: shotgun_ami_ensure_local_publish
      sg_extended_fields: {}
      allowed_entities: ["PublishedFile"]
      supports_multiple_selection: True
  location: "@engines.tk-shotgun.location"

# episode
settings.tk-shotgun.episode:
  apps:
    tk-multi-launchapp: "@settings.tk-multi-launchapp"
    tk-multi-launchmotionbuilder: "@settings.tk-multi-launchapp.motionbuilder"
    tk-multi-publish2: "@settings.tk-multi-publish2.standalone"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-shotgun-folders: "@settings.tk-shotgun-folders"
    tk-shotgun-launchfolder: "@settings.tk-shotgun-launchfolder"
  location: "@engines.tk-shotgun.location"


# episode_step
settings.tk-shotgun.episode_step:
  apps:
    tk-multi-launchapp: "@settings.tk-multi-launchapp.shotgun"
    tk-multi-launchmari: "@settings.tk-multi-launchapp.mari"
    tk-multi-launchmotionbuilder: "@settings.tk-multi-launchapp.motionbuilder"
    tk-multi-publish2: "@settings.tk-multi-publish2.standalone"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-shotgun-launchfolder: "@settings.tk-shotgun-launchfolder"
  location: "@engines.tk-shotgun.location"


# sequence
settings.tk-shotgun.sequence:
  apps:
    tk-multi-launchapp: "@settings.tk-multi-launchapp"
    tk-multi-launchmotionbuilder: "@settings.tk-multi-launchapp.motionbuilder"
    tk-multi-publish2: "@settings.tk-multi-publish2.standalone"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-shotgun-folders: "@settings.tk-shotgun-folders"
    tk-shotgun-launchfolder: "@settings.tk-shotgun-launchfolder"
  location: "@engines.tk-shotgun.location"


# sequence_step
settings.tk-shotgun.sequence_step:
  apps:
    tk-multi-launchapp: "@settings.tk-multi-launchapp.shotgun"
    tk-multi-launchmari: "@settings.tk-multi-launchapp.mari"
    tk-multi-launchmotionbuilder: "@settings.tk-multi-launchapp.motionbuilder"
    tk-multi-publish2: "@settings.tk-multi-publish2.standalone"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-shotgun-launchfolder: "@settings.tk-shotgun-launchfolder"
  location: "@engines.tk-shotgun.location"


# shot
settings.tk-shotgun.shot:
  apps:
    tk-multi-launchapp: "@settings.tk-multi-launchapp"
    tk-multi-launchmotionbuilder: "@settings.tk-multi-launchapp.motionbuilder"
    tk-multi-publish2: "@settings.tk-multi-publish2.standalone"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-shotgun-folders: "@settings.tk-shotgun-folders"
    tk-shotgun-launchfolder: "@settings.tk-shotgun-launchfolder"
  location: "@engines.tk-shotgun.location"

# shot_step
settings.tk-shotgun.shot_step:
  apps:
    tk-multi-launchapp: "@settings.tk-multi-launchapp.shotgun"
    tk-multi-launchmari: "@settings.tk-multi-launchapp.mari"
    tk-multi-launchmotionbuilder: "@settings.tk-multi-launchapp.motionbuilder"
    tk-multi-publish2: "@settings.tk-multi-publish2.standalone"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-shotgun-launchfolder: "@settings.tk-shotgun-launchfolder"
  location: "@engines.tk-shotgun.location"
