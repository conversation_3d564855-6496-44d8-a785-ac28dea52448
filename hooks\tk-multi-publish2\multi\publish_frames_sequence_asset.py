#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

import os
# import json
# import shutil
# import subprocess
from os.path import join
from pprint import pformat

import sgtk
from sgtk.platform.qt import QtGui, QtCore

HookBaseClass = sgtk.get_hook_baseclass()


class PublishFramesSeqPlugin(HookBaseClass):
    """
    Plugin for publish rendered sequence

    """

    @property
    def name(self):
        """
        One line display name describing the plugin
        """
        return "Publish sequence files"

    @property
    def description(self):
        """
        Copy/upload the render sequence to publish area
        """

        loader_url = "https://support.shotgunsoftware.com/hc/en-us/articles/219033078"

        publisher = self.parent
        engine = publisher.engine

        description = """
            This plugin publish copy render files to publish area and
            register in shotgun.

            Optionally create EXR into publish area with the multiple layers appended.
            """

        return "<p>{}</p>".format(description)

    @property
    def settings(self):
        """
        Dictionary defining the settings that this plugin expects to recieve
        through the settings parameter in the accept, validate, publish and
        finalize methods.

        A dictionary on the following form::

            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }

        The type string should be one of the data types that toolkit accepts
        as part of its environment configuration.
        """

        # settings specific to this class
        publish_settings = {
            "Work Template": {
                "type": "template",
                "default": "harmony_shot_work",
                "description": "Harmony work session",
            },
            "Primary Publish Template": {
                "type": "template",
                "default": "harmony_shot_publish",
                "description": "Harmony publish session",
            },
            "Publish Render Template": {
                "type": "template",
                "default": "shot_flat_render_publish_exr",
                "description": "Template to publish and copy render sequence files",
            },
            "Image Sequence Workflow": {
                "type": "str",
                "default": "grouped",
                "description": "Define the workflow to collect image sequences,"
                " which can be either 'individual' or 'grouped'",
            },
            "Publish Render Type": {
                "type": "str",
                "default": "Rendered Image",
                "description": "Published File Type name to use for the published frames",
            },
            "Invalid Image Extensions": {
                "type": "list",
                "default": [],
                "description": "Invalid image extensions for current environment",
            },
            # "Resolution Validation Ignore Tasks": {
            #     "type": "list",
            #     "default": [],
            #     "description": "Tasks to ignore resolution validation"
            # },
        }

        # inherit the settings from the base publish plugin
        plugin_settings = super(PublishFramesSeqPlugin, self).settings or {}

        # update the base settings
        plugin_settings.update(publish_settings)

        return plugin_settings

    @property
    def item_filters(self):
        """
        List of item types that this plugin is interested in.

        Only items matching entries in this list will be presented to the
        accept() method. Strings can contain glob patters such as *, for example
        ["harmony.*", "file.harmony"]
        """
        return ["harmony.frames_sequence"]

    def accept(self, settings, item):
        """
        Method called by the publisher to determine if an item is of any
        interest to this plugin. Only items matching the filters defined via the
        item_filters property will be presented to this method.

        A submit for review task will be generated for each item accepted here. Returns a
        dictionary with the following booleans:

            - accepted: Indicates if the plugin is interested in this value at
                all. Required.
            - enabled: If True, the plugin will be enabled in the UI, otherwise
                it will be disabled. Optional, True by default.
            - visible: If True, the plugin will be visible in the UI, otherwise
                it will be hidden. Optional, True by default.
            - checked: If True, the plugin will be checked in the UI, otherwise
                it will be unchecked. Optional, True by default.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process

        :returns: dictionary with boolean keys accepted, required and enabled.
            This plugin makes use of the tk-multi-reviewsubmission app; if this
            app is not available then the item will not be accepted, this method
            will return a dictionary with the accepted field that has a value of
            False.

            Several properties on the item must also be present for it to be
            accepted. The properties are 'path', 'publish_name', 'color_space',
            'first_frame' and 'last_frame'
        """

        publisher = self.parent
        engine = publisher.engine
        context = engine.context

        # Verify entity type Asset
        entity_type = context.entity.get("type", None) or str(context).split(" ")[1]
        if entity_type != "Asset":
            return {"accepted": False}

        return {"accepted": True, "checked": True}

    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish. Returns a
        boolean to indicate validity.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        :returns: True if item is valid and not in proxy mode, False otherwise.
        """

        # sg_shot = self.get_sg_shot_info(["sg_cut_in", "sg_cut_out"])
        # shot_duartion = int(sg_shot["sg_cut_out"]) - int(sg_shot["sg_cut_in"])

        sequence = item.properties["sequence"]
        layers_sequences = item.properties.get("layers_sequences", [])

        render_publish_template_setting = settings.get("Publish Render Template")
        render_publish_template = self.parent.engine.get_template_by_name(
            render_publish_template_setting.value
        )

        # optional image format/extension validations
        invalid_exts = settings.get("Invalid Image Extensions", []).value
        self.parent.logger.debug("Invalid Image Extensions: {0}".format(invalid_exts))
        current_extension = sequence.format("{extension}").replace(".", "").lower()
        self.parent.logger.debug(
            "Current Image Extension: {0}".format(current_extension)
        )
        if current_extension in invalid_exts:
            valid_choices = render_publish_template.keys["extension"].choices
            self.parent.logger.debug("Valid Choices: {0}".format(valid_choices))
            valid_exts = [e for e in valid_choices if e not in invalid_exts]
            error_msg = (
                "Your rendered image sequence is using an invalid image "
                "extension ({0}), at the moment, only the following ones "
                "are supported: {1}"
            ).format(current_extension, valid_exts)
            self.logger.error(error_msg)
            return False

        # Other known issue is if the render naming convention uses "-"
        # that will cause conflict with the image sequence recognition
        # and will make the numbers look like negative, which will cause problems

        beauty_start = sequence.start()
        beauty_end = sequence.end()
        beauty_padding = sequence.padding()
        item.properties["valid_layers_sequences"] = []

        sequence_name_format = "{basename}{padding}{extension} ({start}-{end})"
        display_name = sequence.format(sequence_name_format)

        # Validating
        for layer_seq in layers_sequences:
            valid = True
            error_msg = ""
            layer_n = layer_seq._base[:-2]

            # Validation 1:  Name
            if not layer_seq._base.endswith("."):
                valid = False
                error_msg = "Invalid name"
            elif "_" in layer_n or "." in layer_n or " " in layer_n:
                valid = False
                error_msg = "Invalid name"

            # Validation 2: Frames
            if layer_seq.start() != beauty_start or layer_seq.end() != beauty_end:
                valid = False
                if error_msg != "":
                    error_msg += ", frame range"
                else:
                    error_msg = "Invalid frame range"

            # Validation 3: Padding
            if layer_seq.padding() != beauty_padding:
                valid = False
                if error_msg != "":
                    error_msg += ", invalid padding"
                else:
                    error_msg = "Invalid padding"

            # # Validation 4: Resolution
            # is_valid, msg = self.validate_image_resolution(
            #     layer_seq, validate_image_res_dict
            # )
            # if not is_valid:
            #     valid = False
            #     if error_msg != "":
            #         error_msg += ", resolution"
            #     else:
            #         error_msg = "Invalid resolution"

            sequence_name_format = "{basename}{padding}{extension} ({start}-{end})"
            display_name = layer_seq.format(sequence_name_format)
            display_name = display_name.replace("@@@", "%03d")
            display_name = display_name.replace("#", "%04d")

            if not valid:
                self.logger.warning(
                    "Layer ommited. {}: {}".format(error_msg, display_name)
                )
                continue

            self.logger.info("Valid layer: {}".format(display_name))
            item.properties["valid_layers_sequences"].append(layer_seq)
            continue

            # Checking first frame number
            if layer_seq.start() != 1:
                self.logger.warning(
                    "Layer ommited. Invalid range: {}".format(display_name)
                )
                continue

            # Checking sequence duration
            seq_duration = layer_seq.start() - layer_seq.end()
            if seq_duration < shot_duartion:
                self.logger.warning(
                    "Layer ommited. Invalid duration: {}".format(display_name)
                )
                continue

            # If layer sequence pass all validations
            self.logger.info("Valid layer: {}".format(display_name))
            item.properties["valid_layers_sequences"].append(layer_seq)

        # Validating maya name
        if "custom_validations" in item.properties:
            if item.properties["custom_validations"]["validate_callback"](item):
                self.logger.warning(
                    "Found invalid non-alphanumeric characters in name: {}".format(
                        sequence.basename()
                    ),
                    extra={
                        "action_button": {
                            "label": "Change Names",
                            "tooltip": "Change current saved sequence names",
                            "callback": lambda: item.properties["custom_validations"][
                                "autofix_callback"
                            ](item),
                        }
                    },
                )
                return False
        return True

    def publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """

        message = "Publish plugin settings:\n%s" % pformat(settings)
        self.parent.engine.logger.debug(message)

        engine = self.parent.engine
        path = item.properties["scene_path"]
        sequence = item.properties["sequence"]
        for source_frame in sequence:
            # self.logger.info(source_frame)
            if not os.path.exists(source_frame):
                raise Exception("{} does not exist".format(source_frame))
        sequence_text_id = item.properties["sequence_text_id"]
        sequence_text2_id = item.properties.get("sequence_text2_id")

        # Getting settings
        primary_publish_template_setting = settings.get("Primary Publish Template")
        render_publish_template_setting = settings.get("Publish Render Template")
        render_publish_type_setting = settings.get("Publish Render Type")
        work_template_setting = settings.get("Work Template")

        image_sequence_workflow = settings.get("Image Sequence Workflow").value

        # We support two different workflows here:
        #
        # 1- The collector created a single main layer item and it
        #    contains all layers grouped in its properties
        #    under the "layers_sequences" key
        #
        # 2- The collector created all layers as individual items
        #
        # The main difference is related to how we can publish
        # the image sequences, where in the first case we can
        # do it ideally as a single published file, for example
        # as a single multi layer exr that we can combine in the
        # publish hook, and in a second case, where we are not
        # able to do a single publish, but we are forced to do
        # one publish per image sequence
        #
        # The collection workflow will be defined by a setting
        # which is set in the environment settings:
        # - Image Sequence Workflow
        #   "individual" or "grouped"
        #
        # TODO: Ideally we should control using an SG override
        #       we need to add the mty-framework-valueoverrides
        #       and define a setting for this case and update
        #       the code acoordingly
        # Get templates from settings

        primary_publish_template = engine.get_template_by_name(
            primary_publish_template_setting.value
        )
        render_publish_template = engine.get_template_by_name(
            render_publish_template_setting.value
        )
        work_template = engine.get_template_by_name(work_template_setting.value)

        # Fields from work path
        fields = work_template.get_fields(path)

        # Dynamic paths and elements from templates and settings
        primary_publish_path = primary_publish_template.apply_fields(fields)
        publish_version = fields["version"]

        # template can be dynamic for file extension, in that case we
        # need to set the proper image file extention
        ext = sequence._ext.lower().replace(".", "")
        fields["extension"] = ext

        message = "Item Properties:\n%s" % pformat(item.properties)
        self.parent.engine.logger.debug(message)

        # optionally, if we need to add the layer name
        grouped_workflow = False
        if image_sequence_workflow == "individual":
            fields["layer"] = sequence_text_id
            if sequence_text2_id:
                fields["buffer"] = sequence_text2_id
        else:
            grouped_workflow = True

        self.parent.engine.logger.debug(
            "sequence fields before applying them to render_publish_template:\n{}".format(
                pformat(fields)
            )
        )
        render_publish_path = render_publish_template.apply_fields(fields)
        render_publish_type_name = render_publish_type_setting.value

        # Ensure folder exists
        self.parent.engine.ensure_folder_exists(os.path.dirname(render_publish_path))

        # Proceed with the corresponding image sequence workflow
        description_updated = item.description
        if grouped_workflow:
            description_updated = self.sequence_grouped_workflow(
                item, sequence, render_publish_path
            )
        else:
            # Assets don't have a defined frame range so start frame for the copy image
            # sequence method is None, so internally the first frame of the sequence is
            # used as start frame
            self.copy_image_sequence(
                sequence, render_publish_path, start_frame=None
            )

        # get media resolution
        media_resolution = self.parent.execute_hook_expression(
            "{config}/tk-multi-publish2/multi/resolution_from_path.py",
            "media_resolution_value",
            path=render_publish_path
        )
        self.parent.logger.info(
            "media resolution from hook: {}".format(media_resolution)
        )

        # finally register the sequence publish
        message = "Publish sequence in shotgun"
        self.logger.info(message)

        message = "File name: {}".format(os.path.basename(render_publish_path))
        self.logger.info(message)

        publish_name = self.get_publish_name(render_publish_path)

        args = {
            "tk": self.parent.engine.sgtk,
            "context": item.context,
            "comment": description_updated,
            "path": render_publish_path,
            "name": publish_name,
            "version_number": publish_version,
            "thumbnail_path": item.get_thumbnail_as_path(),
            "task": self.parent.engine.context.task,
            "dependency_paths": [primary_publish_path],
            "sg_fields": {
                "sg_status_list": "rev",
                "sg_media_resolution": media_resolution
                },
            "published_file_type": render_publish_type_name,
        }

        sg_publishes = sgtk.util.register_publish(**args)

        root_item = self.get_root_item(item)
        self.parent.logger.debug(
            "Storing extra publish data on root item: {}".format(root_item)
        )
        root_item.properties.setdefault("sg_publish_extra_data", [])
        publish_extra_data = root_item.properties["sg_publish_extra_data"]
        publish_extra_data.append(sg_publishes)

        item.properties.sg_publish_data = sg_publishes
        item.properties.sg_publish_path = sg_publishes["path"]["local_path"]

        self.parent.logger.debug(
            "Adding existing publish "
            + "(id:{}) ".format(sg_publishes["id"])
            + "as extra data for item: {}".format(item)
        )

        self.logger.info("Publish sequence in shotgun. Complete")

        # Update task status for revision
        sg_task = self.parent.context.task
        self.parent.logger.info(
            "End of Publish and updating task with id: {} to status: 'rev'".format(
                str(sg_task["id"])
            )
        )

        try:
            self.parent.engine.shotgun.update(
                "Task", sg_task["id"], {"sg_status_list": "rev"}
            )
        except:
            pass

    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        pass

    def get_publish_name(self, path):
        import re
        import os

        regex = r"(?P<name>.*)(?P<ver>[._-]v\d+)(?P<padd>\.?[^.]+)?(?P<ext>\.[^.]+)"

        name = os.path.basename(path)
        match_data = re.match(regex, name).groupdict()

        for key in ["ver", "padd"]:
            value = match_data.get(key)
            if value:
                name = name.replace(value, "")

        return name

    def sequence_grouped_workflow(self, item, beauty_sequence, render_publish_path):
        """ """

        # Image sequence data
        beauty_base = beauty_sequence._base
        beauty_folder = beauty_sequence._dir
        beauty_ext = beauty_sequence._ext
        padding = len(beauty_sequence._frame_pad)
        frame_number = beauty_sequence.start()

        # Assets doesn't need an offset as has no defined frame range, so offset is
        # efectively the first_frame of the sequence
        offset = frame_number

        created_multi_exr = False
        layer_names = []
        valid_layers_sequences = item.get_property(
            "valid_layers_sequences", default_value=[]
        )
        valid_layers_count = len(valid_layers_sequences)

        if valid_layers_count and beauty_ext.lower() == ".exr":
            # Multi process creation
            message = "Creating multilayer exr: {} Layers".format(valid_layers_count)
            self.parent.logger.debug(message)
            self.logger.info(message)

            format_str = "{}:{}::{}"
            openExr_framework = self.load_framework("mty-framework-openexr")
            openExr = openExr_framework.exrCoreTools
            openExr.set_binary("multipart")

            # Register names of all layer to add to the comments.
            layer_names.append(beauty_base[:-1])
            for sub_layer in valid_layers_sequences:
                layer_names.append(sub_layer._base[:-1])

            # Create folder if does not exists
            container_folder = os.path.dirname(render_publish_path)
            self.parent.engine.ensure_folder_exists(container_folder)

            for source_frame in beauty_sequence:
                # we will iterate over all frames, from first to last
                # and we will rename them using shot range
                final_frame = frame_number + offset
                destination_frame = render_publish_path % final_frame
                cmd = ["-combine", "-i"]
                padding = len(source_frame.split(".")[1])
                frame_str = str(frame_number).zfill(padding)

                beauty_file = "{}{}{}".format(beauty_base, frame_str, beauty_ext)
                beauty_path = join(beauty_folder, beauty_file)

                # First layer
                cmd.append(
                    format_str.format(source_frame, 0, item.properties["beauty_output"])
                )

                for layer_sequence in valid_layers_sequences:
                    base_name = layer_sequence._base
                    aov_folder = layer_sequence._dir
                    aov_ext = layer_sequence._ext
                    aov_file = "{}{}{}".format(base_name, frame_str, aov_ext)
                    aov_path = join(aov_folder, aov_file)
                    # Layer N
                    cmd.append(format_str.format(aov_path, 0, base_name[:-1]))

                cmd.append("-o")
                cmd.append(destination_frame)
                cmd.append("-override")

                _err, _info = openExr.execute_command_list(cmd)
                if _err:
                    created_multi_exr = False
                    message = "Failed to create multi exr: {}\ncommand: {}"
                    message = message.format(_info, cmd)
                    self.logger.error(message)
                    raise Exception(message)
                else:
                    created_multi_exr = True

                frame_number += 1

            message = "Creating multilayer exr: Complete"
            self.parent.logger.debug(message)
            self.logger.info(message)

        # If the multi layer fails, then we will copy the beauty
        if not created_multi_exr:
            self.copy_image_sequence(
                beauty_sequence, render_publish_path, None
            )

        description_updated = item.description
        if len(layer_names):
            layers_str = ", ".join(layer_names)
            if description_updated is None:
                description_updated = ""
            description_updated += "\nLayers included: " + layers_str

        return description_updated

    def copy_image_sequence(self, sequence_object, sequence_path, start_frame=None):
        """
        Execute the copy of an image sequence frames using a custom hook

        :param sequence_object: fileseq.Sequence. The object representing the
                                image sequence
        :param sequence_path: str. Target path for the destination copy.
                              it must be defined in fstring format (%0{0-9}d)
        :param start_frame: int. Optional number to define the initial
                            frame number for the target path, which allows
                            for renumbering the image sequence frames
                            If not defined, the original sequence start frame
                            will be used
        """
        message = "Copy sequence to publish area. {}"
        message = message.format(os.path.basename(sequence_path))
        self.logger.info(message)
        self.parent.logger.info(message)

        if start_frame:
            frame_number = start_frame
        else:
            frame_number = sequence_object.start()

        for source_frame in sequence_object:
            # we will iterate over all frames, from first to last
            # and we will rename them using shot range

            source_frame = os.path.normpath(source_frame)

            destination_frame = sequence_path % frame_number
            message = "Copying image sequence frame:\nfrom: {0}\nTo: {1}"
            self.parent.logger.debug(message.format(source_frame, destination_frame))

            # Custom hook to copy files
            self.parent.execute_hook_expression(
                "{config}/tk-multi-publish2/harmony/copy_files.py",
                "execute",
                source=source_frame,
                destination=destination_frame,
            )

            frame_number += 1

        message = "Copy sequence to publish area. Complete"
        self.parent.logger.info(message)
        self.logger.info(message)

    def get_sg_project_info(self, proj_fields):
        engine = self.parent.engine
        sg = engine.shotgun
        sg_proj = engine.context.project

        proj_filter = [["id", "is", sg_proj["id"]]]

        sg_proj = sg.find_one("Project", proj_filter, proj_fields)
        return sg_proj

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root

    def show_message_box_message(self, title, msg):
        """
        Shows a message box
        """

        app = QtGui.QApplication.instance()
        msg_error = QtGui.QMessageBox()
        msg_error.setWindowTitle(title)
        msg_error.setText(msg)
        msg_error.setIcon(QtGui.QMessageBox.Critical)
        msg_error.exec_()
        # msg_error.show()

        return msg_error
