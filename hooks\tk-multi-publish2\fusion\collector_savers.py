# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

import os
import sgtk
from tank_vendor import six
import BlackmagicFusion as bmd
import glob

HookBaseClass = sgtk.get_hook_baseclass()
fusion = bmd.scriptapp("Fusion")


class FusionSessionCollector(HookBaseClass):
    """
    Collector that operates on the fusion session. Should inherit from the basic
    collector hook.
    """

    @property
    def settings(self):
        """
        Dictionary defining the settings that this collector expects to receive
        through the settings parameter in the process_current_session and
        process_file methods.

        A dictionary on the following form::

            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }

        The type string should be one of the data types that toolkit accepts as
        part of its environment configuration.
        """

        # grab any base class settings
        collector_settings = super(FusionSessionCollector, self).settings or {}

        # settings specific to this collector
        fusion_session_settings = {
            "Work area template": {
                "type": "template",
                "default": "fusion_shot_work",
                "description": "Original comp path"},
            "Work render template": {
                "type": "template",
                "default": "shot_flat_render_work_exr",
                "description": "Original path from render"},
            "Publish render sequence template":{
                "type": "template",
                "default": "shot_flat_render_publish_exr",
                "description": "Template to publish and copy sequence files"},
            "Publish render mov template":{
                "type": "template",
                "default": "shot_flat_render_publish_mov",
                "description": "Template to create a mov file"},
        }

        # update the base settings with these settings
        collector_settings.update(fusion_session_settings)

        return collector_settings

    def process_current_session(self, settings, parent_item):
        """
        Analyzes the current session open in Fusion and parents a subtree of
        items under the parent_item passed in.

        :param dict settings: Configured settings for this collector
        :param parent_item: Root item instance

        """
        super(FusionSessionCollector, self).process_current_session(settings, parent_item)
        session_item = next((item for item in parent_item.descendants if item.type_spec == 'fusion.session'), None)
        
        if session_item is None:
            return

        comp = fusion.GetCurrentComp()
        loader_dependencies = self.get_fusion_dependencies()

        _path         = comp.GetAttrs()['COMPS_FileName']
        work_template = self.tank.template_from_path(_path)
        work_fields   = work_template.get_fields(_path)
        work_version  = work_fields.get('version')

        savers = comp.GetToolList(False, "Saver").values()
        for saver in savers:
            saver_path     = saver.GetAttrs()['TOOLST_Clip_Name'].values()[0]
            template_saver = self.tank.templates_from_path(saver_path)

            # Avoid savers out of the pipeline
            if not len(template_saver):
                continue

            template_saver = template_saver[0]
            fields = template_saver.get_fields(saver_path)

            # Avoid old savers
            if fields['version'] != work_version:
                continue

            if not 'aov_name' in fields.keys():
                continue
            if fields['aov_name'] != 'default':
                continue 

            # create an item representing the current fusion session
            item = self.collect_current_saver_session(settings,
                                                      session_item,
                                                      saver,
                                                      loader_dependencies)
            self.collect_sg_savernode(item)
        
    def collect_current_saver_session(self, settings, parent_item, saver_node, loader_dependencies):
        """
        Creates an item that represents the current fusion session.

        :param parent_item: Parent Item instance

        :returns: Item of type fusion.session
        """

        publisher = self.parent

        # get the path to the current file
        path = _session_path()

        # determine the display name for the item
        display_name = "Fusion saver node: {}".format(saver_node.GetAttrs()["TOOLS_Name"])

        # create the session item for the publish hierarchy
        session_item = parent_item.create_item(
            "fusion.session.savers", "Saver node", display_name)

        # get the icon path to display for this item
        icon_path = os.path.join(
            self.disk_location, os.pardir, "icons", "fusion.png")
        session_item.set_icon_from_path(icon_path)

        # if a work template is defined, add it to the item properties so
        # that it can be used by attached publish plugins
        work_template_setting        = settings.get("Work render template")
        publish_template_setting     = settings.get("Publish render sequence template")
        publish_mov_template_setting = settings.get("Publish render mov template")
        work_area_template_settings  = settings.get("Work area template")

        if work_template_setting:
            work_template = publisher.engine.get_template_by_name(
                work_template_setting.value)

            work_area_template = publisher.engine.get_template_by_name(
                work_area_template_settings.value)

            publish_template = publisher.engine.get_template_by_name(
                publish_template_setting.value)

            publish_mov_template = publisher.engine.get_template_by_name(
                publish_mov_template_setting.value)

            primery_publish_template = publisher.engine.get_template_by_name(
                "fusion_shot_publish")

            session_item.properties["saver_node"]               = saver_node
            session_item.properties["work_template"]            = work_template
            session_item.properties["work_area_template"]       = work_area_template
            session_item.properties["primary_publish_template"] = primery_publish_template
            session_item.properties["publish_render_template"]  = publish_template
            session_item.properties["publish_mov_template"]     = publish_mov_template
            session_item.properties["publish_type"]             = "Rendered Image"
            session_item.properties["publish_video_type"]       = "Media Review"
            session_item.properties["loader_dependencies"]      = loader_dependencies

            self.logger.debug("Work template defined for Fusion Savers collection.")
        self.logger.info("Collected current Fusion scene: Savers")
        
        return session_item

    def get_fusion_dependencies(self):
        comp = fusion.GetCurrentComp()
        loaders = comp.GetToolList(False, "Loader").values()
        loader_dependencies = []
        
        for loader in loaders:
            clip_info = loader.GetAttrs()['TOOLST_Clip_Name']
            if clip_info is None:
                continue

            l_path = clip_info[1]
            base_template = self.parent.sgtk.template_from_path(l_path)
            if base_template is None:
                continue

            if base_template.name == 'shot_flat_render_publish_exr':
                continue
            loader_dependencies.append(l_path)
        
        return loader_dependencies

    def collect_sg_savernode(self, parent_item):
        comp = fusion.GetCurrentComp()

        publisher     = self.parent
        engine        = publisher.engine
        context       = engine.context
        sg            = engine.shotgun
        entity_type   = str(context).split(' ')[1]
        path          = comp.GetAttrs()['COMPS_FileName']
        work_template = engine.sgtk.template_from_path(path)
        work_version  = work_template.get_fields(path).get('version')

        saver    = parent_item.properties['saver_node']
        path     = saver.GetAttrs()['TOOLST_Clip_Name'].values()[0]
        template = engine.sgtk.template_from_path(path)

        if template:
            fields = template.get_fields(path)
            template_version = fields.get('version')
            if template_version != work_version:
                return
            frames = template.apply_fields(fields)
            base, ext = os.path.splitext(frames)
            if '.mov' not in ext:
                # Validate first frame registered in sg
                base        = '.'.join(base.split('.')[:-1])
                sg_shot     = self.get_sg_shot_info(['sg_cut_in', 'sg_cut_out'])
                first_path  = '.'.join([base, str(sg_shot['sg_cut_in']).zfill(4)])
                first_path += ext
                if os.path.exists(first_path):
                    rendered_paths = glob.glob("%s*%s" % (base, ext))
                    if rendered_paths:
                        super(FusionSessionCollector, self)._collect_file(
                                parent_item,
                                rendered_paths[0],
                                frame_sequence=True
                                )
            else:
                return
                if os.path.exists(frames):
                    savers_list.append(saver)
                    super(FusionSessionCollector, self)._collect_file(
                            parent_item,
                            frames
                            )

    def get_sg_shot_info(self, shot_fields):
        engine  = self.parent.engine
        sg      = engine.shotgun
        sg_proj = engine.context.project

        context_tokens = str(engine.context).split(' ')
        entity_name    = context_tokens[2]
        shot_filter    = [['project', 'is', sg_proj],
                          ['code', 'is', entity_name]]
        sg_shot = sg.find_one('Shot', shot_filter, shot_fields)
        return sg_shot
        
def _session_path():
    """
    Return the path to the current session
    :return:
    """
    comp = fusion.GetCurrentComp()
    path = comp.GetAttrs()['COMPS_FileName']

    if isinstance(path, unicode):
        path = path.encode("utf-8")

    return path


