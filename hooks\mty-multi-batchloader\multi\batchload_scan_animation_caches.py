########################################################################################
#
# Copyright (c) 2024 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
########################################################################################

import os
import re
import sys
import json
import traceback
from tank import Hook
from html import entities
from pprint import pformat
from tank.platform.qt import QtCore, QtGui


class ScanSceneHook(Hook):

    def scan_scene(self):

        self.parent.engine.logger.info(
            "Scanning publishes for Animation Caches...".ljust(120, "-")
        )

        items_result = []
        warnings = []
        errors = []

        context = self.parent.context
        context_shot_id = context.entity["id"]
        context_shot_name = context.entity["name"]
        context_task = context.to_dict().get("task", {})
        context_task_name = context_task.get("name")

        # Find the assets linked to the current shot -----------------------------------
        filters = [["id", "is", context_shot_id]]
        fields = ["assets", "sg_breakdowns"]
        data_shot = self.parent.shotgun.find_one("Shot", filters, fields)

        self.parent.engine.logger.debug("data_shot:\n{}".format(pformat(data_shot)))

        asset_names = [asset["name"] for asset in data_shot["assets"]]

        if not asset_names:
            msg = "No assets found for the current shot: {}".format(
                context.to_dict().get("entity", {})
            )
            self.parent.engine.logger.warning(msg)
            warnings.append(msg)
            items_result.append({})
            return items_result, warnings, errors

        # query shot breakdowns --------------------------------------------------------
        self.parent.engine.logger.info("Querying shot breakdowns...")

        breakdown_ids = None
        if data_shot.get("sg_breakdowns"):
            breakdown_ids = [
                breakdown["id"] for breakdown in data_shot["sg_breakdowns"]
            ]
        if not breakdown_ids:
            msg = (
                "The current shot doesn't have any breakdowns. "
                "Please contact your supervisor or production coordinator."
            )
            self.parent.engine.logger.error(msg)
            errors.append(msg)
            items_result.append({})
            return items_result, warnings, errors

        filters = [
            ["sg_shot", "is", {"id": context_shot_id, "type": "Shot"}],
            ["id", "in", breakdown_ids]
        ]
        fields = ["code", "sg_asset", "sg_asset.Asset.code", "sg_shot"]
        # fields = ["code", "sg_asset.Asset.code"]

        self.parent.engine.logger.debug(
            "breakdowns filters:\n{}".format(pformat(filters))
        )

        try:
            # CustomEntity30 correspond to the shot breakdown entity
            shot_breakdowns = self.parent.shotgun.find(
                "CustomEntity30", filters, fields
            )
        except Exception as e:
            msg = "Couldn't query shot breakdowns: {}, full traceback: {}".format(
                e, traceback.format_exc()
            )
            self.parent.engine.logger.error(msg)
            errors.append(msg)
            items_result.append({})
            return items_result, warnings, errors

        if not shot_breakdowns:
            msg = (
                "Couldn't query any shot breakdowns. "
                "Please contact your supervisor or production coordinator."
            )
            self.parent.engine.logger.error(msg)
            errors.append(msg)
            items_result.append({})
            return items_result, warnings, errors

        self.parent.engine.logger.info(
            "shot_breakdowns:\n{}".format(pformat(shot_breakdowns))
        )

        breakdowns_dict = {}

        for breakdown in shot_breakdowns:
            breakdown_asset = breakdown["sg_asset.Asset.code"]
            if breakdown_asset not in breakdowns_dict.keys():
                breakdowns_dict[breakdown_asset] = {"namespaces": []}

            breakdowns_dict[breakdown_asset]["namespaces"].append(
                breakdown["code"]
            )

        self.parent.engine.logger.info(
            "breakdowns_dict:\n{}".format(pformat(breakdowns_dict))
        )

        # Load value overrides framework -----------------------------------------------
        valueoverrides = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        ) or self.load_framework("mty-framework-valueoverrides")

        if not valueoverrides:
            self.parent.engine.logger.error("Couldn't load value overrides framework")

        # first load the all settings from the config settings as fallback in case of
        # missing value overrides
        supported_tasks_for_loading_anim_caches = self.parent.get_setting(
            "supported_tasks_for_loading_anim_caches"
        )
        self.parent.engine.logger.info(
            "supported_tasks_for_loading_anim_caches from hook settings: {}".format(
                supported_tasks_for_loading_anim_caches
            )
        )

        anim_caches_task_priority_list = self.parent.get_setting(
            "anim_caches_task_priority_list"
        )
        self.parent.engine.logger.info(
            "anim_caches_task_priority_list from hook settings: {}".format(
                anim_caches_task_priority_list
            )
        )

        anim_caches_published_file_type_names = self.parent.get_setting(
            "anim_caches_published_file_type_names"
        )
        self.parent.engine.logger.info(
            "anim_caches_published_file_type_names from hook settings: {}".format(
                anim_caches_published_file_type_names
            )
        )

        # then try to load the settings from value overrides
        if valueoverrides:
            # Get task priority list
            override_link = {
                "type": "Task",
                "id": self.parent.engine.context.task["id"],
            }

            default_value_code = (
                "mty.multi.batchloader.scan.supported_tasks_for_loading_anim_caches"
            )
            supported_tasks_for_loading_anim_caches_value = valueoverrides.get_value(
                default_value_code, link=override_link
            )
            if supported_tasks_for_loading_anim_caches_value:
                supported_tasks_for_loading_anim_caches = json.loads(
                    supported_tasks_for_loading_anim_caches_value
                )
                self.parent.engine.logger.info(
                    "supported_tasks_for_loading_anim_caches from override: {}".format(
                        supported_tasks_for_loading_anim_caches
                    )
                )

            default_value_code = (
                "mty.multi.batchloader.scan.anim_caches_task_priority_list"
            )
            anim_caches_task_priority_list_value = valueoverrides.get_value(
                default_value_code, link=override_link
            )
            if anim_caches_task_priority_list_value:
                anim_caches_task_priority_list = json.loads(
                    anim_caches_task_priority_list_value
                )
                self.parent.engine.logger.info(
                    "anim_caches_task_priority_list from override: {}".format(
                        anim_caches_task_priority_list
                    )
                )

            default_value_code = (
                "mty.multi.batchloader.scan.anim_caches_published_file_type_names"
            )
            anim_caches_published_file_type_names_value = valueoverrides.get_value(
                default_value_code, link=override_link
            )
            if anim_caches_published_file_type_names_value:
                anim_caches_published_file_type_names = json.loads(
                    anim_caches_published_file_type_names_value
                )
                self.parent.engine.logger.info(
                    "anim_caches_published_file_type_names from override: {}".format(
                        anim_caches_published_file_type_names
                    )
                )

        self.parent.engine.logger.info(
            "Finished loading settings, either from hook settings or value overrides"
        )

        # check if the current task is in the supported tasks list ---------------------
        self.parent.engine.logger.info(
            "context_task_name: {}, type: {}".format(
                context_task_name, type(context_task_name)
            )
        )
        self.parent.engine.logger.info(
            "supported_tasks_for_loading_anim_caches: {}, type: {}".format(
                supported_tasks_for_loading_anim_caches, type(
                    supported_tasks_for_loading_anim_caches
                )
            )
        )
        if context_task_name not in supported_tasks_for_loading_anim_caches:
            msg = (
                "Task {} not in supported tasks list for loading animation caches"
            ).format(context_task_name)
            self.parent.engine.logger.warning(msg)
            warnings.append(msg)
            items_result.append({})
            return items_result, warnings, errors

        # if the current context task is in the supported tasks list, find all the anim
        # caches published files linked to the current shot ----------------------------
        filters = [
            ["entity.Shot.id", "is", context_shot_id],
            ["entity.Shot.code", "is", context_shot_name],
            ["task.Task.content", "in", anim_caches_task_priority_list],
            ["sg_status_list", "is", "apr"],
            [
                "published_file_type.PublishedFileType.code",
                "in",
                anim_caches_published_file_type_names,
            ],
        ]

        self.parent.engine.logger.debug("Published files filters:\n{}".format(pformat(filters)))

        fields = [
            "code",
            "name",
            "published_file_type",
            "version_number",
            "task",
            "path",
            "id",
            "entity.Shot.code",
        ]

        published_files_list = self.parent.shotgun.find(
            "PublishedFile", filters, fields
        )

        if not published_files_list:
            msg = "No published files found for task {} in shot {}".format(
                context_task_name, shot_name
            )
            self.parent.engine.logger.warning(msg)
            warnings.append(msg)
            items_result.append({})
            return items_result, warnings, errors

        # Filter by task priority --------------------------------------------------
        publishes_dict = {}
        for published_file in published_files_list:
            shot_name = published_file.get("entity.Shot.code")
            task_name = published_file.get("task", {}).get("name")
            version_number = published_file.get("version_number", 1)

            if not shot_name:
                self.parent.logger.error(
                    "Couldn't get shot name for publish:\n{}".format(
                        pformat(published_file)
                    )
                )
                continue

            if shot_name not in publishes_dict.keys():
                publishes_dict[shot_name] = {
                    task_name: {version_number: [published_file]}
                }
            else:
                asset_dict = publishes_dict[shot_name]
                if task_name in asset_dict.keys():
                    if version_number in asset_dict[task_name].keys():
                        asset_dict[task_name][version_number].append(published_file)
                    else:
                        asset_dict[task_name][version_number] = [published_file]
                else:
                    asset_dict[task_name] = {version_number: [published_file]}

        # build short dictionary exclusivrly for logging
        publishes_dict_short = {}
        for shot_name_ in publishes_dict.keys():
            if shot_name_ not in publishes_dict_short.keys():
                publishes_dict_short[shot_name_] = []
            for task in publishes_dict[shot_name_].keys():
                publishes_dict_short[shot_name_].append(task)

        self.parent.logger.info(
            "Anim caches publishes_dict_short (for full dict turn on debug):\n{}".format(
                pformat(publishes_dict_short)
            )
        )
        self.parent.logger.debug(
            "Anim caches publishes_dict:\n{}".format(pformat(publishes_dict))
        )

        # Order by version number --------------------------------------------------
        for shot_name in publishes_dict.keys():
            # find available asset namespaces
            self.parent.logger.info("shot_name: {}".format(shot_name))
            self.parent.logger.info(
                "breakdowns_dict.keys(): {}".format(
                    breakdowns_dict.get(shot_name, {}).keys()
                )
            )

            all_namespaces = []
            for asset in breakdowns_dict.keys():
                all_namespaces.extend(breakdowns_dict[asset]["namespaces"])

            # usually the camera namespace is the same as the shot name, so we try
            # to remove it here as it is not relevant for the geometry animation caches
            all_namespaces = list(set(all_namespaces) - set([shot_name]))

            for task in anim_caches_task_priority_list:
                if publishes_dict[shot_name].get(task, None):
                    versions = list(publishes_dict[shot_name][task].keys())
                    latest_version = max(versions)
                    version_publishes_list = publishes_dict[shot_name][task][latest_version]

                    for publish in version_publishes_list:
                        node = publish["name"]
                        path = publish["path"]["local_path"]

                        # find namespace in path basename using regex
                        namespace_pattern = re.compile(
                            r"(?P<head>.+)_"
                            r"(?P<namespace>[a-zA-Z0-9]+)_"
                            r"(?P<tail>.+)"
                        )

                        namespace_match = re.match(
                            namespace_pattern, os.path.basename(path)
                        )
                        if namespace_match:
                            namespace = namespace_match.groupdict().get("namespace", "")
                        else:
                            namespace = ""

                        self.parent.logger.info(
                            "current namespace: '{}', publish name: '[]'".format(
                                namespace, node
                            )
                        )

                        # find out if namespace is in the available namespaces for this
                        # asset, having in account the shot breakdowns
                        asset_namespace_index = None
                        try:
                            asset_namespace_index = all_namespaces.index(namespace)
                            all_namespaces.pop(asset_namespace_index)
                        except ValueError as e:
                            msg = (
                                "Resolved namespace {} from file {} not found in "
                                "breakdown for asset {} or has been already used."
                            ).format(namespace, os.path.basename(path), shot_name)
                            self.parent.logger.error(msg)
                            errors.append(msg)
                            continue

                        items_result.append(
                            {
                                "node": "{}_animation_cache".format(namespace),
                                "path": path,
                                "process_hook": "batchload_process_shaded_assets",
                                "type": "Animation Cache",
                                "sg_data": publish,
                                "other_params": {"namespace": namespace},
                            }
                        )
                    break

            # log an error if we still have available namespaces for this asset
            if all_namespaces:
                msg = (
                    "Missing animation caches for shot '{}' with the following "
                    "namespaces:\n{}"
                ).format(shot_name, pformat(all_namespaces))
                self.parent.logger.error(msg)
                errors.append(msg)

        # Ensure metasync framework is available, but only load it once.
        if not hasattr(self, "metasync"):
            self.metasync = self.parent.engine.custom_frameworks.get(
                "mty-framework-metasync"
            )
        if not self.metasync:
            self.metasync = self.load_framework("mty-framework-metasync")

        transfersManager = self.metasync.transfersManager

        for item in items_result:
            local_path = item.get("path", "")
            if not os.path.exists(local_path):
                self.parent.logger.warning(
                    "Shaded asset file not found on disk: {}".format(local_path)
                )
                self.parent.logger.info(
                    "Trying to download published file:\n{}".format(
                        pformat(item["sg_data"])
                    )
                )

                counter = 0
                max_download_tries = 3
                while counter < max_download_tries:
                    try:
                        transfersManager.ensure_file_is_local(
                            local_path,
                            item.get("sg_data", {}),
                            sound_notifications=False,
                        )
                        break
                    except:
                        error_str = traceback.format_exc()
                        errors.append(error_str)
                        self.parent.logger.error(
                            "Failed to download file:\n{}".format(error_str)
                        )
                        counter += 1

                counter = 0
                max_download_tries = 3
                while counter < max_download_tries:
                    try:
                        transfersManager.ensure_local_dependencies(
                            item.get("sg_data", {})
                        )
                        break
                    except:
                        error_str = traceback.format_exc()
                        errors.append(error_str)
                        self.parent.logger.error(
                            "Failed to download dependencies:\n{}".format(error_str)
                        )
                        counter += 1

        self.parent.logger.debug("items_result:\n{}".format(pformat(items_result)))

        return items_result, warnings, errors
