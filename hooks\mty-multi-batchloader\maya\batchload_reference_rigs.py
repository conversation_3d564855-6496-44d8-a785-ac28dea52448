#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################


import os
import json
import pprint

import sgtk

from tank import Hook
from tank import TankError

import maya.cmds as cmds
import pymel.core as pm


class ProcessItemsHook(Hook):
	"""
	Process items to load it into the scene
	"""

	def load_item(self, item, update_progress):
		"""
		The load item method is executed once for each defined item and its purpose is
		to process the loading of the item into the escene.

		The structure of each item is a dictionary with three keys:

		- "node": The name of the 'node' that is to be operated on. Most DCCs have
			a concept of a node, path or some other way to address a particular
			object in the scene.
		- "type": The object type that this is. This is to
			knows how to handle the object.
		- "path": Path on disk to the object to be loaded.
		"""

		CoreMayaTools = self.load_framework("mty-framework-coremayatools")
		MayaMiscUtils  = CoreMayaTools.MayaMiscUtils

		if item['type'] == 'Rig File':

			update_progress({'progress': 30, 'message': 'Referencing rigs...'})


			self.parent.log_debug("loading %s" % item['path'])

			namespace = MayaMiscUtils._create_namespace('rig', item['node'])
			self.parent.log_debug("NAMESPACE %s" % namespace)

			MayaMiscUtils._create_reference(item['path'], namespace, pm)


		if item['type'] == 'Env Proxy':

			# make sure the gpuCache plugin its loaded
			if 'gpuCache' not in cmds.pluginInfo(query=True, listPlugins=True):
				cmds.loadPlugin('gpuCache')

			# finally create the node structure
			name = item['node']
			transform = cmds.createNode('transform', name=name)
			gpuShape = cmds.createNode('gpuCache', name="%sShape" % name, parent=transform)

			cmds.setAttr('%s.cacheFileName' % gpuShape, item['path'], type='string')

		if item['type'] == 'Env Overrides':

			# first load the json and store the data
			with open(item['path'], 'r') as json_file:
				overrides_data = json.load(json_file)

				matrix = overrides_data['parameters']['xform']['matrix']
				rotatePivot = overrides_data['parameters']['xform']['rotatePivot']
				scalePivot = overrides_data['parameters']['xform']['scalePivot']
				metadata = overrides_data['metadata']['mighty']

				# now list all gpu cache nodes, and find the correct one

				gpu_caches = cmds.ls(type='gpuCache')

				for gpu_node in gpu_caches:
					cache_path = cmds.getAttr("%s.cacheFileName" % gpu_node)

					fields = ['entity.Asset.sg_asset_type',
									'entity.Asset.code',
									'published_file_type',
									'published_file_type.PublishedFileType.code']

					publish_entities = sgtk.util.find_publish(self.parent.sgtk,
															[cache_path],
															fields=fields)

					if not publish_entities:
							continue

					# we expect that only one result comes back
					publish_entity = publish_entities[cache_path]

					env_asset_type = metadata['asset_type']
					asset_type = publish_entity.get('entity.Asset.sg_asset_type')

					self.parent.log_debug('Asset type: %s' % asset_type)

					if not asset_type or asset_type != env_asset_type:
							continue

					env_asset_name = metadata['asset_name']
					asset_name = publish_entity.get('entity.Asset.code')

					self.parent.log_debug('Asset name: %s' % asset_name)

					if not asset_name or asset_name != env_asset_name:
							continue

					transform = cmds.listRelatives(gpu_node, parent=True)
					if cmds.nodeType(transform) != 'transform':
							raise Exception("Incorrect parent type for GPU Cache node")

					# at this point, we should accept this node as the correct one

					cmds.xform(transform, worldSpace=False, matrix=matrix)
					cmds.xform(transform, worldSpace=False, rotatePivot=rotatePivot)
					cmds.xform(transform, worldSpace=False, scalePivot=scalePivot)

					break
