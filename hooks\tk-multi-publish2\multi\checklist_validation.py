########################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
########################################################################################

import pprint
import sgtk
from tank.platform.qt import QtCore, QtGui

QApplication = QtGui.QApplication
QDialog = QtGui.QDialog
QGroupBox = QtGui.QGroupBox
QPushButton = QtGui.QPushButton
QVBoxLayout = QtGui.QVBoxLayout
QCheckBox = QtGui.QCheckBox

pp = pprint.PrettyPrinter(indent=4)
pf = pprint.pformat
HookBaseClass = sgtk.get_hook_baseclass()


class select_output_UI(QDialog):
    accepted_signal = QtCore.Signal()
    rejected_signal = QtCore.Signal()

    def __init__(self, parent_widget=None, parent_plugin=None, *args, **kwargs):
        super(select_output_UI, self).__init__(parent_widget, *args, **kwargs)
        self.parent_plugin = parent_plugin  # Set the parent plugin reference

        # Create layout and add widgets
        self.layout = QVBoxLayout()
        # Set dialog layout
        self.setLayout(self.layout)

        self.validation_list = []

    def add_validators(self, check_list):
        # Create a group box for validations
        self.validation_group = QGroupBox("Validations")
        self.validation_layout = QVBoxLayout()
        self.validation_group.setLayout(self.validation_layout)

        for validate in check_list:
            if "code" in validate and "description" in validate:
                checkbox_validator = QCheckBox(validate["code"])
                checkbox_validator.setToolTip(validate["description"])
                self.validation_layout.addWidget(checkbox_validator)
                self.validation_list.append(
                    {"widget": checkbox_validator, "item": validate}
                )

        # Add the validation group to the main layout
        self.layout.addWidget(self.validation_group)

        self.button = QPushButton("Continue")
        self.layout.addWidget(self.button)
        # Add button signal to accept slot
        self.button.clicked.connect(self.accept)

    def closeEvent(self, event):
        self.reject()

    def accept(self):
        if self.parent_plugin:
            self.parent_plugin.parent.engine.logger.debug(
                "entering accept method of the ui class"
            )
        self.accepted_signal.emit()
        super(select_output_UI, self).accept()

    def reject(self):
        if self.parent_plugin:
            self.parent_plugin.parent.engine.logger.debug(
                "entering reject method of the ui class"
            )
        self.rejected_signal.emit()
        super(select_output_UI, self).reject()


class CheckListValidationsPlugin(HookBaseClass):
    """
    Plugin for creating custom validations. It uses a SG custom non project entity:
    CustomNonProjectEntity02 where the different validations can be defined by anyone
    with editing permissions for that particular entity. It is intended to be customized
    by the prosup team instead of relying on the pipeline team.
    """

    # NOTE: The plugin icon and name are defined by the base file plugin.

    def __init__(self, parent):
        super(CheckListValidationsPlugin, self).__init__(parent)

        self.validation_list = []
        self.validated = False
        self.validations = []
        self.non_validated = []
        self.checklist = None

    @property
    def settings(self):
        # inherit the settings from the base publish plugin
        plugin_settings = super(CheckListValidationsPlugin, self).settings or {}
        return plugin_settings

    def validate(self, settings, item):
        # Enforce validation for custom plugins from settings
        self.parent.logger.info("Running prosup checklist validations...")

        self.validation_list = []
        self.validated = False
        self.validations = []
        self.non_validated = []
        self.checklist = None

        self.checklist = self.get_project_checkvalidates()

        if not self.checklist:
            self.parent.logger.info("No checklist validations found")

            previous = super(CheckListValidationsPlugin, self).validate(settings, item)
            if not previous:
                return False
            return True

        if self.checklist:
            # Dialog for validations checklist
            self.dialog = select_output_UI(
                parent_widget=QApplication.activeWindow(), parent_plugin=self
            )
            # Connect signals to slots
            self.dialog.accepted_signal.connect(self.resume_validation)
            self.dialog.rejected_signal.connect(self.rejected_validation)
            # Set window properties
            self.dialog.setWindowTitle("Prosup Checklist Validations")
            self.dialog.setMinimumWidth(400)

            self.dialog.add_validators(self.checklist)
            self.dialog.exec_()

        self.parent.engine.logger.info(
            f"self.validated: {self.validated}"
        )
        # Ensure validation state
        if not self.validated:
            self.rejected_validation()

        item.properties["checklist_validations"] = self.validations

        if self.non_validated:
            format_string = "\n  - ".join(self.non_validated)
            self.parent.logger.error(
                "At least one mandatory prosup validation is missing:\n\n - {}".format(
                    format_string
                )
            )
            raise Exception(
                "At least one mandatory prosup validation is missing:\n\n  - {}".format(
                    format_string
                )
            )
            return False

        previous = super(CheckListValidationsPlugin, self).validate(settings, item)

        if not previous:
            return False

        return True

    def get_project_checkvalidates(self):
        result = []

        filters = [
            ["sg_type", "is", "ChecklistValidator"],
            ["sg_step.Step.code", "is", self.parent.context.step["name"]],
            ["sg_task_names", "contains", self.parent.context.task["name"]],
            ["sg_status_list", "is", "act"],
            ["sg_project.Project.id", "is", self.parent.context.project["id"]],
        ]

        fields = [
            "code",
            "description",
            "sg_primary_filter",
            "sg_secondary_filter",
            "tags",
            "sg_skippable",
            "sg_engine",
        ]

        checklis_itens = self.parent.tank.shotgun.find(
            "CustomNonProjectEntity02", filters, fields
        )

        # get engine name witrhout "tk-"
        engine_name = self.parent.engine.name
        engine_name = engine_name.replace("tk-", "")

        # Omit items that are not for the current engine
        # but item no have sg_engine set, is not skiped
        for item in checklis_itens:
            if (
                not item.get("sg_engine")
                or engine_name in item.get("sg_engine")
            ):
                result.append(item)

        return result

    def resume_validation(self):
        self.parent.engine.logger.info(
            "Entering resume_validation method of the main class"
        )
        self.validations = []
        self.non_validated = []

        self.parent.engine.logger.info(
            f"Current validations:\n{pf(self.dialog.validation_list)}"
        )

        for checkbox in self.dialog.validation_list:
            # The validate item must be checked if not skippable.
            if (
                not checkbox["widget"].isChecked()
                and not checkbox["item"]["sg_skippable"]
            ):
                self.non_validated.append(checkbox["widget"].text())
            elif (
                not checkbox["widget"].isChecked()
                and checkbox["item"]["sg_skippable"]
            ):
                self.validations.append(checkbox["widget"].text())
            else:
                self.validations.append(checkbox["widget"].text())

        self.validated = True
        self.dialog.close()
        return

    def rejected_validation(self):
        self.parent.engine.logger.info(
            "Entering rejected_validation method of the main class"
        )
        msg = "Couldn't validate checklist validations"
        self.parent.logger.error(msg)
        raise Exception(msg)
        return False
