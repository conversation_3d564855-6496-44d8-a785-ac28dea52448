# Copyright (c) 2015 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

from sgtk import Hook
import pprint


class FilterPublishes(Hook):
    """
    Hook that can be used to filter the list of publishes returned from Shotgun for the current
    location
    """

    def execute(self, publishes, **kwargs):
        """
        Main hook entry point

        :param publishes:    List of dictionaries
                             A list of  dictionaries for the current location within the app.  Each
                             item in the list is a Dictionary of the form:

                             {
                                 "sg_publish" : {Shotgun entity dictionary for a Published File entity}
                             }


        :return List:        The filtered list of dictionaries of the same form as the input 'publishes'
                             list
        """
        # app = self.parent
        self.logger.debug("loader publishes:\n{}".format(pprint.pformat(publishes)))

        result = publishes
        key = kwargs.pop("key", "sg_status_list")
        value = kwargs.pop("value", "apr")

        if key and value:
            result = [
                i
                for i in publishes
                if i["sg_publish"][key] and
                (value in i["sg_publish"][key] or i["sg_publish"][key] == value)
            ]

        self.logger.info(
            "filter_publishes_hook result:\n{}".format(pprint.pformat(result))
        )

        return result
