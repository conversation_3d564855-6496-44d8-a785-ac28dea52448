# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

import os
import sys
import hou
import sgtk

HookBaseClass = sgtk.get_hook_baseclass()


class HoudiniReviewCollector(HookBaseClass):
    """
    Collector that operates on the current houdini session. Should inherit from
    the basic collector hook.
    """

    def process_current_session(self, settings, parent_item):
        """
        """

        super(HoudiniReviewCollector, self).process_current_session(
            settings, parent_item
        )

        main_item = None
        for i in parent_item.descendants:
            if i.type_spec.endswith('.session'):
                main_item = i

        if not main_item:
            main_item = self.collect_current_houdini_session(
                settings, parent_item
            )

        self.collect_review_media(main_item)

    def collect_review_media(self, parent_item):
        """
        Creates items for known output nodes

        :param parent_item: Parent Item instance
        """

        # get fileseq in a partially generic way, untill we have mty-framework-externalpython
        # we know this hook file lives in {config}/houdini/tk-multi-publish2
        # so we will place fileseq in the generic hooks folder, and load 
        # relativelly from that folder
        config_path = os.path.dirname(os.path.dirname(self.disk_location))
        python_modules_path = os.path.join(config_path, "external_python_modules")
        python_future_path  = os.path.join(python_modules_path, "python-future")

        sys.path.append(python_modules_path)
        sys.path.append(python_future_path)

        import fileseq


        publish_icons = os.path.join(config_path, 'tk-multi-publish2', 'icons')
        icon_path = os.path.join(publish_icons, "video.png")

        rop_node = self.getRopNode()

        if rop_node:
            self.logger.info(
                "Processing %s node: %s" % (rop_node, rop_node.path())
            )

            output_path, output_path_expanded = self.getOutputPathFromRop(rop_node)

            self.parent.log_debug("output_path: {0}".format(output_path))
            self.parent.log_debug("output_path_expanded: {0}".format(output_path_expanded))

            sequence = fileseq.findSequenceOnDisk(
                os.path.normpath(output_path_expanded)
            )

            sequence_name_format = '{basename}{padding}{extension} ({start}-{end})'
            display_name         = sequence.format(sequence_name_format)
            display_name         = display_name.replace('@@@', '%03d')
            display_name         = display_name.replace('#', '%04d')


            review_item = parent_item.create_item(
                "houdini.frames_sequence",
                "Review Frames",
                display_name
            )

            review_item.properties['sequence'] = sequence
            review_item.set_icon_from_path(icon_path)

            # the item has been created. update the display name to
            # include the node path to make it clear to the user how it
            # was collected within the current session.
            review_item.name = "%s (%s)" % (review_item.name, rop_node.path())

    def getFirstSceneFrame(self):
        """Get the first frame of the playbar"""

        scene_frame_range = hou.playbar.frameRange()
        first_scene_frame = int(scene_frame_range[0])

        return first_scene_frame


    def getOutputPathFromRop(self, rop_node):
        """Get the output path currently set in the rop node"""

        parm = rop_node.parm("picture")

        first_scene_frame = self.getFirstSceneFrame()

        output_path = parm.unexpandedString()
        output_path_expanded = parm.evalAtFrame(first_scene_frame)

        return output_path, output_path_expanded

    def getNodeInstances(self, category, node_type):
        """Find all instances of the specified node type. Returns a list.
        category must be one of the following:
        hou.chopNetNodeTypeCategory()
        hou.chopNodeTypeCategory()
        hou.cop2NetNodeTypeCategory()
        hou.cop2NodeTypeCategory()
        hou.dopNodeTypeCategory()
        hou.lopNodeTypeCategory()
        hou.managerNodeTypeCategory()
        hou.nodeTypeCategories()
        hou.objNodeTypeCategory()
        hou.rootNodeTypeCategory()
        hou.ropNodeTypeCategory()
        hou.shopNodeTypeCategory()
        hou.sopNodeTypeCategory()
        hou.topNodeTypeCategory()
        hou.vopNetNodeTypeCategory()
        hou.vopNodeTypeCategory()
        node_type must be a string describing the node type (node.type().name())
        """

        nodeType = hou.nodeType(category, node_type)
        nodeInstances = nodeType.instances()

        return nodeInstances


    def getRopNode(self):
        """Get the openGL rop node"""

        opengl_node_comment = "OpenGL ROP for Flipbook SG Version. DON'T MODIFY"

        # get all opengl rop instances
        opengl_node_instances = self.getNodeInstances(
            hou.ropNodeTypeCategory(), "opengl"
        )

        # find the (first) node with the "opengl_node_comment"
        rop_node = None
        if len(opengl_node_instances) > 0:
            for rop in opengl_node_instances:
                if rop.comment() == opengl_node_comment:
                    rop_node = rop
                    break

        return rop_node
