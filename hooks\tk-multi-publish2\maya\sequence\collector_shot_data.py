# -*- coding: utf-8 -*-
# Standard library:
import mimetypes
import os
#   .   .   .   .   .   .   .   .   .   .   .
# Third party:
import sgtk
from tank_vendor import six
import pymel.core as pm
#   .   .   .   .   .   .   .   .   .   .   .
# Project:
# ================================================================


HookBaseClass = sgtk.get_hook_baseclass()


class ShotDataCollector(HookBaseClass):

    @property
    def settings(self):
        # grab any base class settings
        result = \
            super(ShotDataCollector, self).settings or {}

        return result

    def process_current_session(self, settings, parent_item):
        super(ShotDataCollector, self) \
            .process_current_session(settings, parent_item)
        if not parent_item.type_spec.endswith(".session"):
            item = next(
                (i for i in parent_item.descendants if i.type_spec.endswith(".session")),
                parent_item,
            )
        else:
            item = parent_item

        self.collect_shots_data(item)

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def collect_shots_data(self, parent_item):

        icon_path = os.path.join(
            self.disk_location,
            os.pardir,
            os.pardir,
            "icons",
            "sequence",
            "sequencerTrack.png"
        )

        list_of_shots = pm.ls(type="shot")

        shot_item = parent_item.create_item(
            "maya.session.shot",
            "Shot Assets Data",
            name="Shot-Asset links"
        )

        self.parent.engine.execute_hook_expression(
            "{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py",
            "use_farm_or_local_processing",
            item = shot_item
        )

        shot_item \
            .properties["nodes"] = list_of_shots

        shot_item.set_icon_from_path(icon_path)
