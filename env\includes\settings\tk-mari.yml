# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

################################################################################

includes:
- ../app_locations.yml
- ../engine_locations.yml
- ./tk-multi-loader2.yml
- ./tk-multi-publish2.yml
- ./tk-multi-shotgunpanel.yml
- ./tk-multi-workfiles.yml
- ./tk-mari-projectmanager.yml

################################################################################

# asset
settings.tk-mari.asset:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel"
    tk-multi-workfiles: "@settings.tk-multi-workfiles.mari"
  location: "@engines.tk-mari.location"

# asset_step
settings.tk-mari.asset_step:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-breakdown:
      location: "@apps.tk-multi-breakdown.location"
    tk-multi-loader2: "@settings.tk-multi-loader2.mari"
    tk-multi-publish2: "@settings.tk-multi-publish2.mari.asset_step"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel"
    tk-multi-workfiles: "@settings.tk-multi-workfiles.mari"
    tk-mari-projectmanager: "@settings.tk-mari-projectmanager"
  location: "@engines.tk-mari.location"

# project
settings.tk-mari.project:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel"
    tk-multi-workfiles: "@settings.tk-multi-workfiles.mari"
    mty-executeaction-ensure-folders:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Ensure Tasks Folders"
      action_hook: "{config}/tk-shotgun-executeaction/create_folders_for_assigned_tasks.py"
      sg_extended_fields: {}
      allowed_entities: ['Shot', 'Sequence', 'Asset', 'Project']
  location: "@engines.tk-mari.location"
