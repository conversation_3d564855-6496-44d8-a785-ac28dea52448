# Copyright (c) 2015 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

#
# This file is one of the central points in the Shotgun Pipeline Toolkit configuration and
# a counterpart to the folder configuration structure.
#
# The folder structure underneath the project folder is used to create folders on disk -
# templates.yml (this file) refers to those folders. Therefore, the two files need to be
# in sync. This file contains an overview of all locations that are used by Sgt<PERSON>.
#
# Whenever an app or an engine refers to a location on disk, it is using an entry defined in
# this file. For more information, see the Shotgun Pipeline Toolkit Documentation.



#
# The keys section contains the definitions for all the different keys that are being
# used by <PERSON><PERSON>. A key is a magic token that is replaced by a value at runtime, for example
# {Shot}. The section below tells <PERSON><PERSON> which data types and formats to expect for each key.
#
keys:
    Episode:
        type: str
    Sequence:
        type: str
    Shot:
        type: str
    Step:
        type: str
        shotgun_entity_type: Step
        shotgun_field_name: 'code'
    step_code:
        type: str
        shotgun_entity_type: Step
        shotgun_field_name: 'short_name'
    Task:
        type: str
        shotgun_entity_type: Task
        shotgun_field_name: 'content'
    sg_asset_type:
        type: str
        shotgun_entity_type: Asset
        shotgun_field_name: 'sg_asset_type'
    sg_sub_type:
        type: str
        shotgun_entity_type: Asset
        shotgun_field_name: 'sg_sub_type'
    sg_environment_type:
        type: str
        shotgun_entity_type: Asset
        shotgun_field_name: 'sg_environment_type'
    Asset:
        type: str
        shotgun_entity_type: Asset
        shotgun_field_name: 'code'
    AssetMainParent:
        type: str
        shotgun_entity_type: Asset
        shotgun_field_name: 'sg_main_parent'
    usd_edit_name:
        type: str
    name:
        type: str
        filter_by: alphanumeric
    # layer and buffer tokens are used in the global "no engine" image templates
    layer:
        type: str
        filter_by: alphanumeric
    buffer:
        type: str
        filter_by: alphanumeric
    iteration:
        type: int
    version:
        type: int
        format_spec: "03"
    version_four:
       type: int
       format_spec: "04"
       alias: version
    timestamp:
        type: str
    width:
        type: int
    height:
        type: int
    segment_name:
        type: str

    tokenName:
        type: str

    tracker_name:
        type: str
        filter_by: alphanumeric

    color_palette_identifier:
        type: str
        filter_by: alphanumeric


    # Define the user work folders
    current_user_name:
        type: str
        shotgun_entity_type: HumanUser
        shotgun_field_name: login

    # Represents the optional output name for frames written by the Shotgun Write Node
    nuke.output:
        alias: output
        type: str
        filter_by: alphanumeric

    # Represents the optional output name for frames written by the After Effects Publish Rendering Plugin
    afx.comp:
        alias: comp
        type: str
        filter_by: alphanumeric
    afx.seq.ext:
        type: str
        choices:
            png: PNG Image
            exr: EXR Image
        alias: seqExtension
        default: png
    afx.mov.ext:
        alias: extension
        type: str
        choices:
            mov: Quicktime Movie (.mov)
            avi: Audio Video Interleaved (.avi)

    SEQ:
        type: sequence
        format_spec: "04"

    SEQ5:
        type: sequence
        format_spec: "05"

    # Represents a frame sequence exported from Flame
    flame.frame:
        type: sequence
        format_spec: "08"

    eye:
        type: str

    houdini.node:
        alias: node
        type: str
    aov_name:
        type: str

    # these are used by the Hiero exporter and pipeline
    YYYY:
        type: int
        format_spec: "04"
    MM:
        type: int
        format_spec: "02"
    DD:
        type: int
        format_spec: "02"
        alias: DD
    project:
        type: str

    # These are used for the Mari UDIM pipeline:
    UDIM:
        type: sequence
        format_spec: "04"
    # removing default value for more flexibility in substance painter
    #    default: "<UDIM>"
    mari.channel:
        type: str
        alias: channel
    mari.layer:
        type: str
        alias: layer
    mari.project_name:
        type: str
        alias: name
    asset_name:
        type: str
        shotgun_entity_type: Asset
        shotgun_field_name: code
    task_name:
        type: str
        shotgun_entity_type: Task
        shotgun_field_name: content

    # Maya supports two extension types.
    maya_extension:
        type: str
        choices:
            ma: Maya Ascii (.ma)
            mb: Maya Binary (.mb)
        default: ma
        alias: extension

    # 3d Mesh extencion types
    3d_mesh_extension:
        type: str
        choices:
            fbx: FBX (.fbx)
            obj: OBJ (.obj)
        default: fbx
        alias: extension

    # Generic Review Image Sequence Formats
    proxy_image_seq_extension:
        type: str
        choices:
            exr: EXR (Preferred for better quality and optional alpha. HDR format)
            png: PNG (Preferred for better quality and optional alpha)
            tif: TIF (Preferred for better quality and optional premultiplied alpha)
            jpg: JPG (Fallback for lower file size and no alpha)
            sgi: SGI (Custom legacy image sequence format)
        default: png
        alias: extension

    shot_render_seq_extension:
        type: str
        choices:
            exr: EXR (Preferred for better quality and optional alpha. HDR format)
            png: PNG (Preferred for better quality and optional alpha, does not support premultiplied alpha)
            tif: TIF (Preferred for better quality and optional premultiplied alpha. LDR or HDR formats supported)
            tga: TGA (Custom legacy image sequence format)
            iff: IFF (Custom legacy image sequence format)
            jpg: JPG (Fallback for lower file size and no alpha)
        default: exr
        alias: extension

    # Vred supports three extension types
    vred.extension:
        type: str
        choices:
            vpe: VRED Essentials Project Binary (*.vpe)
            vpb: VRED Project Binary (*.vpb)
            vpf: VRED Project File (*.vpf)
        default: vpb

    # Geometry export supports two extension types.
    geo_extension:
        type: str
        choices:
            fbx: FBX (.fbx)
            abc: Alembic (.abc)
        default: abc


    # represents the optional render pass for frames written by VRED
    vred.render_pass:
        type: str
        filter_by: alphanumeric

    # represents the image extension for frames written by VRED
    vred.render_extension:
        type: str
        choices:
            png: PNG Image
            exr: EXR Image
        default: png

    # Represents a frame sequence exported from VRED
    vred.frame:
        type: sequence
        format_spec: "05"
        alias: SEQ

    # Represents the render output from Black Magic Fusion
    fusion.output:
        alias: output
        type: str
        filter_by: alphanumeric

    # Represents a frame padding for custom 2D image shapes
    shape.frame:
        type: sequence
        format_spec: "03"

    shape.frame.number:
        type: str

    frame:
        type: sequence
        format_spec: "04"
    # Represents a piece identifier for custom 2D image shapes
    shape.piece:
        type: str

    # ---- Krita
    krita.image_extension:
        type: str
        choices:
            png: PNG Image
            exr: EXR Image
        alias: extension
        default: exr

    krita.layer_name:
        type: str
        #alias: name

    # ---- Photoshop
    # Photoshop supports two extension types.
    photoshop_extension:
        type: str
        choices:
            psd: Photoshop Document (.psd)
            psb: Photoshop Big (.psb)
        default: psd
        alias: extension

    photoshop.image_extension:
        type: str
        choices:
            png: PNG Image
            exr: EXR Image
        alias: extension
        default: png

    photoshop.layer_name:
        type: str
        #alias: name

    # ----- Maya
    maya.shotnode.name:
        type: str

    maya.part:
        type: str

    secondary_name:
      type: str

    # ----- Render Layers
    render.layer_name:
        type: str

    render.buffer:
        type: str

    # ----- Texture Layers
    texture_extension:
        type: str
        alias: extension

    texture_name:
        type: str
        alias: channel


#
# The paths section contains all the the key locations where files are to be stored
# by Sgtk Apps. Each path is made up of several keys (like {version} or {shot}) and
# these are defined in the keys section above.
#
# Apps use these paths as part of their configuration to define where on disk
# different files should go.
#

paths:

    # Common  path definitions to use as shorthand in order to avoid repetitive and verbose
    # templates. This also makes it easy to change any of the common root paths and have the
    # changes apply to all templates that use them.
    #
    # These don't require the standard formatting (with definition and root_name) because they
    # will be set within each template that uses the alias.

    # Work
    work_shot_root: WorkArea/shots/{Episode}/{Sequence}/shots/{Shot}/{Step}/{Task}
    work_asset_root: WorkArea/assets/{sg_asset_type}/{sg_sub_type}/{Asset}/{Step}/{Task}
    work_asset_root_notask: WorkArea/assets/{sg_asset_type}/{sg_sub_type}/{Asset}/{Step}/MultiTasks

    work_environment_root: WorkArea/assets/{sg_asset_type}/{AssetMainParent}/{sg_environment_type}/{Asset}/{Step}/{Task}
    work_environment_root_notask: WorkArea/assets/{sg_asset_type}/{AssetMainParent}/{sg_environment_type}/{Asset}/{Step}/MultiTasks

    work_sequence_root: WorkArea/shots/{Episode}/{Sequence}/sequence/{Step}/{Task}
    work_episode_root: WorkArea/shots/{Episode}/episode/{Step}/{Task}

    # Publish
    publish_shot_root: PublishArea/shots/{Episode}/{Sequence}/shots/{Shot}/{Step}/{Task}
    publish_asset_root: PublishArea/assets/{sg_asset_type}/{sg_sub_type}/{Asset}/{Step}/{Task}
    publish_asset_root_notask: PublishArea/assets/{sg_asset_type}/{sg_sub_type}/{Asset}/{Step}/MultiTasks

    publish_asset_usd_root: PublishArea/assets/{sg_asset_type}/{sg_sub_type}/{Asset}/usd

    publish_environment_root: PublishArea/assets/{sg_asset_type}/{AssetMainParent}/{sg_environment_type}/{Asset}/{Step}/{Task}
    publish_environment_root_notask: PublishArea/assets/{sg_asset_type}/{AssetMainParent}/{sg_environment_type}/{Asset}/{Step}/MultiTasks

    publish_environment_usd_root: PublishArea/assets/{sg_asset_type}/{AssetMainParent}/{sg_environment_type}/{Asset}/usd

    publish_sequence_root: PublishArea/shots/{Episode}/{Sequence}/sequence/{Step}/{Task}
    publish_episode_root: PublishArea/shots/{Episode}/episode/{Step}/{Task}

    # User
    user_area_root: UserArea/{current_user_name}

    ##########################################################################################
    # Project level paths
    #

    #
    # Hiero
    #

    # The location of WIP files
    hiero_project_work:
        definition: 'WorkArea/editorial/{name}_v{version}.hrox'
    hiero_project_work_area:
        definition: 'WorkArea/editorial'
    # The location of backups of WIP files
    hiero_project_snapshot:
        definition: 'WorkArea/editorial/snapshots/{name}_v{version}_{timestamp}.hrox'
    # The location of published hiero files
    hiero_project_publish:
        definition: 'PublishArea/editorial/{name}_v{version}.hrox'
    hiero_project_publish_area:
        definition: 'PublishArea/editorial'

    ##########################################################################################
    # Deliveries
    #

    # The location for temporal ingest and outgest files
    deliveries_temporal_root:
      definition: 'ProductionArea/Deliveries'

    ##########################################################################################
    # Episode level paths
    #


    # The location of WIP editorial audio files
    episode_audio_work:
      definition: '@work_episode_root/audio/{Episode}_{step_code}_{Task}_{name}_v{version}.wav'
      root_name: primary

    # The location of published audio files
    episode_audio_publish:
      definition: '@publish_episode_root/audio/{Episode}_{step_code}_{Task}_{name}_v{version}.wav'
      root_name: primary

    # The location of work review files
    episode_review_work:
        definition: '@work_episode_root/review/{Episode}_{step_code}_{Task}_{name}_v{version}.mov'
        root_name: 'primary'

    # The location of published review files
    episode_review_publish:
        definition: '@publish_episode_root/review/{Episode}_{step_code}_{Task}_{name}_v{version}.mov'
        root_name: 'primary'

    # The location of work timeline files
    episode_timeline_work:
        definition: '@work_episode_root/timeline/{Episode}_{step_code}_{Task}_{name}_v{version}.edl'
        root_name: 'primary'

    # The location of published timeline files
    episode_timeline_publish:
        definition: '@publish_episode_root/timeline/{Episode}_{step_code}_{Task}_{name}_v{version}.edl'
        root_name: 'primary'

    # The location of published timeline abstraction files
    episode_timeline_abstraction_publish:
        definition: '@publish_episode_root/timeline/{Episode}_{step_code}_{Task}_{name}_v{version}.json'
        root_name: 'primary'

    # The location of published storyboard files
    episode_storyboard_publish:
        definition: '@publish_episode_root/storyboard/{Episode}_{step_code}_{Task}_{name}_v{version}.sbpz'
        root_name: 'primary'

    sequence_storyboard_publish:
        definition: '@publish_sequence_root/storyboard/{Sequence}_{step_code}_{Task}_{name}_v{version}.sbpz'
        root_name: 'primary'

    # The location of published storyboard movie files
    episode_storyboard_video_review_publish:
        definition: '@publish_episode_root/storyboard/review/{Episode}_{step_code}_{Task}_{name}_v{version}/{Episode}_{step_code}_{Task}_{name}_v{version}.mov'

    sequence_storyboard_video_review_publish:
        definition: '@publish_sequence_root/storyboard/review/{Sequence}_{step_code}_{Task}_{name}_v{version}/{Sequence}_{step_code}_{Task}_{name}_v{version}.mov'

    #
    # Photoshop
    #

    # The location of WIP files
    episode_work_area_photoshop:
        definition: '@work_episode_root/photoshop'
    photoshop_episode_work:
        definition: '@episode_work_area_photoshop/{Episode}_{step_code}_{Task}_{name}_v{version}.{photoshop_extension}'
    # The location of backups of WIP files
    photoshop_episode_snapshot:
        definition: '@episode_work_area_photoshop/snapshots/{Episode}_{step_code}_{Task}_{name}_v{version}.{timestamp}.{photoshop_extension}'

    # The location of published files
    episode_publish_area_photoshop:
        definition: '@publish_episode_root/photoshop'
    photoshop_episode_publish:
        definition: '@episode_publish_area_photoshop/{Episode}_{step_code}_{Task}_{name}_v{version}.{photoshop_extension}'
    photoshop_episode_proxy_publish:
        definition: '@episode_publish_area_photoshop/proxy/{Episode}_{step_code}_{Task}_{name}_proxy_v{version}.{photoshop_extension}'
    photoshop_episode_layergroup_publish:
        definition: '@episode_publish_area_photoshop/layers/v{version}/{Episode}_{step_code}_{Task}_{name}_@photoshop_layer_name'
    photoshop_episode_layergroup_proxy_publish:
        definition: '@episode_publish_area_photoshop/layers/v{version}/{Episode}_{step_code}_{Task}_{name}_@photoshop_layer_name_proxy'

    photoshop_episode_layergroup_order:
        definition: '@episode_publish_area_photoshop/{Episode}_{step_code}_{Task}_LayerOrder_{name}_v{version}.json'

    # The location of published images review files
    photoshop_episode_image_review_publish:
        definition: '@episode_publish_area_photoshop/review/{Episode}_{step_code}_{Task}_{name}_v{version}/frames/{Episode}_{step_code}_{Task}_{name}_v{version}.{SEQ5}.png'

    # The location of published video from images
    photoshop_episode_video_review_publish:
        definition: '@episode_publish_area_photoshop/review/{Episode}_{step_code}_{Task}_{name}_v{version}/{Episode}_{step_code}_{Task}_{name}_v{version}.mov'


    #
    # Blender
    #

    # The location of work blender files

    episode_work_area_blender:
        definition: '@work_episode_root/blender'

    blender_episode_work:
        definition: '@episode_work_area_blender/{Episode}_{step_code}_{Task}_{name}_v{version}.blend'
        root_name: 'primary'

    # The location of backups of WIP files
    blender_episode_snapshot:
        definition: '@episode_work_area_blender/snapshots/{Episode}_{step_code}_{Task}_{name}_v{version}.{timestamp}.blend'

    # The location of published files
    episode_publish_area_blender:
        definition: '@publish_episode_root/blender'

    # The location of published blender files
    episode_blender_publish:
        definition: '@episode_publish_area_blender/{Episode}_{step_code}_{Task}_{name}_v{version}.blend'
        root_name: 'primary'

    #
    # Illustrator
    #

    # The location of WIP files
    illustrator_episode_work:
        definition: '@work_episode_root/illustrator/{Episode}_{step_code}_{Task}_{name}_v{version}.ai'
    episode_work_area_illustrator:
        definition: '@work_episode_root/illustrator'
    # The location of backups of WIP files
    illustrator_episode_snapshot:
        definition: '@work_episode_root/illustrator/snapshots/{Episode}_{step_code}_{Task}_{name}_v{version}.{timestamp}.ai'
    # The location of published files
    episode_publish_area_illustrator:
        definition: '@publish_episode_root/illustrator'
    illustrator_episode_publish:
        definition: '@publish_episode_root/illustrator/{Episode}_{step_code}_{Task}_{name}_v{version}.ai'
    # illustrator_episode_proxy_publish:
    #     definition: '@publish_episode_root/illustrator/proxy/{Episode}_{step_code}_{Task}_{name}_proxy_v{version}.ai'

    # # The location of published images review files
    # illustrator_episode_image_review_publish:
    #     definition: '@publish_episode_root/illustrator/review/{Episode}_{step_code}_{Task}_{name}_v{version}/frames/{Episode}_{step_code}_{Task}_{name}_v{version}.{SEQ5}.png'

    # # The location of published video from images
    # illustrator_episode_video_review_publish:
    #     definition: '@publish_episode_root/illustrator/review/{Episode}_{step_code}_{Task}_{name}_v{version}/{Episode}_{step_code}_{Task}_{name}_v{version}.mov'

    #
    # Krita
    #

    # The location of WIP files
    krita_episode_work:
        definition: '@work_episode_root/krita/{Episode}_{step_code}_{Task}_{name}_v{version}.kra'
    episode_work_area_krita:
        definition: '@work_episode_root/krita'
    # The location of backups of WIP files
    krita_episode_snapshot:
        definition: '@work_episode_root/krita/snapshots/{Episode}_{step_code}_{Task}_{name}_v{version}.{timestamp}.kra'
    # The location of published files
    episode_publish_area_krita:
        definition: '@publish_episode_root/krita'
    krita_episode_publish:
        definition: '@publish_episode_root/krita/{Episode}_{step_code}_{Task}_{name}_v{version}.kra'

    # These templates are related to publishing layers within Krita
    # where to export the individual layers before publishing them
    krita_episode_layer_export:
        definition: '@work_episode_root/krita/layers/{Episode}_{step_code}_{Task}_krita_layers_v{version}/@krita_layer_name'
        root_name: 'primary'

    # where to publish the individual layers
    krita_episode_layer_publish:
        definition: '@publish_episode_root/krita/layers/{Episode}_{step_code}_{Task}_krita_layers_v{version}/@krita_layer_name'
        root_name: 'primary'

    # where to export layers as a folder before publishing
    krita_episode_layers_export:
        definition: '@work_episode_root/krita/layers/{Episode}_{step_code}_{Task}_krita_layers_v{version}'
        root_name: 'primary'

    # where to publish layers as a folder
    krita_episode_layers_publish:
        definition: '@publish_episode_root/krita/layers/{Episode}_{step_code}_{Task}_krita_layers_v{version}'
        root_name: 'primary'

    #
    # Adobe Premiere
    #

    # The location of WIP files
    episode_work_area_premiere:
        definition: '@work_episode_root/premiere'

    premiere_episode_work:
        definition: '@episode_work_area_premiere/{Episode}_{step_code}_{Task}_{name}_v{version}.prproj'

    # The location of backups of WIP files
    premiere_episode_snapshot:
        definition: '@episode_work_area_premiere/snapshots/{Episode}_{step_code}_{Task}_{name}_v{version}.{timestamp}.prproj'

    # The location of published files
    episode_publish_area_premiere:
        definition: '@publish_episode_root/premiere'
    premiere_episode_publish:
        definition: '@episode_publish_area_premiere/{Episode}_{step_code}_{Task}_{name}_v{version}.prproj'
    premiere_episode_render_pub_mono:
        definition: '@publish_episode_root/renders/{name}/v{version}/{Episode}_{step_code}_{Task}_{name}_v{version}.{SEQ}.exr'

    premiere_episode_render_movie:
        definition: '@work_episode_root/review/{Episode}_{step_code}_{Task}_{name}_v{version}.mov'


    ##########################################################################################
    # Sequence level paths
    #


    # The location of WIP editorial audio files
    sequence_audio_work:
      definition: '@work_sequence_root/audio/{Sequence}_{step_code}_{Task}_{name}_v{version}.wav'
      root_name: primary

    sequence_audio_publish:
      definition: '@publish_sequence_root/audio/{Sequence}_{step_code}_{Task}_{name}_v{version}.wav'
      root_name: primary

    # The location of work review files
    sequence_review_work:
        definition: '@work_sequence_root/review/{Sequence}_{step_code}_{Task}_{name}_v{version}.mov'
        root_name: 'primary'

    # The location of published review files
    sequence_review_publish:
        definition: '@publish_sequence_root/review/{Sequence}_{step_code}_{Task}_{name}_v{version}.mov'
        root_name: 'primary'

    # The location of published timeline files
    sequence_timeline_publish:
        definition: '@publish_sequence_root/timeline/{Sequence}_{step_code}_{Task}_{name}_v{version}.edl'
        root_name: 'primary'

    # The location of published timeline abstraction files
    sequence_timeline_abstraction_publish:
        definition: '@publish_sequence_root/timeline/{Sequence}_{step_code}_{Task}_{name}_v{version}.json'
        root_name: 'primary'



    #
    # Adobe Premiere
    #

    # The location of WIP files
    sequence_work_area_premiere:
        definition: '@work_sequence_root/premiere'

    premiere_sequence_work:
        definition: '@sequence_work_area_premiere/{Sequence}_{step_code}_{Task}_{name}_v{version}.prproj'

    # The location of backups of WIP files
    premiere_sequence_snapshot:
        definition: '@sequence_work_area_premiere/snapshots/{Sequence}_{step_code}_{Task}_{name}_v{version}.{timestamp}.prproj'

    # The location of published files
    sequence_publish_area_premiere:
        definition: '@publish_sequence_root/premiere'
    premiere_sequence_publish:
        definition: '@sequence_publish_area_premiere/{Sequence}_{step_code}_{Task}_{name}_v{version}.prproj'
    premiere_sequence_render_pub_mono:
        definition: '@publish_sequence_root/renders/{name}/v{version}/{Sequence}_{step_code}_{Task}_{name}_v{version}.{SEQ}.exr'

    premiere_sequence_render_movie:
        definition: '@work_sequence_root/review/{Sequence}_{step_code}_{Task}_{name}_v{version}.mov'


    #
    # Harmony
    #

    # define the location of a work area
    sequence_work_area_harmony:
        definition: '@work_sequence_root/harmony'
        root_name: 'primary'

    # define the location of a publish area
    sequence_publish_area_harmony:
        definition: '@publish_sequence_root/harmony'
        root_name: 'primary'

    # The location of WIP files
    harmony_sequence_work:
        definition: '@sequence_work_area_harmony/scenes/{Sequence}_{step_code}_{Task}_{name}_v{version}.xstage'
        root_name: 'primary'

    # The location of backups of WIP files
    # Note that snapshots folder is  not inside the wip folder
    # otherwise we could confuse the publishing process when files
    # need to be copied from work to publish.
    harmony_sequence_snapshot:
        definition: '@sequence_work_area_harmony/snapshots/{Sequence}_{step_code}_{Task}_{name}_v{version}.{timestamp}.xstage'
        root_name: 'primary'

    # The location of published harmony files
    harmony_sequence_publish:
        definition: '@sequence_publish_area_harmony/scenes/{Sequence}_{step_code}_{Task}_{name}_v{version}/{Sequence}_{step_code}_{Task}_{name}_v{version}.xstage'
        root_name: 'primary'

    #
    # Blender
    #

    # define the location of a work area
    sequence_work_area_blender:
        definition: '@work_sequence_root/blender'
        root_name: 'primary'

    # define the location of a publish area
    sequence_publish_area_blender:
        definition: '@publish_sequence_root/blender'
        root_name: 'primary'

    # The location of WIP files
    blender_sequence_work:
        definition: '@sequence_work_area_blender/scenes/{Sequence}_{step_code}_{Task}_{name}_v{version}.blend'
        root_name: 'primary'

    # The location of backups of WIP files
    blender_sequence_snapshot:
        definition: '@sequence_work_area_blender/snapshots/{Sequence}_{step_code}_{Task}_{name}_v{version}.{timestamp}.blend'
        root_name: 'primary'

    # The location of published blender files
    blender_sequence_publish:
        definition: '@sequence_publish_area_blender/scenes/{Sequence}_{step_code}_{Task}_{name}_v{version}/{Sequence}_{step_code}_{Task}_{name}_v{version}.blend'
        root_name: 'primary'

    #
    # Maya
    #

    # define the location of a work area
    sequence_work_area_maya:
        definition: '@work_sequence_root/maya'
        root_name: 'primary'

    # define the location of a publish area
    sequence_publish_area_maya:
        definition: '@publish_sequence_root/maya'
        root_name: 'primary'

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----
    # The location of WIP files
    maya_sequence_work:
        definition: '@sequence_work_area_maya/{Sequence}_{step_code}_{Task}_{name}_v{version}.{maya_extension}'
        root_name: 'primary'

    maya_sequence_playblast_work:
        definition: '@sequence_work_area_maya/review/{name}/v{version}/sequence/{Sequence}_{step_code}_{Task}_{name}_v{version}.{SEQ}.jpg'
        root_name: 'primary'

    maya_sequence_playblast_shot_work:
        definition: '@sequence_work_area_maya/review/{name}/v{version}/shots/{maya.shotnode.name}/{maya.shotnode.name}_{step_code}_{Task}_{name}_v{version}.{SEQ}.jpg'
        root_name: 'primary'

    maya_sequence_playblast_work_mov:
        definition: '@sequence_work_area_maya/review/{name}/v{version}/{Sequence}_{step_code}_{Task}_{name}_v{version}.mov'
        root_name: 'primary'

    maya_sequence_playblast_shot_work_mov:
        definition: '@sequence_work_area_maya/review/{name}/v{version}/shots/{maya.shotnode.name}_{step_code}_{Task}_{name}_v{version}.mov'
        root_name: 'primary'

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----
    # The location of backups of WIP filesF
    # Note that snapshots folder is  not inside the wip folder
    # otherwise we could confuse the publishing process when files
    # need to be copied from work to publish.
    maya_sequence_snapshot:
        definition: '@sequence_work_area_maya/snapshots/{Sequence}_{step_code}_{Task}_{name}_v{version}.{timestamp}.{maya_extension}'
        root_name: 'primary'

    # The location of published maya files
    maya_sequence_publish:
        definition: '@sequence_publish_area_maya/{Sequence}_{step_code}_{Task}_{name}_v{version}.{maya_extension}'
        root_name: 'primary'

    # The location of the published exported cameras geometry
    maya_scene_cameras_geo:
        definition: '@sequence_publish_area_maya/cameras/{Sequence}_{step_code}_{Task}_{name}_v{version}.{geo_extension}'
        root_name: 'primary'

    maya_sequence_playblast_publish:
        definition: '@sequence_publish_area_maya/Preview/{Sequence}_{step_code}_{Task}_{name}[_{secondary_name}]_v{version}.mov'
        root_name: 'primary'

    # Clips data for a shot node in the sequencer:
    maya_sequence_clip_work:
      definition: '@sequence_work_area_maya/review/{name}/v{version}/clips/{maya.shotnode.name}_{step_code}_{name}_v{version}.mov'
      root_name: 'primary'

    maya_sequence_clip_publish:
      definition: '@sequence_publish_area_maya/review/{name}/v{version}/clips/{maya.shotnode.name}_{step_code}_{name}[_{secondary_name}]_v{version}.mov'
      root_name: 'primary'

    3d_mesh_sequence_publish:
        definition: '@sequence_publish_area_maya/meshes/{Sequence}_{step_code}_{Task}_{name}/v{version}/{Sequence}_{step_code}_{Task}_{name}_{Asset}_v{version}.{3d_mesh_extension}'

    3d_mesh_sequence_publish_notask:
        definition: '@sequence_publish_area_maya/meshes/{Sequence}_{step_code}_{name}/v{version}/{Sequence}_{step_code}_{name}_{Asset}_v{version}.{3d_mesh_extension}'

    #
    # Krita
    #

    # define the location of a work area
    sequence_work_area_krita:
        definition: '@work_sequence_root/krita'
        root_name: 'primary'

    # define the location of a publish area
    sequence_publish_area_krita:
        definition: '@publish_sequence_root/krita'
        root_name: 'primary'

    # The location of WIP files
    krita_sequence_work:
        definition: '@work_sequence_root/krita/{name}_v{version}/{Sequence}_{step_code}_{Task}_{name}_v{version}.kra'
        root_name: 'primary'

    # The location of backups of WIP files
    krita_sequence_snapshot:
        definition: '@work_sequence_root/krita/snapshots/{name}_v{version}/{Sequence}_{step_code}_{Task}_{name}_v{version}.{timestamp}.kra'
        root_name: 'primary'

    # The location of published krita session
    krita_sequence_publish:
        definition: '@publish_sequence_root/krita/{name}_v{version}/{Sequence}_{step_code}_{Task}_{name}_v{version}.kra'
        root_name: 'primary'

    # These templates are related to publishing layers within Krita
    # where to export the individual layers before publishing them
    krita_sequence_layer_export:
        definition: '@work_sequence_root/krita/layers/{Sequence}_{step_code}_{Task}_krita_layers_v{version}/@krita_layer_name'
        root_name: 'primary'

    # where to publish the individual layers
    krita_sequence_layer_publish:
        definition: '@publish_sequence_root/krita/layers/{Sequence}_{step_code}_{Task}_krita_layers_v{version}/@krita_layer_name'
        root_name: 'primary'

    # where to export layers as a folder before publishing
    krita_sequence_layers_export:
        definition: '@work_sequence_root/krita/layers/{Sequence}_{step_code}_{Task}_krita_layers_v{version}'
        root_name: 'primary'

    # where to publish layers as a folder
    krita_sequence_layers_publish:
        definition: '@publish_sequence_root/krita/layers/{Sequence}_{step_code}_{Task}_krita_layers_v{version}'
        root_name: 'primary'

    #
    # Photoshop
    #

    # The location of a work area
    sequence_work_area_photoshop:
        definition: '@work_sequence_root/photoshop'
    photoshop_sequence_work:
        definition: '@sequence_work_area_photoshop/{Sequence}_{step_code}_{Task}_{name}_v{version}.{photoshop_extension}'
    # The location of backups of WIP files
    photoshop_sequence_snapshot:
        definition: '@sequence_work_area_photoshop/snapshots/{Sequence}_{step_code}_{Task}_{name}_v{version}.{timestamp}.{photoshop_extension}'

    # The location of published files
    sequence_publish_area_photoshop:
        definition: '@publish_sequence_root/photoshop'
    photoshop_sequence_publish:
        definition: '@sequence_publish_area_photoshop/{Sequence}_{step_code}_{Task}_{name}_v{version}.{photoshop_extension}'
    photoshop_sequence_proxy_publish:
        definition: '@sequence_publish_area_photoshop/proxy/{Sequence}_{step_code}_{Task}_{name}_proxy_v{version}.{photoshop_extension}'
    photoshop_sequence_layergroup_publish:
        definition: '@sequence_publish_area_photoshop/layers/v{version}/{Sequence}_{step_code}_{Task}_{name}_@photoshop_layer_name'
    photoshop_sequence_layergroup_proxy_publish:
        definition: '@sequence_publish_area_photoshop/layers/v{version}/{Sequence}_{step_code}_{Task}_{name}_@photoshop_layer_name_proxy'

    photoshop_sequence_layergroup_order:
        definition: '@publish_shot_root/photoshop/{Sequence}_{step_code}_{Task}_LayerOrder_{name}_v{version}.json'


    # The location of published images review files
    photoshop_sequence_image_review_publish:
        definition: '@sequence_publish_area_photoshop/review/{Sequence}_{step_code}_{Task}_{name}_v{version}/frames/{Sequence}_{step_code}_{Task}_{name}_v{version}.{SEQ5}.png'

    # The location of published video from images
    photoshop_sequence_video_review_publish:
        definition: '@sequence_publish_area_photoshop/review/{Sequence}_{step_code}_{Task}_{name}_v{version}/{Sequence}_{step_code}_{Task}_{name}_v{version}.mov'



    ##########################################################################################
    # Shot level paths
    #

    shot_review_work:
        definition: '@work_shot_root/review/{Shot}_{step_code}_{Task}_{name}[_{layer}][_{buffer}]_v{version}.mov'

    shot_review_publish:
        definition: '@publish_shot_root/review/{Shot}_{step_code}_{Task}_{name}[_{layer}][_{buffer}]_v{version}.mov'

    shot_audio_work:
        definition: '@work_shot_root/audio/{Shot}_{step_code}_{Task}_{name}_v{version}.wav'

    shot_audio_publish:
        definition: '@publish_shot_root/audio/{Shot}_{step_code}_{Task}_{name}_v{version}.wav'

    shot_camera_work:
        definition: '@work_shot_root/camera/{Shot}_{step_code}_{Task}_camera_{name}_v{version}.abc'

    shot_camera_publish:
        definition: '@publish_shot_root/camera/{Shot}_{step_code}_{Task}_camera_{name}_v{version}.abc'

    #
    # No Engine
    #

    # Generic Review Proxy Image Sequences
    # These have support for different formats, being png the default
    # Other image formats can be used by defining the "extension" field
    # "extension" is an alias for "proxy_image_seq_extension"
    # Also have support for single or multi layer publish, in case more
    # than one image sequence per task is requiered, this is set using
    # the "layer" key as optional
    shot_review_work_proxy:
        definition: '@work_shot_root/review/{name}[_{layer}][_{buffer}]_v{version}/{Shot}_{step_code}_{Task}_{name}[_{layer}][_{buffer}]_v{version}.{SEQ}.{proxy_image_seq_extension}'

    shot_review_publish_proxy:
        definition: '@publish_shot_root/review/{name}[_{layer}][_{buffer}]_v{version}/{Shot}_{step_code}_{Task}_{name}[_{layer}][_{buffer}]_v{version}.{SEQ}.{proxy_image_seq_extension}'


    # Render from lighting
    # shot_render_publish_exr:
    #   definition: '@publish_shot_root/render/{name}_v{version}/{render.layer_name}/{render.buffer}/{Shot}_{step_code}_{Task}_{name}_{render.layer_name}[_{render.buffer}]_v{version}.{SEQ}.exr'

    # shot_review_publish_exr:
    #   definition: '@publish_shot_root/render/{Shot}_{step_code}_{Task}_{name}[_{layer}]_v{version}/{Shot}_{step_code}_{Task}_{name}[_{layer}]_v{version}.{SEQ}.exr'

    # shot_flat_render_publish_exr:
    #   definition: '@publish_shot_root/render/{name}[_{layer}]_v{version}/{Shot}_{step_code}_{Task}_{name}[_{layer}]_v{version}.{SEQ}.exr'

    # shot_flat_render_publish_mov:
    #   definition: '@publish_shot_root/render/{Shot}_{step_code}_{Task}_{name}[_{layer}]_v{version}.mov'

    # shot_flat_render_work_exr:
    #   definition: '@work_shot_root/render/{name}_v{version}/{Shot}_{step_code}_{Task}_{name}_v{version}.{SEQ}.exr'

    shot_render_publish:
      definition: '@publish_shot_root/render/{name}_v{version}/{render.layer_name}/{render.buffer}/{Shot}_{step_code}_{Task}_{name}_{render.layer_name}[_{render.buffer}]_v{version}.{SEQ}.{shot_render_seq_extension}'

    # shot_review_publish:
    #   definition: '@publish_shot_root/render/{Shot}_{step_code}_{Task}_{name}[_{layer}]_v{version}/{Shot}_{step_code}_{Task}_{name}[_{layer}]_v{version}.{SEQ}.mov'

    shot_flat_render_publish:
      definition: '@publish_shot_root/render/{name}[_{layer}]_v{version}/{Shot}_{step_code}_{Task}_{name}[_{layer}]_v{version}.{SEQ}.{shot_render_seq_extension}'

    shot_flat_render_publish_mov:
      definition: '@publish_shot_root/render/{Shot}_{step_code}_{Task}_{name}[_{layer}]_v{version}.mov'

    shot_flat_render_work:
      definition: '@work_shot_root/render/{name}_v{version}/{Shot}_{step_code}_{Task}_{name}_v{version}.{SEQ}.{shot_render_seq_extension}'

    #
    # Flame
    #

    flame_segment_clip:
        definition: '@work_shot_root/finishing/clip/sources/{segment_name}.clip'
    flame_shot_clip:
        definition: '@work_shot_root/finishing/clip/{Shot}.clip'
    flame_shot_batch:
        definition: '@work_shot_root/finishing/batch/{Shot}_{step_code}_{Task}_v{version}.batch'
    flame_shot_render_dpx:
        definition: '@work_shot_root/finishing/renders/{segment_name}_v{version}/{Shot}_{segment_name}_{step_code}_{Task}_v{version}.{flame.frame}.dpx'
    flame_shot_render_exr:
        definition: '@work_shot_root/finishing/renders/{segment_name}_v{version}/{Shot}_{segment_name}_{step_code}_{Task}_v{version}.{flame.frame}.exr'
    flame_shot_comp_dpx:
        definition: '@work_shot_root/finishing/comp/{segment_name}_v{version}/{Shot}_{segment_name}_{step_code}_{Task}_v{version}.{flame.frame}.dpx'
    flame_shot_comp_exr:
        definition: '@work_shot_root/finishing/comp/{segment_name}_v{version}/{Shot}_{segment_name}_{step_code}_{Task}_v{version}.{flame.frame}.exr'

    #
    # Photoshop
    #

    # The location of WIP files
    photoshop_shot_work:
        definition: '@work_shot_root/photoshop/{Shot}_{step_code}_{Task}_{name}_v{version}.{photoshop_extension}'
    shot_work_area_photoshop:
        definition: '@work_shot_root/photoshop'
    # The location of backups of WIP files
    photoshop_shot_snapshot:
        definition: '@work_shot_root/photoshop/snapshots/{Shot}_{step_code}_{Task}_{name}_v{version}.{timestamp}.{photoshop_extension}'
    # The location of published files
    shot_publish_area_photoshop:
        definition: '@publish_shot_root/photoshop'
    photoshop_shot_publish:
        definition: '@publish_shot_root/photoshop/{Shot}_{step_code}_{Task}_{name}_v{version}.{photoshop_extension}'
    photoshop_shot_proxy_publish:
        definition: '@publish_shot_root/photoshop/proxy/{Shot}_{step_code}_{Task}_{name}_proxy_v{version}.{photoshop_extension}'
    photoshop_shot_layergroup_publish:
        definition: '@publish_shot_root/photoshop/layers/v{version}/{Shot}_{step_code}_{Task}_{name}_@photoshop_layer_name'
    photoshop_shot_layergroup_proxy_publish:
        definition: '@publish_shot_root/photoshop/layers/v{version}/{Shot}_{step_code}_{Task}_{name}_@photoshop_layer_name_proxy'

    photoshop_shot_layergroup_order:
        definition: '@publish_shot_root/photoshop/{Shot}_{step_code}_{Task}_LayerOrder_{name}_v{version}.json'

    # The location of published images review files
    photoshop_shot_image_review_publish:
        definition: '@publish_shot_root/photoshop/review/{Shot}_{step_code}_{Task}_{name}_v{version}/frames/{Shot}_{step_code}_{Task}_{name}_v{version}.{SEQ5}.png'

    # The location of published video from images
    photoshop_shot_video_review_publish:
        definition: '@publish_shot_root/photoshop/review/{Shot}_{step_code}_{Task}_{name}_v{version}/{Shot}_{step_code}_{Task}_{name}_v{version}.mov'

    #
    # Illustrator
    #

    # The location of WIP files
    illustrator_shot_work:
        definition: '@work_shot_root/illustrator/{Shot}_{step_code}_{Task}_{name}_v{version}.ai'
    shot_work_area_illustrator:
        definition: '@work_shot_root/illustrator'
    # The location of backups of WIP files
    illustrator_shot_snapshot:
        definition: '@work_shot_root/illustrator/snapshots/{Shot}_{step_code}_{Task}_{name}_v{version}.{timestamp}.ai'
    # The location of published files
    shot_publish_area_illustrator:
        definition: '@publish_shot_root/illustrator'
    illustrator_shot_publish:
        definition: '@publish_shot_root/illustrator/{Shot}_{step_code}_{Task}_{name}_v{version}.ai'
    # illustrator_shot_proxy_publish:
    #     definition: '@publish_shot_root/illustrator/proxy/{Shot}_{step_code}_{Task}_{name}_proxy_v{version}.ai'
    # illustrator_shot_layergroup_publish:
    #     definition: '@publish_shot_root/illustrator/layers/v{version}/{Shot}_{step_code}_{Task}_{name}_@illustrator_layer_name'
    # illustrator_shot_layergroup_proxy_publish:
    #     definition: '@publish_shot_root/illustrator/layers/v{version}/{Shot}_{step_code}_{Task}_{name}_@illustrator_layer_name_proxy'

    # # The location of published images review files
    # illustrator_shot_image_review_publish:
    #     definition: '@publish_shot_root/illustrator/review/{Shot}_{step_code}_{Task}_{name}_v{version}/frames/{Shot}_{step_code}_{Task}_{name}_v{version}.{SEQ5}.png'

    # # The location of published video from images
    # illustrator_shot_video_review_publish:
    #     definition: '@publish_shot_root/illustrator/review/{Shot}_{step_code}_{Task}_{name}_v{version}/{Shot}_{step_code}_{Task}_{name}_v{version}.mov'

    #
    # After Effects
    #

    # The location of WIP files
    aftereffects_shot_work:
        definition: '@work_shot_root/afx/{Shot}_{step_code}_{Task}_{name}_v{version}.aep'
    shot_work_area_aftereffects:
        definition: '@work_shot_root/afx'
    # The location of backups of WIP files
    aftereffects_shot_snapshot:
        definition: '@work_shot_root/afx/snapshots/{Shot}_{step_code}_{Task}_{name}_v{version}.{timestamp}.aep'
    # The location of published files
    shot_publish_area_aftereffects:
        definition: '@publish_shot_root/afx'
    aftereffects_shot_publish:
        definition: '@publish_shot_root/afx/{Shot}_{step_code}_{Task}_{name}_v{version}.aep'
    aftereffects_shot_render_pub_mono:
        # TODO: use dynamic extension using choices defined for afx.seq.ext
        # definition: '@publish_shot_root/afx/render/{name}_v{version}/{afx.comp}/{Shot}_{step_code}_{Task}_{name}_{afx.comp}_v{version}.{SEQ}.{afx.seq.ext}'
        definition: '@publish_shot_root/afx/render/{name}_v{version}/{afx.comp}/{Shot}_{step_code}_{Task}_{name}_{afx.comp}_v{version}.{SEQ}.png'
    # The following template uses {afx.mov.ext} this is a special key, that will be only there
    # in the beta to support different extensions on mac and windows, while using the same
    # output module (Lossless with Alpha)
    aftereffects_shot_render_movie:
        # TODO: use dynamic extension using choices defined for afx.mov.ext
        # definition: '@publish_shot_root/afx/review/{Shot}_{step_code}_{Task}_{name}_{afx.comp}_v{version}.{afx.mov.ext}'
        definition: '@publish_shot_root/afx/review/{Shot}_{step_code}_{Task}_{name}_{afx.comp}_v{version}.mov'
    aftereffects_shot_template_user:
        definition: '@user_area_root/afx/templates/template.aep'


    #
    # Maya
    #

    # define the location of a work area
    shot_work_area_maya:
        definition: '@work_shot_root/maya'
    # define the location of a publish area
    shot_publish_area_maya:
        definition: '@publish_shot_root/maya'
    # The location of WIP files
    maya_shot_work:
        definition: '@shot_work_area_maya/{Shot}_{step_code}_{Task}_{name}_v{version}.{maya_extension}'
    # The location of backups of WIP files
    maya_shot_snapshot:
        definition: '@shot_work_area_maya/snapshots/{Shot}_{step_code}_{Task}_{name}_v{version}.{timestamp}.{maya_extension}'
    maya_shot_playblast:
        definition: '@shot_work_area_maya/playblasts/{Shot}_{step_code}_{Task}_{name}_v{version}.mov'
    # The location of published maya files
    maya_shot_publish:
        definition: '@shot_publish_area_maya/{Shot}_{step_code}_{Task}_{name}_v{version}.{maya_extension}'

    maya_shot_animation_alembic_part_publish:
        definition: '@shot_publish_area_maya/cache/alembic/{Shot}_{step_code}_{Task}_{name}/v{version}/{Asset}/{Shot}_{step_code}_{Task}_{name}_{Asset}_{maya.part}_v{version}.abc'
    maya_shot_animation_alembic_publish:
        definition: '@shot_publish_area_maya/cache/alembic/{Shot}_{step_code}_{Task}_{name}/v{version}/{Asset}/{Shot}_{step_code}_{Task}_{name}_{Asset}_v{version}.abc'

    maya_shot_animation_usd_publish:
        definition: '@shot_publish_area_maya/usd/{Shot}_{step_code}_{Task}_{name}/v{version}/{Asset}/{Shot}_{step_code}_{Task}_{name}_{Asset}_v{version}.usd'

    # template for environment overrides publish
    shot_environment_overrides_publish:
        definition: '@publish_shot_root/overrides/environment/{Shot}_{step_code}_{Task}_{name}/v{version}/{Shot}_{step_code}_{Task}_{name}_overrides_{Asset}_v{version}.json'

    shot_gpu_environment_overrides_publish:
        definition: '@publish_shot_root/overrides/environment/{Shot}_{step_code}_{Task}_{name}/v{version}/{Shot}_{step_code}_{Task}_{name}_gpu_overrides_{Asset}_v{version}.json'

    # template for rigs on the fly abstraction publish
    maya_shot_rig_on_the_fly_json_publish:
        definition: '@publish_shot_root/abstractions/rigsOnTheFly/v{version}/{Shot}_{step_code}_{Task}_{name}_rigsOnTheFly_v{version}.json'

    # template for global locators publish
    maya_shot_global_locators_alembic_publish:
        definition: '@shot_publish_area_maya/cache/alembic/globalLocators/v{version}/{Shot}_{step_code}_{Task}_{name}_globalLocators_v{version}.abc'

    # templafe for mesh form maya

    3d_mesh_shot_publish:
        definition: '@shot_publish_area_maya/meshes/{Shot}_{step_code}_{Task}_{name}/v{version}/{Shot}_{step_code}_{Task}_{name}_{Asset}_v{version}.{3d_mesh_extension}'

    3d_mesh_shot_publish_notask:
        definition: '@shot_publish_area_maya/meshes/{Shot}_{step_code}_{name}/v{version}/{Shot}_{step_code}_{name}_{Asset}_v{version}.{3d_mesh_extension}'


    #
    # Houdini
    #

    # define the location of a work area
    shot_work_area_houdini:
        definition: '@work_shot_root/houdini'
    # define the location of a publish area
    shot_publish_area_houdini:
        definition: '@publish_shot_root/houdini'
    # The location of WIP files
    houdini_shot_work:
        definition: '@work_shot_root/houdini/{Shot}_{step_code}_{Task}_{name}_v{version}.hip'
    # The location of backups of WIP files
    houdini_shot_snapshot:
        definition: '@work_shot_root/houdini/snapshots/{Shot}_{step_code}_{Task}_{name}_v{version}.{timestamp}.hip'
    # The location of published houdini files
    houdini_shot_publish:
        definition: '@publish_shot_root/houdini/{Shot}_{step_code}_{Task}_{name}_v{version}.hip'
    # Alembic caches
    houdini_shot_work_alembic_cache:
        definition: '@work_shot_root/houdini/cache/alembic/{Shot}_{step_code}_{Task}_{name}/{houdini.node}/v{version}/{Shot}_{step_code}_{Task}_{name}_v{version}.abc'

    # Rendered images
    houdini_shot_render:
        definition: '@work_shot_root/images/{Shot}_{step_code}_{Task}_{name}/{houdini.node}/v{version}/{width}x{height}/{Shot}_{step_code}_{Task}_{name}_v{version}.{SEQ}.exr'

    # Additional mantra outputs
    houdini_shot_ifd:
        definition: '@work_shot_root/ifds/{Shot}_{step_code}_{Task}_{name}/{houdini.node}/v{version}/{width}x{height}/{Shot}_{step_code}_{Task}_{name}_v{version}.{SEQ}.ifd'

    houdini_shot_dcm:
        definition: '@work_shot_root/dcms/{Shot}_{step_code}_{Task}_{name}/{houdini.node}/v{version}/{width}x{height}/{Shot}_{step_code}_{Task}_{name}_v{version}.{SEQ}.dcm'

    houdini_shot_extra_plane:
        definition: '@work_shot_root/images/{Shot}_{step_code}_{Task}_{name}/{houdini.node}/{aov_name}/v{version}/{width}x{height}/{Shot}_{step_code}_{Task}_{name}_v{version}.{SEQ}.exr'


    #
    # 3dsmax
    #

    # define the location of a work area
    shot_work_area_max:
        definition: '@work_shot_root/3dsmax'
    # define the location of a publish area
    shot_publish_area_max:
        definition: '@publish_shot_root/3dsmax'
    # The location of WIP files
    max_shot_work:
        definition: '@work_shot_root/3dsmax/{Shot}_{step_code}_{Task}_{name}_v{version}.max'
    # The location of backups of WIP files
    max_shot_snapshot:
        definition: '@work_shot_root/3dsmax/snapshots/{Shot}_{step_code}_{Task}_{name}_v{version}.{timestamp}.max'
    # The location of published max files
    max_shot_publish:
        definition: '@publish_shot_root/3dsmax/{Shot}_{step_code}_{Task}_{name}_v{version}.max'


    #
    # Motionbuilder
    #

    # define the location of a work area
    shot_work_area_mobu:
        definition: '@work_shot_root/mobu'
    # define the location of a publish area
    shot_publish_area_mobu:
        definition: '@publish_shot_root/mobu'
    # The location of WIP files
    mobu_shot_work:
        definition: '@work_shot_root/mobu/{Shot}_{step_code}_{Task}_{name}_v{version}.fbx'
    # The location of backups of WIP files
    mobu_shot_snapshot:
        definition: '@work_shot_root/mobu/snapshots/{Shot}_{step_code}_{Task}_{name}_v{version}.{timestamp}.fbx'
    # The location of published mobu files
    mobu_shot_publish:
        definition: '@publish_shot_root/mobu/{Shot}_{step_code}_{Task}_{name}_v{version}.fbx'


    #
    # Nuke
    #

    # define the location of a work area
    shot_work_area_nuke:
        definition: '@work_shot_root/nuke'
    # define the location of a publish area
    shot_publish_area_nuke:
        definition: '@publish_shot_root/nuke'
    # The location of WIP script files
    nuke_shot_work:
        definition: '@work_shot_root/nuke/{Shot}_{step_code}_{Task}_{name}_v{version}.nk'
    # The location of backups of WIP files
    nuke_shot_snapshot:
        definition: '@work_shot_root/nuke/snapshots/{Shot}_{step_code}_{Task}_{name}_v{version}.{timestamp}.nk'
    # The location of published nuke script files
    nuke_shot_publish:
        definition: '@publish_shot_root/nuke/{Shot}_{step_code}_{Task}_{name}_v{version}.nk'
    # write node outputs
    nuke_shot_render_mono_dpx:
        definition: '@work_shot_root/images/{Shot}_{step_code}_{Task}_{name}/v{version}/{width}x{height}/{Shot}_{step_code}_{Task}_{name}_{nuke.output}_v{version}.{SEQ}.dpx'
    nuke_shot_render_pub_mono_dpx:
        definition: '@publish_shot_root/elements/{Shot}_{step_code}_{Task}_{name}/v{version}/{width}x{height}/{Shot}_{step_code}_{Task}_{name}_{nuke.output}_v{version}.{SEQ}.dpx'
    nuke_shot_render_stereo:
        definition: '@work_shot_root/images/{Shot}_{step_code}_{Task}_{name}/v{version}/{width}x{height}/{Shot}_{step_code}_{Task}_{name}_{nuke.output}_{eye}_v{version}.{SEQ}.exr'
    nuke_shot_render_pub_stereo:
        definition: '@publish_shot_root/elements/{Shot}_{step_code}_{Task}_{name}/v{version}/{width}x{height}/{Shot}_{step_code}_{Task}_{name}_{nuke.output}_{eye}_v{version}.{SEQ}.exr'
    # review output
    #shot_quicktime_quick:
    #    definition: '@work_shot_root/review/quickdaily/{Shot}_{step_code}_{Task}_{name}_{iteration}.mov'
    nuke_shot_render_movie:
        definition: '@work_shot_root/review/{Shot}_{step_code}_{Task}_{name}_{nuke.output}_v{version}.mov'

    #
    # Harmony
    #

    # define the location of a work area
    shot_work_area_harmony:
        definition: '@work_shot_root/harmony'
        root_name: 'primary'

    # define the location of a publish area
    shot_publish_area_harmony:
        definition: '@publish_shot_root/harmony'
        root_name: 'primary'

    # The location of WIP files
    harmony_shot_work:
        definition: '@shot_work_area_harmony/scenes/{Shot}_{step_code}_{Task}_{name}_v{version}.xstage'
        root_name: 'primary'

    # The location of WIP files
    harmony_shot_work_frames_folder:
        definition: '@shot_work_area_harmony/scenes/frames'
        root_name: 'primary'

    harmony_shot_publish_camera:
        definition: "@shot_publish_area_harmony/camera/{Shot}_{step_code}_{Task}_camera_{name}_v{version}.jsonx"
        root_name: 'primary'

    harmony_shot_publish_peg:
        definition: "@shot_publish_area_harmony/peg/{Shot}_{step_code}_{Task}_peg_{name}_{tracker_name}_v{version}.jsonx"
        root_name: 'primary'

    # The location of backups of WIP files
    # Note that snapshots folder is  not inside the wip folder
    # otherwise we could confuse the publishing process when files
    # need to be copied from work to publish.
    harmony_shot_snapshot:
        definition: '@shot_work_area_harmony/snapshots/{Shot}_{step_code}_{Task}_{name}_v{version}.{timestamp}.xstage'
        root_name: 'primary'

    # The location of published harmony files
    harmony_shot_publish:
        definition: '@shot_publish_area_harmony/scenes/{Shot}_{step_code}_{Task}_{name}_v{version}/{Shot}[_{step_code}][_{Task}]_{name}_v{version}.xstage'
        root_name: 'primary'


    #
    # Blender
    #

    # define the location of a work area
    shot_work_area_blender:
        definition: '@work_shot_root/blender'
        root_name: 'primary'

    # define the location of a publish area
    shot_publish_area_blender:
        definition: '@publish_shot_root/blender'
        root_name: 'primary'

    # The location of WIP files
    blender_shot_work:
        definition: '@shot_work_area_blender/scenes/{Shot}_{step_code}_{Task}_{name}_v{version}.blend'
        root_name: 'primary'

    # The location of backups of WIP files
    blender_shot_snapshot:
        definition: '@shot_work_area_blender/snapshots/{Shot}_{step_code}_{Task}_{name}_v{version}.{timestamp}.blend'
        root_name: 'primary'

    # The location of published blender files
    blender_shot_publish:
        definition: '@shot_publish_area_blender/scenes/{Shot}_{step_code}_{Task}_{name}_v{version}/{Shot}[_{step_code}][_{Task}]_{name}_v{version}.blend'
        root_name: 'primary'



    #
    # Hiero
    #

    # export of shot asset data from hiero
    hiero_plate_path:
        definition: '@work_shot_root/editorial/{YYYY}_{MM}_{DD}/plates/{project}_{Shot}_{step_code}_{Task}.mov'
    hiero_render_path:
        definition: '@work_shot_root/editorial/{YYYY}_{MM}_{DD}/renders/{project}_{Shot}_{step_code}_{Task}.{SEQ}.dpx'


    #
    # Fusion
    #

    # define the location of a work area
    shot_work_area_fusion:
        definition: '@work_shot_root/fusion'
        root_name: 'primary'

    # define the location of a publish area
    shot_publish_area_fusion:
        definition: '@publish_shot_root/fusion'
        root_name: 'primary'

    # The location of WIP script files
    fusion_shot_work:
        definition: '@work_shot_root/fusion/{Shot}_{step_code}_{Task}_{name}_v{version}.comp'
        root_name: 'primary'

    # The location of backups of WIP files
    fusion_shot_snapshot:
        definition: '@work_shot_root/fusion/snapshots/{Shot}_{step_code}_{Task}_{name}_v{version}.{timestamp}.comp'
        root_name: 'primary'

    # The location of published fusion script files
    fusion_shot_publish:
        definition: '@publish_shot_root/fusion/{Shot}_{step_code}_{Task}_{name}_v{version}.comp'
        root_name: 'primary'

    # write node outputs
    fusion_shot_render_mono_dpx:
        definition: '@work_shot_root/images/{name}/v{version}/{width}x{height}/{Shot}_{step_code}_{Task}_{name}_{fusion.output}_v{version}.{SEQ}.dpx'
        root_name: 'primary'

    fusion_shot_render_pub_mono_dpx:
        definition: '@publish_shot_root/elements/{name}/v{version}/{width}x{height}/{Shot}_{step_code}_{Task}_{name}_{fusion.output}_v{version}.{SEQ}.dpx'
        root_name: 'primary'

    fusion_shot_render_stereo:
        definition: '@work_shot_root/images/{name}/v{version}/{width}x{height}/{Shot}_{step_code}_{Task}_{name}_{fusion.output}_{eye}_v{version}.{SEQ}.exr'
        root_name: 'primary'

    fusion_shot_render_pub_stereo:
        definition: '@publish_shot_root/elements/{name}/v{version}/{width}x{height}/{Shot}_{step_code}_{Task}_{name}_{fusion.output}_{eye}_v{version}.{SEQ}.exr'
        root_name: 'primary'

    fusion_shot_render_aov:
        definition: '@work_shot_root/images/{name}/v{version}/{aov_name}/{Shot}_{step_code}_{Task}_{name}_{aov_name}_v{version}.{SEQ}.exr'
        root_name: 'primary'

    # review output
    shot_quicktime_quick:
        definition: '@work_shot_root/review/quickdaily/{Shot}_{step_code}_{Task}_{name}_{iteration}.mov'
        root_name: 'primary'

    fusion_shot_render_movie:
        definition: '@work_shot_root/review/{Shot}_{step_code}_{Task}_{name}_v{version}.mov'
        root_name: 'primary'


    #
    # Krita
    #

    # shot
    # define the location of a work area
    shot_work_area_krita:
        definition: '@work_shot_root/krita'
        root_name: 'primary'

    # define the location of a publish area
    shot_publish_area_krita:
        definition: '@publish_shot_root/krita'
        root_name: 'primary'

    # The location of WIP files
    krita_shot_work:
        definition: '@work_shot_root/krita/{name}_v{version}/{Shot}_{step_code}_{Task}_{name}_v{version}.kra'
        root_name: 'primary'

    # The location of backups of WIP files
    krita_shot_snapshot:
        definition: '@work_shot_root/krita/snapshots/{name}_v{version}/{Shot}_{step_code}_{Task}_{name}_v{version}.{timestamp}.kra'
        root_name: 'primary'

    # The location of published krita session
    krita_shot_publish:
        definition: '@publish_shot_root/krita/{name}_v{version}/{Shot}_{step_code}_{Task}_{name}_v{version}.kra'
        root_name: 'primary'

    # These templates are related to publishing layers within Krita
    # where to export the individual layers before publishing them
    krita_shot_layer_export:
        definition: '@work_shot_root/krita/layers/{Shot}_{step_code}_{Task}_krita_layers_v{version}/@krita_layer_name'
        root_name: 'primary'

    # where to publish the individual layers
    krita_shot_layer_publish:
        definition: '@publish_shot_root/krita/layers/{Shot}_{step_code}_{Task}_krita_layers_v{version}/@krita_layer_name'
        root_name: 'primary'

    # where to export layers as a folder before publishing
    krita_shot_layers_export:
        definition: '@work_shot_root/krita/layers/{Shot}_{step_code}_{Task}_krita_layers_v{version}'
        root_name: 'primary'

    # where to publish layers as a folder
    krita_shot_layers_publish:
        definition: '@publish_shot_root/krita/layers/{Shot}_{step_code}_{Task}_krita_layers_v{version}'
        root_name: 'primary'


    ##########################################################################################
    # Asset pipeline

    #
    # No Engine
    #

    # Generic Review Proxy Image Sequences
    # These have support for different formats, being png the default
    # Other image formats can be used by defining the "extension" field
    # "extension" is an alias for "proxy_image_seq_extension"
    # Also have support for single or multi layer publish, in case more
    # than one image sequence per task is requiered, this is set using
    # the "layer" key as optional
    asset_review_work_proxy:
        definition: '@work_asset_root/review/{name}[_{layer}][_{buffer}]_v{version}/{Asset}_{step_code}_{Task}_{name}[_{layer}][_{buffer}]_v{version}.{SEQ}.{proxy_image_seq_extension}'

    asset_review_publish_proxy:
        definition: '@publish_asset_root/review/{name}[_{layer}][_{buffer}]_v{version}/{Asset}_{step_code}_{Task}_{name}[_{layer}][_{buffer}]_v{version}.{SEQ}.{proxy_image_seq_extension}'


    #
    # Alembic caches
    #

    asset_alembic_cache:
        definition: '@publish_asset_root/meshes/alembics/{Asset}_{step_code}_{Task}_alembic_{name}_v{version}.abc'

    asset_alembic_cache_notask:
        definition: '@publish_asset_root_notask/meshes/alembics/{Asset}_{step_code}_alembic_{name}_v{version}.abc'

    asset_gpu_cache:
        definition: '@publish_asset_root/caches/{Asset}_{step_code}_{Task}_gpu_{name}_v{version}.abc'

    asset_gpu_cache_notask:
        definition: '@publish_asset_root_notask/caches/{Asset}_{step_code}_gpu_{name}_v{version}.abc'

    asset_dynamic_attrs_alembic:
        definition: '@publish_asset_root/meshes/alembics/dynAttrs/{Asset}_dynAttrs_{step_code}_{Task}_{name}_v{version}.abc'

    asset_dynamic_attrs_alembic_notask:
        definition: '@publish_asset_root_notask/meshes/alembics/dynAttrs/{Asset}_dynAttrs_{step_code}_{name}_v{version}.abc'

    #
    # USDs
    #

    asset_usda_container:
        definition: '@publish_asset_usd_root/{name}/{Asset}_{name}_Asset_v{version}.usda'

    asset_usdc_cache:
        definition: '@publish_asset_usd_root/{name}/geo/{Asset}_{name}_geo_v{version}.usd'

    asset_usdc_edits:
        definition: '@publish_asset_usd_root/{name}/edits/{Asset}_{name}_{usd_edit_name}_v{version}.usd'

    asset_usda_materials:
        definition: '@publish_asset_usd_root/{name}/materials/{Asset}_{name}_materials_v{version}.usda'

    #
    # Structure Abstractions
    #
    asset_structure_abstraction:
        definition: '@publish_asset_root/abstractions/{Asset}_{step_code}_{Task}_abstraction_{name}_v{version}.json'

    asset_structure_abstraction_notask:
        definition: '@publish_asset_root_notask/abstractions/{Asset}_{step_code}_abstraction_{name}_v{version}.json'

    #
    # Review Movies
    #

    asset_review_publish:
        definition: '@publish_asset_root/review/{Asset}_{step_code}_{Task}_{name}_v{version}.mov'

    asset_review_publish_notask:
        definition: '@publish_asset_root_notask/review/{Asset}_{step_code}_{name}_v{version}.mov'

    #
    # Review Sequences
    #

    asset_review_sequence_publish:
        definition: '@publish_asset_root/review/{Asset}_{step_code}_{Task}_{name}_v{version}/{Asset}_{step_code}_{Task}_{name}_v{version}.{SEQ}.png'

    asset_review_sequence_publish_notask:
        definition: '@publish_asset_root_notask/review/{Asset}_{step_code}_{name}_v{version}/{Asset}_{step_code}_{name}_v{version}.{SEQ}.png'



    #
    # Custom Shapes
    #


    asset_publish_proxy_custom_shape_image:
        definition: '@publish_asset_root/shapes/proxies/{shape.piece}/version/v{version}/{Asset}_{step_code}_{Task}_{shape.piece}_v{version}_{shape.frame}.png'

    asset_publish_render_custom_shape_image:
        definition: '@publish_asset_root/shapes/render/{shape.piece}/version/v{version}/{Asset}_{step_code}_{Task}_{shape.piece}_v{version}_{shape.frame}.exr'

    asset_publish_proxy_custom_shape_library_image:
        definition: '@publish_asset_root/shapes/proxies/{shape.piece}/library/{Asset}_{step_code}_{Task}_{shape.piece}_{shape.frame.number}.png'

    asset_publish_render_custom_shape_library_image:
        definition: '@publish_asset_root/shapes/render/{shape.piece}/library/{Asset}_{step_code}_{Task}_{shape.piece}_{shape.frame.number}.exr'

    asset_publish_proxy_custom_shape_image_notask:
        definition: '@publish_asset_root_notask/shapes/proxies/{shape.piece}/version/v{version}/{Asset}_{step_code}_{shape.piece}_v{version}_{shape.frame}.png'

    asset_publish_render_custom_shape_image_notask:
        definition: '@publish_asset_root_notask/shapes/render/{shape.piece}/version/v{version}/{Asset}_{step_code}_{shape.piece}_v{version}_{shape.frame}.exr'

    asset_publish_proxy_custom_shape_library_image_notask:
        definition: '@publish_asset_root_notask/shapes/proxies/{shape.piece}/library/{Asset}_{step_code}_{shape.piece}_{shape.frame.number}.png'

    asset_publish_render_custom_shape_library_image_notask:
        definition: '@publish_asset_root_notask/shapes/render/{shape.piece}/library/{Asset}_{step_code}_{shape.piece}_{shape.frame.number}.exr'

    #
    # Photoshop
    #

    # The location of WIP files
    asset_work_area_photoshop:
        definition: '@work_asset_root/photoshop'

    photoshop_asset_work:
        definition: '@asset_work_area_photoshop/{Asset}_{step_code}_{Task}_{name}_v{version}.{photoshop_extension}'


    asset_work_area_photoshop_notask:
        definition: '@work_asset_root_notask/photoshop'

    photoshop_asset_work_notask:
        definition: '@asset_work_area_photoshop_notask/{Asset}_{step_code}_{name}_v{version}.{photoshop_extension}'

    # The location of backups of WIP files
    photoshop_asset_snapshot:
        definition: '@asset_work_area_photoshop/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.{photoshop_extension}'

    photoshop_asset_snapshot_notask:
        definition: '@asset_work_area_photoshop_notask/snapshots/{Asset}_{step_code}_{name}_v{version}.{timestamp}.{photoshop_extension}'

    # The location of published files
    asset_publish_area_photoshop:
        definition: '@publish_asset_root/photoshop'
    photoshop_asset_publish:
        definition: '@asset_publish_area_photoshop/{Asset}_{step_code}_{Task}_{name}_v{version}.{photoshop_extension}'
    photoshop_asset_proxy_publish:
        definition: '@asset_publish_area_photoshop/proxy/{Asset}_{step_code}_{Task}_{name}_proxy_v{version}.{photoshop_extension}'

    asset_publish_area_photoshop_notask:
        definition: '@publish_asset_root_notask/photoshop'
    photoshop_asset_publish_notask:
        definition: '@asset_publish_area_photoshop_notask/{Asset}_{step_code}_{name}_v{version}.{photoshop_extension}'
    photoshop_asset_proxy_publish_notask:
        definition: '@asset_publish_area_photoshop_notask/{Asset}_{step_code}_{name}_proxy_v{version}.{photoshop_extension}'

    # where to export the individual layers before publishing them
    photoshop_asset_layer_export:
        definition: '@asset_work_area_photoshop/layers/v{version}/{Asset}_{step_code}_{Task}_{name}_@photoshop_layer_name'
        root_name: 'primary'

    photoshop_asset_layer_export_notask:
        definition: '@asset_work_area_photoshop_notask/layers/v{version}/{Asset}_{step_code}_{name}_@photoshop_layer_name'
        root_name: 'primary'

    # Disabled because these templates are clashing with photoshop_asset_layergroup_publish and photoshop_asset_layergroup_publish_notask
    # where to publish the individual layers
    # photoshop_asset_layer_publish:
    #     definition: '@asset_publish_area_photoshop/layers/v{version}/{Asset}_{step_code}_{Task}_{name}_@photoshop_layer_name'
    #     root_name: 'primary'

    # photoshop_asset_layer_publish_notask:
    #     definition: '@asset_publish_area_photoshop_notask/layers/v{version}/{Asset}_{step_code}_{name}_@photoshop_layer_name'
    #     root_name: 'primary'

    photoshop_asset_layergroup_publish:
        definition: '@asset_publish_area_photoshop/layers/v{version}/{Asset}_{step_code}_{Task}_{name}_@photoshop_layer_name'
    photoshop_asset_layergroup_publish_notask:
        definition: '@asset_publish_area_photoshop_notask/layers/v{version}/{Asset}_{step_code}_{name}_@photoshop_layer_name'

    photoshop_asset_layergroup_order:
        definition: '@asset_publish_area_photoshop/{Asset}_{step_code}_{Task}_LayerOrder_{name}_v{version}.json'

    photoshop_asset_layergroup_order_notask:
        definition: '@asset_publish_area_photoshop_notask/{Asset}_{step_code}_LayerOrder_{name}_v{version}.json'



    photoshop_asset_layergroup_proxy_publish:
        definition: '@asset_publish_area_photoshop/layers/v{version}/{Asset}_{step_code}_{Task}_{name}_@photoshop_layer_name_proxy'
    photoshop_asset_layergroup_proxy_publish_notask:
        definition: '@asset_publish_area_photoshop_notask/layers/v{version}/{Asset}_{step_code}_{name}_@photoshop_layer_name_proxy'

    # The location of published images review files
    photoshop_asset_image_review_publish:
        definition: '@asset_publish_area_photoshop/review/{Asset}_{step_code}_{Task}_{name}_v{version}/frames/{Asset}_{step_code}_{Task}_{name}_v{version}.{SEQ5}.png'
    photoshop_asset_image_review_publish_notask:
        definition: '@asset_work_area_photoshop_notask/review/{Asset}_{step_code}_{Task}_{name}_v{version}/frames/{Asset}_{step_code}_{Task}_{name}_v{version}.{SEQ5}.png'

    # The location of published video from images
    photoshop_asset_video_review_publish:
        definition: '@asset_publish_area_photoshop/review/{Asset}_{step_code}_{Task}_{name}_v{version}/{Asset}_{step_code}_{Task}_{name}_v{version}.mov'
    photoshop_asset_video_review_publish_notask:
        definition: '@asset_work_area_photoshop_notask/review/{Asset}_{step_code}_{Task}_{name}_v{version}/{Asset}_{step_code}_{Task}_{name}_v{version}.mov'


    #
    # Illustrator
    #

    # The location of WIP files
    asset_work_area_illustrator:
        definition: '@work_asset_root/illustrator'

    illustrator_asset_work:
        definition: '@asset_work_area_illustrator/{Asset}_{step_code}_{Task}_{name}_v{version}.ai'


    asset_work_area_illustrator_notask:
        definition: '@work_asset_root_notask/illustrator'

    illustrator_asset_work_notask:
        definition: '@asset_work_area_illustrator_notask/{Asset}_{step_code}_{name}_v{version}.ai'

    # The location of backups of WIP files
    illustrator_asset_snapshot:
        definition: '@asset_work_area_illustrator/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.ai'

    illustrator_asset_snapshot_notask:
        definition: '@asset_work_area_illustrator_notask/snapshots/{Asset}_{step_code}_{name}_v{version}.{timestamp}.ai'

    # The location of published files
    asset_publish_area_illustrator:
        definition: '@publish_asset_root/illustrator'
    illustrator_asset_publish:
        definition: '@asset_publish_area_illustrator/{Asset}_{step_code}_{Task}_{name}_v{version}.ai'
    # illustrator_asset_proxy_publish:
    #     definition: '@asset_publish_area_illustrator/proxy/{Asset}_{step_code}_{Task}_{name}_proxy_v{version}.ai'

    asset_publish_area_illustrator_notask:
        definition: '@publish_asset_root_notask/illustrator'
    illustrator_asset_publish_notask:
        definition: '@asset_publish_area_illustrator_notask/{Asset}_{step_code}_{name}_v{version}.ai'
    # illustrator_asset_proxy_publish_notask:
    #     definition: '@asset_publish_area_illustrator_notask/{Asset}_{step_code}_{name}_proxy_v{version}.ai'

    # # where to export the individual layers before publishing them
    # illustrator_asset_layer_export:
    #     definition: '@asset_work_area_illustrator/layers/v{version}/{Asset}_{step_code}_{Task}_{name}_@illustrator_layer_name'
    #     root_name: 'primary'

    # illustrator_asset_layer_export_notask:
    #     definition: '@asset_work_area_illustrator_notask/layers/v{version}/{Asset}_{step_code}_{name}_@illustrator_layer_name'
    #     root_name: 'primary'

    # illustrator_asset_layergroup_publish:
    #     definition: '@asset_publish_area_illustrator/layers/v{version}/{Asset}_{step_code}_{Task}_{name}_@illustrator_layer_name'
    # illustrator_asset_layergroup_publish_notask:
    #     definition: '@asset_publish_area_illustrator_notask/layers/v{version}/{Asset}_{step_code}_{name}_@illustrator_layer_name'

    # illustrator_asset_layergroup_proxy_publish:
    #     definition: '@asset_publish_area_illustrator/layers/v{version}/{Asset}_{step_code}_{Task}_{name}_@illustrator_layer_name_proxy'
    # illustrator_asset_layergroup_proxy_publish_notask:
    #     definition: '@asset_publish_area_illustrator_notask/layers/v{version}/{Asset}_{step_code}_{name}_@illustrator_layer_name_proxy'

    # # The location of published images review files
    # illustrator_asset_image_review_publish:
    #     definition: '@asset_publish_area_illustrator/review/{Asset}_{step_code}_{Task}_{name}_v{version}/frames/{Asset}_{step_code}_{Task}_{name}_v{version}.{SEQ5}.png'
    # illustrator_asset_image_review_publish_notask:
    #     definition: '@asset_work_area_illustrator_notask/review/{Asset}_{step_code}_{Task}_{name}_v{version}/frames/{Asset}_{step_code}_{Task}_{name}_v{version}.{SEQ5}.png'

    # # The location of published video from images
    # illustrator_asset_video_review_publish:
    #     definition: '@asset_publish_area_illustrator/review/{Asset}_{step_code}_{Task}_{name}_v{version}/{Asset}_{step_code}_{Task}_{name}_v{version}.mov'
    # illustrator_asset_video_review_publish_notask:
    #     definition: '@asset_work_area_illustrator_notask/review/{Asset}_{step_code}_{Task}_{name}_v{version}/{Asset}_{step_code}_{Task}_{name}_v{version}.mov'


    #
    # after effects
    #

    # The location of WIP files

    asset_work_area_aftereffects:
        definition: '@work_asset_root/afx'

    asset_work_area_aftereffects_notask:
        definition: '@work_asset_root_notask/afx'

    aftereffects_asset_work:
        definition: '@asset_work_area_aftereffects/{Asset}_{step_code}_{Task}_{name}_v{version}.aep'

    aftereffects_asset_work_notask:
        definition: '@asset_work_area_aftereffects_notask/{Asset}_{step_code}_{name}_v{version}.aep'

    # The location of backups of WIP files
    aftereffects_asset_snapshot:
        definition: '@asset_work_area_aftereffects/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.aep'

    aftereffects_asset_snapshot_notask:
        definition: '@asset_work_area_aftereffects_notask/snapshots/{Asset}_{step_code}_{name}_v{version}.{timestamp}.aep'

    # The location of published files
    asset_publish_area_aftereffects:
        definition: '@publish_asset_root/afx'

    asset_publish_area_aftereffects_notask:
        definition: '@publish_asset_root_notask/afx'

    aftereffects_asset_publish:
        definition: '@publish_asset_root/afx/{Asset}_{step_code}_{Task}_{name}_v{version}.aep'

    aftereffects_asset_publish_notask:
        definition: '@publish_asset_root_notask/afx/{Asset}_{step_code}_{name}_v{version}.aep'

    aftereffects_asset_render_pub_mono:
        # TODO: use dynamic extension using choices defined for afx.seq.ext
        # definition: '@publish_asset_root/afx/render/{name}_v{version}/{afx.comp}/{Asset}_{step_code}_{Task}_{name}_{afx.comp}_v{version}.{SEQ}.{afx.seq.ext}'
        definition: '@publish_asset_root/afx/render/{name}_v{version}/{afx.comp}/{Asset}_{step_code}_{Task}_{name}_{afx.comp}_v{version}.{SEQ}.png'

    aftereffects_asset_render_pub_mono_notask:
        # TODO: use dynamic extension using choices defined for afx.seq.ext
        # definition: '@publish_asset_root_notask/afx/render/{name}_v{version}/{afx.comp}/{Asset}_{step_code}_{name}_{afx.comp}_v{version}.{SEQ}.{afx.seq.ext}'
        definition: '@publish_asset_root_notask/afx/render/{name}_v{version}/{afx.comp}/{Asset}_{step_code}_{name}_{afx.comp}_v{version}.{SEQ}.png'

    # The following template uses {afx.mov.ext} this is a special key, that will be only there
    # in the beta to support different extensions on mac and windows, while using the same
    # output module (Lossless with Alpha)
    aftereffects_asset_render_movie:
        # TODO: use dynamic extension using choices defined for afx.mov.ext
        # definition: '@publish_asset_root/afx/review/{Asset}_{step_code}_{Task}_{name}_{afx.comp}_v{version}.{afx.mov.ext}'
        definition: '@publish_asset_root/afx/review/{Asset}_{step_code}_{Task}_{name}_{afx.comp}_v{version}.mov'

    aftereffects_asset_render_movie_notask:
        # TODO: use dynamic extension using choices defined for afx.mov.ext
        # definition: '@publish_asset_root_notask/afx/review/{Asset}_{step_code}_{name}_{afx.comp}_v{version}.{afx.mov.ext}'
        definition: '@publish_asset_root_notask/afx/review/{Asset}_{step_code}_{name}_{afx.comp}_v{version}.mov'

    #
    # Mari
    #
    asset_mari_texture_tif:
        definition: '@publish_asset_root/mari/{Asset}_{step_code}_{Task}_{name}_{mari.channel}[_{mari.layer}]_v{version}.{UDIM}.tif'

    asset_mari_texture_tif_notask:
        definition: '@publish_asset_root_notask/mari/{Asset}_{step_code}_{name}_{mari.channel}[_{mari.layer}]_v{version}.{UDIM}.tif'

    #
    # Maya
    #

    # define the location of a work area
    asset_work_area_maya:
        definition: '@work_asset_root/maya'

    asset_work_area_maya_notask:
        definition: '@work_asset_root_notask/maya'

    # define the location of a publish area
    asset_publish_area_maya:
        definition: '@publish_asset_root/maya'

    asset_publish_area_maya_notask:
        definition: '@publish_asset_root_notask/maya'

    # The location of WIP files
    maya_asset_work:
        definition: '@asset_work_area_maya/{Asset}_{step_code}_{Task}_{name}_v{version}.{maya_extension}'

    maya_asset_work_notask:
        definition: '@asset_work_area_maya_notask/{Asset}_{step_code}_{name}_v{version}.{maya_extension}'

    # The location of backups of WIP files
    maya_asset_snapshot:
        definition: '@asset_work_area_maya/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.{maya_extension}'

    maya_asset_snapshot_notask:
        definition: '@asset_work_area_maya_notask/snapshots/{Asset}_{step_code}_{name}_v{version}.{timestamp}.{maya_extension}'

    # The location of published maya files
    maya_asset_publish:
        definition: '@asset_publish_area_maya/{Asset}_{step_code}_{Task}_{name}_v{version}.{maya_extension}'

    maya_asset_publish_notask:
        definition: '@asset_publish_area_maya_notask/{Asset}_{step_code}_{name}_v{version}.{maya_extension}'

    # The location of publish 3d objets
    3d_mesh_asset_publish:
        definition: '@publish_asset_root/meshes/{3d_mesh_extension}/{Asset}_{step_code}_{Task}_{3d_mesh_extension}_{name}_v{version}.{3d_mesh_extension}'

    3d_mesh_asset_publish_notask:
        definition: '@publish_asset_root_notask/meshes/{3d_mesh_extension}/{Asset}_{step_code}_{3d_mesh_extension}_{name}_v{version}.{3d_mesh_extension}'


    #
    # Houdini
    #

    # define the location of a work area
    asset_work_area_houdini:
        definition: '@work_asset_root/houdini'

    asset_work_area_houdini_notask:
        definition: '@work_asset_root_notask/houdini'

    # define the location of a publish area
    asset_publish_area_houdini:
        definition: '@publish_asset_root/houdini'

    asset_publish_area_houdini_notask:
        definition: '@publish_asset_root_notask/houdini'

    # The location of WIP files
    houdini_asset_work:
        definition: '@asset_work_area_houdini/{Asset}_{step_code}_{Task}_{name}_v{version}.hip'

    houdini_asset_work_notask:
        definition: '@asset_work_area_houdini_notask/{Asset}_{step_code}_{name}_v{version}.hip'

    # The location of backups of WIP files
    houdini_asset_snapshot:
        definition: '@asset_work_area_houdini/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.hip'

    houdini_asset_snapshot_notask:
        definition: '@asset_work_area_houdini_notask/snapshots/{Asset}_{step_code}_{name}_v{version}.{timestamp}.hip'

    # The location of published houdini files
    houdini_asset_publish:
        definition: '@asset_publish_area_houdini/{Asset}_{step_code}_{Task}_{name}_v{version}.hip'

    houdini_asset_publish_notask:
        definition: '@asset_publish_area_houdini_notask/{Asset}_{step_code}_{name}_v{version}.hip'

    # Alembic caches
    houdini_asset_work_alembic_cache:
        definition: '@asset_work_area_houdini/cache/alembic/{Asset}_{step_code}_{Task}_{name}/{houdini.node}/v{version}/{Asset}_{step_code}_{Task}_{name}_v{version}.abc'

    houdini_asset_work_alembic_cache_notask:
        definition: '@asset_work_area_houdini_notask/cache/alembic/{Asset}_{step_code}_{name}/{houdini.node}/v{version}/{Asset}_{step_code}_{name}_v{version}.abc'

    # Rendered images
    houdini_asset_render:
        definition: '@work_asset_root/images/{Asset}_{step_code}_{Task}_{name}/{houdini.node}/v{version}/{width}x{height}/{Asset}_{step_code}_{Task}_{name}_v{version}.{SEQ}.exr'

    houdini_asset_render_notask:
        definition: '@work_asset_root_notask/images/{Asset}_{step_code}_{name}/{houdini.node}/v{version}/{width}x{height}/{Asset}_{step_code}_{name}_v{version}.{SEQ}.exr'

    # Additional mantra outputs
    houdini_asset_ifd:
        definition: '@work_asset_root/ifds/{Asset}_{step_code}_{Task}_{name}/{houdini.node}/v{version}/{width}x{height}/{Asset}_{step_code}_{Task}_{name}_v{version}.{SEQ}.ifd'

    houdini_asset_ifd_notask:
        definition: '@work_asset_root_notask/ifds/{Asset}_{step_code}_{name}/{houdini.node}/v{version}/{width}x{height}/{Asset}_{step_code}_{name}_v{version}.{SEQ}.ifd'

    houdini_asset_dcm:
        definition: '@work_asset_root/dcms/{Asset}_{step_code}_{Task}_{name}/{houdini.node}/v{version}/{width}x{height}/{Asset}_{step_code}_{Task}_{name}_v{version}.{SEQ}.dcm'

    houdini_asset_dcm_notask:
        definition: '@work_asset_root_notask/dcms/{Asset}_{step_code}_{name}/{houdini.node}/v{version}/{width}x{height}/{Asset}_{step_code}_{name}_v{version}.{SEQ}.dcm'

    houdini_asset_extra_plane:
        definition: '@work_asset_root/images/{Asset}_{step_code}_{Task}_{name}/{houdini.node}/{aov_name}/v{version}/{width}x{height}/{Asset}_{step_code}_{Task}_{name}_v{version}.{SEQ}.exr'

    houdini_asset_extra_plane_notask:
        definition: '@work_asset_root_notask/images/{Asset}_{step_code}_{name}/{houdini.node}/{aov_name}/v{version}/{width}x{height}/{Asset}_{step_code}_{name}_v{version}.{SEQ}.exr'


    #
    # 3dsmax
    #

    # define the location of a work area
    asset_work_area_max:
        definition: '@work_asset_root/3dsmax'

    asset_work_area_max_notask:
        definition: '@work_asset_root_notask/3dsmax'

    # define the location of a publish area
    asset_publish_area_max:
        definition: '@publish_asset_root/3dsmax'

    asset_publish_area_max_notask:
        definition: '@publish_asset_root_notask/3dsmax'

    # The location of WIP files
    max_asset_work:
        definition: '@asset_work_area_max/{Asset}_{step_code}_{Task}_{name}_v{version}.max'

    max_asset_work_notask:
        definition: '@asset_work_area_max_notask/{Asset}_{step_code}_{name}_v{version}.max'

    # The location of backups of WIP files
    max_asset_snapshot:
        definition: '@asset_work_area_max/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.max'

    max_asset_snapshot_notask:
        definition: '@asset_work_area_max_notask/snapshots/{Asset}_{step_code}_{name}_v{version}.{timestamp}.max'

    # The location of published max files
    max_asset_publish:
        definition: '@asset_publish_area_max/{Asset}_{step_code}_{Task}_{name}_v{version}.max'

    max_asset_publish_notask:
        definition: '@asset_publish_area_max_notask/{Asset}_{step_code}_{name}_v{version}.max'

    #
    # Motionbuilder
    #

    # define the location of a work area
    asset_work_area_mobu:
        definition: '@work_asset_root/mobu'

    asset_work_area_mobu_notask:
        definition: '@work_asset_root_notask/mobu'

    # define the location of a publish area
    asset_publish_area_mobu:
        definition: '@publish_asset_root/mobu'

    asset_publish_area_mobu_notask:
        definition: '@publish_asset_root_notask/mobu'

    # The location of WIP files
    mobu_asset_work:
        definition: '@asset_work_area_mobu/{Asset}_{step_code}_{Task}_{name}_v{version}.fbx'

    mobu_asset_work_notask:
        definition: '@asset_work_area_mobu_notask/{Asset}_{step_code}_{name}_v{version}.fbx'

    # The location of backups of WIP files
    mobu_asset_snapshot:
        definition: '@asset_work_area_mobu/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.fbx'

    mobu_asset_snapshot_notask:
        definition: '@asset_work_area_mobu_notask/snapshots/{Asset}_{step_code}_{name}_v{version}.{timestamp}.fbx'

    # The location of published Motionbuilder files
    mobu_asset_publish:
        definition: '@asset_publish_area_mobu/{Asset}_{step_code}_{Task}_{name}_v{version}.fbx'

    mobu_asset_publish_notask:
        definition: '@asset_publish_area_mobu_notask/{Asset}_{step_code}_{name}_v{version}.fbx'

    #
    # Nuke
    #

    # define the location of a work area
    asset_work_area_nuke:
        definition: '@work_asset_root/nuke'

    asset_work_area_nuke_notask:
        definition: '@work_asset_root_notask/nuke'

    # define the location of a publish area
    asset_publish_area_nuke:
        definition: '@publish_asset_root/nuke'

    asset_publish_area_nuke_notask:
        definition: '@publish_asset_root_notask/nuke'

    # outputs from the Shotgun Write Node for assets
    nuke_asset_render:
        definition: '@work_asset_root/images/{Asset}_{step_code}_{Task}_{name}/v{version}/{width}x{height}/{Asset}_{step_code}_{Task}_{name}_{nuke.output}_v{version}.{SEQ}.exr'

    nuke_asset_render_pub:
        definition: '@publish_asset_root/elements/{Asset}_{step_code}_{Task}_{name}/v{version}/{width}x{height}/{Asset}_{step_code}_{Task}_{name}_{nuke.output}_v{version}.{SEQ}.exr'

    nuke_asset_render_notask:
        definition: '@work_asset_root_notask/images/{Asset}_{step_code}_{name}/v{version}/{width}x{height}/{Asset}_{step_code}_{name}_{nuke.output}_v{version}.{SEQ}.exr'

    nuke_asset_render_pub_notask:
        definition: '@publish_asset_root_notask/elements/{Asset}_{step_code}_{name}/v{version}/{width}x{height}/{Asset}_{step_code}_{name}_{nuke.output}_v{version}.{SEQ}.exr'

    # review output
    nuke_asset_render_movie:
        definition: '@work_asset_root/review/{Asset}_{step_code}_{Task}_{name}_{nuke.output}_v{version}.mov'
    asset_quicktime_quick:
        definition: '@work_asset_root/review/quickdaily/{Asset}_{step_code}_{Task}_{name}_{iteration}.mov'

    nuke_asset_render_movie:
        definition: '@work_asset_root_notask/review/{Asset}_{step_code}_{name}_{nuke.output}_v{version}.mov'
    asset_quicktime_quick:
        definition: '@work_asset_root_notask/review/quickdaily/{Asset}_{step_code}_{name}_{iteration}.mov'

    # The location of WIP script files
    nuke_asset_work:
        definition: '@asset_work_area_nuke/{Asset}_{step_code}_{Task}_{name}_v{version}.nk'

    nuke_asset_work_notask:
        definition: '@asset_work_area_nuke_notask/{Asset}_{step_code}_{name}_v{version}.nk'

    # The location of backups of WIP files
    nuke_asset_snapshot:
        definition: '@asset_work_area_nuke/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.nk'

    nuke_asset_snapshot_notask:
        definition: '@asset_work_area_nuke_notask/snapshots/{Asset}_{step_code}_{name}_v{version}.{timestamp}.nk'

    # The location of published nuke script files
    nuke_asset_publish:
        definition: '@asset_publish_area_nuke/{Asset}_{step_code}_{Task}_{name}_v{version}.nk'

    nuke_asset_publish_notask:
        definition: '@asset_publish_area_nuke_notask/{Asset}_{step_code}_{name}_v{version}.nk'

    #
    # Alias
    #

    # define the location of a work area
    asset_work_area_alias:
        definition: '@work_asset_root/alias'

    asset_work_area_alias_notask:
        definition: '@work_asset_root_notask/alias'

    # define the location of a publish area
    asset_publish_area_alias:
        definition: '@publish_asset_root/alias'

    asset_publish_area_alias_notask:
        definition: '@publish_asset_root_notask/alias'

    # The location of WIP files
    alias_asset_work:
        definition: '@asset_work_area_alias/{Asset}_{step_code}_{Task}_{name}_v{version}.wire'

    alias_asset_work_notask:
        definition: '@asset_work_area_alias_notask/{Asset}_{step_code}_{name}_v{version}.wire'

    # The location of backups of WIP files
    alias_asset_snapshot:
        definition: '@asset_work_area_alias/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.wire'

    alias_asset_snapshot_notask:
        definition: '@asset_work_area_alias_notask/snapshots/{Asset}_{step_code}_{name}_v{version}.{timestamp}.wire'

    # The location of published Alias files
    alias_asset_publish:
        definition: '@asset_publish_area_alias/{Asset}_{step_code}_{Task}_{name}_v{version}.wire'

    alias_asset_publish_notask:
        definition: '@asset_publish_area_alias_notask/{Asset}_{step_code}_{name}_v{version}.wire'

    # Alias translations

    alias_asset_igs_publish:
        definition: '@asset_publish_area_alias/translations/{Asset}_{step_code}_{Task}_{name}_v{version}.igs'
    alias_asset_catpart_publish:
        definition: '@asset_publish_area_alias/translations/{Asset}_{step_code}_{Task}_{name}_v{version}.CATPart'
    alias_asset_jt_publish:
        definition: '@asset_publish_area_alias/translations/{Asset}_{step_code}_{Task}_{name}_v{version}.jt'
    alias_asset_stp_publish:
        definition: '@asset_publish_area_alias/translations/{Asset}_{step_code}_{Task}_{name}_v{version}.stp'
    alias_asset_wref_publish:
        definition: '@asset_publish_area_alias/translations/{Asset}_{step_code}_{Task}_{name}_v{version}.wref'

    alias_asset_igs_publish_notask:
        definition: '@asset_publish_area_alias_notask/translations/{Asset}_{step_code}_{name}_v{version}.igs'
    alias_asset_catpart_publish_notask:
        definition: '@asset_publish_area_alias_notask/translations/{Asset}_{step_code}_{name}_v{version}.CATPart'
    alias_asset_jt_publish_notask:
        definition: '@asset_publish_area_alias_notask/translations/{Asset}_{step_code}_{name}_v{version}.jt'
    alias_asset_stp_publish_notask:
        definition: '@asset_publish_area_alias_notask/translations/{Asset}_{step_code}_{name}_v{version}.stp'
    alias_asset_wref_publish_notask:
        definition: '@asset_publish_area_alias_notask/translations/{Asset}_{step_code}_{name}_v{version}.wref'

    #
    # VRED
    #

    # define the location of a work area
    asset_work_area_vred:
        definition: '@work_asset_root/vred'

    asset_work_area_vred_notask:
        definition: '@work_asset_root_notask/vred'

    # define the location of a publish area
    asset_publish_area_vred:
        definition: '@publish_asset_root/vred'

    asset_publish_area_vred_notask:
        definition: '@publish_asset_root_notask/vred'

    # The location of WIP files
    vred_asset_work:
        definition: '@asset_work_area_vred/{Asset}_{step_code}_{Task}_{name}_v{version}.{vred.extension}'

    vred_asset_work_notask:
        definition: '@asset_work_area_vred/{Asset}_{step_code}_{name}_v{version}.{vred.extension}'

    # The location of backups of WIP files
    vred_asset_snapshot:
        definition: '@asset_work_area_vred/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.{vred.extension}'

    vred_asset_snapshot_notask:
        definition: '@asset_work_area_vred_notask/snapshots/{Asset}_{step_code}_{name}_v{version}.{timestamp}.{vred.extension}'

    # The location of published VRED files
    vred_asset_publish:
        definition: '@asset_publish_area_vred/{Asset}_{step_code}_{Task}_{name}_v{version}.{vred.extension}'

    vred_asset_publish_notask:
        definition: '@asset_publish_area_vred_notask/{Asset}_{step_code}_{name}_v{version}.{vred.extension}'

    # define the location of VRED geometry published files
    vred_asset_geometry_publish:
        definition: '@asset_publish_area_vred/geometry/{Asset}_{step_code}_{Task}_{name}_v{version}.osb'

    vred_asset_geometry_publish_notask:
        definition: '@asset_publish_area_vred_notask/geometry/{Asset}_{step_code}_{name}_v{version}.osb'

    # define the location of the WIP render images
    vred_asset_render_work:
        definition: '@work_asset_root/images/{name}/v{version}/{Asset}_{step_code}_{Task}_{name}_v{version}[-{vred.render_pass}].{vred.render_extension}'

    vred_asset_render_work_notask:
        definition: '@work_asset_root_notask/images/{name}/v{version}/{Asset}_{step_code}_{name}_v{version}[-{vred.render_pass}].{vred.render_extension}'


    # define the location of the WIP renderings for an image sequence
    vred_asset_render_sequence_work:
        definition: '@work_asset_root/images/{name}/v{version}/{Asset}_{step_code}_{Task}_{name}_v{version}[-{vred.render_pass}]-{vred.frame}.{vred.render_extension}'

    vred_asset_render_sequence_work_notask:
        definition: '@work_asset_root_notask/images/{name}/v{version}/{Asset}_{step_code}_{name}_v{version}[-{vred.render_pass}]-{vred.frame}.{vred.render_extension}'

    # define the location of the published render images
    vred_asset_render_publish:
        definition: '@publish_asset_root/images/{name}/v{version}/{Asset}_{step_code}_{Task}_{name}_v{version}[-{vred.render_pass}].{vred.render_extension}'

    vred_asset_render_publish_notask:
        definition: '@publish_asset_root_notask/images/{name}/v{version}/{Asset}_{step_code}_{name}_v{version}[-{vred.render_pass}].{vred.render_extension}'

    # define the location of the published renderings for an image sequence
    vred_asset_render_sequence_publish:
        definition: '@publish_asset_root/images/{name}/v{version}/{Asset}_{step_code}_{Task}_{name}_v{version}[-{vred.render_pass}]-{vred.frame}.{vred.render_extension}'

    vred_asset_render_sequence_publish_notask:
        definition: '@publish_asset_root_notask/images/{name}/v{version}/{Asset}_{step_code}_{name}_v{version}[-{vred.render_pass}]-{vred.frame}.{vred.render_extension}'

    #
    # Harmony
    #

    # asset
    # define the location of a work area
    asset_work_area_harmony:
        definition: '@work_asset_root/harmony'
        root_name: 'primary'

    asset_work_area_harmony_notask:
        definition: '@work_asset_root_notask/harmony'
        root_name: 'primary'

    # define the location of a publish area
    asset_publish_area_harmony:
        definition: '@publish_asset_root/harmony'
        root_name: 'primary'

    asset_publish_area_harmony_notask:
        definition: '@publish_asset_root_notask/harmony'
        root_name: 'primary'

    # The location of WIP files
    harmony_asset_work_frames_folder:
        definition: '@asset_work_area_harmony/scenes/frames'
        root_name: 'primary'

    # The location of WIP files
    harmony_asset_work:
        definition: '@asset_work_area_harmony/scenes/{Asset}_{step_code}_{Task}_{name}_v{version}.xstage'
        root_name: 'primary'

    harmony_asset_work_notask:
        definition: '@asset_work_area_harmony_notask/scenes/{Asset}_{step_code}_{Task}_{name}_v{version}.xstage'
        root_name: 'primary'

    # The location of face related output renders
    harmony_asset_work_face_reference:
        definition: '@asset_work_area_harmony/scenes/frames/References/Face_Reference_{shape.frame}.png'
        root_name: 'primary'

    harmony_asset_work_face_reference_notask:
        definition: '@asset_work_area_harmony_notask/scenes/frames/References/Face_Reference_{shape.frame}.png'
        root_name: 'primary'

    harmony_asset_publish_face_reference:
        definition: '@asset_publish_area_harmony/scenes/{Asset}_{step_code}_{Task}_{name}_v{version}/frames/References/Face_Reference_{shape.frame}.png'
        root_name: 'primary'

    harmony_asset_publish_face_reference_notask:
        definition: '@asset_publish_area_harmony_notask/scenes/{Asset}_{step_code}_{name}_v{version}/frames/References/Face_Reference_{shape.frame}.png'
        root_name: 'primary'

    # The location of generic output renders
    harmony_asset_work_sequence_png:
        definition: "@asset_work_area_harmony/scenes/frames/{tokenName}.{frame}.png"
        root_name: 'primary'

    harmony_asset_work_sequence_png_notask:
        definition: "@asset_work_area_harmony_notask/scenes/frames/{tokenName}.{frame}.png"
        root_name: 'primary'

    harmony_asset_publish_sequence_png:
        definition: "@asset_publish_area_harmony/scenes/{Asset}_{step_code}_{Task}/v{version}/frames/{Asset}_{name}.{frame}.png"
        root_name: 'primary'

    harmony_asset_publish_sequence_png_notask:
        definition: "@asset_publish_area_harmony_notask/scenes/{Asset}_{step_code}/v{version}/frames/{Asset}_{name}.{frame}.png"
        root_name: 'primary'

    # The location of Template TPL files
    harmony_asset_work_tpl:
        definition: "@asset_work_area_harmony/pipelineLibrary/{tokenName}.tpl"
        root_name: 'primary'

    harmony_asset_work_tpl_notask:
        definition: "@asset_work_area_harmony_notask/pipelineLibrary/{tokenName}.tpl"
        root_name: 'primary'

    harmony_asset_publish_tpl:
        definition: "@asset_publish_area_harmony/tpl/{Asset}_{step_code}_{Task}_{name}_v{version}.tpl"
        root_name: 'primary'

    harmony_asset_publish_tpl_notask:
        definition: "@asset_publish_area_harmony_notask/tpl/{Asset}_{step_code}_{name}_v{version}.tpl"
        root_name: 'primary'

    harmony_asset_publish_color_palette:
        definition: "@asset_publish_area_harmony/color_palettes/{Asset}_color_palette_{color_palette_identifier}_v{version}.plt"
        root_name: 'primary'

    harmony_asset_publish_color_palette_notask:
        definition: "@asset_publish_area_harmony_notask/color_palettes/{Asset}_color_palette_{color_palette_identifier}_v{version}.plt"
        root_name: 'primary'

    # The location of backups of WIP files
    # Note that snapshots folder is  not inside the wip folder
    # otherwise we could confuse the publishing process when files
    # need to be copied from work to publish.
    harmony_asset_snapshot:
        definition: '@asset_work_area_harmony/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.xstage'
        root_name: 'primary'

    harmony_asset_snapshot_notask:
        definition: '@asset_work_area_harmony_notask/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.xstage'
        root_name: 'primary'

    # The location of published files
    harmony_asset_publish:
        definition: '@asset_publish_area_harmony/scenes/{Asset}_{step_code}_{Task}_{name}_v{version}/{Asset}_{step_code}_{Task}_{name}_v{version}.xstage'
        root_name: 'primary'

    harmony_asset_publish_notask:
        definition: '@asset_publish_area_harmony_notask/scenes/{Asset}_{step_code}_{name}_v{version}/{Asset}_{step_code}_{Task}_{name}_v{version}.xstage'
        root_name: 'primary'


    #
    # Blender
    #

    # asset
    # define the location of a work area
    asset_work_area_blender:
        definition: '@work_asset_root/blender'
        root_name: 'primary'

    asset_work_area_blender_notask:
        definition: '@work_asset_root_notask/blender'
        root_name: 'primary'

    # define the location of a publish area
    asset_publish_area_blender:
        definition: '@publish_asset_root/blender'
        root_name: 'primary'

    asset_publish_area_blender_notask:
        definition: '@publish_asset_root_notask/blender'
        root_name: 'primary'

    # The location of WIP files
    blender_asset_work:
        definition: '@asset_work_area_blender/scenes/{Asset}_{step_code}_{Task}_{name}_v{version}.blend'
        root_name: 'primary'

    blender_asset_work_notask:
        definition: '@asset_work_area_blender_notask/scenes/{Asset}_{step_code}_{Task}_{name}_v{version}.blend'
        root_name: 'primary'

    # The location of backups of WIP files
    blender_asset_snapshot:
        definition: '@asset_work_area_blender/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.blend'
        root_name: 'primary'

    blender_asset_snapshot_notask:
        definition: '@asset_work_area_blender_notask/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.blend'
        root_name: 'primary'

    # The location of published files
    blender_asset_publish:
        definition: '@asset_publish_area_blender/scenes/{Asset}_{step_code}_{Task}_{name}_v{version}/{Asset}_{step_code}_{Task}_{name}_v{version}.blend'
        root_name: 'primary'

    blender_asset_publish_notask:
        definition: '@asset_publish_area_blender_notask/scenes/{Asset}_{step_code}_{name}_v{version}/{Asset}_{step_code}_{Task}_{name}_v{version}.blend'
        root_name: 'primary'



    #
    # Custom Shapes
    #

    asset_work_proxy_custom_shape_image:
        definition: '@asset_work_area_harmony/scenes/frames/proxies/{shape.piece}/{shape.piece}_{shape.frame}.png'
    asset_work_proxy_custom_shape_image_notask:
        definition: '@asset_work_area_harmony_notask/scenes/frames/proxies/{shape.piece}/{shape.piece}_{shape.frame}.png'


    asset_work_render_custom_shape_image:
        definition: '@asset_work_area_harmony/scenes/frames/render/{shape.piece}/{shape.piece}_{shape.frame}.exr'
    asset_work_render_custom_shape_image_notask:
        definition: '@asset_work_area_harmony_notask/scenes/frames/render/{shape.piece}/{shape.piece}_{shape.frame}.exr'

    #
    # Fusion
    #

    # asset based

    # define the location of a work area
    asset_work_area_fusion:
        definition: '@work_asset_root/fusion'
        root_name: 'primary'

    asset_work_area_fusion_notask:
        definition: '@work_asset_root_notask/fusion'
        root_name: 'primary'

    # define the location of a publish area
    asset_publish_area_fusion:
        definition: '@publish_asset_root/fusion'
        root_name: 'primary'

    asset_publish_area_fusion_notask:
        definition: '@publish_asset_root_notask/fusion'
        root_name: 'primary'

    # The location of WIP script files
    fusion_asset_work:
        definition: '@asset_work_area_fusion/{Asset}_{step_code}_{Task}_{name}_v{version}.comp'
        root_name: 'primary'

    fusion_asset_work_notask:
        definition: '@asset_work_area_fusion_notask/{Asset}_{step_code}_{name}_v{version}.comp'
        root_name: 'primary'

    # The location of backups of WIP files
    fusion_asset_snapshot:
        definition: '@asset_work_area_fusion/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.comp'
        root_name: 'primary'

    fusion_asset_snapshot_notask:
        definition: '@asset_work_area_fusion/snapshots/{Asset}_{step_code}_{name}_v{version}.{timestamp}.comp'
        root_name: 'primary'

    # The location of published fusion script files
    fusion_asset_publish:
        definition: '@asset_publish_area_fusion/{Asset}_{step_code}_{Task}_{name}_v{version}.comp'
        root_name: 'primary'

    fusion_asset_publish_notask:
        definition: '@asset_publish_area_fusion_notask/{Asset}_{step_code}_{name}_v{version}.comp'
        root_name: 'primary'

    # write node outputs
    fusion_asset_render_mono_dpx:
        definition: '@work_asset_root/images/{name}/v{version}/{width}x{height}/{Asset}_{step_code}_{Task}_{name}_{fusion.output}_v{version}.{SEQ}.dpx'
        root_name: 'primary'

    fusion_asset_render_mono_dpx_notask:
        definition: '@work_asset_root_notask/images/{name}/v{version}/{width}x{height}/{Asset}_{step_code}_{name}_{fusion.output}_v{version}.{SEQ}.dpx'
        root_name: 'primary'


    fusion_asset_render_pub_mono_dpx:
        definition: '@publish_asset_root/elements/{name}/v{version}/{width}x{height}/{Asset}_{step_code}_{Task}_{name}_{fusion.output}_v{version}.{SEQ}.dpx'
        root_name: 'primary'

    fusion_asset_render_pub_mono_dpx_notask:
        definition: '@publish_asset_root_notask/elements/{name}/v{version}/{width}x{height}/{Asset}_{step_code}_{name}_{fusion.output}_v{version}.{SEQ}.dpx'
        root_name: 'primary'


    fusion_asset_render_stereo:
        definition: '@work_asset_root/images/{name}/v{version}/{width}x{height}/{Asset}_{step_code}_{Task}_{name}_{fusion.output}_{eye}_v{version}.{SEQ}.exr'
        root_name: 'primary'

    fusion_asset_render_stereo_notask:
        definition: '@work_asset_root_notask/images/{name}/v{version}/{width}x{height}/{Asset}_{step_code}_{name}_{fusion.output}_{eye}_v{version}.{SEQ}.exr'
        root_name: 'primary'


    fusion_asset_render_pub_stereo:
        definition: '@publish_asset_root/elements/{name}/v{version}/{width}x{height}/{Asset}_{step_code}_{Task}_{name}_{fusion.output}_{eye}_v{version}.{SEQ}.exr'
        root_name: 'primary'

    fusion_asset_render_pub_stereo_notask:
        definition: '@publish_asset_root_notask/elements/{name}/v{version}/{width}x{height}/{Asset}_{step_code}_{name}_{fusion.output}_{eye}_v{version}.{SEQ}.exr'
        root_name: 'primary'

    # review output
    asset_quicktime_quick:
        definition: '@work_asset_root/review/quickdaily/{Asset}_{step_code}_{Task}_{name}_{iteration}.mov'
        root_name: 'primary'

    asset_quicktime_quick_notask:
        definition: '@work_asset_root_notask/review/quickdaily/{Asset}_{step_code}_{name}_{iteration}.mov'
        root_name: 'primary'

    fusion_asset_render_movie:
        definition: '@work_asset_root/review/{Asset}_{step_code}_{Task}_{name}_v{version}.mov'
        root_name: 'primary'

    fusion_asset_render_movie_notask:
        definition: '@work_asset_root_notask/review/{Asset}_{step_code}_{name}_v{version}.mov'
        root_name: 'primary'


    # ---- Krita

    # asset
    # define the location of a work area
    asset_work_area_krita:
        definition: '@work_asset_root/krita'
        root_name: 'primary'

    asset_work_area_krita_notask:
        definition: '@work_asset_root_notask/krita'
        root_name: 'primary'

    # define the location of a publish area
    asset_publish_area_krita:
        definition: '@publish_asset_root/krita'
        root_name: 'primary'

    asset_publish_area_krita_notask:
        definition: '@publish_asset_root_notask/krita'
        root_name: 'primary'

    # The location of WIP files
    krita_asset_work:
        definition: '@asset_work_area_krita/{Asset}_{step_code}_{Task}_{name}_v{version}.kra'
        root_name: 'primary'

    krita_asset_work_notask:
        definition: '@asset_work_area_krita_notask/{Asset}_{step_code}_{name}_v{version}.kra'
        root_name: 'primary'

    # The location of backups of WIP files
    krita_asset_snapshot:
        definition: '@asset_work_area_krita/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.kra'
        root_name: 'primary'

    krita_asset_snapshot_notask:
        definition: '@asset_work_area_krita/snapshots/{Asset}_{step_code}_{name}_v{version}.{timestamp}.kra'
        root_name: 'primary'

    # Location of the Krita session once published
    krita_asset_publish:
        definition: '@asset_publish_area_krita/{Asset}_{step_code}_{Task}_{name}_v{version}.kra'
        root_name: 'primary'

    krita_asset_publish_notask:
        definition: '@asset_publish_area_krita/{Asset}_{step_code}_{name}_v{version}.kra'
        root_name: 'primary'

    # These templates are related to publishing layers within Krita

    # where to export the individual layers before publishing them
    krita_asset_layer_export:
        definition: '@asset_work_area_krita/layers/v{version}/{Asset}_{step_code}_{Task}_{name}_layer_@krita_layer_name'
        root_name: 'primary'

    krita_asset_layer_export_notask:
        definition: '@asset_work_area_krita_notask/layers/v{version}/{Asset}_{step_code}_{name}_layer_@krita_layer_name'
        root_name: 'primary'

    # where to publish the individual layers
    krita_asset_layer_publish:
        definition: '@asset_publish_area_krita/layers/v{version}/{Asset}_{step_code}_{Task}_{name}_layer_@krita_layer_name'
        root_name: 'primary'

    krita_asset_layer_publish_notask:
        definition: '@asset_publish_area_krita/layers/v{version}/{Asset}_{step_code}_{name}_layer_@krita_layer_name'
        root_name: 'primary'

    # where to export layers as a folder before publishing
    krita_asset_layers_export:
        definition: '@asset_work_area_krita/layers/{Asset}_{step_code}_{Task}_{name}_krita_layers_v{version}'
        root_name: 'primary'

    krita_asset_layers_export_notask:
        definition: '@asset_work_area_krita/layers/{Asset}_{step_code}_{name}_krita_layers_v{version}'
        root_name: 'primary'

    # where to publish layers as a folder
    krita_asset_layers_publish:
        definition: '@asset_publish_area_krita/layers/{Asset}_{step_code}_{Task}_{name}_krita_layers_v{version}'
        root_name: 'primary'

    krita_asset_layers_publish_notask:
        definition: '@asset_publish_area_krita/layers/{Asset}_{step_code}_{name}_krita_layers_v{version}'
        root_name: 'primary'

    #
    # Substance Painter
    #


    # asset
    # define the location of a work area
    asset_work_area_substancepainter:
        definition: '@work_asset_root/substancepainter'
        root_name: 'primary'

    asset_work_area_substancepainter_notask:
        definition: '@work_asset_root_notask/substancepainter'
        root_name: 'primary'

    # define the location of a publish area
    asset_publish_area_substancepainter:
        definition: '@publish_asset_root/substancepainter'
        root_name: 'primary'

    asset_publish_area_substancepainter_notask:
        definition: '@publish_asset_root_notask/substancepainter'
        root_name: 'primary'

    # The location of WIP files
    substancepainter_asset_work:
        definition: '@asset_work_area_substancepainter/{Asset}_{step_code}_{Task}_{name}_v{version}.spp'
        root_name: 'primary'

    substancepainter_asset_work_notask:
        definition: '@asset_work_area_substancepainter_notask/{Asset}_{step_code}_{name}_v{version}.spp'
        root_name: 'primary'

    # The location of backups of WIP files
    substancepainter_asset_snapshot:
        definition: '@asset_work_area_substancepainter/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.spp'
        root_name: 'primary'

    substancepainter_asset_snapshot_notask:
        definition: '@asset_work_area_substancepainter_notask/snapshots/{Asset}_{step_code}_{name}_v{version}.{timestamp}.spp'
        root_name: 'primary'


    # The folder where the textures are exported for a project
    substancepainter_asset_textures_path_export:
        definition: '@asset_work_area_substancepainter/export'
        root_name: 'primary'

    substancepainter_asset_textures_path_export_notask:
        definition: '@asset_work_area_substancepainter_notask/export'
        root_name: 'primary'

    # The location of published substance painter files
    substancepainter_asset_publish:
        definition: '@asset_publish_area_substancepainter/{Asset}_{step_code}_{Task}_{name}_v{version}.spp'
        root_name: 'primary'

    substancepainter_asset_publish_notask:
        definition: '@asset_publish_area_substancepainter_notask/{Asset}_{step_code}_{name}_v{version}.spp'
        root_name: 'primary'

    # a texture folder publish
    substancepainter_asset_textures_path_publish:
        definition: '@asset_publish_area_substancepainter/textures/{Asset}_{step_code}_{Task}_{name}_textures_v{version}'
        root_name: 'primary'

    substancepainter_asset_textures_path_publish_notask:
        definition: '@asset_publish_area_substancepainter_notask/textures/{Asset}_{step_code}_{name}_textures_v{version}'
        root_name: 'primary'

    # a texture udim sequence
    substancepainter_asset_texture_path_publish:
        definition: '@asset_publish_area_substancepainter/textures/v{version}/{texture_name}/{Asset}_{Task}_{name}_{texture_name}_v{version}[.{UDIM}].{texture_extension}'
        root_name: 'primary'

    substancepainter_asset_texture_path_publish_notask:
        definition: '@asset_publish_area_substancepainter_notask/textures/v{version}/{texture_name}/{Asset}_{name}_{texture_name}_v{version}[.{UDIM}].{texture_extension}'
        root_name: 'primary'

    ##########################################################################################
    # Env pipeline


    #
    # Alembic caches
    #

    environment_alembic_cache:
        definition: '@publish_environment_root/alembics/{Asset}_{step_code}_{Task}_alembic_{name}_v{version}.abc'

    environment_gpu_cache:
        definition: '@publish_environment_root/caches/{Asset}_{step_code}_{Task}_gpu_{name}_v{version}.abc'

    environment_dynamic_attrs_alembic:
        definition: '@publish_environment_root/alembics/dynAttrs/{Asset}_dynAttrs_{step_code}_{Task}_{name}_v{version}.abc'


    #
    # USDs
    #

    environment_usdc_cache:
        definition: '@publish_environment_usd_root/{name}/geo/{Asset}_{name}_v{version}.usd'

    environment_usda_cache:
        definition: '@publish_environment_usd_root/{name}/edits/{Asset}_{name}_v{version}.usda'

    environment_usdm_cache:
        definition: '@publish_environment_usd_root/{name}/materials/{Asset}_{name}_v{version}.usda'


    #
    # Structure Abstractions
    #
    environment_structure_abstraction:
        definition: '@publish_environment_root/abstractions/{Asset}_{step_code}_{Task}_abstraction_{name}_v{version}.json'

    #
    # Review Movies
    #

    environment_review_publish:
        definition: '@publish_environment_root/review/{Asset}_{step_code}_{Task}_{name}_v{version}.mov'

    #
    # Review Sequences
    #

    environment_review_sequence_publish:
        definition: '@publish_environment_root/review/{Asset}_{step_code}_{Task}_{name}_v{version}/{Asset}_{step_code}_{Task}_{name}_v{version}.{SEQ}.png'


    #
    # Custom Shapes
    #


    environment_publish_proxy_custom_shape_image:
        definition: '@publish_environment_root/shapes/proxies/{shape.piece}/version/v{version}/{Asset}_{step_code}_{Task}_{shape.piece}_v{version}_{shape.frame}.png'

    environment_publish_render_custom_shape_image:
        definition: '@publish_environment_root/shapes/render/{shape.piece}/version/v{version}/{Asset}_{step_code}_{Task}_{shape.piece}_v{version}_{shape.frame}.exr'

    environment_publish_proxy_custom_shape_library_image:
        definition: '@publish_environment_root/shapes/proxies/{shape.piece}/library/{Asset}_{step_code}_{Task}_{shape.piece}_{shape.frame.number}.png'

    environment_publish_render_custom_shape_library_image:
        definition: '@publish_environment_root/shapes/render/{shape.piece}/library/{Asset}_{step_code}_{Task}_{shape.piece}_{shape.frame.number}.exr'


    #
    # Photoshop
    #

    # The location of WIP files
    photoshop_environment_work:
        definition: '@work_environment_root/photoshop/{Asset}_{step_code}_{Task}_{name}_v{version}.{photoshop_extension}'
    environment_work_area_photoshop:
        definition: '@work_environment_root/photoshop'
    # The location of backups of WIP files
    photoshop_environment_snapshot:
        definition: '@work_environment_root/photoshop/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.{photoshop_extension}'
    # The location of published files
    environment_publish_area_photoshop:
        definition: '@publish_environment_root/photoshop'
    photoshop_environment_publish:
        definition: '@publish_environment_root/photoshop/{Asset}_{step_code}_{Task}_{name}_v{version}.{photoshop_extension}'

    # where to export the individual layers before publishing them
    photoshop_environment_layer_export:
        definition: '@environment_work_area_photoshop/layers/v{version}/{Asset}_{step_code}_{Task}_{name}_layer_@photoshop_layer_name'
        root_name: 'primary'

    # where to publish the individual layers
    photoshop_environment_layer_publish:
        definition: '@environment_publish_area_photoshop/layers/v{version}/{Asset}_{step_code}_{Task}_{name}_layer_@photoshop_layer_name'
        root_name: 'primary'


    #
    # Illustrator
    #

    # The location of WIP files
    illustrator_environment_work:
        definition: '@work_environment_root/illustrator/{Asset}_{step_code}_{Task}_{name}_v{version}.ai'
    environment_work_area_illustrator:
        definition: '@work_environment_root/illustrator'
    # The location of backups of WIP files
    illustrator_environment_snapshot:
        definition: '@work_environment_root/illustrator/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.ai'
    # The location of published files
    environment_publish_area_illustrator:
        definition: '@publish_environment_root/illustrator'
    illustrator_environment_publish:
        definition: '@publish_environment_root/illustrator/{Asset}_{step_code}_{Task}_{name}_v{version}.ai'

    # # where to export the individual layers before publishing them
    # illustrator_environment_layer_export:
    #     definition: '@environment_work_area_illustrator/layers/v{version}/{Asset}_{step_code}_{Task}_{name}_layer_@illustrator_layer_name'
    #     root_name: 'primary'

    # # where to publish the individual layers
    # illustrator_environment_layer_publish:
    #     definition: '@environment_publish_area_illustrator/layers/v{version}/{Asset}_{step_code}_{Task}_{name}_layer_@illustrator_layer_name'
    #     root_name: 'primary'


    #
    # after effects
    #

    # The location of WIP files
    aftereffects_environment_work:
        definition: '@work_environment_root/afx/{Asset}_{step_code}_{Task}_{name}_v{version}.aep'
    environment_work_area_aftereffects:
        definition: '@work_environment_root/afx'
    # The location of backups of WIP files
    aftereffects_environment_snapshot:
        definition: '@work_environment_root/afx/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.aep'
    # The location of published files
    environment_publish_area_aftereffects:
        definition: '@publish_environment_root/afx'
    aftereffects_environment_publish:
        definition: '@publish_environment_root/afx/{Asset}_{step_code}_{Task}_{name}_v{version}.aep'
    aftereffects_environment_render_pub_mono:
        # TODO: use dynamic extension using choices defined for afx.seq.ext
        # definition: '@publish_environment_root/afx/render/{name}_v{version}/{afx.comp}/{name}_{afx.comp}_v{version}.{SEQ}.{afx.seq.ext}'
        definition: '@publish_environment_root/afx/render/{name}_v{version}/{afx.comp}/{name}_{afx.comp}_v{version}.{SEQ}.png'
    # The following template uses {afx.mov.ext} this is a special key, that will be only there
    # in the beta to support different extensions on mac and windows, while using the same
    # output module (Lossless with Alpha)
    aftereffects_environment_render_movie:
        # TODO: use dynamic extension using choices defined for afx.mov.ext
        # definition: '@publish_environment_root/review/{Asset}_{step_code}_{name}_{afx.comp}_v{version}.{afx.mov.ext}'
        definition: '@publish_environment_root/review/{Asset}_{step_code}_{name}_{afx.comp}_v{version}.mov'


    #
    # Mari
    #
    environment_mari_texture_tif:
        definition: '@publish_environment_root/mari/{Asset}_{step_code}_{Task}_{name}_{mari.channel}[_{mari.layer}]_v{version}.{UDIM}.tif'

    #
    # Maya
    #

    # define the location of a work area
    environment_work_area_maya:
        definition: '@work_environment_root/maya'
    # define the location of a publish area
    environment_publish_area_maya:
        definition: '@publish_environment_root/maya'
    # The location of WIP files
    maya_environment_work:
        definition: '@environment_work_area_maya/{Asset}_{step_code}_{Task}_{name}_v{version}.{maya_extension}'
    # The location of backups of WIP files
    maya_environment_snapshot:
        definition: '@environment_work_area_maya/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.{maya_extension}'
    # The location of published maya files
    maya_environment_publish:
        definition: '@environment_publish_area_maya/{Asset}_{step_code}_{Task}_{name}_v{version}.{maya_extension}'
    # The location of publish 3d objets
    3d_mesh_environment_publish:
        definition: '@environment_publish_area_maya/meshes/{Asset}_{step_code}_{Task}_{name}/v{version}/{Asset}_{step_code}_{Task}_{name}_v{version}.{3d_mesh_extension}'

    # NOTASK

    # define the location of a work area
    environment_work_area_maya_notask:
        definition: '@work_environment_root_notask/maya'
    # define the location of a publish area
    environment_publish_area_maya_notask:
        definition: '@publish_environment_root_notask/maya'
    # The location of WIP files
    maya_environment_work_notask:
        definition: '@environment_work_area_maya_notask/{Asset}_{step_code}_{name}_v{version}.{maya_extension}'
    # The location of backups of WIP files
    maya_environment_snapshot_notask:
        definition: '@environment_work_area_maya_notask/snapshots/{Asset}_{step_code}_{name}_v{version}.{timestamp}.{maya_extension}'
    # The location of published maya files
    maya_environment_publish_notask:
        definition: '@environment_publish_area_maya_notask/{Asset}_{step_code}_{name}_v{version}.{maya_extension}'

    3d_mesh_environment_publish_notask:
        definition: '@environment_publish_area_maya_notask/meshes/{Asset}_{step_code}_{name}/v{version}/{Asset}_{step_code}_{name}_v{version}.{3d_mesh_extension}'


    #
    # Houdini
    #

    # define the location of a work area
    environment_work_area_houdini:
        definition: '@work_environment_root/houdini'
    # define the location of a publish area
    environment_publish_area_houdini:
        definition: '@publish_environment_root/houdini'
    # The location of WIP files
    houdini_environment_work:
        definition: '@work_environment_root/houdini/{Asset}_{step_code}_{Task}_{name}_v{version}.hip'
    # The location of backups of WIP files
    houdini_environment_snapshot:
        definition: '@work_environment_root/houdini/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.hip'
    # The location of published houdini files
    houdini_environment_publish:
        definition: '@publish_environment_root/houdini/{Asset}_{step_code}_{Task}_{name}_v{version}.hip'
    # Alembic caches
    houdini_environment_work_alembic_cache:
        definition: '@work_environment_root/houdini/cache/alembic/{Asset}_{step_code}_{Task}_{name}/{houdini.node}/v{version}/{Asset}_{step_code}_{Task}_{name}_v{version}.abc'

    # Rendered images
    houdini_environment_render:
        definition: '@work_environment_root/images/{Asset}_{step_code}_{Task}_{name}/{houdini.node}/v{version}/{width}x{height}/{Asset}_{step_code}_{Task}_{name}_v{version}.{SEQ}.exr'

    # Additional mantra outputs
    houdini_environment_ifd:
        definition: '@work_environment_root/ifds/{Asset}_{step_code}_{Task}_{name}/{houdini.node}/v{version}/{width}x{height}/{Asset}_{step_code}_{Task}_{name}_v{version}.{SEQ}.ifd'

    houdini_environment_dcm:
        definition: '@work_environment_root/dcms/{Asset}_{step_code}_{Task}_{name}/{houdini.node}/v{version}/{width}x{height}/{Asset}_{step_code}_{Task}_{name}_v{version}.{SEQ}.dcm'

    houdini_environment_extra_plane:
        definition: '@work_environment_root/images/{Asset}_{step_code}_{Task}_{name}/{houdini.node}/{aov_name}/v{version}/{width}x{height}/{Asset}_{step_code}_{Task}_{name}_v{version}.{SEQ}.exr'


    #
    # 3dsmax
    #

    # define the location of a work area
    environment_work_area_max:
        definition: '@work_environment_root/3dsmax'
    # define the location of a publish area
    environment_publish_area_max:
        definition: '@publish_environment_root/3dsmax'
    # The location of WIP files
    max_environment_work:
        definition: '@work_environment_root/3dsmax/{Asset}_{step_code}_{Task}_{name}_v{version}.max'
    # The location of backups of WIP files
    max_environment_snapshot:
        definition: '@work_environment_root/3dsmax/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.max'
    # The location of published max files
    max_environment_publish:
        definition: '@publish_environment_root/3dsmax/{Asset}_{step_code}_{Task}_{name}_v{version}.max'


    #
    # Motionbuilder
    #

    # define the location of a work area
    environment_work_area_mobu:
        definition: '@work_environment_root/mobu'
    # define the location of a publish area
    environment_publish_area_mobu:
        definition: '@publish_environment_root/mobu'
    # The location of WIP files
    mobu_environment_work:
        definition: '@work_environment_root/mobu/{Asset}_{step_code}_{Task}_{name}_v{version}.fbx'
    # The location of backups of WIP files
    mobu_environment_snapshot:
        definition: '@work_environment_root/mobu/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.fbx'
    # The location of published Motionbuilder files
    mobu_environment_publish:
        definition: '@publish_environment_root/mobu/{Asset}_{step_code}_{Task}_{name}_v{version}.fbx'


    #
    # Nuke
    #

    # define the location of a work area
    environment_work_area_nuke:
        definition: '@work_environment_root/nuke'
    # define the location of a publish area
    environment_publish_area_nuke:
        definition: '@publish_environment_root/nuke'
    # outputs from the Shotgun Write Node for assets
    nuke_environment_render:
        definition: '@work_environment_root/images/{Asset}_{step_code}_{Task}_{name}/v{version}/{width}x{height}/{Asset}_{step_code}_{Task}_{name}_{nuke.output}_v{version}.{SEQ}.exr'
    nuke_environment_render_pub:
        definition: '@publish_environment_root/elements/{Asset}_{step_code}_{Task}_{name}/v{version}/{width}x{height}/{Asset}_{step_code}_{Task}_{name}_{nuke.output}_v{version}.{SEQ}.exr'
    # review output
    nuke_environment_render_movie:
        definition: '@work_environment_root/review/{Asset}_{step_code}_{Task}_{name}_{nuke.output}_v{version}.mov'
    environment_quicktime_quick:
        definition: '@work_environment_root/review/quickdaily/{Asset}_{step_code}_{Task}_{name}_{iteration}.mov'
    # The location of WIP script files
    nuke_environment_work:
        definition: '@work_environment_root/nuke/{Asset}_{step_code}_{Task}_{name}_v{version}.nk'
    # The location of backups of WIP files
    nuke_environment_snapshot:
        definition: '@work_environment_root/nuke/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.nk'
    # The location of published nuke script files
    nuke_environment_publish:
        definition: '@publish_environment_root/nuke/{Asset}_{step_code}_{Task}_{name}_v{version}.nk'

    #
    # Alias
    #

    # define the location of a work area
    environment_work_area_alias:
        definition: '@work_environment_root/alias'
    # define the location of a publish area
    environment_publish_area_alias:
        definition: '@publish_environment_root/alias'
    # The location of WIP files
    alias_environment_work:
        definition: '@work_environment_root/alias/{Asset}_{step_code}_{Task}_{name}_v{version}.wire'
    # The location of backups of WIP files
    alias_environment_snapshot:
        definition: '@work_environment_root/alias/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.wire'
    # The location of published Alias files
    alias_environment_publish:
        definition: '@publish_environment_root/alias/{Asset}_{step_code}_{Task}_{name}_v{version}.wire'

    # Alias translations

    alias_environment_igs_publish:
        definition: '@publish_environment_root/alias/translations/{Asset}_{step_code}_{Task}_{name}_v{version}.igs'
    alias_environment_catpart_publish:
        definition: '@publish_environment_root/alias/translations/{Asset}_{step_code}_{Task}_{name}_v{version}.CATPart'
    alias_environment_jt_publish:
        definition: '@publish_environment_root/alias/translations/{Asset}_{step_code}_{Task}_{name}_v{version}.jt'
    alias_environment_stp_publish:
        definition: '@publish_environment_root/alias/translations/{Asset}_{step_code}_{Task}_{name}_v{version}.stp'
    alias_environment_wref_publish:
        definition: '@publish_environment_root/alias/translations/{Asset}_{step_code}_{Task}_{name}_v{version}.wref'

    #
    # VRED
    #

    # define the location of a work area
    environment_work_area_vred:
        definition: '@work_environment_root/vred'
    # define the location of a publish area
    environment_publish_area_vred:
        definition: '@publish_environment_root/vred'
    # The location of WIP files
    vred_environment_work:
        definition: '@work_environment_root/vred/{Asset}_{step_code}_{Task}_{name}_v{version}.{vred.extension}'
    # The location of backups of WIP files
    vred_environment_snapshot:
        definition: '@work_environment_root/vred/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.{vred.extension}'
    # The location of published VRED files
    vred_environment_publish:
        definition: '@publish_environment_root/vred/{Asset}_{step_code}_{Task}_{name}_v{version}.{vred.extension}'

    # define the location of VRED geometry published files
    vred_environment_geometry_publish:
        definition: '@publish_environment_root/vred/geometry/{Asset}_{step_code}_{Task}_{name}_v{version}.osb'

    # define the location of the WIP render images
    vred_environment_render_work:
        definition: '@work_environment_root/images/{name}/v{version}/{Asset}_{step_code}_{Task}_{name}_v{version}[-{vred.render_pass}].{vred.render_extension}'

    # define the location of the WIP renderings for an image sequence
    vred_environment_render_sequence_work:
        definition: '@work_environment_root/images/{name}/v{version}/{Asset}_{step_code}_{Task}_{name}_v{version}[-{vred.render_pass}]-{vred.frame}.{vred.render_extension}'

    # define the location of the published render images
    vred_environment_render_publish:
        definition: '@publish_environment_root/images/{name}/v{version}/{Asset}_{step_code}_{Task}_{name}_v{version}[-{vred.render_pass}].{vred.render_extension}'

    # define the location of the published renderings for an image sequence
    vred_environment_render_sequence_publish:
        definition: '@publish_environment_root/images/{name}/v{version}/{Asset}_{step_code}_{Task}_{name}_v{version}[-{vred.render_pass}]-{vred.frame}.{vred.render_extension}'


    #
    # Harmony
    #

    # asset
    # define the location of a work area
    environment_work_area_harmony:
        definition: '@work_environment_root/harmony'
        root_name: 'primary'

    # define the location of a publish area
    environment_publish_area_harmony:
        definition: '@publish_environment_root/harmony'
        root_name: 'primary'

    # The location of WIP files
    harmony_environment_work:
        definition: '@environment_work_area_harmony/scenes/{Asset}_{step_code}_{Task}_{name}_v{version}.xstage'
        root_name: 'primary'

    harmony_environment_work_face_reference:
        definition: '@environment_work_area_harmony/scenes/frames/References/Face_Reference_{shape.frame}.png'
        root_name: 'primary'

    harmony_environment_publish_face_reference:
        definition: '@environment_publish_area_harmony/scenes/{Asset}_{step_code}_{Task}_{name}_v{version}/frames/References/Face_Reference_{shape.frame}.png'
        root_name: 'primary'

    harmony_environment_work_sequence_png:
        definition: "@environment_work_area_harmony/scenes/frames/{tokenName}.{frame}.png"
        root_name: 'primary'

    harmony_environment_publish_sequence_png:
        definition: "@environment_publish_area_harmony/scenes/{Asset}_{step_code}_{Task}/v{version}/frames/{Asset}_{name}.{frame}.png"
        root_name: 'primary'

    harmony_environment_work_tpl:
        definition: "@environment_work_area_harmony/pipelineLibrary/{tokenName}.tpl"
        root_name: 'primary'

    harmony_environment_publish_tpl:
        definition: "@environment_publish_area_harmony/tpl/{Asset}_{step_code}_{Task}_{name}_v{version}.tpl"
        root_name: 'primary'


    # The location of backups of WIP files
    # Note that snapshots folder is  not inside the wip folder
    # otherwise we could confuse the publishing process when files
    # need to be copied from work to publish.
    harmony_environment_snapshot:
        definition: '@environment_work_area_harmony/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.xstage'
        root_name: 'primary'

    harmony_environment_publish:
        definition: '@environment_publish_area_harmony/scenes/{Asset}_{step_code}_{Task}_{name}_v{version}/{Asset}_{step_code}_{Task}_{name}_v{version}.xstage'
        root_name: 'primary'

    #
    # Custom Shapes
    #

    environment_work_proxy_custom_shape_image:
        definition: '@environment_work_area_harmony/scenes/frames/proxies/{shape.piece}/{shape.piece}_{shape.frame}.png'

    environment_work_render_custom_shape_image:
        definition: '@environment_work_area_harmony/scenes/frames/render/{shape.piece}/{shape.piece}_{shape.frame}.exr'


    #
    # Blender
    #

    # define the location of a work area
    environment_work_area_blender:
        definition: '@work_environment_root/blender'
    # define the location of a publish area
    environment_publish_area_blender:
        definition: '@publish_environment_root/blender'
    # The location of WIP files
    blender_environment_work:
        definition: '@environment_work_area_blender/{Asset}_{step_code}_{Task}_{name}_v{version}.blend'
    # The location of backups of WIP files
    blender_environment_snapshot:
        definition: '@environment_work_area_blender/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.blend'
    # The location of published blender files
    blender_environment_publish:
        definition: '@environment_publish_area_blender/{Asset}_{step_code}_{Task}_{name}_v{version}.blend'
    # The location of publish 3d objets
    3d_mesh_environment_publish:
        definition: '@environment_publish_area_blender/meshes/{Asset}_{step_code}_{Task}_{name}/v{version}/{Asset}_{step_code}_{Task}_{name}_v{version}.{3d_mesh_extension}'

    # Blender NoTask

    # define the location of a work area
    environment_work_area_blender_notask:
        definition: '@work_environment_root_notask/blender'
    # define the location of a publish area
    environment_publish_area_blender_notask:
        definition: '@publish_environment_root_notask/blender'
    # The location of WIP files
    blender_environment_work_notask:
        definition: '@environment_work_area_blender_notask/{Asset}_{step_code}_{name}_v{version}.blend'
    # The location of backups of WIP files
    blender_environment_snapshot_notask:
        definition: '@environment_work_area_blender_notask/snapshots/{Asset}_{step_code}_{name}_v{version}.{timestamp}.blend'
    # The location of published blender files
    blender_environment_publish_notask:
        definition: '@environment_publish_area_blender_notask/{Asset}_{step_code}_{name}_v{version}.blend'




    #
    # Fusion
    #

    # asset based

    # define the location of a work area
    environment_work_area_fusion:
        definition: '@work_environment_root/fusion'
        root_name: 'primary'

    # define the location of a publish area
    environment_publish_area_fusion:
        definition: '@publish_environment_root/fusion'
        root_name: 'primary'

    # The location of WIP script files
    fusion_environment_work:
        definition: '@work_environment_root/fusion/{name}_v{version}.comp'
        root_name: 'primary'

    # The location of backups of WIP files
    fusion_environment_snapshot:
        definition: '@work_environment_root/fusion/snapshots/{name}_v{version}.{timestamp}.comp'
        root_name: 'primary'

    # The location of published fusion script files
    fusion_environment_publish:
        definition: '@publish_environment_root/fusion/{name}_v{version}.comp'
        root_name: 'primary'

    # write node outputs
    fusion_environment_render_mono_dpx:
        definition: '@work_environment_root/images/{name}/v{version}/{width}x{height}/{Shot}_{name}_{fusion.output}_v{version}.{SEQ}.dpx'
        root_name: 'primary'

    fusion_environment_render_pub_mono_dpx:
        definition: '@publish_environment_root/elements/{name}/v{version}/{width}x{height}/{Shot}_{name}_{fusion.output}_v{version}.{SEQ}.dpx'
        root_name: 'primary'


    fusion_environment_render_stereo:
        definition: '@work_environment_root/images/{name}/v{version}/{width}x{height}/{Shot}_{name}_{fusion.output}_{eye}_v{version}.{SEQ}.exr'
        root_name: 'primary'

    fusion_environment_render_pub_stereo:
        definition: '@publish_environment_root/elements/{name}/v{version}/{width}x{height}/{Shot}_{name}_{fusion.output}_{eye}_v{version}.{SEQ}.exr'
        root_name: 'primary'

    # review output
    environment_quicktime_quick:
        definition: '@work_environment_root/review/quickdaily/{Shot}_{name}_{iteration}.mov'
        root_name: 'primary'

    fusion_environment_render_movie:
        definition: '@work_environment_root/review/{Shot}_{name}_v{version}.mov'
        root_name: 'primary'


    # ---- Krita

    # asset
    # define the location of a work area
    environment_work_area_krita:
        definition: '@work_environment_root/krita'
        root_name: 'primary'

    # define the location of a publish area
    environment_publish_area_krita:
        definition: '@publish_environment_root/krita'
        root_name: 'primary'

    # The location of WIP files
    krita_environment_work:
        definition: '@environment_work_area_krita/{Asset}_{step_code}_{Task}_{name}_v{version}.kra'
        root_name: 'primary'

    # The location of backups of WIP files
    krita_environment_snapshot:
        definition: '@environment_work_area_krita/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.kra'
        root_name: 'primary'

    # Location of the Krita session once published
    krita_environment_publish:
        definition: '@environment_publish_area_krita/{Asset}_{step_code}_{Task}_{name}_v{version}.kra'
        root_name: 'primary'

    # These templates are related to publishing layers within Krita

    # where to export the individual layers before publishing them
    krita_environment_layer_export:
        definition: '@environment_work_area_krita/layers/v{version}/{Asset}_{step_code}_{Task}_{name}_layer_@krita_layer_name'
        root_name: 'primary'

    # where to publish the individual layers
    krita_environment_layer_publish:
        definition: '@environment_publish_area_krita/layers/v{version}/{Asset}_{step_code}_{Task}_{name}_layer_@krita_layer_name'
        root_name: 'primary'

    # where to export layers as a folder before publishing
    krita_environment_layers_export:
        definition: '@environment_work_area_krita/layers/{Asset}_{step_code}_{Task}_{name}_krita_layers_v{version}'
        root_name: 'primary'

    # where to publish layers as a folder
    krita_environment_layers_publish:
        definition: '@environment_publish_area_krita/layers/{Asset}_{step_code}_{Task}_{name}_krita_layers_v{version}'
        root_name: 'primary'


    #
    # Substance Painter
    #


    # asset
    # define the location of a work area
    environment_work_area_substancepainter:
        definition: '@work_environment_root/substancepainter'
        root_name: 'primary'
    # define the location of a publish area
    environment_publish_area_substancepainter:
        definition: '@publish_environment_root/substancepainter'
        root_name: 'primary'

    # The location of WIP files
    substancepainter_environment_work:
        definition: '@environment_work_area_substancepainter/{Asset}_{step_code}_{Task}_{name}_v{version}.spp'
        root_name: 'primary'

    # The location of backups of WIP files
    substancepainter_environment_snapshot:
        definition: '@environment_work_area_substancepainter/snapshots/{Asset}_{step_code}_{Task}_{name}_v{version}.{timestamp}.spp'
        root_name: 'primary'

    # The folder where the textures are exported for a project
    substancepainter_environment_textures_path_export:
        definition: '@environment_work_area_substancepainter/export'
        root_name: 'primary'

    # The location of published substance painter files
    substancepainter_environment_publish:
        definition: '@environment_publish_area_substancepainter/{Asset}_{step_code}_{Task}_{name}_v{version}.spp'
        root_name: 'primary'

    # a texture folder publish
    substancepainter_environment_textures_path_publish:
        definition: '@environment_publish_area_substancepainter/textures/{Asset}_{step_code}_{Task}_{name}_textures_v{version}'
        root_name: 'primary'

    # a texture folder publish
    substancepainter_environment_texture_path_publish:
        definition: '@environment_publish_area_substancepainter/textures/{Asset}_{step_code}_{Task}_{name}_{texture_name}_v{version}.{texture_extension}'
        root_name: 'primary'




#
# The strings section is similar to the paths section - but rather than defining paths
# on disk, it contains a list of strings. Strings are typically used when you want to be
# able to configure the way data is written to shotgun - it may be the name field for a
# review version or the formatting of a publish.
#

strings:

    # when a review Version in Shotgun is created inside of Nuke, this is the
    # name that is being given to it (the code field)
    nuke_shot_version_name: "{Shot}_{name}_{nuke.output}_v{version}.{iteration}"
    nuke_quick_shot_version_name: "{Shot}_{name}_quick_{iteration}"

    nuke_asset_version_name: "{Asset}_{step_code}_{Task}_{name}_{nuke.output}_v{version}.{iteration}"
    nuke_quick_asset_version_name: "{Asset}_{step_code}_{Task}_{name}_quick_{iteration}"

    # defines how the {tk_version} token in Hiero gets formatted back to tk.
    hiero_version: "{version}"

    # define how new Mari projects should be named
    mari_asset_project_name: "{mari.project_name} - Asset {asset_name}, {task_name}"

    # ---- Krita
    # represents the name of a layer file
    krita_layer_name: "{krita.layer_name}_v{version}.{krita.image_extension}"

    # ---- Photoshop
    # represents the name of a layer file
    photoshop_layer_name: "{photoshop.layer_name}_v{version}.{photoshop.image_extension}"
    photoshop_layer_name_proxy: "{photoshop.layer_name}_proxy_v{version}.{photoshop.image_extension}"

    # # ---- Illustrator
    # # represents the name of a layer file
    # illustrator_layer_name: "{illustrator.layer_name}_v{version}.{illustrator.image_extension}"
    # illustrator_layer_name_proxy: "{illustrator.layer_name}_proxy_v{version}.{illustrator.image_extension}"
