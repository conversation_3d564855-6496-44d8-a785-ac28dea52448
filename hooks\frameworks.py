################################################################################
#
# Copyright (c) 2023 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
################################################################################

import sgtk

from pprint import pformat

HookBaseClass = sgtk.get_hook_baseclass()


class Frameworks(HookBaseClass):
    def collect(self, avoid_frameworks=[]):
        """
        A hook to collect the different studio frameworks in a single dict

        """

        frameworks = {}

        base_frameworks = [
            "mty-framework-metasync",
            "mty-framework-ffmpeg",
            "mty-framework-mediatools",
            "mty-framework-presto",
            "mty-framework-ftp",
            "mty-framework-valueoverrides",
            "mty-framework-login",
            "mty-framework-timelogs",
            "mty-framework-imagemagick",
            "mty-framework-opencolorio",
            "mty-framework-openimageio",
            "mty-framework-openexr",
            "mty-framework-validations",
            "mty-framework-deadline",
            "mty-framework-credentials",
            "mty-framework-deliveries"
        ]

        for framework in base_frameworks:
            self.logger.info("loadframework = {}".format(framework))
            if framework not in avoid_frameworks:
                frameworks[framework] = self.load_framework(framework)

        return frameworks
