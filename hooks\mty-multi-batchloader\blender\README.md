# Blender Batchloader Integration with Loader Actions

Este directorio contiene los hooks del batchloader para Blender que reutilizan las acciones del tk-multi-loader2 usando el patrón `execute_hook_expression`.

## Configuración

La configuración del batchloader para Blender está en `env/includes/settings/mty-multi-batchloader.yml`:


## Patrón de Reutilización

Cada hook de proceso usa el siguiente patrón para reutilizar las acciones del loader:

```python
# Obtener la configuración del hook de acciones
hook_expression = self.parent.get_setting('actions_hook')
actions_method = self.parent.get_setting('actions_method', 'execute_action')

# Ejecutar la acción del loader
self.parent.execute_hook_expression(
    hook_expression,
    actions_method,
    name='action_name',  # Nombre de la acción del loader
    params=None,
    sg_publish_data=sg_data,
    path=file_path
)
```

## Archivos Incluidos

### Hooks de Proceso (Process Hooks)
- `batchload_process_audios.py` - Procesa archivos de audio usando la acción `add_audio`
- `batchload_process_textures.py` - Procesa texturas usando la acción `add_texture`
- `batchload_process_blend_files.py` - Procesa archivos Blend usando las acciones `link`, `append` o `import`
- `batchload_process_shot_camera_alembic_cache.py` - Procesa cámaras Alembic usando la acción `add_camera`

### Hooks de Escaneo (Scan Hooks)
- `../multi/batchload_scan_audios.py` - Escanea archivos de audio disponibles
- `../multi/batchload_scan_textures.py` - Escanea texturas disponibles
- `../multi/batchload_scan_blend_files.py` - Escanea archivos Blend disponibles
- `../multi/batchload_scan_shot_camera_alembic_cache.py` - Escanea cámaras Alembic disponibles

## Acciones Disponibles

Las siguientes acciones del tk-blender_actions.py están disponibles para el batchloader:

- `add_audio` - Añade audio al timeline de animación
- `add_texture` - Crea material con textura y lo asigna al objeto seleccionado
- `add_camera` - Importa cámara desde archivo Alembic
- `import` - Importa archivos usando el importador apropiado
- `link` - Enlaza archivos Blend como biblioteca
- `append` - Añade contenido de archivos Blend a la escena

## Ventajas de este Enfoque

1. **Reutilización de Código**: No duplicamos la lógica de carga entre loader y batchloader
2. **Consistencia**: Ambas aplicaciones usan exactamente la misma lógica de carga
3. **Mantenimiento**: Solo necesitamos actualizar las acciones en un lugar
4. **Funcionalidad Completa**: El batchloader tiene acceso a todas las características del loader
5. **Configuración Centralizada**: Los mapeos de color, configuraciones de proyecto, etc. se manejan en un solo lugar

## Uso

1. Abrir Blender en un contexto de Shot
2. Ejecutar el Batchloader desde el menú de ShotGrid
3. Seleccionar los tipos de archivos a cargar (audios, texturas, blend files, shot cameras, etc.)
4. El batchloader escaneará automáticamente los archivos disponibles
5. Seleccionar los archivos deseados y ejecutar la carga
6. Los archivos se cargarán usando las mismas acciones que el loader individual
