# Blender Batchloader Integration with Loader Actions

This directory contains the batchloader hooks for <PERSON><PERSON><PERSON> that reuse tk-multi-loader2 actions using the `execute_hook_expression` pattern.

## Configuration

The batchloader configuration for <PERSON>lender is in `env/includes/settings/mty-multi-batchloader.yml`:

## Reuse Pattern

Each process hook uses the following pattern to reuse loader actions:

```python
# Get the actions hook configuration
hook_expression = self.parent.get_setting('actions_hook')
actions_method = self.parent.get_setting('actions_method', 'execute_action')

# Execute the loader action
self.parent.execute_hook_expression(
    hook_expression,
    actions_method,
    name='action_name',  # Name of the loader action
    params=None,
    sg_publish_data=sg_data,
    path=file_path
)
```

## Included Files

### Process Hooks
- `batchload_process_audios.py` - Processes audio files using the `add_audio` action
- `batchload_process_textures.py` - Processes textures using the `add_texture` action
- `batchload_process_blend_files.py` - Processes Blend files using `link`, `append` or `import` actions
- `batchload_process_shot_camera_alembic_cache.py` - Processes Alembic cameras using the `add_camera` action

### Scan Hooks
- `../multi/batchload_scan_audios.py` - Scans for available audio files
- `../multi/batchload_scan_textures.py` - Scans for available textures
- `../multi/batchload_scan_blend_files.py` - Scans for available Blend files
- `../multi/batchload_scan_shot_camera_alembic_cache.py` - Scans for available Alembic cameras

## Available Actions

The following actions from tk-blender_actions.py are available for the batchloader:

- `add_audio` - Adds audio to the animation timeline
- `add_texture` - Creates material with texture and assigns it to selected object
- `add_camera` - Imports camera from Alembic file
- `import` - Imports files using the appropriate importer
- `link` - Links Blend files as library
- `append` - Adds content from Blend files to the scene

## Advantages of this Approach

1. **Code Reuse**: No duplication of loading logic between loader and batchloader
2. **Consistency**: Both applications use exactly the same loading logic
3. **Maintenance**: Only need to update actions in one place
4. **Full Functionality**: Batchloader has access to all loader features
5. **Centralized Configuration**: Color mappings, project settings, etc. are handled in one place

## Usage

1. Open Blender in a Shot context
2. Run the Batchloader from the ShotGrid menu
3. Select the file types to load (audios, textures, blend files, shot cameras, etc.)
4. The batchloader will automatically scan for available files
5. Select the desired files and execute the load
6. Files will be loaded using the same actions as the individual loader
