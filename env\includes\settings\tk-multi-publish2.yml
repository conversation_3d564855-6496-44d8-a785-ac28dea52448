# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

################################################################################

includes:
  - ../app_locations.yml
  - ./color-spaces.yml

################################################################################

publish_help_url: &help_url "https://support.shotgunsoftware.com/hc/en-us/articles/115000068574-Integrations-User-Guide#The%20Publisher"

################################################################################

# ---- Stand alone publish

settings.tk-multi-publish2.standalone:
  collector: "{self}/collector.py:{config}/tk-multi-publish2/standalone/collector.py"
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{config}/tk-multi-publish2/standalone/publish_copy.py"
      settings:
        skip_extensions: ['mov']
    # - name: Upload for review
    #   hook: "{self}/upload_version.py"
    #   settings: { }
    - name: Publish Video File
      hook: "{self}/publish_file.py:{config}/tk-multi-publish2/standalone/publish_copy.py:{config}/tk-multi-publish2/standalone/publish_video.py"
      settings: { }
  context_mapping:
    Episode:
      strStoryboard:
        sbpz:
          file_type: "Storyboard Pro Packed Project"
          is_primary: true
          template: episode_storyboard_publish
        mov:
          file_type: "Media Review"
          is_primary: false
          template: episode_storyboard_video_review_publish
      strRough:
        sbpz:
          file_type: "Storyboard Pro Packed Project"
          is_primary: true
          template: episode_storyboard_publish
        mov:
          file_type: "Media Review"
          is_primary: false
          template: episode_storyboard_video_review_publish
      strClean:
        sbpz:
          file_type: "Storyboard Pro Packed Project"
          is_primary: true
          template: episode_storyboard_publish
        mov:
          file_type: "Media Review"
          is_primary: false
          template: episode_storyboard_video_review_publish
    Sequence:
      strStoryboard:
        sbpz:
          file_type: "Storyboard Pro Packed Project"
          is_primary: true
          template: sequence_storyboard_publish
        mov:
          file_type: "Media Review"
          is_primary: false
          template: sequence_storyboard_video_review_publish
      strRough:
        sbpz:
          file_type: "Storyboard Pro Packed Project"
          is_primary: true
          template: sequence_storyboard_publish
        mov:
          file_type: "Media Review"
          is_primary: false
          template: sequence_storyboard_video_review_publish
      strClean:
        sbpz:
          file_type: "Storyboard Pro Packed Project"
          is_primary: true
          template: sequence_storyboard_publish
        mov:
          file_type: "Media Review"
          is_primary: false
          template: sequence_storyboard_video_review_publish
  secondary_items:
    sbpz: ["mov"]

  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

################################################################################

# ---- 3dsMax

# asset step
settings.tk-multi-publish2.3dsmax.asset_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
    Work Template: max_asset_work
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      settings:
        Publish Template: max_asset_publish
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session_geometry.py"
      settings:
        Publish Template: asset_alembic_cache
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

# shot step
settings.tk-multi-publish2.3dsmax.shot_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
    Work Template: max_shot_work
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      settings:
        Publish Template: max_shot_publish
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

################################################################################

# ---- Houdini

# asset step
settings.tk-multi-publish2.houdini.asset_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py:{config}/houdini/tk-multi-publish2/basic_collector_override.py"
  collector_settings:
    Work Template: houdini_asset_work
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      settings:
        Publish Template: houdini_asset_publish
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

# shot step
settings.tk-multi-publish2.houdini.shot_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py:{config}/tk-multi-publish2/houdini/basic_collector_override.py:{config}/tk-multi-publish2/houdini/review_media_collector.py"
  collector_settings:
    Work Template: houdini_shot_work
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      settings:
        Publish Template: houdini_shot_publish

    - name: Publish sequence files
      hook: "{config}/tk-multi-publish2/multi/publish_frames_sequence.py"
      settings:
        Work Template: houdini_shot_work
        Primary Publish Template: houdini_shot_publish
        Publish Render Template: shot_review_publish_proxy
        Publish Render Type: Rendered Image

    - name: Publish video file
      hook: "{config}/tk-multi-publish2/houdini/publish_frames_video.py"
      settings:
        Work Template: houdini_shot_work
        Primary Publish Template: houdini_shot_publish
        Publish Render Template: shot_review_publish_proxy
        Publish Review Template: shot_review_publish
        Publish Review Type: Media Review

  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

################################################################################

# ---- Mari

# asset_step
settings.tk-multi-publish2.mari.asset_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_mari_textures.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      settings:
        Publish Template: asset_mari_texture_tif
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

################################################################################

# ---- Maya

# asset step
settings.tk-multi-publish2.maya.asset_step:
  # collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py:{config}/tk-multi-publish2/maya/rigging/attrs_collector.py:{config}/tk-multi-publish2/maya/collector_alembic.py:{config}/tk-multi-publish2/maya/assets/collector_fbx.py:{config}/tk-multi-publish2/maya/assets/collector_obj.py:{config}/tk-multi-publish2/maya/usd/high_root_usd_collector.py:{config}/tk-multi-publish2/maya/collector_geo.py:{config}/tk-multi-publish2/maya/rigging/roms_collector.py:{config}/tk-multi-publish2/maya/rigging/collector_facial_media_review.py:{config}/tk-multi-publish2/maya/assets/gpu_collector.py"
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py:{config}/tk-multi-publish2/maya/rigging/attrs_collector.py:{config}/tk-multi-publish2/maya/collector_alembic.py:{config}/tk-multi-publish2/maya/assets/collector_fbx.py:{config}/tk-multi-publish2/maya/assets/collector_obj.py:{config}/tk-multi-publish2/maya/usd/high_root_usd_collector.py:{config}/tk-multi-publish2/maya/rigging/roms_collector.py:{config}/tk-multi-publish2/maya/rigging/collector_facial_media_review.py:{config}/tk-multi-publish2/maya/assets/gpu_collector.py"
  # collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py:{config}/tk-multi-publish2/maya/collector_alembic.py:{config}/tk-multi-publish2/maya/assets/collector_fbx.py:{config}/tk-multi-publish2/maya/assets/collector_obj.py:{config}/tk-multi-publish2/maya/assets/gpu_collector.py"
  collector_settings:
    Work Template: maya_asset_work
    Publish Template: maya_asset_publish
    Transform Node: model_high_root
    Valid Steps: ['model']
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py:{config}/tk-multi-publish2/maya/update_session_publish.py:{config}/tk-multi-publish2/multi/checklist_validation.py:{config}/tk-multi-publish2/multi/framework_validations.py"
    #    :{config}/tk-multi-publish2/maya/checklist_validation.py
    #    :{config}/tk-multi-publish2/maya/file_checker.py
      settings:
        Publish Template: maya_asset_publish
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Validate meshes in scene.
      hook: "{config}/tk-multi-publish2/maya/rigging/attrs_plugin.py"
      settings: { }

    - name: Rig Dynamic Attrs Alembics
      hook: "{config}/tk-multi-publish2/maya/rigging/publish_asset_dynattr_alembics.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Work Template: maya_asset_work
        Primary Publish Template: maya_asset_publish
        Publish Template: asset_dynamic_attrs_alembic
        Publish type: "Alembic Cache"
        Export mode: "alembic"
        Allowed asset types: [ "character", "prop" ]
        Allowed groups: [ "model_high_root" ]
        Task dynamic Attrs: ["fullrig"]

    - name: USDc Asset
      hook: "{config}/tk-multi-publish2/maya/usd/publish_asset_usd.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Work Template: maya_asset_work
        Primary Publish Template: maya_asset_publish
        USDc Publish Template: asset_usdc_cache
        USDc Publish type: "USDc Asset"
        USDa Publish Template: asset_usda_container
        USDa Publish type: "USDa Asset"

    - name: Alembic Asset
      hook: "{config}/tk-multi-publish2/maya/publish_asset_alembic.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Work Template: maya_asset_work
        Primary Publish Template: maya_asset_publish
        Alembic Publish Type:     Alembic Cache
        Alembic Publish Template: asset_alembic_cache
        Transform Node: model_high_root
        Valid Steps: ['model']

    - name: FBX Asset
      hook: "{config}/tk-multi-publish2/maya/assets/publish_asset_fbx.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Work Template: maya_asset_work
        Primary Publish Template: maya_asset_publish
        FBX Publish Type: FBX Mesh
        FBX Publish Template: 3d_mesh_asset_publish
        Transform Node: model_high_root
        Valid Steps: ['model']

    - name: OBJ Asset
      hook: "{config}/tk-multi-publish2/maya/assets/publish_asset_obj.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Work Template: maya_asset_work
        Primary Publish Template: maya_asset_publish
        OBJ Publish Type: OBJ Mesh
        OBJ Publish Template: 3d_mesh_asset_publish
        Transform Node: model_high_root
        Valid Steps: ['model']

    # - name: Shape nodes
    #   hook: "{config}/tk-multi-publish2/maya/publish_mdl_shapeOrig.py"
    #   settings:
    #     Work Template: maya_asset_work
    #     Primary Publish Template: maya_asset_publish
    #     Model Group name: "model_high_root"

    - name: Rig ROM Alembics
      hook: "{config}/tk-multi-publish2/maya/rigging/publish_asset_rom_alembics.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Work Template: maya_asset_work
        Primary Publish Template: maya_asset_publish
        Publish Template: asset_rom_alembic
        Publish type: "Alembic Cache"
        Rom Publish type: "Animation Data"
        Rom Task: "ROMAnimation"
        Export mode: "alembic"
        Allowed asset types: [ "character", "prop" ]
        Allowed groups: [ "model_high_root" ]
        Allowed steps: [ "rig" ]
        Allowed tasks: [ "fullrig" ]

    - name: Rig ROM Atom
      hook: "{config}/tk-multi-publish2/maya/rigging/publish_asset_rom_atom.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Work Template: maya_asset_work
        Primary Publish Template: maya_asset_publish
        Publish Template: asset_maya_atom_animation
        Publish type: "Animation Data"
        Allowed asset types: [ "character", "prop" ]
        Allowed groups: [ "model_high_root" ]
        Allowed steps: [ "rig" ]
        Allowed tasks: [ "romanimation" ]

    - name: GPU Cache
      hook: "{config}/tk-multi-publish2/maya/assets/publish_asset_gpu_cache.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Work Template: maya_asset_work
        Primary Publish Template: maya_asset_publish
        Publish Template: asset_gpu_cache
        Publish type: "GPU Cache"
        Allowed asset types: ["envprop", "envlocation", "prop"]
        Allowed steps: ["model"]
        Allowed tasks: ["modeling, mdlmodeling", "multitasks"]
        Allowed groups: ["model_high_root"]

    # - name: Facial Media Review
    #  hook: "{config}/tk-multi-publish2/maya/rigging/publish_facial_review.py"
    #  settings:
    #    Work Template: maya_asset_work
    #    Primary Publish Template: maya_asset_publish
    #    Asset Review Work Template: asset_maya_work_review
    #    Asset Review Publish Template: asset_review_publish
    #    Head joint name: [ J_Head, J_Bind_Head_M ]

  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_multi_secondary_outputs_to_farm.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"
  check_hooks: "{config}/tk-multi-publish2/maya/validations/utils.py:{config}/tk-multi-publish2/maya/validations/pool.py:{config}/tk-multi-publish2/maya/validations/checks.py:{config}/tk-multi-publish2/maya/state_actions.py"


# asset step notask
settings.tk-multi-publish2.maya.asset_step_notask:
  # collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py:{config}/tk-multi-publish2/maya/rigging/attrs_collector.py:{config}/tk-multi-publish2/maya/collector_alembic.py:{config}/tk-multi-publish2/maya/assets/collector_fbx.py:{config}/tk-multi-publish2/maya/assets/collector_obj.py:{config}/tk-multi-publish2/maya/usd/high_root_usd_collector.py:{config}/tk-multi-publish2/maya/collector_geo.py"
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py:{config}/tk-multi-publish2/maya/rigging/attrs_collector.py:{config}/tk-multi-publish2/maya/collector_alembic.py:{config}/tk-multi-publish2/maya/assets/collector_fbx.py:{config}/tk-multi-publish2/maya/assets/collector_obj.py"
  collector_settings:
    Work Template: maya_asset_work_notask
  publish_plugins:
    # - name: Publish to Shotgun
    #   hook: "{self}/publish_file.py"
    #   settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py:{config}/tk-multi-publish2/maya/update_session_publish.py:{config}/tk-multi-publish2/multi/checklist_validation.py:{config}/tk-multi-publish2/multi/framework_validations.py"
      #  :{config}/tk-multi-publish2/maya/framework_validation.py
      settings:
        Publish Template: maya_asset_publish_notask
    - name: Validate meshes in scene.
      hook: "{config}/tk-multi-publish2/maya/rigging/attrs_plugin.py"
      settings: { }

    - name: Rig Dynamic Attrs Alembics
      hook: "{config}/tk-multi-publish2/maya/rigging/publish_asset_dynattr_alembics.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Work Template: maya_asset_work_notask
        Primary Publish Template: maya_asset_publish_notask
        Publish Template: asset_dynamic_attrs_alembic_notask
        Publish type: "Alembic Cache"
        Export mode: "alembic"
        Allowed asset types: [ "character", "prop" ]
        Allowed groups: [ "model_high_root" ]
        Task dynamic Attrs: ["fullrig"]

    - name: USD Asset
      hook: "{config}/tk-multi-publish2/maya/usd/publish_asset_usd.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Work Template: maya_asset_work_notask
        Primary Publish Template: maya_asset_publish_notask
        USDc Publish Template: asset_usdc_cache
        USDc Publish type: "USDc Asset"
        USDa Publish Template: asset_usda_container
        USDa Publish type: "USDa Asset"

    - name: Alembic Asset
      hook: "{config}/tk-multi-publish2/maya/publish_asset_alembic.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Work Template: maya_asset_work_notask
        Primary Publish Template: maya_asset_publish_notask
        Alembic Publish Type:     Alembic Cache
        Alembic Publish Template:  asset_alembic_cache_notask
        Transform Node: model_high_root
        Valid Steps: ['model']

    - name: FBX Asset
      hook: "{config}/tk-multi-publish2/maya/assets/publish_asset_fbx.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Work Template: maya_asset_work_notask
        Primary Publish Template: maya_asset_publish_notask
        FBX Publish Type: FBX Mesh
        FBX Publish Template: 3d_mesh_asset_publish_notask
        Transform Node: model_high_root
        Valid Steps: ['model']

    - name: OBJ Asset
      hook: "{config}/tk-multi-publish2/maya/assets/publish_asset_obj.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Work Template: maya_asset_work_notask
        Primary Publish Template: maya_asset_publish_notask
        OBJ Publish Type: OBJ Mesh
        OBJ Publish Template: 3d_mesh_asset_publish_notask
        Transform Node: model_high_root
        Valid Steps: ['model']

    # - name: Shape nodes
    #   hook: "{config}/tk-multi-publish2/maya/publish_mdl_shapeOrig.py"
    #   settings:
    #     Work Template: maya_asset_work_notask
    #     Primary Publish Template: maya_asset_publish_notask
    #     Model Group name: "model_high_root"

    - name: Rig ROM Atom
      hook: "{config}/tk-multi-publish2/maya/rigging/publish_asset_rom_atom.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Work Template: maya_asset_work
        Primary Publish Template: maya_asset_publish
        Publish Template: asset_maya_atom_animation
        Publish type: "Animation Data"
        Allowed asset types: [ "character", "prop" ]
        Allowed groups: [ "model_high_root" ]
        Allowed steps: [ "rig" ]
        Allowed tasks: [ "romanimation" ]

    - name: GPU Cache
      hook: "{config}/tk-multi-publish2/maya/assets/publish_asset_gpu_cache.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Work Template: maya_asset_work
        Primary Publish Template: maya_asset_publish
        Publish Template: asset_gpu_cache
        Publish type: "GPU Cache"
        Allowed asset types: ["envprop", "envlocation", "prop"]
        Allowed steps: ["model"]
        Allowed tasks: ["modeling"]
        Allowed groups: ["model_high_root"]

    # - name: Facial Media Review
    #  hook: "{config}/tk-multi-publish2/maya/rigging/publish_facial_review.py"
    #  settings:
    #    Work Template: maya_asset_work
    #    Primary Publish Template: maya_asset_publish
    #    Asset Review Work Template: asset_maya_work_review
    #    Asset Review Publish Template: asset_review_publish
    #    Head joint name: [ J_Head, J_Bind_Head_M ]

  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_multi_secondary_outputs_to_farm.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"
  check_hooks: "{config}/tk-multi-publish2/maya/validations/utils.py:{config}/tk-multi-publish2/maya/validations/pool.py:{config}/tk-multi-publish2/maya/validations/checks.py:{config}/tk-multi-publish2/maya/state_actions.py"

# environment step
settings.tk-multi-publish2.maya.environment_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py:{config}/tk-multi-publish2/maya/rigging/attrs_collector.py"
  collector_settings:
    Work Template: maya_environment_work
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py:{config}/tk-multi-publish2/maya/update_session_publish.py:{config}/tk-multi-publish2/multi/checklist_validation.py:{config}/tk-multi-publish2/multi/framework_validations.py"
      #  :{config}/tk-multi-publish2/maya/framework_validation.py
      settings:
        Publish Template: maya_environment_publish
    - name: Validate meshes in scene.
      hook: "{config}/tk-multi-publish2/maya/rigging/attrs_plugin.py"
      settings: { }

    - name: Rig Dynamic Attrs Alembics
      hook: "{config}/tk-multi-publish2/maya/rigging/publish_asset_dynattr_alembics.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Work Template: maya_environment_work
        Primary Publish Template: maya_environment_publish
        Publish Template: environment_dynamic_attrs_alembic
        Publish type: "Alembic Cache"
        Export mode: "alembic"
        Allowed asset types: [ "envprop" ]
        Allowed groups: [ "model_high_root" ]
        Task dynamic Attrs: ["fullrig"]

    - name: USD Asset
      hook: "{config}/tk-multi-publish2/maya/usd/publish_asset_usd.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Work Template: maya_environment_work
        Primary Publish Template: maya_environment_publish
        USDc Publish Template: asset_usdc_cache
        USDc Publish type: "USDc Asset"
        USDa Publish Template: asset_usda_container
        USDa Publish type: "USDa Asset"

  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"
  check_hooks: "{config}/tk-multi-publish2/maya/validations/utils.py:{config}/tk-multi-publish2/maya/validations/pool.py:{config}/tk-multi-publish2/maya/validations/checks.py:{config}/tk-multi-publish2/maya/state_actions.py"


# environment step notask
settings.tk-multi-publish2.maya.environment_step_notask:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py:{config}/tk-multi-publish2/maya/rigging/attrs_collector.py"
  collector_settings:
    Work Template: maya_environment_work_notask
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py:{config}/tk-multi-publish2/maya/update_session_publish.py:{config}/tk-multi-publish2/multi/checklist_validation.py:{config}/tk-multi-publish2/multi/framework_validations.py"
      #  :{config}/tk-multi-publish2/maya/framework_validation.py
      settings:
        Publish Template: maya_environment_publish_notask
    - name: Validate meshes in scene.
      hook: "{config}/tk-multi-publish2/maya/rigging/attrs_plugin.py"
      settings: { }

    - name: Rig Dynamic Attrs Alembics
      hook: "{config}/tk-multi-publish2/maya/rigging/publish_asset_dynattr_alembics.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Work Template: maya_environment_work_notask
        Primary Publish Template: maya_environment_publish_notask
        Publish Template: environment_dynamic_attrs_alembic_notask
        Publish type: "Alembic Cache"
        Export mode: "alembic"
        Allowed asset types: [ "envprop" ]
        Allowed groups: [ "model_high_root" ]
        Task dynamic Attrs: ["fullrig"]

    - name: USD Asset
      hook: "{config}/tk-multi-publish2/maya/usd/publish_asset_usd.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Work Template: maya_environment_work_notask
        Primary Publish Template: maya_environment_publish_notask
        USDc Publish Template: asset_usdc_cache
        USDc Publish type: "USDc Asset"
        USDa Publish Template: asset_usda_container
        USDa Publish type: "USDa Asset"

  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"
  check_hooks: "{config}/tk-multi-publish2/maya/validations/utils.py:{config}/tk-multi-publish2/maya/validations/pool.py:{config}/tk-multi-publish2/maya/validations/checks.py:{config}/tk-multi-publish2/maya/state_actions.py"


# sequence step
settings.tk-multi-publish2.maya.sequence_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py:{config}/tk-multi-publish2/maya/collector_sequence_cameras.py:{config}/tk-multi-publish2/maya/sequence/collector_media_review.py:{config}/tk-multi-publish2/maya/sequence/collector_media_topview_review.py:{config}/tk-multi-publish2/maya/sequence/collector_shot_data.py:{config}/tk-multi-publish2/maya/layout/collector_sequence_split_shots.py"
  collector_settings:
    Work Template: maya_sequence_work
  publish_plugins:
    - name: Validate Shot Data
      hook: "{config}/tk-multi-publish2/maya/layout/shot_links.py"
      settings: { }
    # - name: Publish to Shotgun
    #   hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{config}/tk-multi-publish2/maya/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py:{config}/tk-multi-publish2/maya/update_session_publish.py:{config}/tk-multi-publish2/multi/checklist_validation.py:{config}/tk-multi-publish2/multi/framework_validations.py"
      #   {self}/publish_file.py
      #  :{config}/tk-multi-publish2/maya/framework_validation.py
      settings:
        Publish Template: maya_sequence_publish

    - name: Sequence Review to Shotgun
      hook: "{config}/tk-multi-publish2/maya/sequence/plugin_media_review.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        work_template: maya_sequence_work
        primary_publish_template: maya_sequence_publish
        media_review_publish_template: maya_sequence_playblast_publish
        clip_publish_template: maya_sequence_clip_publish
        publish_type: Maya Review
        shot_prefix: "{context.entity}_"
        shot_digits: 4
        framerate: 25.0
        sequence_start_frame: 1001
        # plugin_hooks:
        #   clips_publisher: "{config}/tk-multi-publish2/maya/sequence/clips_publisher.py"

    - name: Sequence TopView Review to Shotgun
      hook: "{config}/tk-multi-publish2/maya/sequence/plugin_media_topview_review.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        work_template: maya_sequence_work
        primary_publish_template: maya_sequence_publish
        media_review_publish_template: maya_sequence_playblast_publish
        publish_type: Maya TopVIew Review
        shot_prefix: "{context.entity}_"
        padding: 4
        framerate: 25.0
        sequence_start_frame: 1001
        # plugin_hooks:
        #   clips_publisher: "{config}/tk-multi-publish2/maya/sequence/clips_publisher.py"

    - name: Sequence Cameras Geo
      hook: "{config}/tk-multi-publish2/maya/publish_sequence_cameras.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Work Template: maya_sequence_work
        Primary Publish Template: maya_sequence_publish
        Publish Template: maya_scene_cameras_geo
        Publish type: Scene Cameras
        Cameras group: "Cameras"
        Export type: "alembic"
        Shot Prefix: "{context.entity}_"
        Shot Digits: 4

    - name: Sequence Shot Splitter
      hook: "{config}/tk-multi-publish2/maya/layout/split_shot_queue.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Work Template: maya_sequence_work
        Primary Publish Template: maya_sequence_publish
        Valid Step Names: ["layout"]
        Valid Task Names: ["layout", "blocking", "polish", "lytassembly", "lytblocking", "ltypolish", "ltylayout"]

  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_multi_secondary_outputs_to_farm.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"
  check_hooks: "{config}/tk-multi-publish2/maya/validations/utils.py:{config}/tk-multi-publish2/maya/validations/pool.py:{config}/tk-multi-publish2/maya/validations/checks.py:{config}/tk-multi-publish2/maya/state_actions.py"

# shot step
settings.tk-multi-publish2.maya.shot_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py:{config}/tk-multi-publish2/maya/shot/alembics/collector_alembic_assets.py:{config}/tk-multi-publish2/maya/collector_shot_media_review.py:{config}/tk-multi-publish2/maya/shot/overrides/gpu/collector_gpu_overrides.py:{config}/tk-multi-publish2/maya/shot/overrides/collector_overrides.py:{config}/tk-multi-publish2/maya/collector_camera.py:{config}/tk-multi-publish2/maya/collector_render_layers.py:{config}/tk-multi-publish2/maya/shot/rig_on_the_fly_abstraction/collector_rigs_on_the_fly_assets.py:{config}/tk-multi-publish2/maya/shot/global_locators/collector_globalLocators_assets.py"
  collector_settings:
    Work Template: maya_shot_work
    Publish Frames Template: shot_render_publish

    # collector gpu overrides settings -------------------------------------------------
    Environment Publish Types:
      - GPU Cache
    Environment Asset Type: EnvLocation
    # collector alembic assets settings ------------------------------------------------
    Asset Geometry Cache Export Supported Types:
      - Character
      - EnvProp
      - Prop
    # collector environment overrides settings -----------------------------------------
    Environment Overrides Supported Types:
      - EnvLocation
      - EnvModule
    attributes:
      asset_type: 'assetType'
    # collector rig on the fly settings ------------------------------------------------
    asset_type: onTheFlyController
    common_attributes:
      - assetType
      - mty_mainAsset
      - mty_mainAssetID
      - mty_constraints
    schema_version: "1.0.0"
    # collector global locators settings -----------------------------------------------
    global_locators_supported_assets:
      - Character
    global_locators_supported_names:
      - LOC_Global_Scale
      - Global_Scale_LOC
    # ----------------------------------------------------------------------------------
    Environment Overrides Name: overrides

  publish_plugins:
    # - name: Publish to Shotgun
    #   hook: "{self}/publish_file.py"
    #   settings: { }
    - name: Shot Camera Alembic
      hook: "{config}/tk-multi-publish2/maya/camera_alembic.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Work Template: maya_shot_work
        Primary Publish Template: maya_shot_publish
        Publish Template: shot_camera_publish
        Publish Type: "Shot Camera Alembic"
        camera_type_field:  "sg_camera_movement"
        remove_shake_camera_type: "Static Shake"
        camera_alembic_allowed_steps: ["Setup", "Layout", "Animation"]
    # - name: Media Review
    #   # hook: "{config}/tk-multi-publish2/maya/publish_shot_media_review.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
    #   # settings:
    #   #   Work Template: maya_shot_work
    #   #   Primary Publish Template: maya_shot_publish
    #   #   Review Publish Template: shot_review_publish
    #   #   Publish type: Media Review
    #   #   Camera Pattern: [ "rigged_CAMShape", "free_CAMShape" ]
    #   #   Camera Export Review: "rigged_CAM"
    #   #   sg_in_frame_field: sg_cut_in
    #   #   sg_out_frame_field: sg_cut_out
    #   #   width: 1920
    #   #   height: 1080
    #   #   tasks_to_find_audio: ["Editorial", "edlAnimatic"]
    #   #   custom_render_settings:
    #   #     v_opts:
    #   #       PYB_lighting:
    #   #         displayLights: "all"
    #   #         shadows: True
    #   #       PYB_two_side_lighting_off:
    #   #         twoSidedLightning: False
    #   #     v2_opts:
    #   #       PYB_alpha_cut:
    #   #         transparencyAlgorithm: 5
    #   #     others: {}
    #   hook: "{config}/tk-multi-publish2/multi/publish_frames_video.py:{config}/tk-multi-publish2/maya/shot/export_playblast_frames.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
    #   settings:
    #     Work Template: maya_shot_work
    #     Primary Publish Template: maya_shot_publish
    #     Publish Render Template: shot_review_publish_proxy
    #     Publish Review Template: shot_review_publish
    #     Image Sequence Workflow: individual
    #     Publish Review Type: Media Review
    #     Editorial Video Opacity: 0.35
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py:{config}/tk-multi-publish2/maya/update_session_publish.py:{config}/tk-multi-publish2/multi/checklist_validation.py:{config}/tk-multi-publish2/multi/framework_validations.py"
      settings:
        Publish Template: maya_shot_publish
        Enforced Publish Plugins:
          { "Shot Geometry Assets": { "Animation": [ "Polish" ] } }
    - name: Shot GPU Environment Overrides
      hook: "{config}/tk-multi-publish2/maya/shot/overrides/gpu/plugin_gpu_overrides.py"
      settings:
        Work Template: maya_shot_work
        Primary Publish Template: maya_shot_publish
        Publish Template: shot_gpu_environment_overrides_publish
        Publish Type: "Shot Environment Overrides"
    - name: Shot Environment Overrides
      hook: "{config}/tk-multi-publish2/maya/shot/overrides/plugin_overrides.py"
      settings:
        Publish Template: shot_environment_overrides_publish
        Work Template: maya_shot_work
        Primary Publish Template: maya_shot_publish
        Publish Type: "Shot Environment Overrides"
        plugin_hooks:
          overrides: "{config}/tk-multi-publish2/maya/shot/overrides/overrides.py"
          helpers: "{config}/tk-multi-publish2/maya/shot/overrides/helpers.py"

    # - name: Shot USD Assets
    #   hook: "{config}/tk-multi-publish2/maya/shot/usd/plugin_shot_usd.py"
    #   settings:
    #     Work Template: maya_shot_work
    #     USD Publish Template: maya_shot_animation_usd_publish
    #     Publish Session Template: maya_shot_publish
    #     USD publish_type: "Maya Shot Geo USD"
    #     start_field: sg_cut_in
    #     end_field: sg_cut_out

    #     map_of_tasks:
    #       supported:
    #         - polish
    #         - anm3dpolish
    #       not_supported:
    #         - blocking
    #         - layout
    #         - lyt3dlayout
    #         - anm3dblocking
    #     map_of_pipeline_steps:
    #       supported:
    #         - animation
    #       not_supported:
    #         - layout

    - name: Shot Alembic Assets
      hook: "{config}/tk-multi-publish2/maya/shot/alembics/plugin_alembic_assets.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        workarea_template: maya_shot_work
        publish_template: maya_shot_animation_alembic_publish
        primary_publish_template: maya_shot_publish
        Publish Part Template: maya_shot_animation_alembic_part_publish
        Publish parts: false
        publish_type: "Maya Shot Geo Alembic"
        Export mode: alembic
        Entity Filter: Asset
        plugin_hooks:
          accept: "{config}/tk-multi-publish2/maya/shot/alembics/accept_hook.py"
          alembic: "{config}/tk-multi-publish2/maya/shot/alembics/alembic.py"
          temporal_accept: "{config}/tk-multi-publish2/maya/shot/alembics/temporal_accept_hook.py"
        # Ignore Asset Types:
        #   - Camera
        #   - EnvLocation
        #   - EnvModule
        # extensions:
        #   - ma
        #   - abc
        # map_of_asset_group_names:
        #   Rig:
        #     - rig_high_root
        #     - rig_low_root
        #   Modeling:
        #     - model_high_root
        map_of_tasks:
          supported:
            - polish
            - anm3dpolish
          not_supported:
            - blocking
            - layout
            - lyt3dlayout
            - anm3dblocking
        map_of_pipeline_steps:
          supported:
            - animation
          not_supported:
            - layout
        # map_of_entities:
        #   supported:
        #     - Asset
        #   not_supported:
        #     - Shot
        camera cache publish type: Shot Camera Alembic

    - name: Render Sequence
      hook: "{config}/tk-multi-publish2/multi/publish_frames_sequence.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Work Template: maya_shot_work
        Primary Publish Template: maya_shot_publish
        Publish Render Template: shot_render_publish
        Publish Render Type: Rendered Image
        Image Sequence Workflow: individual
        Invalid Image Extensions: ['sgi', 'jpg']
      # Resolution Validation Ignore Tasks: ["render"]

    - name: Media Review
      hook: "{config}/tk-multi-publish2/multi/publish_frames_video.py:{config}/tk-multi-publish2/maya/shot/export_playblast_frames.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Work Template: maya_shot_work
        Primary Publish Template: maya_shot_publish
        Publish Render Template: shot_review_publish_proxy
        Publish Review Template: shot_review_publish
        Image Sequence Workflow: individual
        Publish Review Type: Media Review
        Editorial Video Opacity: 0.35

    - name: Shot Rig on the Fly Assets
      hook: "{config}/tk-multi-publish2/maya/shot/rig_on_the_fly_abstraction/plugin_rigs_on_the_fly_assets.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Work Template: maya_shot_work
        Primary Publish Template: maya_shot_publish
        Publish Template: maya_shot_rig_on_the_fly_json_publish
        Publish Type: Maya Shot RigsOnTheFly Abstraction
        supported_tasks:
          - Layout
        supported_pipeline_steps:
          - Layout
        map_of_entities:
          supported:
            - Shot
          not_supported:
            - Asset

    - name: Shot Global Locators Assets
      hook: "{config}/tk-multi-publish2/maya/shot/global_locators/plugin_globalLocators_assets.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        workarea_template: maya_shot_work
        primary_publish_template: maya_shot_publish
        publish_template: maya_shot_global_locators_alembic_publish
        # Publish Part Template: maya_shot_animation_alembic_part_publish
        # Publish parts: false
        publish_type: "Maya Shot Global Locators Alembic"
        supported_tasks:
          - Layout
        supported_pipeline_steps:
          - Layout

  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_multi_secondary_outputs_to_farm.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"
  overrides_hook: "{config}/tk-multi-publish2/maya/shot/overrides/overrides.py"
  check_hooks: "{config}/tk-multi-publish2/maya/validations/utils.py:{config}/tk-multi-publish2/maya/validations/pool.py:{config}/tk-multi-publish2/maya/validations/checks.py:{config}/tk-multi-publish2/maya/state_actions.py"

################################################################################

# ---- Nuke

# asset step
settings.tk-multi-publish2.nuke.asset_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
    Work Template: nuke_asset_work
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/nuke_start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/nuke_publish_script.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      settings:
        Publish Template: nuke_asset_publish
    - name: Submit for Review
      hook: "{engine}/tk-multi-publish2/basic/submit_for_review.py"
      settings: { }
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

# shot step
settings.tk-multi-publish2.nuke.shot_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
    Work Template: nuke_shot_work
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/nuke_start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/nuke_publish_script.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      settings:
        Publish Template: nuke_shot_publish
    - name: Submit for Review
      hook: "{engine}/tk-multi-publish2/basic/submit_for_review.py"
      settings: { }
    - name: Update Flame Clip
      hook: "{engine}/tk-multi-publish2/basic/nuke_update_flame_clip.py"
      settings:
        Flame Clip Template: flame_shot_clip
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

################################################################################

# ---- NukeStudio

settings.tk-multi-publish2.nukestudio:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
    Work Template: hiero_project_work
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/nukestudio_start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/nukestudio_publish_project.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      settings:
        Publish Template: hiero_project_publish
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

################################################################################

# ---- Photoshop

# asset step
settings.tk-multi-publish2.photoshop.asset_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py:{config}/tk-multi-publish2/photoshopcc/collector_photoshop_proxy.py:{config}/tk-multi-publish2/photoshopcc/collector_photoshop_layergroups.py:{config}/tk-multi-publish2/photoshopcc/collector_photoshop_layergroups_proxies.py"
  collector_settings:
    Primary Publish Template: photoshop_asset_publish
    Work Template: photoshop_asset_work
    Publish Photoshop Proxy Template: photoshop_asset_proxy_publish
    Publish Layer Group Image Template: photoshop_asset_layergroup_publish
    Publish Layer Group Proxy Image Template: photoshop_asset_layergroup_proxy_publish
    Publish Image Review Template: photoshop_asset_image_review_publish
    Publish Video Template: photoshop_asset_video_review_publish
    Photoshop Proxy Image File Type: "Photoshop Proxy Image"
    Image File Type: "Photoshop Layer Group Image"
    Proxy Image File Type: "Photoshop Layer Group Proxy Image"
    Image Sequence Review Type: "Image Sequence Review"
    Video File Type: "Media Review"
    PS Proxy Resize Percentage: 50
  publish_plugins:
    # - name: Publish to Shotgun
    #   hook: "{self}/publish_file.py"
    #   settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_document.py:{config}/tk-multi-publish2/photoshopcc/publish_document_media_res.py:{config}/tk-multi-publish2/multi/framework_validations.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      settings:
        Publish Template: photoshop_asset_publish
    - name: Publish Layer Group Order
      hook: "{config}/tk-multi-publish2/photoshopcc/publish_photoshop_layer_groups_order.py"
      settings:
        File extension: "JSON"
        File Type: "Layer Group Order"
        Layer Group Order Template: photoshop_asset_layergroup_order
        Layer Group Template: photoshop_asset_layergroup_publish
        Layer Group Proxy Template: photoshop_asset_layergroup_proxy_publish

    - name: Photoshop Proxy Image
      hook: "{config}/tk-multi-publish2/photoshopcc/publish_photoshop_proxy.py"
      settings: { }

    - name: Layer Group Images
      hook: "{config}/tk-multi-publish2/photoshopcc/publish_photoshop_layer_groups.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Available Steps: []
        Available Tasks: []
        # Document Width: 3840
        # Document Height: 2160
        # Image Resolution: 72
        Layer Group Image Format: png

    - name: Layer Group Proxy Images
      hook: "{config}/tk-multi-publish2/photoshopcc/publish_photoshop_layer_groups_proxies.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Available Steps: []
        Available Tasks: []
        # Document Width: 3840
        # Document Height: 2160
        # Image Resolution: 72
        Proxy Image Size Percentage: 20
        Layer Group Proxy Image Format: png

    - name: Layer Groups Video
      hook: "{config}/tk-multi-publish2/photoshopcc/publish_photoshop_layer_groups_video.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Available Steps: []
        Available Tasks: []
        # Document width: 3840
        # Document height: 2160
        # Image resolution: 300

    # - name: Upload for review
    #   hook: "{engine}/tk-multi-publish2/basic/upload_version.py"
    #   settings: { }
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_multi_secondary_outputs_to_farm.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

# asset step notask
settings.tk-multi-publish2.photoshop.asset_step_notask:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
    Work Template: photoshop_asset_work_notask
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_document.py:{config}/tk-multi-publish2/photoshopcc/publish_document_media_res.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      settings:
        Publish Template: photoshop_asset_publish_notask
    - name: Upload for review
      hook: "{engine}/tk-multi-publish2/basic/upload_version.py"
      settings: { }
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_multi_secondary_outputs_to_farm.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

# environment step
settings.tk-multi-publish2.photoshop.environment_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
    Work Template: photoshop_environment_work
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_document.py:{config}/tk-multi-publish2/photoshopcc/publish_document_media_res.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      settings:
        Publish Template: photoshop_environment_publish
    - name: Upload for review
      hook: "{engine}/tk-multi-publish2/basic/upload_version.py"
      settings: { }
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_multi_secondary_outputs_to_farm.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

# environment step notask
settings.tk-multi-publish2.photoshop.environment_step_notask:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
    Work Template: photoshop_environment_work_notask
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_document.py:{config}/tk-multi-publish2/photoshopcc/publish_document_media_res.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      settings:
        Publish Template: photoshop_environment_publish_notask
    - name: Upload for review
      hook: "{engine}/tk-multi-publish2/basic/upload_version.py"
      settings: { }
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_multi_secondary_outputs_to_farm.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

# shot_step
settings.tk-multi-publish2.photoshop.shot_step:
    # {self}/collector.py
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py:{config}/tk-multi-publish2/photoshopcc/collector_photoshop_proxy.py:{config}/tk-multi-publish2/photoshopcc/collector_photoshop_layergroups.py:{config}/tk-multi-publish2/photoshopcc/collector_photoshop_layergroups_proxies.py"
  collector_settings:
    Primary Publish Template: photoshop_shot_publish
    Work Template: photoshop_shot_work
    Publish Photoshop Proxy Template: photoshop_shot_proxy_publish
    Publish Layer Group Image Template: photoshop_shot_layergroup_publish
    Publish Layer Group Proxy Image Template: photoshop_shot_layergroup_proxy_publish
    Publish Image Review Template: photoshop_shot_image_review_publish
    Publish Video Template: photoshop_shot_video_review_publish
    Photoshop Proxy Image File Type: "Photoshop Proxy Image"
    Image File Type: "Photoshop Layer Group Image"
    Proxy Image File Type: "Photoshop Layer Group Proxy Image"
    Image Sequence Review Type: "Image Sequence Review"
    Video File Type: "Media Review"
    PS Proxy Resize Percentage: 50
  publish_plugins:
    # - name: Publish to Shotgun
    #   hook: "{self}/publish_file.py:{config}/tk-multi-publish2/photoshopcc/validate_task_context.py"
    #   settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_document.py:{config}/tk-multi-publish2/photoshopcc/publish_document_media_res.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      settings:
        Publish Template: photoshop_shot_publish
    - name: Publish Layer Group Order
      hook: "{config}/tk-multi-publish2/photoshopcc/publish_photoshop_layer_groups_order.py"
      settings:
        File extension: "JSON"
        File Type: "Layer Group Order"
        Layer Group Order Template: photoshop_shot_layergroup_order
        Layer Group Template: photoshop_shot_layergroup_publish
        Layer Group Proxy Template: photoshop_shot_layergroup_proxy_publish

    - name: Photoshop Proxy Image
      hook: "{config}/tk-multi-publish2/photoshopcc/publish_photoshop_proxy.py"
      settings: { }

    - name: Layer Group Images
      hook: "{config}/tk-multi-publish2/photoshopcc/publish_photoshop_layer_groups.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Available Steps: []
        Available Tasks: []
        # Document Width: 3840
        # Document Height: 2160
        # Image Resolution: 72
        Layer Group Image Format: png

    - name: Layer Group Proxy Images
      hook: "{config}/tk-multi-publish2/photoshopcc/publish_photoshop_layer_groups_proxies.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Available Steps: []
        Available Tasks: []
        # Document Width: 3840
        # Document Height: 2160
        # Image Resolution: 72
        Proxy Image Size Percentage: 20
        Layer Group Proxy Image Format: png

    - name: Layer Groups Video
      hook: "{config}/tk-multi-publish2/photoshopcc/publish_photoshop_layer_groups_video.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Available Steps: []
        Available Tasks: []
        # Document width: 3840
        # Document height: 2160
        # Image resolution: 300
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_multi_secondary_outputs_to_farm.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

# episode_step
settings.tk-multi-publish2.photoshop.episode_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py:{config}/tk-multi-publish2/photoshopcc/collector_photoshop_proxy.py:{config}/tk-multi-publish2/photoshopcc/collector_photoshop_layergroups.py:{config}/tk-multi-publish2/photoshopcc/collector_photoshop_layergroups_proxies.py"
  collector_settings:
    Primary Publish Template: photoshop_episode_publish
    Work Template: photoshop_episode_work
    Publish Photoshop Proxy Template: photoshop_episode_proxy_publish
    Publish Layer Group Image Template: photoshop_episode_layergroup_publish
    Publish Layer Group Proxy Image Template: photoshop_episode_layergroup_proxy_publish
    Publish Image Review Template: photoshop_episode_image_review_publish
    Publish Video Template: photoshop_episode_video_review_publish
    Photoshop Proxy Image File Type: "Photoshop Proxy Image"
    Image File Type: "Photoshop Layer Group Image"
    Proxy Image File Type: "Photoshop Layer Group Proxy Image"
    Image Sequence Review Type: "Image Sequence Review"
    Video File Type: "Media Review"
    PS Proxy Resize Percentage: 50
  publish_plugins:
    # - name: Publish to Shotgun
    #   hook: "{self}/publish_file.py:{config}/tk-multi-publish2/photoshopcc/validate_task_context.py"
    #   settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_document.py:{config}/tk-multi-publish2/photoshopcc/publish_document_media_res.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      settings:
        Publish Template: photoshop_episode_publish
    - name: Publish Layer Group Order
      hook: "{config}/tk-multi-publish2/photoshopcc/publish_photoshop_layer_groups_order.py"
      settings:
        File extension: "JSON"
        File Type: "Layer Group Order"
        Layer Group Order Template: photoshop_episode_layergroup_publish
        Layer Group Template: photoshop_episode_layergroup_publish
        Layer Group Proxy Template: photoshop_episode_layergroup_proxy_publish

    - name: Photoshop Proxy Image
      hook: "{config}/tk-multi-publish2/photoshopcc/publish_photoshop_proxy.py"
      settings: { }

    - name: Layer Group Images
      hook: "{config}/tk-multi-publish2/photoshopcc/publish_photoshop_layer_groups.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Available Steps: []
        Available Tasks: []
        # Document Width: 3840
        # Document Height: 2160
        # Image Resolution: 72
        Layer Group Image Format: png

    - name: Layer Group Proxy Images
      hook: "{config}/tk-multi-publish2/photoshopcc/publish_photoshop_layer_groups_proxies.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Available Steps: []
        Available Tasks: []
        # Document Width: 3840
        # Document Height: 2160
        # Image Resolution: 72
        Proxy Image Size Percentage: 20
        Layer Group Proxy Image Format: png

    - name: Layer Groups Video
      hook: "{config}/tk-multi-publish2/photoshopcc/publish_photoshop_layer_groups_video.py:{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py"
      settings:
        Available Steps: []
        Available Tasks: []
        # Document width: 3840
        # Document height: 2160
        # Image resolution: 300
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_multi_secondary_outputs_to_farm.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

################################################################################

# ---- After Effects

# asset step
settings.tk-multi-publish2.aftereffects.asset_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
    Work Template: aftereffects_asset_work
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_document.py:{config}/tk-multi-publish2/aftereffects/update_publish_document.py:{config}/tk-multi-publish2/multi/framework_validations.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      settings:
        Publish Template: aftereffects_asset_publish
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Render Render Queue Items
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/make_rendering.py"
      settings: { }
    - name: Copy Renderings to Publish Location
      hook: "{self}/publish_file.py:{config}/tk-multi-publish2/aftereffects/copy_rendering.py"
      settings:
        Primary Publish Template: aftereffects_asset_publish
        Publish Sequence Template: aftereffects_asset_render_pub_mono
        Publish Movie Template: aftereffects_asset_render_movie
        Default Sequence Output Module: PNG Sequence with Alpha
        # Default Movie Output Module: Lossless with Alpha
        Valid Image Extensions: ['png']
    - name: Publish Renderings to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_rendering.py:{config}/tk-multi-publish2/aftereffects/publish_rendering_media_res.py"
      settings: { }
    - name: Upload for review
      hook: "{engine}/tk-multi-publish2/basic/upload_version.py:{config}/tk-multi-publish2/aftereffects/upload_version.py"
      # {self}/upload_version.py
      settings:
        Publish Type: "Media Review"
        Editorial Video Opacity: 0.35
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

# shot_step
settings.tk-multi-publish2.aftereffects.shot_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
    Work Template: aftereffects_shot_work
  publish_plugins:
    # - name: Publish to Shotgun
    #   hook: "{self}/publish_file.py"
    #   settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_document.py:{config}/tk-multi-publish2/aftereffects/update_publish_document.py:{config}/tk-multi-publish2/multi/framework_validations.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      settings:
        Publish Template: aftereffects_shot_publish
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Render Render Queue Items
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/make_rendering.py"
      settings: { }
    - name: Copy Renderings to Publish Location
      hook: "{self}/publish_file.py:{config}/tk-multi-publish2/aftereffects/copy_rendering.py"
      settings:
        Primary Publish Template: aftereffects_shot_publish
        Publish Sequence Template: aftereffects_shot_render_pub_mono
        Publish Movie Template: aftereffects_shot_render_movie
        Default Sequence Output Module: PNG Sequence with Alpha
        # Default Movie Output Module: Lossless with Alpha
        Valid Image Extensions: ['png']
    - name: Publish Renderings to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_rendering.py:{config}/tk-multi-publish2/aftereffects/publish_rendering_media_res.py"
      settings: { }
    - name: Upload for review
      hook: "{engine}/tk-multi-publish2/basic/upload_version.py:{config}/tk-multi-publish2/aftereffects/upload_version.py"
      # {self}/upload_version.py
      settings:
        Publish Type: "Media Review"
        Editorial Video Opacity: 0.35
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

################################################################################

# ---- Motion Builder

# asset_step
settings.tk-multi-publish2.motion_builder.asset_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
    Work Template: mobu_asset_work
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      settings:
        Publish Template: mobu_asset_publish
  help_url: *help_url
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  location: "@apps.tk-multi-publish2.location"

# shot_step
settings.tk-multi-publish2.motion_builder.shot_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
    Work Template: mobu_shot_work
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      settings:
        Publish Template: mobu_shot_publish
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

################################################################################

# ---- Alias

# asset_step
settings.tk-multi-publish2.alias.asset_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
    Work Template: alias_asset_work
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      settings:
        Publish Template: alias_asset_publish
    - name: Publish IGES to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_translation.py"
      settings:
        Publish Template: alias_asset_igs_publish
    - name: Publish CATPart to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_translation.py"
      settings:
        Publish Template: alias_asset_catpart_publish
    - name: Publish JT to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_translation.py"
      settings:
        Publish Template: alias_asset_jt_publish
    - name: Publish STEP to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_translation.py"
      settings:
        Publish Template: alias_asset_stp_publish
    - name: Publish WREF to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_translation.py"
      settings:
        Publish Template: alias_asset_wref_publish
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

################################################################################

# ---- VRED

# asset_step
settings.tk-multi-publish2.vred.asset_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
    Work Template: vred_asset_work
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      settings:
        Publish Template: vred_asset_publish
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session_geometry.py"
      settings:
        Publish Template: vred_asset_geometry_publish
    - name: Publish Rendering to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_rendering.py"
      settings:
        Publish Image Template: vred_asset_render_publish
        Publish Sequence Template: vred_asset_render_sequence_publish
    - name: Upload for review
      hook: "{self}/upload_version.py:{engine}/tk-multi-publish2/basic/upload_version.py"
      settings: { }
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

################################################################################

# --- Harmony

# asset step
settings.tk-multi-publish2.harmony.asset_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py:{config}/tk-multi-publish2/harmony/collector_face_reference.py:{config}/tk-multi-publish2/harmony/collector_asset_sequence_multi.py:{config}/tk-multi-publish2/harmony/collector.py:{config}/tk-multi-publish2/harmony/collector_tpl.py:{config}/tk-multi-publish2/harmony/collector_color_palettes.py"

  collector_settings:
    Work Template: harmony_asset_work
    Publish Template: harmony_asset_publish

    Work Frames Folder Template: harmony_asset_work_frames_folder
    Publish Frames Template: asset_review_publish_proxy
    Image Sequence Workflow: individual
    Beauty rendered name: final
    Beauty register layer: default

    Shape Proxy Work Template: asset_work_proxy_custom_shape_image
    Shape Render Work Template: asset_work_render_custom_shape_image
    Shape Proxy Publish Library Template: asset_publish_proxy_custom_shape_library_image
    Shape Render Publish Library Template: asset_publish_render_custom_shape_library_image
    Shape Proxy Publish Version Template: asset_publish_proxy_custom_shape_image
    Shape Render Publish Version Template: asset_publish_render_custom_shape_image

    Work asset Seq Template: harmony_asset_work_sequence_png
    Publish asset Seq Template: asset_review_sequence_publish
    Publish asset MOV Template: asset_review_publish

    Work Face Ref Template: harmony_asset_work_face_reference
    Publish Face Ref Seq Template: asset_review_sequence_publish
    Publish Face Video Seq Template: asset_review_publish

    Work TPL Template: harmony_asset_work_tpl
    Publish TPL Template: harmony_asset_publish_tpl
    TPL Allowed Tasks: ["Rig"]

    Publish Color Palette Template: harmony_asset_publish_color_palette
    Color Palettes Allowed Tasks: ["rigColorPalettes"]
    Color Palettes Allowed Contexts: ["Asset"]

  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{config}/tk-multi-publish2/harmony/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook:  "{self}/publish_file.py:{config}/tk-multi-publish2/harmony/basic/publish_session.py:{config}/tk-multi-publish2/multi/framework_validations.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      settings:
        Publish Template: harmony_asset_publish
        Verification elements:
          - sg_working_color_space
        Copy to Publish folders:
          - audio
          - elements
          - environments
          - jobs
          - palette-library
          - scripts
        Audio validations: [ ]
        Color Spaces: "@settings.color-spaces.harmony"

    - name: Publish Face reference sequence
      hook: "{self}/publish_file.py:{config}/tk-multi-publish2/harmony/publish_face_ref_sequence.py"
      settings: { }
    - name: Publish Face reference video
      hook: "{self}/publish_file.py:{config}/tk-multi-publish2/harmony/publish_face_ref_video.py"
      settings: { }

    # - name: Publish Asset Sequence
    #   hook: "{self}/publish_file.py:{config}/tk-multi-publish2/harmony/publish_asset_sequence.py"
    #   settings: { }

    # - name: Publish Asset Video
    #   hook: "{self}/publish_file.py:{config}/tk-multi-publish2/harmony/publish_asset_video.py"
    #   settings: { }

    - name: Publish Asset TPL
      hook: "{self}/publish_file.py:{config}/tk-multi-publish2/harmony/publish_tpl.py"
      settings:
        Ignored TPL Palettes: ["Slider_Pipe.plt", "line_thickness_slider.plt"]


    - name: Publish Pieces
      hook: "{self}/publish_file.py:{config}/tk-multi-publish2/harmony/publish_pieces.py"
      settings:
        Work Template: harmony_asset_work
        Proxy Work Template: asset_work_proxy_custom_shape_image
        Render Work Template: asset_work_render_custom_shape_image
        Proxy Publish Library Template: asset_publish_proxy_custom_shape_library_image
        Render Publish Library Template: asset_publish_render_custom_shape_library_image
        Proxy Publish Version Template: asset_publish_proxy_custom_shape_image
        Render Publish Version Template: asset_publish_render_custom_shape_image

    - name: Publish Asset sequence files
      hook: "{config}/tk-multi-publish2/multi/publish_frames_sequence_asset.py"
      settings:
        Work Template: harmony_asset_work
        Primary Publish Template: harmony_asset_publish
        Publish Render Template: asset_review_publish_proxy
        Publish Render Type: Rendered Image
        Image Sequence Workflow: individual
        Invalid Image Extensions: ['sgi', 'jpg']
      # Resolution Validation Ignore Tasks: ["render"]

    - name: Publish Asset video file
      hook: "{config}/tk-multi-publish2/multi/publish_frames_video_asset.py"
      settings:
        Work Template: harmony_asset_work
        Primary Publish Template: harmony_asset_publish
        Publish Render Template: asset_review_publish_proxy
        Publish Review Template: asset_review_publish
        Image Sequence Workflow: individual
        Publish Review Type: Media Review

    - name: Publish color palette files
      hook: "{config}/tk-multi-publish2/harmony/publish_color_palettes.py"
      settings:
        Work Template: harmony_shot_work
        Primary Publish Template: harmony_shot_publish
        Publish Color Palette Template: harmony_asset_publish_color_palette

  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

# sequence step
settings.tk-multi-publish2.harmony.sequence_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
    Work Template: harmony_sequence_work
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      settings:
        Publish Template: harmony_sequence_publish
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

# shot step
settings.tk-multi-publish2.harmony.shot_step:
  collector: "{self}/collector.py:{config}/tk-multi-publish2/harmony/basic/collector.py:{config}/tk-multi-publish2/harmony/basic_render_collector.py:{config}/tk-multi-publish2/harmony/collector_shot_camera.py:{config}/tk-multi-publish2/harmony/collector_shot_pegs.py"
  collector_settings:
    Publish Template: harmony_shot_publish
    Work Template: harmony_shot_work
    Work Frames Folder Template: harmony_shot_work_frames_folder
    Publish Frames Template: shot_review_publish_proxy
    Publish Camera Template: harmony_shot_publish_camera
    Publish Peg Template: harmony_shot_publish_peg
    Image Sequence Workflow: individual
    Beauty rendered name: final
    Beauty register layer: default
    camera_attr_name: "mtyShotCamera"
    camera_attr_type: "BOOL"
    peg_attr_name: "mtyShotTracker"
    peg_attr_type: "BOOL"
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{config}/tk-multi-publish2/harmony/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{config}/tk-multi-publish2/harmony/basic/publish_session.py:{config}/tk-multi-publish2/multi/framework_validations.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      settings:
        Publish Template: harmony_shot_publish
        Verification elements:
          - sg_working_color_space
          - sg_resolution
          - sg_fps
          - frame_range
        Copy to Publish folders:
          - audio
          - elements
          - environments
          - jobs
          - palette-library
          - scripts
        Color Spaces: "@settings.color-spaces.harmony"
        # No Resolution Validation Tasks:
        #   - Render
        Audio validations: [ ]
        # [Exists, Published, Latest]
        Timechart validation: false
        Thumbnail files: [ ]
        id_nodes_dict file name: "id_nodes_dict_tmp"
        id_elements_mapping file name: "id_elements_mapping_tmp"

    - name: Publish sequence files
      hook: "{config}/tk-multi-publish2/multi/publish_frames_sequence.py"
      settings:
        Work Template: harmony_shot_work
        Primary Publish Template: harmony_shot_publish
        Publish Render Template: shot_review_publish_proxy
        Publish Render Type: Rendered Image
        Image Sequence Workflow: individual
        Invalid Image Extensions: ['sgi', 'jpg']
      # Resolution Validation Ignore Tasks: ["render"]

    - name: Publish video file
      hook: "{config}/tk-multi-publish2/multi/publish_frames_video.py"
      settings:
        Work Template: harmony_shot_work
        Primary Publish Template: harmony_shot_publish
        Publish Render Template: shot_review_publish_proxy
        Publish Review Template: shot_review_publish
        Image Sequence Workflow: individual
        Publish Review Type: Media Review
        Editorial Video Opacity: 0.35

    - name: Publish camera file
      hook: "{config}/tk-multi-publish2/harmony/publish_shot_camera.py"
      settings:
        Work Template: harmony_shot_work
        Primary Publish Template: harmony_shot_publish
        # camera_node_color: [5, 5, 255, 255]
        camera_attr_name: "mtyShotCamera"
        camera_attr_type: "BOOL"
        # Allowed Tasks: ["inbetween"]

    - name: Publish tracker peg files
      hook: "{config}/tk-multi-publish2/harmony/publish_shot_tracker_pegs.py"
      settings:
        Work Template: harmony_shot_work
        Primary Publish Template: harmony_shot_publish
        peg_attr_name: "mtyShotTracker"
        peg_attr_type: "BOOL"

  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

################################################################################

# --- Blender

# episode step
settings.tk-multi-publish2.blender.episode_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
      Work Template: blender_episode_work
  publish_plugins:
  - name: Publish to Shotgun
    hook: "{self}/publish_file.py"
    settings: {}
  - name: Upload for review
    hook: "{self}/upload_version.py"
    settings: {}
  - name: Begin file versioning
    hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
    settings: {}
  - name: Publish to Shotgun
    hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py"
    settings:
        Publish Template: blender_episode_publish
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

# asset step
settings.tk-multi-publish2.blender.asset_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
      Work Template: blender_asset_work
  publish_plugins:
  - name: Publish to Shotgun
    hook: "{self}/publish_file.py"
    settings: {}
  - name: Upload for review
    hook: "{self}/upload_version.py"
    settings: {}
  - name: Begin file versioning
    hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
    settings: {}
  - name: Publish to Shotgun
    hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py"
    settings:
        Publish Template: blender_asset_publish
  #- name: Publish to Shotgun
  #  hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session_geometry.py"
  #  settings:
  #      Publish Template: asset_alembic_cache
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

# asset step notask
settings.tk-multi-publish2.blender.asset_step_notask:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
      Work Template: blender_asset_work_notask
  publish_plugins:
  - name: Publish to Shotgun
    hook: "{self}/publish_file.py"
    settings: {}
  - name: Upload for review
    hook: "{self}/upload_version.py"
    settings: {}
  - name: Begin file versioning
    hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
    settings: {}
  - name: Publish to Shotgun
    hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py"
    settings:
        Publish Template: blender_asset_publish_notask
  #- name: Publish to Shotgun
  #  hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session_geometry.py"
  #  settings:
  #      Publish Template: asset_alembic_cache_notask
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

# environment step
settings.tk-multi-publish2.blender.environment_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
      Work Template: blender_environment_work
  publish_plugins:
  - name: Publish to Shotgun
    hook: "{self}/publish_file.py"
    settings: {}
  - name: Upload for review
    hook: "{self}/upload_version.py"
    settings: {}
  - name: Begin file versioning
    hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
    settings: {}
  - name: Publish to Shotgun
    hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py"
    settings:
        Publish Template: blender_environment_publish
  #- name: Publish to Shotgun
  #  hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session_geometry.py"
  #  settings:
  #      Publish Template: environment_alembic_cache
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

# environment step notask
settings.tk-multi-publish2.blender.environment_step_notask:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
      Work Template: blender_environment_work_notask
  publish_plugins:
  - name: Publish to Shotgun
    hook: "{self}/publish_file.py"
    settings: {}
  - name: Upload for review
    hook: "{self}/upload_version.py"
    settings: {}
  - name: Begin file versioning
    hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
    settings: {}
  - name: Publish to Shotgun
    hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py"
    settings:
        Publish Template: blender_environment_publish_notask
  #- name: Publish to Shotgun
  #  hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session_geometry.py"
  #  settings:
  #      Publish Template: environment_alembic_cache_notask
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

# sequence step
settings.tk-multi-publish2.blender.sequence_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
      Work Template: blender_sequence_work
  publish_plugins:
  - name: Publish to Shotgun
    hook: "{self}/publish_file.py"
    settings: {}
  - name: Upload for review
    hook: "{self}/upload_version.py"
    settings: {}
  - name: Begin file versioning
    hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
    settings: {}
  - name: Publish to Shotgun
    hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py"
    settings:
        Publish Template: blender_sequence_publish
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

# shot step
settings.tk-multi-publish2.blender.shot_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
      Work Template: blender_shot_work
  publish_plugins:
  - name: Publish to Shotgun
    hook: "{self}/publish_file.py"
    settings: {}
  - name: Upload for review
    hook: "{self}/upload_version.py"
    settings: {}
  - name: Begin file versioning
    hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
    settings: {}
  - name: Publish to Shotgun
    hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py"
    settings:
        Publish Template: blender_shot_publish
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

################################################################################

# ---- Fusion

# asset step
settings.tk-multi-publish2.fusion.asset_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"

  collector_settings:
    Work Template: fusion_asset_work
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      settings:
        Publish Template: fusion_asset_publish
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

# shot step
settings.tk-multi-publish2.fusion.shot_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py:{config}/tk-multi-publish2/fusion/collector_savers.py"

  collector_settings:
    Work Template: fusion_shot_work
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py:{config}/tk-multi-publish2/fusion/publish_session_master.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      settings:
        Publish Template: fusion_shot_publish
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish sequence files
      hook: "{config}/tk-multi-publish2/fusion/publish_saver_sequence.py"
      settings: { }
    - name: Publish video file
      hook: "{config}/tk-multi-publish2/fusion/publish_saver_video.py"
      settings: { }
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

  # ---- Krita

# asset step
settings.tk-multi-publish2.krita.asset_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
    Work Template: krita_asset_work
    Publish Layers as Folder: false
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      # TODO: fix checklist_validations hook to be compatible with Krita
      # hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py"
      settings:
        Publish Template: krita_asset_publish
    #- name: Publish Layers to Shotgun
    #  hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_layers.py"
    #  settings:
    #      # defines what folder to use as a export
    #      Export Template: krita_asset_layer_export
    #      # defines the name of a layer file
    #      Layer Name Template: krita_layer_name
    #      # defines what folder to use as a publish
    #      Publish Template: krita_asset_layer_publish
    - name: Publish Layer to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_layer.py:{config}/tk-multi-publish2/krita/publish_layer.py"
      settings:
        Publish Template: krita_asset_layer_publish
        # defines the name of the file that will be exported prior being published
        Export Template: krita_asset_layer_export
        # defines the name of the file that will be published
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"


# environment step
settings.tk-multi-publish2.krita.environment_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
    Work Template: krita_environment_work
    Publish Layers as Folder: false
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      # TODO: fix checklist_validations hook to be compatible with Krita
      # hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py"
      settings:
        Publish Template: krita_environment_publish
    #- name: Publish Layers to Shotgun
    #  hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_layers.py"
    #  settings:
    #      # defines what folder to use as a export
    #      Export Template: krita_environment_layer_export
    #      # defines the name of a layer file
    #      Layer Name Template: krita_layer_name
    #      # defines what folder to use as a publish
    #      Publish Template: krita_environment_layer_publish
    - name: Publish Layer to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_layer.py:{config}/tk-multi-publish2/krita/publish_layer.py"
      settings:
        Publish Template: krita_environment_layer_publish
        # defines the name of the file that will be exported prior being published
        Export Template: krita_environment_layer_export
        # defines the name of the file that will be published
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

# environment step notask
settings.tk-multi-publish2.krita.environment_step_notask:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
    Work Template: krita_environment_work_notask
    Publish Layers as Folder: false
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      # TODO: fix checklist_validations hook to be compatible with Krita
      # hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py"
      settings:
        Publish Template: krita_environment_publish_notask
    #- name: Publish Layers to Shotgun
    #  hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_layers.py"
    #  settings:
    #      # defines what folder to use as a export
    #      Export Template: krita_environment_layer_export_notask
    #      # defines the name of a layer file
    #      Layer Name Template: krita_layer_name
    #      # defines what folder to use as a publish
    #      Publish Template: krita_environment_layer_publish_notask
    - name: Publish Layer to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_layer.py:{config}/tk-multi-publish2/krita/publish_layer.py"
      settings:
        Publish Template: krita_environment_layer_publish_notask
        # defines the name of the file that will be exported prior being published
        Export Template: krita_environment_layer_export_notask
        # defines the name of the file that will be published
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

# shot step
settings.tk-multi-publish2.krita.shot_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
    Work Template: krita_shot_work
    Publish Layers as Folder: true
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      # TODO: fix checklist_validations hook to be compatible with Krita
      # hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py"
      settings:
        Publish Template: krita_shot_publish
    - name: Publish Layers to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_layers.py"
      settings:
        # defines what folder to use as a export
        Export Template: krita_shot_layers_export
        # defines the name of a layer file
        Layer Name Template: krita_layer_name
        # defines what folder to use as a publish
        Publish Template: krita_shot_layers_publish
    - name: Publish Layer to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_layer.py"
      settings:
        Publish Template: krita_shot_layer_publish
        # defines the name of the file that will be exported prior being published
        Export Template: krita_shot_layer_export
        # defines the name of the file that will be published
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

  # sequence step
settings.tk-multi-publish2.krita.sequence_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
    Work Template: krita_sequence_work
    Publish Layers as Folder: true
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      # TODO: fix checklist_validations hook to be compatible with Krita
      # hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py"
      settings:
        Publish Template: krita_sequence_publish
    - name: Publish Layers to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_layers.py"
      settings:
        # defines what folder to use as a export
        Export Template: krita_sequence_layers_export
        # defines the name of a layer file
        Layer Name Template: krita_layer_name
        # defines what folder to use as a publish
        Publish Template: krita_sequence_layers_publish
    - name: Publish Layer to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_layer.py"
      settings:
        Publish Template: krita_sequence_layer_publish
        # defines the name of the file that will be exported prior being published
        Export Template: krita_sequence_layer_export
        # defines the name of the file that will be published
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

  # episode step
settings.tk-multi-publish2.krita.episode_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
    Work Template: krita_episode_work
    Publish Layers as Folder: true
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      # TODO: fix checklist_validations hook to be compatible with Krita
      # hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py"
      settings:
        Publish Template: krita_episode_publish
    - name: Publish Layers to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_layers.py"
      settings:
        # defines what folder to use as a export
        Export Template: krita_episode_layers_export
        # defines the name of a layer file
        Layer Name Template: krita_layer_name
        # defines what folder to use as a publish
        Publish Template: krita_episode_layers_publish
    - name: Publish Layer to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_layer.py"
      settings:
        Publish Template: krita_episode_layer_publish
        # defines the name of the file that will be exported prior being published
        Export Template: krita_episode_layer_export
        # defines the name of the file that will be published
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

# --- Substance Painter

# asset step
settings.tk-multi-publish2.substancepainter.asset_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py:{config}/tk-multi-publish2/substance/collector.py"
  collector_settings:
      Work Template: substancepainter_asset_work
      Work Export Template: substancepainter_asset_textures_path_export
      Publish Textures as Folder: false
  publish_plugins:
  - name: Publish to Shotgun
    hook: "{self}/publish_file.py"
    settings: {}
  - name: Publish to Shotgun
    hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_textures.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
    settings:
      Publish Template: substancepainter_asset_textures_path_publish
  - name: Publish to Shotgun
    hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_texture.py:{config}/tk-multi-publish2/substance/publish_texture.py"
    settings:
      Publish Template: substancepainter_asset_texture_path_publish
      Scene Work Template: substancepainter_asset_work
  - name: Upload for review
    hook: "{self}/upload_version.py"
    settings: {}
  - name: Begin file versioning
    hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
    settings: {}
  - name: Publish to Shotgun
    hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py"
    settings:
        Publish Template: substancepainter_asset_publish
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"


################################################################################

# ---- Premiere
# episode step
settings.tk-multi-publish2.premiere.episode_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
    Work Template: premiere_episode_work
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_document.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      settings:
        Publish Template: premiere_episode_publish
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings:
        Movie Output Module: Lossless
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"

# sequence_step
settings.tk-multi-publish2.premiere.sequence_step:
  collector: "{self}/collector.py:{engine}/tk-multi-publish2/basic/collector.py"
  collector_settings:
    Work Template: premiere_sequence_work
  publish_plugins:
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py"
      settings: { }
    - name: Begin file versioning
      hook: "{engine}/tk-multi-publish2/basic/start_version_control.py"
      settings: { }
    - name: Publish to Shotgun
      hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_document.py:{config}/tk-multi-publish2/multi/checklist_validation.py"
      settings:
        Publish Template: premiere_sequence_publish
    - name: Upload for review
      hook: "{self}/upload_version.py"
      settings:
        Movie Output Module: Lossless
  post_phase: "{config}/post_phase.py:{config}/tk-multi-publish2/multi/publish_timelogs.py"
  path_info: "{self}/path_info.py:{config}/tk-multi-publish2/multi/name_from_path_info.py"
  help_url: *help_url
  location: "@apps.tk-multi-publish2.location"
