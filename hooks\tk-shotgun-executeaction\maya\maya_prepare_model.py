#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

"""
An app that prepares mod files for rigging:
    - duplicates the model hierarchy
    - applies a suffix to the duplicated items ("_fb")
    - creates a "notForRender" attribute in all duplicated shapes
    - adds a hash to the transform nodes

"""

from tank import Hook
from tank import TankError
from tank.platform import Application
from tank.platform.qt import QtCore, QtGui
import tank

import os
import hashlib
import re

import maya.cmds as mc

class ProcessItemsHook(Hook):


    # def execute(self, entity_type, entities, other_params, **kwargs):
    def execute(self, **kwargs):

        self.prepare_model()
        return {'succes': [1], 'messages': [], 'errors': []}


    def prepare_model(self):
        main = mc.ls("model_high_root")
        if len(main) > 0:
            main = main[0]
            duplicated_main = '{}_fb'.format(main)
            cluster = mc.cluster(main)
            mc.delete(cluster)
            mc.delete(main, ch=True)
            if not mc.objExists(duplicated_main):
                mc.duplicate(main, name=duplicated_main)

                lista_nombres = mc.listRelatives(main,
                                                 ad=True,
                                                 type='transform')
                lista_obj_01 = mc.listRelatives(main,
                                                ad=True,
                                                type='transform',
                                                f=True)
                lista_obj_02 = mc.listRelatives(duplicated_main,
                                                ad=True,
                                                type='transform',
                                                f=True)

                meshes_with_existing_attr = []
                for i in range(len(lista_nombres)):
                    obj = mc.rename(lista_obj_02[i], '{}_fb'.format(lista_nombres[i]))
                    list_target_mesh = mc.listRelatives(lista_obj_01[i], shapes=True)
                    list_source_mesh = mc.listRelatives(obj, shapes=True, fullPath=True) or []

                    for i in range(len(list_source_mesh)):
                        mc.rename(list_source_mesh[i], "{}_fb".format(list_target_mesh[i]))

                    list_source_mesh = mc.listRelatives(obj, shapes=True)

                    if not list_source_mesh is None:
                        for i in range(len(list_source_mesh)):
                            mc.connectAttr('{}.worldMesh[0]'.format(list_source_mesh[i]),
                                           '{}.inMesh'.format(list_target_mesh[i]))
                            mc.setAttr('{}.visibility'.format(list_source_mesh[i]), 0)

                            # add notForRender attr
                            node = list_source_mesh[i]
                            attr_exists = mc.attributeQuery(
                                "notForRender", node=node, exists=True
                            )
                            if attr_exists == True:
                                meshes_with_existing_attr.append(node)
                                mc.setAttr("{}.notForRender".format(node),
                                           lock=False)
                                mc.setAttr("{}.notForRender".format(node),
                                           True,
                                           keyable=False,
                                           channelBox=True,
                                            lock=False)
                            else:
                                mc.addAttr(node,
                                           longName="notForRender",
                                           attributeType="bool",
                                           defaultValue=True)
                                mc.setAttr("{}.notForRender".format(node),
                                           True,
                                           keyable=False,
                                           channelBox=True,
                                           lock=True)
                if len(meshes_with_existing_attr) > 0:
                    message = "These meshes already have an existing "\
                              "'notForRender' attribute: \n\n" \
                              "{}".format(meshes_with_existing_attr)
                    mc.confirmDialog(title="Warning",
                                     message=message,
                                     button=["Ok"],
                                     defaultButton="Ok",
                                     cancelButton="Ok",
                                     dismissString="Ok")

                mc.setAttr('{}.hiddenInOutliner'.format(duplicated_main), 1)
                mc.setAttr('{}.v'.format(duplicated_main), 0)
                mc.warning("CONEXION ESTABLECIDA")

            else:
                mc.warning("YA EXISTE LA CONEXION")

            # Add ID_file attr (hash)
            ID_attr = '{}.ID_file'.format(main)
            ID_file_attr_exists = mc.attributeQuery("ID_file",
                                                    node=main,
                                                    exists=True)
            if not ID_file_attr_exists:
                mc.addAttr(main, longName='ID_file', dataType='string')
            else:
                mc.setAttr(ID_attr, lock=False)

            fileName = mc.file(q=1, sn=1)
            fileName = os.path.basename(fileName)
            version = re.search("\_\w{1}\d{3}\.", fileName)
            fileName = fileName.replace(version.group(), ".")

            mc.setAttr(ID_attr,
                       hashlib.sha224('{0}_{1}'.format(main, fileName).encode('utf-8')).hexdigest(),
                       type='string')
            mc.setAttr(ID_attr, lock=True)

            # Remove ID_file attr from duplicated hierarchy
            dup_hier_attr = mc.attributeQuery("ID_file",
                                              node=duplicated_main,
                                              exists=True)
            if dup_hier_attr:
                dup_ID_attr = '{}.ID_file'.format(duplicated_main)
                mc.setAttr(dup_ID_attr, lock=False)
                mc.deleteAttr(dup_ID_attr)

        else:
            mc.warning("Couldn't find 'model_high_root'")
