var log = MessageLog.trace;

function reset_expression_columns() {

    var expr_columns = [];

    var number_of_columns = column.numberOf();

    for (i = 0; i< number_of_columns; ++i) {
        var name = column.getName(i);
        var display_name = column.getDisplayName(name);
        var type = column.type(name);
        if (type === "EXPR") {
            var line = "\t" + i + ": \tdisplay name -> " + display_name + ", \ttype ->" + type + ", \tname -> " + name
            log(line);
            expr_columns.push(display_name);
        }
    }

    log("Found " + expr_columns.length + " expression columns");

    for (var c = 0; c < expr_columns.length; c++) {
        var current_column = expr_columns[c];

        log("------------------------------------------");
        log("Reseting expression on column: " + current_column);
        var col_expr = column.getTextOfExpr(current_column);
        // log(col_expr);

        var set_expr_1 = column.setTextOfExpr(current_column, "test");
        log("set temporary expression: " + set_expr_1);

        var tmp = column.getTextOfExpr(current_column);
        // log(tmp);

        set_expr_2 = column.setTextOfExpr(current_column, col_expr);
        log("set back original expression: " + set_expr_2);

        // tmp = column.getTextOfExpr(current_column);
        // log(tmp);

        log("Finished reseting expression on column: " + current_column);
    }
    return null;
}

reset_expression_columns();
