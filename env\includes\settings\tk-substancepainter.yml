# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

# Author: <PERSON>
# Contact: https://www.linkedin.com/in/diegogh/

################################################################################

includes:
- ../app_locations.yml
- ../engine_locations.yml
- ./tk-multi-breakdown.yml
- ./tk-multi-loader2.yml
- ./tk-multi-publish2.yml
- ./tk-multi-screeningroom.yml
- ./tk-multi-shotgunpanel.yml
- ./tk-multi-snapshot.yml
- ./tk-multi-workfiles2.yml

################################################################################

# asset
settings.tk-substancepainter.asset:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel.substancepainter"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.substancepainter.project"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  debug_logging: false
  location: "@engines.tk-substancepainter.location"

# asset_step
settings.tk-substancepainter.asset_step:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-loader2: "@settings.tk-multi-loader2.substancepainter"
    tk-multi-breakdown: "@settings.tk-multi-breakdown.substancepainter"
    tk-multi-publish2: "@settings.tk-multi-publish2.substancepainter.asset_step"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel.substancepainter"
    tk-multi-snapshot: "@settings.tk-multi-snapshot.substancepainter.asset_step"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.substancepainter.asset_step"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset" ]

  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-snapshot, name: Snapshot...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  - {app_instance: tk-multi-publish2, name: Publish...}
  template_project: asset_work_area_substancepainter
  location: "@engines.tk-substancepainter.location"

# project
settings.tk-substancepainter.project:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel.substancepainter"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.substancepainter.project"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    mty-executeaction-ensure-folders:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Ensure Tasks Folders"
      action_hook: "{config}/tk-shotgun-executeaction/create_folders_for_assigned_tasks.py"
      sg_extended_fields: {}
      allowed_entities: ['Shot', 'Sequence', 'Asset', 'Project']
  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  debug_logging: true
  location: "@engines.tk-substancepainter.location"
