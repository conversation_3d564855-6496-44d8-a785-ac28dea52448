#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

import sgtk
from sgtk import TankError

HookBaseClass = sgtk.get_hook_baseclass()

class GetVersionNumber(HookBaseClass):
    """
    Hook called to scan the disk and determine the highest version.
    Given a template and some fields, return the highest version number found on shotgun.
    We build the path, find a publish on shotgun and then get the latest version for it.
    """

    def get_locked_number(self, publishedFile):
        """
        This method seached for the locked version related to a brakedown entity if there is a
        locked publishedFile assigned.

        Returns version number for the locked published file in breakdown entity or None value.
        """

        # Get brakedown entity for context entity
        item_brakedown = self.parent.engine.shotgun.find_one("CustomEntity30",
            [['sg_shot', 'is', self.parent.engine.context.entity],
             ['code', 'is', publishedFile['entity']['name']]],

            ['sg_locked_version.PublishedFile.version_number']
        )

        if item_brakedown and item_brakedown.get('sg_locked_version.PublishedFile.version_number'):
            return item_brakedown.get('sg_locked_version.PublishedFile.version_number')

        return None


    def execute(self, template, curr_fields, **kwargs):
        """
        Main hook entry point.

        :param template: Template object to calculate for
        :param dict curr_fields: A complete set of fields for the template

        :returns: The highest version number found
        :rtype: int
        """

        self.parent.log_debug("Template: %s" % template)
        self.parent.log_debug("Fields: %s" % curr_fields)

        current_path = template.apply_fields(curr_fields)
        self.parent.log_debug("Current Path: %s" % current_path)

        tk = self.parent.tank
        fields = ['entity', 'published_file_type', 'name']
        publish = sgtk.util.find_publish(tk, [current_path], fields=fields)
        publish = publish[current_path]
        self.parent.log_debug("Current Publish: %s" % publish)

        # Return locked version number for breakdown entity
        locked_version_number = self.get_locked_number(publish)
        if locked_version_number:
            return locked_version_number

        # Find latest aproved published file version number
        filters = [['entity', 'is', publish['entity']],
                   ['sg_status_list', 'is', 'apr'],
                   ['published_file_type', 'is', publish['published_file_type']],
                   ['name', 'is', publish['name']]]
        order = [{'field_name':'version_number', 'direction':'desc'}]
        latest = self.parent.shotgun.find_one('PublishedFile', filters, ['version_number'], order=order)

        self.parent.log_debug("Latest Publish: %s" % latest)

        return latest['version_number']
