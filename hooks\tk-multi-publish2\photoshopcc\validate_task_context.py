# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

import os
import pprint
import traceback

import sgtk


HookBaseClass = sgtk.get_hook_baseclass()


class taskValidationPlugin(HookBaseClass):

    ############################################################################
    # standard publish plugin properties

    @property
    def settings(self):
        # inherit the settings from the base publish plugin
        plugin_settings = super(taskValidationPlugin, self).settings or {}

        return plugin_settings

    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish.

        Returns a boolean to indicate validity.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process

        :returns: True if item is valid, False otherwise.
        """

        if self.parent.engine.context.task:
            previous = super(taskValidationPlugin, self).validate(settings, item)
        else:
            raise Exception('Error: Task is missing from context, please close software and restar shotgun desktop.')

        return True
