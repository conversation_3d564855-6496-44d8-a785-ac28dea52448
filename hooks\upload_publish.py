#####################################################################################
#
# Copyright (c) 2021 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

import os
import sgtk
import traceback

HookBaseClass = sgtk.get_hook_baseclass()

class UploadPublish(HookBaseClass):

    # example call:
    # execute_hook_expression(
    #   "{config}/ensure_publish_is_local.py",
    #   "execute", set_progress=set_progress,
    #   publish_id=417217
    # )

    def execute(self, logger, set_progress, synclog):
        try:
            if not hasattr(self, 'metasync'):
                self.metasync = self.load_framework("mty-framework-metasync")

            transfersManager = self.metasync.transfersManager
            set_progress(10)
            transfersManager.process_upload(synclog, logger)
            set_progress(100)
        except:
            #logger.error(traceback.format_exc())
            set_progress(0)
            raise Exception(traceback.format_exc())

