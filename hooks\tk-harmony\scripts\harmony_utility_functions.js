"use strict";

var log = MessageLog.trace;
var pp = JSON.stringify;

var META_SHOTGUN_PATH = "meta.shotgun.path";


// Ensure variables False and True are correctly initialized because they are not being
// translated correctly through the SG bridge of the tk-harmony engine
var False = false;
var True = true;


function get_scene_root() {
    return scene.currentProjectPath();
}

function set_node_metadata(node_name, attr_name, value) {
    var visualAttrName = attr_name;
    var idx = attr_name.lastIndexOf(".");
    if (idx >= 0) {
      visualAttrName = attr_name.substr(idx + 1);
    }

    var attr = node.getAttr(node_name, 1.0, attr_name);
    if(attr.keyword() == "") {
        if (node.createDynamicAttr(node_name, "STRING", attr_name, visualAttrName, false)) {
          attr = node.getAttr(node_name, 1.0, attr_name);
        }

        if (attr.keyword() != "") {
            node.setTextAttr(node_name, attr_name, 1.0, value || visualAttrName);
        }
    } else {
        node.setTextAttr(node_name, attr_name, 1.0, value || visualAttrName);
    }
}

function get_all_nodes_by_type(node_type) {
    var nodes = node.getNodes([node_type]);

    if (!nodes) {
        return [];
    }

    return nodes;
}

function filter_enabled_nodes(nodes_array) {
    var enabled_nodes = [];
    for (var i = 0; i < nodes_array.length; i++) {
        var current_node = nodes_array[i];
        if (node.getEnable(current_node)) {
            enabled_nodes.push(current_node);
        }
    }

    return enabled_nodes;
}

function get_disabled_nodes_by_type(node_type) {
    var nodes = node.getNodes([node_type]);
    var enabled_nodes = [];
    for (var i = 0; i < nodes.length; i++) {
        var current_node = nodes[i];
        if (!node.getEnable(current_node)) {
            enabled_nodes.push(current_node);
        }
    }

    return enabled_nodes;
}

function filter_nodes_by_type(nodes_array, node_type) {
    filtered_nodes_array = [];
    for (var i = 0; i < nodes_array.length; i++) {
        var current_node = nodes_array[i];
        if (node.type(current_node) == node_type) {
            // log("found filtered node: " + current_node);
            filtered_nodes_array.push(current_node);
        }
    }
    return filtered_nodes_array;
}

function get_enabled_nodes_by_type(node_type) {
    // var nodes_array = get_all_nodes_by_type(node_type);
    var nodes_array = get_nodes_and_subnodes(node_type, ["Top"]);
    var enabled_nodes = filter_enabled_nodes(nodes_array);

    return enabled_nodes;
}

function filter_nodes_by_prefix(nodes_array, name_prefix) {
    var filtered_nodes = [];

    for (var i = 0; i < nodes_array.length; i++) {
        var current_node = nodes_array[i];
        var current_node_name = node.getName(current_node);
        if (name_prefix !== undefined) {
            if (custom_startswith(current_node_name, name_prefix, false)) {
                filtered_nodes.push(current_node);
            }
        } else {
            filtered_nodes.push(current_node);
        }
    }

    return filtered_nodes;
}

function filter_nodes_by_attribute(nodes_array, attr_name) {
    var filtered_nodes = [];

    for (var i = 0; i < nodes_array.length; i++) {
        var current_node = nodes_array[i];
        var attr =  node.getAttr(current_node, frame.current(), attr_name);
        if (attr.name() !== "") {
            filtered_nodes.push(current_node);
        }
    }

    return filtered_nodes;
}

function get_write_nodes_output_paths(list_of_nodes) {

    var output_paths = [];

    var image_extensions = {
        "TGA": ".tga",
        "TGA4": ".tga",
        "TGA3": ".tga",
        "TGA1": ".tga",
        "SGI": ".sgi",
        "SGI4": ".sgi",
        "SGI3": ".sgi",
        "SGI1": ".sgi",
        "SGIDP4": ".sgi",
        "SGIDP3": ".sgi",
        "PSD": ".psd",
        "PSD4": ".psd",
        "PSD3": ".psd",
        "PSD1": ".psd",
        "PSDDP4": ".psd",
        "PSDDP3": ".psd",
        "PNG": ".png",
        "PNG4": ".png",
        "PNGDP4": ".png",
        "PNGDP3": ".png",
        "JPG": ".jpg",
        "TIF": ".tif",
        "BMP": ".bmp",
        "BMP4": ".bmp",
        "EXR": ".exr",
        "EXR_ZIP_1LINE": ".exr",
    };

    // get the current scene root
    var scene_root = get_scene_root();

    // iterate through the nodes to get the relevant attributes to build the
    // output path for each one of those nodes.
    for (var i = 0; i < list_of_nodes.length; i++) {
        var current_node = list_of_nodes[i];

        // get output extension
        var out_extension_attr = node.getAttr(current_node, 1, "DRAWING_TYPE");
        var out_extension_key = out_extension_attr.textValue();
        if (image_extensions.hasOwnProperty(out_extension_key)) {
            var out_extension = image_extensions[out_extension_key];
        } else {
            // if out_extension_key doesn't exist in the image_extensions associative
            // array keys, then we skip this node as we can't process it.
            continue;
        };

        // get relative output path
        var relative_path_attr = node.getAttr(current_node, 1, "DRAWING_NAME");
        var relative_path = relative_path_attr.textValue();

        // get leading zeros value
        var leading_zeros_attr = node.getAttr(current_node, 1, "LEADING_ZEROS");
        var leading_zeros = leading_zeros_attr.intValue();

        // get first frame value
        var start_frame_attr = node.getAttr(current_node, 1, "START");
        var start_frame = start_frame_attr.intValue();

        var padded_string = ""
        for (var n = 0; n < leading_zeros; n++) {
            padded_string += "0";
        }
        padded_string += start_frame.toString();

        // log(scene_root);
        // log(relative_path);
        // log(leading_zeros);
        // log(start_frame);
        // log(padded_string);
        // log(out_extension);

        var sequence_basename = relative_path + padded_string + out_extension;
        var sequence_path = scene_root + "/" + sequence_basename;

        // create a new item in the result array with the relevant information
        output_paths.push(sequence_path);
    }

    return output_paths;
}

function get_number_of_read_nodes() {
    var original_selection = selection.selectedNodes();
    Action.perform("onActionPropagateLayerSelection()", "timelineView");
    var selected_nodes = selection.selectedNodes();
    var read_nodes = [];
    for (var i = 0; i < selected_nodes.length; i++) {
        var current_node = selected_nodes[i];
        if (node.type(current_node) == "READ") {
            read_nodes.push(current_node);
        }
    }
    selection.clearSelection();
    selection.addNodesToSelection(original_selection);

    log("number of read nodes: " + read_nodes.length);

    return read_nodes.length;
}

function get_selected_nodes() {
    var original_selection = selection.selectedNodes();
    return original_selection;
}

function create_node(node_type, prev_node, color, node_name, xOffset, yOffset, current_node, share_parent) {
    // if (node_type == "PEG") {
    try {
        var parent_node = node.parentNode(current_node);
        var prev_node_Xpos = node.coordX(current_node);
        // prev_node_Xpos += (node.width(current_node) / 2);
        var prev_node_Ypos = node.coordY(current_node);
        var prev_node_Zpos = node.coordZ(current_node);

        var new_node = node.add(
            parent_node,
            node_name,
            node_type,
            prev_node_Xpos - xOffset,
            prev_node_Ypos + yOffset,
            prev_node_Zpos
        );
    } catch (error) {
        log("Couldn't create node: " + error);
        return null;
    }

    if (share_parent == true) {
        node.link(prev_node, 0, new_node, 0);
    } else {
        node.link(current_node, 0, new_node, 0);
    }
    node.setColor(new_node, color);
    log("Created node: " + node.getName(new_node));

    return new_node;
}

function copy_node_pivot(source_node, target_node) {
    // get the pivot values of the source node
    var source_pivot_x_attr = node.getAttr(source_node, frame.current(), "pivot.x");
    var source_pivot_x_value = source_pivot_x_attr.doubleValue();
    var source_pivot_y_attr = node.getAttr(source_node, frame.current(), "pivot.y");
    var source_pivot_y_value = source_pivot_y_attr.doubleValue();
    try {
        var source_pivot_z_attr = node.getAttr(source_node, frame.current(), "pivot.z");
        var source_pivot_z_value = source_pivot_z_attr.doubleValue();
    } catch (error) {
        log("Couldn't get source pivot z: " + error);
        var source_pivot_z_value = 0;
    }

    // set the pivot of the target node
    var target_pivot_x_attr = node.getAttr(target_node, frame.current(), "pivot.x");
    target_pivot_x_attr.setValue(source_pivot_x_value);
    var target_pivot_y_attr = node.getAttr(target_node, frame.current(), "pivot.y");
    target_pivot_y_attr.setValue(source_pivot_y_value);
    try {
        var target_pivot_z_attr = node.getAttr(target_node, frame.current(), "pivot.z");
        target_pivot_z_attr.setValue(source_pivot_z_value);
    } catch (error) {
        log("Couldn't get target pivot z: " + error);
    }
}

function create_attr(node_name, attr_name, attr_type) {
    var attr = node.getAttr(node_name, frame.current(), attr_name);
    if (attr.keyword() == "") {
        var visual_attr_name = attr_name;
        if (node.createDynamicAttr(node_name, attr_type, attr_name, visual_attr_name, false)) {
            attr = node.getAttr(node_name, frame.current(), attr_name);
        };
        if (attr.keyword() != "") {
            node.setTextAttr(node_name, attr_name, frame.current(), "true");
        };
    };
    return attr;
}

function get_attr_value(node_name, attr_name) {
    var attr = node.getAttr(node_name, 1.0, attr_name);

    log("---------------------------");
    var attr_name_from_obj = attr.name();
    if (attr_name_from_obj == "") {
        log("Attribute '" + attr_name + "' doesn't exit.");
        return null;
    }
    log("name: \t" + attr.name());
    log("keyword: \t" + attr.keyword());
    log("fullKeyword: \t" + attr.fullKeyword());
    log("type: \t" + attr.typeName());

    switch (attr.typeName()) {
        case "STRING":
            return attr.textValue();
        case "INT":
            return attr.intValue();
        case "DOUBLE":
            return attr.doubleValue();
        case "DOUBLEVB":
            return attr.doubleValue();
        case "DOUBLEVB":
            return attr.doubleValue();
        case "BOOL":
                return attr.boolValue();
        case "GENERIC_ENUM":
            return attr.textValue();
        case "ELEMENT":
            return attr.textValue();
        case "CUSTOM_NAME":
            return attr.textValue();
        case "DRAWING":
            return attr.textValue();
        case "ENABLE":
            return attr.textValue();
        default:
            return null;
    }

    // if (attr.typeName() == "POSITION_3D") {
    //     log_subattributes_info(attr);
    // };
    // if (attr.typeName() == "SCALE_3D") {
    //     log_subattributes_info(attr);
    // };
    // if (attr.typeName() == "ROTATION_3D") {
    //     log_subattributes_info(attr);
    // };
    // if (attr.typeName() == "DYN_GROUP") {
    //     log_subattributes_info(attr);
    // };
    // if (attr.typeName() == "PATH_3D") {
    //     log_subattributes_info(attr);
    // };
    // if (attr.typeName() == "QUATERNION_PATH") {
    //     log_subattributes_info(attr);
    // };

    // if (attr.typeName() == "DRAWING") {
    //     log_subattributes_info(attr);
    // };

    // if (attr.typeName() == "ELEMENT") {
    //     log_subattributes_info(attr);
    // };

    // if (attr.typeName() == "CUSTOM_NAME") {
    //     log_subattributes_info(attr);
    // };
};

function convert_to_lowerCamelCase(str) {
    // Replace all characters in the group "_ -/" with an empty string
    str = str.replace(/[_\-\/\s]+(.)?/g, function(match, c) { return c ? c.toUpperCase() : ''; });

    // Make the first character lowercase
    return str.charAt(0).toLowerCase() + str.slice(1);
}

function get_nodes_and_subnodes(node_type, nodes_array) {
    // Check if nodes_array is undefined or null, and set it to an empty array
    nodes_array = nodes_array || [];
    var filtered_nodes = [];
    for (var i = 0; i < nodes_array.length; i++) {
        // log(nodes_array[i]);
        var current_node = nodes_array[i];
        if (node.type(current_node) == node_type) {
            filtered_nodes.push(current_node);
        } else if (node.isGroup(current_node)) {
            var group_subnodes = node.subNodes(current_node);
            // Concatenate the results from the recursive call to filtered_nodes
            filtered_nodes = filtered_nodes.concat(get_nodes_and_subnodes(node_type, group_subnodes));
        }
    }
    return filtered_nodes;
}

function get_all_group_nodes_in_scene(group_node, group_nodes_array) {
    // groups_node can be "Top" to get all nodes in the scene or any other specific
    // group if you want to get subnodes from an specific group

    // Check if nodes_array is undefined or null, and set it to an empty array
    group_nodes_array = group_nodes_array || [];

    var all_subnodes_nodes = node.subNodes(group_node);
    for (var i = 0; i < all_subnodes_nodes.length; i++) {
        var current_node = all_subnodes_nodes[i];
        log("current node: " + current_node);
        if (node.type(current_node) === "GROUP") {
            group_nodes_array.push(current_node);
            group_nodes_array = get_all_group_nodes_in_scene(current_node, group_nodes_array);
        }
    }
    return group_nodes_array;
}

function filter_nodes(nodes_array, node_type) {
    var filtered_nodes = [];
    for (var i = 0; i < nodes_array.length; i++) {
        var current_node = nodes_array[i];
        if (node.type(current_node) === node_type) {
            filtered_nodes.push(current_node);
        }
    }
    return filtered_nodes;
}

function filter_vector_read_nodes(read_nodes_array) {
    var filtered_nodes = [];
    for (var i = 0; i < read_nodes_array.length; i++) {
        var current_node = read_nodes_array[i];
        var element_id = node.getElementId(current_node);
        var vector_type = element.vectorType(element_id);
        if (vector_type !== 0) {
            filtered_nodes.push(current_node);
        }
    }
    return filtered_nodes;
}

function get_main_display_node() {
    var log = MessageLog.trace;
    include("harmony_utility_functions.js");

    // get root (Top) subnodes
    var top_nodes = node.subNodes("Top");
    // filter subnodes, only Display nodes are kept
    var top_display_nodes = filter_nodes(top_nodes, "DISPLAY")

    log(JSON.stringify(top_display_nodes, null, 2));

    // set default display node variable
    var main_display_node = "Top/Display";

    // set temporary fallback default display node variable (temporary solution for
    // scenes where the main display node has been deleted and the current displa node
    // is called 'Display_1')
    if (node.getName(main_display_node) === "") {
        var main_display_node = "Top/Display_1";
    }

    // check if the default display node exists in the scene
    var display_index = top_display_nodes.indexOf(main_display_node)

    if (display_index === -1) {
        log(main_display_node + " not found in array, using first node from array");
        main_display_node = top_display_nodes[0];
    } else {
        log(main_display_node + "found in array, index: " + display_index);
    }

    return main_display_node;
}

function custom_startswith(main_string, prefix, case_sensitive) {
    /**
     * Checks if a string starts with a specific prefix.
     *
     * @param {string} main_string - The main string to check.
     * @param {string} prefix - The prefix to check for.
     * @param {boolean} case_sensitive - Whether the comparison should be case sensitive.
     * @return {boolean} Returns true if the main_string starts with the prefix, false otherwise.
     */

    // set default value for case_sensitive
    if (case_sensitive === undefined) {
        case_sensitive = true;
    }

    if (case_sensitive === false) {
        main_string = main_string.toLowerCase();
        prefix = prefix.toLowerCase();
    }

    if (main_string.length < prefix.length) {
        return false;
    }

    for (var i = 0; i < prefix.length; i++) {
        if (main_string[i] !== prefix[i]) {
            return false;
        }
    }

    return true;
}

function get_group_node(nodes_list) {
    for (var i = 0; i < nodes_list.length; ++i) {
        if (node.type(nodes_list[i]) == "GROUP") {
            return nodes_list[i]
        }
    }
}

function get_main_peg(group_node) {
    var multi_port_in_node = group_node + "/Multi-Port-In";
    var main_peg_node = node.dstNode(multi_port_in_node, 0, 0);

    return main_peg_node;
}

function import_tpl(tpl_path) {
    // get the scene good position, this means where there are no nodes, before
    // importing the tpl to avoid them to be taken in account for the current scene
    // boundings
    var good_position = get_good_scene_position(0, 100);
    log("scene good position:\n" + JSON.stringify(good_position, null, 4));

    // save the existing backdrops to know which ones are new after importing the tpl,
    // so we can set them as movable
    var scene_root = node.root();
    var existing_scene_root_backdrops = Backdrop.backdrops(scene_root);

    log("------------------------------------------");
    log("existing scene root backdrops:\n" + JSON.stringify(existing_scene_root_backdrops, null, 4));

    var copyOpt = copyPaste.getCurrentCreateOptions();
    var pasteOpt = copyPaste.getCurrentPasteOptions();
    var templateCopy = copyPaste.copyFromTemplate(tpl_path, 0, 0, copyOpt);
    copyPaste.pasteNewNodes(templateCopy, "", pasteOpt);
    var tpl_nodes = selection.selectedNodes();

    if (good_position.x != 0 && good_position.y != 0) {
        // find out what nodes and backdrops are movable
        var movable_objects_dict = get_movables_dict_from_nodes_array(
            tpl_nodes, existing_scene_root_backdrops
        );
        // get the top left position to calculate the offset position for all movable objects
        var top_left_position = get_top_left_position(movable_objects_dict);
        log("top left position:\n" + JSON.stringify(top_left_position, null, 4));

        // get the offset position
        var offset_position = calculate_offset(top_left_position.x_pos, top_left_position.y_pos, good_position);
        log("offset position:\n" + JSON.stringify(offset_position, null, 4));

        // set the offset position
        set_offset_position(movable_objects_dict, offset_position);
    }

    // We need to add the metadata to the main peg instead of the group as it doesn't
    // the main tpl group doesn't maintain any custom attribute between sessions. If metadata
    // is added to a main tpl group, it lives just for the current session, but reopening
    // the scene will remove the attribute.
    var tpl_group_node = get_group_node(tpl_nodes);
    var main_peg_node = get_main_peg(tpl_group_node);
    set_node_metadata(main_peg_node, META_SHOTGUN_PATH, tpl_path);

    // log("tpl_nodes");
    // log(tpl_nodes);

    return tpl_nodes;
}

function verify_if_node_exists(node_path) {
    var node_name = node.getName(node_path);
    if (node_name == node.noNode()) {
        return false;
    } else {
        return true;
    }
}

function rename_node(node_path, new_node_name) {
    // first we check if the new name is valid
    var node_parent = node.parentNode(node_path);
    var new_node_path = node_parent + "/" + new_node_name;
    var tmp_node_name = node.getName(new_node_path);

    log(typeof(tmp_node_name));

    // if tmp_node_name is not an empty string, then the new name is not valid
    if (tmp_node_name !== node.noNode()) {
        log("invalid node name (node already exists)");
        return false;
    }

    if (tmp_node_name !== new_node_name) {
        node.rename(node_path, new_node_name);
        log("renamed node " + node_path + " to " + new_node_name);
    }

    return new_node_path;
}

function modify_attr(nodes_array, attr_name, value) {
    try {
        for (var i = 0; i < nodes_array.length; i++) {
            var node_attr = node.getAttr(nodes_array[i], frame.current(), attr_name);
            node_attr.setValue(value);
        }
        return true;
    } catch (err) {
        return false;
    }
}

function get_slider_data(slider_node) {
    var ui_data_attr = node.getAttr(slider_node, frame.current(), "uiData");
    var ui_data_attr_value = ui_data_attr.textValue();
    log(ui_data_attr_value);
    var json_obj = JSON.parse(ui_data_attr_value);

    return json_obj;
}

function get_unique_column_name(column_prefix) {
  var suffix = 0;
  // finds if unique name for a column
  var column_name = column_prefix;
  while(suffix < 2000) {
      if (!column.type(column_name)) {
          break;
      }

      suffix = suffix + 1;
      column_name = column_prefix + "_" + suffix;
  }
  return column_name;
}

function create_expression_column(column_name) {
    var unique_column_name = get_unique_column_name(column_name);
    var expr_column_created = column.add(unique_column_name, "EXPR");
    if (!expr_column_created) {
        return null;
    } else {
        return unique_column_name;
    }
}

function copy_file(srcFilename, dstFilename) {
  var srcFile = new PermanentFile(srcFilename);
  var dstFile = new PermanentFile(dstFilename);
  srcFile.copy(dstFile);
}

function import_image_sequence(publish_path, img_folder, img_name, img_extension, img_count, scale_multiplier) {
    // Create a new read in the scene

    var good_position = get_good_scene_position(0, 100);
    var read = node.add("/Top", img_name, "READ", good_position.x, good_position.y, 0);
    // log("read: " + read);

    // add publish path as metadata
    set_node_metadata(read, META_SHOTGUN_PATH, publish_path)

    // Set attributes similar to "project resolution" to keep the relative sizes of
    // both, the image sequence and the Harmony canvas, consistent (1 pixel == 1 pixel)
    var image_scale_x = node.getAttr(read, frame.current(), "scale.x");
    var image_scale_y = node.getAttr(read, frame.current(), "scale.y");
    image_scale_x.setValue(scale_multiplier);
    image_scale_y.setValue(scale_multiplier);

    var alignmentAttr = node.getAttr(read, frame.current(), "ALIGNMENT_RULE");
    alignmentAttr.setValue("As Is");

    // Create a new element in the scene
    var element_id = element.add(img_name, "COLOR", scene.numberOfUnitsZ(), img_extension.toUpperCase(), 0);
    // log("element_id: " + element_id);

    // Create a new unique column
    var unique_column_name = get_unique_column_name(img_name);
    // log("unique_column_name: " + unique_column_name);
    column.add(unique_column_name, "DRAWING");
    column.setElementIdOfDrawing(unique_column_name, element_id);

    node.linkAttr(read, "DRAWING.ELEMENT", unique_column_name);

    for (var i = 1; i <= img_count; i++) {
        var message = "Importing: " + img_name;
        message += "\nCreating drawing " + i.toString() + " of " + img_count.toString();
        // progress_callback(message);
        log(message);

        var timing = i.toString();
        // unfortunately MovieImport.image(i) does not work as it gives us
        // the images in the wrong order. luckily we can recreate the filename
        // easily...
        var image_path = img_folder + img_name + "-" + timing + "." + img_extension;
        // log("image_path: " + image_path);

        // create a drawing drawing, 'true' indicate that the file exists.
        Drawing.create(element_id, timing, true);

        // get the actual path, in tmp folder.
        var drawing_file_path = Drawing.filename(element_id, timing);
        // log("drawing_file_path: " + drawing_file_path);

        // Create a copy of the image qith the right namimng.
        copy_file(image_path, drawing_file_path);
        column.setEntry(unique_column_name, 1, i, timing);
    }

    var message = "Importing sequence: " + img_name;
    message += "\nDone.";
    // last one with a timer to close the show busy dialog automatically
    // progress_callback(message, 3000);
    log(message);

    // name of the new drawing layer.
    return read;
}

function reset_expression_columns() {

    var expr_columns = [];

    var number_of_columns = column.numberOf();

    for (i = 0; i< number_of_columns; ++i) {
        var name = column.getName(i);
        var display_name = column.getDisplayName(name);
        var type = column.type(name);
        if (type === "EXPR") {
            var line = "\t" + i + ": \tdisplay name -> " + display_name + ", \ttype ->" + type + ", \tname -> " + name
            log(line);
            expr_columns.push(display_name);
        }
    }

    log("Found " + expr_columns.length + " expression columns");

    for (var c = 0; c < expr_columns.length; c++) {
        var current_column = expr_columns[c];

        log("------------------------------------------");
        log("Reseting expression on column: " + current_column);
        var col_expr = column.getTextOfExpr(current_column);
        // log(col_expr);

        var set_expr_1 = column.setTextOfExpr(current_column, "test");
        log("set temporary expression: " + set_expr_1);

        var tmp = column.getTextOfExpr(current_column);
        // log(tmp);

        set_expr_2 = column.setTextOfExpr(current_column, col_expr);
        log("set back original expression: " + set_expr_2);

        // tmp = column.getTextOfExpr(current_column);
        // log(tmp);

        log("Finished reseting expression on column: " + current_column);
    }
}

function custom_basename(filename) {
    var pos = filename.lastIndexOf( ".");
    if (pos >= 0) {
        filename = filename.substr(0,pos);
    }
    var name = filename.split("/");
    if (name.length > 0) {
        name = name[ name.length-1];
    }
    return  name;
}

function get_write_nodes_output_names(list_of_nodes) {

    var output_paths = [];

    // iterate through the nodes to get the relevant attributes to build the
    // output path for each one of those nodes.
    for (var i = 0; i < list_of_nodes.length; i++) {
        var current_node = list_of_nodes[i];
        // get relative output path
        var relative_path_attr = node.getAttr(current_node, 1, "DRAWING_NAME");
        var relative_path = relative_path_attr.textValue();

        // remove "frames/" of the relative path
        var name_split = relative_path.split("/");
        var clean_name = name_split[name_split.length - 1];

        // remove "." of the name
        if (clean_name.lastIndexOf(".") === clean_name.length - 1) {
            var clean_name = clean_name.slice(0, -1);
        }
        // create a new item in the result array with the relevant information
        output_paths.push(clean_name);
    }

    return output_paths;
}

function is_scene_palette_list(palette_list) {
    // Palette list with an element id of -1 are scene palette lists.
    return palette_list.elementId == -1;
}

function get_scene_palette_list() {
    return PaletteObjectManager.getScenePaletteList();
}

function get_scene_palette_list_path() {
    scene_palette_list = get_scene_palette_list();
    return scene_palette_list.getPath();
}

function get_asset_scene_palettes(asset_palette_prefix) {
    // Find all scene palettes that start with the given palette_root

    asset_scene_palettes = {};

    var numPaletteLists = PaletteObjectManager.getNumPaletteLists();
    log("numPaletteLists: " + numPaletteLists);
    for (var i = 0; i < numPaletteLists; ++i) {
        var palette_list = PaletteObjectManager.getPaletteListByIndex(i);
        if (is_scene_palette_list(palette_list)) {
            log("found scene palettes list");
            log("----------------------------------------------");
            for (var j=0; j < palette_list.numPalettes; ++j) {
                var palette = palette_list.getPaletteByIndex(j);
                var palette_name = palette.getName();
                if (palette_name.indexOf(asset_palette_prefix) != -1) {
                    if (!asset_scene_palettes[palette_name]) {
                        asset_scene_palettes[palette_name] = {};
                    }

                asset_scene_palettes[palette_name]["palette_obj"] = palette;
                var palette_path = palette.getPath();
                var palette_full_path = palette_path + "/" + palette_name + ".plt";
                asset_scene_palettes[palette_name]["palette_path"] = palette_full_path;
                }
            }
        }

    }
    return asset_scene_palettes;
}

function get_external_palettes(pipeLib_folder_path, entity_name) {
    var palette_location_codes = {
        69: "ELEMENT",
        74: "JOB",
        83: "SCENE",
        86: "ENVIRONMENT",
        88: "EXTERNAL"
    };
    var scene_folder_path = get_scene_root();

    // Constant containing the path to the external id
    var external_path = PaletteObjectManager.Constants.Location.EXTERNAL;

    var external_palette_names = []; // Array to hold the palette names

    var numPaletteLists = PaletteObjectManager.getNumPaletteLists(); // Get the number of palette lists
    for (var i = 0; i < numPaletteLists; ++i) { // Loop through all palette lists
        var palette_list = PaletteObjectManager.getPaletteListByIndex(i); // Get the palette list

        // Check if the palette list is a scene palette list
        if (is_scene_palette_list(palette_list)) {
            // If it is, log a message
            log("found scene palettes list");
            log("----------------------------------------------");
            for (var j=0; j < palette_list.numPalettes; ++j) { // Loop through all palettes in the list
                var palette = palette_list.getPaletteByIndex(j); // Get the palette
                var palette_name = palette.getName(); // Get the palette name
                var palette_path = palette.getPath(); // Get the palette path
                var palette_location = palette.location; // Get the palette location

                // Log the palette name and path
                log("==========================");
                log("Palette name: " + palette_name);
                log("Palette path: " + palette_path);
                log("Palette location: " + palette_location + ": " + palette_location_codes[palette_location]);
                //   log("==========================");

                // Check if the palette is external
                if (palette_location == external_path &&
                    palette_name.indexOf(entity_name) !== 0 &&
                    palette_path.indexOf(pipeLib_folder_path) !== 0) {
                        external_palette_names.push(palette_name);
                }
            }
        }

        // If there are palettes outside the scene palette folder,
        // return the array of palette names
        if (external_palette_names.length > 0) {
            return external_palette_names;
        }
        // If all palettes are inside the scene palette folder,
        // return false
        else {
            return false;
        }
    }
}

function harmony_is_ready() {
    return true;
}

function scene_is_dirty() {
    return scene.isDirty();
}

function get_scene_boundings() {
    log("");
    log("Getting scene boundings...");

    var scene_root = node.root();
    var root_subnodes = node.subNodes(scene_root);
    // log(JSON.stringify(root_subnodes, null, 2));

    var scene_root_backdrops = Backdrop.backdrops(scene_root);
    // log(JSON.stringify(scene_root_backdrops, null, 2));

    var scene_bounds = {
        "x_min": 0,
        "x_max": 0,
        "y_min": 0,
        "y_max": 0
    }
    // get nodes boundings
    for (var i = 0; i < root_subnodes.length; ++i) {
        var current_node = root_subnodes[i];
        log("-------------------")
        log("current node: " + current_node);
        var node_x_min_pos = node.coordX(current_node);
        var node_y_min_pos = node.coordY(current_node);
        var node_x_max_pos = node.coordX(current_node) + node.width(current_node);
        var node_y_max_pos = node.coordY(current_node) + node.height(current_node);

        // check min x bounding
        if (node_x_min_pos < scene_bounds.x_min) {
            log("found new x min: " + node_x_min_pos + " < " + scene_bounds.x_min);
            scene_bounds.x_min = node_x_min_pos;
        }

        // check max x bounding
        if (node_x_max_pos > scene_bounds.x_max) {
            log("found new x max: " + node_x_max_pos + " > " + scene_bounds.x_max);
            scene_bounds.x_max = node_x_max_pos;
        }

        // check min y bounding
        if (node_y_min_pos < scene_bounds.y_min) {
            log("found new y min: " + node_y_min_pos + " < " + scene_bounds.y_min);
            scene_bounds.y_min = node_y_min_pos;
        }

        // check max y bounding
        if (node_y_max_pos > scene_bounds.y_max) {
            log("found new y max: " + node_y_max_pos + " > " + scene_bounds.y_max);
            scene_bounds.y_max = node_y_max_pos;
        }
    }

    // get backdrops boundings
    for (var i = 0; i < scene_root_backdrops.length; ++i) {
        var current_backdrop = scene_root_backdrops[i];
        log("-------------------")
        log("current_backdrop: " + current_backdrop.title.text);
        var backdrop_x_min_pos = current_backdrop.position.x;
        var backdrop_y_min_pos = current_backdrop.position.y;
        var backdrop_x_max_pos = current_backdrop.position.x + current_backdrop.position.w;
        var backdrop_y_max_pos = current_backdrop.position.y + current_backdrop.position.h;

        // check min x bounding
        if (backdrop_x_min_pos < scene_bounds.x_min) {
            log("found new x min: " + backdrop_x_min_pos + " < " + scene_bounds.x_min);
            scene_bounds.x_min = backdrop_x_min_pos;
        }

        // check max x bounding
        if (backdrop_x_max_pos > scene_bounds.x_max) {
            log("found new x max: " + backdrop_x_max_pos + " > " + scene_bounds.x_max);
            scene_bounds.x_max = backdrop_x_max_pos;
        }

        // check min y bounding
        if (backdrop_y_min_pos < scene_bounds.y_min) {
            log("found new y min: " + backdrop_y_min_pos + " < " + scene_bounds.y_min);
            scene_bounds.y_min = backdrop_y_min_pos;
        }

        // check max y bounding
        if (backdrop_y_max_pos > scene_bounds.y_max) {
            log("found new y max: " + backdrop_y_max_pos + " > " + scene_bounds.y_max);
            scene_bounds.y_max = backdrop_y_max_pos;
        }
    }
    log("scene boundings:\n" + JSON.stringify(scene_bounds, null, 4));

    return scene_bounds;
}

function get_good_scene_position(x_offset, y_offset) {
    var scene_boundings = get_scene_boundings();
    scene_x_min = scene_boundings.x_min;
    scene_y_max = scene_boundings.y_max;

    if (scene_x_min != 0 && scene_y_max != 0) {
        return { "x": scene_x_min + x_offset, "y": scene_y_max + y_offset };
    } else {
        return { "x": 0, "y": 0 };
    }
}

function get_offset(pos_1, pos_2) {
    return Math.round((pos_1 + pos_2) / 2);
}

function calculate_backdrop_boundaries(backdrop) {
    return {
        "left": backdrop.position.x,
        "top": backdrop.position.y,
        "right": backdrop.position.x + backdrop.position.w,
        "bottom": backdrop.position.y + backdrop.position.h
    };
}

// Function to check if one backdrop is contained within another
function is_contained(outer, inner) {
    return inner.left >= outer.left &&
           inner.right <= outer.right &&
           inner.top >= outer.top &&
           inner.bottom <= outer.bottom;
}

function get_movables_dict_from_nodes_array(nodes_array, old_backdrops_list) {
    var movable_dict  = {"nodes": {}, "waypoints": {}, "backdrops": {}};

    // var nodes_array = selection.selectedNodes();
    var nodes_parent = node.root();
    if (nodes_array.length != 0) {
        var nodes_parent = node.parentNode(nodes_array[0]);
    }

    var all_backdrops_list = Backdrop.backdrops(nodes_parent);

    // first we iterate through the nodes to fill the movable dict. By default all nodes
    // are movable. Later, when we iterate through the backdrops, we will decide if the
    // node is movable or not
    for (var i = 0; i < nodes_array.length; ++i) {
        var current_node = nodes_array[i];
        movable_dict["nodes"][node.getName(current_node)] = {
            "node_path": current_node,
            "movable": true,
            "x_pos": node.coordX(current_node),
            "y_pos": node.coordY(current_node),
            "width": node.width(current_node),
            "height": node.height(current_node)
        }
    }

    // then we iterate through the selected waypoints to fill the movable dict
    var selected_waypoints = selection.selectedWaypoints();
    for (var i = 0; i < selected_waypoints.length; ++i) {
        var current_waypoint = selected_waypoints[i];
        movable_dict["waypoints"][current_waypoint] = {
            "node_path": current_waypoint,
            "movable": true,
            "x_pos": waypoint.coordX(current_waypoint),
            "y_pos": waypoint.coordY(current_waypoint),
            "width": null,
            "height": null
        }
    }

    // filter out old backdrops and keep only the new ones
    var new_backdrops_list = [];
    for (var i = 0; i < all_backdrops_list.length; i++) {
        var found = false;
        // log("-------------------");
        // log("current all backdrop:\n" + (JSON.stringify(all_backdrops_list[i], null, 4)));
        for (var j = 0; j < old_backdrops_list.length; j++) {
            // log("----------");
            // log("current old backdrop:\n" + (JSON.stringify(old_backdrops_list[j], null, 4)));
            if (JSON.stringify(all_backdrops_list[i]) === JSON.stringify(old_backdrops_list[j])) {
                found = true;
                break;
            }
        }
        if (!found) {
            new_backdrops_list.push(all_backdrops_list[i]);
        }
    }

    log("------------------------------------------");
    log("number of old backdrops: " + old_backdrops_list.length);
    log("number of all backdrops: " + all_backdrops_list.length);
    log("number of new backdrops: " + new_backdrops_list.length);
    log("------------------------------------------");

    // then we iterate through the backdrops to find its children and we fill the
    // movable dict. Also by default, all backdrops are set to movable
    for (var i = 0; i < all_backdrops_list.length; ++i) {
        var current_backdrop = all_backdrops_list[i];
        for (var j = 0; j < all_backdrops_list.length; ++j) {
            var new_current_backdrop = new_backdrops_list[j];
            if (JSON.stringify(current_backdrop) === JSON.stringify(new_current_backdrop)) {
                // log("-------------------");
                // log("current backdrop: " + current_backdrop.title.text);

                var current_backdrop_children = Backdrop.nodes(current_backdrop);
                // log(JSON.stringify(current_backdrop_children, null, 4));

                for (var k = 0; k < nodes_array.length; ++k) {
                    var current_node = nodes_array[k];
                    movable_dict["backdrops"][current_backdrop.title.text] = {
                        "backdrop_data": current_backdrop,
                        "movable": true,
                        "x_pos": current_backdrop.position.x,
                        "y_pos": current_backdrop.position.y,
                        "width": current_backdrop.position.w,
                        "height": current_backdrop.position.h,
                        "children": current_backdrop_children
                    }
                }
            }
        }
    }

    // the following chunk of code is commented as there's no method in the Harmony API
    // to move individual backdrops. The way it works is by getting all of them and
    // then modify the backdrop data to later set their data again, but for all of them,
    // so basically we need to use Backdrop.setBackdrops() with all the needed data
    // modifications, in our case, we modify the position properties.
    // The reason why this is commented is because it basically sets the background as
    // movable or not, but because of the previous explanation, we need all of the
    // imported backdrops to be movable. Also, because we set the backdrops position
    // and not really "move" the backdrops, we need to move all of the children nodes
    // manually, thus we also commented the chunk where we differentiate between nodes
    // contained in a backdrop and nodes that are not.

    /*
    // we iterate through the movable dict to correctly set the movable property
    // of the nodes
    for (var node_name in movable_dict.nodes) {
        var current_node = movable_dict.nodes[node_name];
        var node_path = current_node["node_path"];

        // iterate over backdrops to check if the node is a child
        for (var outer_backdrop_name in movable_dict.backdrops) {
            var current_backdrop = movable_dict.backdrops[outer_backdrop_name];
            if (current_backdrop.children.indexOf(node_path) != -1) {
                movable_dict.nodes[node_name]["movable"] = false;
                break;
            }
        }
    }

    // finally we need to iterate through the backdrops to set the movable property,
    // based on if it is contained in another backdrop or not
    for (var outer_backdrop_name in movable_dict.backdrops) {
        var outer_backdrop_boundaries = calculate_backdrop_boundaries(
            movable_dict.backdrops[outer_backdrop_name]["backdrop_data"]
        );

        // log(
        //     "outer backdrop " + outer_backdrop_name + " boundaries:\n" + JSON.stringify(
        //         outer_backdrop_boundaries, null, 4
        //     )
        // );

        for (var inner_backdrop_name in movable_dict.backdrops) {
            if (outer_backdrop_name !== inner_backdrop_name) {
                var inner_backdrop_boundaries = calculate_backdrop_boundaries(
                    movable_dict.backdrops[inner_backdrop_name]["backdrop_data"]
                );

                // log(
                //     "inner backdrop " + inner_backdrop_name + " boundaries:\n" + JSON.stringify(
                //         inner_backdrop_boundaries, null, 4
                //     )
                // );

                log("-------------------");
                if (is_contained(outer_backdrop_boundaries, inner_backdrop_boundaries)) {
                    log(inner_backdrop_name + " is contained within " + outer_backdrop_name);
                    movable_dict.backdrops[inner_backdrop_name]["movable"] = false;
                } else {
                    log(inner_backdrop_name + " is not contained within " + outer_backdrop_name);
                }
            }
        }
    }
    */

    log("movable_dict:\n" + JSON.stringify(movable_dict, null, 8));

    return movable_dict;
}

function get_top_left_position(movable_dict) {
    function set_top_left_values(items_dict) {
        for (var node_name in items_dict) {
            var current_item = items_dict[node_name];
            var x_pos = current_item["x_pos"];
            var y_pos = current_item["y_pos"];

            // set initial values for top_left_x and top_left_y if they are null
            if (top_left_x === null && top_left_y === null) {
                top_left_x = x_pos;
                top_left_y = y_pos;
                // top_left_item = current_item;
            }

            // continuew if x_pos and y_pos are the same as top_left_x and top_left_y
            if (x_pos === top_left_x && y_pos === top_left_y) {
                // top_left_item = current_item;
                continue;
            }

            // finally compare the current item's x_pos and y_pos with top_left_x and
            // top_left_y if they are not the same
            if (x_pos < top_left_x) {
                top_left_x = x_pos;
            }
            if (y_pos < top_left_y) {
                top_left_y = y_pos;
                // top_left_item = current_item;
            }
        }
    }

    // var top_left_item = null;
    var top_left_x = null;
    var top_left_y = null;

    set_top_left_values(movable_dict["nodes"]);
    set_top_left_values(movable_dict["waypoints"]);
    set_top_left_values(movable_dict["backdrops"]);

    return {"x_pos": top_left_x, "y_pos": top_left_y};
}

function calculate_offset(obj_x_pos, obj_y_pos, scene_good_position) {
    var x_offset = scene_good_position.x - obj_x_pos;
    var y_offset = scene_good_position.y - obj_y_pos;
    return {"x": x_offset, "y": y_offset};
}

function set_offset_position(movable_objects_dict, offset_position) {
    var scene_root = node.root();
    var scene_root_backdrops = Backdrop.backdrops(scene_root);

    // first we move the backdrops
    for (var backdrop_name in movable_objects_dict.backdrops) {
        var current_backdrop = movable_objects_dict.backdrops[backdrop_name];
        if (current_backdrop.movable === false) {
            continue;
        }
        var current_backdrop_data = current_backdrop.backdrop_data;
        var backdrop_title = current_backdrop_data.title.text;
        var current_backdrop_x = current_backdrop_data.position.x;
        var current_backdrop_y = current_backdrop_data.position.y;
        var current_backdrop_w = current_backdrop_data.position.w;
        var current_backdrop_h = current_backdrop_data.position.h;

        // iterate through the scene root backdrops
        for (var i = 0; i < scene_root_backdrops.length; i++) {
            var current_scene_backdrop = scene_root_backdrops[i];
            var current_scene_backdrop_title = current_scene_backdrop.title.text;
            var current_scene_backdrop_x = current_scene_backdrop.position.x;
            var current_scene_backdrop_y = current_scene_backdrop.position.y;
            var current_scene_backdrop_w = current_scene_backdrop.position.w;
            var current_scene_backdrop_h = current_scene_backdrop.position.h;

            if (
                backdrop_title === current_scene_backdrop_title &&
                current_backdrop_x === current_scene_backdrop_x &&
                current_backdrop_y === current_scene_backdrop_y &&
                current_backdrop_w === current_scene_backdrop_w &&
                current_backdrop_h === current_scene_backdrop_h
            ) {
                var new_backdrop_x = current_backdrop_data.position.x + offset_position.x;
                var new_backdrop_y = current_backdrop_data.position.y + offset_position.y;
                // current_scene_backdrop.position.x = new_backdrop_x;
                // current_scene_backdrop.position.y = new_backdrop_y;
                scene_root_backdrops[i].position.x = new_backdrop_x;
                scene_root_backdrops[i].position.y = new_backdrop_y;

                Backdrop.setBackdrops(scene_root, scene_root_backdrops);
                break;
            }
        }
    }

    // then we move the nodes
    for (var node_name in movable_objects_dict.nodes) {
        var current_node = movable_objects_dict.nodes[node_name];
        if (current_node.movable === false) {
            continue;
        }
        var current_node_path = current_node.node_path;
        var current_node_x = current_node.x_pos;
        var current_node_y = current_node.y_pos;
        var new_node_x = current_node_x + offset_position.x;
        var new_node_y = current_node_y + offset_position.y;

        // set the new node position (coord)
        node.setCoord(current_node_path, new_node_x, new_node_y);
    }

    // then we move the waypoints
    for (var waypoint_name in movable_objects_dict.waypoints) {
        var current_waypoint = movable_objects_dict.waypoints[waypoint_name];
        if (current_waypoint.movable === false) {
            continue;
        }
        var current_waypoint_path = current_waypoint.node_path;
        var current_waypoint_x = current_waypoint.x_pos;
        var current_waypoint_y = current_waypoint.y_pos;
        var new_waypoint_x = current_waypoint_x + offset_position.x;
        var new_waypoint_y = current_waypoint_y + offset_position.y;

        // set the new waypoint position (coord)
        waypoint.setCoord(current_waypoint_path, new_waypoint_x, new_waypoint_y);
    }
}

function get_node_good_position(node_path, x_offset, y_offset) {
    var node_x = node.coordX(node_path);
    var node_y = node.coordY(node_path);
    var node_height = node.height(node_path);
    return {"x": node_x - x_offset, "y": node_y + node_height + y_offset};
}

function insert_node_below(src_node, new_node, x_offset, y_offset) {
    // first get the number of output connections of the source node (one node's output
    // could be connected to several other nodes)
    var src_output_ports = node.numberOfOutputPorts(src_node);
    var src_info = {
        "output_connections": {},
        "position": {
            "x": 0,
            "y": 0
        }
    };

    for (var i = 0; i < src_output_ports; i++) {
        for (var j = 0; j < node.numberOfOutputLinks(src_node, i); j++) {
            var dest_node = node.dstNode(src_node, i, j);
            var dest_node_info = node.dstNodeInfo(src_node, i, j);
            src_info["output_connections"][dest_node] = {
                "src_output_port": i,
                "dest_input_port": dest_node_info.port,
                "original_src_node": src_node
            };
        }
    }

    var src_good_position = get_node_good_position(src_node, 0, 0);

    // get the position of the source node
    // src_info["position"]["x"] = node.coordX(src_node);
    // src_info["position"]["y"] = node.coordY(src_node);
    src_info["position"]["x"] = src_good_position.x;
    src_info["position"]["y"] = src_good_position.y;

    // // get the dimensions of the new node
    var new_node_width = node.width(new_node);
    var new_node_height = node.height(new_node);

    // move new node to a closer position to the source node
    var new_node_x = src_info["position"]["x"] + x_offset - (new_node_width / 2);
    // var new_node_y = src_info["position"]["y"] + y_offset + new_node_height;
    var new_node_y = src_info["position"]["y"] + y_offset;
    node.setCoord(new_node, new_node_x, new_node_y);

    // connect the source node to the new node
    // TODO: support multi output ports nodes
    node.link(src_node, 0, new_node, 0);

    // we need to disconnect the destination nodes from the source node and then
    // replicate their connections with the new node
    for (var dest_node in src_info["output_connections"]) {
        var dest_input_port = src_info["output_connections"][dest_node]["dest_input_port"];
        node.unlink(dest_node, dest_input_port);
        node.link(new_node, 0, dest_node, dest_input_port);
    }

    log("src_info:\n" + JSON.stringify(src_info, null, 4));
}
