# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

################################################################################

includes:
- ../app_locations.yml

################################################################################

# ---- 3dsMax

settings.tk-multi-snapshot.3dsmax.asset_step:
  hook_scene_operation: "{engine}/tk-multi-snapshot/basic/scene_operation.py"
  template_snapshot: max_asset_snapshot
  template_work: max_asset_work
  location: "@apps.tk-multi-snapshot.location"

settings.tk-multi-snapshot.3dsmax.shot_step:
  hook_scene_operation: "{engine}/tk-multi-snapshot/basic/scene_operation.py"
  template_snapshot: max_shot_snapshot
  template_work: max_shot_work
  location: "@apps.tk-multi-snapshot.location"

################################################################################

# ---- Hiero

settings.tk-multi-snapshot.hiero:
  template_snapshot: hiero_project_snapshot
  template_work: hiero_project_work
  location: "@apps.tk-multi-snapshot.location"

################################################################################

# ---- Houdini

# asset step
settings.tk-multi-snapshot.houdini.asset_step:
  template_snapshot: houdini_asset_snapshot
  template_work: houdini_asset_work
  location: "@apps.tk-multi-snapshot.location"

# shot step
settings.tk-multi-snapshot.houdini.shot_step:
  template_snapshot: houdini_shot_snapshot
  template_work: houdini_shot_work
  location: "@apps.tk-multi-snapshot.location"

################################################################################

# ---- Maya

# asset step
settings.tk-multi-snapshot.maya.asset_step:
  template_snapshot: maya_asset_snapshot
  template_work: maya_asset_work
  location: "@apps.tk-multi-snapshot.location"

# asset step
settings.tk-multi-snapshot.maya.asset_step_notask:
  template_snapshot: maya_asset_snapshot_notask
  template_work: maya_asset_work_notask
  location: "@apps.tk-multi-snapshot.location"

# environment step
settings.tk-multi-snapshot.maya.environment_step:
  template_snapshot: maya_environment_snapshot
  template_work: maya_environment_work
  location: "@apps.tk-multi-snapshot.location"

# environment step notask
settings.tk-multi-snapshot.maya.environment_step_notask:
  template_snapshot: maya_environment_snapshot_notask
  template_work: maya_environment_work_notask
  location: "@apps.tk-multi-snapshot.location"

# shot step
settings.tk-multi-snapshot.maya.shot_step:
  template_snapshot: maya_shot_snapshot
  template_work: maya_shot_work
  location: "@apps.tk-multi-snapshot.location"

# sequence step
settings.tk-multi-snapshot.maya.sequence_step:
  template_snapshot: maya_sequence_snapshot
  template_work: maya_sequence_work
  location: "@apps.tk-multi-snapshot.location"

################################################################################

# ---- Nuke

# asset step
settings.tk-multi-snapshot.nuke.asset_step:
  template_snapshot: nuke_asset_snapshot
  template_work: nuke_asset_work
  location: "@apps.tk-multi-snapshot.location"

# shot step
settings.tk-multi-snapshot.nuke.shot_step:
  template_snapshot: nuke_shot_snapshot
  template_work: nuke_shot_work
  location: "@apps.tk-multi-snapshot.location"

################################################################################

# ---- Photoshop

# episode step
settings.tk-multi-snapshot.photoshop.episode_step:
  template_snapshot: photoshop_episode_snapshot
  template_work: photoshop_episode_work
  location: "@apps.tk-multi-snapshot.location"

# asset step
settings.tk-multi-snapshot.photoshop.asset_step:
  template_snapshot: photoshop_asset_snapshot
  template_work: photoshop_asset_work
  location: "@apps.tk-multi-snapshot.location"

# asset step notask
settings.tk-multi-snapshot.photoshop.asset_step_notask:
  template_snapshot: photoshop_asset_snapshot_notask
  template_work: photoshop_asset_work_notask
  location: "@apps.tk-multi-snapshot.location"

# environment step
settings.tk-multi-snapshot.photoshop.environment_step:
  template_snapshot: photoshop_environment_snapshot
  template_work: photoshop_environment_work
  location: "@apps.tk-multi-snapshot.location"

# environment step notask
settings.tk-multi-snapshot.photoshop.environment_step_notask:
  template_snapshot: photoshop_environment_snapshot_notask
  template_work: photoshop_environment_work_notask
  location: "@apps.tk-multi-snapshot.location"

# shot step
settings.tk-multi-snapshot.photoshop.shot_step:
  template_snapshot: photoshop_shot_snapshot
  template_work: photoshop_shot_work
  location: "@apps.tk-multi-snapshot.location"

################################################################################

# ---- After Effects

# asset step
settings.tk-multi-snapshot.aftereffects.asset_step:
  template_snapshot: aftereffects_asset_snapshot
  template_work: aftereffects_asset_work
  hook_scene_operation: "{engine}/tk-multi-snapshot/basic/scene_operation.py"
  location: "@apps.tk-multi-snapshot.location"

# shot step
settings.tk-multi-snapshot.aftereffects.shot_step:
  template_snapshot: aftereffects_shot_snapshot
  template_work: aftereffects_shot_work
  hook_scene_operation: "{engine}/tk-multi-snapshot/basic/scene_operation.py"
  location: "@apps.tk-multi-snapshot.location"

################################################################################

# ---- Motion Builder

# asset step
settings.tk-multi-snapshot.motionbuilder.asset_step:
  template_snapshot: mobu_asset_snapshot
  template_work: mobu_asset_work
  location: "@apps.tk-multi-snapshot.location"

# shot step
settings.tk-multi-snapshot.motionbuilder.shot_step:
  template_snapshot: mobu_shot_snapshot
  template_work: mobu_shot_work
  location: "@apps.tk-multi-snapshot.location"

################################################################################

# ---- Alias

# asset step
settings.tk-multi-snapshot.alias.asset_step:
  template_snapshot: alias_asset_snapshot
  template_work: alias_asset_work
  hook_scene_operation: "{engine}/tk-multi-snapshot/basic/scene_operation.py"
  location: "@apps.tk-multi-snapshot.location"

################################################################################

# ---- VRED

# asset step
settings.tk-multi-snapshot.vred.asset_step:
  template_snapshot: vred_asset_snapshot
  template_work: vred_asset_work
  hook_scene_operation: "{engine}/tk-multi-snapshot/basic/scene_operation.py"
  location: "@apps.tk-multi-snapshot.location"

################################################################################

# ---- Harmony

# asset step
settings.tk-multi-snapshot.harmony.asset_step:
  hook_scene_operation: "{engine}/tk-multi-snapshot/scene_operation_tk-harmony.py"
  template_snapshot: harmony_asset_snapshot
  template_work: harmony_asset_work
  hook_thumbnail: "{engine}/thumbnail.py"
  location: "@apps.tk-multi-snapshot.location"

# sequence step
settings.tk-multi-snapshot.harmony.sequence_step:
  hook_scene_operation: "{engine}/tk-multi-snapshot/scene_operation_tk-harmony.py"
  template_snapshot: harmony_sequence_snapshot
  template_work: harmony_sequence_work
  hook_thumbnail: "{engine}/thumbnail.py"
  location: "@apps.tk-multi-snapshot.location"

# shot step
settings.tk-multi-snapshot.harmony.shot_step:
  hook_scene_operation: "{engine}/tk-multi-snapshot/scene_operation_tk-harmony.py"
  template_snapshot: harmony_shot_snapshot
  template_work: harmony_shot_work
  hook_thumbnail: "{engine}/thumbnail.py"
  location: "@apps.tk-multi-snapshot.location"

################################################################################

# ---- Blender

# episode step
settings.tk-multi-snapshot.blender.episode_step:
  hook_scene_operation: "{engine}/tk-multi-snapshot/scene_operation_tk-blender.py"
  template_snapshot: blender_episode_snapshot
  template_work: blender_episode_work
  hook_thumbnail: "{engine}/thumbnail.py"
  location: "@apps.tk-multi-snapshot.location"

# asset step
settings.tk-multi-snapshot.blender.asset_step:
  hook_scene_operation: "{engine}/tk-multi-snapshot/scene_operation_tk-blender.py"
  template_snapshot: blender_asset_snapshot
  template_work: blender_asset_work
  hook_thumbnail: "{engine}/thumbnail.py"
  location: "@apps.tk-multi-snapshot.location"

# asset step notask
settings.tk-multi-snapshot.blender.asset_step_notask:
  hook_scene_operation: "{engine}/tk-multi-snapshot/scene_operation_tk-blender.py"
  template_snapshot: blender_asset_snapshot_notask
  template_work: blender_asset_work_notask
  hook_thumbnail: "{engine}/thumbnail.py"
  location: "@apps.tk-multi-snapshot.location"

# environment step
settings.tk-multi-snapshot.blender.environment_step:
  hook_scene_operation: "{engine}/tk-multi-snapshot/scene_operation_tk-blender.py"
  template_snapshot: blender_environment_snapshot
  template_work: blender_environment_work
  hook_thumbnail: "{engine}/thumbnail.py"
  location: "@apps.tk-multi-snapshot.location"

# environment step notask
settings.tk-multi-snapshot.blender.environment_step_notask:
  hook_scene_operation: "{engine}/tk-multi-snapshot/scene_operation_tk-blender.py"
  template_snapshot: blender_environment_snapshot_notask
  template_work: blender_environment_work_notask
  hook_thumbnail: "{engine}/thumbnail.py"
  location: "@apps.tk-multi-snapshot.location"

# sequence step
settings.tk-multi-snapshot.blender.sequence_step:
  hook_scene_operation: "{engine}/tk-multi-snapshot/scene_operation_tk-blender.py"
  template_snapshot: blender_sequence_snapshot
  template_work: blender_sequence_work
  hook_thumbnail: "{engine}/thumbnail.py"
  location: "@apps.tk-multi-snapshot.location"

# shot step
settings.tk-multi-snapshot.blender.shot_step:
  hook_scene_operation: "{engine}/tk-multi-snapshot/scene_operation_tk-blender.py"
  template_snapshot: blender_shot_snapshot
  template_work: blender_shot_work
  hook_thumbnail: "{engine}/thumbnail.py"
  location: "@apps.tk-multi-snapshot.location"

################################################################################

# ---- Fusion

# asset step
settings.tk-multi-snapshot.fusion.asset_step:
  hook_scene_operation: "{engine}/tk-multi-snapshot/scene_operation_tk-fusion.py"
  template_snapshot: fusion_asset_snapshot
  template_work: fusion_asset_work
  hook_thumbnail: "{engine}/thumbnail.py"
  location: "@apps.tk-multi-snapshot.location"

# shot step
settings.tk-multi-snapshot.fusion.shot_step:
  hook_scene_operation: "{engine}/tk-multi-snapshot/scene_operation_tk-fusion.py"
  template_snapshot: fusion_shot_snapshot
  template_work: fusion_shot_work
  hook_thumbnail: "{engine}/thumbnail.py"
  location: "@apps.tk-multi-snapshot.location"


# ---- Krita

# asset step
settings.tk-multi-snapshot.krita.asset_step:
  hook_scene_operation: "{engine}/tk-multi-snapshot/scene_operation_tk-krita.py"
  template_snapshot: krita_asset_snapshot
  template_work: krita_asset_work
  hook_thumbnail: "{engine}/thumbnail.py"
  location: "@apps.tk-multi-snapshot.location"

# environment step
settings.tk-multi-snapshot.krita.environment_step:
  hook_scene_operation: "{engine}/tk-multi-snapshot/scene_operation_tk-krita.py"
  template_snapshot: krita_environment_snapshot
  template_work: krita_environment_work
  hook_thumbnail: "{engine}/thumbnail.py"
  location: "@apps.tk-multi-snapshot.location"

# environment step notask
settings.tk-multi-snapshot.krita.environment_step_notask:
  hook_scene_operation: "{engine}/tk-multi-snapshot/scene_operation_tk-krita.py"
  template_snapshot: krita_environment_snapshot_notask
  template_work: krita_environment_work_notask
  hook_thumbnail: "{engine}/thumbnail.py"
  location: "@apps.tk-multi-snapshot.location"

# shot step
settings.tk-multi-snapshot.krita.shot_step:
  hook_scene_operation: "{engine}/tk-multi-snapshot/scene_operation_tk-krita.py"
  template_snapshot: krita_shot_snapshot
  template_work: krita_shot_work
  hook_thumbnail: "{engine}/thumbnail.py"
  location: "@apps.tk-multi-snapshot.location"

# sequence step
settings.tk-multi-snapshot.krita.sequence_step:
  hook_scene_operation: "{engine}/tk-multi-snapshot/scene_operation_tk-krita.py"
  template_snapshot: krita_sequence_snapshot
  template_work: krita_sequence_work
  hook_thumbnail: "{engine}/thumbnail.py"
  location: "@apps.tk-multi-snapshot.location"

# episode step
settings.tk-multi-snapshot.krita.episode_step:
  hook_scene_operation: "{engine}/tk-multi-snapshot/scene_operation_tk-krita.py"
  template_snapshot: krita_episode_snapshot
  template_work: krita_episode_work
  hook_thumbnail: "{engine}/thumbnail.py"
  location: "@apps.tk-multi-snapshot.location"

# ---- Substance Painter

# asset step
settings.tk-multi-snapshot.substancepainter.asset_step:
  hook_scene_operation: "{engine}/tk-multi-snapshot/scene_operation_tk-substancepainter.py"
  template_snapshot: substancepainter_asset_snapshot
  template_work: substancepainter_asset_work
  hook_thumbnail: "{engine}/thumbnail.py"
  location: "@apps.tk-multi-snapshot.location"

################################################################################

# ---- Premiere

# episode step
settings.tk-multi-snapshot.premiere.episode_step:
  template_snapshot: premiere_episode_snapshot
  template_work: premiere_episode_work
  hook_scene_operation: "{engine}/tk-multi-snapshot/basic/scene_operation.py"
  location: "@apps.tk-multi-snapshot.location"

# sequence step
settings.tk-multi-snapshot.premiere.sequence_step:
  template_snapshot: premiere_sequence_snapshot
  template_work: premiere_sequence_work
  hook_scene_operation: "{engine}/tk-multi-snapshot/basic/scene_operation.py"
  location: "@apps.tk-multi-snapshot.location"
