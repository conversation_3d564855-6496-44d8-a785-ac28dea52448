########################################################################################
#
# Copyright (c) 2024 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
########################################################################################


import os
import json
import pprint
import traceback

import sgtk

from tank import Hook
from tank import TankError
from tank.platform.qt import QtCore, QtGui

import maya.cmds as cmds
import pymel.core as pm


class ProcessItemsHook(Hook):
    """
    Process items to load them into the scene
    """

    def load_item(self, item, update_progress):
        """
        The load item method is executed once for each defined item and its purpose is
        to process the loading of the item into the scene.

        The structure of each item is a dictionary with three keys:

        - "node": The name of the 'node' that is to be operated on. Most DCCs have
            a concept of a node, path, or some other way to address a particular
            object in the scene.
        - "type": The object type that this is. This is to
            know how to handle the object.
        - "path": Path on disk to the object to be loaded.
        """

        CoreMayaTools = self.load_framework("mty-framework-coremayatools")
        MayaMiscUtils = CoreMayaTools.MayaMiscUtils

        # self.parent.logger.debug("Shaded asset item:\n{}".format(pprint.pformat(item)))

        shaded_scene_path = item["path"]

        if item["type"] == "Shaded Assets":
            update_progress({"progress": 30, "message": "Referencing Shaded Assets..."})
            self.parent.logger.info("loading {}".format(shaded_scene_path))

            asset_namespaces = item.get("other_params", {}).get("namespaces")

            # query scene references to find out if the namespace is already loaded
            scene_references = pm.listReferences()
            existing_namespaces = [ref.namespace for ref in scene_references]

            already_used_namespaces = []

            for namespace in asset_namespaces:
                if namespace in existing_namespaces:
                    self.parent.logger.warning(
                        "Shaded asset namespace {} already loaded".format(namespace)
                    )
                    already_used_namespaces.append(namespace)
                    continue
                try:
                    # namespace = MayaMiscUtils._create_namespace("rig", item["node"])
                    self.parent.logger.info(
                        "Shaded asset namespace {}".format(namespace)
                    )
                    MayaMiscUtils._create_reference(item["path"], namespace, pm)
                except:
                    self.parent.logger.error(
                        "Error loading item '{}' with namespace '{}'".format(
                            shaded_scene_path, namespace
                        )
                    )
                    self.parent.logger.error(traceback.format_exc())

                self.logger.info(
                    "Successfully imported shaded asset: {}".format(shaded_scene_path)
                )

            if already_used_namespaces:
                msg = "The following namespaces were already loaded:\n\n{}".format(
                    "\n".join(already_used_namespaces)
                )
                self.parent.engine.logger.warning(msg)
                self.show_message(msg, icon="Warning")


    def show_message(self, msg, icon=None):
        """
        Displays a message box with a specified message and icon.

        This method creates a QMessageBox to display a message to the user.
        The appearance of the message box can be customized using the 'icon' parameter.

        Args:
            msg (str): The message to be displayed in the message box.
            icon (str, optional): The icon to be displayed in the message box.
                Valid options are "NoIcon", "Question", "Information", "Warning", and "Critical".
                Defaults to "NoIcon" if not provided or invalid.
        """

        icons_dict = {
            "NoIcon": QtGui.QMessageBox.NoIcon,
            "Question": QtGui.QMessageBox.Question,
            "Information": QtGui.QMessageBox.Information,
            "Warning": QtGui.QMessageBox.Warning,
            "Critical": QtGui.QMessageBox.Critical,
        }

        if not icon or icon not in icons_dict.keys():
            icon_selection = icons_dict["NoIcon"]
        else:
            icon_selection = icons_dict[icon]

        msg_error = QtGui.QMessageBox()
        msg_error.setWindowTitle("Finished execution.")
        msg_error.setText(msg)
        msg_error.setIcon(icon_selection)
        msg_error.exec_()
