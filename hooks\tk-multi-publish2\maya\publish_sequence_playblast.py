# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

import os
import sgtk
import maya.cmds as cmds
import maya.mel as mel
import pymel.core as pm
import re
import traceback
import subprocess
from sgtk.platform.qt import QtGui, QtCore

HookBaseClass = sgtk.get_hook_baseclass()


class MayaScenePlayblastPublishPlugin(HookBaseClass):
    """
    Plugin for publish Scene playblast Publish

    """

    def __init__(self, parent):
        super(MayaScenePlayblastPublishPlugin, self).__init__(parent)
        self.camera_setup()

    @property
    def icon(self):
        """
        Path to an png icon on disk
        """

        # look for icon one level up from this hook's folder in "icons" folder
        return os.path.join(self.disk_location, os.pardir, "icons", "review.png")

    @property
    def name(self):
        """
        One line display name describing the plugin
        """
        return "Publish maya geometry"

    @property
    def description(self):
        """
        Verbose, multi-line description of what the plugin does. This can
        contain simple html for formatting.
        """

        loader_url = "https://support.shotgunsoftware.com/hc/en-us/articles/219033078"

        return """
        <p>This plugin publish a playblast from the shot found in the scene.</p>

        """

    @property
    def settings(self):
        """
        Dictionary defining the settings that this plugin expects to recieve
        through the settings parameter in the accept, validate, publish and
        finalize methods.

        A dictionary on the following form::

            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }

        The type string should be one of the data types that toolkit accepts
        as part of its environment configuration.
        """
        # inherit the settings from the base publish plugin
        plugin_settings = super(MayaScenePlayblastPublishPlugin, self).settings or {}

        # settings specific to this class
        maya_publish_settings = {
            "Work Template": {
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                "correspond to a template defined in "
                "templates.yml.",
            },
            "Primary Publish Template": {
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                "correspond to a template defined in "
                "templates.yml.",
            },
            "Publish Template": {
                "type": "template",
                "default": None,
                "description": "Template path for published Scene playblast. Should"
                "correspond to a template defined in "
                "templates.yml.",
            },
            "Publish type": {
                "type": "string",
                "default": "Maya Layout Playblast",
                "description": "The published file type to register.",
            },
            "Shot Prefix": {
                "type": "string",
                "default": "Shot_",
                "description": "The string or dynamic prefix to use to validate shot naming conventions."
                "It can be a plain string or a tokenized one, which use a system of keys,"
                "Those keys match items from the context, supported tokens are as follow:"
                "{context.project} which matchs: context.project['name']"
                "{context.entity} which matchs: context.entity['name']"
                "{context.step} which matchs: context.step['name']"
                "{context.task} which matchs: context.task['name']",
            },
            "Shot Digits": {
                "type": "int",
                "default": 3,
                "description": "The valid amount of digits in the shot name after the shot prefix.",
            },
        }

        # update the base settings
        plugin_settings.update(maya_publish_settings)

        return plugin_settings

    @property
    def item_filters(self):
        return ["maya.session.sceneplayblast"]

    def accept(self, settings, item):
        sceneShots = self.__get_secuencer_shots(settings)
        # Only acept the plugin if there was any shot found
        if sceneShots["valid_shots"] or sceneShots["invalid_shots"]:
            item.properties["scene_shots"] = sceneShots
            return {"accepted": True, "checked": True}
        else:
            return {"accepted": False}

    def fix_path(self, path):
        """Replace all backward slashes with forward slashes."""

        path = path.replace("\\\\", "/")
        path = path.replace("\\", "/")

        return path

    def __shot_code_from_shot(self, shot_name, shot_prefix, shot_digits):
        """
        Exrtracts the shot name from listed shots in the scene.

        :param shot_name: the name from the given shot.

        :returns: The shot code as integer number or False if there is no match format.
        """
        shot_name_attr = cmds.shot(shot_name, q=True, shotName=True)

        self.parent.log_debug("Shot Name Attribute: {0}".format(shot_name_attr))

        # Get the shot code from the paremter string

        regex = r"%s(?P<shot_code>[0-9]{%s})" % (shot_prefix, shot_digits)

        self.parent.log_debug("regex: {0}".format(regex))

        shot_code = re.match(regex, str(shot_name_attr))

        if shot_code:
            # Convert the string to a integer
            shot_code = int(shot_code.group("shot_code").lstrip("0"))
            self.parent.log_debug("Valid shot name: {0}".format(shot_code))
            return shot_code
        else:
            return False

    def __resolve_shot_prefix(self, shot_prefix):
        """
        From a given tokenized string, return a computed string
        This computed string will be used to match the shot names
        The tokenized string is "magick" concept to allow for special
        keys taken from the context, supported tokens are as follow:
        {context.project} = context.project['name']
        {context.entity} = context.entity['name']
        {context.step} = context.step['name']
        {context.task} = context.task['name']
        """

        context = self.parent.context

        known_values = {}
        context_dict = context.to_dict()

        self.parent.log_debug("Context as dict: {0}".format(context_dict))

        for item, value in iter(context_dict.items()):
            if item == "additional_entities":
                continue

            if item:
                known_values[item] = value.get("name", "unknown")
            else:
                known_values[item] = "unknown"

        known_tokens = {
            "{context.project}": known_values["project"],
            "{context.entity}": known_values["entity"],
            "{context.step}": known_values["step"],
            "{context.task}": known_values["task"],
        }

        if "{" in shot_prefix:
            for token in known_tokens:
                value = known_tokens[token]
                shot_prefix = shot_prefix.replace(token, value)

        return shot_prefix

    def __get_secuencer_shots(self, settings):
        shots = {"valid_shots": [], "invalid_shots": []}
        # iteerate over the list of shots in the scene

        shot_prefix = self.__resolve_shot_prefix(settings["Shot Prefix"].value)
        shot_digits = settings["Shot Digits"].value

        for shot in cmds.ls(type="shot"):
            # Validate naming convention
            shot_code = self.__shot_code_from_shot(shot, shot_prefix, shot_digits)
            if shot_code == False:
                shots["invalid_shots"].append(shot)
            else:
                shots["valid_shots"].append(shot)

        return shots

    def validate(self, settings, item):
        scene_shots = item.properties.get("scene_shots")
        if len(scene_shots["invalid_shots"]) != 0:
            error_msg = "Warning!!: The followin Shots don't match naming conventions!, Check it!!\n%s"
            self.logger.error(error_msg % "\n".join(scene_shots["invalid_shots"]))
            raise Exception(error_msg)

        currentPanel = cmds.getPanel(withFocus=1)
        panelType = cmds.getPanel(typeOf=currentPanel)
        if not panelType == "modelPanel":
            error_msg = "You need to select one viewport panel"
            raise Exception(error_msg)

        return True

    def strip_version_from_name(self, path_string):
        name, ext = os.path.splitext(path_string)
        name = "_".join(name.split("_")[:-1])

        return name

    def enable_maya_states(self):
        cmds.select(clear=True)
        thisPanel = cmds.getPanel(withFocus=1)
        panelType = cmds.getPanel(typeOf=thisPanel)

        if not panelType == "modelPanel":
            error_msg = "You need to select one viewport panel."
            self.logger.error(error_msg)
            raise Exception(error_msg)

        cmds.modelEditor(thisPanel, e=1, dtx=1)
        states = {}
        states["cameras"] = cmds.modelEditor(thisPanel, query=True, cameras=1)
        states["joints"] = cmds.modelEditor(thisPanel, query=True, joints=1)
        states["follicles"] = cmds.modelEditor(thisPanel, query=True, follicles=1)
        states["deformers"] = cmds.modelEditor(thisPanel, query=True, deformers=1)
        states["nurbsCurves"] = cmds.modelEditor(thisPanel, query=True, nurbsCurves=1)
        states["ikHandles"] = cmds.modelEditor(thisPanel, query=True, ikHandles=1)
        states["dynamics"] = cmds.modelEditor(thisPanel, query=True, dynamics=1)
        states["locators"] = cmds.modelEditor(thisPanel, query=True, locators=1)
        for typeOfObject in states:
            eval("cmds.modelEditor(thisPanel, e=1, " + typeOfObject + "=0)")

        return states, thisPanel

    def concatenate_videos(self, shot_videos, pub_path, ffpeg_frwrk):
        concatenate_file_path = str(
            os.path.join(os.path.dirname(pub_path), "concatenate_file.txt")
        )
        concatenate_file = open(concatenate_file_path, "w")

        for path in shot_videos:
            concatenate_file.write("file '" + path + "'\n")

        concatenate_file.close()
        scene_video_file = str(pub_path)

        ffmpeg_cmd = [
            "-f",
            "concat",
            "-safe",
            "0",
            "-probesize",
            "16M",
            "-i",
            "{}".format(concatenate_file_path),
            "-y",
            "-c",
            "copy",
            "-preset",
            "veryfast",
            "-crf",
            "24",
            "-max_muxing_queue_size",
            "9999",
            "{}".format(scene_video_file),
        ]
        _err, _info = ffpeg_frwrk.ffmpegCore.execute_command(ffmpeg_cmd)

        if _err:
            raise Exception(
                "Failed to concatenate shot videos to scene video: %s" % (_info)
            )

        if os.path.exists(concatenate_file_path):
            os.remove(concatenate_file_path)

    def get_project_data_from_SG(self):
        """
        Returns a dictionary of project data from SG relevant to a
        shot of the following form:

            {
                'code': (str),
                 'id': (int),
                 'sg_fps': (float),
                 'sg_output_color_space': (str),
                 'sg_working_color_space': (str),
                 'type': (str),
             }
        """

        project_data = {}

        filters = [
            ["id", "is", self.parent.engine.context.project["id"]],
        ]
        fields = [
            "code",
            "id",
            "sg_fps",
            "sg_working_color_space",
            "sg_output_color_space",
        ]

        project_data = self.parent.engine.shotgun.find_one(
            entity_type="Project", filters=filters, fields=fields
        )

        return project_data

    def convert_avi_to_mov(
        self, mov_filename, playblastpath, playblastname, shot, version, ffpeg_frwrk
    ):
        playblastname = self.fix_path(playblastname)
        mov_filename = self.fix_path(mov_filename)

        project_fps = self.project_data.get("sg_fps", "24")

        codec = "libx264"
        options = ["-pix_fmt", "yuv420p", "-preset", "veryfast", "-crf", "24"]

        convert_cmd = [
            "-hide_banner",
            "-r",
            str(project_fps),
            "-probesize",
            "10M",
            "-i",
            str(playblastname),
            "-max_muxing_queue_size",
            "2048",
            "-vcodec",
            codec,
            "-acodec",
            "aac",
        ]

        convert_cmd.extend(options)

        convert_cmd.extend(["-r", str(project_fps), "-y", mov_filename])

        self.parent.engine.logger.info(
            "about to execute command: {}".format(convert_cmd)
        )

        _err, _info = ffpeg_frwrk.ffmpegCore.execute_command(convert_cmd)
        if _err:
            raise Exception("Failed to convert playblast to video: %s" % (_info))

        # delete the playblast file, we only want to keep the transcoded one
        os.remove(playblastname)

    def create_shots_playblasts(
        self, list_of_shots, show_states, current_panel, publishpath, fields
    ):
        # Loading FFMPEG framework
        ffmpeg = self.load_framework("mty-framework-ffmpeg")

        temp_playblast_path = None

        shots_videos = []
        currentPanel = current_panel

        for shot in list_of_shots:
            try:
                shot.depthOfField.set(1)
            except:
                pass
            try:
                shot.overscan.set(1)
            except:
                pass

            self.parent.log_debug(os.path.dirname(publishpath))
            self.parent.log_debug(fields)

            version = "v{0}".format(str(fields["version"]).zfill(3))

            temp_playblast_path = os.path.join(
                os.path.dirname(publishpath), "temp", version
            )

            playblast_path = os.path.join(temp_playblast_path, shot.getShotName())

            if not os.path.exists(playblast_path):
                os.makedirs(playblast_path)

            playblast_name = "{0}_{1}_{2}".format(
                shot.getShotName(), fields["step_code"], version
            )

            playblast_filename = (
                str(playblast_path + playblast_name).replace("\\", "/") + ".avi"
            )

            aPlayBackSliderPython = mel.eval("$tmpVar=$gPlayBackSlider")

            audio = cmds.timeControl(aPlayBackSliderPython, sound=True, query=True)

            self.parent.log_debug(playblast_filename)

            options = {
                "format": "avi",
                "compression": "none",
                "quality": 50,
                "width": 1280,
                "height": 720,
                "sequenceTime": True,
                "forceOverwrite": True,
                "filename": playblast_filename,
                "startTime": shot.getSequenceStartTime(),
                "endTime": shot.getSequenceEndTime(),
                "sound": audio,
                "clearCache": True,
                "viewer": False,
                "showOrnaments": True,
                "percent": 100,
            }

            try:
                pm.playblast(**options)
            except:
                full_error = traceback.format_exc()

                raise Exception("Failed to create playblast: {}".format(full_error))

            version = fields["version"]

            mov_filename = playblast_filename.replace(".avi", ".mov")

            self.convert_avi_to_mov(
                playblast_filename.replace(".avi", ".mov"),
                playblast_path,
                playblast_filename,
                shot,
                version,
                ffmpeg,
            )

            # store the path to shot video in order to concatenate later
            shots_videos.append(mov_filename.replace("/", os.path.sep))

        # Concatenate with ffmpeg shot videos to publish path as output
        self.concatenate_videos(shots_videos, publishpath, ffmpeg)

        # restore viewport views
        for typeOfObject in show_states:
            eval(
                "cmds.modelEditor(currentPanel, e=1, "
                + typeOfObject
                + "="
                + str(show_states[typeOfObject])
                + ")"
            )

        return temp_playblast_path

    def get_root_item(self, item):
        """A handy recursive function to get an item's root parent
        This is mostly useful because the root is the only one
        where we can store extra data
        """

        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def publish(self, settings, item):
        # create a pointer to the parent root item to store
        # any extra publishes apart form the primary one
        root_item = self.get_root_item(item)
        root_item.properties.setdefault("sg_publish_extra_data", [])

        self.logger.debug("Storing extra publish data on root item: " + str(root_item))

        publish_extra_data = root_item.properties["sg_publish_extra_data"]

        # set sg_camera_movement field
        self.set_sg_shot_camera_movement()

        # Create the review videos
        # Get the publish path from settings template
        work_template_name = settings["Work Template"].value

        work_template = self.parent.engine.get_template_by_name(work_template_name)

        scene_path = os.path.abspath(cmds.file(query=True, sn=True))

        fields = work_template.get_fields(scene_path)  # delete

        publish_version = fields["version"]

        publish_template_name = settings["Publish Template"].value

        publish_template = self.parent.engine.get_template_by_name(
            publish_template_name
        )

        publish_path = publish_template.apply_fields(fields)  # delete

        scene_pub_template_name = settings["Primary Publish Template"].value
        scene_pub_template = self.parent.engine.get_template_by_name(
            scene_pub_template_name
        )
        primary_publish_path = scene_pub_template.apply_fields(fields)

        # determine the publish name:
        publish_name = fields.get("name")
        if not publish_name:
            publish_name = os.path.basename(publish_path)

        showStates, currentPanel = self.enable_maya_states()

        # Find additional info from the scene:
        list_of_shots = pm.ls(type="shot")

        if list_of_shots:
            self.project_data = self.get_project_data_from_SG()

            first_frame = int(list_of_shots[0].getStartTime())
            last_frame = int(list_of_shots[-1].getEndTime())
            temp_playblast_path = self.create_shots_playblasts(
                list_of_shots, showStates, currentPanel, publish_path, fields
            )

            sg_fields_to_update = {
                "sg_status_list": "rev",
                "task.Task.sg_status_list": "rev",
            }

            # register the publish:
            publish_data = {
                "tk": self.parent.engine.sgtk,
                "context": item.context,
                "comment": item.description,
                "path": publish_path,
                "name": publish_name,
                "version_number": publish_version,
                "thumbnail_path": item.get_thumbnail_as_path(),
                "task": self.parent.engine.context.task,
                "published_file_type": settings["Publish type"].value,
                "dependency_paths": [primary_publish_path],
                "sg_fields": sg_fields_to_update,
            }

            sg_publishes = sgtk.util.register_publish(**publish_data)

            self.parent.log_debug(
                "Adding existing publish "
                + "(id:%s) as extra data for item: %s" % (sg_publishes["id"], item)
            )

            publish_extra_data.append(sg_publishes)

            work_status = "rev"

            self.parent.engine.shotgun.update(
                "PublishedFile", sg_publishes["id"], {"sg_status_list": work_status}
            )

            sg_version = self.submit_version(
                temp_playblast_path,
                publish_path,
                [sg_publishes],
                self.parent.engine.context.task,
                item.description,
                True,
                first_frame,
                last_frame,
                work_status,
            )

            # Upload in a new thread and make our own event loop to wait for the thread to finish.
            event_loop = QtCore.QEventLoop()
            thread = UploaderThread(
                self.parent,
                sg_version,
                publish_path,
                item.get_thumbnail_as_path(),
                True,
            )
            thread.finished.connect(event_loop.quit)
            thread.start()
            event_loop.exec_()

            # log any errors generated in the thread
            for e in thread.get_errors():
                self.parent.log_error(e)
        else:
            print("Warning!!: There are not pipeline shots to playblast")

    def getSecuencerShots(self, settings, all_shots=False):
        shots = []

        shot_prefix = self.__resolve_shot_prefix(settings["Shot Prefix"].value)
        shot_digits = settings["Shot Digits"].value

        for shot in cmds.ls(type="shot"):
            shot_dict = {}

            shot_code = self.__shot_code_from_shot(shot, shot_prefix, shot_digits)

            if shot_code == False and all_shots == False:
                print(
                    "Warning!!: The Shot "
                    + shot
                    + " doesn't match naming conventions!, Check it!!"
                )
            else:
                shot_dict["shot_code"] = shot_code
                shot_dict["shot_name"] = cmds.shot(shot, q=True, shotName=True)
                shot_dict["shot_node"] = shot
                shot_dict["currentCamera"] = cmds.shot(
                    shot, query=True, currentCamera=True
                )
                shot_dict["sequenceStartFrame"] = cmds.getAttr(
                    shot + ".sequenceStartFrame"
                )
                shot_dict["sequenceEndFrame"] = cmds.getAttr(shot + ".sequenceEndFrame")
                shot_dict["startFrame"] = cmds.getAttr(shot + ".startFrame")
                shot_dict["endFrame"] = cmds.getAttr(shot + ".endFrame")

                shots.append(shot_dict)
        return sorted(shots)

    def submit_version(
        self,
        path_to_frames,
        path_to_movie,
        sg_publishes,
        sg_task,
        comment,
        store_on_disk,
        first_frame,
        last_frame,
        work_status,
        override_entity=False,
    ):
        """
        Create a version in Shotgun for
        this path and linked to this publish.

        """

        user = self.parent.engine.context.user

        # create a name for the version based on the file name
        # grab the file name, strip off extension
        name = os.path.splitext(os.path.basename(path_to_movie))[0]

        name = name.replace("_", " ")

        name = name.capitalize()

        LinkFolder = {
            "local_path": os.path.dirname(path_to_frames) + os.sep,
            "content_type": None,
            "link_type": "local",
            "name": name,
        }

        LinkFile = {
            "local_path": path_to_movie,
            "content_type": None,
            "link_type": "local",
            "name": name,
        }

        if override_entity != False:
            entity = override_entity
        else:
            entity = self.parent.engine.context.entity

        # Create the version in Shotgun
        data = {
            "code": name,
            "sg_status_list": work_status,
            "entity": entity,
            "sg_task": sg_task,
            "sg_version_type": "Production",
            "sg_first_frame": first_frame,
            "sg_last_frame": last_frame,
            "frame_count": (last_frame - first_frame + 1),
            "frame_range": "%s-%s" % (first_frame, last_frame),
            "sg_frames_have_slate": False,
            "published_files": sg_publishes,
            "created_by": user,
            "description": comment,
            "sg_path_to_frames": path_to_frames,
            "sg_movie_has_slate": True,
            "project": self.parent.engine.context.project,
            "user": user,
        }

        if store_on_disk:
            data["sg_path_to_movie"] = path_to_movie

        sg_version = self.parent.engine.shotgun.create("Version", data)

        self.parent.log_debug("Created version in shotgun: %s" % str(data))

        return sg_version

    def finalize(self, settings, item):
        pass

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def camera_setup(self):
        list_of_cameras = pm.ls(type="camera")

        for camera in list_of_cameras:
            try:
                camera.displayFilmGate.set(0)
                camera.displayResolution.set(0)
                camera.displayGateMask.set(0)
                camera.displayFieldChart.set(0)
                camera.displaySafeAction.set(0)
                camera.displaySafeTitle.set(0)
                camera.displayFilmPivot.set(0)
                camera.displayFilmOrigin.set(0)
                camera.overscan.set(1)
            except:
                pass

    def set_sg_shot_camera_movement(self):
        shots = pm.ls(type="shot")
        engine = self.parent.engine
        sg = engine.shotgun

        for shot_ in shots:
            shot_name = shot_.getShotName()
            sg_shot = sg.find_one(
                entity_type="Shot",
                filters=[["code", "is", shot_name]],
                fields=["sg_camera_movement", "code"],
            )

            startFrame = shot_.startFrame.get()
            endFrame = shot_.endFrame.get()
            camera_ = shot_.currentCamera.get()

            pm.currentTime(startFrame)
            start_frame_world_matrix = camera_.worldMatrix.get()

            pm.currentTime(endFrame)
            last_frame_world_matrix = camera_.worldMatrix.get()

            if start_frame_world_matrix == last_frame_world_matrix:
                sg.update(
                    entity_type="Shot",
                    entity_id=sg_shot["id"],
                    data={"sg_camera_movement": "Static"},
                )

            else:
                sg.update(
                    entity_type="Shot",
                    entity_id=sg_shot["id"],
                    data={"sg_camera_movement": "Dynamic"},
                )


# ================================================================


class UploaderThread(QtCore.QThread):
    """
    Simple worker thread that encapsulates uploading to shotgun.
    Broken out of the main loop so that the UI can remain responsive
    even though an upload is happening
    """

    def __init__(self, app, version, path_to_movie, thumbnail_path, upload_to_shotgun):
        QtCore.QThread.__init__(self)
        self._app = app
        self._version = version
        self._path_to_movie = path_to_movie
        self._thumbnail_path = thumbnail_path
        self._upload_to_shotgun = upload_to_shotgun
        self._errors = []

    def get_errors(self):
        """
        can be called after execution to retrieve a list of errors
        """
        return self._errors

    def run(self):
        """
        Thread loop
        """
        upload_error = False

        if self._upload_to_shotgun:
            try:
                self._app.tank.shotgun.upload(
                    "Version",
                    self._version["id"],
                    self._path_to_movie,
                    "sg_uploaded_movie",
                )
            except:
                self._errors.append(
                    "Movie upload to Shotgun failed: {}".format(traceback.print_exc())
                )
                upload_error = True

        if not self._upload_to_shotgun or upload_error:
            try:
                self._app.tank.shotgun.upload_thumbnail(
                    "Version", self._version["id"], self._thumbnail_path
                )
            except:
                self._errors.append(
                    "Thumbnail upload to Shotgun failed: {}".format(
                        traceback.print_exc()
                    )
                )
