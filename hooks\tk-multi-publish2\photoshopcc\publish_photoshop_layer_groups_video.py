# -*- coding: utf-8 -*-
# Standard library:
import os
import re
import json
import pprint
import fileseq
import tempfile
import traceback

import sgtk

from sgtk.platform.qt import QtCore, QtGui

# ======================================================================================

HookBaseClass = sgtk.get_hook_baseclass()


pp = pprint.pprint
pf = pprint.pformat


class LayersToVideoPublishPlugin(HookBaseClass):
    @property
    def icon(self):

        return os.path.join(self.disk_location, os.pardir, "icons", "review.png")

    @property
    def name(self):

        return "Publish Media Review"

    @property
    def description(self):

        return """
        <p>This plugin publish a playblast from the current shot.</p>

        """

    @property
    def settings(self):

        # inherit the settings from the base publish plugin
        plugin_settings = super(LayersToVideoPublishPlugin, self).settings or {}

        # settings specific to this class
        review_publish_settings = {
            "Available Steps": {
                "type": "list",
                "default": [],
                "description": "A list of Step names to filter.",
            },
            "Available Tasks": {
                "type": "list",
                "default": [],
                "description": "A list of tasks names to filter.",
            },
            # "Document width": {
            #     "type": "int",
            #     "default": 1024,
            #     "description": "Photoshop document width size."
            # },
            # "Document height": {
            #     "type": "int",
            #     "default": 768,
            #     "description": "Photoshop document height size."
            # },
            # "Image resolution": {
            #     "type": "int",
            #     "default": 72,
            #     "description": "Image resolution in dpi."
            # }
        }

        # update the base settings
        plugin_settings.update(review_publish_settings)

        return plugin_settings

    @property
    def item_filters(self):
        return ["photoshop.layergroups", "photoshop.layergroupsproxies"]

    def accept(self, settings, item):

        self.photoshop = self.parent.engine.adobe
        context = self.parent.context

        pipelineSteps = settings["Available Steps"].value
        pipelineTasks = settings["Available Tasks"].value

        valueoverrides = self.parent.engine.custom_frameworks.get(
            'mty-framework-valueoverrides'
        ) or self.load_framework('mty-framework-valueoverrides')
        override_code_tasks = 'mty.publisher.photoshop.layer_groups_video.tasks'
        data_tasks = valueoverrides.get_value(override_code_tasks)
        self.parent.logger.info("\n data tasks {}".format(data_tasks))
        if data_tasks is not None:
            pipelineTasks = json.loads(data_tasks)

        override_code_steps = 'mty.publisher.photoshop.layer_groups_video.steps'
        data_steps = valueoverrides.get_value(override_code_steps)
        if data_steps is not None:
            pipelineSteps = json.loads(data_steps)

        self.parent.logger.info("\n available steps {}".format(pipelineSteps))
        self.parent.logger.info("\n available tasks {}".format(pipelineTasks))

        non_filter_steps = pipelineSteps == []
        non_filter_tasks = pipelineTasks == []

        accepted_tasks = False
        accepted_steps = False
        if not non_filter_tasks:
            step_filter_tasks = (
                context.task["name"] in pipelineTasks
            )
            if step_filter_tasks:
                accepted_tasks = True
        else:
            accepted_tasks = True

        if not non_filter_steps:
            step_filter_steps = (
                context.step["name"] in pipelineSteps
            )
            if step_filter_steps:
                accepted_steps = True
        else:
            accepted_steps = True

        if accepted_tasks and accepted_steps:
            return {"accepted": True, "checked": True}
        else:
            return {"accepted": False, "checked": True}

    # ---------------------------------------------------------------------

    def validate_actions(self, state, **kwargs):

        if state["errors_found"] > 0:
            self.logger.error(
                "There are {0} errors in the scene".format(state["errors_found"])
            )

            for key in state["errors"]:
                error_message = "{0} ({1}):".format(key, len(state["errors"][key]))
                problems_message = ""

                for element in state["errors"][key]:
                    problems_message += "{0}\n".format(element)

                self.logger.error(
                    error_message,
                    extra={
                        "action_show_more_info": {
                            "label": "Show details",
                            "tooltip": "Show all information from the error",
                            "text": "<h3>{0}</h3><pre>{1}</pre>".format(
                                error_message, problems_message
                            ),
                        }
                    },
                )

                #   . . . . . . . . . . . . . . . . . . . . . .

                if state["callbacks"][key]["enabled"]:
                    log = None
                    message_type = state["callbacks"][key]["type"]

                    if message_type == "error":
                        log = self.logger.error
                    elif message_type == "warning":
                        log = self.logger.warning
                    elif message_type == "debug":
                        log = self.logger.debug
                    else:
                        log = self.logger.info

                    message = state["callbacks"][key]["message"]
                    callback = state["callbacks"][key]["callback"]
                    label = state["callbacks"][key]["label"]
                    tooltip = state["callbacks"][key]["tooltip"]

                    log(
                        message,
                        extra={
                            "action_button": {
                                "label": label,
                                "tooltip": tooltip,
                                "callback": callback(),
                            }
                        },
                    )
                #   . . . . . . . . . . . . . . . . . . . . . .

        return state["errors_found"]

    def composer_set(self, name, list_of_errors, state, action):
        if len(list_of_errors) > 0:
            state["errors_found"] += len(list_of_errors)
            state["errors"][name] = list_of_errors

            if "enabled" not in action.keys():
                raise Exception(
                    "Error in {0}: \n".format(name)
                    + '"enabled" key is not present in action dictionary.\n'
                    + "provided action dictionary: {0}".format(str(action))
                )

            #   . . . . . . . . . . . . . . . . . . . . . .

            set_of_keys = set(action.keys())

            set_of_required_keys = {
                "enabled",
                "type",
                "callback",
                "label",
                "tooltip",
                "message",
            }

            #   . . . . . . . . . . . . . . . . . . . . . .

            if not set_of_required_keys.issuperset(set_of_keys):
                raise Exception(
                    "Error in {0}: \n".format(name)
                    + "Missing keys in action dictionary.\n"
                    + "provided action dictionary: {0}".format(str(action))
                )

            #   . . . . . . . . . . . . . . . . . . . . . .

            if action["enabled"]:

                state["callbacks"][name] = {
                    "enabled": action["enabled"],
                    "type": action["type"],
                    "callback": action["callback"],
                    "label": action["label"],
                    "tooltip": action["tooltip"],
                    "message": action["message"],
                }
            else:
                state["callbacks"][name] = {"enabled": False}

    def validate(self, settings, item):

        state = {"errors_found": 0, "errors": {}, "callbacks": {}}

        # self.composer_set(
        #     name="Preference Units errors",
        #     list_of_errors=self.units_errors(settings),
        #     state=state,
        #     action={"enabled": False}
        # )


        # state_result = self.validate_actions(state)

        # if state_result:
        #     there_is_error = "Validation checks have not been passed, " + \
        #         "found {0} problems".format(state_result)

        #     raise Exception(there_is_error)

        # else:
        #     self.logger.debug("All checks passed!")

        self.logger.debug("All checks passed!")

        """ omited by coordinator request, they are gonna use frame numbers

        # Validate continuos layer naming numbering
        numbers_list = []
        for name in sorted(groups_names):
            numbers_list.append(int(name))

        numbers_list = sorted(numbers_list)

        self.parent.log_debug('Total layer groups: ' + str(len(numbers_list)))
        self.parent.log_debug('Smallest number: ' + str(numbers_list[0]))
        self.parent.log_debug('Bigest number: ' + str(numbers_list[-1]))

        if (numbers_list[-1] - numbers_list[0] + 1) != len(numbers_list):
            raise Exception("Layers are not named correctly, they need to be continued numbers...")
        """

        return True

    # ----------------------------------------------------------------------------------

    def get_resize_value(self):
        # get horizontal project resolution
        hook_expression = "{config}/get_entity_resolution.py"
        project_resolution = self.parent.execute_hook_expression(
            hook_expression,
            "get_resolution",
            engine=self.parent.engine,
        )

        if not project_resolution:
            error_msg = ("Couldn't get project resolution from SG.")
            self.parent.engine.logger.error(error_msg)
            # self.parent.engine.show_message(error_msg)
            QtGui.QMessageBox.error(
                None, "Invalid project data", error_msg
            )
            return None

        pattern = re.compile(r"(?P<width>\d+)x(?P<height>\d+)")
        match = re.match(pattern, str(project_resolution))
        if not match:
            error_msg = ("Couldn't get image resolution.")
            self.parent.engine.logger.error(error_msg)
            # self.parent.engine.show_message(error_msg)
            QtGui.QMessageBox.error(
                None, "Invalid image resolution", error_msg
            )
            return None

        project_width = int(match.groupdict()["width"])
        resize_value = "{0}x{0}".format(project_width)

        return resize_value

    def export_copy_image(self, source_path, dest_path, cmd=[]):

        if not os.path.exists(os.path.dirname(dest_path)):
            os.makedirs(os.path.dirname(dest_path))

        if not cmd:
            # default copy source to dest path command
            cmd = [source_path, "-resize", self.resize_value, dest_path]

        self.ImageMagickCoreTools.set_binary("convert")
        imgagemagick_bin_path = self.ImageMagickCoreTools.get_bin_path()
        self.parent.logger.info(
            "imgagemagick_bin_path: {}".format(imgagemagick_bin_path)
        )

        self.parent.logger.info("EXPORT COPY: magick convert {}".format(cmd))
        _err, _out, executed_cmd = self.ImageMagickCoreTools.execute_command(cmd)
        if _err:
            raise Exception(
                (
                    "Failed to export image: {0} to {1}\n"
                    "Error: {2}\n"
                    "Executed command: {3}"
                ).format(
                    source_path, dest_path, _out, executed_cmd
                )
            )

    # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

    def register_publish(
        self, comment, path, name, version_number, thumbnail, publish_type, dependencies=[]
    ):

        # get media resolution
        media_resolution = self.parent.execute_hook_expression(
            "{config}/tk-multi-publish2/multi/resolution_from_path.py",
            "media_resolution_value",
            path=path
        )
        self.parent.logger.info(
            "media resolution from hook: {}".format(media_resolution)
        )

        # register the publish:
        publish_data = {
            "tk": self.parent.engine.sgtk,
            "context": self.parent.engine.context,
            "comment": comment,
            "path": path,
            "name": name,
            "version_number": version_number,
            "thumbnail_path": thumbnail,
            "task": self.parent.engine.context.task,
            "published_file_type": publish_type,
            "dependency_paths": dependencies,
            "sg_fields":{"sg_media_resolution": media_resolution},
        }

        sg_publish = sgtk.util.register_publish(**publish_data)

        self.parent.tank.shotgun.update(
            "PublishedFile", sg_publish["id"], {"sg_status_list": "ip"}
        )

        return sg_publish

    # ----------------------------------------------------------------------------------

    def publish_layer_groups_review_images(self, item, layer_group_paths, scene_data):

        layer_group_items = []

        self.resize_value = self.get_resize_value()

        for layer_group_path in layer_group_paths.keys():
            self.parent.logger.info("layer_group_path: {}".format(layer_group_path))

            layer_index = layer_group_paths[layer_group_path]

            fields = scene_data["image_publish_template"].get_fields(layer_group_path)
            fields["SEQ5"] = layer_index

            image_seq_publish_template = scene_data["publish_template"]
            image_seq_publish_path = image_seq_publish_template.apply_fields(fields)
            image_seq_publish_path_basename = os.path.basename(image_seq_publish_path)

            # copy published layer group png image to sequence review path usimg the new
            # sequence review name as file name
            self.logger.info(
                "Converting sequence image '{}', please wait".format(
                    os.path.basename(image_seq_publish_path)
                )
            )
            self.parent.logger.info(
                "Converting sequence image '{}'".format(
                    os.path.basename(image_seq_publish_path)
                )
            )
            self.export_copy_image(layer_group_path, image_seq_publish_path)

            layer_group_items.append(image_seq_publish_path)
            self.parent.logger.info(
                "image_seq_publish_path: {}".format(image_seq_publish_path)
            )

        # register publish for sequence images
        publish_name = self.parent.util.get_publish_name(
            image_seq_publish_path, sequence=True
        )

        # keys must match the values expected by the register_publish method dictionary
        # values and with the create_and_register_video dictionary keys
        publish_data = {
            "comment": item.description,
            "path": image_seq_publish_path,
            "name": publish_name,
            "version_number": scene_data["publish_version_number"],
            "thumbnail": item.get_thumbnail_as_path(),
            "publish_type": scene_data["publish_type"],
            "dependencies": [scene_data["primary_publish_path"]],
        }

        self.parent.logger.info(
            "layer_group publish_data:\n{}".format(pprint.pformat(publish_data))
        )

        sg_publish = self.register_publish(**publish_data)

        return layer_group_items, sg_publish

    # ----------------------------------------------------------------------------------

    def build_video_from_images(self, sequence_path, video_path):
        seq_obj = fileseq.findSequenceOnDisk(sequence_path)
        seq_start_frame = seq_obj.start()

        convert_cmd = (
            '-r '
            '1/5 '
            '-start_number '
            '{0} '
            '-i '
            '{1} '
            '-vf '
            '"scale=1920:-2, '
            'premultiply=inplace=1" '
            '-vcodec '
            'libx264 '
            '-shortest '
            '{2} '
            '-y'
        ).format(str(seq_start_frame ), sequence_path, video_path)

        # ensure the right binary is set
        self.ffmpeg.ffmpegCore.set_binary("convert")
        # get the full patht o the binary and use it to log the fill command
        path_to_binary  = self.ffmpeg.ffmpegCore.get_bin_path()
        self.parent.logger.info(
            "convert cmd: {} {}".format(os.path.basename(path_to_binary), convert_cmd)
        )
        _err, _info = self.ffmpeg.ffmpegCore.execute_command(convert_cmd)

        if _err:
            raise Exception("Failed to convert images to video: {}".format(_info))

    # ----------------------------------------------------------------------------------

    def create_and_register_video(
        self,
        main_publish,
        sequence_path,
        item,
        scene_data,
        sg_type,
        video_path,
        settings,
        extra_dependencies,
    ):
        # Craete video from layer groups images
        self.build_video_from_images(sequence_path, video_path)
        publish_name = self.parent.util.get_publish_name(video_path)

        dependencies = [main_publish, sequence_path]
        if extra_dependencies:
            dependencies.extend(extra_dependencies)

        sg_publish_data = {
            "comment": item.description,
            "path": video_path,
            "name": publish_name,
            "version_number": scene_data["publish_version_number"],
            "thumbnail": item.get_thumbnail_as_path(),
            "publish_type": sg_type,
            "dependencies": dependencies,
        }
        self.parent.logger.info(
            "video sg_publish_data:\n{}".format(pprint.pprint(sg_publish_data))
        )
        sg_video_publish = self.register_publish(**sg_publish_data)

        first, last = self.get_shot_range(settings, sequence_path)

        # Generate version
        sg_version = self.submit_version(
            sequence_path,
            video_path,
            [sg_video_publish],
            self.parent.engine.context.task,
            item.description,
            True,
            first,
            last,
            "rev",
        )
        event_loop = QtCore.QEventLoop()
        thread = UploaderThread(
            self.parent, sg_version, video_path, item.get_thumbnail_as_path(), True
        )
        thread.finished.connect(event_loop.quit)
        thread.start()
        event_loop.exec_()

        # log any errors generated in the thread
        for e in thread.get_errors():
            self.parent.log_error(e)

        return sg_video_publish

    def publish(self, settings, item):

        root_item = self.get_root_item(item)
        already_published = root_item.properties.get("media_data_already_published", False)
        if already_published:
            return

        # Load ffmpeg framework
        self.ffmpeg = self.parent.engine.custom_frameworks.get(
            "mty-framework-ffmpeg", None
        )
        if not self.ffmpeg:
            self.parent.engine.logger.error(
                "Couldn't get ffmpeg framework from engine.custom_frameworks"
            )
            self.ffmpeg = self.load_framework("mty-framework-ffmpeg")

        # Load ImageMagick framework
        self.imagemagick = self.parent.engine.custom_frameworks.get(
            "mty-framework-imagemagick", None
        )
        if not self.imagemagick:
            self.parent.engine.logger.error(
                "Couldn't get imagemagick framework from engine.custom_frameworks"
            )
            self.imagemagick = self.load_framework("mty-framework-imagemagick")
        self.ImageMagickCoreTools = self.imagemagick.imageMagickCore

        root_item.properties.setdefault("sg_publish_extra_data", [])
        sg_publish_extra_data = root_item.properties["sg_publish_extra_data"]

        published_layer_group_image_paths = root_item.properties["layer_group_paths"]
        ps_proxy_publish_path = root_item.properties.get("ps_proxy_publish_path", None)

        # get templates, file types and layer group data from item (from collector)
        templates_and_file_types = item.properties.get("templates_and_file_types", None)

        if not templates_and_file_types:
            raise Exception("Couldn't get templates from item.")
        if not published_layer_group_image_paths:
            raise Exception("Couldn't published layer group image paths from item.")

        # Get PS document data from templates_and_file_types dict (gathered from collector)
        # PS_document = templates_and_file_types.get("PS_document")
        PS_document_path = templates_and_file_types.get("PS_document_path")
        # instead of getting the document from the templates and file types dict,
        # get it from the current opened document, because the previous document
        # has been already closed by the main publisher
        PS_document = self.parent.engine.adobe.get_active_document()

        primary_publish_template = templates_and_file_types.get("primary_publish_template")
        work_template = templates_and_file_types.get("primary_work_template")
        image_publish_template = templates_and_file_types.get("image_publish_template")
        image_review_publish_template = templates_and_file_types.get(
            "image_seq_publish_template"
        )
        video_publish_template = templates_and_file_types.get("video_publish_template")

        # get template fields and apply them to the relevant templates
        fields = work_template.get_fields(PS_document_path)
        primary_publish_path = primary_publish_template.apply_fields(fields)
        video_publish_path = video_publish_template.apply_fields(fields)
        video_publish_type = templates_and_file_types.get("video_file_type")

        curent_scene_data = {
            "PS_document": PS_document,
            "scene_path": PS_document_path,
            "temp_folder": tempfile.gettempdir(),
            "primary_publish_template": primary_publish_template,
            "primary_publish_path": primary_publish_path,
            "work_template": work_template,
            "publish_type": templates_and_file_types.get("image_seq_file_type"),
            "publish_template": image_review_publish_template,
            "image_publish_template": image_publish_template,
            "fields": fields,
            "publish_version_number": fields["version"],
        }

        layer_group_review_paths, sg_publish = self.publish_layer_groups_review_images(
            item, published_layer_group_image_paths, curent_scene_data
        )
        sg_publish_extra_data.append(sg_publish)

        self.parent.logger.info(
            "image_seq_publish_path: {}".format(sg_publish["path"]["local_path"])
        )

        if ps_proxy_publish_path:
            list_published_layer_group_image_paths = list(
                published_layer_group_image_paths.keys()
                )
            list_published_layer_group_image_paths.append(ps_proxy_publish_path)
        self.parent.logger.info(
            "published_layer_group_image_paths (to add as dependencies of the main item):\n{}".format(
                pprint.pformat(published_layer_group_image_paths)
            )
        )

        # Create video from images
        video_publish = self.create_and_register_video(
            primary_publish_path,
            sg_publish["path"]["local_path"],
            item,
            curent_scene_data,
            video_publish_type,
            video_publish_path,
            settings,
            list_published_layer_group_image_paths,
        )
        sg_publish_extra_data.append(video_publish)

        # self.parent.logger.info(
        #     "layer_groups sg_publish_extra_data: {}".format(pf(sg_publish_extra_data))
        # )

        # Update task status for revision
        sg_task = self.parent.context.task
        self.parent.logger.info(
            "End of Publish and updating task with id: {} to status: 'rev'".format(
                str(sg_task['id'])
            )
        )

        try:
            self.parent.engine.shotgun.update(
                "Task", sg_task["id"], {"sg_status_list": 'rev'}
            )
        except:
            pass

        root_item.properties["media_data_already_published"] = True

    # ----------------------------------------------------------------------------------

    def get_shot_range(self, settings, sequence_path):
        """ """

        entity = self.parent.context.entity
        sg_entity_type = entity["type"]

        if not sg_entity_type == "Shot":
            file_sequence = fileseq.findSequenceOnDisk(sequence_path)
            self.parent.logger.info("file_sequence: {}".format(file_sequence))
            self.parent.logger.info("file_sequence start: {}".format(file_sequence.start()))
            self.parent.logger.info("file_sequence end: {}".format(file_sequence.end()))
            return file_sequence.start(), file_sequence.end()

        sg_filters = [["id", "is", entity["id"]]]
        sg_in_field = "sg_cut_in"
        sg_out_field = "sg_cut_out"
        fields = [sg_in_field, sg_out_field]

        data = self.parent.shotgun.find_one(
            sg_entity_type, filters=sg_filters, fields=fields
        )

        self.logger.debug(data)

        if not data:
            message = "Cant find a valid shotgun entity for: {}".format(entity)
            self.logger.error(message)
            raise Exception(message)

        first_frame = data.get(sg_in_field)
        last_frame = data.get(sg_out_field)

        if not first_frame or not last_frame:
            message = "Can't find a valid frame range from: {}".format(entity)
            self.logger.error(message)
            raise Exception(message)

        return first_frame, last_frame

    # def _get_project_resolution_from_SG(self):

    #     project_data = {}

    #     filters = [
    #         ["id", "is", self.parent.engine.context.project["id"]],
    #     ]
    #     fields = ["sg_resolution"]

    #     project_data = self.parent.engine.shotgun.find_one(
    #         entity_type="Project", filters=filters, fields=fields
    #     )

    #     return project_data

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root

    def fix_path(self, path):
        """
        Fixes the path to be compatible with the Harmony API, which only accepts forward
        slashes
        """
        # fix image_path
        path = path.replace("\\\\", "/")
        path = path.replace("\\", "/")

        return path

    # ----------------------------------------------------------------------------------

    def submit_version(
        self,
        path_to_frames,
        path_to_movie,
        sg_publishes,
        sg_task,
        comment,
        store_on_disk,
        first_frame,
        last_frame,
        work_status,
        override_entity=False,
        has_slate=False,
    ):
        """
        Create a version in Shotgun for
        this path and linked to this publish.

        """

        user = self.parent.engine.context.user

        # create a name for the version based on the file name
        # grab the file name, strip off extension
        name = os.path.splitext(os.path.basename(path_to_movie))[0]
        name = name.replace("_", " ")
        # name = name.capitalize()

        if override_entity != False:
            entity = override_entity
        else:
            entity = self.parent.engine.context.entity

        # Create the version in Shotgun
        data = {
            "code": name,
            "sg_status_list": work_status,
            "entity": entity,
            "sg_task": sg_task,
            "sg_version_type": "Production",
            "sg_first_frame": first_frame,
            "sg_last_frame": last_frame,
            "frame_count": (last_frame - first_frame + 1),
            "frame_range": "{}-{}".format(first_frame, last_frame),
            "sg_frames_have_slate": False,
            "published_files": sg_publishes,
            "created_by": user,
            "description": comment,
            "sg_path_to_frames": path_to_frames,
            "sg_movie_has_slate": True,
            "project": self.parent.engine.context.project,
            "user": user,
            "sg_movie_has_slate": has_slate,
        }

        if store_on_disk:
            data["sg_path_to_movie"] = path_to_movie

        sg_version = self.parent.engine.shotgun.create("Version", data)

        self.parent.logger.debug("Created version in shotgun: {}".format(str(data)))

        return sg_version

    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        pass


class UploaderThread(QtCore.QThread):
    """
    Simple worker thread that encapsulates uploading to shotgun.
    Broken out of the main loop so that the UI can remain responsive
    even though an upload is happening
    """

    def __init__(self, app, version, path_to_movie, thumbnail_path, upload_to_shotgun):
        QtCore.QThread.__init__(self)
        self._app = app
        self._version = version
        self._path_to_movie = path_to_movie
        self._thumbnail_path = thumbnail_path
        self._thumbnail_path = thumbnail_path
        self._upload_to_shotgun = upload_to_shotgun
        self._errors = []

    def get_errors(self):
        """
        can be called after execution to retrieve a list of errors
        """
        return self._errors

    def run(self):
        """
        Thread loop
        """
        upload_error = False

        if self._upload_to_shotgun:
            try:
                self._app.tank.shotgun.upload(
                    "Version",
                    self._version["id"],
                    self._path_to_movie,
                    "sg_uploaded_movie",
                )
            except:
                self._errors.append(
                    "Movie upload to Shotgun failed: {}".format(traceback.print_exc())
                )
                upload_error = True

        if not self._upload_to_shotgun or upload_error:
            try:
                self._app.tank.shotgun.upload_thumbnail(
                    "Version", self._version["id"], self._thumbnail_path
                )
            except:
                self._errors.append(
                    "Thumbnail upload to Shotgun failed: {}".format(traceback.print_exc())
                )
