# -*- coding: utf-8 -*-
# Standard library:
import pprint
# ___   ___   ___   ___   ___   ___  ___
# Third party:
import sgtk

# ___   ___   ___   ___   ___   ___  ___
# Project:
HookBaseClass = sgtk.get_hook_baseclass()
pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)
pf = pprint.pformat


# ====================================================================

class TemporalFilter(HookBaseClass):
    def __init__(self, parent):
        super(TemporalFilter, self).__init__(parent)

    def accept_shot_by_tag(self, list_of_tags):
        result = self.parent.shotgun.find_one(
            entity_type='Shot',
            filters=[
                ['id', 'is', self.parent.context.entity['id']],
                ['tags', 'in', list_of_tags]
            ]
        )

        if result:
            return True
        else:
            return False
