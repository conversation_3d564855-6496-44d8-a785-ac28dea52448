#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################


import os
import pprint

import sgtk

HookBaseClass = sgtk.get_hook_baseclass()

class BreakdownSceneTransfers(HookBaseClass):
    """
    Breakdown transfer operations for all engines.

    This implementation handles transfer logic for all engines.
    """

    def update(self, items):
        """
        Ensures local files for given a number of scene items passed from the app.
        Then it calls the parent inherent hook to procedd with the actual update.

        Once a selection has been performed in the main UI and the user clicks
        the update button, this method is called.

        The items parameter is a list of dictionaries on the same form as was
        generated by the scan_scene hook above. The path key now holds
        the that each node should be updated *to* rather than the current path.
        """

        engine = self.parent.engine
        logger = engine.logger
        logger.debug('Running scene transfer hook...')

        # ensure metasync framework is available, but only load it once
        if not hasattr(self, 'metasync'):
            self.metasync = self.load_framework("mty-framework-metasync")
            logger.debug("metasync: %s" % self.metasync)

        transfersManager = self.metasync.transfersManager

        publish_paths = []

        for i in items:
            publish_paths.append(i["path"])

        publishes = sgtk.util.find_publish(self.parent.tank, publish_paths)

        for path in publishes:
            sg_publish_data = publishes[path]

            if not os.path.exists(path):
                sg_publish_data = publishes[path]

                transfersManager.ensure_file_is_local(path, sg_publish_data)
                transfersManager.ensure_local_dependencies(sg_publish_data)
            else:
                transfersManager.ensure_local_dependencies(sg_publish_data)

        # finally call the original method to perform the actual scene update
        super(BreakdownSceneTransfers, self).update(items)
