# -*- coding: utf-8 -*-
# Standard library:
import os
import re
import sys
import json
import pprint
import shutil
import tempfile
import traceback

import sgtk

from sgtk.platform.qt import QtCore

# ======================================================================================

HookBaseClass = sgtk.get_hook_baseclass()


pp = pprint.pprint
pf = pprint.pformat


class LayerGroupsOrderPublishPlugin(HookBaseClass):
    @property
    def icon(self):

        return os.path.join(self.disk_location, os.pardir, "icons", "image_publish.png")

    @property
    def item_filters(self):
        """
        List of item types that this plugin is interested in.

        Only items matching entries in this list will be presented to the
        accept() method. Strings can contain glob patters such as *, for example
        ["maya.*", "file.maya"]
        """
        return ["photoshop.document"]

    @property
    def description(self):

        return """
        <p>This plugin publishes JSON file with the output Layer Groups Order.</p>

        """

    @property
    def settings(self):

        # inherit the settings from the base publish plugin
        plugin_settings = super(LayerGroupsOrderPublishPlugin, self).settings or {}

        # settings specific to this class
        review_publish_settings = {
            "File type": {
                "type": "string",
                "default": "Layer Group Order",
                "description": "Extension to define the output file format.",
            },
            "File extension": {
                "type": "string",
                "default": "json",
                "description": "Extension to define the output file format.",
            },
            "Layer Group Order Template": {
                "type": "string",
                "default": None,
                "description": "Template to pulish Layer Group Order",
            },
            "Layer Group Template": {
                "type": "string",
                "default": None,
                "description": "Template to get Layer Group path",
            },
            "Layer Group Proxy Template": {
                "type": "string",
                "default": None,
                "description": "Template to get Layer Group Proxy path",
            }
        }

        # update the base settings
        plugin_settings.update(review_publish_settings)

        return plugin_settings

    def accept(self, settings, item):

        self.parent.logger.info("\nstart accept plugin -----------------------------\n")

        return {"accepted": True, "checked": True}


    def validate(self, settings, item):
        root_item = self.get_root_item(item)
        layer_groups_data = root_item.properties.get("all_output_layer_groups", {})
        if not layer_groups_data:
            self.parent.logger.error("The current session cant get layer groups")
            return False

        return True


    def register_publish(
        self,
        comment,
        path,
        name,
        version_number,
        thumbnail,
        published_file_type,
        dependencies=[],
    ):

        # Register the publish:
        publish_data = {
            "tk": self.parent.engine.sgtk,
            "context": self.parent.engine.context,
            "comment": comment,
            "path": path,
            "name": name,
            "version_number": version_number,
            "thumbnail_path": thumbnail,
            "task": self.parent.engine.context.task,
            "published_file_type": published_file_type,
            "dependency_paths": dependencies,
        }
        sg_publish = sgtk.util.register_publish(**publish_data)

        self.parent.tank.shotgun.update(
            "PublishedFile", sg_publish["id"], {"sg_status_list": "ip"}
        )

        return sg_publish

    # ----------------------------------------------------------------------------------

    def publish(self, settings, item):
        # load a properties
        self.engine = self.parent.engine
        self.shotgun = self.parent.engine.shotgun
        self.context = self.parent.engine.context
        self.publisher = self.parent

        # Get the all layer groups data from root item
        root_item = self.get_root_item(item)
        layer_groups_data = root_item.properties.get("all_output_layer_groups", {})
        if not layer_groups_data:
            return

        root_item = self.get_root_item(item)
        root_item.properties.setdefault("sg_publish_extra_data", [])
        sg_publish_extra_data = root_item.properties["sg_publish_extra_data"]

        # get the primary path from item
        primary_path = item.properties.sg_publish_data["path"]["local_path"]

        # get seettings
        name_templete_layer_groups_order = settings.get("Layer Group Order Template").value
        name_templete_layer_group = settings.get("Layer Group Template").value
        name_templete_layer_group_proxy = settings.get("Layer Group Proxy Template").value
        layer_order_type = settings.get("File type").value

        # loads templates
        templete_layer_group =  self.engine.get_template_by_name(name_templete_layer_group)
        templete_layer_group_proxy =  self.engine.get_template_by_name(name_templete_layer_group_proxy)
        templete_layer_groups_order =  self.engine.get_template_by_name(name_templete_layer_groups_order)

        # get fields to get  all layer groups path
        primary_template = self.parent.sgtk.template_from_path(primary_path)
        fields = primary_template.get_fields(primary_path)
        fields["extension"] = "png"
        dict_layer_order = {}


        for layer_group_key, layer_group_value in layer_groups_data.items():
            layer_name = layer_groups_data[layer_group_key]["reformat_name"]
            new_fields = {"photoshop.layer_name": layer_name}
            new_fields.update(fields)

            layer_group_path = templete_layer_group.apply_fields(new_fields)
            layer_group_proxy_path = templete_layer_group_proxy.apply_fields(new_fields)
            old_data = layer_groups_data.get(layer_group_key, {})

            old_data["layer_group_path"] = layer_group_path
            old_data["layer_group_proxy_path"] = layer_group_proxy_path

            dict_layer_order[layer_group_key] = old_data

        path_order = templete_layer_groups_order.apply_fields(fields)

        # create folder if not exist
        if not os.path.exists(os.path.dirname(path_order)):
            os.makedirs(os.path.dirname(path_order))

        # save layer groups order in json file
        with open(path_order, "w") as outfile:
            json.dump(dict_layer_order, outfile)

        # Get publish name
        publish_name = self.parent.util.get_publish_name(
            path_order, sequence=False
        )
        self.parent.logger.info(
            "publish_name from util (not used): {}".format(publish_name)
        )

        # Get publish version
        publish_version = fields.get("version")

        # register publish data
        publish_dict ={
            "comment": item.description,
            "path": path_order,
            "name": publish_name,
            "version_number": publish_version,
            "thumbnail": item.get_thumbnail_as_path(),
            "published_file_type": layer_order_type,
            "dependencies":[primary_path]
            }

        sg_publishes = self.register_publish(**publish_dict)
        sg_publish_extra_data.append(sg_publishes)

    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        pass

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root
