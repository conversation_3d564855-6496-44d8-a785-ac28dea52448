# Copyright (c) 2019 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
import os
import json
import time
import pprint

import sgtk


HookBaseClass = sgtk.get_hook_baseclass()

pp = pprint.pprint
pf = pprint.pformat


class UnpublishedFilesError(Exception):
    pass


class AfterEffectsProjectPublishUpdatePlugin(HookBaseClass):
    """
    Plugin for extending an after effects project publish. It adds published
    (upstream) dependencies to the published file.

    This hook relies on functionality found in the base file publisher hook in
    the publish2 app and should inherit from it in the configuration and the engine
    publish document hook. The hook setting for this plugin should look something
    like this:

        hook: >-
            {self}/publish_file.py
            :{engine}/tk-multi-publish2/basic/publish_document.py
            :{config}/tk-multi-publish2/aftereffects/update_publish_document.py

    """

    @property
    def settings(self):

        return super(AfterEffectsProjectPublishUpdatePlugin, self).settings

    @property
    def item_filters(self):

        return ["aftereffects.project"]

    def accept(self, settings, item):

        return super(AfterEffectsProjectPublishUpdatePlugin, self).accept(
            settings, item
        )

    def validate(self, settings, item):

        engine = self.parent.engine
        context = engine.context
        task_id = context.task["id"]
        self.parent.engine.logger.debug("context:\n{}".format(pf(context)))
        self.parent.engine.logger.debug("task_id: {}".format(task_id))


        validate_published_files = True

        # # ---- load ValueOverrides
        # try:
        #     import ValueOverrides
        #     self.parent.logger.info("Imported ValueOverrides")
        #     setting_values_str = os.environ["ValueOverridesSettings"]
        #     setting_values = json.loads(setting_values_str)
        #     self.parent.logger.debug(
        #         "ValueOverridesSettings:\n\n{}\n".format(
        #             pf(setting_values_str)
        #         )
        #     )
        #     CoreOverrides = ValueOverrides.CoreValueOverrides(
        #         setting_values, engine.context.project, engine.shotgun
        #     )
        # except ImportError as e:
        #     self.parent.logger.warning("Could not import ValueOverrides")
        #     # we know it will not work if the engine app as not
        #     # launched from the before app launch hook
        #     self.parent.logger.warning(e)
        #     CoreOverrides = None
        #     pass

        # value_code = "mty.publisher.afx.validation.published_files"

        # if CoreOverrides:
        #     CoreOverrides.clear_cache()
        #     project_overrides = CoreOverrides.get_project_overrides()
        #     self.parent.logger.debug(
        #             "CoreOverrides.get_project_overrides:\n\n{}\n".format(
        #                 pf(project_overrides)
        #             )
        #         )

        #     for override in project_overrides:
        #         if override["code"] == value_code:
        #             task_ids = [task["id"] for task in override["sg_tasks"] if override["sg_tasks"]]
        #             self.parent.logger.info("task_ids: {}".format(task_ids))
        #             if task_id in task_ids:
        #                 validate_published_files = override["sg_boolean_value"]

        # Get value overrides for published files validation
        # Load value overrides framework -----------------------------------------------
        valueoverrides = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        ) or self.load_framework("mty-framework-valueoverrides")

        if not valueoverrides:
            self.parent.engine.logger.error("Couldn't load value overrides framework")

        if valueoverrides:
            override_link = {
                "type": "Task",
                "id": self.parent.engine.context.task["id"],
            }
            default_value_code = "mty.publisher.afx.validation.published_files"

            validate_published_files = valueoverrides.get_value(
                default_value_code, link=override_link
            )

            self.parent.engine.logger.info(
                "validate_published_files from override: {}".format(
                    validate_published_files
                )
            )

        self.parent.engine.logger.info(
            "validate_published_files: {}".format(validate_published_files)
        )

        # ---- check for unpublished files
        published_files, unpublished_files = self.get_published_and_unpublished_files()

        if published_files:
            # keep only one instances of each publish, because every published file
            # could be used more than once in the project.
            unique_published_files = []
            for publish in published_files:
                if publish not in unique_published_files:
                    unique_published_files.append(publish)

            self.parent.logger.info("-" * 80)
            self.parent.logger.info(
                "Total number of published files: {}\n{}".format(
                    len(unique_published_files), pf(unique_published_files)
                )
            )

            item.properties["publish_dependencies"] = unique_published_files

        if unpublished_files:
            unpublished_files_missin_item_name = [item[0] for item in unpublished_files]
            self.parent.logger.info("-" * 80)
            self.parent.logger.info(
                "Total number of not published files: {}\n{}".format(
                    len(unpublished_files), pf(unpublished_files)
                )
            )

            error_msg = (
                "You are using unpublished files in your project.\n"
                "Yo need to remove them to be able to publish.\n"
                "\nUnpublished files:\n{}"
                ""
            ).format(pf(unpublished_files_missin_item_name))


            # Only throws the error if there is n override in place for this task
            if validate_published_files:
                unpublished_files_msg = [item[-1] for item in unpublished_files]
                self.logger.error(
                    error_msg,
                    extra={
                        "action_show_more_info": {
                            "label": "Show Items",
                            "tooltip": "Display the list of unpublished items.",
                            "text": "<pre>{}</pre>".format(
                                pf(unpublished_files_msg)
                            ),
                        }
                    },
                )

                self.logger.error(
                    error_msg,
                    extra={
                        "action_button": {
                            "label": "Select items",
                            "tooltip": "Select all unpublished items",
                            "callback": lambda: self.select_unpublished_items(
                                unpublished_files
                            ),
                        }
                    },
                )
                raise UnpublishedFilesError(error_msg)
            else:
                self.parent.engine.logger.info("Skipping unpublished files validation")
        return super(AfterEffectsProjectPublishUpdatePlugin, self).validate(
            settings, item
        )

    def publish(self, settings, item):

        publish_fields = item.properties.get("publish_fields", {})
        publish_fields.update({"sg_status_list": "rev"})
        item.properties["publish_fields"] = publish_fields

        return super(AfterEffectsProjectPublishUpdatePlugin, self).publish(
            settings, item
        )

    def finalize(self, settings, item):

        publish_data = item.properties.sg_publish_data
        dependency_paths = item.properties.get("publish_dependencies")

        dependencies = []

        if dependency_paths:
            dependencies = sgtk.util.find_publish(self.parent.sgtk, dependency_paths)
            dependencies = [dependencies[path] for path in dependencies]

        if dependencies:
            filters = [
                ["published_file", "is", publish_data],
                ["dependent_published_file", "in", dependencies],
            ]
            publish_dependencies = self.parent.shotgun.find(
                "PublishedFileDependency", filters
            )

            batch_data = []
            for publish in publish_dependencies:
                batch_data.append(
                    {
                        "request_type": "update",
                        "entity_type": "PublishedFileDependency",
                        "entity_id": publish["id"],
                        "data": {"sg_type": "Hard"},
                    }
                )

            self.parent.shotgun.batch(batch_data)

        # self.set_sg_status_list(publish_data)

        # Update task status for revision
        sg_task = self.parent.context.task
        self.parent.logger.info(
            "End of Publish and updating task with id: {} to status: 'rev'".format(
                str(sg_task['id'])
            )
        )

        try:
            self.parent.engine.shotgun.update(
                "Task", sg_task["id"], {"sg_status_list": 'rev'}
            )
        except:
            pass

        return super(AfterEffectsProjectPublishUpdatePlugin, self).finalize(
            settings, item
        )

    # published files validation start -------------------------------------------------
    def set_sg_status_list(self, publish_data):
        # Update downstream_published_files status for publish
        batch_data_status = []
        status = "rev"

        batch_data_status.append(
                    {
                        "request_type": "update",
                        "entity_type": "PublishedFile",
                        "entity_id": publish_data["id"],
                        "data": {"sg_status_list": status},
                    }
                )
        filters = [
                ["id", "is", publish_data["id"]],
            ]
        fields = ["downstream_published_files"]

        downstreams = self.parent.shotgun.find_one(
                "PublishedFile", filters, fields
            )
        downstreams = downstreams.get("downstream_published_files", [])

        if downstreams:
            for downstream in downstreams:
                batch_data_status.append(
                    {
                        "request_type": "update",
                        "entity_type": "PublishedFile",
                        "entity_id": downstream["id"],
                        "data": {"sg_status_list": status},
                    }
                )

        self.parent.engine.logger.info(
            "Changing status to rev for publishes:\n{}".format(pf(batch_data_status))
        )

        self.parent.shotgun.batch(batch_data_status)

    def collect_all_items_in_project_by_type(self, item_type):
        items = []

        engine = self.parent.engine
        adobe = engine.adobe

        project_items = adobe.app.project.items

        for i, item in enumerate(engine.iter_collection(project_items)):
            if item.typeName == item_type:
                if item not in items:
                    items.append(item)

        return items

    def get_tk_from_paths(self, file_paths):

        for path in file_paths:
            # self.parent.logger.info("path[0] tk: {}".format(path[0]))
            try:
                tk = sgtk.sgtk_from_path(path[0])
                return tk
            except:
                continue
        return None

    def get_published_files_entities_dict(self, file_paths):
        result = {}

        paths_only = [path[0] for path in file_paths]

        tk = self.get_tk_from_paths(file_paths)
        if tk:
            result = sgtk.util.find_publish(tk, paths_only)

        return result

    def filter_published_and_unpublished_files(self, file_paths, published_files_dict):
        published_files = []
        unpublished_files = []

        for path in file_paths:
            # self.parent.logger.info("path[0] filter: {}".format(path[0]))
            if not path[0] in published_files_dict.keys():
                unpublished_files.append([path[0], path[1]])
            else:
                # published_files.append(published_files_dict.get(path[0]))
                published_files.append(path[0])

        return published_files, unpublished_files

    def get_published_and_unpublished_files(self):

        # items = self.collect_all_items_in_project_by_type("Footage")

        # self.parent.logger.info("Time to collect all items: {}".format(collect_time_total))
        items = self.collect_all_footage_items()

        file_paths = [[item.get("fsName"), item.get("name")] for item in items if item.get("fsName")]

        self.parent.logger.info("-" * 80)
        file_paths = list(set(tuple(i) for i in file_paths))
        self.parent.logger.info("Total number of files: {}".format(len(file_paths)))
        self.parent.logger.debug("Files:\n{}".format(pf(file_paths)))

        published_files_dict = self.get_published_files_entities_dict(file_paths)
        self.parent.logger.debug(
            "published_files_dict:\n{}".format(pf(published_files_dict))
        )

        published_files = None
        unpublished_files = None

        (
            published_files,
            unpublished_files,
        ) = self.filter_published_and_unpublished_files(
            file_paths, published_files_dict
        )

        return published_files, unpublished_files

    def deselect_all_items(self):
        items = []

        engine = self.parent.engine
        adobe = engine.adobe

        project_items = adobe.app.project.items

        for i, item in enumerate(engine.iter_collection(project_items)):
            item.selected = False

    def select_unpublished_items(self, unpublished_items):

        # first deselect any selected item
        # self.deselect_all_items()

        # select any item that has unpublished file references
        unpublished_items_only = []
        self.parent.logger.info("Start loop of unpublished_items:")
        for item in unpublished_items:
            self.parent.logger.info(item)
            self.parent.logger.info(item[-1])
            unpublished_items_only.append(item[-1])
        # unpublished_items_only = [item[-1] ]

        self.parent.logger.info(
            "unpublished_items_only:\n{}".format(pf(unpublished_items_only))
        )
        str_list_unpublished_items_only = [
            '"{}"'.format(unpiblished_item) for unpiblished_item in unpublished_items_only
            ]
        str_list_unpublished_items_only = ", ".join(str_list_unpublished_items_only)
        cmd = """

    function selectFootageItemsByName(names) {
        for (var i = 1; i <= app.project.numItems; i++) {
            var item = app.project.item(i);
            if (item instanceof FootageItem && names.indexOf(item.name) !== -1) {
                item.selected = true;
            } else {
                item.selected = false;
            }
        }
    }

    var namesToSelect = [%s];



    selectFootageItemsByName(namesToSelect);
    """ % str_list_unpublished_items_only
        engine = self.parent.engine
        self.parent.logger.debug("select unpublished files cmd:{}".format(cmd))
        engine.adobe.rpc_eval(cmd)

        # for item in unpublished_items_only:
        #     item.selected = True

    # published files validation end ---------------------------------------------------

    def collect_all_footage_items(self):
        engine = self.parent.engine

        temp_dir = engine.project_path
        temp_dir = os.path.dirname(temp_dir)
        temp_file_path = os.path.join(temp_dir, "footage.json")
        temp_file_path = temp_file_path.replace("\\","/")

        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)

        self.parent.logger.info("temp_file_path:{}".format(temp_file_path))

        cmd = """
    function getAllFootagesInProject() {
        var footages = [];
        var project = app.project;

        if (project && project.rootFolder) {
            searchFootagesInFolder(project.rootFolder, footages);
        }

        return footages;
    }


    function searchFootagesInFolder(folder, footages) {
        for (var i = 1; i <= folder.numItems; i++) {
            var item = folder.item(i);

            if (item instanceof FolderItem) {

                searchFootagesInFolder(item, footages);
            } else if (item instanceof FootageItem && item.file && item.file.fsName) {

                footages.push(item);
            } else if (item instanceof CompItem) {

                searchFootagesInComp(item, footages);
            }
        }
    }


    function searchFootagesInComp(comp, footages) {
        for (var j = 1; j <= comp.numLayers; j++) {
            var layer = comp.layer(j);

            if (layer.source instanceof FootageItem && layer.source.file && layer.source.file.fsName) {

                footages.push(layer.source);
            } else if (layer.source instanceof CompItem) {

                searchFootagesInComp(layer.source, footages);
            }
        }
    }


    function writeFootagesToTxt(footages, filePath) {
        var file = new File(filePath);
        if (file.open("w")) {
            file.writeln("[");
            for (var i = 0; i < footages.length; i++) {
                var footageFsName = decodeURI(footages[i].file.fsName);
                var footageName = footages[i].name;
                file.writeln('  {"fsName": "' + footageFsName + '", "name": "' + footageName + '"}' + (i < footages.length - 1 ? ',' : ''));
            }
            file.writeln("]");
            file.close();
        }
    }

    var allFootages = getAllFootagesInProject();

    var txtFilePath = "%s";

    writeFootagesToTxt(allFootages, txtFilePath);

    """%(temp_file_path)

        self.parent.logger.debug("save footage files to file cmd:{}".format(cmd))
        trys = 0
        data_from_file = None
        engine.adobe.rpc_eval(cmd)
        while trys < 10:
            if os.path.exists(temp_file_path):
                break
            else:
                time.sleep(2)
                trys += 1
        with open(temp_file_path, 'r') as file:
            conten = file.read()
            conten = conten.replace("\\","/")
            data_from_file = json.loads(conten)
            self.parent.logger.debug(
                "data_from_file:\n{}".format(pf(data_from_file))
            )

        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)

        return data_from_file
