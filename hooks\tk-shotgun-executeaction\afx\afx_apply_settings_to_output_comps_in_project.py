#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

"""
An app that project settings to all compositions in the project
shot and project settings collected from SG:
    shot settings:
        - shot sg_cut_in (first shot frame)
        - shot sg_cut_out (last shot frame)
    project settings:
        - frame rate
        - resolution
        - working color space

"""

from tank import Hook
from tank import TankError
from tank.platform import Application
from tank.platform.qt import QtCore, QtGui
import tank

import os
import re
import pprint


class ProcessItemsHook(Hook):
    def execute(self, **kwargs):

        self.parent.engine.logger.info(
            "execute action apply_settings_to_output_compositions start. {}".format(
                "-" * 80
            )
        )

        result = self.apply_settings_to_output_compositions()

        self.parent.engine.logger.info(
            "execute action apply_settings_to_output_compositions end. {}".format(
                "-" * 80
            )
        )

        return result

    def apply_settings_to_output_compositions(self):

        adobe = self.parent.engine.adobe

        result = {"succes": [1], "messages": [], "errors": []}

        # Get relevant data for the hook
        shot_data = self.get_shot_data_from_SG()
        project_data = self.get_project_data_from_SG()

        self.parent.engine.logger.info(
            "shot_data:\n{}".format(pprint.pformat(shot_data))
        )
        self.parent.engine.logger.info(
            "project_data:\n{}".format(pprint.pformat(project_data))
        )

        compositions = self.collect_all_compositions_in_project()

        if not compositions:
            msg = ("Couldn't get project compositions!")
            self.show_message(msg, icon="Question")
            return result

        list_of_output_comps = self.filter_output_compositions(compositions)
        # list_of_output_comps = list(set(list_of_output_comps))
        self.parent.engine.logger.info(
            "Found {} output compositions".format(len(list_of_output_comps))
        )

        if not list_of_output_comps:
            # msg = ("Couldn't get any output composition!")
            msg = (
                "Couldn't find any Output Comp.\n(TIP: your output comp name must start"
                " with 'Output' and/or it might be missing the phrase 'AFX Output Comp'"
                " in its comment.)"
            )
            self.show_message(msg, icon="Warning")
            return result

        self.parent.engine.logger.debug(
            "Filtered output compositions:\n{}".format(
                pprint.pformat(list_of_output_comps)
            )
        )

        frame_rate = project_data.get("sg_fps")
        shot_first_frame = shot_data.get("sg_cut_in")
        shot_last_frame = shot_data.get("sg_cut_out")
        shot_duration = (shot_last_frame - shot_first_frame) + 1
        # resolution = project_data.get("sg_resolution")

        # get resolution from SG
        hook_expression = "{config}/get_entity_resolution.py"
        resolution = self.parent.engine.execute_hook_expression(
            hook_expression,
            "get_resolution",
            engine=self.parent.engine,
        )

        for comp in list_of_output_comps:
            self.set_composition_settings(
                comp,
                frame_rate,
                shot_first_frame,
                shot_duration,
                resolution
            )

            self.parent.engine.logger.info("Getting output comp layers")
            list_of_comp_layers = self.get_composition_layers(comp)
            if list_of_comp_layers:
                self.parent.engine.logger.info(
                    "{} layers found.".format(len(list_of_comp_layers))
                )
                self.parent.engine.logger.info("Modifying each comp layer")
                for layer in list_of_comp_layers:
                    self.parent.engine.logger.info(
                        "layer: {}".format(layer.name)
                    )
                    self.set_layer_settings(
                        layer,
                        frame_rate,
                        shot_first_frame,
                        shot_duration,
                    )

        output_comps_num = len(list_of_output_comps)
        comp_suffix = "" if output_comps_num == 1 else "s"
        composition_names = [comp.name for comp in list_of_output_comps]
        msg = (
            "Finished applying the shot settings to {} output composition{}.\n\n{}"
        ).format(output_comps_num, comp_suffix, pprint.pformat(composition_names))

        self.show_message(msg, icon="Information")
        # alert_box = adobe.alert(msg)

        return result

    def get_shot_data_from_SG(self):
        """
        Returns a dictionary of shot data from SG if the current context
        is a shot of the following form:

        {
            'id': (int),
             'sg_cut_duration': (int),
             'sg_cut_duration_in_seconds': (float),
             'sg_cut_in': (int),
             'sg_cut_out': (int),
             'type': (str),
         }
        """

        shot_data = {}

        if self.parent.engine.context.entity["type"] == "Shot":
            filters = [
                ["id", "is", self.parent.engine.context.entity["id"]],
            ]
            fields = [
                "sg_cut_in",
                "sg_cut_out",
                "sg_cut_duration",
                "sg_cut_duration_in_seconds",
            ]

            shot_data = self.parent.engine.shotgun.find_one(
                entity_type="Shot", filters=filters, fields=fields
            )

        return shot_data

    def get_project_data_from_SG(self):
        """
        Returns a dictionary of project data from SG relevant to a
        shot of the following form:

            {
                'code': (str),
                 'id': (int),
                 'sg_fps': (float),
                 'sg_output_color_space': (str),
                 'sg_working_color_space': (str),
                 'type': (str),
             }
        """

        project_data = {}

        filters = [
            ["id", "is", self.parent.engine.context.project["id"]],
        ]
        fields = [
            "code",
            "id",
            "sg_fps",
            "sg_working_color_space",
            "sg_output_color_space",
        ]

        project_data = self.parent.engine.shotgun.find_one(
            entity_type="Project", filters=filters, fields=fields
        )

        return project_data

    def collect_all_compositions_in_project(self):
        compositions = []

        adobe = self.parent.engine.adobe

        project_items = adobe.app.project.items

        for i, item in enumerate(self.parent.engine.iter_collection(project_items)):
            if item.typeName == "Composition":
                compositions.append(item)

        return compositions

    def filter_output_compositions(self, list_of_comps):
        """
        Filters a list of comps to get only comps which name Start with "Output" and its
        comment contains the following elements (in order):
        - AFX or afx (or any mix of those leters)
        - Output or Out (the first letter o can be capital or lower)
        - Comp or Composite or Composition (the first letter C can be capital or lower)

        The name of the comp is NOT case sensitive so either 'Output' or 'output'
        should work.
        """

        output_comps = []

        for comp in list_of_comps:
            # Match comp name
            original_comp_name = str(comp.name)
            name_pattern = re.compile(r"(?P<output_tag>^[Oo]utput)")
            match_name = re.match(name_pattern, original_comp_name)

            # Match output comp comment (metadata)
            original_comp_comment = comp.comment
            output_comp_pattern = re.compile(
                (
                    r"(?P<head>.+)?"
                    r"(?P<AFX_comp>[afxAFX]{3} [Oo]ut(put)? [Cc]omp([ositne]+)?)"
                    r"(?P<tail>.+)?"
                )
            )
            match_output_comp = re.match(output_comp_pattern, original_comp_comment)

            self.parent.engine.logger.debug(
                "original_comp_name: {}".format(original_comp_name)
            )
            self.parent.engine.logger.debug(
                "original_comp_comment: {}".format(original_comp_comment)
            )
            if match_name and match_output_comp:
                self.parent.engine.logger.debug(
                    "match_name:\n{}".format(
                        pprint.pformat(match_name.groupdict())
                    )
                )
                self.parent.engine.logger.debug(
                    "match_output_comp:\n{}".format(
                        pprint.pformat(match_output_comp.groupdict())
                    )
                )
                if comp not in output_comps:
                    output_comps.append(comp)

        return output_comps

    def get_composition_layers(self, comp):
        layers = []

        adobe = self.parent.engine.adobe

        comp_layers = comp.layers
        self.parent.engine.logger.info("comp_layers: {}".format(comp_layers))

        for i, item in enumerate(self.parent.engine.iter_collection(comp_layers)):
            self.parent.engine.logger.info("layer: {}".format(item))
            self.parent.engine.logger.info("layer.name: {}".format(item.name))

            try:
                if item.source.numLayers:
                    continue
            except:
                layers.append(item)

        return layers

    def set_composition_settings(
        self,
        comp,
        frame_rate,
        shot_first_frame,
        shot_duration,
        resolution,
    ):

        def convert_frames_to_seconds(frames, frame_rate):
            secs = int(frames / frame_rate)
            extra_frames = frames % frame_rate
            extra_secs = int(((extra_frames * 100) / 60))
            result = float("{}.{}".format(secs, extra_secs))

            return result

        adobe = self.parent.engine.adobe


        if frame_rate:
            comp.frameRate = frame_rate

        if frame_rate and shot_first_frame and shot_duration:
            comp_start_in_secs = float(shot_first_frame) / float(frame_rate)
            comp.displayStartFrame = shot_first_frame
            comp.workAreaStart = comp_start_in_secs

            comp_duration = float(shot_duration) / float(frame_rate)
            comp.duration = comp_duration
            comp.workAreaDuration = comp_duration

        if resolution:
            pattern = re.compile(r"(?P<width>\d+)x(?P<height>\d+)")
            match = re.match(pattern, str(resolution))
            if match:
                width = int(match.groupdict()["width"])
                height = int(match.groupdict()["height"])

                comp.width = width
                comp.height = height

    def set_layer_settings(
        self,
        layer,
        frame_rate,
        shot_first_frame,
        shot_duration,
    ):

        if shot_first_frame and shot_duration:

            layer_inPoint = 0.0
            layer.inPoint = layer_inPoint

            layer_outPoint = float(shot_duration) / float(frame_rate)
            layer_outPoint += layer_inPoint
            self.parent.engine.logger.info("layer.inPoint: {}".format(layer_inPoint))
            self.parent.engine.logger.info("layer.outPoint: {}".format(layer_outPoint))
            layer.outPoint = layer_outPoint

    def show_message(self, msg, icon=None):
        """
        Shows a message box with the input message
        """

        from sgtk.platform.qt import QtCore, QtGui

        icons_dict = {
            "NoIcon": QtGui.QMessageBox.NoIcon,
            "Question": QtGui.QMessageBox.Question,
            "Information": QtGui.QMessageBox.Information,
            "Warning": QtGui.QMessageBox.Warning,
            "Critical": QtGui.QMessageBox.Critical,
        }

        if not icon or icon not in icons_dict.keys():
            icon_selection = icons_dict["NoIcon"]
        else:
            icon_selection = icons_dict.get(icon, QtGui.QMessageBox.Information)

        msg_error = QtGui.QMessageBox()
        msg_error.setWindowTitle("Finished execution.")
        msg_error.setText(msg)
        msg_error.setIcon(icon_selection)
        msg_error.exec_()
