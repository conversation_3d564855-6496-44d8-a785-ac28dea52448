# -*- coding: utf-8 -*-
# Standard library:
import os
import json
from collections import namedtuple
import pprint
import re
import copy
# ___   ___   ___   ___   ___   ___  ___
# Third party:
import sgtk
import pymel.core as pm

# ___   ___   ___   ___   ___   ___  ___
# Project:
HookBaseClass = sgtk.get_hook_baseclass()
pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)
pf = pprint.pformat
# ===============================================================================

PublishMetadata = namedtuple(
    'PublishMetadata',
    ['fields', 'reference', 'publish', 'path_to_publish', 'root_node']
)


COMPARE_FLOAT_PRECISION = 3
XFORM_FLOAT_PRECISION = 6


class Overrides(HookBaseClass):
    def __init__(self, parent):
        super(Overrides, self).__init__(parent)

    # --------------------------------------------------------------------------

    def publish(self, settings, item, dry_run=False):
        list_of_publish_metadata = (
            self._list_of_publish_metadata(settings, item)
        )
        result = []

        for meta in list_of_publish_metadata:
            with open(meta.path_to_publish, 'w') as outfile:
                outfile.write(self.json_string_overrides(meta.root_node))
                self.parent.logger.info(
                    '\n\n\n - JSON override saved at: {0}\n\n\n'.format(
                        meta.path_to_publish)
                )

            publish_name = self._publish_name(meta)

            path_to_primary_publish = \
                self._get_primary_publish_path(
                    settings=settings,
                    fields=meta.fields
                )

            result.append(
                sgtk.util.register_publish(
                    tk=self.parent.sgtk,
                    context=self.parent.context,
                    comment=item.description,
                    path=meta.path_to_publish,
                    name=publish_name,
                    version_number=meta.fields['version'],
                    thumbnail_path=item.get_thumbnail_as_path(),
                    dependency_paths=[path_to_primary_publish],
                    task=self.parent.context.task,
                    published_file_type=settings['Publish Type'].value,
                    sg_fields={
                        'sg_status_list': 'rev',
                        "task.Task.sg_status_list": "rev",
                        'tags': [
                            {
                                'id': 2288,
                                'name': 'environment-overrides',
                                'type': 'Tag'
                            },
                            {
                                'type': 'Tag',
                                'id': 4664,
                                'name': 'FixedFullOverrides'
                            },
                        ]
                    },
                    dry_run=dry_run
                )
            )

        return result

    #  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

    def _publish_name(self, meta):
        publish_name = self._regex_replace(
            regex=r'_v\d{3}',
            source_string=os.path.basename(meta.path_to_publish),
            replace_string=''
        )
        publish_name = self._regex_replace(
            regex=r'e\d{3}_\w{3}\d{3}_\d{4}_\w{3}_',
            source_string=publish_name,
            replace_string=''
        )
        return publish_name

    def _get_primary_publish_path(self, settings, fields):
        scene_pub_template_name = settings["Primary Publish Template"].value
        scene_pub_template = self.parent.engine.get_template_by_name(
            scene_pub_template_name
        )
        return scene_pub_template.apply_fields(fields)


    def _regex_replace(self, regex, source_string, replace_string):
        result = ''
        pattern = re.compile(regex)
        result = pattern.sub(repl=replace_string, string=source_string)
        return result


    def _list_of_publish_metadata(self, settings, item):
        result = []
        _map = item.properties['map_of_publishes_by_path']

        for ref in item.properties['list_of_references']:
            _node = ref.nodes()[0]
            _fields = self._reference_fields(
                settings=settings,
                reference=ref,
                map_of_publishes_by_path=_map
            )
            result.append(
                PublishMetadata(
                    fields=_fields,
                    reference=ref,
                    publish=_map[str(ref.path)],
                    path_to_publish=self._publish_path(settings, _fields),
                    root_node=_node
                )
            )
        # self.parent.logger.info("_list_of_publish_metadata:\n{}".format(pf(result)))

        return result

    def _reference_fields(self, settings, reference, map_of_publishes_by_path):
        result = self._get_file_fields(settings)
        # print("\n" + (">" * 120))
        # pp(map_of_publishes_by_path)
        # print("\n" + (">" * 120))
        # result['Asset'] = (
        #     map_of_publishes_by_path[str(reference.path)]['entity.Asset.code']
        # )
        result['Asset'] = (self.get_namespace(reference.nodes()[0]))
        return result

    #  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
    def _get_file_fields(self, settings):
        template = self.parent.engine.get_template_by_name(
            settings.get('Work Template').value
        )
        scene_path = os.path.abspath(pm.sceneName())
        return template.get_fields(scene_path)

    def _publish_path(self, settings, fields):
        template = self.parent.engine.get_template_by_name(
            settings.get("Publish Template").value
        )
        publish_path = template.apply_fields(fields)
        publish_folder = os.path.dirname(publish_path)
        self.parent.ensure_folder_exists(publish_folder)
        return publish_path

    # ---------------------------------------------------------------------------

    def json_string_overrides(self, root_node):
        result = {'overrides': []}

        def recurse(node):

            if self._is_intancer_node(node):
                instancer_override_node = self._create_instancer_override_node(node)
                if instancer_override_node:
                    result['overrides'].append(
                        instancer_override_node
                    )
            else:
                schema_node = self._create_override_node(node)
                result['overrides'].append(schema_node)

                list_of_children = node.listRelatives(type='transform')
                if list_of_children:
                    for child in list_of_children:
                        recurse(child)

        recurse(root_node)

        root_node_override = {
            'hierarchy': 'root',
            'node_name': str(root_node.stripNamespace()),
            'hidden': not root_node.visibility.get(),
            'node_type': 'group',
            'namespace': self.get_namespace(root_node),
        }
        root_node_override.update(self._transform_data(root_node))

        result.update({'asset_type': root_node.assetType.get()})
        result.update({'asset_code': root_node.assetCode.get()})
        result.update({'namespace': self.get_namespace(root_node)})
        result.update({"version": "0.1.1"})
        result['overrides'].append(root_node_override)

        # self.parent.logger.info("Unfiltered overrides for {}:\n{}".format(root_node.name(), pf(result)))

        result['overrides'] = self.filter_overrides(
            root_node, result['overrides']
        )

        result = json.dumps(result, indent=4)

        return result

    #  -   -   -   -   -   -   -   -   -   -   -   -   -   -   -   -   -

    def filter_overrides(self, root_node, list_of_overrides):
        map_of_overrides = self.map_of_node_original_overrides(root_node)

        # self.parent.logger.info("map_of_override for node {}:\n{}".format(root_node.name(), pf(map_of_overrides)))

        result = []

        for override in list_of_overrides:
            source_override = map_of_overrides[override['hierarchy']]
            if override['node_type'] == 'instances':
                result.append(override)
                continue

            filtered_override = self.remove_not_overriden(override, source_override)
            if filtered_override:
                result.append(filtered_override)

        return result

    #  -    -    -    -    -    -    -    -    -    -    -    -    -

    def remove_not_overriden(self, override, source_override):
        result = copy.deepcopy(override)
        # self.parent.logger.info("result remove_not_overriden:\n{}".format(pf(result)))

        xform_keys = copy.deepcopy(override['xform'])
        # self.parent.logger.info("xform_keys remove_not_overriden:\n{}".format(pf(xform_keys)))

        same_xform = self.xform_compare(xform_keys, source_override['xform'])
        same_vis = result['hidden'] == source_override['hidden']
        is_instancer = override['node_type'] == 'instances'
        # self.parent.logger.info("same_xform: {}".format(same_xform))
        # self.parent.logger.info("same_vis: {}".format(same_vis))
        # self.parent.logger.info("is_instancer: {}".format(is_instancer))

        if not same_vis or not same_xform or is_instancer:
            # if something chnged, return the override
            return result

        return None

    #  -    -    -    -    -    -    -    -    -    -    -    -    -

    def map_of_node_original_overrides(self, root_node):
        result = {}

        def recurse(node):
            # pp(node.sourceOverrides.get())
            result.update({
                node.hierarchy.get(): json.loads(node.sourceOverrides.get())
            })
            list_of_children = node.listRelatives(type='transform')
            if list_of_children:
                for child in list_of_children:
                    recurse(child)

        recurse(root_node)

        return result

    # ---------------------------------------------------------------------------
    def _is_intancer_node(self, node):
        return True if (node.itemType.get()).lower() == 'instances' else False

    def _create_instancer_override_node(self, node):

        list_of_children = node.listRelatives()

        # check if at least one instance has been transformed or hidden,
        # if so, then we will write the node data and all of the instances
        # data, otherwise we will skip this node and its instances

        list_of_matrices, list_of_source_matrices = self._get_matrices_lists(list_of_children)
        matrices_compare = self.compare_lists(
            list_of_matrices,
            list_of_source_matrices
        )

        list_of_visibilities, list_of_source_visibilities = self._get_visibilities_lists(list_of_children)
        visibilities_compare = self.compare_lists(
            list_of_visibilities,
            list_of_source_visibilities
        )

        # also check if the instancer group has changed, incase we need to
        # write this data but not the data for the whole set of instances

        node_matrix, node_source_matrix = self._get_matrices_lists([node])
        node_matrix_compare = self.compare_lists(
            node_matrix,
            node_source_matrix
        )

        node_visibility, node_source_visibility = self._get_visibilities_lists([node])
        node_visibility_compare = self.compare_lists(
            node_visibility,
            node_source_visibility
        )

        # if the main node trnsformations or visibility, or if any of
        # its instances' transformations or visibilities didn't change,
        # return None, otherwise, continue and create the main node
        # (instancer) data

        if not matrices_compare and not visibilities_compare \
            and not node_matrix_compare and not node_visibility_compare:
            self.parent.logger.info(
                "Nothing has been modified for instancer {} or its instances".format(
                    node.name()
                )
            )
            return None

        self.parent.logger.info(
            ("Something has been modified for instancer {} or its "
             "instances:\ninstances matrices_compare: {}\ninstances "
             "visibilities_compare: {}\ninstancer node matrix compare: {}"
             "\ninstancer node visibility compar: {}").format(
                node.name(), matrices_compare, visibilities_compare,
                node_matrix_compare, node_visibility_compare
            )
        )

        _parent_node = node.getParent()
        _parent_node_name = (
            str(_parent_node.stripNamespace())
            if node.getParent() is not None
            else 'root'
        )
        result = {
            'node_type': node.itemType.get().lower(),
            'parent': _parent_node_name,
            'node_name': str(node.stripNamespace()),
            'hidden': not node.visibility.get(),
            "namespace": self.get_namespace(node),
            'instances': []
        }
        result.update(self._hierarchy_data(node))
        result.update(self._transform_data(node))

        # Once the instancer data has been created, we need to check if the
        # instances did change, or if just the main node (instancer) changed
        # if none of the instances has been modified, return None
        if not matrices_compare and not visibilities_compare:
            return result

        for child in list_of_children:
            result['instances'].append(
                {
                    'hidden': not child.visibility.get(),
                    "namespace": self.get_namespace(child)
                }
            )
            result['instances'][-1].update(self._transform_data(child))

        return result

    def is_node_instancer_children(self, node):
        result = False
        _parent = node.getParent()
        if (_parent.itemType.get()).lower() == 'instances':
            result = True
        return result


    def _get_matrices_lists(self, list_of_nodes):
        list_of_matrices = []
        list_of_source_matrices = []

        for node in list_of_nodes:
            list_of_matrices.append(
                tuple(
                    self.round_array(
                        reduce(lambda x, y: x + y, node.matrix.get().tolist())
                    )
                )
            )
            list_of_source_matrices.append(
                tuple(
                    json.loads(node.sourceOverrides.get()).get("xform", {}).get("matrix")
                )
            )
        return list_of_matrices, list_of_source_matrices


    def _get_visibilities_lists(self, list_of_nodes):
        list_of_visibilities = []
        list_of_source_visibilities = []

        for node in list_of_nodes:
            list_of_visibilities.append(not node.visibility.get())
            list_of_source_visibilities.append(
                json.loads(node.sourceOverrides.get()).get("hidden")
            )
        return list_of_visibilities, list_of_source_visibilities


    def compare_lists(self, list_a, list_b):
        """ Compare input lists as sets to check if there's a missmatch"""

        return set(list_a) - set(list_b)

    #  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

    def _create_override_node(self, node):
        _parent_node = node.getParent()
        _parent_node_name = (
            str(_parent_node.stripNamespace())
            if node.getParent() is not None
            else 'root'
        )

        result = {
            'node_type': node.itemType.get().lower(),
            'parent': _parent_node_name,
            'node_name': str(node.stripNamespace()),
            'hidden': not node.visibility.get(),
            "namespace": self.get_namespace(node),
        }

        result.update(
            self._transform_data(node)
        )

        result.update(
            self._ids_data(
                id_client=self._get_attribute(node, 'externalID'),
                client_key='fb'
            )
        )
        result.update(
            self._hierarchy_data(node)
        )

        return result

    #  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

    def _hierarchy_data(self, node):
        result = {
            'hierarchy': 'root'
        }
        _hierarchy = self._get_attribute(node, 'hierarchy')
        if _hierarchy:
            result['hierarchy'] = _hierarchy

        return result

    #  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

    def round_array(self, array):
        return [round(x, XFORM_FLOAT_PRECISION) for x in array]

    def round_compare(self, a, b):
        PRECISION = COMPARE_FLOAT_PRECISION
        return round(a, PRECISION) == round(b, PRECISION)

    def array_compare(self, a, b):
        if len(a) != len(b):
            return False

        comparison = []
        for i in range(len(a)):
           comparison.append(self.round_compare(a[i], b[i]))

        return all(comparison)

    def xform_compare(self, a, b):
        for key in ['matrix', 'rotatePivot', 'scalePivot']:
            item_a = a[key]
            item_b = b[key]
            equal = self.array_compare(item_a, item_b)
            if not equal:
                return False
        return True


    def _transform_data(self, node):

        return {
            'xform': {
                # 'rotatePivot': round_array(node.getRotatePivot().tolist()),
                # 'scalePivot': round_array(node.getScalePivot().tolist()),
                'rotatePivot': self.round_array(
                    pm.xform(node, query=True, rotatePivot=True, objectSpace=True)
                ),
                'scalePivot': self.round_array(
                    pm.xform(node, query=True, scalePivot=True, objectSpace=True)
                ),
                'matrix': self.round_array(
                    reduce(lambda x, y: x + y, node.matrix.get().tolist())
                )
            }
        }

    #  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

    def _ids_data(
            self, id_mighty=0, id_client=0, client_key='default'
    ):
        result = {
            'ids': {
                'mighty': None,
                client_key: None
            }
        }
        if id_mighty:
            result['ids']['mighty'] = id_mighty
        if id_client:
            result['ids']['fb'] = id_client

        return result

    #  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

    def _get_attribute(self, node, attribute_name):
        result = 0
        _attr = attribute_name
        _name = node.name()
        if node.hasAttr(_attr):
            result = pm.getAttr('{0}.{1}'.format(_name, _attr))

        return result

    #  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

    def get_namespace(self, node):
        result = None
        result = str(node.namespace())
        if result.endswith(":"):
            result = result[:-1]

        return result
