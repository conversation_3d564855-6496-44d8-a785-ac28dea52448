##########################################################################################
# This file contains file patterns that should be ignored
# when folders are created on disk. Similar to a gitignore file.
#
# Sometimes, there may be files inside the configuration scaffold
# that should not be copied across. It may be because the configuration
# was created on a mac, or because the configuration is kept in source
# control. This file makes it easy for Sgt<PERSON> to ignore such files.
#

placeholder

# ignore mac files
.DS_Store

# ignore subversion folders
.svn

# ignore git
.gitignore
.git

# this is the last line of the file
##########################################################################################
