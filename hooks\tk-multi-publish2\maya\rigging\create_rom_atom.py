#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

"""
An app that syncs the frame range between a scene and a shot in Shotgun.
Plus adding and modifiying the scene Ren<PERSON> Settings

"""

import sys
import os

from tank import Hook
from tank import TankError
from tank.platform import Application
from tank.platform.qt import QtCore, QtGui
import tank

import os
import re

from maya import cmds
import maya.OpenMaya as om

class ProcessItemsHook(Hook):

    def execute(self, entity_type, entities, other_params, **kwargs):


        # SELECT CURVES
        nurbs_curves = cmds.ls(type='nurbsCurve', ni=1, o=1, r=1)
        curve_transforms = [cmds.listRelatives(i, p=1, type='transform', fullPath=True)[0] for i in nurbs_curves]
        cmds.select(curve_transforms)

        # EXPORT
        if curve_transforms:
            # Export atom file to curves
            filepath = cmds.file(q=True, sn=True)
            filepath = filepath.replace(".ma", ".atom")

            cmds.file(
                filepath,
                es=True,
                force=True,
                type="atomExport",
                options='"precision=8","statics=1","baked=1","sdk=1","constraint=1","animLayers=1","selected=selectedOnly","whichRange=1","range=1:10","hierarchy=none","controlPoints=1","useChannelBox=1","options=keys","opyKeyCmd=-animation objects -option keys -hierarchy none -controlPoints 1 "'
            )

            msg = "Atom animation file created: \n {}".format(filepath)
            cmds.confirmDialog(title="Success", message=msg,
                               button=['OK'],
                               defaultButton='OK')
        else:

            msg = "There is no animation curves in the scene."
            cmds.confirmDialog(title="Error", message=msg,
                               button=['OK'],
                               defaultButton='OK')
        return {'succes': [1], 'messages': [], 'errors': []}