# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

import os
import sys
import pprint

import sgtk


__author__ = "Diego Garcia Huerta"
__contact__ = "https://www.linkedin.com/in/diegogh/"


HookBaseClass = sgtk.get_hook_baseclass()


SESSION_PUBLISHED_TYPE = "Substance Painter Project File"


class SubstancePainterSessionCollector(HookBaseClass):
    """
    Collector that operates on the substance painter session. Should inherit
    from the basic collector hook.
    """

    @property
    def settings(self):
        """
        Dictionary defining the settings that this collector expects to receive
        through the settings parameter in the process_current_session and
        process_file methods.

        A dictionary on the following form::

            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }

        The type string should be one of the data types that toolkit accepts as
        part of its environment configuration.
        """

        # grab any base class settings
        collector_settings = (
            super(SubstancePainterSessionCollector, self).settings or {}
        )

        # settings specific to this collector
        substancepainter_session_settings = {
            "Work Template": {
                "type": "template",
                "default": None,
                "description": "Template path for artist work files. Should "
                "correspond to a template defined in "
                "templates.yml. If configured, is made available"
                "to publish plugins via the collected item's "
                "properties. ",
            },
            "Work Export Template": {
                "type": "template",
                "default": None,
                "description": "Template path for where the textures are "
                "exported. Should correspond to a template defined in "
                "templates.yml.",
            },
            "Publish Textures as Folder": {
                "type": "bool",
                "default": True,
                "description": "Publish Substance Painter textures as a folder."
                "If true (default) textures will be all exported"
                " together as a folder publish."
                "If false, each texture will be exported and"
                " published as each own version stream.",
            },
        }

        # update the base settings with these settings
        collector_settings.update(substancepainter_session_settings)

        return collector_settings

    def collect_textures(self, settings, parent_item):
        publisher = self.parent
        engine = sgtk.platform.current_engine()

        self.logger.debug("Exporting textures...")

        export_path = self.get_export_path(settings)
        if not export_path:
            export_path = engine.app.get_project_export_path()

        engine.show_busy(
            "Exporting textures",
            "Texture are being exported so they can " "be published.\n\nPlease wait...",
        )

        map_export_info = engine.app.export_document_maps(export_path)
        engine.clear_busy()

        self.logger.debug("Collecting exported textures...")

        publisher.log_debug("Raw result:\n{0}".format(pprint.pformat(map_export_info)))

        # example export info
        # {u'shader': {u'Diffuse(.$udim)': u'P:\\WorkArea\\...\\export/Diffuse.1009.png',
        #             u'Emissive(.$udim)': u'',
        #             u'Glossiness(.$udim)': u'P:\\WorkArea\\...\\export/Glossiness.1009.png',
        #             u'Height(.$udim)': u'P:\\WorkArea\\...\\export/Height.1009.png',
        #             u'Normal(.$udim)': u'P:\\WorkArea\\...\\export/Normal.1009.png',
        #             u'Specular(.$udim)': u'P:\\WorkArea\\...\\export/Specular.1009.png'}}

        icon_path = os.path.join(self.disk_location, os.pardir, "icons", "texture.png")

        # get fileseq in a partially generic way, untill
        # we have mty-framework-externalpython
        # we know this hook files lives in
        # {config}/tk-multi-publish2/substance, so we will place fileseq there
        config_path = os.path.dirname(os.path.dirname(self.disk_location))
        python_modules_path = os.path.join(config_path, "external_python_modules")
        publisher.log_debug('external_python_modules: {0}'.format(python_modules_path))
        sys.path.append(python_modules_path)

        if sys.version_info[0] < 3:
            pythonfuture = os.path.join(python_modules_path, "python-future")
            sys.path.append(pythonfuture)

        import fileseq

        for texture_set_name, texture_set in iter(map_export_info.items()):
            for texture_id, texture_file in iter(texture_set.items()):

                if not os.path.exists(texture_file):
                    continue

                if '$udim' in texture_id:
                    # get the path as a udim pattern
                    seq_path = publisher.util.get_frame_sequence_path(
                        texture_file, frame_spec='%04d'
                    )

                    publisher.log_debug("Raw File Sequence path: {0}".format(seq_path))

                    udim_texture_paths = []
                    seq = fileseq.findSequenceOnDisk(seq_path)
                    udims = {fr: seq[idx] for idx, fr in enumerate(seq.frameSet())}

                    # save the path as a generic udim token
                    texture_file = seq_path.replace('%04d', '$udim')

                    publisher.log_debug("UDIM File Sequence path: {0}".format(texture_file))


                texture_name = os.path.basename(texture_file)

                publisher.log_debug('UI Texture item name: {0}'.format(texture_name))

                self.logger.debug("texture: %s" % texture_file)
                textures_item = parent_item.create_item(
                    "substancepainter.texture", "Texture", texture_name
                )
                textures_item.set_icon_from_path(icon_path)


                if '$udim' in texture_id:
                    textures_item.properties["is_udim"] = True
                    textures_item.properties["udims"] = udims
                else:
                    textures_item.properties["is_udim"] = False
                    textures_item.properties["udims"] = None


                textures_item.properties["path"] = texture_file
                textures_item.properties["publish_type"] = "UDIM Texture"
