# -*- coding: utf-8 -*-
# Standard library:
import pprint
from pprint import pformat
# ___   ___   ___   ___   ___   ___  ___
# Third party:
import sgtk

# ___   ___   ___   ___   ___   ___  ___
# Project:
HookBaseClass = sgtk.get_hook_baseclass()
# ====================================================================
pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)


class Query(HookBaseClass):

    def __init__(self, parent):
        super(Query, self).__init__(parent)
        self._shotgun = self.parent.shotgun
        self._entity_task = self.shotgun_for_task_entity()

    # ---------------------------------------------------------------------------
    @property
    def SG(self):
        return self._shotgun

    @property
    def current_task_entity(self):
        return self._entity_task

    @property
    def current_pipeline_step_name(self):
        return self.parent.context.step['name']

    # ---------------------------------------------------------------------------

    def shotgun_for_task_entity(self):
        return self.parent.shotgun.find_one(
            entity_type='Task',
            filters=[
                ['id', 'is', self.parent.context.task['id']],
            ],
            fields=['code', 'step', 'content']
        )

    # ---------------------------------------------------------------------------
    def shotgun_for_shot_published_types(self, shot_entity=None, **kwargs):
        if not shot_entity:
            raise ValueError(
                '<shot_entity> parameter should be a valid entity.'
            )

        list_of_filters = [
            ['entity', 'is', shot_entity]
        ]
        if kwargs.get('filters'):
            list_of_filters.extend(kwargs['filters'])

        list_of_fields = [
            'path',
            'entity',
            'name',
            'version_number',
            'created_at'
        ]

        if kwargs.get('fields'):
            list_of_filters.extend(kwargs['filters'])

        return self.SG.find(
            entity_type='PublishedFile',
            filters=list_of_filters,
            fields=list_of_fields
        )

    # ---------------------------------------------------------------------------

    def shotgun_for_shot_verbose_data(self, entity_shot):
        # filters = [['id', 'is', self.parent.context.entity['id']]]
        filters = [['id', 'is', entity_shot['id']]]
        fields = [
            'assets',
            'sg_breakdowns',
            'sg_sequence',
            'code',
            'sg_status_list',
            'tast_template',
            'sg_shot_type',
            'id',
            'sg_animation_exposition',
            'description',
            'sg_sequence.Sequence.episode',
            'sg_asset.Asset.task'

        ]
        entity = self.parent.shotgun.find_one("Shot", filters, fields)

        return entity

    # ---------------------------------------------------------------------------

    def shotgun_for_shot_breakdown_list(self, entity_shot):
        result = []

        if not entity_shot.get('sg_breakdowns'):
            raise ValueError(
                '<entity_shot> parameter must have data inside '
                'the \"sg_breakdown\" key.'
            )

        for breakdown in entity_shot['sg_breakdowns']:
            result.append(
                self.parent.engine.shotgun.find_one(
                    "CustomEntity30",
                    [
                        ['id', 'is', breakdown['id']]],
                    [
                        'sg_locked_version.PublishedFile.id',
                        'sg_asset'
                    ]
                )
            )

        return result

    # ---------------------------------------------------------------------------

    def shotgun_published_files_from_breakdowns(self, list_of_breakdowns):
        _lock_field = 'sg_locked_version.PublishedFile.id'

        print("\n" + (">" * 120))
        print('LIST OF BREAKDOWNS')
        pp(list_of_breakdowns)

        list_of_locked_breakdowns = filter(
            lambda _b: _b[_lock_field],
            list_of_breakdowns
        )

        _fields = [
            'id',
            'code',
            'path_cache',
            'path',
            'entity',
            'entity.Asset.sg_sub_type',
            'entity.Asset.sg_asset_type',
            'entity.Asset.code',
            'version_number'
        ]

        order = [{'field_name': 'id', 'direction': 'desc'}]

        locked_publishes = []

        list_of_locked_breakdown_ids = (
            [x['id'] for x in list_of_locked_breakdowns]
        )

        list_of_verbosed_locked_publishes = []
        if list_of_locked_breakdowns:
            list_of_verbosed_locked_publishes = (
                self.SG.find(
                    entity_type='PublishedFile',
                    filters=[['id', 'in', list_of_locked_breakdown_ids]],
                    fields=_fields,
                    order=[{'field_name': 'id', 'direction': 'desc'}]
                )
            )

        list_of_unlocked_publishes = \
            self._get_latest_publishes_from_breakdowns(
                list_of_breakdowns=list_of_breakdowns
            )

        print('LIST OF UNLOCKED PUBLISHES:')
        pp(list_of_unlocked_publishes)

        print("\n" + (">" * 120))

        return (list_of_verbosed_locked_publishes + list_of_unlocked_publishes)

    def shotgun_for_latest_version_by_type(self, publish_type):
        result = self.SG.summarize(
            entity_type='PublishedFile',
            filters=[
                ['entity', 'is', self.parent.context.entity],
                ['published_file_type.PublishedFileType.code', 'is',
                 publish_type]
            ],
            summary_fields=[
                {'field': 'version_number', 'type': 'maximum'}
            ]
        )
        return result['summaries']['version_number']

    def _get_latest_publishes_from_breakdowns(self, list_of_breakdowns):
        _lock_field = 'sg_locked_version.PublishedFile.id'
        result = []
        list_of_unlocked_breakdowns = filter(
            lambda _b: not _b[_lock_field],
            list_of_breakdowns
        )

        list_of_asset_names = [
            b['sg_asset']['name'] for b in list_of_unlocked_breakdowns
        ]
        list_of_asset_names = list(set(list_of_asset_names))




        list_of_processed_asset_ids = []
        for breakdown in list_of_unlocked_breakdowns:
            if breakdown['sg_asset']['id'] in list_of_processed_asset_ids:
                continue

            result.append(
                self.SG.find_one(
                    entity_type='PublishedFile',
                    filters=[
                        [
                            'entity.Asset.sg_breakdowns', 'is', breakdown
                        ],
                        [
                            'published_file_type.PublishedFileType.code',
                            'in',
                            ['GPU Cache', 'Maya Scene']
                        ],
                        ['sg_status_list', 'is', 'apr']
                    ],
                    fields=_fields,
                    order=[
                        {'field_name': 'version_number', 'direction': 'desc'}
                    ]
                )
            )
            list_of_processed_asset_ids['sg_asset']['id']

        return result
