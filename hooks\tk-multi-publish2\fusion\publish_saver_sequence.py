# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

import os
import sgtk
import re
import traceback
import shutil
from sgtk.platform.qt import QtGui, QtCore
import BlackmagicFusion as bmd
from os import listdir
from os.path import isfile, join

HookBaseClass = sgtk.get_hook_baseclass()

fusion = bmd.scriptapp("Fusion")

class FusionPublishSaverSeqPlugin(HookBaseClass):
    """
    Plugin for publish rendered sequence

    """

    @property
    def name(self):
        """
        One line display name describing the plugin
        """
        return "Publish sequence files"

    @property
    def description(self):
        """
        Copy/upload the render sequence to publish area
        """

        loader_url = "https://support.shotgunsoftware.com/hc/en-us/articles/219033078"

        engine        = self.parent.engine
        comp          = fusion.GetCurrentComp()
        path          = comp.GetAttrs()['COMPS_FileName']
        work_template = engine.sgtk.template_from_path(path)
        work_version  = work_template.get_fields(path).get('version')

        aovs   = []
        savers = comp.GetToolList(False, "Saver").values()
        for saver in savers:
            path     = saver.GetAttrs()['TOOLST_Clip_Name'].values()[0]
            template = engine.sgtk.template_from_path(path)
            if template is None:
                continue
            if template.name != "fusion_shot_render_aov":
                continue
            fields           = template.get_fields(path)
            template_version = fields.get('version')
            if template_version != work_version:
                continue
            if fields['aov_name'] != 'default':
                aovs.append(" - {}".format(fields['aov_name']))
            
        description = """
            This plugin publish copy render files to publish area and 
            register in shotgun.
            """
        if len(aovs):
            aovs_str    = "<br />".join(aovs)
            description = """{}
            <br />Multi EXR. AOVs:
            <br /> - default
            <br />{}""".format(description, aovs_str)
        
        return "<p>{}</p>".format(description)

    @property
    def settings(self):
        """
        Dictionary defining the settings that this plugin expects to recieve
        through the settings parameter in the accept, validate, publish and
        finalize methods.

        A dictionary on the following form::

            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }

        The type string should be one of the data types that toolkit accepts
        as part of its environment configuration.
        """

        # settings specific to this class
        fusion_publish_settings = {
            "Work render template": {
                "type": "template",
                "default": "shot_flat_render_work_exr",
                "description": "Original path from render"},
            "Publish render template":{
                "type": "template",
                "default": "shot_flat_render_publish_exr",
                "description": "Template to publish and copy sequence files"}
        }

        # inherit the settings from the base publish plugin
        plugin_settings = super(FusionPublishSaverSeqPlugin, self).settings or {}

        # update the base settings
        plugin_settings.update(fusion_publish_settings)
        return plugin_settings

    @property
    def item_filters(self):
        """
        List of item types that this plugin is interested in.

        Only items matching entries in this list will be presented to the
        accept() method. Strings can contain glob patters such as *, for example
        ["fusion.*", "file.fusion"]
        """
        return ["fusion.session.savers"]

    def accept(self, settings, item):
        """
        Method called by the publisher to determine if an item is of any
        interest to this plugin. Only items matching the filters defined via the
        item_filters property will be presented to this method.

        A submit for review task will be generated for each item accepted here. Returns a
        dictionary with the following booleans:

            - accepted: Indicates if the plugin is interested in this value at
                all. Required.
            - enabled: If True, the plugin will be enabled in the UI, otherwise
                it will be disabled. Optional, True by default.
            - visible: If True, the plugin will be visible in the UI, otherwise
                it will be hidden. Optional, True by default.
            - checked: If True, the plugin will be checked in the UI, otherwise
                it will be unchecked. Optional, True by default.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process

        :returns: dictionary with boolean keys accepted, required and enabled.
            This plugin makes use of the tk-multi-reviewsubmission app; if this
            app is not available then the item will not be accepted, this method
            will return a dictionary with the accepted field that has a value of
            False.

            Several properties on the item must also be present for it to be
            accepted. The properties are 'path', 'publish_name', 'color_space',
            'first_frame' and 'last_frame'
        """
        
        context        = self.parent.engine.context
        saver_node     = item.properties['saver_node']
        saver_path     = saver_node.GetAttrs()['TOOLST_Clip_Name'].values()[0]
        template_saver = self.parent.tank.templates_from_path(saver_path)

        # If the saver node is not used any template to render
        if not len(template_saver):
            return {'accepted': False}
        else:
            template_saver = template_saver[0]

        # Verify entity type SHOT
        entity_type = str(context).split(' ')[1]
        if entity_type != "Shot":
            return {'accepted': False}
        
        # If the saver node is using a different template than
        #    shot_flat_render_work_exr
        #if item.properties['work_template'] != template_saver:
        if template_saver.name != 'fusion_shot_render_aov':
            return {'accepted': False}

        return {"accepted": True,
                "checked": True}
        
    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish. Returns a
        boolean to indicate validity.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        :returns: True if item is valid and not in proxy mode, False otherwise.
        """

        # Environment info
        engine        = self.parent.engine
        comp          = fusion.GetCurrentComp()
        _path         = comp.GetAttrs()['COMPS_FileName']
        work_template = engine.sgtk.template_from_path(_path)
        work_fields   = work_template.get_fields(_path)
        work_version  = work_fields.get('version')
        sg_shot       = self.get_sg_shot_info(['sg_cut_in', 'sg_cut_out'])

        # Saver info
        saver_node      = item.properties['saver_node']
        saver_path     = saver_node.GetAttrs()['TOOLST_Clip_Name'].values()[0]
        path_container = os.path.dirname(saver_path)
        file_base      = os.path.basename(saver_path)
        path_parts     = file_base.split('.')
        padding        = len(path_parts[1])

        error_frames = []
        report_error = False
        for i in xrange(sg_shot['sg_cut_in'], sg_shot['sg_cut_out']+1):
            frame_str = str(i).zfill(padding)
            validating_file = '.'.join([path_parts[0], frame_str , path_parts[-1]])
            cur_file = join(path_container, validating_file)
            if not os.path.exists(cur_file):
                error_frames.append(frame_str)
                if not report_error:
                    self.logger.error('Missing frames sample: {}'.format(cur_file))
                    report_error = True

        aovs_info = self.get_valid_aovs(item)
        aov_errors = []
        for aov_data in aovs_info.keys():
            #"MissingFrames":[]
            if not aovs_info[aov_data]['Valid']:
                str_error = "AOV {}, missing frames: {}".format(
                    aov_data, len(aovs_info[aov_data]['MissingFrames']))
                aov_errors.append(str_error)

        if len(error_frames):
            error_msg = "Missing frames: {}".format(error_frames)
            if len(aov_errors):
                error_msg = '\n'.join([error_msg]+aov_errors)
            raise Exception(error_msg)
        elif len(aov_errors):
            error_msg = '\n'.join(aov_errors)
            raise Exception(error_msg)

        return True

    def publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        self.logger.info('Publish saver sequence process')
        work_path_t       = item.properties['work_area_template']
        publish_path_t    = item.properties['publish_render_template']
        primary_publish_t = item.properties['primary_publish_template']
        saver_path        = item.properties['saver_node'].GetAttrs()['TOOLST_Clip_Name'].values()[0]

        comp            = fusion.GetCurrentComp()
        work_path       = comp.GetAttrs()['COMPS_FileName']
        fields          = work_path_t.get_fields(work_path)
        path_container  = os.path.dirname(saver_path)
        publish_path    = publish_path_t.apply_fields(fields) 
        primary_publish = primary_publish_t.apply_fields(fields)
        publish_version = fields["version"]

        # determine the publish name:
        publish_name = fields["name"]

        self.parent.engine.ensure_folder_exists(os.path.dirname(publish_path))

        sg_shot = self.get_sg_shot_info(['sg_cut_in', 'sg_cut_out'])

        path_parts = saver_path.split('.')
        publish_parts = publish_path.split('.')

        padding = len(path_parts[1])

        aovs_info = self.get_valid_aovs(item)
        valid_aovs = {}
        for aov_name, aov_data in aovs_info.items():
            if aov_data['Valid']:
                valid_aovs[aov_name] = aov_data
        
        if len(valid_aovs.keys()) == 0:
            "Avoid multiprocess creation"
            self.logger.info('Copy to publish area, no layers detected')
            for i in xrange(sg_shot['sg_cut_in'], sg_shot['sg_cut_out']+1):
                frame_str = str(i).zfill(padding)
                source_file = '.'.join([path_parts[0], frame_str , path_parts[-1]])
                destination_file = '.'.join([publish_parts[0], frame_str , publish_parts[-1]])
                shutil.copyfile(source_file, destination_file)
        else:
            "Multi process creation"
            self.logger.info('Creating multi exr')
            format_str  = '{}:{}::{}'
            openExr_framework = self.load_framework("mty-framework-openexr")
            openExr = openExr_framework.exrCoreTools
            openExr.set_binary("multipart")

            for i in xrange(sg_shot['sg_cut_in'], sg_shot['sg_cut_out']+1):
                cmd  = ['-combine', '-i']
                frame_str = str(i).zfill(padding)
                beauty_file = '.'.join([path_parts[0], frame_str , path_parts[-1]])
                beauty_path = join(path_container, beauty_file)

                cmd.append(format_str.format(beauty_path, 0, 'default'))

                for aov_name, aov_data in valid_aovs.items():
                    aov_parts = aov_data['path_parts']
                    aov_file = '.'.join([aov_parts[0], frame_str , aov_parts[-1]])
                    aov_path = join(aov_data['Folder'], aov_file)
                    cmd.append(format_str.format(aov_path, 0, aov_name))

                cmd.append('-o')
                destination_file = '.'.join([publish_parts[0], frame_str , publish_parts[-1]])
                cmd.append(destination_file)
                cmd.append('-override')

                # Create folder if does not exists
                container_folder = os.path.dirname(destination_file)
                if not os.path.exists(container_folder):
                    os.mkdir(container_folder)

                _err, _info = openExr.execute_command_list(cmd)
                if _err:
                    message = "Failed to create video: %s\ncommand: %s"
                    raise Exception(message % (_info, cmd))

        self.logger.info('Register sequence in shotgun')

        loaders_paths     = item.properties['loader_dependencies']
        aovs_upstreams    = self.get_aovs_savers_upstreams(item)
        dependency_paths  = aovs_upstreams + [primary_publish]

        args = {
            "tk":                  self.parent.engine.sgtk,
            "context":             item.context,
            "comment":             item.description,
            "path":                publish_path,
            "name":                publish_name,
            "version_number":      publish_version,
            "thumbnail_path":      item.get_thumbnail_as_path(),
            "task":                self.parent.engine.context.task,
            "dependency_paths":    dependency_paths,
            "sg_status_list":      "rev",
            "published_file_type": item.properties['publish_type']
        }

        sg_publishes = sgtk.util.register_publish(**args)

        root_item = self.get_root_item(item)
        root_item.properties.setdefault('sg_publish_extra_data', [])
        publish_extra_data = root_item.properties['sg_publish_extra_data']
        publish_extra_data.append(sg_publishes)

        item.properties.sg_publish_data = sg_publishes
        item.properties.sg_publish_path = sg_publishes["path"]["local_path"]
        self.parent.tank.shotgun.update("PublishedFile", sg_publishes["id"], {"sg_status_list": 'rev'})

        self.parent.log_debug(
            "Adding existing publish " +
            "(id:{}) ".format(sg_publishes['id']) +
            "as extra data for item: {}".format(item)
        )
        return True

    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        pass

    def get_sg_shot_info(self, shot_fields):
        engine  = self.parent.engine
        sg      = engine.shotgun
        sg_proj = engine.context.project

        context_tokens = str(engine.context).split(' ')
        entity_name = context_tokens[2]
        shot_filter = [['project', 'is', sg_proj],
                       ['code', 'is', entity_name]]
        # shot_fields = ['sg_cut_in', 'sg_cut_out']
        sg_shot = sg.find_one('Shot', shot_filter, shot_fields)
        return sg_shot

    def get_root_item(self, item):

        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root

    def get_valid_aovs(self, item):

        sg_shot    = self.get_sg_shot_info(['sg_cut_in', 'sg_cut_out'])
        saver_path = item.properties['saver_node'].GetAttrs()['TOOLST_Clip_Name'].values()[0]
       
        path_container = os.path.dirname(saver_path)
        file_base      = os.path.basename(saver_path)

        path_parts = file_base.split('.')
        padding    = len(path_parts[1])
        engine     = self.parent.engine

        comp       = fusion.GetCurrentComp()
        savers     = comp.GetToolList(False, "Saver").values()

        _path         = comp.GetAttrs()['COMPS_FileName']
        work_template = engine.sgtk.template_from_path(_path)
        work_version  = work_template.get_fields(_path).get('version')

        aov_info = {}
        for saver in savers:
            path     = saver.GetAttrs()['TOOLST_Clip_Name'].values()[0]
            template = engine.sgtk.template_from_path(path)
            if template is None:
                continue
            if template.name != "fusion_shot_render_aov":
                continue
            fields           = template.get_fields(path)
            template_version = fields.get('version')
            if template_version != work_version:
                continue
            
            aov_name = fields['aov_name']
            if aov_name != 'default':
                aov_info[aov_name] = {"path_parts": path.split('.'),
                                      "Valid":  True,
                                      "Folder": path,
                                      "MissingFrames":[]}

        error_frames = []
        for i in xrange(sg_shot['sg_cut_in'], sg_shot['sg_cut_out']+1):
            frame_str = str(i).zfill(padding)
            validating_file = '.'.join([path_parts[0], frame_str , path_parts[-1]])
            if not os.path.exists(join(path_container, validating_file)):
                error_frames.append(frame_str)

            for aov, aov_data in aov_info.items():
                aov_file = '.'.join([aov_data["path_parts"][0],
                                        frame_str ,
                                        aov_data["path_parts"][-1]])
                if not os.path.exists(join(path_container, aov_file)):
                    aov_data['Valid'] = False
                    aov_data['MissingFrames'].append(i)

        return aov_info

    def get_aovs_savers_upstreams(self, item):
        work_path_template = item.properties['work_area_template']
        comp               = fusion.GetCurrentComp()
        work_path          = comp.GetAttrs()['COMPS_FileName']
        work_area_fields   = work_path_template.get_fields(work_path)

        all_savers  = comp.GetToolList(False, "Saver").values()
        upstream_dependencies_paths = []

        for saver in all_savers:
            # Validation AOV saver
            saver_path    = saver.GetAttrs()['TOOLST_Clip_Name'].values()[0]
            template_path = self.parent.engine.sgtk.template_from_path(saver_path)
            if template_path is None:
                continue

            # Validation current setup
            invalid_fields_bool = False
            saver_fields = template_path.get_fields(saver_path)
            for work_field, work_value in work_area_fields.items():
                if work_field in saver_fields.keys():
                    if work_value != saver_fields[work_field]:
                        invalid_fields_bool = True
                        break
            if invalid_fields_bool:
                continue

            # Validating upstream nodes
            saver_upstream = self.get_all_upstream(saver)
            for up_node in saver_upstream:
                if 'TOOLST_Clip_Name' in up_node.GetAttrs():
                    up_saver_path = up_node.GetAttrs()['TOOLST_Clip_Name'].values()[0]

                    if up_saver_path not in upstream_dependencies_paths:
                        upstream_dependencies_paths.append(up_saver_path)

        return upstream_dependencies_paths

    def get_all_upstream(self, node):
        if node is None:
            return []

        main_input = node.FindMainInput(1)
        if main_input is None:
            return []
        input = main_input.GetConnectedOutput()
        if input is None:
            return []
        else:
            tool_node = input.GetTool()
            return [tool_node] + self.get_all_upstream(tool_node)

class UploaderThread(QtCore.QThread):
    """
    Simple worker thread that encapsulates uploading to shotgun.
    Broken out of the main loop so that the UI can remain responsive
    even though an upload is happening
    """
    def __init__(self, app, version, path_to_movie, thumbnail_path, upload_to_shotgun):
        QtCore.QThread.__init__(self)
        self._app = app
        self._version = version
        self._path_to_movie = path_to_movie
        self._thumbnail_path = thumbnail_path
        self._upload_to_shotgun = upload_to_shotgun
        self._errors = []

    def get_errors(self):
        """
        can be called after execution to retrieve a list of errors
        """
        return self._errors

    def run(self):
        """
        Thread loop
        """
        upload_error = False

        if self._upload_to_shotgun:
            try:
                self._app.tank.shotgun.upload("Version", self._version["id"], self._path_to_movie, "sg_uploaded_movie")
            except Exception as e:
                self._errors.append("Movie upload to Shotgun failed: %s" % e)
                upload_error = True

        if not self._upload_to_shotgun or upload_error:
            try:
                self._app.tank.shotgun.upload_thumbnail("Version", self._version["id"], self._thumbnail_path)
            except Exception as e:
                self._errors.append("Thumbnail upload to Shotgun failed: %s" % e)
