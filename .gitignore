# ---- ignore special files written during config setup

core/app_store.yml
core/install_location.yml
core/interpreter_*.cfg
core/pipeline_configuration.yml
core/shotgun.yml
tk-metadata
Makefile
.idea/

# compiled python files
*.py[co]
*.py [co]

# Packages
*.egg
*.egg-info
dist
build
eggs
parts
bin
var
sdist
develop-eggs
.installed.cfg

# Installer logs
pip-log.txt

# Unit test / coverage reports
.coverage
.tox

#Translations
*.mo

#Mr Developer
.mr.developer.cfg

output.html
*.swp
Thumbs.db
*-small.*
hooks/tk-harmony/templates/HPL.json
