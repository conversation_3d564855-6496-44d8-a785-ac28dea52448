import os
import fileseq

import sgtk

HookBaseClass = sgtk.get_hook_baseclass()

class MultiTransferActions(HookBaseClass):


    def ensure_file_is_local(self, path, publish):

        if not hasattr(self, 'metasync'):
            self.metasync = self.parent.engine.custom_frameworks.get(
                "mty-framework-metasync"
            )
            if not self.metasync:
                self.metasync = self.load_framework("mty-framework-metasync")

        transfersManager = self.metasync.transfersManager
        if "%" in path:
            dirname = os.path.dirname(path)
            if os.path.exists(dirname) and self._collect_sequenced_files(path):
                transfersManager.ensure_local_dependencies(publish)
                return path
        else:
            if os.path.exists(path):
                transfersManager.ensure_local_dependencies(publish)
                return path

        transfersManager.ensure_file_is_local(path, publish)
        transfersManager.ensure_local_dependencies(publish)

        return path

    def _collect_sequenced_files(self, sequence_path):
        """Returns a list containing all of the sequence files"""
        # folder_path = os.path.dirname(sequence_path)
        # folder_files = os.listdir(folder_path)

        # name, ext = os.path.splitext(sequence_path)
        # sequence_files = []

        # if len(folder_files) > 0:
        #     for file in sorted(folder_files):
        #         if file.endswith(ext):
        #             path = os.path.join(folder_path, file)
        #             sequence_files.append(path)

        fileseq_obj = fileseq.findSequenceOnDisk(sequence_path)
        if fileseq_obj:
            self.parent.log_info("Found sequence on disk: {}".format(fileseq_obj))
            sequence_files = list(fileseq_obj)
        else:
            self.parent.log_warning(
                "Could not find sequence on disk: {}".format(sequence_path)
            )
            sequence_files = []

        return sequence_files, fileseq_obj
