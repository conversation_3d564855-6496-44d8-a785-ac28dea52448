################################################################################
#
# Copyright (c) 2021 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
################################################################################

import sgtk

from pprint import pformat

HookBaseClass = sgtk.get_hook_baseclass()


class FFmpegBackend(HookBaseClass):

    def collect(self):
        """
        A hook to define the required FFmpeg dependency and communciation
        It must know how to locate FFmpeg and execute commands for it

        The logic can be defined directly in the hook or can also be loaded 
        from other frameworks, which can works as follows:

        sample = self.load_framework("mty-framework-ffmpeg")

        backend = sample.sampleCore

        In this case, the sample.sampleCore is a class instance that have
        the following methods:

        - execute_command(command)
            a method to receive and cache the passed host to connect to
        """

        # custom hook loads studio ffmpeg framework
        ffmpeg = self.load_framework("mty-framework-ffmpeg")
        backend = ffmpeg.ffmpegCore
        
        return backend
