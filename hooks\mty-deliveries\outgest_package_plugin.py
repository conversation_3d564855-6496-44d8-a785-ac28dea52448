################################################################################
#
# Copyright (c) 2024 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
################################################################################

import re
import os
import shutil

import sgtk

from pprint import pformat

HookBaseClass = sgtk.get_hook_baseclass()


class OutgestPlugin(HookBaseClass):
    """
    Base class plugin for outgesting a file an its dependencies to Shotgun.
    This plugin is typically configured to act upon published files on studio
    Shotgun and those need to be packed, uploaded and registered with Client
    It needs to be extended for each particualr case but it contains standard
    operations for validating and registering versions with Shotgun.
    """

    ############################################################################
    # requiered custpm ingest plugin properties

    @property
    def settings_schema(self):
        """
        Dictionary defining the settings that this plugin expects to recieve
        through the settings parameter in the accept, validate, publish and
        finalize methods.
        A dictionary on the following form:
            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }
        The type string should be one of the data types that toolkit accepts
        as part of its environment configuration.
        """
        settings = {
            "supported_entity_identifiers": {
                "type": "list",
                "default": [],
                "description": "random",
            },
            'supported_entity_types': {
                'type': 'dict',
                'default': {},
                'description': '',
            },
            'supported_task_names': {
                'type': 'dict',
                'default': {},
                'description': '',
            },
        }

        return settings

    ############################################################################
    # requiered custom ingest plugin methods

    def outgest(self, location, entity, files):
        """ This is a plugin that expects a single remote file

            Args:
                location (dict): The entity representing the remote location
                entity (dict): PublishedFile entity
                files (dict): A map of local files to already uploaded remote files
        """

        direct_submissions = []

        # Not used code, kept just for safety
        # query_entities_on_client_site = self.query_entities_on_client_site()
        # self.logger.info(
        #     "query entities on client site: {}".format(
        #         query_entities_on_client_site
        #     )
        # )

        # if query_entities_on_client_site:
        #     # plugin can use settings from the mappings hoook
        #     ingest_mappings = self.parent.mappings['ingest']

        #     # Ensure entity and task exists
        #     client_entity = self.resolve_client_entity(entity, location)
        #     # client_task = self.resolve_client_task(entity, client_entity)

        transfer_backend = self.get_transfer_backend(location)

        remote_root = os.path.dirname(
            self.resolve_remote_entity_path(
                transfer_backend, entity
            )
        )

        dependencies = self.resolve_publish_dependencies(entity)
        if dependencies:
            self.logger.debug("file_dependencies:\n{0}".format(pformat(dependencies)))
            remote_root = os.path.join(remote_root, "dependencies")
            if not os.path.exists(remote_root):
                os.makedirs(remote_root, exist_ok=True)

        for dependency in dependencies:

            # update remote_root to be per dependency parent entity
            entity_name = dependency.get("entity", {}).get("name", None)
            if entity_name:
                # differentiate if the file is a sequence, in which case, we add the
                # basename as container folder for the sequence
                path_cache = dependency.get("path_cache", None)
                if path_cache:
                    if "%" in path_cache:
                        sequence_dirname = os.path.dirname(path_cache).split("/")[-1]
                        dependency_remote_root = os.path.join(
                            remote_root, entity_name, sequence_dirname
                        )
                        os.makedirs(dependency_remote_root, exist_ok=True)
                    else:
                        dependency_remote_root = os.path.join(remote_root, entity_name)
                        os.makedirs(dependency_remote_root, exist_ok=True)
                else:
                    dependency_remote_root = os.path.join(remote_root, entity_name)
                    os.makedirs(dependency_remote_root, exist_ok=True)
            else:
                self.logger.warning(f"Couldn't get entity name:\n{pformat(dependency)}")
                dependency_remote_root = remote_root

            files.update(
                self.upload_local_file(
                    location, dependency, force_folder=dependency_remote_root
                )
            )

        # return the standarzed list of versions from this ingest
        return direct_submissions
