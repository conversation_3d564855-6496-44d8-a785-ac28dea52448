# -*- coding: utf-8 -*-
# Standard library:
import pprint
import os
import re
import shutil
import traceback
# -- -- -- -- -- -- -- -- -- -- -- -- -- --
# Third party:
import sgtk
import maya.cmds as cmds
import maya.mel as mel
import pymel.core as pm
from sgtk.platform.qt import QtGui, QtCore

# -- -- -- -- -- -- -- -- -- -- -- -- -- --
# project:

HookBaseClass = sgtk.get_hook_baseclass()
# =============================================================================
pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)
pf = pprint.pformat


class MayaMediaReviewPlugin(HookBaseClass):

    def __init__(self, parent):
        super(MayaMediaReviewPlugin, self).__init__(parent)
        self.camera_setup()

        self.engine = self.parent.engine

    @property
    def icon(self):
        return os.path.join(
            self.disk_location,
            os.pardir,
            os.pardir,
            "icons",
            "review.png"
        )

    @property
    def name(self):
        return "MayaMediaReviewPlugin"

    @property
    def description(self):
        return """
        <p>This plugin publish a playblast from the shot found in the scene.</p>

        """

    @property
    def item_filters(self):
        return ["maya.session.sceneplayblast"]

    @property
    def settings(self):
        plugin_settings = super(MayaMediaReviewPlugin, self).settings or {}

        maya_publish_settings = {
            "work_template": {
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "primary_publish_template": {
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },

            "media_review_publish_template": {
                "type": "template",
                "default": None,
                "description": "Template path for published Scene playblast. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "publish_type": {
                "type": "string",
                "default": "Maya Layout Playblast",
                "description": "The published file type to register.",
            },
            "shot_prefix": {
                "type": "string",
                "default": "Shot_",
                "description": "The string or dynamic prefix to use to validate shot naming conventions."
                               "It can be a plain string or a tokenized one, which use a system of keys,"
                               "Those keys match items from the context, supported tokens are as follow:"
                               "{context.project} which matchs: context.project['name']"
                               "{context.entity} which matchs: context.entity['name']"
                               "{context.step} which matchs: context.step['name']"
                               "{context.task} which matchs: context.task['name']",
            },
            "shot_digits": {
                "type": "int",
                "default": 3,
                "description": "The valid amount of digits in the shot name after the shot prefix.",
            },
            'framerate': {
                'type': 'int',
                'default': 24.0,
                'description': 'Framerate to based operations on'
            },
            'sequence_start_frame': {
                'type': 'int',
                'default': 1001,
                'description': (
                    'Sequences often start at frame 1000 or 1001 in order to '
                    'have room previosuly for simulation or just to keep the '
                    'numbering with the same digit count and not have to pad '
                    'with 0s at the beginning'
                )
            },
            'clip_publish_template': {
                'type': 'template',
                'default': 'maya_sequence_clip_publish',
                'description': (
                    'Template for saving individual shot clips at '
                    'sequence level'
                )
            },
            'plugin_hooks': {
                'type': 'dict',
                'default': None,
                'description': 'Utility hooks for extra work'
            }

        }

        plugin_settings.update(maya_publish_settings)

        return plugin_settings

    def plugin_hook(self, settings, string_key):
        _h = settings.get("plugin_hooks")
        return self.parent.create_hook_instance(_h.value.get(string_key))

    # --------------------------------------------------------------------------

    def accept(self, settings, item):
        print("\n" + (">" * 120))
        pp('{0}.accept'.format(self.name))


        sceneShots = self.__get_sequencer_shots(settings)
        if sceneShots['valid_shots'] or sceneShots['invalid_shots']:
            item.properties["scene_shots"] = sceneShots
            return {
                "accepted": True,
                "checked": True
            }
        else:
            return {"accepted": False}

    # --------------------------------------------------------------------------

    def fix_path(self, path):
        path = path.replace('\\', '/')
        path = path.replace('\\\\', '/')

        return path

    def get_project_fps_from_sg(self):

        engine = self.parent.engine
        data = engine.shotgun.find_one(
            entity_type="Project",
            filters=[["id", "is", engine.context.project.get("id")]],
            fields=["sg_fps"]
        )
        project_fps = data.get("sg_fps")

        return project_fps

    def get_project_resolution_from_sg(self):
        hook_expression = "{config}/get_entity_resolution.py"
        project_resolution = self.parent.engine.execute_hook_expression(
            hook_expression,
            "get_resolution",
            engine=self.parent.engine,
        )
        if project_resolution:
            resolution_width = int(project_resolution.split("x")[0])
            resolution_height = int(project_resolution.split("x")[1])
        else:
            # Fallback to HD resolution
            resolution_width = 1920
            resolution_height = 1080

        return resolution_width, resolution_height

    # --------------------------------------------------------------------------

    def __shot_code_from_shot(self, shot_name, shot_prefix, shot_digits):
        shot_name_attr = cmds.shot(shot_name, q=True, shotName=True)

        self.parent.logger.debug(
            'Shot Name Attribute: {0}'.format(shot_name_attr)
        )

        regex = r'%s(?P<shot_code>[0-9]{%s})' % (shot_prefix, shot_digits)

        self.parent.logger.debug('regex: {0}'.format(regex))

        shot_code = re.match(regex, str(shot_name_attr))

        if shot_code:
            shot_code = int(shot_code.group('shot_code').lstrip("0"))
            self.parent.logger.debug('Valid shot name: {0}'.format(shot_code))
            return shot_code
        else:
            return False

    def __resolve_shot_prefix(self, shot_prefix):
        context = self.parent.context
        known_values = {}
        context_dict = context.to_dict()

        self.parent.logger.debug('Context as dict: {0}'.format(context_dict))

        for item, value in context_dict.items():

            if item in ["additional_entities", "source_entity"]:
                continue

            if item and value:
                known_values[item] = value.get('name', 'unknown')
            else:
                known_values[item] = 'unknown'

        known_tokens = {
            '{context.project}': known_values['project'],
            '{context.entity}': known_values['entity'],
            '{context.step}': known_values['step'],
            '{context.task}': known_values['task']
        }

        if '{' in shot_prefix:
            for token in known_tokens:
                value = known_tokens[token]
                shot_prefix = shot_prefix.replace(token, value)

        return shot_prefix

    def __get_sequencer_shots(self, settings):
        shots = {'valid_shots': [], 'invalid_shots': []}
        # iteerate over the list of shots in the scene

        shot_prefix = self.__resolve_shot_prefix(settings['shot_prefix'].value)
        shot_digits = settings['shot_digits'].value

        for shot in cmds.ls(type='shot'):
            # Validate naming convention
            shot_code = self.__shot_code_from_shot(
                shot, shot_prefix, shot_digits)
            if shot_code == False:
                shots['invalid_shots'].append(shot)
            else:
                shots['valid_shots'].append(shot)

        return shots

    def get_sequence_shots(self, list_of_shot_names):
        """
        Queries shotgun for all shots in the current sequence

        Returns
        -------
        list of dicts
            Each dict contains the id, sg_cut_in, sg_cut_out, and code fields
            of a shot in the current sequence
        """
        sg_proj = self.engine.context.project
        shotgun = self.engine.shotgun
        sequence_entity = self.engine.context.entity

        # get sequence shots
        filters = [
            ["project", "is", sg_proj],
            ["sg_sequence", "is", sequence_entity],
            ["code", "in", list_of_shot_names],
            # ["sg_status_list", "not_in", ["hld", "omt"]],
        ]

        fields = ["id", "sg_cut_in", "sg_cut_out", "code", "sg_cut_order"]

        seq_shots = shotgun.find("Shot", filters, fields)

        return seq_shots

    def get_sequence_shots_editorial_audios(self, list_of_shot_names, seq_shots=[]):
        """
        Queries shotgun for all published editorial audio files for shots in the
        current sequence

        Parameters
        ----------
        seq_shots : list of dicts
            Each dict contains the id, sg_cut_in, sg_cut_out, and code fields
            of a shot in the current sequence

        Returns
        -------
        list of dicts
            Each dict contains the following fields:
                - path,
                - entity,
                - entity.Shot.sg_cut_in,
                - entity.Shot.sg_cut_out,
                - entity.Shot.code,
                - entity.Shot.sg_cut_order,
                - version_number,
                - task,
                - task.Task.step,
                - project.Project.sg_resolution
        """
        sg_proj = self.engine.context.project
        shotgun = self.engine.shotgun
        sequence_entity = self.engine.context.entity

        # get sequence shots
        if not seq_shots:
            seq_shots = self.get_sequence_shots(list_of_shot_names)

        # get editorial audio for all sequence shots
        filters = [
            ["project", "is", sg_proj],
            ["entity", "in", seq_shots],
            ["published_file_type.PublishedFileType.code", "is", "Editorial Audio"],
            ["entity.Shot.code", "in", list_of_shot_names],
            ["sg_status_list", "is", "apr"],
        ]

        fields = [
            "path",
            "entity",
            "entity.Shot.sg_cut_in",
            "entity.Shot.sg_cut_out",
            "entity.Shot.code",
            "entity.Shot.sg_cut_order",
            "version_number",
            "task",
            "task.Task.step",
            "project.Project.sg_resolution",
        ]

        order = [{"field_name": "version_number", "direction": "desc"}]

        seq_shots_audio_publishes = shotgun.find(
            "PublishedFile", filters, fields, order=order
        )

        return seq_shots_audio_publishes

    def get_sequence_shots_editorial_videos(self, list_of_shot_names, seq_shots=[]):
        """
        Queries shotgun for all published editorial video files for shots in the
        current sequence

        Parameters
        ----------
        seq_shots : list of dicts
            Each dict contains the id, sg_cut_in, sg_cut_out, and code fields
            of a shot in the current sequence

        Returns
        -------
        list of dicts
            Each dict contains the following fields:
                - path,
                - entity,
                - entity.Shot.sg_cut_in,
                - entity.Shot.sg_cut_out,
                - entity.Shot.code,
                - entity.Shot.sg_cut_order,
                - version_number,
                - task,
                - task.Task.step,
                - project.Project.sg_resolution
                - project.Project.sg_fps
        """
        sg_proj = self.engine.context.project
        shotgun = self.engine.shotgun
        sequence_entity = self.engine.context.entity

        # get sequence shots
        if not seq_shots:
            seq_shots = self.get_sequence_shots(list_of_shot_names)

        # get editorial audio for all sequence shots
        filters = [
            ["project", "is", sg_proj],
            ["entity", "in", seq_shots],
            ["published_file_type.PublishedFileType.code", "is", "Media Review"],
            ["task.Task.step.Step.code", "is", "Editorial"],
            ["entity.Shot.code", "in", list_of_shot_names],
            ["sg_status_list", "is", "apr"],
        ]

        fields = [
            "path",
            "entity",
            "entity.Shot.sg_cut_in",
            "entity.Shot.sg_cut_out",
            "entity.Shot.code",
            "entity.Shot.sg_cut_order",
            "version_number",
            "task",
            "task.Task.step",
            "project.Project.sg_resolution",
            "project.Project.sg_fps",
        ]

        order = [{"field_name": "version_number", "direction": "desc"}]

        seq_shots_video_publishes = shotgun.find(
            "PublishedFile", filters, fields, order=order
        )

        return seq_shots_video_publishes

    def filter_latest_publishes(
        self, publishes_list, latest_publishes={}, category="media"
    ):
        """
        Filters the given list of publishes to keep only the latest version
        for each shot and category. If a publish does not have a version number,
        it attempts to extract it from the file path. Updates the existing
        `latest_publishes` dictionary with the latest versions found.

        Parameters
        ----------
        publishes_list : list
            List of dictionaries, each representing a publish with details like
            entity, version number, and path.
        latest_publishes : dict, optional
            Dictionary to update with the latest version publishes, organized
            by shot name and category.
        category : str, optional
            The category under which to organize the latest publishes. Default
            is "media".

        Returns
        -------
        dict
            Updated dictionary with the latest version publish for each shot
            and category.
        """

        version_regex = re.compile(r"(?P<head>.+)_v(?P<version>\d{3})(?P<tail>.+)")

        for publish in publishes_list:
            shot_name = publish.get("entity.Shot.code") or publish.get(
                "entity", {}
            ).get("code")
            version_number = publish.get("version_number")

            # version number could be empty because is a custom field that we
            # added it to published files but existing publishes might not have
            # a value. In that case, we try to get the version number from the
            # file path
            if not version_number:
                local_path = publish.get("path", {}).get("local_path")
                if not local_path:
                    version_number = 0
                else:
                    basename = os.path.basename(local_path)
                    match = re.match(version_regex, basename)
                    if not match:
                        version_number = 0
                    else:
                        version_number = int(match.group("version"))

                # add the version number to the publish dict
                publish["version_number"] = version_number

            # check if the current shot is already in the dictionary, if not add it
            if shot_name not in latest_publishes.keys():
                latest_publishes[shot_name] = {category: publish}
            # if the shot is already in the dictionary, check if the current
            # version is newer than the one in the dictionary. If so, replace it,
            # otherwise do nothing
            else:
                if category in latest_publishes[shot_name].keys():
                    existing_version_number = latest_publishes[shot_name][category][
                        "version_number"
                    ]
                    if version_number > existing_version_number:
                        latest_publishes[shot_name] = {category: publish}
                else:
                    latest_publishes[shot_name][category] = publish

        return latest_publishes

    def get_capture_options(self, mayacapture):
        # camera options
        cam_opts = mayacapture.CameraOptions.copy()
        cam_opts['displayResolution'] = True

        # set capture custom viewport settings
        view_opts = mayacapture.ViewportOptions.copy()
        view_opts['grid'] = False
        view_opts['polymeshes'] = True
        view_opts['displayAppearance'] = "smoothShaded"
        view_opts['nurbsCurves'] = False
        view_opts['locators'] = False
        view_opts['joints'] = False
        view_opts['pluginShapes'] = True
        view_opts['pluginObjects'] = ("gpuCacheDisplayFilter", True)
        view_opts['ikHandles'] = False
        view_opts['textures'] = True

        # Enable viewport2 AmbientOclusion
        view2_opts = mayacapture.Viewport2Options.copy()
        view2_opts['ssaoEnable'] = True
        view2_opts['maxHardwareLights'] = 8
        view2_opts['textureMaxResolution'] = 2048
        view2_opts['enableTextureMaxRes'] = False
        view2_opts["singleSidedLighting"] = False
        view2_opts["multiSampleEnable"] = False
        view2_opts["transparentShadow"] = True

        return cam_opts, view_opts, view2_opts

    # --------------------------------------------------------------------------

    def validate(self, settings, item):

        errors = []

        scene_shots = item.properties.get("scene_shots")
        if len(scene_shots['invalid_shots']) != 0:
            error_msg = (
                "The followin Shots don't match naming conventions!\n"
                "{0}"
            ).format("\n".join(scene_shots['invalid_shots']))
            self.logger.error(error_msg)
            errors.append(error_msg)

        currentPanel = cmds.getPanel(withFocus=True)
        panelType = cmds.getPanel(typeOf=currentPanel)

        if not panelType == "modelPanel":
            error_msg = "You need to select one viewport panel"
            self.logger.error(error_msg)
            if not self.parent.engine.has_ui:
                pass
            else:
                errors.append(error_msg)

        result = self.collect_shot_editorial_files(item)
        if not result:
            error_msg = "Couldn't find the latest editorial videos or audios for this sequence's shots"
            self.logger.error(error_msg)
            errors.append(error_msg)

        if errors:
            return False
        else:
            return True

    def collect_shot_editorial_files(self, item, list_of_shots=[]):

        # file_type_code_field = 'published_file_type.PublishedFileType.code'

        # editorial_preview = self.parent.shotgun.find_one(
        #     'PublishedFile',
        #     [
        #         [file_type_code_field, 'is', 'Media Review'],
        #         ['task.Task.step.Step.code', 'is', 'Editorial'],
        #         ['entity', 'is', self.parent.context.entity]
        #     ],
        #     ['path'],
        #     order=[
        #         {'field_name':'version_number', 'direction':'desc'},
        #         {'field_name':'id', 'direction':'desc'}
        #     ]
        # )

        # if editorial_preview:

        #     editorial_preview_file = editorial_preview['path']['local_path']

        #     editorial_preview_file = self.fix_path(editorial_preview_file)

        #     metasync = self.load_framework("mty-framework-metasync")
        #     transfersManager = metasync.transfersManager
        #     transfersManager.ensure_file_is_local(
        #         editorial_preview_file, editorial_preview)

        #     if os.path.exists(editorial_preview_file):
        #         item.properties["editorial_preview"] = editorial_preview_file
        #     else:
        #         return False

        # return True

        filtered_publishes_dict = {}

        if not list_of_shots:
            list_of_shots = self.get_ordered_list_of_shots()

        list_of_shot_names = [s.getShotName() for s in list_of_shots]
        seq_shot_entities = self.get_sequence_shots(list_of_shot_names)

        if not list_of_shots:
            msg = "Couldn't get any shot node in the current scene"
            self.parent.engine.logger.error(msg)
            raise Exception(msg)
        if not seq_shot_entities:
            msg = "Couldn't get any shot entity for sequence {}".format(
                self.parent.engine.context.entity
            )
            self.parent.engine.logger.error(msg)
            raise Exception(msg)

        self.parent.engine.logger.info(
            "list_of_shots:\n{}".format(pf(list_of_shots))
        )
        self.parent.engine.logger.debug(
            "seq_shot_entities:\n{}".format(pf(seq_shot_entities))
        )

        try:
            # get audio and video editorial published files for all sequence shots
            shots_audio_publishes = self.get_sequence_shots_editorial_audios(
                list_of_shot_names, seq_shot_entities
            )
            filtered_publishes_dict = self.filter_latest_publishes(
                shots_audio_publishes, category="audio"
            )

            shots_video_publishes = self.get_sequence_shots_editorial_videos(
                list_of_shot_names, seq_shot_entities
            )
            # for this call, we re-use the filtered_publishes_dict to end up with a
            # single dict containing both audio and video latest publishes
            filtered_publishes_dict = self.filter_latest_publishes(
                shots_video_publishes,
                latest_publishes=filtered_publishes_dict,
                category="video",
            )
            self.parent.engine.logger.debug(
                "filtered_publishes_dict (videos):\n{}".format(
                    pf(filtered_publishes_dict)
                )
            )
        except Exception as e:
            msg = "Failed to get published files: {}, full traceback:\n{}".format(
                e, traceback.format_exc()
            )
            self.parent.engine.logger.error(msg)
            raise Exception(msg)

        item.properties["filtered_publishes_dict"] = filtered_publishes_dict
        item.properties["seq_shot_entities"] = seq_shot_entities

        return filtered_publishes_dict

    def enable_maya_states(self):

        cmds.select(clear=True)
        thisPanel = cmds.getPanel(withFocus=True)
        panelType = cmds.getPanel(typeOf=thisPanel)

        if not panelType == "modelPanel":
            error_msg = "You need to select one viewport panel."
            if self.parent.engine.has_ui:
                self.logger.error(error_msg)
                raise Exception(error_msg)
            else:
                return [], None

        cmds.modelEditor(thisPanel, edit=True, displayTextures=True)
        states = {}
        states['cameras'] = cmds.modelEditor(thisPanel, query=True, cameras=True)
        states['joints'] = cmds.modelEditor(thisPanel, query=True, joints=True)
        states['follicles'] = cmds.modelEditor(
            thisPanel, query=True, follicles=True)
        states['deformers'] = cmds.modelEditor(
            thisPanel, query=True, deformers=True)
        states['nurbsCurves'] = cmds.modelEditor(
            thisPanel, query=True, nurbsCurves=True)
        states['ikHandles'] = cmds.modelEditor(
            thisPanel, query=True, ikHandles=True)
        states['dynamics'] = cmds.modelEditor(
            thisPanel, query=True, dynamics=True)
        states['locators'] = cmds.modelEditor(
            thisPanel, query=True, locators=True)
        for typeOfObject in states:
            eval('cmds.modelEditor(thisPanel, edit=True, ' + typeOfObject + '=False)')

        return states, thisPanel

    def concatenate_videos(self, shot_videos, pub_path, ffpeg_frwrk):

        concatenate_file_path = str(os.path.join(
            os.path.dirname(pub_path), 'concatenate_file.txt'))
        concatenate_file = open(concatenate_file_path, 'w')

        for path in shot_videos:
            concatenate_file.write("file '{}'\n".format(path))

        concatenate_file.close()
        scene_video_file = str(pub_path)

        ffmpeg_cmd = [
            '-f',
            'concat',
            '-safe',
            '0',
            '-probesize', '16M',
            '-i',
            '{}'.format(concatenate_file_path),
            '-y',
            '-c',
            'copy',
            '-preset', 'veryfast',
            '-crf', "24",
            '-max_muxing_queue_size',
            '9999',
            '{}'.format(scene_video_file)
        ]
        # ensure ffmpeg is the set bin
        ffpeg_frwrk.ffmpegCore.set_binary("convert")
        _err, _info = ffpeg_frwrk.ffmpegCore.execute_command(ffmpeg_cmd)

        if _err:
            message = "Failed to concatenate shot videos to scene video: {0}"
            raise Exception(message.format(_info))

        try:
            if os.path.exists(concatenate_file_path):
                self.parent.logger.debug(
                    "Deleting: {0}".format(concatenate_file_path)
                )
                os.remove(concatenate_file_path)
        except:
            pass

    def transcode_and_format_clip(
            self,
            mov_file_path,
            playblast_file_path,
            editorial_preview_path,
            audio_path,
            seq_first_frame,
            shot_start_frame,
            shot_end_frame,
            shot_name,
            version,
            mediatools,
            project_fps
    ):

        logger = self.parent.engine.logger

        logger.info("Transcoding and formatting clip...")
        logger.debug(f"mov_file_path: {mov_file_path}, type: {type(mov_file_path)}")
        logger.debug(f"playblast_file_path: {playblast_file_path}, type: {type(playblast_file_path)}")
        logger.debug(f"editorial_preview_path: {editorial_preview_path}, type: {type(editorial_preview_path)}")
        logger.debug(f"audio_path: {audio_path}, type: {type(audio_path)}")
        logger.debug(f"seq_first_frame: {seq_first_frame}, type: {type(seq_first_frame)}")
        logger.debug(f"shot_start_frame: {shot_start_frame}, type: {type(shot_start_frame)}")
        logger.debug(f"shot_end_frame: {shot_end_frame}, type: {type(shot_end_frame)}")
        logger.debug(f"shot name: {shot_name}, type: {type(shot_name)}")
        logger.debug(f"version: {version}, type: {type(version)}")
        logger.debug(f"mediatools: {mediatools}, type: {type(mediatools)}")
        logger.debug(f"project_fp: {project_fps}, type: {type(project_fps)}")

        # create a custom input video stream from the img seq playblast
        media_stream = mediatools.Modifier.create_input_stream(
            playblast_file_path, start_number=0, r=project_fps, probesize=u'10M'
        )

        # create a custom input stream for the audio
        audio_stream = mediatools.Modifier.create_input_stream(audio_path)

        # split the video and audio streams to use them independently
        # we will only modify the video one while passing audio as is
        video_stream = media_stream.video

        # define the video overlay options using quadrants_media_overlay
        # which will automatically trim, scale and position the media
        relative_start_frame = shot_start_frame - seq_first_frame
        relative_end_frame = shot_end_frame - seq_first_frame
        overlay_data = {
            'supD': {
                'file_path': editorial_preview_path,
                'start_frame': relative_start_frame,
                'end_frame': relative_end_frame,
            }
        }
        media_stream = mediatools.Modifier.create_quadrants_media_overlay(
            video_stream, overlay_data, media_filters={"opacity":0.35}
        )

        # define the text overlay options using quadrants_text_overlay
        # which will automatically position the text in proper sections
        # and it will also include handy text formatting defaults
        step_name = self.parent.context.step['name']
        task_name = self.parent.context.task['name']

        text_data = {
            'supA': str(shot_name),
            'supB': '{0}-{1}'.format(step_name, task_name),
            'supC': version,
            'infD': '{frame_number}:%s' % shot_start_frame,
        }

        media_stream = mediatools.Modifier.create_quadrants_text_overlay(
            text_data, media_stream, font_size=32
        )

        # finally concatenate the modified video and the original audio
        media_stream = mediatools.Modifier.ffmpeg.concat(
            media_stream, audio_stream, v=1, a=1
        )

        # and transcode it with a preset and some extra custom parameters
        mediatools.Transcoder.transcode(
            media_stream, mov_file_path, 'h264_high_yuv420p',
            r=project_fps, max_muxing_queue_size=2048
        )

    def get_ordered_list_of_shots(self):
        """Get the shots in the right editorial order instead of using the natural
        sort to support sequences where the order of the shots has been changed or
        is not a numerig order, for instance: 0050, 0040, 0030, 0020, 0010"""

        list_of_shots = pm.ls(type="shot")

        # Reorder list of shots based on the sequenceStartFrame attribute, hence
        # the order of the shots in the camera sequencer
        list_of_shots = sorted(list_of_shots, key=lambda item: item.sequenceStartFrame.get())

        return list_of_shots

    def create_sequence_playblast(
            self,
            item,
            seq_first_frame,
            seq_last_frame,
            list_of_shots,
            show_states,
            current_panel,
            publish_path,
            fields
    ):

        self.parent.logger.info("Creating shots playblasts...")

        engine = self.parent.engine
        logger = engine.logger
        shots_videos = []
        currentPanel = current_panel  # variable needed for restoring viewport view
        temp_playblast_root_path = None

        # Get editorial shot publishes dictionary --------------------------------------
        engine.show_busy("Please wait...", "Collecting shot editorial files...")
        filtered_publishes_dict = item.properties.get("filtered_publishes_dict", {})
        if not filtered_publishes_dict:
            filtered_publishes_dict = self.collect_shot_editorial_files(
                item, list_of_shots
            )
        engine.clear_busy()

        # Load frameworks --------------------------------------------------------------
        mediatools = self.load_framework("mty-framework-mediatools")
        mayacapture = self.load_framework("mty-framework-mayacapture")
        metasync = self.load_framework("mty-framework-metasync")
        transfersManager = metasync.transfersManager

        # Store current scene options --------------------------------------------------
        # Save the current value of cached playback pref
        cache_opt_orig_value = cmds.optionVar(query="cachedPlaybackEnable")

        # Disable cached playback in case it is enabled
        if cache_opt_orig_value == 1:
            cmds.optionVar(intValue=("cachedPlaybackEnable", 0))

        # Clear selection
        cmds.select(clear=True)

        # Save current frame before creating the playblasts to restore it
        # at the end
        original_current_frame = cmds.sequenceManager(
            query=True, currentTime=True
        )

        # # Enable Color Management
        # color_management_enabled = cmds.colorManagementPrefs(
        #     query=True, cmEnabled=True
        # )
        # if color_management_enabled is False:
        #     cmds.colorManagementPrefs(edit=True, cmEnabled=True)

        # Get project data from SG -----------------------------------------------------
        engine.show_busy("Please wait...", "Collecting project data...")
        project_fps = self.get_project_fps_from_sg()
        res_width, res_height = self.get_project_resolution_from_sg()
        logger.info("project fps: {0}".format(project_fps))
        logger.info(
            "project resolution: {0}, {1}".format(res_width, res_height)
        )
        engine.clear_busy()

        # Build temp playblast root path -----------------------------------------------
        logger.debug(
            "publish_path dirname: {}".format(os.path.dirname(publish_path))
        )
        logger.debug("fields:\n{}".format(pf(fields)))

        version = "v{0}".format(str(fields['version']).zfill(3))

        temp_playblast_root_path = os.path.join(
            os.path.dirname(publish_path),
            'temp',
            version
        )

        if not os.path.exists(temp_playblast_root_path):
            os.makedirs(temp_playblast_root_path)

        # Create capture options -------------------------------------------------------
        cam_opts, view_opts, view2_opts = self.get_capture_options(mayacapture)

        # TODO: Implement custom render settings and add them to the item when collecting it
        enable_two_sides = True
        if "two_side_lightning" in item.properties.get("custom_render_settings", {}).get("others", {}):
            enable_two_sides = item.properties.get("custom_render_settings", {}).get("others", {}).get("two_side_lightning", True)

        # build the generic playblast config dictionary, which will be used to create the
        # playblasts. This needs to be updated per shot in the loop, specifically these
        # values: camera, filename, frames
        playblast_config = {
            'camera': None,
            'width': res_width,
            'height': res_height,
            'filename': None,
            'frames': [],
            'format': "image",
            'compression': "jpg",
            'viewer': False,
            'overwrite': True,
            'viewport_options': view_opts,
            'viewport2_options': view2_opts,
            'camera_options': cam_opts,
            'two_sides': enable_two_sides,
            "off_screen": True,
        }

        # Create shots playblasts ------------------------------------------------------
        list_of_playblast_sequences = []

        logger.info(
            "About to iterate over shots to create each shot playblast..."
        )
        for shot_ in list_of_shots:
            shot_name = shot_.getShotName()
            logger.info(
                "Working with shot: {}".format(shot_name).rjust(80, "=")
            )
            shot_camera = shot_.getCurrentCamera()
            seq_first_frame = shot_.getSequenceStartTime()
            shot_start_frame = shot_.getStartTime()
            shot_end_frame = shot_.getEndTime()
            shot_mid_frame = int((shot_start_frame + shot_end_frame) / 2)

            list_of_frames = []
            for i in range(int(shot_start_frame), int(shot_end_frame) + 1):
                list_of_frames.append(i)

            playblast_basename = "{0}_{1}_{2}".format(
                shot_name,
                fields["Task"],
                version
            )
            playblast_img_seq_path = os.path.join(
                temp_playblast_root_path, shot_name, playblast_basename
            )
            playblast_img_seq_path = self.fix_path(playblast_img_seq_path)
            playblast_img_seq_fullpath = "{}.%04d.jpg".format(
                playblast_img_seq_path
            )
            logger.info(
                "playblast full path: {}".format(playblast_img_seq_fullpath)
            )
            list_of_playblast_sequences.append(playblast_img_seq_fullpath)

            # update playblast config with shot specific values
            playblast_config["camera"] = shot_camera
            playblast_config["filename"] = playblast_img_seq_path
            playblast_config['frames'] = list_of_frames

            cmds.sequenceManager(currentTime=shot_mid_frame)

            try:
                shot_.depthOfField.set(1)
            except:
                pass
            try:
                shot_.overscan.set(1)
            except:
                pass

            # Create playblast ---------------------------------------------------------
            self.parent.logger.info(
                "About to create playblast for shot: {}".format(shot_name)
            )
            engine.show_busy(
                "Please wait...", "Creating playblast for shot {}".format(shot_name)
            )
            try:
                mayacapture.capture(**playblast_config)
            except Exception as e:
                msg = (
                    "Failed to create playblast for shot: {}, error: {}, "
                    "full traceback:\n{}"
                ).format(shot_name, e, traceback.format_exc())
                logger.error(msg)
                engine.clear_busy()
                raise Exception(msg)
            engine.clear_busy()

            shot_playblast_path = os.path.join(temp_playblast_root_path, playblast_basename)
            shot_playblast_path = self.fix_path(shot_playblast_path)
            self.parent.logger.info(
                "shot playblast full path: {}".format(shot_playblast_path)
            )

            # options = {
            #     "format": "avi",
            #     "compression": "none",
            #     "quality": 50,
            #     "width": 1280,
            #     "height": 720,
            #     "sequenceTime": False,
            #     "forceOverwrite": True,
            #     "filename": shot_playblast_path,
            #     # "startTime": shot.getSequenceStartTime(),
            #     # "endTime": shot.getSequenceEndTime(),
            #     "startTime": shot_.startFrame.get(),
            #     "endTime": shot_.endFrame.get(),
            #     "sound": audio_path,
            #     "clearCache": True,
            #     "viewer": False,
            #     "showOrnaments": True,
            #     "percent": 100,
            # }

            # try:
            #     pm.playblast(**options)
            # except Exception as e:
            #     full_error = traceback.format_exc()

            #     raise Exception(
            #         "Failed to create playblast: %s" % e +
            #         ' - ' +
            #         '\n' +
            #         full_error
            #     )

            # version = fields['version']

            # mov_filename = shot_playblast_path.replace(".avi", ".mov")

            # transcode img sequence with audio and editorial files using
            # mediatols framework ------------------------------------------------------
            mov_basename = "{}_{}.mov".format(shot_name, version)
            mov_full_path = os.path.join(temp_playblast_root_path, mov_basename)
            mov_full_path = self.fix_path(mov_full_path)

            audio_path = (
                filtered_publishes_dict[shot_name]
                .get("audio", {})
                .get("path", {})
                .get("local_path")
            )
            audio_path = self.fix_path(audio_path)

            editorial_preview_path = (
                filtered_publishes_dict[shot_name]
                .get("video", {})
                .get("path", {})
                .get("local_path")
            )
            editorial_preview_path = self.fix_path(editorial_preview_path)

            # make sure both, video and audio files exist on the local system
            engine.show_busy("Please wait...", "Downloading enditorial files...")
            if audio_path and not os.path.exists(audio_path):
                audio_publish = filtered_publishes_dict[shot_name].get("audio", {})
                transfersManager.ensure_file_is_local(audio_path, audio_publish)

            if editorial_preview_path and not os.path.exists(
                editorial_preview_path
            ):
                video_publish = filtered_publishes_dict[shot_name].get("video", {})
                transfersManager.ensure_file_is_local(
                    editorial_preview_path, video_publish
                )
            engine.clear_busy()

            engine.show_busy(
                "Please wait...",
                "Transcoding and formatting clip for shot {}".format(shot_name)
            )
            try:
                self.transcode_and_format_clip(
                    mov_full_path,
                    playblast_img_seq_fullpath,
                    editorial_preview_path,
                    audio_path,
                    seq_first_frame,
                    shot_start_frame,
                    shot_end_frame,
                    shot_name,
                    version,
                    mediatools,
                    project_fps
                )
            except Exception as e:
                msg = (
                    "Failed to transcode and format clip for shot: {}, error: {}, "
                    "full traceback:\n{}"
                ).format(shot_name, e, traceback.format_exc())
                logger.error(msg)
                engine.clear_busy()
                raise Exception(msg)
            engine.clear_busy()

            # store the path to shot video in order to concatenate later
            if not os.path.exists(mov_full_path):
                raise Exception(
                    "Failed to create playblast for shot: {}".format(shot_name)
                )

            shots_videos.append(mov_full_path)

            # delete the playblast file, we only want to keep the transcoded one
            engine.show_busy("Please wait...", "Deleting tmp image sequence playblast")
            logger.info(
                "Deleting tmp image sequence playblast, path:\n{}".format(
                    playblast_img_seq_fullpath
                )
            )
            try:
                seq_dirname = os.path.dirname(playblast_img_seq_fullpath)
                shutil.rmtree(seq_dirname)
            except Exception as e:
                msg = "Couldn't delete path {}: {}, full traceback:\n{}".format(
                    seq_dirname, e, traceback.format_exc()
                )
                logger.error(msg)
            engine.clear_busy()

        engine.show_busy(
            "Please wait...",
            "Concatenating all shot clips into a single sequence video"
        )
        # Concatenate with ffmpeg shot videos to publish path as output
        self.concatenate_videos(shots_videos, publish_path, mediatools.ffmpeg)
        engine.clear_busy()

        # if the concatenated video exists, delete the individual shot videos
        if os.path.exists(publish_path):
            engine.show_busy(
                "Please wait...",
                "Deleting individual shot videos"
            )
            for shot_video in shots_videos:
                logger.info(
                    "Deleting individual shot video, path:\n{}".format(
                        shot_video
                    )
                )
                try:
                    os.remove(shot_video)
                except Exception as e:
                    msg = "Couldn't delete path {}: {}, full traceback:\n{}".format(
                        shot_video, e, traceback.format_exc()
                    )
                    logger.error(msg)

            # also try to delete the root playblasts videosdir
            tmp_root_videos_dir = os.path.dirname(shots_videos[0])
            logger.info("Deleting root playblasts videos dir: {}".format(tmp_root_videos_dir))
            try:
                shutil.rmtree(tmp_root_videos_dir)
            except Exception as e:
                msg = "Couldn't delete path {}: {}, full traceback:\n{}".format(
                    tmp_root_videos_dir, e, traceback.format_exc()
                )
                logger.error(msg)
            engine.clear_busy()

        # Restore original scene settings ----------------------------------------------
        # Restore cached playback value
        cmds.optionVar(intValue=("cachedPlaybackEnable", cache_opt_orig_value))
        cmds.sequenceManager(currentTime=original_current_frame)

        # restore viewport views
        if show_states:
            for typeOfObject in show_states:
                eval('cmds.modelEditor(currentPanel, e=1, ' + typeOfObject +
                    '=' + str(show_states[typeOfObject]) + ')')


    def get_root_item(self, item):
        """ A handy recursive function to get an item's root parent
            This is mostly useful because the root is the only one
            where we can store extra data
        """

        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def publish(self, settings, item):

        # Set maya viewport states. This might raise an exception and that's why it is
        # running as the first step
        showStates, currentPanel = self.enable_maya_states()

        # create a pointer to the parent root item to store
        # any extra publishes apart form the primary one
        root_item = self.get_root_item(item)
        root_item.properties.setdefault('sg_publish_extra_data', [])

        self.parent.engine.logger.debug(
            'Storing extra publish data on root item: {}'.format(str(root_item))
        )

        publish_extra_data = root_item.properties['sg_publish_extra_data']

        # set sg_camera_movement field
        self.parent.engine.show_busy(
            "Please wait...",
            "Setting camera movement for all sequence shots"
        )
        self.set_sg_shot_camera_movement(item)
        self.parent.engine.clear_busy()

        # Create the review videos
        # Get the maya work template from the settings
        work_template_name = settings["work_template"].value
        work_template = self.parent.engine.get_template_by_name(work_template_name)

        # get the current scene path
        scene_path = os.path.abspath(cmds.file(query=True, sn=True))
        scene_path = self.fix_path(scene_path)

        fields = work_template.get_fields(scene_path)
        publish_version = fields["version"]

        # Get the media review template from the settings
        publish_template_name = settings["media_review_publish_template"].value
        publish_template = self.parent.engine.get_template_by_name(
            publish_template_name
        )

        # apply the fields of the maya scene working template to the media review
        # publish template
        publish_path = publish_template.apply_fields(fields)

        # Get the maya scene publish template to be able to add it as a dependency
        # for the media review publishes
        scene_pub_template_name = settings["primary_publish_template"].value
        scene_pub_template = self.parent.engine.get_template_by_name(
            scene_pub_template_name
        )
        primary_publish_path = scene_pub_template.apply_fields(fields)

        # determine the publish name
        publish_name = fields.get("name")
        if not publish_name:
            publish_name = os.path.basename(publish_path)

        # Find additional info from the scene:
        list_of_shots = self.get_ordered_list_of_shots()

        if not list_of_shots:
            msg = "Couldn't get list of pipeline shots to playblast"
            self.parent.engine.logger.error(msg)
            raise Exception(msg)

        first_frame = int(list_of_shots[0].getStartTime())
        last_frame = int(list_of_shots[-1].getEndTime())
        self.create_sequence_playblast(
            item,
            first_frame,
            last_frame,
            list_of_shots,
            showStates,
            currentPanel,
            publish_path,
            fields
        )

        sg_fields_to_update = {
            "sg_status_list": "rev",
            "task.Task.sg_status_list": "rev",
        }

        # register the publish:
        publish_data = {
            "tk": self.parent.engine.sgtk,
            "context": item.context,
            "comment": item.description,
            "path": publish_path,
            "name": publish_name,
            "version_number": publish_version,
            "thumbnail_path": item.get_thumbnail_as_path(),
            "task": self.parent.engine.context.task,
            "sg_fields": sg_fields_to_update,
            "published_file_type": settings["publish_type"].value,
            "dependency_paths": [primary_publish_path],
        }

        sg_publishes = sgtk.util.register_publish(**publish_data)

        self.parent.logger.debug(
            "Adding existing publish " +
            "(id:%s) as extra data for item: %s" % (
                sg_publishes['id'],
                item
            )
        )

        publish_extra_data.append(sg_publishes)

        # #  -    -    -    -    -    -    -    -    -    -
        # # Publish clips media review:
        # # TODO: Link this output type to a value override
        # # to be able to turn it on and off per show or context
        # path_to_publish = publish_path
        # if not os.path.exists(os.path.dirname(path_to_publish)):
        #     os.makedirs(os.path.dirname(path_to_publish))
        #     copyfile(seq_playblast_path, path_to_publish)

        # clips_hook = self.plugin_hook(settings, 'clips_publisher')
        # list_of_published_clips = clips_hook.publish_clips(
        #     settings,
        #     item=item,
        #     path_to_source=path_to_publish,
        #     dry_run=False
        # )
        # # Pass publish data for the post_phase hook to upload it
        # publish_extra_data.extend(list_of_published_clips)
        # #  -    -    -    -    -    -    -    -    -    -

        # work_status = 'rev'

        # self.parent.engine.shotgun.update(
        #     "PublishedFile",
        #     sg_publishes["id"],
        #     {"sg_status_list": work_status}
        # )

        sg_version = self.submit_version(
            publish_path,
            [sg_publishes],
            self.parent.engine.context.task,
            item.description,
            True,
            "rev"
        )

        # Upload in a new thread and make our own
        # event loop to wait for the thread to finish.
        event_loop = QtCore.QEventLoop()
        thread = UploaderThread(
            self.parent, sg_version, publish_path,
            item.get_thumbnail_as_path(), True)
        thread.finished.connect(event_loop.quit)
        thread.start()
        event_loop.exec_()

        # log any errors generated in the thread
        for e in thread.get_errors():
            self.parent.logger.error(e)

    def submit_version(
            self,
            path_to_movie,
            sg_publishes,
            sg_task,
            comment,
            store_on_disk,
            work_status,
            override_entity=False
    ):
        """
        Create a version in Shotgun for
        this path and linked to this publish.

        """

        user = self.parent.engine.context.user

        # create a name for the version based on the file name
        # grab the file name, strip off extension
        name = os.path.splitext(os.path.basename(path_to_movie))[0]

        name = name.replace("_", " ")

        name = name.capitalize()

        if override_entity != False:
            entity = override_entity
        else:
            entity = self.parent.engine.context.entity

        # Create the version in Shotgun
        data = {
            "code": name,
            "sg_status_list": work_status,
            "entity": entity,
            "sg_task": sg_task,
            # "sg_first_frame": first_frame,
            # "sg_last_frame": last_frame,
            # "frame_count": (last_frame - first_frame + 1),
            # "frame_range": "%s-%s" % (first_frame, last_frame),
            "sg_frames_have_slate": False,
            "published_files": sg_publishes,
            "created_by": user,
            "description": comment,
            # "sg_path_to_frames": path_to_frames,
            "sg_movie_has_slate": False,
            "project": self.parent.engine.context.project,
            "user": user,
            "sg_version_type": "Production",
        }

        if store_on_disk:
            data["sg_path_to_movie"] = path_to_movie

        sg_version = self.parent.engine.shotgun.create("Version", data)

        self.parent.engine.logger.debug(
            "Created version in shotgun: {0}".format(data)
        )

        return sg_version

    def finalize(self, settings, item):
        pass

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def camera_setup(self):
        list_of_cameras = pm.ls(type="camera")

        for camera in list_of_cameras:
            try:
                camera.displayFilmGate.set(0)
                camera.displayResolution.set(0)
                camera.displayGateMask.set(0)
                camera.displayFieldChart.set(0)
                camera.displaySafeAction.set(0)
                camera.displaySafeTitle.set(0)
                camera.displayFilmPivot.set(0)
                camera.displayFilmOrigin.set(0)
                camera.overscan.set(1)
            except:
                pass

    def set_sg_shot_camera_movement(self, item):
        shots = pm.ls(type="shot")
        engine = self.parent.engine
        sg = engine.shotgun

        all_shot_names = [shot.getShotName() for shot in shots]

        seq_shot_entities = item.properties.get("seq_shot_entities", [])
        if not seq_shot_entities:
            seq_shot_entities = self.get_sequence_shots(all_shot_names)

        sg_update_requests = []
        for shot_ in shots:
            shot_name = shot_.getShotName()

            # find the sg shot entity
            for shot_entity in seq_shot_entities:
                if shot_entity["code"] == shot_name:
                    sg_shot = shot_entity
                    break

            # get relevant data from the maya shot node
            startFrame = shot_.startFrame.get()
            endFrame = shot_.endFrame.get()
            camera_ = shot_.currentCamera.get()

            # find out if the camera is moving by checking its world matrix at the start,
            #  middle, and end frames
            pm.currentTime(startFrame)
            start_frame_world_matrix = camera_.worldMatrix.get()

            pm.currentTime(endFrame)
            last_frame_world_matrix = camera_.worldMatrix.get()

            pm.currentTime(int((startFrame + endFrame) / 2))
            mid_frame_world_matrix = camera_.worldMatrix.get()

            # build generic update request
            request = {
                "request_type": "update",
                "entity_type": "Shot",
                "entity_id": sg_shot["id"],
                "data": {},
            }

            # update the request depending on camera movement
            if sg_shot.get("sg_camera_movement") != "Static Shake":
                if start_frame_world_matrix == last_frame_world_matrix == mid_frame_world_matrix:
                    # sg.update(
                    #     entity_type="Shot",
                    #     entity_id=sg_shot["id"],
                    #     data={"sg_camera_movement": "Static"}
                    # )
                    request["data"] = {"sg_camera_movement": "Static"}

                else:
                    # sg.update(
                    #     entity_type="Shot",
                    #     entity_id=sg_shot["id"],
                    #     data={"sg_camera_movement": "Moving"}
                    # )
                    request["data"] = {"sg_camera_movement": "Moving"}

            sg_update_requests.append(request)

        # run update requests
        sg.batch(sg_update_requests)

# ================================================================


class UploaderThread(QtCore.QThread):
    """
    Simple worker thread that encapsulates uploading to shotgun.
    Broken out of the main loop so that the UI can remain responsive
    even though an upload is happening
    """

    def __init__(self, app, version, path_to_movie, thumbnail_path,
                 upload_to_shotgun):
        QtCore.QThread.__init__(self)
        self._app = app
        self._version = version
        self._path_to_movie = path_to_movie
        self._thumbnail_path = thumbnail_path
        self._upload_to_shotgun = upload_to_shotgun
        self._errors = []

    def get_errors(self):
        """
        can be called after execution to retrieve a list of errors
        """
        return self._errors

    def run(self):
        """
        Thread loop
        """
        upload_error = False

        if self._upload_to_shotgun:
            try:
                self._app.tank.shotgun.upload(
                    "Version", self._version["id"], self._path_to_movie,
                    "sg_uploaded_movie"
                )
            except Exception as e:
                self._errors.append(
                    "Movie upload to Shotgun failed: {0}".format(e)
                )
                upload_error = True

        if not self._upload_to_shotgun or upload_error:
            try:
                self._app.tank.shotgun.upload_thumbnail(
                    "Version", self._version["id"], self._thumbnail_path)
            except Exception as e:
                self._errors.append(
                    "Thumbnail upload to Shotgun failed: {0}".format(e)
                )
