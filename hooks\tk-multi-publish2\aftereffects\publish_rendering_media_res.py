# Copyright (c) 2019 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
import re


import sgtk


HookBaseClass = sgtk.get_hook_baseclass()


class AfterEffectsRenderPublishPlugin(HookBaseClass):
    """
    Plugin for publishing after effects renderings. Based on the original engine hook.

    This hook relies on functionality found in the base file publisher hook in
    the publish2 app and should inherit from it in the configuration. The hook
    setting for this plugin should look something like this::

        hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_rendering.py"

    """

    REJECTED, PARTIALLY_ACCEPTED, FULLY_ACCEPTED = range(3)

    def publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        item.properties["publish_type"] = "Rendered Image"
        render_paths = item.properties.get("renderpaths", [])

        published_renderings = item.properties.get("published_renderings", [])
        # we will register whatever paths
        # are set in the render_queue item
        for each_path in render_paths:

            self.parent.logger.info("each_path: {}".format(each_path))

            # get media resolution
            media_resolution = self.parent.execute_hook_expression(
                "{config}/tk-multi-publish2/multi/resolution_from_path.py",
                "media_resolution_value",
                path=each_path
            )
            self.parent.logger.info(
                "media resolution from hook: {}".format(media_resolution)
            )

            if media_resolution:
                # Add resolution to publish publish_fields
                publish_fields = item.properties.get("publish_fields", {})
                publish_fields.update({"sg_media_resolution": media_resolution})
                item.properties["publish_fields"] = publish_fields

            match = re.search(r"[\[]?([#@]+)[\]]?", each_path)
            if match:
                each_path = each_path.replace(
                    match.group(0), "%0{}d".format(len(match.group(1)))
                )
            item.properties["path"] = re.sub(r"[\[\]]", "", each_path)

            self.parent.logger.info(
                "item publish_fields: {}".format(item.properties["publish_fields"])
            )
            self.parent.logger.info("item path: {}".format(item.properties["path"]))

            super(AfterEffectsRenderPublishPlugin, self).publish(settings, item)
            published_renderings.append(item.properties.get("sg_publish_data"))
