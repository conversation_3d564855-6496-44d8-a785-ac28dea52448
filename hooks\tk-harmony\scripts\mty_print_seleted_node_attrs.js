// Log selected node attributes. Useful to know the name and type of all of the
// attributes of a node.

var log = MessageLog.trace;

function log_attr_info(attr, indent_str)
{
   log("---------------------------");
   log(indent_str + "name: \t" + attr.name());
   log(indent_str + "keyword: \t" + attr.keyword());
   log(indent_str + "fullKeyword: \t" + attr.fullKeyword());
   log(indent_str + "type: \t" + attr.typeName());

    if (attr.typeName() == "STRING") {
       log(indent_str + "attr value: \t" + attr.textValue());
    };
    if (attr.typeName() == "INT") {
       log(indent_str + "attr value: \t" + attr.intValue());
    };
    if (attr.typeName() == "DOUBLE") {
       log(indent_str + "attr value: \t" + attr.doubleValue());
    };
    if (attr.typeName() == "DOUBLEVB") {
       log(indent_str + "attr value: \t" + attr.doubleValue());
    };
    if (attr.typeName() == "ALIAS") {
       log(indent_str + "attr value: \t" + attr.doubleValue());
    };
    if (attr.typeName() == "BOOL") {
       log(indent_str + "attr value: \t" + attr.boolValue());
    };
    if (attr.typeName() == "GENERIC_ENUM") {
       log(indent_str + "attr value: \t" + attr.textValue());
    };
    if (attr.typeName() == "ELEMENT") {
       log(indent_str + "attr value: \t" + attr.textValue());
    };
    if (attr.typeName() == "CUSTOM_NAME") {
       log(indent_str + "attr value: \t" + attr.textValue());
    };
    if (attr.typeName() == "DRAWING") {
       log(indent_str + "attr value: \t" + attr.textValue());
    };
    if (attr.typeName() == "ENABLE") {
       log(indent_str + "attr value: \t" + attr.textValue());
    };

    if (attr.typeName() == "POSITION_3D") {
        log_subattributes_info(attr);
    };
    if (attr.typeName() == "SCALE_3D") {
        log_subattributes_info(attr);
    };
    if (attr.typeName() == "ROTATION_3D") {
        log_subattributes_info(attr);
    };
    if (attr.typeName() == "DYN_GROUP") {
        log_subattributes_info(attr);
    };
    if (attr.typeName() == "PATH_3D") {
        log_subattributes_info(attr);
    };
    if (attr.typeName() == "QUATERNION_PATH") {
        log_subattributes_info(attr);
    };

    if (attr.typeName() == "DRAWING") {
        log_subattributes_info(attr);
    };

    if (attr.typeName() == "ELEMENT") {
        log_subattributes_info(attr);
    };

    if (attr.typeName() == "CUSTOM_NAME") {
        log_subattributes_info(attr);
    };
};


function log_subattributes_info(attr)
{
    var subattributes = attr.getSubAttributes();
    for (var s = 0; s < subattributes.length; ++s) {
        log_attr_info(subattributes[s], "        ");
    };
};


function main()
{
    var sel_node = selection.selectedNode(0);
    var startFrame = scene.getStartFrame();

    var attrs = node.getAttrList(sel_node, startFrame);

    for (var i = 0; i < attrs.length; ++i) {
       log("");
       log_attr_info(attrs[i], "");
    };
    log("");
    log("");
    log("Selected node type: " + node.type(sel_node));
};
