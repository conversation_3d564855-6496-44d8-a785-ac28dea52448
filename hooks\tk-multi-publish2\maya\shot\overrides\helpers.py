# -*- coding: utf-8 -*-
# Standard library:
import pprint
import json
import copy
# ___   ___   ___   ___   ___   ___  ___
# Third party:
import sgtk

# ___   ___   ___   ___   ___   ___  ___
# Project:
HookBaseClass = sgtk.get_hook_baseclass()
# ====================================================================
pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)
pf = pprint.pformat


class OverridesHelpers(HookBaseClass):
    def __init__(self, parent):
        super(OverridesHelpers, self).__init__(parent)

    @property
    def name(self):
        return 'OverridesHelpers'

    def overriden_references(self, settings, list_of_references):
        print("\n" + (">" * 120))
        pp('{0}.overriden_references'.format(self.name))
        print("list_of_references:\n{0}".format(pf(list_of_references)))
        result = {
            'list_of_references': [],
            'overriden_nodes': []
        }

        def recurse(reference, node):
            source_overrides = self.sanityze_overrides(
                json.loads(node.sourceOverrides.get())
            )
            node_overrides = {
                'xform': {
                    'rotatePivot': node.getRotatePivot().tolist(),
                    'scalePivot': node.getScalePivot().tolist(),
                    'matrix': reduce(
                        lambda x, y: x + y, node.matrix.get().tolist()
                    )
                },
                'hidden': not node.visibility.get()
            }

            is_overriden = self.__is_overriden(node_overrides, source_overrides)

            if is_overriden:
                result['overriden_nodes'].append((node.name(), is_overriden))
                if reference not in result['list_of_references']:
                    result['list_of_references'].append(reference)

            if node.listRelatives(type='transform'):
                for child in node.listRelatives(type='transform'):
                    recurse(reference, child)

        for reference in list_of_references:
            root_node = reference.nodes()[0]
            recurse(reference, root_node)

        return result

    def sanityze_overrides(self, override):
        result = copy.deepcopy(override)
        if not result.get('xform'):
            result['xform'] = {
                'matrix': [
                    1.0, 0.0, 0.0, 0.0,
                    0.0, 1.0, 0.0, 0.0,
                    0.0, 0.0, 1.0, 0.0,
                    0.0, 0.0, 0.0, 1.0
                ],
                'rotatePivot': [0.0, 0.0, 0.0],
                'scalePivot': [0.0, 0.0, 0.0],
            }

        return result

    def __is_overriden(self, node_override, source_override):
        overrides = [
            node_override['xform']['matrix'] ==
            source_override['xform']['matrix'],
            node_override['xform']['scalePivot'] ==
            source_override['xform']['scalePivot'],
            node_override['xform']['rotatePivot'] ==
            source_override['xform']['rotatePivot'],
            node_override['hidden'] ==
            source_override['hidden']
        ]

        for each in overrides:
            if each is False:
                return True

        return False
