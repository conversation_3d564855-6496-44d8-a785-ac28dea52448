#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

"""
An app to render write nodes. This is cause by the behavior of Harmony 21 which
introduces some issues when working with the SG toolkit: rendering a scene using
the usual methods might hang or take a considerable higher amount of time (let's say
3-4 hours) for a scene that outside of the pipeline would take only a few minutes.

Instead of rendering directly, we launch an extra process to handle the rendering.
NOTE: any present write node in the scene will be rendered. Perhaps in the future
would be a good idea to filter the write nodes by attribute.

"""

from tank import Hook
from tank import TankError
from tank.platform import Application
from tank.platform.qt import QtCore, QtGui
import tank

import os
import re
import six
import sys
import time
import pprint
import fileseq
import tempfile
import traceback
import subprocess
from datetime import datetime

QApplication = QtGui.QApplication
QMessageBox = QtGui.QMessageBox


class ProcessItemsHook(Hook):
    def execute(self, **kwargs):
        self.parent.engine.logger.info(
            "execute action render_write_nodes start. {}".ljust(120, "-")
        )

        time_start = datetime.now()

        # display node validation
        selected_display_node = self.get_active_display()
        main_display_node = self.get_main_display()
        if selected_display_node != main_display_node:
            msg_error = (
                "Your scene is not using the main display node:\n"
                "    current display: {}\n"
                "    main display: {}\n\n"
                "Please make sure to use the main display node, which must be "
                "located in 'Top/Display'.\n"
                "If your main display node is named differently, please rename it "
                "to 'Top/Display'."
            ).format(selected_display_node, main_display_node)

            self.parent.engine.error_sound()

            QtGui.QMessageBox.critical(None, "Wrong display node selected.", msg_error)
            result = {"succes": [], "messages": [msg_error], "errors": [1]}

            return result

        # for some reason the code continues even when selected_display_node and
        # main_display_node are different, so these lines are to avoid that. It is
        # basically the same code as before but without the sound effect and message box
        if selected_display_node != main_display_node:
            msg_error = (
                "Your scene is not using the main display node:\n"
                "    current display: {}\n"
                "    main display: {}\n\n"
                "Please make sure to use the main display node, which must be "
                "located in 'Top/Display'.\n"
                "If your main display node is named differently, please rename it "
                "to 'Top/Display'."
            ).format(selected_display_node, main_display_node)
            result = {"succes": [], "messages": [msg_error], "errors": [1]}

            return result

        # update scene burnin (in case it exists)
        # and error message will be shown only if we are at shot context

        # first we get the override to know if we need to check for this node in the
        # current project
        valueoverrides = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        ) or self.load_framework("mty-framework-valueoverrides")
        override_check_burnin_node = "mty.engine.harmony.search_for_burnin_node"
        check_burnins = valueoverrides.get_value(override_check_burnin_node)
        self.parent.logger.info(
            "check_burnins override value: {}".format(check_burnins)
        )

        if check_burnins:
            context = self.parent.context
            self.parent.logger.info("context:\n{}".format(pprint.pformat(context)))
            self.parent.logger.info(
                "entity type: {}".format(context.entity.get("type"))
            )
            if context.entity.get("type") == "Shot":
                show_error = True
            else:
                show_error = False
        else:
            show_error = False

        self.parent.logger.info("show_error: {}".format(show_error))
        burnin_values = self.set_scene_burnIn_attrs(show_error_message=show_error)

        # Ensure the scene is saved
        needs_saving = self.parent.engine.app.custom_script("scene.isDirty();")
        if needs_saving:
            # self.parent.engine.error_sound()

            msg_error = (
                "Your scene has unsaved changes, please save it first and try again."
            )
            if burnin_values:
                if len(burnin_values) == 2:
                    if burnin_values[0] != burnin_values[1]:
                        msg_error = (
                            "{}\n\nOne of the unsaved changes is the updated burnin "
                            "node:\n\n"
                            "old value:  {}\n"
                            "new value: {}"
                        ).format(msg_error, burnin_values[0], burnin_values[1])
            QtGui.QMessageBox.critical(None, "Scene needs saving.", msg_error)
            result = {"succes": [], "messages": [msg_error], "errors": [1]}

            return result

        # xstage_path = self.parent.engine.app.save_project()
        # self.parent.logger.info(
        #     "xstage_path (from save_project() method): {}".format(xstage_path)
        # )

        msg_start = (
            "Your render will start in the background. Do you want to proceed?"
        )

        # Create custom dialog
        dialog = QtGui.QDialog()
        dialog.setWindowTitle("Render start")
        dialog.setModal(True)  # Make the dialog modal

        layout = QtGui.QVBoxLayout(dialog)

        # Label with message
        label = QtGui.QLabel(msg_start)
        layout.addWidget(label)

        # Layout for buttons
        button_layout = QtGui.QHBoxLayout()

        # OK button
        ok_button = QtGui.QPushButton("OK")
        ok_button.clicked.connect(dialog.accept)

        # Cancel button
        cancel_button = QtGui.QPushButton("Cancel")
        cancel_button.clicked.connect(dialog.reject)

        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)

        layout.addLayout(button_layout)

        # Show dialog and get result
        result = dialog.exec_()

        # If cancelled, stop the process
        if result == QtGui.QDialog.Rejected:
            self.parent.logger.info("Render cancelled by user")
            return None

        successfully_rendered = self._render_write_nodes()

        self.parent.logger.info(
            "successfully_rendered: {}".format(successfully_rendered)
        )

        if successfully_rendered:
            # rendering execution time ends here
            time_end = datetime.now()
            time_difference = time_end - time_start

            # play sound from engine attribute (set in engine_init)
            self.parent.engine.success_sound()

            msg_finished = (
                "Finished rendering write nodes.\nRendering time: {}"
            ).format(time_difference)


            create_movs_start = datetime.now()
            # get output paths
            output_paths = self.get_write_nodes_output_paths()
            self.parent.logger.info(
                "output_paths:\n{}".format(pprint.pformat(output_paths))
            )
            if output_paths:
                # get the audio path if context entity type is Shot
                audio_path = self.get_audio_path()

                (
                    failed_sequences,
                    mov_output_paths,
                    sg_project_data,
                    previews_dir,
                ) = self.create_mov_files(output_paths, audio_path=audio_path)

                self.parent.logger.info(
                    "mov_output_paths:\n{}".format(mov_output_paths)
                )

                if mov_output_paths:
                    rv_path = os.environ.get("RV_PATH", None)

                    # Only proceed with RV dialog if all conditions are met
                    if rv_path and sg_project_data and previews_dir:
                        open_renders_in_rv = open_in_rv_dialog()

                        self.parent.logger.info(
                            "open_renders_in_rv: {}".format(open_renders_in_rv)
                        )

                        if open_renders_in_rv:
                            opened_in_rv = self.open_previews_in_rv(
                                previews_dir, sg_project_data["sg_fps"]
                            )
                        else:
                            opened_in_rv = None
                    else:
                        # Show message box when RV is not installed
                        msg_box = QtGui.QMessageBox()
                        msg_box.setIcon(QtGui.QMessageBox.Warning)
                        msg_box.setWindowTitle('RV Not Found')
                        msg_box.setText('RV is not installed in the system.')
                        msg_box.setStandardButtons(QtGui.QMessageBox.Ok)
                        msg_box.exec_()
                        opened_in_rv = None

                create_movs_end = datetime.now()
                create_movs_time_diff = create_movs_end - create_movs_start

                msg_finished += "\n\nCreating preview mov(s) time: {}".format(
                    create_movs_time_diff
                )

                if failed_sequences:
                    msg_finished += "\n\nCouldn't create preview mov(s) for:\n{}".format(
                        pprint.pformat(failed_sequences)
                    )

                if not opened_in_rv and mov_output_paths:
                    msg_finished += "\n\nYou can find your preview mov(s) in:\n\n{}\n".format(
                        os.path.dirname(mov_output_paths[0])
                    )

            scene_root = self.parent.engine.app.custom_script("scene.currentProjectPath();")
            scene_name = self.parent.engine.app.custom_script("scene.currentVersionName();")
            frames_location = os.path.join(scene_root, 'frames')
            previews_location = os.path.join(scene_root, "previews", scene_name)

            # QtGui.QMessageBox.information(None, "Render finished", msg_finished)

            dialog = self.create_info_dialog(msg_finished, frames_location, previews_location)
            dialog.exec_()

            result = {"succes": [1], "messages": [], "errors": []}

        elif not successfully_rendered:
            # play sound from engine attribute (set in engine_init)
            self.parent.engine.error_sound()

            msg_error = "Render failed."
            QtGui.QMessageBox.critical(None, "Render failed", msg_error)
            result = {"succes": [], "messages": [], "errors": [1]}

        self.parent.engine.logger.info(
            "execute action render_write_nodes end. {}".ljust(120, "-")
        )

        return result

    def create_info_dialog(self, message, frames_location, previews_location):
        """
        Creates a dialog with a message and an option to open two different
        folder locations in the system.

        Args:
            message (str): The message to be displayed in the dialog.
            frames_location (str): The file or directory path for frames location.
            previews_location (str): The file or directory path for previews location.

        Returns:
            QtGui.QDialog: The created dialog.
        """

        dialog = QtGui.QDialog()
        dialog.setWindowTitle("Render finished")

        layout = QtGui.QVBoxLayout(dialog)

        label = QtGui.QLabel(message)
        layout.addWidget(label)

        groupBox = QtGui.QGroupBox()
        layout_buttons = QtGui.QHBoxLayout()

        open_frames_location_button = QtGui.QPushButton("Open render folder")
        open_frames_location_button.clicked.connect(lambda: self.open_location(frames_location))

        open_previews_location_button = QtGui.QPushButton("Open previews folder")
        open_previews_location_button.clicked.connect(lambda: self.open_location(previews_location))

        ok_button = QtGui.QPushButton("Close")
        ok_button.clicked.connect(dialog.accept)

        layout_buttons.addWidget(open_frames_location_button)
        layout_buttons.addWidget(open_previews_location_button)
        layout_buttons.addWidget(ok_button)

        groupBox.setLayout(layout_buttons)
        groupBox.setStyleSheet("QGroupBox { border: 0; }")

        layout.addWidget(groupBox)

        dialog.setWindowFlag(QtCore.Qt.WindowStaysOnTopHint)
        return dialog

    def open_location(self, location):
        """
        This method opens the location in the system's default application.

        Args:
            location (str): The file or directory path to be opened.
        """

        system = sys.platform
        if system == "linux":
            cmd = 'xdg-open "%s"' % location
        elif system == "darwin":
            cmd = 'open "%s"' % location
        elif system == "win32":
            # QProcess expects backwards slashes for windows paths, so we need to
            # convert them
            location = location.replace("/", "\\")
            cmd = 'cmd.exe /C start "explorer" "%s"' % location
        else:
            raise Exception("Platform '%s' is not supported." % system)

        self.parent.engine.logger.info("Open Location full command: {}".format(cmd))

        # Qt 5.x code (PySide2)
        if QtCore.__version__.startswith("5."):
            try:
                exit_code = QtCore.QProcess.execute(cmd)
            except Exception as e:
                self.parent.engine.logger.info(
                    "Open Location error: {}, full traceback:\n{}".format(
                        e, traceback.format_exc()
                    )
                )

        # Qt 6.x code (PySide6)
        elif QtCore.__version__.startswith("6."):
            try:
                url = QtCore.QUrl.fromLocalFile(location)
                self.parent.engine.logger.info("Open Location url: {}".format(url))
                QtGui.QDesktopServices.openUrl(url)
            except Exception as e:
                self.parent.engine.logger.info(
                    "Open Location error: {}, full traceback:\n{}".format(
                        e, traceback.format_exc()
                    )
                )

    def get_active_display(self):
        dcc_app = self.parent.engine.app
        cmds = 'scene.getDefaultDisplay();'
        return dcc_app.custom_script(cmds)

    def get_main_display(self):
        dcc_app = self.parent.engine.app
        cmds  = """

include("harmony_utility_functions.js");

get_main_display_node();

    """
        return dcc_app.custom_script(cmds)

    def set_active_display(self, main_display_node):
        dcc_app = self.parent.engine.app
        cmds = 'node.setAsGlobalDisplay("{}");'.format(main_display_node)
        dcc_app.custom_script(cmds)


    def _session_path(self):
        """
        Return the path to the current session
        :return:
        """

        # get the path to the current file
        path = self.parent.engine.app.get_current_project_path()

        if sys.version_info[0] == 2:
            if isinstance(path, six.text_type):
                path = six.ensure_str(path)

        return path

    def _render_write_nodes(self):
        result = {"succes": [1], "messages": [], "errors": []}

        software_path = os.environ.get("CURRENT_SOFTWARE_PATH")
        self.parent.engine.logger.info("software_path: {}".format(software_path))
        if not software_path:
            return None

        current_session_path = self._session_path()

        # sometime _session_path return uknown
        # is because engine.app do time out

        if not current_session_path or current_session_path == "Unknown":
            # current_session_path = self.get_path_from_harmony_script()
            msg_error = (
                "Couldn't not obtain the current scene path.\n"
                "current_session_path: {}"
            ).format(current_session_path)
            QtGui.QMessageBox.critical(None, "Scene needs saving.", msg_error)
            result = {"succes": [], "messages": [], "errors": [1]}

            return result

        self.parent.engine.logger.info(
            "current_session_path: {}".format(current_session_path)
        )

        # if not current_session_path:
        #     return None

        # TODO: find a way to get the same command to work in all platforms. Most likely
        # has to do with the use of the shell argument when calling Popen, but needs
        # further investigation
        render_cmd = [
            "{}".format(software_path),
            "-batch",
            "-scene",
            "{}".format(current_session_path),
        ]
        if sys.platform != "win32":
            # render_cmd = (
            #     '{} -batch -scene {}'.format(software_path, current_session_path)
            # )
            render_cmd = " ".join(render_cmd)

        self.parent.engine.logger.info("render_cmd: {}".format(render_cmd))

        process = subprocess.Popen(
            render_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True
        )
        stdout, stderr = process.communicate()

        # decode process communication depending on python interpreter
        stdout = six.ensure_str(stdout)
        stderr = six.ensure_str(stderr)

        self.parent.engine.logger.info("stdout:\n{}".format(stdout))

        if stderr:
            if "ERROR" in stderr:
                self.parent.engine.logger.error("stderr:\n{}".format(stderr))
                return None

        return result


    def set_scene_burnIn_attrs(self, show_error_message=False):
        """
        Set attributes to scene_node and scene_peg if they exist in the scene
        """

        self.parent.engine.logger.info(
            "About to set scene_node and scene_peg attributes"
        )

        if show_error_message == True:
            show_error = "true"
        else:
            show_error = "false"

        script_str = (
            """
var log = MessageLog.trace;


function get_scene_burnin_node() {
    var nodes = node.subNodes("Top");
    log(nodes.length);
    for (var i= 0; i<nodes.length; ++i)
    {
        // log(node.type(nodes[i]))
        if (node.type(nodes[i]) == "BurnIn")
        {
            var node_name = node.getName(nodes[i]);
            if (node_name.toLowerCase().search("scene") != -1)
            {
                log(nodes[i]);
                return nodes[i];
            };
        };
    };
    // if scene_node couldn't be found
    return null;
};


function set_scene_node_attr(scene_node, current_value)
{
    var scene_name = scene.currentVersionName();
    var scene_attr_str = "Scene: " + scene_name;
    log("current_value: " + current_value);
    log("scene_attr_str: " + scene_attr_str);

    if (current_value != scene_attr_str) {
        var attr = node.getAttr(scene_node, 1, "printinfo");
        node.setTextAttr(scene_node, attr.keyword(), frame.current(), scene_attr_str);
        return scene_attr_str;
    } else {
        log("Scene Burnin node already has the right value, nothing to update");
        return current_value;
    };
};


function set_scene_peg_attrs(scene_peg)
{
    var attr_x = node.getAttr(scene_peg, 1, "position.x");
    // var attr_y = node.getAttr(scene_peg, 1, "position.y");

    attr_x.setValue(0);
    // attr_y.setValue(0);

    return null;
};

function main(show_error_message) {
    var scene_node = get_scene_burnin_node();
    if (node.getName(scene_node) != ""){
        var scene_peg = node.srcNode(scene_node, 0);
        // get the current scene_node value
        var current_value = node.getAttr(scene_node, 1, "printinfo").textValue();
        var new_value = set_scene_node_attr(scene_node, current_value);
        set_scene_peg_attrs(scene_peg);
        return [current_value, new_value];
    } else {
        var msg = (
            "Couldn't find a BurnIn node called 'Scene' at the scene Top. " +
            "Please make sure it exists and it is not inside a group, " +
            "its path must be: /Top/Scene"
        );
        log(msg);

        if (show_error_message == true){
            MessageBox.warning(msg, 1, 0, 0, "Couldn't find 'Scene' BurnIn node");
        };
        return [];
    };

    return [];
}

main(%s);

"""
            % show_error
        )

        # self.parent.logger.info("get_path_script:\n{}".format(script_str))
        engine = self.parent.engine
        # burnin_values will be either an empty list, or a list containing two elements:
        # first element is the old value, second element is the updated value
        burnin_values = engine.app.custom_script(script_str)

        return burnin_values

    def get_write_nodes_output_paths(self):
        """Get all aoutput paths of the enabled write nodes"""

        output_paths = self.parent.engine.app.custom_script(
            """
include("harmony_utility_functions.js");
var enabled_write_nodes = get_enabled_nodes_by_type("%s");
get_write_nodes_output_paths(enabled_write_nodes);
"""
            % "WRITE"
        )

        return output_paths

    def get_audio_path(self):
        engine = self.parent.engine
        context = engine.context
        context_entity_type = context.entity["type"]

        # if context entity type is Shot, get the latest editorial audio path
        if context_entity_type == "Shot":
            sg_proj = self.parent.engine.context.project
            shotgun = self.parent.engine.shotgun
            current_entity = self.parent.engine.context.entity

            order = [{"field_name": "version_number", "direction": "desc"}]
            sg_audio_data = shotgun.find_one(
                "PublishedFile",
                filters=[
                    ["project", "is", sg_proj],
                    [
                        "published_file_type.PublishedFileType.code",
                        "is",
                        "Editorial Audio",
                    ],
                    ["entity.Shot.id", "is", current_entity["id"]],
                ],
                fields=[
                    "path",
                    "entity.Shot.sg_cut_in",
                    "entity.Shot.sg_cut_out",
                    "entity",
                    "version_number",
                ],
                order=order,
            )

            if not sg_audio_data:
                audio_path = None
            else:
                audio_path = sg_audio_data.get("path", {}).get("local_path", None)
        else:
            audio_path = None

        if audio_path:
            audio_path = audio_path.replace("\\", "/")

            # ensure audio file exists locally
            self.ensure_file_is_local(audio_path, sg_audio_data)

        return audio_path

    def ensure_file_is_local(self, path, publish):
        if not hasattr(self, "metasync"):
            self.metasync = self.parent.engine.custom_frameworks.get(
                "mty-framework-metasync", None
            ) or self.load_framework("mty-framework-metasync")

        transfersManager = self.metasync.transfersManager
        if "%" in path:
            dirname = os.path.dirname(path)
            if os.path.exists(dirname) and self._collect_sequenced_files(path):
                transfersManager.ensure_local_dependencies(publish)
                return path
        else:
            if os.path.exists(path):
                transfersManager.ensure_local_dependencies(publish)
                return path

        transfersManager.ensure_file_is_local(path, publish)
        transfersManager.ensure_local_dependencies(publish)

        return path

    def get_preview_mov_path(self, sequence_obj, pattern, output_dir):
        seq_basename = sequence_obj.basename()
        regex_match = re.match(pattern, seq_basename)
        if regex_match:
            seq_basename = regex_match.groupdict().get("name", None) or seq_basename
        mov_basename = "{}_preview.mov".format(seq_basename)
        mov_full_path = os.path.join(output_dir, mov_basename)
        mov_full_path = mov_full_path.replace("\\\\", "/")
        mov_full_path = mov_full_path.replace("\\", "/")

        return mov_full_path

    def open_previews_in_rv(self, media_source, fps):
        # First try to get RV path from the environment variables. Skip if the path
        # doesn't exist in the env variables
        rv_path = os.environ.get("RV_PATH", None)
        # if rv was found, try to open all of the previews in rv
        if not rv_path:
            return None

        args = [
            # '"{}"'.format(rv_path),
            rv_path,
            "-fps",
            str(fps),
            "-l",
            "-lram",
            "4096.0",
            "-rthreads",
            "6",
            "-view",
            "defaultLayout",
            "-layout",
            "packed",
            "-mouse",
            "1",
            media_source,
        ]

        # args.extend(media_source)
        self.parent.logger.info("full rv command:\n{}".format(" ".join(args)))

        process = subprocess.Popen(
            args, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True
        )

        stdout, stderr = process.communicate()

        # decode process communication depending on python interpreter
        stdout = six.ensure_str(stdout)
        stderr = six.ensure_str(stderr)
        if stderr:
            self.parent.engine.logger.error("stderr:\n{}".format(stderr))

        if not stderr:
            return True

    def create_mov_files(self, output_paths, audio_path=None):
        """Create a mov file with audio (if audio path is provided) for each
        output path."""

        engine = self.parent.engine
        ffmpeg = engine.custom_frameworks.get(
            "mty-framework-ffmpeg"
        ) or self.load_framework("mty-framework-ffmpeg")
        ffmpeg.ffmpegCore.set_binary("convert")

        # get project data
        sg = engine.shotgun
        sg_proj = engine.context.project

        proj_filter = [["id", "is", sg_proj["id"]]]
        fields = ["sg_working_color_space", "sg_fps"]

        sg_project_data = sg.find_one("Project", proj_filter, fields)

        scene_root = self.parent.engine.app.custom_script("scene.currentProjectPath();")
        scene_name = self.parent.engine.app.custom_script("scene.currentVersionName();")

        self.parent.logger.info("scene_root: {}".format(scene_root))
        self.parent.logger.info("scene_name: {}".format(scene_name))

        if not scene_root:
            scene_root = tempfile.gettempdir()
        previews_dir = os.path.join(scene_root, "previews", scene_name)
        if not os.path.exists(previews_dir):
            os.makedirs(previews_dir)

        previews_dir = previews_dir.replace("\\\\", "/")
        previews_dir = previews_dir.replace("\\", "/")

        self.parent.logger.info("previews_dir: {}".format(previews_dir))

        # common ffmpeg command string -------------------------------------------------
        # Audio cmds section
        audio_cmd_1 = ""
        audio_cmd_2 = ""
        if audio_path:
            if os.path.exists(audio_path):
                audio_cmd_1 = ' -i "{}"'.format(audio_path)
                audio_cmd_2 = " -acodec pcm_s16le"

        ffmpeg_str = (
            "-hide_banner"
            " -loglevel error"
            " -start_number {FRAME}"
            " -r {FRAME_RATE}"
            " -i {SEQ}"
            "{AUDIO_CMD_1}"
            " -vcodec libx264"
            " -pix_fmt yuv420p"
            "{AUDIO_CMD_2}"
            " -crf 5"
            " -r {FRAME_RATE}"
            " -y"
            " {VIDEO}"
        )

        pattern = re.compile(r"(?P<name>[a-zA-Z0-9]+)(?P<tail>[.-_]+)?")

        # ------------------------------------------------------------------------------

        failed_sequences = []
        mov_output_paths = []

        for seq_path in output_paths:
            if not os.path.exists(seq_path):
                failed_sequences.append(seq_path)
                continue

            all_sequences = fileseq.findSequencesOnDisk(seq_path)
            # we assum there's only one sequence, so we grab index 0
            sequence_obj = all_sequences[0]
            # source sequence to convert
            source_seq = os.path.normpath(
                "".join(
                    [
                        sequence_obj.dirname(),
                        sequence_obj.basename(),
                        "%0{}d".format(sequence_obj._zfill),
                        sequence_obj.extension(),
                    ]
                )
            )

            mov_full_path = self.get_preview_mov_path(
                sequence_obj, pattern, previews_dir
            )

            ffmpeg_cmd = ffmpeg_str.format(
                FRAME=str(sequence_obj.start()),
                SEQ=source_seq,
                FRAME_RATE=str(sg_project_data["sg_fps"]),
                VIDEO=mov_full_path,
                AUDIO_CMD_1=audio_cmd_1,
                AUDIO_CMD_2=audio_cmd_2,
            )

            _info, _err = ffmpeg.ffmpegCore.execute_command(ffmpeg_cmd)
            if os.path.exists(mov_full_path):
                mov_output_paths.append(mov_full_path)
            else:
                if _err:
                    message = (
                        "Failed to create video: {}\nfull_ffmpeg cmd: {}\nerror:\n{}"
                    )
                    self.parent.logger.error(
                        message.format(mov_full_path, ffmpeg_cmd, _err)
                    )
                    message = "Failed to create video: {}"
                    failed_sequences.append(
                        message.format(os.path.basename(mov_full_path))
                    )

        return failed_sequences, mov_output_paths, sg_project_data, previews_dir


def open_in_rv_dialog():
    app = QApplication.instance()
    msg_box = QMessageBox()
    msg_box.setWindowTitle('Open in RV')
    msg_box.setText('Do you want to open your recently rendered files in RV?')
    msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
    result = msg_box.exec_()

    if result == QMessageBox.Yes:
        print('Opening in RV - Yes')
        return True
    elif result == QMessageBox.No:
        print('Not opening in RV - No')
        return False
