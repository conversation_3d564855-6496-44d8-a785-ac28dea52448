# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

################################################################################

includes:
- ../app_locations.yml

################################################################################

# asset
settings.tk-multi-setframerange.3dsmax:
  hook_frame_operation: "{engine}/tk-multi-setframerange/basic/frame_operations.py"
  location: "@apps.tk-multi-setframerange.location"

# blender
settings.tk-multi-setframerange.blender:
  hook_frame_operation: "{engine}/tk-multi-setframerange/frame_operations_tk-blender.py"
  location: "@apps.tk-multi-setframerange.location"

# krita
settings.tk-multi-setframerange.krita:
  hook_frame_operation: "{engine}/tk-multi-setframerange/frame_operations_tk-krita.py"
  location: "@apps.tk-multi-setframerange.location"