#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

# import mimetypes
import os
import re
import json
import sgtk
import pprint


pp = pprint.pprint
pf = pprint.pformat


HookBaseClass = sgtk.get_hook_baseclass()


class PsdProxyCollector(HookBaseClass):
    """ """

    @property
    def settings(self):
        # grab any base class settings
        collector_settings = super(PsdProxyCollector, self).settings or {}

        photoshop_settings = {
            "Primary Publish Template": {
                "type": "template",
                "default": None,
                "description": "A work file template required by this collector.",
            },
            "Work Template": {
                "type": "template",
                "default": None,
                "description": "A work file template required by this collector.",
            },
            "Publish Photoshop Proxy Template": {
                "type": "template",
                "default": None,
                "description": "A publish file template required by ps_proxy_image_item.",
            },
            "Photoshop Proxy Image File Type": {
                "type": "string",
                "default": None,
                "description": "Published file type for ps proxy images",
            },
            "PS Proxy Resize Percentage": {
                "type": "in",
                "default": None,
                "description": "Percentage for resizing the main document to export the ps proxy",
            },
        }

        # update the base settings with these settings
        collector_settings.update(photoshop_settings)

        return collector_settings

    def process_current_session(self, settings, parent_item):
        """
        Analyzes the current scene open in a DCC and parents a subtree of items
        under the parent_item passed in.

        :param dict settings: Configured settings for this collector
        :param parent_item: Root item instance
        """

        # let the parent collector do its own job
        super(PsdProxyCollector, self).process_current_session(settings, parent_item)

        self._collect_photoshop_proxy(parent_item, settings)

    def _collect_photoshop_proxy(self, item, settings):
        """
        Creates a proxy version of the main publish.
        """

        engine = self.parent.engine
        context = engine.context
        photoshop = engine.adobe

        self.parent.logger.debug(
            "collector_photoshop_proxy context:\n{}".format(pf(context))
        )

        # ------------------------------------------------------------------------------
        # Get templates and file types from settings
        primary_publish_template = settings.get("Primary Publish Template").value
        primary_work_template = settings.get("Work Template").value
        ps_proxy_image_pub_template = settings.get(
            "Publish Photoshop Proxy Template"
        ).value

        ps_proxy_image_file_type = settings.get("Photoshop Proxy Image File Type").value

        # ------------------------------------------------------------------------------
        # Load value overrides framework
        valueoverrides = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        ) or self.load_framework("mty-framework-valueoverrides")

        # Get excluded tasks from value override in case it exists
        if valueoverrides:
            default_value_code = "mty.engine.photoshop.proxy.excluded_tasks"
            override_link = {"type": "Task", "id": context.task["id"]}
            excluded_tasks = valueoverrides.get_value(
                default_value_code, link=override_link
            )
            if excluded_tasks:
                excluded_tasks = json.loads(excluded_tasks)
                self.parent.logger.info(
                    "PS proxy export excluded_tasks: {}".format(excluded_tasks)
                )

            # if current task name in excluded_tasks list, don't add the item
            current_task_name = context.task["name"]
            if current_task_name in excluded_tasks:
                return None

        # ------------------------------------------------------------------------------
        # Get document data
        PS_document = photoshop.app.activeDocument
        PS_document_path = PS_document.fullName.fsName or None

        # --------------------------------------------------------------------------
        # Create a PS proxy item
        ps_proxy_item = item.create_item(
            "photoshop.proxy",
            "Proxy Image",
            "Photoshop Proxy Image",
        )

        # get the icon path to display for this item
        icon_path = os.path.join(
            self.parent.engine.disk_location,
            "hooks",
            "tk-multi-publish2",
            "icons",
            "photoshop.png"
        )
        self.parent.logger.info("Proxy item icon_path: {}".format(icon_path))
        ps_proxy_item.set_icon_from_path(icon_path)

        # get resize percentage. This is the size percentage to which the ps document
        # will be scaled down. First we get it from the hook settings, then from the
        # overrides framework, either a default value or a value override
        resize_percentage = settings.get("PS Proxy Resize Percentage").value

        # Get excluded tasks from value override in case it exists
        if valueoverrides:
            default_value_code = "mty.publisher.photoshopcc.ps_proxy_resize_percentage"
            override_link = {"type": "Task", "id": context.task["id"]}
            resize_percentage = valueoverrides.get_value(
                default_value_code, link=override_link
            )
            if resize_percentage:
                # resize_percentage = json.loads(resize_percentage)
                if not isinstance(resize_percentage, int):
                    resize_percentage = int(resize_percentage)
                self.parent.logger.info(
                    (
                        "PS proxy export resize_percentage from override: {}, "
                        "type: {}"
                    ).format(resize_percentage, type(resize_percentage))
                )
            else:
                if not isinstance(resize_percentage, int):
                    resize_percentage = int(resize_percentage)
                self.parent.logger.info(
                    (
                        "PS proxy export resize_percentage from settings: {}, "
                        "type: {}"
                    ).format(resize_percentage, type(resize_percentage))
                )

            ps_proxy_item.properties["photoshop_proxy_data"] = {}

            ps_proxy_data = {
                "source_path": PS_document_path,
                "resize_percentage": resize_percentage
            }

        # Add relevant data to the proxy item, like the source psd path, resize
        # percentage, etc
        ps_proxy_item.properties["photoshop_proxy_data"].update(ps_proxy_data)

        tk = self.parent.engine.sgtk
        context = self.parent.engine.context
        task = self.parent.engine.context.task

        # Create the dictionary that will be used in all sibling hooks. This
        # contains all of the relevant templates and file types, as well as
        # basic date of the PS document.
        if not ps_proxy_item.properties.get(
            "templates_and_file_types"
        ):
            ps_proxy_item.properties["templates_and_file_types"] = {
                "primary_publish_template_name": primary_publish_template,
                "primary_publish_template": engine.get_template_by_name(
                    primary_publish_template
                ),
                "primary_work_template_name": primary_work_template,
                "primary_work_template": engine.get_template_by_name(
                    primary_work_template
                ),
                "publish_photoshop_proxy_template_name": ps_proxy_image_pub_template,
                "publish_photoshop_proxy_template": engine.get_template_by_name(
                    ps_proxy_image_pub_template
                ),
                "ps_proxy_image_file_type": ps_proxy_image_file_type,
                "PS_document_path": PS_document_path,
                "PS_document": PS_document,
                "tk": tk,
                "context": context,
                "task": task,
            }

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root
