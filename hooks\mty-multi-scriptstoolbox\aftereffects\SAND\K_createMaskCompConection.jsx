// By <PERSON> "<PERSON><PERSON>" Andrade
// Requested by <PERSON> "<PERSON><PERSON>" Stripes

var scriptName = "Mask comp Conector";
/* Mask maker thing. 
 * @_layer is the layer object where it will be applied the main mask aka.
 */
function maskMaker (_layer,_layerName){
    var maskV = _layer.Masks.addProperty("Mask");
    //maskV.maskFeather.setValue([463,463]);
    var brmShape = maskV.property("maskShape");
    maskV.name = _layerName;
    var brmSValue = brmShape.value;
    brmSValue.closed = true;
    brmShape.setValue(brmSValue);
    return (maskV);
}

function makeOnSelected (_layer,_mainComp){
    var layerComp = _layer.source;
    var maskNumber = _layer.property('ADBE Mask Parade').numProperties;
    var maskName = "Script Mask " + (maskNumber+1);
    var layerMask = maskMaker(_layer, maskName);
    layerMask.maskMode = MaskMode.NONE;
    var chrLayer = layerComp.layer("RIMLIGHT MASKS");
    var chrMask = maskMaker(chrLayer, maskName);
    chrMask.maskPath.expression = 'comp("'+_mainComp.name+'").layer("'+_layer.name+'").mask("'+maskName+'").maskPath';
}

function main (){
    app.beginUndoGroup("Script Mask");
    var mainComp = app.project.activeItem;
    var myLayers = mainComp.selectedLayers;
    if (myLayers.length > 0){
        var currentLayer = myLayers.length;
        var endMsg = "Mask Applied on:\n";
        while (currentLayer--) {
            makeOnSelected(myLayers[currentLayer],mainComp);
            endMsg = endMsg + "\n  * "+myLayers[currentLayer].name;
        }
        alert (endMsg,scriptName);
    }else{
        app.endUndoGroup();
        alert ("No layers selected.",scriptName);
    }
}
main();