"use strict";

function get_nodes_elements_ids_mapping(file_path)
{
    // Map elements ids to node names

    MessageLog.trace("---------------------------------------------------------------");
    MessageLog.trace("Start get_nodes_elements_ids_mapping function");

    read_nodes = node.getNodes(["READ"]);
    MessageLog.trace("read_nodes.length: " + read_nodes.length);

    id_nodes_mapping = {};
    for (i=0; i < read_nodes.length; i++)
        {
            var elem_id = node.getElementId(read_nodes[i]);
            if (elem_id != -1)
            {
                // MessageLog.trace("node:" + read_nodes[i]);
                var elem_id_str = String(elem_id);
                // MessageLog.trace("elem_id:" + elem_id);
                id_nodes_mapping[elem_id_str] = read_nodes[i];
                // id_nodes_mapping[elem_id_str] = {};
                // id_nodes_mapping[elem_id_str]["node_path"] = read_nodes[i];
                // id_nodes_mapping[elem_id_str]["element_id"] = elem_id;
            }
        }

    // var id_nodes_mapping_str = JSON.stringify(id_nodes_mapping, null, 2);

    // file = new File(file_path);
    // file.open(2);
    // file.write(id_nodes_mapping_str);
    // file.close();

    MessageLog.trace(
        "id_nodes_mapping found keys: " + Object.keys(id_nodes_mapping).length
    );

    MessageLog.trace("Finished get_nodes_elements_ids_mapping function");
    MessageLog.trace("---------------------------------------------------------------");

    return id_nodes_mapping;
}


function get_elements_mapping(file_path)
{
    // Map elements ids to element names from columns because element.numberOf
    // doesn't return all elements!

    MessageLog.trace("---------------------------------------------------------------");
    MessageLog.trace("Start get_elements_mapping function");

    var id_nodes_mapping = get_nodes_elements_ids_mapping("");
    MessageLog.trace(JSON.stringify(id_nodes_mapping, null, 2));

    column_count = column.numberOf();
    MessageLog.trace("column_count: " + column_count);
    elements_mapping = {};
    for (column_index=0; column_index < column_count; column_index++)
    {
        var column_name = column.getName(column_index);
        var column_type = column.type(column_name);
         if (column_type == "DRAWING") {
            // var column_display_name = column.getDisplayName(column_name);
            var column_element_id = column.getElementIdOfDrawing(column_name);
            var column_element_name = element.getNameById(column_element_id);
            var element_vector_type = element.vectorType(column_element_id);
            var element_physical_name = element.physicalName(column_element_id);
            var id_str = String(column_element_id);
            var node_path = id_nodes_mapping[id_str];
            var node_name = node.getName(node_path)

            elements_mapping[id_str] = {};
            elements_mapping[id_str]["element_id"] = column_element_id;
            elements_mapping[id_str]["element_name"] = column_element_name;
            elements_mapping[id_str]["element_vector_type"] = element_vector_type;
            elements_mapping[id_str]["element_physical_name"] = element_physical_name;
            elements_mapping[id_str]["node_name"] = node_name;
            elements_mapping[id_str]["node_path"] = node_path;
        }
    }

    var elements_mapping_str = JSON.stringify(elements_mapping, null, 2)

    if (!file_path || file_path == null) {
        return elements_mapping;
    }
    file = new File(file_path);
    file.open(2);
    file.write(elements_mapping_str);
    file.close();

    MessageLog.trace(
        "elements_mapping found keys: " + Object.keys(elements_mapping).length
    );

    MessageLog.trace("Finished get_elements_mapping function");
    MessageLog.trace("---------------------------------------------------------------");

}
