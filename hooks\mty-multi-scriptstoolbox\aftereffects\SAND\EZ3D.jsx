{
    function EZ3DScript(thisObj) {
        function buildUI(thisObj) {
            var layerComment = "Created by EZ3D";
            var uniqueSuffix = " Null"
            var myPanel = (thisObj instanceof Panel) ? thisObj : new Window("palette", "EZ3D", undefined, { resizeable: true });

            // Top Row
            var groupOne = myPanel.add("group", undefined, "groupOne");
            groupOne.orientation = "row";

            var btnRig = groupOne.add("button", undefined, "Rig");
            btnRig.helpTip = "Creates Parent Null Objects for every selected layer";

            var btnApply = groupOne.add("button", undefined, "Apply");
            btnApply.helpTip = "Applies Z Position and Scale Effects to Parent Null Objects";

            var btnReset = groupOne.add("button", undefined, "Reset");
            btnReset.helpTip = "Resets effects applied";

            // Middle Row
            var groupTwo = myPanel.add("group", undefined, "groupTwo");
            groupTwo.orientation = "row";

            groupTwo.add("statictext", undefined, "Z Effect:");
            var zEffectInput = groupTwo.add("edittext", undefined, "1000");
            zEffectInput.characters = 5;
            zEffectInput.helpTip = "The distance that will be applied to each layer";

            groupTwo.add("statictext", undefined, "Scale Effect:");
            var scaleEffectInput = groupTwo.add("edittext", undefined, "38");
            scaleEffectInput.characters = 5;
            scaleEffectInput.helpTip = "The amount each layer should scale to compensate for Z effect (% per 1000 distance)";

            // Bottom Row
            var groupThree = myPanel.add("group", undefined, "groupThree");
            groupThree.orientation = "row";

            var btnMatchPlane = groupThree.add("button", undefined, "Match Plane");
            btnMatchPlane.helpTip = "Match all selected layers to the same Z position as the first selected layer";

            var btnRemove = groupThree.add("button", undefined, "Remove");
            btnRemove.helpTip = "Reverts all layers back to original size and deletes null objects";

            var btnHelp = groupThree.add("button", undefined, "?");
            btnHelp.helpTip = "Help & Info";
            btnHelp.onClick = function () {
                var helpWindow = new Window("dialog", "Help & Info");
                helpWindow.orientation = "column";
                helpWindow.alignChildren = "left";

                helpWindow.add("statictext", undefined, "Script created with ♡♡love♡♡ by Nick Greenawalt (Motion by Nick).");
                helpWindow.add("statictext", undefined, "EZ3D is a script to help set up 3D scenes by turning 2D layers 3D and giving you control over their position and scale.");

                // Bullet Points
                helpWindow.add("statictext", undefined, "• Rig: Creates Parent Null Objects for every selected layer, based on the order they are selected.");
                helpWindow.add("statictext", undefined, "• Apply: Applies Z Position and Scale Effects to Parent Null Objects. Each new instance of Apply replaces the previous.");
                helpWindow.add("statictext", undefined, "• Reset: Resets effects applied to original state.");
                helpWindow.add("statictext", undefined, "• Match Plane: If multiple layers are selected, this matches all layers to the Z position of the first selected layer.");
                helpWindow.add("statictext", undefined, "• Remove: Reverts all layers back to original size and deletes Parent Null objects.");

                helpWindow.add("statictext", undefined, "");
                helpWindow.add("statictext", undefined, "For any questions, comments, concerns:");
                var emailLink = helpWindow.add("statictext", undefined, "<EMAIL>");
                emailLink.graphics.foregroundColor = emailLink.graphics.newPen(emailLink.graphics.PenType.SOLID_COLOR, [0, 0, 1], 1);

                var closeButton = helpWindow.add("button", undefined, "Close");
                closeButton.onClick = function () {
                    helpWindow.close();
                }

                helpWindow.center();
                helpWindow.show();
            };

            // Actions for "Remove" button
            btnRemove.onClick = function () {
                if (app.project.activeItem instanceof CompItem) {
                    var activeComp = app.project.activeItem;
                    var layers = activeComp.layers;

                    app.beginUndoGroup("EZ3D Remove Nulls");

                    var nullLayers = [];

                    for (var i = layers.length; i > 0; i--) {
                        var currentLayer = layers[i];
                        if (currentLayer.comment === layerComment) {
                            // Set scale and Z position back to original
                            currentLayer.scale.setValue([100, 100, 100]);
                            currentLayer.position.setValue([currentLayer.position.value[0], currentLayer.position.value[1], 0]);
                            nullLayers.push(currentLayer);

                            var childLayerName = currentLayer.name.split(uniqueSuffix)[0] // define the string we expect for the art layer name 
                            if (layers.byName(childLayerName)) { // check there is still a layer with that name
                                if (layers.byName(childLayerName).parent === currentLayer) { // check it's parented to the current null
                                    layers.byName(childLayerName).threeDLayer = false; // make it 2D
                                }
                            }
                        }
                    }

                    // Now, after all nulls have been reset, delete them
                    for (var j = 0; j < nullLayers.length; j++) {
                        nullLayers[j].remove();
                    }

                    app.endUndoGroup();
                } else {
                    alert("Mochiron, senpai! But you need to be in the right composition. (＾◡＾)");
                }
            }

            // Actions for "Rig" button
            btnRig.onClick = function () {
                if (app.project.activeItem instanceof CompItem) {
                    var activeComp = app.project.activeItem;
                    var selectedLayers = activeComp.selectedLayers;

                    app.beginUndoGroup("EZ3D Rigging");

                    for (var i = 0; i < selectedLayers.length; i++) {
                        selectedLayers[i].threeDLayer = true;
                        var myNull = activeComp.layers.addNull();
                        myNull.threeDLayer = true;
                        myNull.name = selectedLayers[i].name + uniqueSuffix;
                        myNull.comment = layerComment;
                        selectedLayers[i].parent = myNull;
                    }

                    app.endUndoGroup();
                } else {
                    alert("UwU! Make sure you have a composition active and some layers selected!");
                }
            }

            // Actions for "Apply" button
            btnApply.onClick = function () {
                if (app.project.activeItem instanceof CompItem) {
                    var activeComp = app.project.activeItem;
                    var layers = activeComp.layers;

                    app.beginUndoGroup("EZ3D Apply Effects");

                    var zEffect = parseFloat(zEffectInput.text);
                    var scaleEffectPercentage = parseFloat(scaleEffectInput.text) / 100;
                    var nullCounter = 0;
                    for (var i = layers.length; i > 0; i--) {
                        var currentLayer = layers[i];
                        if (currentLayer.comment === layerComment) {
                            var zOffset = nullCounter * zEffect;
                            currentLayer.position.setValue([currentLayer.position.value[0], currentLayer.position.value[1], zOffset]);

                            var scaleIncrease = 1 + (zOffset / 1000 * scaleEffectPercentage);
                            currentLayer.scale.setValue([currentLayer.scale.value[0] * scaleIncrease, currentLayer.scale.value[1] * scaleIncrease, currentLayer.scale.value[2] * scaleIncrease]);

                            nullCounter++;
                        }
                    }

                    app.endUndoGroup();
                } else {
                    alert("OwO! Something's not right! Make sure you're in the right composition.");
                }
            }

            // Actions for "Reset" button
            btnReset.onClick = function () {
                if (app.project.activeItem instanceof CompItem) {
                    var activeComp = app.project.activeItem;
                    var layers = activeComp.layers;

                    app.beginUndoGroup("EZ3D Reset");

                    for (var i = layers.length; i > 0; i--) {
                        var currentLayer = layers[i];
                        if (currentLayer.comment === layerComment) {
                            currentLayer.position.setValue([currentLayer.position.value[0], currentLayer.position.value[1], 0]);
                            currentLayer.scale.setValue([100, 100, 100]);
                        }
                    }

                    app.endUndoGroup();
                } else {
                    alert("Nyan! Something's fishy! Make sure you're in the right composition.");
                }
            }

            // Actions for "Match Plane" button
            btnMatchPlane.onClick = function () {
                if (app.project.activeItem instanceof CompItem) {
                    var activeComp = app.project.activeItem;
                    var selectedLayers = activeComp.selectedLayers;

                    if (selectedLayers.length < 2) {
                        alert("UwU! You need to select at least two layers or nulls to match them.");
                        return;
                    }

                    app.beginUndoGroup("EZ3D Match Plane");

                    var referenceZ = selectedLayers[0].position.value[2];
                    var referenceScale = selectedLayers[0].scale.value;

                    for (var i = 1; i < selectedLayers.length; i++) {
                        var currentPosition = selectedLayers[i].position.value;
                        selectedLayers[i].position.setValue([currentPosition[0], currentPosition[1], referenceZ]);
                        selectedLayers[i].scale.setValue(referenceScale); // Set scale of matched layers to the same value as reference layer
                    }

                    app.endUndoGroup();
                } else {
                    alert("Teehee! Looks like you're lost! Make sure you're in the right composition.");
                }
            }

            myPanel.layout.layout(true);
            return myPanel;
        }

        var EZ3DScriptPal = buildUI(thisObj);

        if ((EZ3DScriptPal != null) && (EZ3DScriptPal instanceof Window)) {
            EZ3DScriptPal.center();
            EZ3DScriptPal.show();
        }
    }

    EZ3DScript(this);
}