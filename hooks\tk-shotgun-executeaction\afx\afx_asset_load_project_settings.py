#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

"""
An app that loads a template specific to the project and sets all relevant
project settings found in SG:
    shot settings:
        - shot sg_cut_in (first shot frame)
        - shot sg_cut_out (last shot frame)
    project settings:
        - frame rate
        - resolution
        - working color space

"""

from tank import Hook
from tank import TankError
from tank.platform import Application
from tank.platform.qt import QtCore, QtGui
import tank

import os
import re
import pprint


class ProcessItemsHook(Hook):
    def execute(self, **kwargs):

        self.parent.engine.logger.info(
            "execute action load_project_settings start. {}".format("-" * 80)
        )

        result = self.load_project_settings()

        self.parent.engine.logger.info(
            "execute action load_project_settings end. {}".format("-" * 80)
        )

        return result

    def load_project_settings(self):

        adobe = self.parent.engine.adobe

        result = {"succes": [1], "messages": [], "errors": []}

        # Get relevant data for the hook
        project_data = self.get_project_data_from_SG()

        self.parent.engine.logger.info(
            "project_data:\n{}".format(pprint.pformat(project_data))
        )

        # set project settings
        self.set_colorspace(project_data.get("sg_working_color_space"))
        self.set_color_depth()
        self.set_time_display_type()

        compositions = self.collect_all_compositions_in_project()
        self.parent.engine.logger.info("found {} compositions".format(len(compositions)))
        # compositions = list(set(compositions))
        # self.parent.engine.logger.info("found {} compositions after set".format(len(compositions)))

        # set shot settings
        if compositions:
            list_of_output_comps = self.filter_output_compositions(compositions)
            # list_of_output_comps = list(set(list_of_output_comps))
            self.parent.engine.logger.info("found {} output compositions".format(len(list_of_output_comps)))

            if list_of_output_comps:
                frame_rate = project_data.get("sg_fps")
                # resolution = project_data.get("sg_resolution")
                # get resolution from SG
                hook_expression = "{config}/get_entity_resolution.py"
                resolution = self.parent.engine.execute_hook_expression(
                    hook_expression,
                    "get_resolution",
                    engine=self.parent.engine,
                )

                for comp in list_of_output_comps:
                    self.set_composition_settings(
                        comp,
                        frame_rate,
                        resolution,
                    )

        # remove render queue items
        render_queue_items = self.collect_all_render_items_in_render_queue()
        self.remove_items(render_queue_items)

        msg = (
            "Finished loading the project settings.\nPlease "
            "save your file using SG's File Save."
        )
        self.show_message(msg, icon="Information")
        # alert_box = adobe.alert(msg)

        return result

    def get_project_data_from_SG(self):
        """
        Returns a dictionary of project data from SG relevant to a
        shot of the following form:

            {
                'code': (str),
                 'id': (int),
                 'sg_fps': (float),
                 'sg_output_color_space': (str),
                 'sg_working_color_space': (str),
                 'type': (str),
             }
        """

        project_data = {}

        filters = [
            ["id", "is", self.parent.engine.context.project["id"]],
        ]
        fields = [
            "code",
            "id",
            "sg_fps",
            "sg_working_color_space",
            "sg_output_color_space",
        ]

        project_data = self.parent.engine.shotgun.find_one(
            entity_type="Project", filters=filters, fields=fields
        )

        return project_data

    def set_colorspace(self, working_colorspace):

        adobe = self.parent.engine.adobe

        if working_colorspace:
            colorspace_mappings = {
                "sRGB": "sRGB IEC61966-2.1",
                "rec709 2.4": "Rec.709 Gamma 2.4",
                "rec709": "HDTV (Rec. 709)",
            }

            if working_colorspace in colorspace_mappings.keys():
                afx_colorspace = colorspace_mappings[working_colorspace]

                adobe.app.project.workingSpace = afx_colorspace
                self.parent.engine.logger.info(
                    "Set working colorspace to {}".format(afx_colorspace)
                )
            else:
                self.parent.engine.logger.info(
                    "Couldn't find an afx colorspace mapping for colorspace '{}'".format(
                        working_colorspace
                    )
                )

    def set_color_depth(self):
        """
        Sets the project color depth per channel. Valid choices are:
            8
            16
            32
        """

        adobe = self.parent.engine.adobe

        # Read color_depth value from tk-aftereffects.yml
        color_depth = self.parent.get_setting("color_depth")
        adobe.app.project.bitsPerChannel = color_depth

    def set_time_display_type(self):
        """
        Sets display time to frames instead of using the default: timecode.

        There are two different choices:
            2012 = Timecode
            2013 = Frames
        """

        adobe = self.parent.engine.adobe

        adobe.app.project.timeDisplayType = 2013

    def collect_all_compositions_in_project(self):
        compositions = []

        adobe = self.parent.engine.adobe

        project_items = adobe.app.project.items

        for i, item in enumerate(self.parent.engine.iter_collection(project_items)):
            if item.typeName == "Composition":
                if item not in compositions:
                    compositions.append(item)

        return compositions

    def filter_output_compositions(self, list_of_comps):
        """
        Filters a list of comps to get only comps which name start with 'Output'
        It is NOT case sensitive so either 'Output' or 'output' should work.
        """

        output_comps = []

        for comp in list_of_comps:
            original_comp_name = comp.name
            match_pattern = re.compile(r"(?P<output_tag>^[Oo]utput)")
            match = re.match(match_pattern, original_comp_name)
            if match:
                if comp not in output_comps:
                    output_comps.append(comp)

        return output_comps

    def set_composition_settings(
        self,
        comp,
        frame_rate,
        resolution,
    ):

        adobe = self.parent.engine.adobe

        if frame_rate:
            comp.frameRate = frame_rate

        if resolution:
            pattern = re.compile(r"(?P<width>\d+)x(?P<height>\d+)")
            match = re.match(pattern, str(resolution))
            if match:
                width = int(match.groupdict()["width"])
                height = int(match.groupdict()["height"])

                comp.width = width
                comp.height = height

    def collect_all_render_items_in_render_queue(self):
        render_queue_items = []

        adobe = self.parent.engine.adobe

        for i, queue_item in enumerate(
            self.parent.engine.iter_collection(adobe.app.project.renderQueue.items)
        ):
            render_queue_items.append(queue_item)

        return render_queue_items

    def remove_items(self, list_of_items):
        for item in list_of_items:
            item.remove()

    def show_message(self, msg, icon=None):
        """
        Shows a message box with the input message
        """

        from sgtk.platform.qt import QtCore, QtGui

        icons_dict = {
            "NoIcon": QtGui.QMessageBox.NoIcon,
            "Question": QtGui.QMessageBox.Question,
            "Information": QtGui.QMessageBox.Information,
            "Warning": QtGui.QMessageBox.Warning,
            "Critical": QtGui.QMessageBox.Critical,
        }

        if not icon or icon not in icons_dict.keys():
            icon_selection = icons_dict["NoIcon"]
        else:
            icon_selection = icons_dict[icon]

        msg_error = QtGui.QMessageBox()
        msg_error.setWindowTitle("Finished execution.")
        msg_error.setText(msg)
        msg_error.setIcon(icon_selection)
        msg_error.exec_()
