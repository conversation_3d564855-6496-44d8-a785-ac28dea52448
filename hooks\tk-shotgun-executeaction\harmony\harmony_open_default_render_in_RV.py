#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

"""
An app that fixes amn image (read) node's scale and proportions, by setting the right
parameters in the node properties windows. Basically replicates the import options
when using "Project Resolution" in the alignment rules when importing image as bitmap.
Also adds the proper scaling in the transformation tab

"""

from tank import Hook
from tank import TankError
from tank.platform import Application
from tank.platform.qt import QtCore, QtGui
import tank

import os
import re
import six
import pprint
import fileseq
import subprocess


class ProcessItemsHook(Hook):
    def execute(self, **kwargs):
        self.parent.engine.logger.info(
            "\nexecute action open_default_render_in_RV start. {}".format("-" * 80)
        )

        opened_in_rv = None

        selected_previews = self.open_selection_window_for_rv()
        if not selected_previews:
            result = {
                "succes": [],
                "messages": ["Couldm't get selected sequences"],
                "errors": [1]
            }
            return result

        selected_sequences_paths = self.find_selected_sequences_paths(selected_previews)

        self.parent.engine.logger.info('Selected previews: '+ str(selected_previews))
        self.parent.engine.logger.info('Selected sequences path: '+ str(selected_sequences_paths))

        if len(selected_previews) > 0:
            opened_in_rv = self.open_selected_seq_in_RV(selected_sequences_paths)

        if not opened_in_rv:
            result = {
                "succes": [],
                "messages": ["Couldm't open sequences in RV"],
                "errors": [1]
            }
        else:
            result = {
                "succes": [1],
                "messages": ["Succesfully opened sequences in RV"],
                "errors": []
            }

        # # Ensure the scene is saved
        # self.parent.engine.app.save_project()

        self.parent.engine.logger.info(
            "\nexecute action open_default_render_in_RV end. {}".format("-" * 80)
        )

        return result

    def extract_names_from_path(self, path_list):
        names = []
        for path in path_list:
            # file_name = path.split('/')[-1]
            # self.parent.engine.logger.info("working with path: {}".format(path))
            file_name = os.path.basename(path)
            file_name = file_name.split('.')[0]
            names.append(file_name)

        return names

    def fix_path(self, path):
        path = path.replace("\\\\", "/")
        path = path.replace("\\", "/")

        return path

    # def find_rendered_sequence(self):
    #     """
    #     Finds a sequence matching basename == 'final.' and extension == '.png'
    #     Return:
    #         fileseq object or None
    #     """
    #     current_project_path = self.parent.engine.app.get_current_project_path()
    #     frames_folder = "{}/frames".format(os.path.dirname(current_project_path))

    #     sequences = fileseq.findSequencesOnDisk(frames_folder)

    #     rendered_sequence = None
    #     for seq in sequences:
    #         seq_basename = seq.basename()
    #         seq_extension = seq.extension()
    #         if not seq_basename == "final." or not seq_extension == ".png":
    #             continue
    #         rendered_sequence = seq
    #         break

    #     return rendered_sequence

    def find_selected_sequences_paths(self, selected_previews):
        """
        Finds sequences matching the selected names
            Args:
                selected_previews (list): write nodes names
            Return:
                selected_sequences_dir_list (list):
                    selected sequences paths compatible with rv
        """
        self.parent.logger.debug(" ** Find selected sequences paths... ")
        scene_root = self.parent.engine.app.custom_script("scene.currentProjectPath();")

        self.parent.logger.debug("scene_root: {}".format(scene_root))

        sequences_dir = os.path.join(scene_root, "frames")
        sequences_dir = self.fix_path(sequences_dir)

        self.parent.logger.debug("sequences_dir: {}".format(sequences_dir))

        # fix output paths to correct UNC paths
        fixed_output_paths = []
        for output_path in self.output_paths:
            fixed_output_path = self.fix_UNC_path(output_path)
            fixed_output_paths.append(fixed_output_path)

        self.output_paths = fixed_output_paths
        self.parent.logger.info("output_paths: {}".format(self.output_paths))
        self.parent.logger.info("selected_previews: {}".format(selected_previews))

        # get selected sequences paths
        selected_sequences_dir_list = []
        for output_path in self.output_paths:
            dirname = os.path.dirname(output_path)
            # file_name = os.path.basename(output_path).split('.')[0]
            # ext = os.path.basename(output_path).split('.')[-1]
            file_name, ext = os.path.splitext(os.path.basename(output_path))
            # remove the sequence number part from file_name
            file_name, _ = os.path.splitext(file_name)
            self.parent.engine.logger.debug(
                "dirname: {}, file_name: {}, ext: {}".format(dirname, file_name, ext)
            )
            if file_name in selected_previews:
                # seq_path = dirname + '/' + file_name + '.#' + ext
                seq_path = "{}/{}.#{}".format(dirname, file_name, ext)
                seq_path = self.fix_path(seq_path)
                self.parent.engine.logger.debug(
                    "seq_path: {}".format(seq_path)
                )
                selected_sequences_dir_list.append(seq_path)

        return selected_sequences_dir_list

    def get_sg_editorial_info(self):
        sg_proj = self.parent.engine.context.project
        shotgun = self.parent.engine.shotgun
        current_entity = self.parent.engine.context.entity

        filters = [
            ["project", "is", sg_proj],
            ["published_file_type.PublishedFileType.code", "is", "Editorial Audio"],
            ["entity.Shot.id", "is", current_entity["id"]],
        ]

        fields = [
            "path",
            "entity.Shot.sg_cut_in",
            "entity.Shot.sg_cut_out",
            "entity",
            "version_number",
            "created_at",
        ]

        order = [{"field_name": "created_at", "direction": "desc"}]

        sg_entity = shotgun.find_one("PublishedFile", filters, fields, order=order)

        return sg_entity

    def ensure_file_is_local(self, path, publish):
        if not hasattr(self, "metasync"):
            self.metasync = self.load_framework("mty-framework-metasync")

        transfersManager = self.metasync.transfersManager

        if not os.path.exists(path):
            transfersManager.ensure_file_is_local(path, publish)

        return path

    def get_sg_project_info(self, proj_fields):
        engine = self.parent.engine
        sg = engine.shotgun
        sg_proj = engine.context.project

        proj_filter = [["id", "is", sg_proj["id"]]]

        sg_proj = sg.find_one("Project", proj_filter, proj_fields)

        return sg_proj

    def open_selection_window_for_rv(self):
        """
        Opens qt window with active write nodes name list for user to select.

            Returns:
                selected_names (list): names of selected write nodes
            Raises:
                Exception: If rendered sequences are not found in the 'frames' folder.

            Note:
            - It is assumes that the `CheckboxListDialog` class is defined elsewhere
            in the code.

        """

        self.parent.logger.debug(" ** Open selection window for rv... ")
        # get enabled write nodes
        self.output_paths = self.parent.engine.app.custom_script("""
            include("harmony_utility_functions.js");
            var enabled_write_nodes = get_enabled_nodes_by_type("WRITE");
            log("Enabled write nodes:");
            log(JSON.stringify(enabled_write_nodes, null, 2));
            get_write_nodes_output_paths(enabled_write_nodes);
            """
        )
        self.parent.engine.logger.info('Output paths: ' + str(self.output_paths))

        # extract names for ui window
        ui_names = self.extract_names_from_path(self.output_paths)
        self.parent.engine.logger.info('ui_names: ' + str(ui_names))

        # ensure write nodes have rendered files
        missing_rendered_sequences = []
        for seq_path in self.output_paths:
            seq_path = self.fix_path(seq_path)
            # self.parent.engine.logger.info('seq path: ' + str(seq_path))
            if not os.path.exists(seq_path):
                missing_rendered_sequences.append(seq_path)

        # raise error if nothing is rendered,
        # if some sequences are not rendered, create message
        msg = None
        if missing_rendered_sequences:
            if len(missing_rendered_sequences) == len(self.output_paths):
                msg = (
                    "Couldn't find any rendered sequence in the project's "
                    "'frames' folder. Please render it/them first and try again."
                )
                QtGui.QMessageBox.warning(None, "Render your sequence first", msg)
                raise Exception(msg)
            else:
                self.parent.engine.logger.debug('missing rendered seq list: '+ str(missing_rendered_sequences))
                missing_names = self.extract_names_from_path(missing_rendered_sequences)
                self.parent.engine.logger.debug('missing names: ' + str(missing_names))
                # remove missing names from ui list name
                new_list = [item for item in ui_names if item not in missing_names]
                self.parent.engine.logger.debug('new list without missing: ' + str(new_list))
                ui_names = new_list
                msg = (
                    " Warning:\n"
                    "\nMissing rendered sequence(s):\n    - {0}\n "
                    "\nTo be able to open {1} in RV, please render {1} first "
                    "and try again.\n\n"
                )
                if len(missing_names) == 1:
                    msg = msg.format("\n    - ".join(missing_names), "it")
                else:
                    msg = msg.format("\n    - ".join(missing_names), "them")

        dialog = CheckboxListDialog(ui_names, msg)
        dialog.open_dialog()

        selected_names = dialog.selected_names
        return selected_names

    def open_selected_seq_in_RV(self, selected_sequences):
        """
        Opens selected rendered sequences in RV, located in the frames folder of the
        current project.
        This function launches RV with the specified media sources, including audio if
        the context is a shot.

            Args:
                selected_sequences (list): a list of paths compatible with rv
            Returns:
                bool: True if RV was successfully launched; otherwise, None.
            Raises:
                Exception: If there are issues with finding RV, missing published
                audio, or other exceptions during the RV launch.

        """

        result = None

        # First try to get RV path from the environment variables. Return if the path
        # doesn't exist in the env variables
        rv_path = os.environ.get("RV_PATH", None)

        if not rv_path or not os.path.exists(rv_path):
            msg = (
                "Couldn't find RV installed in the system.\n\n(Is it installed in the "
                "default location?)"
            )
            self.parent.engine.logger.error(
                "RV path couldn't be found in the env variables"
            )
            QtGui.QMessageBox.critical(None, "Couldn't find RV", msg)
            return result

        current_entity = self.parent.engine.context.entity
        entity_type = current_entity.get("type", "").lower()

        self.parent.logger.info('RV path: ' + rv_path)

        # Get latest audio (editorial) published file, only if entity_type is 'shot'
        if entity_type == "shot":
            sg_audio_data = self.get_sg_editorial_info()
            if not sg_audio_data:
                # Editorial audio is missing
                msg = "Apparently there's no published audio for this shot."
                QtGui.QMessageBox.error(None, "No published audio", msg)
                raise Exception(msg)

            last_published_audio = sg_audio_data.get("path", False)

            # Audio validation
            if not last_published_audio:
                # Editorial audio is missing
                msg = "Apparently there's no published audio for this shot."
                QtGui.QMessageBox.error(None, "No published audio", msg)
                raise Exception(msg)

            last_published_audio_path = last_published_audio.get("local_path", None)
            last_published_audio_path = self.fix_path(last_published_audio_path)

            # ensure file exists locally
            if not os.path.exists(last_published_audio_path):
                self.ensure_file_is_local(last_published_audio_path, sg_audio_data)

        # Finally we launch rv with the needed arguments
        sg_proj = self.get_sg_project_info(
            ["sg_working_color_space", "sg_fps"]
        )

        fps = str(sg_proj.get("sg_fps")) or "24"

        # set sources depending on the context: for shots, audio is a requirement,
        # but for assets, it is not.
        media_sources = r""
        for path in selected_sequences:
            if entity_type == "shot":
                # media_sources.append('[ ' + path + ' ' + last_published_audio_path + ' ] ')
                media_sources += r"[ {} {} ] ".format(path, last_published_audio_path)
            elif entity_type == "asset":
                # media_sources.append(path + ' ')
                media_sources += r"{} ".format(path)

        # add main brackets to media sources
        # media_sources = r"[ {}]".format(media_sources)
        self.parent.logger.info('media_sources: {}'.format(media_sources))

        args = [
            '"{}"'.format(rv_path),
            "-fps",
            str(fps),
            "-l",
            "-lram",
            "4096.0",
            "-rthreads",
            "6",
            "-view",
            "defaultLayout",
            "-layout",
            "packed",
            "-mouse",
            "1",
        ]

        args.append(media_sources)

        full_cmd = " ".join(args)
        self.parent.engine.logger.info("full rv command:\n{}".format(full_cmd))

        extract = subprocess.Popen(
            full_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True
        )

        stdout, stderr = extract.communicate()
        # decode process communication depending on python interpreter
        stdout = six.ensure_str(stdout)
        stderr = six.ensure_str(stderr)
        if stderr:
            self.parent.engine.logger.error("stderr:\n{}".format(stderr))

        if not stderr:
            return True

        return result

    def fix_UNC_path(self, path):
        """
        Fixes UNC paths to be compatible with RV.
        """
        # last index of filesystem_locations is the workarea path
        workarea_path = self.parent.engine.context.filesystem_locations[-1]
        workarea_path = self.fix_path(workarea_path)
        # self.parent.engine.logger.info("workarea_path: {}".format(workarea_path))
        # the basename of the filesystem_locations is the shot or asset name in Harmony
        workarea_basename = os.path.basename(workarea_path)
        # self.parent.engine.logger.info("workarea_basename: {}".format(workarea_basename))
        path_split = path.split(workarea_basename)
        # self.parent.engine.logger.info("path_split: {}".format(path_split))
        # build the fixed path
        path = "{}{}".format(workarea_path, path_split[-1])

        return path


class CheckboxListDialog(QtGui.QDialog):
    """
    A custom QDialog for displaying a list of checkboxes.

    This dialog allows the user to select items from a list using checkboxes.

    Args:
        names (list of str): A list of names to be displayed as checkboxes.

    Methods:
        get_selected_names(): Get a list of selected names.
        open_dialog(): Open the dialog and interact with the user.
    """
    def __init__(self, names, message=None):
        """
        Initialize the CheckboxListDialog.

        Args:
            names (list of str): A list of names to be displayed as checkboxes.
        """
        QtGui.QDialog.__init__(self)
        self.setWindowTitle('Enabled write node(s)')
        self.setMinimumWidth(240)

        layout = QtGui.QVBoxLayout()

        # add label with custom message if it exists
        if message:
            label = QtGui.QLabel(message)
            layout.addWidget(label)

        # Group box for checkboxes
        group_box = QtGui.QGroupBox("Available sequences")
        # Layout for checkboxes within the group box
        group_layout = QtGui.QVBoxLayout()

        self.selected_names = []
        self.name_checkboxes = []
        for name in names:
            checkbox = QtGui.QCheckBox(name)
            checkbox.setChecked(True)
            self.name_checkboxes.append(checkbox)
            group_layout.addWidget(checkbox)

        group_box.setLayout(group_layout)
        layout.addWidget(group_box)

        button_layout = QtGui.QVBoxLayout()

        ok_button = QtGui.QPushButton('OK')
        ok_button.clicked.connect(self.accept)
        button_layout.addWidget(ok_button)

        cancel_button = QtGui.QPushButton('Cancel')
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(cancel_button)

        layout.addLayout(button_layout)

        self.setLayout(layout)

    def get_selected_names(self):
        """
        Get a list of selected names from the checkboxes.

        Returns:
            list of str: A list of selected names.
        """
        self.selected_names = [checkbox.text() for checkbox in self.name_checkboxes if checkbox.isChecked()]

    def open_dialog(self):
        """
        Open the dialog and interact with the user.

        This method displays the dialog to the user and handles the user's interaction.
        """

        if not QtGui.QApplication.instance():
            app = QtGui.QApplication(sys.argv)
        result = self.exec_()

        if result == QtGui.QDialog.Accepted:
            self.selected_names = [checkbox.text() for checkbox in self.name_checkboxes if checkbox.isChecked()]
        else:
            self.selected_names = []
