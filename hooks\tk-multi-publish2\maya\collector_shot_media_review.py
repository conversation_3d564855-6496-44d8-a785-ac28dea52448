#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

import mimetypes
import os
import sgtk
import json
from tank_vendor import six

HookBaseClass = sgtk.get_hook_baseclass()


class ShotMediaReviewCollector(HookBaseClass):
    """
    """

    def process_current_session(self, settings, parent_item):
        """
        Analyzes the current scene open in a DCC and parents a subtree of items
        under the parent_item passed in.

        :param dict settings: Configured settings for this collector
        :param parent_item: Root item instance
        """

        # create an item representing the current maya session
        # let parent collector do it own work
        super(ShotMediaReviewCollector, self).process_current_session(settings, parent_item)

        if not parent_item.type_spec.endswith(".session"):
            item = next(
                (i for i in parent_item.descendants if i.type_spec.endswith(".session")),
                parent_item,
            )
        else:
            item = parent_item
        # load overrides framework
        overrides_framework = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        )
        if not overrides_framework:
            overrides_framework = self.load_framework("mty-framework-valueoverrides")
        default_value = "mty.publisher.maya.collect_media_review"
        link = {"type": "Task", "id": self.parent.context.task["id"]}
        override_media_review = overrides_framework.get_value(default_value, link=link)

        if override_media_review:
            override_media_review = json.loads(override_media_review)

        # skip collect shot media review if task is on override list
        task = self.parent.context.task['name']
        if not override_media_review or task not in override_media_review:
            self._collect_shot_media_review(item)


    def _collect_shot_media_review(self, parent_item):
        """
        Creates item for review media to be generated.

        :param parent_item: Parent Item instance
        """

        # Collector must be only for Editorial Step
        review_item = parent_item.create_item(
            "maya.session.shot.review",
            "Shot Media Review",
            "Shot Media Review Publish"
        )

        self.parent.engine.execute_hook_expression(
            "{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py",
            "use_farm_or_local_processing",
            item = review_item
        )

        # get the icon path to display for this item
        icon_path = os.path.join(
            self.disk_location,
            os.pardir,
            "icons",
            "review.png"
        )
        review_item.set_icon_from_path(icon_path)
        review_item.properties["publish_type"] = "Media Review"

        return review_item
