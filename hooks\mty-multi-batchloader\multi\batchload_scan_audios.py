#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

import os
import sys
import json
import traceback
from tank import Hook
from html import entities
from pprint import pformat
from tank.platform.qt import QtCore, QtGui



class ScanSceneHook(Hook):

    def scan_scene(self):

        self.parent.engine.logger.info("")
        self.parent.engine.logger.info(
            "Scanning scene for Editorial Audios...".ljust(120, "-")
        )

        items_result = []
        warnings = []
        errors = []

        shot_id = self.parent.context.entity['id']

        filters = [['id', 'is', shot_id]]
        fields = ['assets']
        data_shot = self.parent.shotgun.find_one('Shot', filters, fields)

        audios_tasks_list = self.parent.get_setting("audios_tasks_list")

        # ------------------------------------------------------------------------------
        # Load value overrides framework
        valueoverrides = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        ) or self.load_framework("mty-framework-valueoverrides")

        if not valueoverrides:
            self.parent.engine.logger.error("Couldn't load value overrides framework")
            audios_tasks_list = self.parent.get_setting(
                "audios_tasks_list"
            )
            self.parent.engine.logger.info(
                "audios_tasks_list from hook settings: {}".format(
                    audios_tasks_list
                )
            )

        # Get task priority list
        if valueoverrides:
            default_value_code = "mty.multi.batchloader.scan.audios_priority_list"
            override_link = {"type": "Task", "id": self.parent.engine.context.task["id"]}
            audios_tasks_list = valueoverrides.get_value(
                default_value_code, link=override_link
            )
            if audios_tasks_list:
                audios_tasks_list = json.loads(audios_tasks_list)
                self.parent.engine.logger.info(
                    "audios_tasks_list from override: {}".format(
                        audios_tasks_list
                    )
                )

        published_file_type_names = ['Editorial Audio']


        filters = [
            ['entity.Shot.id', 'is', data_shot["id"]],
            ['task.Task.content', 'in', audios_tasks_list],
            ['published_file_type.PublishedFileType.code', 'in', published_file_type_names]
        ]

        fields = [
            "code",
            "name",
            "published_file_type",
            "version_number",
            "task",
            "path",
            'id',
            "entity",
        ]

        order = [{"field_name": "version_number", "direction": "desc"}]

        self.parent.logger.info("Retrieving audio from Shotgun...")

        try:

            self.parent.log_debug("Scanning scene to get the audio needed for the scene.")

            audio_search = self.parent.shotgun.find_one(
                "PublishedFile", filters, fields, order=order
            )

            if not audio_search:
                items_result.append({})
                return items_result, warnings, errors

            items_result = [{
                'node': audio_search['name'],
                'path': audio_search['path']['local_path'],
                'process_hook': 'batchload_process_audios',
                'type': 'Audio file',
                'sg_data': audio_search,
                'other_params': audio_search,
            }]


            # self.parent.logger.info("Audio: %s" % pformat(audio))

            # Ensure metasync framework is available, but only load it once.
            if not hasattr(self, 'metasync'):
                self.metasync = self.parent.engine.custom_frameworks.get(
                "mty-framework-metasync"
            )
            if not self.metasync:
                self.metasync = self.load_framework("mty-framework-metasync")

            transfersManager = self.metasync.transfersManager

            for aud in items_result:
                if "path" in aud and "sg_data" in aud:
                    if not os.path.exists(aud['path']):
                        self.parent.logger.warning(
                            "Audio not found in local storage: %s" % aud['path']
                        )
                        transfersManager.ensure_file_is_local(
                            aud['path'], aud['sg_data']
                        )
                    transfersManager.ensure_local_dependencies(aud.get('sg_data', {}))

        except:
            import traceback

            error_str = traceback.format_exc()
            errors.append(error_str)
            self.parent.log_debug(error_str)

        return items_result, warnings, errors
