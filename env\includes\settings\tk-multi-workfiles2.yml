# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

################################################################################

includes:
- ../app_locations.yml
- ./workfiles2_templates_per_engine.yml

################################################################################

# This configuration is used whenever a general file manager
# is needed. This configuration doesn't look for files,
# but only lets you switch to a valid work area. When a user
# switches (by selecting a task), the engine is restarted, and
# another environment is loaded in. This environment will
# have another file manager app running, with different settings,
# allowing files to be loaded and saved.
#
# References to this @workfiles2 include typically appears at
# 'transit' levels in the configuration, where work normally
# doesn't happen, but you just want the user to jump to their
# work area. The work area is typically a task or pipeline step
# and these 'transit' areas can be project, shot, or sequence level.

settings.tk-multi-workfiles2: &settings_tk-multi-workfiles2
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  entities:
  - caption: Assets
    entity_type: Asset
    hierarchy: [sg_asset_type, code]
    filters:
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Episodes
    entity_type: Episode
    filters: []
    hierarchy: [code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    entity_type: Sequence
    filters:
    hierarchy: [episode, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    entity_type: Shot
    filters:
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"

# launches at startup.
settings.tk-multi-workfiles2.launch_at_startup:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  launch_at_startup: true
  entities:
  - caption: Assets
    entity_type: Asset
    hierarchy: [sg_asset_type, code]
    filters:
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    entity_type: Sequence
    filters:
    hierarchy: [episode, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    entity_type: Shot
    filters:
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"

################################################################################

# ---- 3dsmax

settings.tk-multi-workfiles2.3dsmax:
  <<: *settings_tk-multi-workfiles2
  hook_scene_operation: "{engine}/tk-multi-workfiles2/basic/scene_operation.py"

settings.tk-multi-workfiles2.3dsmax.asset_step:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  hook_scene_operation: "{engine}/tk-multi-workfiles2/basic/scene_operation.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  template_publish: max_asset_publish
  template_publish_area: asset_publish_area_max
  template_work: max_asset_work
  template_work_area: asset_work_area_max
  saveas_default_name: master
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    filters:
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_asset_type, sg_main_parent, sg_environment_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    step_filter_on: Sequence
    entity_type: Sequence
    hierarchy: [code]
    filters:
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"

settings.tk-multi-workfiles2.3dsmax.shot_step:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  hook_scene_operation: "{engine}/tk-multi-workfiles2/basic/scene_operation.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  template_publish: max_shot_publish
  template_publish_area: shot_publish_area_max
  template_work: max_shot_work
  template_work_area: shot_work_area_max
  saveas_default_name: master
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    filters:
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_asset_type, sg_main_parent, sg_environment_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    step_filter_on: Sequence
    entity_type: Sequence
    hierarchy: [code]
    filters:
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"

################################################################################

# ---- hiero/nukestudio

settings.tk-multi-workfiles2.hiero:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py"
  entities:
  - caption: Projects
    entity_type: Project
    filters: []
    hierarchy: [name]
  show_my_tasks: false
  template_publish: hiero_project_publish
  template_publish_area: hiero_project_publish_area
  template_work: hiero_project_work
  template_work_area: hiero_project_work_area
  saveas_default_name: master
  location: "@apps.tk-multi-workfiles2.location"

################################################################################

# ---- Houdini

# asset_step
settings.tk-multi-workfiles2.houdini.asset_step:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  template_publish: houdini_asset_publish
  template_publish_area: asset_publish_area_houdini
  template_work: houdini_asset_work
  template_work_area: asset_work_area_houdini
  saveas_default_name: master
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    filters:
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_asset_type, sg_main_parent, sg_environment_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    step_filter_on: Sequence
    entity_type: Sequence
    hierarchy: [code]
    filters:
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"

# shot_step
settings.tk-multi-workfiles2.houdini.shot_step:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  template_publish: houdini_shot_publish
  template_publish_area: shot_publish_area_houdini
  template_work: houdini_shot_work
  template_work_area: shot_work_area_houdini
  saveas_default_name: master
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    filters:
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_asset_type, sg_main_parent, sg_environment_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    step_filter_on: Sequence
    entity_type: Sequence
    hierarchy: [code]
    filters:
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  templates_per_engine: "@settings.templates_per_engine"
  location: '@apps.tk-multi-workfiles2.location'

################################################################################

# ---- Maya

# launches at startup.
settings.tk-multi-workfiles2.maya.project:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  hook_scene_operation: "{self}/scene_operation_{engine_name}.py:{config}/tk-multi-workfiles2/scene_operation.py"
  launch_at_startup: true
  entities:
  - caption: Assets
    entity_type: Asset
    hierarchy: [sg_asset_type, code]
    filters:
     - [sg_asset_type, is_not, Environment]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    hierarchy: [sg_main_parent, sg_environment_type, code]
    filters:
     - [sg_asset_type, is, Environment]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    entity_type: Sequence
    filters:
    hierarchy: [episode, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"


# asset_step
settings.tk-multi-workfiles2.maya.asset_step:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  hook_scene_operation: "{self}/scene_operation_{engine_name}.py:{config}/tk-multi-workfiles2/scene_operation.py"
  template_publish: maya_asset_publish
  template_publish_area: asset_publish_area_maya
  template_work: maya_asset_work
  template_work_area: asset_work_area_maya
  saveas_default_name: master
  entities:
  - caption: Assets
    entity_type: Asset
    hierarchy: [sg_asset_type, code]
    filters:
     - [sg_asset_type, is_not, Environment]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    hierarchy: [sg_main_parent, sg_environment_type, code]
    filters:
     - [sg_asset_type, is, Environment]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    entity_type: Sequence
    filters:
    hierarchy: [episode, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"

# asset_step_notask
settings.tk-multi-workfiles2.maya.asset_step_notask:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  hook_scene_operation: "{self}/scene_operation_{engine_name}.py:{config}/tk-multi-workfiles2/scene_operation.py"
  template_publish: maya_asset_publish_notask
  template_publish_area: asset_publish_area_maya_notask
  template_work: maya_asset_work_notask
  template_work_area: asset_work_area_maya_notask
  saveas_default_name: master
  entities:
  - caption: Assets
    entity_type: Asset
    hierarchy: [sg_asset_type, code]
    filters:
     - [sg_asset_type, is_not, Environment]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    hierarchy: [sg_main_parent, sg_environment_type, code]
    filters:
     - [sg_asset_type, is, Environment]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    entity_type: Sequence
    filters:
    hierarchy: [episode, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  location: "@apps.tk-multi-workfiles2.location"


# environment_step
settings.tk-multi-workfiles2.maya.environment_step:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py"
  hook_scene_operation: "{self}/scene_operation_{engine_name}.py:{config}/tk-multi-workfiles2/scene_operation.py"
  template_publish: maya_environment_publish
  template_publish_area: environment_publish_area_maya
  template_work: maya_environment_work
  template_work_area: environment_work_area_maya
  saveas_default_name: master
  entities:
  - caption: Assets
    entity_type: Asset
    hierarchy: [sg_asset_type, code]
    filters:
     - [sg_asset_type, is_not, Environment]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    hierarchy: [sg_main_parent, sg_environment_type, code]
    filters:
     - [sg_asset_type, is, Environment]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    entity_type: Sequence
    filters:
    hierarchy: [episode, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  location: "@apps.tk-multi-workfiles2.location"

  # environment_step_notask
settings.tk-multi-workfiles2.maya.environment_step_notask:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py"
  hook_scene_operation: "{self}/scene_operation_{engine_name}.py:{config}/tk-multi-workfiles2/scene_operation.py"
  template_publish: maya_environment_publish_notask
  template_publish_area: environment_publish_area_maya_notask
  template_work: maya_environment_work_notask
  template_work_area: environment_work_area_maya_notask
  saveas_default_name: master
  entities:
  - caption: Assets
    entity_type: Asset
    hierarchy: [sg_asset_type, code]
    filters:
     - [sg_asset_type, is_not, Environment]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    hierarchy: [sg_main_parent, sg_environment_type, code]
    filters:
     - [sg_asset_type, is, Environment]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    entity_type: Sequence
    filters:
    hierarchy: [episode, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  location: "@apps.tk-multi-workfiles2.location"


# sequence_step
settings.tk-multi-workfiles2.maya.sequence_step:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py"
  hook_scene_operation: "{self}/scene_operation_{engine_name}.py:{config}/tk-multi-workfiles2/scene_operation.py"
  template_publish: maya_sequence_publish
  template_publish_area: sequence_publish_area_maya
  template_work: maya_sequence_work
  template_work_area: sequence_work_area_maya
  saveas_default_name: master
  entities:
  - caption: Assets
    entity_type: Asset
    hierarchy: [sg_asset_type, code]
    filters:
     - [sg_asset_type, is_not, Environment]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    hierarchy: [sg_main_parent, sg_environment_type, code]
    filters:
     - [sg_asset_type, is, Environment]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    entity_type: Sequence
    filters:
    hierarchy: [episode, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  location: '@apps.tk-multi-workfiles2.location'

# shot_step
settings.tk-multi-workfiles2.maya.shot_step:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  hook_scene_operation: "{self}/scene_operation_{engine_name}.py:{config}/tk-multi-workfiles2/scene_operation.py"
  template_publish: maya_shot_publish
  template_publish_area: shot_publish_area_maya
  template_work: maya_shot_work
  template_work_area: shot_work_area_maya
  saveas_default_name: master
  entities:
  - caption: Assets
    entity_type: Asset
    hierarchy: [sg_asset_type, code]
    filters:
     - [sg_asset_type, is_not, Environment]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    hierarchy: [sg_main_parent, sg_environment_type, code]
    filters:
     - [sg_asset_type, is, Environment]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    entity_type: Sequence
    filters:
    hierarchy: [episode, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  templates_per_engine: "@settings.templates_per_engine"
  location: '@apps.tk-multi-workfiles2.location'

################################################################################

# ---- nuke

# asset_step
settings.tk-multi-workfiles2.nuke.asset_step:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  template_publish: nuke_asset_publish
  template_publish_area: asset_publish_area_nuke
  template_work: nuke_asset_work
  template_work_area: asset_work_area_nuke
  saveas_default_name: master
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    filters:
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_asset_type, sg_main_parent, sg_environment_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    step_filter_on: Sequence
    entity_type: Sequence
    hierarchy: [code]
    filters:
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"

# shot_step
settings.tk-multi-workfiles2.nuke.shot_step:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  template_publish: nuke_shot_publish
  template_publish_area: shot_publish_area_nuke
  template_work: nuke_shot_work
  template_work_area: shot_work_area_nuke
  saveas_default_name: master
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    filters:
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_asset_type, sg_main_parent, sg_environment_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    step_filter_on: Sequence
    entity_type: Sequence
    hierarchy: [code]
    filters:
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"

################################################################################

# ---- photoshop
settings.tk-multi-workfiles2.photoshop.project:
  <<: *settings_tk-multi-workfiles2
  hook_scene_operation: "{config}/tk-multi-workfiles2/photoshopcc/scene_operation_tk-photoshopcc.py"

# episode_step
settings.tk-multi-workfiles2.photoshop.episode_step:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py"
  template_publish: photoshop_episode_publish
  template_publish_area: episode_publish_area_photoshop
  template_work: photoshop_episode_work
  template_work_area: episode_work_area_photoshop
  saveas_default_name: master
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    filters:
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_asset_type, sg_main_parent, sg_environment_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Episodes
    entity_type: Episode
    filters: []
    hierarchy: [code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    step_filter_on: Sequence
    entity_type: Sequence
    hierarchy: [code]
    filters:
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  location: "@apps.tk-multi-workfiles2.location"

# asset_step
settings.tk-multi-workfiles2.photoshop.asset_step:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  template_publish: photoshop_asset_publish
  template_publish_area: asset_publish_area_photoshop
  template_work: photoshop_asset_work
  template_work_area: asset_work_area_photoshop
  saveas_default_name: master
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    filters:
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_asset_type, sg_main_parent, sg_environment_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Episodes
    entity_type: Episode
    filters: []
    hierarchy: [code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    step_filter_on: Sequence
    entity_type: Sequence
    hierarchy: [code]
    filters:
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"

# asset_step_notask
settings.tk-multi-workfiles2.photoshop.asset_step_notask:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py"
  template_publish: photoshop_asset_publish_notask
  template_publish_area: asset_publish_area_photoshop_notask
  template_work: photoshop_asset_work_notask
  template_work_area: asset_work_area_photoshop_notask
  saveas_default_name: master
  entities:
  - caption: Assets
    entity_type: Asset
    hierarchy: [sg_asset_type, code]
    filters:
     - [sg_asset_type, is_not, Environment]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    hierarchy: [sg_main_parent, sg_environment_type, code]
    filters:
     - [sg_asset_type, is, Environment]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Episodes
    entity_type: Episode
    filters: []
    hierarchy: [code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    entity_type: Sequence
    filters:
    hierarchy: [episode, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  location: "@apps.tk-multi-workfiles2.location"


# environment_step
settings.tk-multi-workfiles2.photoshop.environment_step:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py"
  template_publish: photoshop_environment_publish
  template_publish_area: environment_publish_area_photoshop
  template_work: photoshop_environment_work
  template_work_area: environment_work_area_photoshop
  saveas_default_name: master
  entities:
  - caption: Assets
    entity_type: Asset
    hierarchy: [sg_asset_type, code]
    filters:
     - [sg_asset_type, is_not, Environment]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    hierarchy: [sg_main_parent, sg_environment_type, code]
    filters:
     - [sg_asset_type, is, Environment]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Episodes
    entity_type: Episode
    filters: []
    hierarchy: [code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    entity_type: Sequence
    filters:
    hierarchy: [episode, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  location: "@apps.tk-multi-workfiles2.location"

  # environment_step_notask
settings.tk-multi-workfiles2.photoshop.environment_step_notask:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py"
  template_publish: photoshop_environment_publish_notask
  template_publish_area: environment_publish_area_photoshop_notask
  template_work: photoshop_environment_work_notask
  template_work_area: environment_work_area_photoshop_notask
  saveas_default_name: master
  entities:
  - caption: Assets
    entity_type: Asset
    hierarchy: [sg_asset_type, code]
    filters:
     - [sg_asset_type, is_not, Environment]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    hierarchy: [sg_main_parent, sg_environment_type, code]
    filters:
     - [sg_asset_type, is, Environment]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Episodes
    entity_type: Episode
    filters: []
    hierarchy: [code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    entity_type: Sequence
    filters:
    hierarchy: [episode, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  location: "@apps.tk-multi-workfiles2.location"

# shot_step
settings.tk-multi-workfiles2.photoshop.shot_step:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  template_publish: photoshop_shot_publish
  template_publish_area: shot_publish_area_photoshop
  template_work: photoshop_shot_work
  template_work_area: shot_work_area_photoshop
  saveas_default_name: master
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    filters:
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_asset_type, sg_main_parent, sg_environment_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Episodes
    entity_type: Episode
    filters: []
    hierarchy: [code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    step_filter_on: Sequence
    entity_type: Sequence
    hierarchy: [code]
    filters:
    - [sg_status_list, is_not, na]
    sub_hierarchy:
      entity_type: Task
      filters:
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  hook_scene_operation: "{config}/tk-multi-workfiles2/photoshopcc/scene_operation_tk-photoshopcc.py"
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"

################################################################################

# ---- after effects

# project
settings.tk-multi-workfiles2.aftereffects:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    filters:
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_asset_type, sg_main_parent, sg_environment_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    step_filter_on: Sequence
    entity_type: Sequence
    hierarchy: [code]
    filters:
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  hook_scene_operation: "{engine}/tk-multi-workfiles2/basic/scene_operation.py"
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"

# asset_step
settings.tk-multi-workfiles2.aftereffects.asset_step:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  template_publish: aftereffects_asset_publish
  template_publish_area: asset_publish_area_aftereffects
  template_work: aftereffects_asset_work
  template_work_area: asset_work_area_aftereffects
  saveas_default_name: master
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    filters:
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_asset_type, sg_main_parent, sg_environment_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    step_filter_on: Sequence
    entity_type: Sequence
    hierarchy: [code]
    filters:
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  hook_scene_operation: "{engine}/tk-multi-workfiles2/basic/scene_operation.py"
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"

# shot_step
settings.tk-multi-workfiles2.aftereffects.shot_step:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  template_publish: aftereffects_shot_publish
  template_publish_area: shot_publish_area_aftereffects
  template_work: aftereffects_shot_work
  template_work_area: shot_work_area_aftereffects
  saveas_default_name: master
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    filters:
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_asset_type, sg_main_parent, sg_environment_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    step_filter_on: Sequence
    entity_type: Sequence
    hierarchy: [code]
    filters:
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  hook_scene_operation: "{engine}/tk-multi-workfiles2/basic/scene_operation.py"
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"

################################################################################

# ---- premiere

# project
settings.tk-multi-workfiles2.premiere:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py"
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    filters:
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_asset_type, sg_main_parent, sg_environment_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    step_filter_on: Sequence
    entity_type: Sequence
    hierarchy: [code]
    filters:
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  hook_scene_operation: "{engine}/tk-multi-workfiles2/basic/scene_operation.py"
  location: "@apps.tk-multi-workfiles2.location"

# episode
settings.tk-multi-workfiles2.premiere.episode_step:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py"
  template_publish: premiere_episode_publish
  template_publish_area: episode_publish_area_premiere
  template_work: premiere_episode_work
  template_work_area: episode_work_area_premiere
  saveas_default_name: master
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    filters:
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_asset_type, sg_main_parent, sg_environment_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    step_filter_on: Sequence
    entity_type: Sequence
    hierarchy: [code]
    filters:
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  hook_scene_operation: "{engine}/tk-multi-workfiles2/basic/scene_operation.py"
  location: "@apps.tk-multi-workfiles2.location"

# sequence_step
settings.tk-multi-workfiles2.premiere.sequence_step:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py"
  template_publish: premiere_sequence_publish
  template_publish_area: sequence_publish_area_premiere
  template_work: premiere_sequence_work
  template_work_area: sequence_work_area_premiere
  saveas_default_name: master
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    filters:
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_asset_type, sg_main_parent, sg_environment_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    step_filter_on: Sequence
    entity_type: Sequence
    hierarchy: [code]
    filters:
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  hook_scene_operation: "{engine}/tk-multi-workfiles2/basic/scene_operation.py"
  location: "@apps.tk-multi-workfiles2.location"

################################################################################

# ---- motion builder

settings.tk-multi-workfiles2.motionbuilder.asset_step:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  template_publish: mobu_asset_publish
  template_publish_area: asset_publish_area_mobu
  template_work: mobu_asset_work
  template_work_area: asset_work_area_mobu
  saveas_default_name: master
  entities:
  - caption: Assets
    entity_type: Asset
    hierarchy: [sg_asset_type, code]
    filters:
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"

settings.tk-multi-workfiles2.motionbuilder.shot_step:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  template_publish: mobu_shot_publish
  template_publish_area: shot_publish_area_mobu
  template_work: mobu_shot_work
  template_work_area: shot_work_area_mobu
  saveas_default_name: master
  entities:
  - caption: Assets
    entity_type: Asset
    hierarchy: [sg_asset_type, code]
    filters:
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"

################################################################################

# ---- alias

# project
settings.tk-multi-workfiles2.alias:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py"
  entities:
  - caption: Assets
    entity_type: Asset
    hierarchy: [sg_asset_type, code]
    filters:
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  my_tasks_filters:
    - filter_operator: any
      filters:
        - [task_assignees.Group.users, is, '{context.user}']
        - [task_assignees, is, '{context.user}']
  custom_actions_hook: "{engine}/tk-multi-workfiles2/basic/custom_actions.py"
  hook_scene_operation: "{engine}/tk-multi-workfiles2/basic/scene_operation.py"
  location: "@apps.tk-multi-workfiles2.location"

# asset_step
settings.tk-multi-workfiles2.alias.asset_step:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py"
  template_publish: alias_asset_publish
  template_publish_area: asset_publish_area_alias
  template_work: alias_asset_work
  template_work_area: asset_work_area_alias
  saveas_default_name: master
  entities:
  - caption: Assets
    entity_type: Asset
    hierarchy: [sg_asset_type, code]
    filters:
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  my_tasks_filters:
    - filter_operator: any
      filters:
        - [task_assignees.Group.users, is, '{context.user}']
        - [task_assignees, is, '{context.user}']
  custom_actions_hook: "{engine}/tk-multi-workfiles2/basic/custom_actions.py"
  hook_scene_operation: "{engine}/tk-multi-workfiles2/basic/scene_operation.py"
  location: "@apps.tk-multi-workfiles2.location"

################################################################################

# ---- VRED

# project
settings.tk-multi-workfiles2.vred:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py"
  entities:
  - caption: Assets
    entity_type: Asset
    hierarchy: [sg_asset_type, code]
    filters:
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  my_tasks_filters:
    - filter_operator: any
      filters:
        - [task_assignees.Group.users, is, '{context.user}']
        - [task_assignees, is, '{context.user}']
  hook_scene_operation: "{engine}/tk-multi-workfiles2/basic/scene_operation.py"
  location: "@apps.tk-multi-workfiles2.location"

# asset_step
settings.tk-multi-workfiles2.vred.asset_step:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  template_publish: vred_asset_publish
  template_publish_area: asset_publish_area_vred
  template_work: vred_asset_work
  template_work_area: asset_work_area_vred
  saveas_default_name: master
  entities:
  - caption: Assets
    entity_type: Asset
    hierarchy: [sg_asset_type, code]
    filters:
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  my_tasks_filters:
    - filter_operator: any
      filters:
        - [task_assignees.Group.users, is, '{context.user}']
        - [task_assignees, is, '{context.user}']
  hook_scene_operation: "{engine}/tk-multi-workfiles2/basic/scene_operation.py"
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"

################################################################################

# -- harmony

settings.tk-multi-workfiles2.harmony.project:
  allow_task_creation: true
  create_new_task_hook: "{self}/create_new_task.py"
  custom_actions_hook: "{self}/custom_actions.py:{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [sg_asset_type, is_not, Environment]
    - ["sg_status_list", "is_not", "na"]
    - ["sg_status_list", "is_not", "omt"]
    hierarchy: [sg_asset_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    filters:
    - [sg_asset_type, is, Environment]
    - ["sg_status_list", "is_not", "na"]
    - ["sg_status_list", "is_not", "omt"]
    hierarchy: [sg_asset_type, sg_main_parent, sg_environment_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    step_filter_on: Sequence
    entity_type: Sequence
    hierarchy: [code]
    filters:
      - ["sg_status_list", "is_not", "na"]
      - ["sg_status_list", "is_not", "omt"]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    - ["sg_status_list", "is_not", "na"]
    - ["sg_status_list", "is_not", "omt"]
    sub_hierarchy:
      entity_type: Task
      filters:
      - ["sg_status_list", "is_not", "na"]
      - ["sg_status_list", "is_not", "omt"]
      link_field: entity
      hierarchy: [step]
  file_extensions: []
  hook_copy_file: "{engine}/tk-multi-workfiles2/copy_file.py"
  hook_scene_operation: "{engine}/tk-multi-workfiles2/scene_operation_tk-harmony.py:{config}/tk-multi-workfiles2/tk-harmony_scene_operation.py"
  launch_at_startup: false
  my_tasks_extra_display_fields: []
  saveas_default_name: master
  saveas_prefer_version_up: false
  show_my_tasks: true
  template_publish:
  template_publish_area:
  template_work:
  template_work_area:
  version_compare_ignore_fields: []
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"


# asset_step
settings.tk-multi-workfiles2.harmony.asset_step:
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    filters:
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_asset_type, sg_main_parent, sg_environment_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    step_filter_on: Sequence
    entity_type: Sequence
    hierarchy: [code]
    filters:
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  hook_scene_operation: "{engine}/tk-multi-workfiles2/scene_operation_tk-harmony.py:{config}/tk-multi-workfiles2/tk-harmony_scene_operation.py"
  hook_copy_file: "{engine}/tk-multi-workfiles2/copy_file.py"
  custom_actions_hook: "{self}/custom_actions.py:{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  saveas_default_name: master
  template_publish: harmony_asset_publish
  template_publish_area: asset_publish_area_harmony
  template_work: harmony_asset_work
  template_work_area: asset_work_area_harmony
  # here you can specify your own template project for when "new file" is
  # pressed in the workfiles interface. Make sure you name the project "template"
  # template_project_folder: "C:/pipeline/config/templates/harmony/newfile/template.xstage"
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"


# shot_step
settings.tk-multi-workfiles2.harmony.shot_step:
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    filters:
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_asset_type, sg_main_parent, sg_environment_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    step_filter_on: Sequence
    entity_type: Sequence
    hierarchy: [code]
    filters:
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  hook_scene_operation: "{engine}/tk-multi-workfiles2/scene_operation_tk-harmony.py:{config}/tk-multi-workfiles2/tk-harmony_scene_operation.py"
  hook_copy_file: "{engine}/tk-multi-workfiles2/copy_file.py"
  custom_actions_hook: "{self}/custom_actions.py:{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"

  saveas_default_name: master
  template_publish: harmony_shot_publish
  template_publish_area: shot_publish_area_harmony
  template_work: harmony_shot_work
  template_work_area: shot_work_area_harmony
  # here you can specify your own template project for when "new file" is
  # pressed in the workfiles interface. Make sure you name the project "template"
  # template_project_folder: "C:/pipeline/config/templates/harmony/newfile/template.xstage"
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"


# sequence_step
settings.tk-multi-workfiles2.harmony.sequence_step:
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    filters:
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_asset_type, sg_main_parent, sg_environment_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    step_filter_on: Sequence
    entity_type: Sequence
    hierarchy: [code]
    filters:
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  hook_scene_operation: "{engine}/tk-multi-workfiles2/scene_operation_tk-harmony.py:{config}/tk-multi-workfiles2/tk-harmony_scene_operation.py"
  hook_copy_file: "{engine}/tk-multi-workfiles2/copy_file.py"
  custom_actions_hook: "{self}/custom_actions.py:{config}/tk-multi-workfiles2/custom_actions.py"
  saveas_default_name: master
  template_publish: harmony_sequence_publish
  template_publish_area: sequence_publish_area_harmony
  template_work: harmony_sequence_work
  template_work_area: sequence_work_area_harmony
  # here you can specify your own template project for when "new file" is
  # pressed in the workfiles interface. Make sure you name the project "template"
  # template_project_folder: "C:/pipeline/config/templates/harmony/newfile/template.xstage"
  location: "@apps.tk-multi-workfiles2.location"


################################################################################

# -- blender

settings.tk-multi-workfiles2.blender.project:
  allow_task_creation: true
  create_new_task_hook: "{self}/create_new_task.py"
  custom_actions_hook: "{self}/custom_actions.py"
  entities:
  - caption: Assets
    entity_type: Task
    filters:
    - [entity, type_is, Asset]
    hierarchy: [entity.Asset.sg_asset_type, entity, step, content]
  - caption: Shots
    entity_type: Task
    filters:
    - [entity, type_is, Shot]
    hierarchy: [entity.Shot.sg_sequence, entity, step, content]
  file_extensions: []
  hook_copy_file: "{self}/copy_file.py"
  hook_filter_publishes: default
  hook_filter_work_files: default
  hook_scene_operation: "{engine}/tk-multi-workfiles2/scene_operation_tk-blender.py"
  launch_at_startup: false
  my_tasks_extra_display_fields: []
  saveas_default_name: scene
  saveas_prefer_version_up: false
  show_my_tasks: true
  template_publish:
  template_publish_area:
  template_work:
  template_work_area:
  version_compare_ignore_fields: []
  location: "@apps.tk-multi-workfiles2.location"

# episode_step
settings.tk-multi-workfiles2.blender.episode_step:
  hook_scene_operation: "{engine}/tk-multi-workfiles2/scene_operation_tk-blender.py"
  template_publish: blender_episode_publish
  template_publish_area: episode_publish_area_blender
  template_work: blender_episode_work
  template_work_area: episode_work_area_blender
  location: "@apps.tk-multi-workfiles2.location"

# asset_step
settings.tk-multi-workfiles2.blender.asset_step:
  hook_scene_operation: "{engine}/tk-multi-workfiles2/scene_operation_tk-blender.py"
  template_publish: blender_asset_publish
  template_publish_area: asset_publish_area_blender
  template_work: blender_asset_work
  template_work_area: asset_work_area_blender
  location: "@apps.tk-multi-workfiles2.location"

# asset_step_notask
settings.tk-multi-workfiles2.blender.asset_step_notask:
  hook_scene_operation: "{engine}/tk-multi-workfiles2/scene_operation_tk-blender.py"
  template_publish: blender_asset_publish_notask
  template_publish_area: asset_publish_area_blender_notask
  template_work: blender_asset_work_notask
  template_work_area: asset_work_area_blender
  location: "@apps.tk-multi-workfiles2.location"

# environment_step
settings.tk-multi-workfiles2.blender.environment_step:
  hook_scene_operation: "{engine}/tk-multi-workfiles2/scene_operation_tk-blender.py"
  template_publish: blender_environment_publish
  template_publish_area: environment_publish_area_blender
  template_work: blender_environment_work
  template_work_area: environment_work_area_blender
  location: "@apps.tk-multi-workfiles2.location"

# environment_step_notask
settings.tk-multi-workfiles2.blender.environment_step_notask:
  hook_scene_operation: "{engine}/tk-multi-workfiles2/scene_operation_tk-blender.py"
  template_publish: blender_environment_publish_notask
  template_publish_area: environment_publish_area_blender_notask
  template_work: blender_environment_work_notask
  template_work_area: environment_work_area_blender_notask
  location: "@apps.tk-multi-workfiles2.location"

# sequence_step
settings.tk-multi-workfiles2.blender.sequence_step:
  hook_scene_operation: "{engine}/tk-multi-workfiles2/scene_operation_tk-blender.py"
  template_publish: blender_sequence_publish
  template_publish_area: sequence_publish_area_blender
  template_work: blender_sequence_work
  template_work_area: sequence_work_area_blender
  location: "@apps.tk-multi-workfiles2.location"

# shot_step
settings.tk-multi-workfiles2.blender.shot_step:
  hook_scene_operation: "{engine}/tk-multi-workfiles2/scene_operation_tk-blender.py"
  template_publish: blender_shot_publish
  template_publish_area: shot_publish_area_blender
  template_work: blender_shot_work
  template_work_area: shot_work_area_blender
  location: "@apps.tk-multi-workfiles2.location"

################################################################################

# -- fusion

settings.tk-multi-workfiles2.fusion.project:
  allow_task_creation: true
  create_new_task_hook: "{self}/create_new_task.py"
  custom_actions_hook: "{self}/custom_actions.py"
  entities: &fusion-entities
  - caption: Assets
    entity_type: Asset
    filters:
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    filters:
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_asset_type, sg_main_parent, sg_environment_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    step_filter_on: Sequence
    entity_type: Sequence
    hierarchy: [code]
    filters:
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  file_extensions: []
  hook_copy_file: "{self}/copy_file.py"
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  #hook_scene_operation: "{engine}/tk-multi-workfiles2/scene_operation_tk-fusion.py"
  hook_scene_operation: "{engine}/tk-multi-workfiles2/scene_operation_tk-fusion.py:{config}/tk-multi-workfiles2/fusion/fusion_extend_scene_operation.py"
  launch_at_startup: false
  my_tasks_extra_display_fields: []
  saveas_default_name: scene
  saveas_prefer_version_up: false
  show_my_tasks: true
  template_publish:
  template_publish_area:
  template_work:
  template_work_area:
  version_compare_ignore_fields: []
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"

# asset_step
settings.tk-multi-workfiles2.fusion.asset_step:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  #hook_scene_operation: "{engine}/tk-multi-workfiles2/scene_operation_tk-fusion.py"
  hook_scene_operation: "{engine}/tk-multi-workfiles2/scene_operation_tk-fusion.py:{config}/tk-multi-workfiles2/fusion/fusion_extend_scene_operation.py"
  entities: *fusion-entities
  template_publish: fusion_asset_publish
  template_publish_area: asset_publish_area_fusion
  template_work: fusion_asset_work
  template_work_area: asset_work_area_fusion
  saveas_default_name: scene
  saveas_prefer_version_up: false
  version_compare_ignore_fields: []
  file_extensions: []
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"

# shot_step
settings.tk-multi-workfiles2.fusion.shot_step:
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  #hook_scene_operation: "{engine}/tk-multi-workfiles2/scene_operation_tk-fusion.py"
  hook_scene_operation: "{engine}/tk-multi-workfiles2/scene_operation_tk-fusion.py:{config}/tk-multi-workfiles2/fusion/fusion_extend_scene_operation.py"
  entities: *fusion-entities
  template_publish: fusion_shot_publish
  template_publish_area: shot_publish_area_fusion
  template_work: fusion_shot_work
  template_work_area: shot_work_area_fusion
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"


################################################################################

# -- krita

settings.tk-multi-workfiles2.krita.project:
  allow_task_creation: true
  create_new_task_hook: "{self}/create_new_task.py"
  hook_filter_publishes: "{config}/tk-multi-workfiles2/filter_publishes.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Environments
    entity_type: Asset
    filters:
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_asset_type, sg_main_parent, sg_environment_type, code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Episodes
    entity_type: Episode
    filters: []
    hierarchy: [code]
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Sequences
    step_filter_on: Sequence
    entity_type: Sequence
    hierarchy: [code]
    filters:
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  - caption: Shots
    step_filter_on: Shot
    entity_type: Shot
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
    filters:
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    sub_hierarchy:
      entity_type: Task
      filters:
      - [sg_status_list, is_not, na]
      link_field: entity
      hierarchy: [step]
  file_extensions: []
  hook_copy_file: "{self}/copy_file.py"
  hook_filter_work_files: default
  hook_scene_operation: "{engine}/tk-multi-workfiles2/scene_operation_tk-krita.py"
  launch_at_startup: false
  my_tasks_extra_display_fields: []
  saveas_default_name: scene
  saveas_prefer_version_up: false
  show_my_tasks: true
  template_publish:
  template_publish_area:
  template_work:
  template_work_area:
  version_compare_ignore_fields: []
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"


# asset_step
settings.tk-multi-workfiles2.krita.asset_step:
  hook_scene_operation: "{engine}/tk-multi-workfiles2/scene_operation_tk-krita.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  template_publish: krita_asset_publish
  template_publish_area: asset_publish_area_krita
  template_work: krita_asset_work
  template_work_area: asset_work_area_krita
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"

# environment_step
settings.tk-multi-workfiles2.krita.environment_step:
  hook_scene_operation: "{engine}/tk-multi-workfiles2/scene_operation_tk-krita.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  template_publish: krita_environment_publish
  template_publish_area: environment_publish_area_krita
  template_work: krita_environment_work
  template_work_area: environment_work_area_krita
  location: "@apps.tk-multi-workfiles2.location"

# environment_step_notask
settings.tk-multi-workfiles2.krita.environment_step_notask:
  hook_scene_operation: "{engine}/tk-multi-workfiles2/scene_operation_tk-krita.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  template_publish: krita_environment_publish_notask
  template_publish_area: environment_publish_area_krita_notask
  template_work: krita_environment_work_notask
  template_work_area: environment_work_area_krita_notask
  location: "@apps.tk-multi-workfiles2.location"

# shot_step
settings.tk-multi-workfiles2.krita.shot_step:
  hook_scene_operation: "{engine}/tk-multi-workfiles2/scene_operation_tk-krita.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  template_publish: krita_shot_publish
  template_publish_area: shot_publish_area_krita
  template_work: krita_shot_work
  template_work_area: shot_work_area_krita
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"


# sequence_step
settings.tk-multi-workfiles2.krita.sequence_step:
  hook_scene_operation: "{engine}/tk-multi-workfiles2/scene_operation_tk-krita.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  template_publish: krita_sequence_publish
  template_publish_area: sequence_publish_area_krita
  template_work: krita_sequence_work
  template_work_area: sequence_work_area_krita
  location: "@apps.tk-multi-workfiles2.location"

# episode_step
settings.tk-multi-workfiles2.krita.episode_step:
  hook_scene_operation: "{engine}/tk-multi-workfiles2/scene_operation_tk-krita.py"
  custom_actions_hook: "{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  template_publish: krita_episode_publish
  template_publish_area: episode_publish_area_krita
  template_work: krita_episode_work
  template_work_area: episode_work_area_krita
  location: "@apps.tk-multi-workfiles2.location"


################################################################################

# -- substancepainter

settings.tk-multi-workfiles2.substancepainter.project:
  allow_task_creation: true
  create_new_task_hook: "{self}/create_new_task.py"
  custom_actions_hook: "{self}/custom_actions.py:{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  entities:
  - caption: Assets
    entity_type: Task
    filters:
    - [entity, type_is, Asset]
    - [sg_status_list, is_not, na]
    hierarchy: [entity.Asset.sg_asset_type, entity, step, content]
  - caption: Shots
    entity_type: Task
    filters:
    - [entity, type_is, Shot]
    - [sg_status_list, is_not, na]
    hierarchy: [entity.Shot.sg_sequence, entity, step, content]
  file_extensions: []
  hook_copy_file: "{self}/copy_file.py"
  hook_filter_publishes: default
  hook_filter_work_files: default
  hook_scene_operation: "{engine}/tk-multi-workfiles2/scene_operation_tk-substancepainter.py"
  launch_at_startup: false
  my_tasks_extra_display_fields: []
  saveas_default_name: master
  saveas_prefer_version_up: false
  show_my_tasks: true
  template_publish:
  template_publish_area:
  template_work:
  template_work_area:
  version_compare_ignore_fields: []
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"

# asset_step
settings.tk-multi-workfiles2.substancepainter.asset_step:
  hook_scene_operation: "{engine}/tk-multi-workfiles2/scene_operation_tk-substancepainter.py"
  custom_actions_hook: "{self}/custom_actions.py:{config}/tk-multi-workfiles2/custom_actions.py:{config}/tk-multi-workfiles2/save_workfile_change_context.py"
  saveas_default_name: master
  template_publish: substancepainter_asset_publish
  template_publish_area: asset_publish_area_substancepainter
  template_work: substancepainter_asset_work
  template_work_area: asset_work_area_substancepainter
  templates_per_engine: "@settings.templates_per_engine"
  location: "@apps.tk-multi-workfiles2.location"
