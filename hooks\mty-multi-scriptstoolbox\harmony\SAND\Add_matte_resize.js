include("harmony_utility_functions.js");

var log = MessageLog.trace;

NODE_TO_FIX = {
    "MELLO_G": ["CA_16"]
}

function get_src_index(startingNode, src_node){
  var numOutputPorts = node.numberOfInputPorts(startingNode);
    for(var i = 0; i<numOutputPorts; i++){
        var linkIdx = i;
        var source = node.srcNode(startingNode, (linkIdx));
        if (source == src_node){
            return linkIdx
        };
    };
};

function get_dst_index(startingNode, dst_node) {
    var numOutputPorts = node.numberOfOutputPorts(startingNode);
    for(var i = 0; i<numOutputPorts; i++){
        var portIdx = i;
        var dst = node.dstNode(startingNode, portIdx, 0);
        if(dst_node == dst) {
            return portIdx
        };
    };
};

function add_matte_resize() {
    var all_groups = get_all_group_nodes_in_scene("Top", []);
    var node_to_fix = []
    var attrName = "FixByPipeline"
    var myColor = new ColorRGBA(190, 190, 190, 255);

    for (var i = 0; i < all_groups.length; ++i) {
        var current_node = all_groups[i]
        var name = node.getName(current_node);
        for (var key in NODE_TO_FIX) {
            if (NODE_TO_FIX.hasOwnProperty(key)) {
                if (name.substring(0, key.length) === key){
                    for (var j = 0; j < NODE_TO_FIX[key].length; ++j) {
                        node_to_fix.push(current_node + "/" + NODE_TO_FIX[key][j]);
                    };
                };
            };
        };
    };
    log (node_to_fix);
    var fixed_nodes = []
    for (var i = 0; i < node_to_fix.length; ++i) {
        var current_node = node_to_fix[i]
        var dst_nd = node.dstNode(current_node, 0, 0);
        var attr = node.getAttr(dst_nd, frame.current(), attrName);
        if (attr.keyword() == "") {
            var src_index = get_src_index(dst_nd, current_node);
            var dst_index = get_dst_index(current_node, dst_nd);
            node.unlink(dst_nd, src_index);
            var new_node = create_node("MATTE_RESIZE", current_node, myColor, "Matte_resize_fix", 0, 100, current_node);
            node.link(new_node, dst_index, dst_nd, src_index);
            node.setTextAttr(new_node, "RADIUS", frame.current(), 0.1);

            fixed_nodes.push(current_node);
            create_attr(new_node, attrName, "BOOL");
        };
    };
    if (fixed_nodes.length == 0) {
        var message = "the following nodes were already fixed:\n" + JSON.stringify(node_to_fix, null, 4);

        var return_dict = {
            "successful_execution": false,
            "message": message
        };
        log(message);
        return return_dict
    }else{
        var message = "the following nodes were fixed:\n" + JSON.stringify(fixed_nodes, null, 4);
        var return_dict = {
            "successful_execution": true,
            "message": message
        }
        log(message);
        return return_dict
    };

};


add_matte_resize();