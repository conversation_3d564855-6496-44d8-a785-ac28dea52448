# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

import os
import sgtk
import maya.cmds as cmds
import maya.mel as mel
import re
import traceback
from sgtk.platform.qt import QtGui, QtCore

HookBaseClass = sgtk.get_hook_baseclass()

class MayaSceneCamerasPublishPlugin(HookBaseClass):
    """
    Plugin for publish Scene playblast Publish

    """

    @property
    def icon(self):
        """
        Path to an png icon on disk
        """

        # look for icon one level up from this hook's folder in "icons" folder
        return os.path.join(
            self.disk_location,
            os.pardir,
            "icons",
            "camera_publish.png"
        )

    @property
    def name(self):
        """
        One line display name describing the plugin
        """
        return "Publish Sequence Cameras"

    @property
    def description(self):
        """
        Verbose, multi-line description of what the plugin does. This can
        contain simple html for formatting.
        """

        loader_url = "https://support.shotgunsoftware.com/hc/en-us/articles/219033078"

        return """
        <p>This plugin publish a alembic of fbx File based on settings descriptions, it will fetch for the asociated
        cameras to a shot in the sequencer but also contained in the main camera group.</p>

        """

    @property
    def settings(self):
        """
        Dictionary defining the settings that this plugin expects to recieve
        through the settings parameter in the accept, validate, publish and
        finalize methods.

        A dictionary on the following form::

            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }

        The type string should be one of the data types that toolkit accepts
        as part of its environment configuration.
        """

        # settings specific to this class
        maya_publish_settings = {
            "Work Template": {
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "Primary Publish Template": {
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file for publish path. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },

            "Publish Template": {
                "type": "template",
                "default": None,
                "description": "Template path for published Scene FBX file. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "Publish type": {
                "type": "string",
                "default": "Scene Cameras FBX",
                "description": "The published file type to register.",
            },
            "Cameras group": {
                "type": "string",
                "default": 'Cameras',
                "description": "The root group name where al cameras to be contained.",
            },
            "Export type": {
                "type": "string",
                "default": "alembic",
                "description": "The export type format.",
            },
            "Shot Prefix": {
                "type": "string",
                "default": "Shot_",
                "description": "The string or dynamic prefix to use to validate shot naming conventions."
                               "It can be a plain string or a tokenized one, which use a system of keys,"
                               "Those keys match items from the context, supported tokens are as follow:"
                               "{context.project} which matchs: context.project['name']"
                               "{context.entity} which matchs: context.entity['name']"
                               "{context.step} which matchs: context.step['name']"
                               "{context.task} which matchs: context.task['name']",
            },
            "Shot Digits": {
                "type": "int",
                "default": 3,
                "description": "The valid amount of digits in the shot name after the shot prefix.",
            },
        }

        # inherit the settings from the base publish plugin
        plugin_settings = super(MayaSceneCamerasPublishPlugin, self).settings or {}

        # update the base settings
        plugin_settings.update(maya_publish_settings)

        return plugin_settings

    @property
    def item_filters(self):
        """
        List of item types that this plugin is interested in.

        Only items matching entries in this list will be presented to the
        accept() method. Strings can contain glob patters such as *, for example
        ["maya.*", "file.maya"]
        """
        return ["maya.session.camerasgeo"]

    def accept(self, settings, item):
        """
        Method called by the publisher to determine if an item is of any
        interest to this plugin. Only items matching the filters defined via the
        item_filters property will be presented to this method.

        A submit for review task will be generated for each item accepted here. Returns a
        dictionary with the following booleans:

            - accepted: Indicates if the plugin is interested in this value at
                all. Required.
            - enabled: If True, the plugin will be enabled in the UI, otherwise
                it will be disabled. Optional, True by default.
            - visible: If True, the plugin will be visible in the UI, otherwise
                it will be hidden. Optional, True by default.
            - checked: If True, the plugin will be checked in the UI, otherwise
                it will be unchecked. Optional, True by default.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process

        :returns: dictionary with boolean keys accepted, required and enabled.
            This plugin makes use of the tk-multi-reviewsubmission app; if this
            app is not available then the item will not be accepted, this method
            will return a dictionary with the accepted field that has a value of
            False.

            Several properties on the item must also be present for it to be
            accepted. The properties are 'path', 'publish_name', 'color_space',
            'first_frame' and 'last_frame'
        """

        # Env variable must be declared with content to acept the pluginn
        if os.environ.get('SEQUENCE_SPLIT_PUBLISH'):
            return {
                "accepted": True,
                "checked": True
            }
        else:
            return {"accepted": False}

    def __resolve_shot_prefix(self, shot_prefix):
        """
        From a given tokenized string, return a computed string
        This computed string will be used to match the shot names
        The tokenized string is "magick" concept to allow for special
        keys taken from the context, supported tokens are as follow:
        {context.project} = context.project['name']
        {context.entity} = context.entity['name']
        {context.step} = context.step['name']
        {context.task} = context.task['name']
        """

        context = self.parent.context

        known_values = {}
        context_dict = context.to_dict()

        self.parent.log_debug('Context as dict: {0}'.format(context_dict))

        for item, value in iter(context_dict.items()):

            self.parent.log_debug('item: {0}'.format(item))
            self.parent.log_debug('value: {0}'.format(value))

            if item == 'additional_entities':
                continue

            if item:
                known_values[item] = value.get('name', 'unknown')
            else:
                known_values[item] = 'unknown'

        known_tokens = {'{context.project}': known_values['project'],
                        '{context.entity}': known_values['entity'],
                        '{context.step}': known_values['step'],
                        '{context.task}': known_values['task']}

        if '{' in shot_prefix:
            for token in known_tokens:
                value = known_tokens[token]
                shot_prefix = shot_prefix.replace(token, value)

        return shot_prefix

    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish. Returns a
        boolean to indicate validity.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        :returns: True if item is valid and not in proxy mode, False otherwise.
        """
        coremayatools = self.load_framework("mty-framework-coremayatools")

        # Get the cameras group node name from plugin settings
        cams_group_name = settings["Cameras group"].value
        self.MayaCamerasUtils = coremayatools.MayaCamerasUtils
        cameras_grp = self.MayaCamerasUtils._validate_cameras_root(cmds, cams_group_name)

        item.properties['cameras_main_group'] = cameras_grp

        shot_prefix = self.__resolve_shot_prefix(settings['Shot Prefix'].value)
        shot_digits = settings['Shot Digits'].value

        errors = []

        if cameras_grp == None:
            error_msg = "Warning!!: A root group with name: {},  is not present in the scene!!".format(cams_group_name)
            errors.append(error_msg)
        else:
            cameras_errors, cameras_list = self.MayaCamerasUtils.validate_item_for_cameras(cameras_grp, shot_prefix, shot_digits, cmds, re)

            if cameras_errors:
                errors.extend(cameras_errors)

            if not cameras_list:
                error_msg = "Warning!!: There is no cameras in {} group, item must be published at least with one camera.!!".format(cameras_grp)
                errors.append(error_msg)
            else:
                item.properties['cameras_shapes'] = cameras_list

        if errors:
            self.logger.error(errors)
            raise Exception(errors)

        return True

    def select_data_from_settings(self, settings):
        """
        Selects the required data from settings to complete the template building on publish.
        """
        cams_format_export = settings["Export type"].value
        if cams_format_export == 'alembic':
            export_extension =  'abc'

        elif cams_format_export == 'fbx':
            export_extension =  'fbx'

        else:
            export_extension =  'abc'

        publish_type = '{} {}'.format(settings["Publish type"].value, export_extension)

        return export_extension, publish_type, cams_format_export


    def export_to_format(self, format_exp, node_group, publish_dest, sg_logger):
        if format_exp == 'alembic':
            result = self.MayaCamerasUtils.export_to_abc(node_group, publish_dest, sg_logger, cmds)
        elif format_exp == 'fbx':
            result = self.MayaCamerasUtils.export_to_fbx(publish_dest, sg_logger, mel)

        if not result:
            raise Exception('Exporting to {} file failed.'.format(format_exp))


    def publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        cam_node_name = item.properties.get('cameras_main_group')
        # Select the cameras shapes to be exported
        cameras_list = item.properties.get('cameras_shapes')
        cmds.select(cl=True)
        for cam in cameras_list:
            cmds.select(cam, add=True)


        # Get the current format to be exported from settings
        export_extension, tank_type, format_export = self.select_data_from_settings(settings)

        scene_path = os.path.abspath(cmds.file(query=True, sn=True))
        work_template_name = settings["Work Template"].value
        work_template = self.parent.engine.get_template_by_name(work_template_name)
        fields = work_template.get_fields(scene_path)
        publish_version = fields["version"]

        # create the publish path by applying the fields
        # with the publish template:
        publish_template_name = settings["Publish Template"].value
        publish_template = self.parent.engine.get_template_by_name(publish_template_name)
        fields["geo_extension"] = export_extension
        publish_path = publish_template.apply_fields(fields)

        # ensure the publish folder exists:
        publish_folder = os.path.dirname(publish_path)
        self.parent.ensure_folder_exists(publish_folder)

        # determine the publish name:
        publish_name = fields.get("name")
        if not publish_name:
            publish_name = os.path.basename(publish_path)

        scene_pub_template_name = settings["Primary Publish Template"].value
        scene_pub_template = self.parent.engine.get_template_by_name(scene_pub_template_name)
        primary_publish_path = scene_pub_template.apply_fields(fields)

        # Export cameras with vales added of plugin settings
        self.export_to_format(format_export, cam_node_name, publish_path, self.parent.log_debug)

        sg_fields_to_update = {
            "sg_status_list": "rev",
            "task.Task.sg_status_list": "rev",
        }

        args = {
            "tk": self.parent.engine.sgtk,
            "context": item.context,
            "comment": item.description,
            "path": publish_path,
            "name": publish_name,
            "version_number": publish_version,
            "thumbnail_path": item.get_thumbnail_as_path(),
            "task": self.parent.engine.context.task,
            "dependency_paths": [primary_publish_path],
            "published_file_type": tank_type,
            "sg_fields": sg_fields_to_update,
        }
        sg_publishes = sgtk.util.register_publish(**args)

        self.parent.tank.shotgun.update("PublishedFile", sg_publishes["id"], {"sg_status_list": 'rev'})

        return True




    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        pass



class UploaderThread(QtCore.QThread):
    """
    Simple worker thread that encapsulates uploading to shotgun.
    Broken out of the main loop so that the UI can remain responsive
    even though an upload is happening
    """
    def __init__(self, app, version, path_to_movie, thumbnail_path, upload_to_shotgun):
        QtCore.QThread.__init__(self)
        self._app = app
        self._version = version
        self._path_to_movie = path_to_movie
        self._thumbnail_path = thumbnail_path
        self._upload_to_shotgun = upload_to_shotgun
        self._errors = []

    def get_errors(self):
        """
        can be called after execution to retrieve a list of errors
        """
        return self._errors

    def run(self):
        """
        Thread loop
        """
        upload_error = False

        if self._upload_to_shotgun:
            try:
                self._app.tank.shotgun.upload("Version", self._version["id"], self._path_to_movie, "sg_uploaded_movie")
            except:
                self._errors.append(
                    "Movie upload to Shotgun failed: {}".format(traceback.print_exc())
                )
                upload_error = True

        if not self._upload_to_shotgun or upload_error:
            try:
                self._app.tank.shotgun.upload_thumbnail("Version", self._version["id"], self._thumbnail_path)
            except:
                self._errors.append(
                    "Thumbnail upload to Shotgun failed: {}".format(traceback.print_exc())
                )
