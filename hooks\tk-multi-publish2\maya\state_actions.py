# -*- coding: utf-8 -*-
# Standard library:
import pprint
#   .   .   .   .   .   .   .   .   .   .   .
# Third party:
import sgtk
#   .   .   .   .   .   .   .   .   .   .   .
# Project:
# ================================================================
pp = pprint.PrettyPrinter(indent=3).pprint

HookBaseClass = sgtk.get_hook_baseclass()


class FileCheckAction(HookBaseClass):

    def generate_actions(self, state, **kwargs):

        if state["errors_found"] > 0:
            self.logger.error(
                "There are {0} errors in the scene"
                .format(state["errors_found"])
            )

            for key in state["errors"]:
                error_message = (
                    "{0} ({1}):".format(
                        key,
                        len(state["errors"][key])
                    )
                )
                problems_message = ""

                for element in state["errors"][key]:
                    problems_message += "{0}\n".format(element)

                self.logger.error(
                    error_message,
                    extra={
                        "action_show_more_info":
                        {
                            "label":
                                "Show details",
                            "tooltip":
                                "Show all information from the error",
                            "text":
                                "<h3>{0}</h3><pre>{1}</pre>".format(
                                    error_message,
                                    problems_message
                                )
                        }
                    }
                )

                #   . . . . . . . . . . . . . . . . . . . . . .

                if state["callbacks"][key]["enabled"]:
                    log = None
                    message_type = \
                        state["callbacks"][key]["type"]

                    if message_type == "error":
                        log = self.logger.error
                    elif message_type == "warning":
                        log = self.logger.warning
                    elif message_type == "debug":
                        log = self.logger.debug
                    else:
                        log = self.logger.info

                    message = \
                        state["callbacks"][key]["message"]

                    callback = \
                        state["callbacks"][key]["callback"]

                    label = \
                        state["callbacks"][key]["label"]

                    tooltip = \
                        state["callbacks"][key]["tooltip"]

                    log(message, extra={
                        "action_button":
                            {
                                "label": label,
                                "tooltip": tooltip,
                                "callback": callback()
                            }
                    })
                #   . . . . . . . . . . . . . . . . . . . . . .

            there_is_error = \
                "Checks have not been passed, " + \
                "found {0} problems" \
                .format(state["errors_found"])

            raise Exception(there_is_error)

        else:
            self.logger.debug("All checks passed!")
