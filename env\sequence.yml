# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

description: Apps and engines loaded when a Sequence is loaded. Since std VFX
  config template has a file system structure which is centered around pipeline
  steps, this environment is largely empty. Most of the work takes place on a
  level in the file system where both a shot and a pipeline step is available -
  e.g Shot ABC, modeling, so all apps for loading, publishing etc. are located
  in the shot_step environment. This environment mostly contains utility apps
  and the tank work files app, which lets you choose a task to work on and load
  associated content into an application.

################################################################################

includes:
- ./includes/frameworks.yml
- ./includes/settings/tk-3dsmax.yml
- ./includes/settings/tk-houdini.yml
- ./includes/settings/tk-maya.yml
- ./includes/settings/tk-motionbuilder.yml
- ./includes/settings/tk-nuke.yml
- ./includes/settings/tk-shell.yml
- ./includes/settings/tk-shotgun.yml
- ./includes/settings/tk-harmony.yml
- ./includes/settings/tk-fusion.yml
- ./includes/settings/tk-krita.yml
- ./includes/settings/tk-blender.yml

################################################################################
# configuration for all engines to load in a sequence context

engines:
  tk-3dsmax: "@settings.tk-3dsmax.sequence"
  tk-houdini: "@settings.tk-houdini.sequence"
  tk-maya: "@settings.tk-maya.sequence"
  tk-motionbuilder: "@settings.tk-motionbuilder.sequence"
  tk-nuke: "@settings.tk-nuke.sequence"
  tk-shell: "@settings.tk-shell.sequence"
  tk-shotgun: "@settings.tk-shotgun.sequence"
  tk-harmony: "@settings.tk-harmony.sequence"
  tk-fusion: "@settings.tk-fusion.sequence"
  tk-krita: "@settings.tk-krita.sequence"
  tk-blender: "@settings.tk-blender.sequence"

################################################################################
# reference all of the common frameworks

frameworks: "@frameworks"
