#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

"""
An app that ensure local folders and database cached filesystem folders
for tasks assigned to the current user

"""

import sys
import os

from tank import Hook
from tank import TankError
from tank.platform import Application
from tank.platform.qt import QtCore, QtGui
import tank

class ProcessItemsHook(Hook):

    def execute(self, entity_type, entities, other_params, **kwargs):

        self.parent.engine.show_busy("Ensure folders", "Syncing folders and cache...")

        filters = [['task_assignees', 'is', self.parent.context.user],
                   ['project', 'is', self.parent.context.project]]
        tasks = self.parent.shotgun.find('Task', filters)

        tasky_ids = [t['id'] for t in tasks]

        self.parent.sgtk.create_filesystem_structure('Task', tasky_ids,
                                                     engine=self.parent.engine.name)

        self.parent.engine.clear_busy()

        return {'succes': [1], 'messages': [], 'errors': []}
