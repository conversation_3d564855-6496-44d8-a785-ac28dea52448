# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

################################################################################

includes:
- ../app_locations.yml
- ../engine_locations.yml
- ./tk-multi-loader2.yml
- ./tk-multi-publish2.yml
- ./tk-multi-reviewsubmission.yml
- ./tk-multi-screeningroom.yml
- ./tk-multi-shotgunpanel.yml
- ./tk-multi-snapshot.yml
- ./tk-multi-workfiles2.yml

################################################################################

# asset
settings.tk-photoshopcc.asset:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2"
  shelf_favorites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  location: '@engines.tk-photoshopcc.location'

# asset_step
settings.tk-photoshopcc.asset_step:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    tk-multi-loader2: "@settings.tk-multi-loader2.photoshop"
    tk-multi-publish2: "@settings.tk-multi-publish2.photoshop.asset_step"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel.photoshop"
    tk-multi-snapshot: "@settings.tk-multi-snapshot.photoshop.asset_step"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.photoshop.asset_step"
  #  tk-multi-reviewsubmission: "@settings.tk-multi-reviewsubmission.photoshop"
    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset" ]

  shelf_favorites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-snapshot, name: Snapshot...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  - {app_instance: tk-multi-publish2, name: Publish...}
  location: '@engines.tk-photoshopcc.location'

# asset_step_notask
settings.tk-photoshopcc.asset_step_notask:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-loader2: "@settings.tk-multi-loader2.photoshop"
    tk-multi-publish2: "@settings.tk-multi-publish2.photoshop.asset_step_notask"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel.photoshop"
    tk-multi-snapshot: "@settings.tk-multi-snapshot.photoshop.asset_step_notask"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.photoshop.asset_step_notask"
  #  tk-multi-reviewsubmission: "@settings.tk-multi-reviewsubmission.photoshop"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset" ]

  menu_favourites:
    - { app_instance: tk-multi-workfiles2, name: File Open... }
    - { app_instance: tk-multi-snapshot, name: Snapshot... }
    - { app_instance: tk-multi-workfiles2, name: File Save... }
    - { app_instance: tk-multi-publish2, name: Publish... }
  template_project: asset_work_area_photoshop_notask
  location: "@engines.tk-photoshopcc.location"

# environment_step
settings.tk-photoshopcc.environment_step:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-loader2: "@settings.tk-multi-loader2.photoshop"
    tk-multi-publish2: "@settings.tk-multi-publish2.photoshop.environment_step"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel"
    tk-multi-snapshot: "@settings.tk-multi-snapshot.photoshop.environment_step"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.photoshop.environment_step"
  #  tk-multi-reviewsubmission: "@settings.tk-multi-reviewsubmission.photoshop"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Environment" ]

  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-snapshot, name: Snapshot...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  - {app_instance: tk-multi-publish2, name: Publish...}
  location: "@engines.tk-photoshopcc.location"

# environment_step_notask
settings.tk-photoshopcc.environment_step_notask:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-loader2: "@settings.tk-multi-loader2.photoshop"
    tk-multi-publish2: "@settings.tk-multi-publish2.photoshop.environment_step_notask"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel"
    tk-multi-snapshot: "@settings.tk-multi-snapshot.photoshop.environment_step_notask"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.photoshop.environment_step_notask"
  #  tk-multi-reviewsubmission: "@settings.tk-multi-reviewsubmission.photoshop"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Environment" ]

  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-snapshot, name: Snapshot...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  - {app_instance: tk-multi-publish2, name: Publish...}
  location: "@engines.tk-photoshopcc.location"

# project
settings.tk-photoshopcc.project:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.photoshop.project"
    mty-executeaction-ensure-folders:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Ensure Tasks Folders"
      action_hook: "{config}/tk-shotgun-executeaction/create_folders_for_assigned_tasks.py"
      sg_extended_fields: {}
      allowed_entities: ['Shot', 'Sequence', 'Asset', 'Project']
  shelf_favorites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  location: '@engines.tk-photoshopcc.location'

# episode_step
settings.tk-photoshopcc.episode_step:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    tk-multi-loader2: "@settings.tk-multi-loader2.photoshop"
    tk-multi-publish2: "@settings.tk-multi-publish2.photoshop.episode_step"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel.photoshop"
    tk-multi-snapshot: "@settings.tk-multi-snapshot.photoshop.episode_step"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.photoshop.episode_step"
  #  tk-multi-reviewsubmission: "@settings.tk-multi-reviewsubmission.photoshop"
    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Episode" ]

  shelf_favorites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-snapshot, name: Snapshot...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  - {app_instance: tk-multi-publish2, name: Publish...}
  location: '@engines.tk-photoshopcc.location'

# shot
settings.tk-photoshopcc.shot:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2"
    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'

  shelf_favorites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  location: '@engines.tk-photoshopcc.location'

# shot_step
settings.tk-photoshopcc.shot_step:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    tk-multi-loader2: "@settings.tk-multi-loader2.photoshop"
    tk-multi-publish2: "@settings.tk-multi-publish2.photoshop.shot_step"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel.photoshop"
    tk-multi-snapshot: "@settings.tk-multi-snapshot.photoshop.shot_step"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.photoshop.shot_step"
  #  tk-multi-reviewsubmission: "@settings.tk-multi-reviewsubmission.photoshop"
    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Shot" ]

  shelf_favorites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-snapshot, name: Snapshot...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  - {app_instance: tk-multi-publish2, name: Publish...}
  location: '@engines.tk-photoshopcc.location'
