#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

"""
An app that creates a "CAMERA" node based on the current shot. This new node will
be used for exporting a json file containing the camera animation which will be used
in comp (AFX). The name of the camera node will be {shot_code}_camera. It will be
created only if there's no camera node with the same name and with the relevant metadata,
otherwise, that existing node will be renamed and the node will be created.

It creates a string attribute in the camera node called 'mtyShotCamera' set to
true. This allows us to collect specific write nodes for the publisher. This attribute
is not exposed in the interface so it can be modified only via scripting. This
functionality is used by the collertor_camera  hook."

"""

from tank import Hook
from tank import TankError
from tank.platform import Application
from tank.platform.qt import QtCore, QtGui
import tank
import sgtk

import os
import re
import pprint


class ProcessItemsHook(Hook):
    def execute(self, **kwargs):

        self.parent.engine.logger.info(
            "execute action create_pipeline_shot_camera_node start. {}".format("-" * 80)
        )

        result = self.create_shot_camera_node()

        if not result:
            result = {"succes": [1], "messages": [], "errors": []}

        self.parent.engine.logger.info(
            "execute action create_pipeline_shot_camera_node end. {}".format("-" * 80)
        )

        return result

    def create_shot_camera_node(self):

        result = {"succes": [1], "messages": [], "errors": []}

        # get camera_node_color from settings stored in tk-harmony.yml
        camera_node_color = self.parent.get_setting("camera_node_color")
        camera_node_color_str = str(camera_node_color)[1:-1]
        camera_attr_name = self.parent.get_setting("camera_attr_name")
        camera_attr_type = self.parent.get_setting("camera_attr_type")
        context = self.parent.engine.context
        self.parent.engine.logger.debug("context:\n{}".format(pprint.pformat(context)))
        if context.entity["type"] == "Shot":
            shot_name = context.entity["name"]
            camera_node_name = "{}-Camera".format(shot_name)
        else:
            error_msg = (
                "Couldn't get shot name from context. Please make sure you "
                "are working in the Assembly or Layout tasks of a shot."
            )
            # self.parent.engine.show_message(error_msg)
            QtGui.QMessageBox.error(
                None, "Couldn't get shot name from context", error_msg
            )

            raise Exception(error_msg)

        create_shot_camera_node_cmd = """
include("harmony_utility_functions.js");

function createNode(nodeType, prevNode, color, nodeName, xPos, yPos) {
    var prevNodeParent = node.parentNode(prevNode);

    if (nodeType == "PEG") {
        var newNode = node.add(
            prevNode,
            nodeName,
            nodeType,
            xPos,
            yPos,
            0
        );
    } else if (nodeType == "CAMERA") {
        var prevNodeXpos = node.coordX(prevNode);
        var prevNodeYpos = node.coordY(prevNode);
        var prevNodeZpos = node.coordZ(prevNode);
        var newNode = node.add(
            prevNodeParent,
            nodeName,
            nodeType,
            prevNodeXpos,
            prevNodeYpos + 100,
            prevNodeZpos
        );
    };

    if (nodeType != "PEG") {
        node.link(prevNode, 0, newNode, 0);
    }
    node.setColor(newNode, color);
    log("Created node: " + node.getName(newNode));

    return newNode;
}

function createShotCameraNode(nodeName) {
    // var selectedNodes = selection.numberOfNodesSelected();

    var createdNodesArray = [];

    var good_position = get_good_scene_position(0, 100);
    var xPos = good_position.x;
    var yPos = good_position.y;

    // var cameraNodeColor = new ColorRGBA(0, 0, 255, 255);
    var cameraNodeColor = new ColorRGBA(%s);

    var pegNode = createNode(
        "PEG",
        "Top",
        cameraNodeColor,
        nodeName + "-PEG",
        xPos,
        yPos
    );

    createdNodesArray.push(pegNode);

    var cameraNode = createNode(
        "CAMERA",
        pegNode,
        cameraNodeColor,
        nodeName,
        null,
        null
    );

    // set the new camera as display camera
    node.setAsDefaultCamera(cameraNode);

    // var mty_attr = create_attr(cameraNode, "mtyShotCamera", "BOOL")
    var mty_attr = create_attr(cameraNode, "%s", "%s")
    // log("attr name: " + mty_attr.name())
    // log("attr value: " + mty_attr.boolValue())

    // move camera into initial position: 0, 0, 12
    var pos_attr = node.getAttr(cameraNode, frame.current(), "OFFSET");
    var pos_value = pos_attr.pos3dValue();
    pos_value.setXYZ(0.0, 0.0, 12.0);
    pos_attr.setValue(pos_value);

    // Lock the camera node
    node.setLocked(cameraNode, true);

    createdNodesArray.push(cameraNode);

    selection.clearSelection();
    selection.addNodesToSelection(createdNodesArray);

    return createdNodesArray;
}

function collect_shot_camera_node(nodeName) {
    // Get the passthrough nodes by attr
    var array_of_node_types = ["CAMERA"];
    var array_of_nodes = node.getNodes(array_of_node_types);

    var array_of_shot_camera_nodes = [];
    for (var i = 0; i < array_of_nodes.length; ++i) {
        var node_path = array_of_nodes[i];
        log("checking node: " + node_path)
        // var attr = node.getAttr(node_path, 1.0, "mtyShotCamera");
        var attr = node.getAttr(node_path, 1.0, "%s");
        // log(attr.name())
        // log(attr.typeName())
        // log(attr.boolValue())
        if (attr != null) {
            // if (attr.keyword() != "" && attr.typeName() == "BOOL")
            if (attr.keyword() != "" && attr.typeName() == "%s") {
                if (attr.boolValue() == true) {
                    if (node.getName(node_path) == nodeName) {
                        log("Found shot camera node: " + node_path);
                        array_of_shot_camera_nodes.push(node_path);
                    }
                }
            }
        }
    }
    return array_of_shot_camera_nodes;
}


// var nodeName = "testShot_Camera";
var nodeName = "%s";

var array_of_shot_camera_nodes = collect_shot_camera_node(nodeName);
log("found " + array_of_shot_camera_nodes.length + " shot cameras");

if (array_of_shot_camera_nodes.length == 0) {
    var createdNodesArray = createShotCameraNode(nodeName);
    MessageBox.information(createdNodesArray.length + " camera nodes were created");
} else {
    selection.clearSelection();
    selection.addNodesToSelection(array_of_shot_camera_nodes);
    MessageBox.information(
        "Shot camera already exists, you don't need to create it again! \
  If you want to create it again from scratch, you need to delete the existing \
camera first."
    );
};

""" % (
    camera_node_color_str,
    camera_attr_name,
    camera_attr_type,
    camera_attr_name,
    camera_attr_type,
    camera_node_name,
)

        self.parent.engine.logger.debug(create_shot_camera_node_cmd)
        self.parent.engine.app.execute(create_shot_camera_node_cmd)

        return result
