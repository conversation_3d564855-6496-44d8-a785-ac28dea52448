# -*- coding: utf-8 -*-
# Standard library:
import os
from pprint import pformat
from collections import namedtuple
import json
# ___   ___   ___   ___   ___   ___  ___
# Third party:
import sgtk
import pymel.core as pm
# ___   ___   ___   ___   ___   ___  ___
# Project:
import pprint

HookBaseClass = sgtk.get_hook_baseclass()
pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)
pf = pprint.pformat


# ===============================================================================


class MayaEnvironmentOverridesCollector(HookBaseClass):
    @property
    def settings(self):
        _settings = {
            'Environment Overrides Supported Types': {
                'type': 'list',
                'default': None,
                'description': (
                    'Environment publish file type name. This is to '
                    'unlock support to create environment overrides '
                    'on certain publiish files found in the scene.'
                )
            },
            'attributes': {
                'type': 'dict',
                'default': None,
                'description': ''
            }
        }

        result = super(MayaEnvironmentOverridesCollector, self).settings or {}
        result.update(_settings)

        return result

    @property
    def name(self):
        return 'MayaEnvironmentOverridesCollector'

    # ---------------------------------------------------------------------------

    def process_current_session(self, settings, parent_item):
        print("\n\n" + (">" * 120))
        print('\nCOLLECTOR - {0}:\n'.format(self.name))
        # let parent collector do its own work
        super(MayaEnvironmentOverridesCollector, self) \
            .process_current_session(settings, parent_item)

        if not parent_item.type_spec.endswith(".session"):
            item = next(
                (i for i in parent_item.descendants if i.type_spec.endswith(".session")),
                parent_item,
            )
        else:
            item = parent_item

        collected_item = self._collect_environments(settings, item)


    # ---------------------------------------------------------------------------

    def _collect_environments(self, settings, parent_item):
        list_of_references = pm.listReferences()

        item = parent_item.create_item(
            type_spec='maya.session.asset.environment.overrides',
            type_display='Hierarchy Environment Overrides',
            name='Environment Overrides Publish'
        )

        self.parent.engine.execute_hook_expression(
            "{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py",
            "use_farm_or_local_processing",
            item = item
        )

        item.set_icon_from_path(
            os.path.join(self.disk_location, 'icon_overrides.png')
        )

        item.properties['list_of_references'] = (
            self._list_of_filtered_references(settings, list_of_references)
        )

        return item

    def _list_of_filtered_references(self, settings, list_of_references):
        result = self._filter_environment_references(
            settings,
            self._reference_has_attribute(
                settings,
                list_of_references
            )
        )
        # self.parent.logger.info(
        #     "Overrides _list_of_filtered_references:\n{}".format(pf(result)
        # ))
        return result

    def _filter_environment_references(self, settings, list_of_references):
        result = []
        _attr = settings.get('attributes').value.get('asset_type')
        # self.parent.logger.info(
        #     "Overrides filter env references (_attr): {}".format(_attr)
        # )
        list_of_supported_types = (
            settings.get('Environment Overrides Supported Types').value
        )

        for _ref in list_of_references:
            _node = _ref.nodes()[0]
            # self.parent.logger.info(
            #     "Overrides filter env references (_node): {}".format(_node.name())
            # )
            _attr_value = _node.getAttr(_attr)
            # self.parent.logger.info(
            #     "Overrides filter env references (_attr_value): {}".format(_attr_value)
            # )
            if _attr_value in list_of_supported_types:
                result.append(_ref)

        return result

    def _reference_has_attribute(self, settings, list_of_references):
        result = []
        _attr = settings.get('attributes').value.get('asset_type')
        # self.parent.logger.info(
        #     "Overrides reference has attribute (_attr): {}".format(_attr)
        # )

        for _ref in list_of_references:
            if _ref.nodes()[0].hasAttr(_attr):
                result.append(_ref)

        return result
