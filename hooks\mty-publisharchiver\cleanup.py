################################################################################
#
# Copyright (c) 2021 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
################################################################################

import sgtk

from pprint import pformat

HookBaseClass = sgtk.get_hook_baseclass()


class CleanupPublishHook(HookBaseClass):

    def cleanup(self, publish={}, logger=None):
        """
        A hook to define the action taken when doing cleanup
        Args:
            Publish
            Logger
        """

        self.parent.archiver.move_to_cleanup_area([publish])

        # default hook logs file name
        logger.info(publish['code'] + ' was archived... ')
