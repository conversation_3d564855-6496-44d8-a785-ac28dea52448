import os
import sys
import json
import pprint
import tempfile
import sgtk
import subprocess

HookBaseClass = sgtk.get_hook_baseclass()

class RunMayapy(HookBaseClass):
    def process_secondary_outputs(self, *args, **kwargs):
        """
        Run a Python script using mayapy.
        """
        print("kwargs!")
        pprint.pprint(kwargs)
        print("args!")
        pprint.pprint(args)

        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix=".json") as temp_file:
            json.dump(kwargs, temp_file)
            temp_file_path = temp_file.name
            print(f"JSON temp: {temp_file_path}")

        dict_properties = kwargs.get("secondary_outputs_to_farm")
    
        env = os.environ.copy()
        env["SG_PROCESSING_SECONDARY_OUTPUTS"] = "True"

        # Get current maya version
        maya_version = dict_properties.get("maya_version")

        # Mayapy path
        if sys.platform == "win32":
            mayapy_path = f'C:/Program Files/Autodesk/Maya{maya_version}/bin/mayapy.exe'
        elif sys.platform == "linux":
            mayapy_path = f"/opt/Autodesk/Maya{maya_version}/bin/mayapy"
        elif sys.platform == "darwin":
            mayapy_path = f"/Applications/Autodesk/maya{maya_version}/bin/mayapy"
        else:
            raise Exception(f"Unsupported operating system: {sys.platform}")

        # Check if mayapy exists
        if not os.path.exists(mayapy_path):
            raise FileNotFoundError(f"Mayapy not found at: {mayapy_path}")

        # Process hook path
        # config = os.path.dirname(os.path.dirname(self.disk_location))
        directory_dir = os.path.dirname(os.path.abspath(__file__))
        hook_name = "process_maya_secondary_outputs.py"
        process_maya_secondary_outputs_path = os.path.join(directory_dir, hook_name)
        process_maya_secondary_outputs_path = process_maya_secondary_outputs_path.replace("\\", "/")

        temp_file_path = temp_file_path.replace("\\", "/")

        command = [mayapy_path, process_maya_secondary_outputs_path, temp_file_path]
        print (f"Command: {command}")
        # Run subprocess command
        process = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            env=env
        )
        stdout, stderr = process.communicate()
        print("stdout:", stdout.decode())
        print("stderr:", stderr.decode())

        print(f"Process completed for Maya {maya_version}")

    def process_split_on_farm(self, *args, **kwargs):
        print("kwargs!")
        pprint.pprint(kwargs)
        print("args!")
        pprint.pprint(args)

        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix=".json") as temp_file:
            json.dump(kwargs, temp_file)
            temp_file_path = temp_file.name
            print(f"JSON temp: {temp_file_path}")

        maya_version = kwargs.get("maya_version")

        # Mayapy path
        if sys.platform == "win32":
            mayapy_path = f'C:/Program Files/Autodesk/Maya{maya_version}/bin/mayapy.exe'
        elif sys.platform == "linux":
            mayapy_path = f"/opt/Autodesk/Maya{maya_version}/bin/mayapy"
        elif sys.platform == "darwin":
            mayapy_path = f"/Applications/Autodesk/maya{maya_version}/bin/mayapy"
        else:
            raise Exception(f"Unsupported operating system: {sys.platform}")

        directory_dir = os.path.dirname(os.path.abspath(__file__))
        hook_name = "process_maya_split_on_farm.py"
        process_maya_split_shot = os.path.join(directory_dir, hook_name)
        process_maya_split_shot = process_maya_split_shot.replace("\\", "/")
        temp_file_path = temp_file_path.replace("\\", "/")

        env = os.environ.copy()
        env["SG_PROCESSING_SECONDARY_OUTPUTS"] = "True"

        command = [mayapy_path, process_maya_split_shot, temp_file_path]
        print (f"Command: {command}")
        # Run subprocess command
        process = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            env=env
        )
        stdout, stderr = process.communicate()
        print("stdout:", stdout.decode())
        print("stderr:", stderr.decode())


