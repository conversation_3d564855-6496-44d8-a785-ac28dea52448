#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

"""
An app that syncs the frame range between a scene and a shot in Shotgun.
Plus adding and modifiying the scene Render Settings

"""

import sys
import os

from tank import Hook
import os
from maya import cmds
import sgtk

class ProcessItemsHook(Hook):

    def execute(self, entity_type, entities, other_params, **kwargs):

        gpus_found = self.list_gpu_nodes()
        for gpu_path in gpus_found:
            latest = self.get_latest_publish(gpu_path)
            if latest:
                if gpu_path != latest["path"]["local_path"]:
                    self.parent.log_debug("Older: {0}".format(gpu_path))
                    self.parent.log_debug("Latest: {0}".format(latest["path"]["local_path"]))
                    for gpunode in gpus_found[gpu_path]:
                        self.parent.log_debug("\tnode changed: {0}".format(gpunode))
                        cmds.setAttr("%s.cacheFileName" % gpunode, latest["path"]["local_path"], type="string")

        return {'succes': [1], 'messages': [], 'errors': []}

    def list_gpu_nodes(self):
        # also look for gpu cache nodes
        gpu_nodes = cmds.ls(l=True, type="gpuCache")
        self.parent.log_debug("gpu nodes found: {0}".format(len(gpu_nodes)))
        listed_gpu_nodes = {}
        for gpu_node in gpu_nodes:
            # ensure this is actually part of this session and not referenced
            if cmds.referenceQuery(gpu_node, isNodeReferenced=True):
                # this is embedded in another reference, so don't include it in
                # the breakdown
                continue
            # get path and make it platform dependent
            # (maya uses C:/style/paths)
            gpu_cache_path = cmds.getAttr("%s.cacheFileName" % gpu_node)
            gpu_cache_path = gpu_cache_path.replace("/", os.path.sep)
            if gpu_cache_path:
                if gpu_cache_path not in listed_gpu_nodes:
                    listed_gpu_nodes[gpu_cache_path] = [gpu_node]
                else:
                    listed_gpu_nodes[gpu_cache_path].append(gpu_node)

        return listed_gpu_nodes

    def get_latest_publish(self, current_path):

        tk = self.parent.engine.sgtk
        fields = ['entity', 'published_file_type', 'name']
        publish = sgtk.util.find_publish(tk, [current_path], fields=fields)
        publish = publish[current_path]
        # Find latest approved published file version number
        filters = [['entity', 'is', publish['entity']],
                   ['published_file_type', 'is', publish['published_file_type']]
                   ]
        order = [{'field_name': 'version_number', 'direction': 'desc'}]
        latests = self.parent.engine.shotgun.find('PublishedFile', filters, ['version_number', 'path'], order=order)

        map_of_latests = {}
        for publish in latests:
            path = publish["path"]["local_path"]
            template = tk.template_from_path(path)
            fields = template.get_fields(path)
            if fields['name'] not in map_of_latests:
                map_of_latests[fields['name']] = []
            map_of_latests[fields['name']].append(publish)

        map_setting = ["proxy", "master"]
        latest = None
        for model_type in map_setting:
            if model_type in map_of_latests and map_of_latests[model_type]:
                latest = map_of_latests[model_type][0]
                break

        return latest
