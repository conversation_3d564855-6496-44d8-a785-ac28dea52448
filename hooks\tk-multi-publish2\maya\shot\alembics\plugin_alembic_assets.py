from pprint import pprint, pformat
# -*- coding: utf-8 -*-
# Standard library:
import os
import sys
import sgtk
import pprint
import re
import traceback
import subprocess
# ___   ___   ___   ___   ___   ___  ___
# Third party:
from sgtk.platform.qt import QtGui, QtCore
import maya.cmds as cmds
import maya.mel as mel
import pymel.core as pm

# ___   ___   ___   ___   ___   ___  ___
# Project:
HookBaseClass = sgtk.get_hook_baseclass()
# ====================================================================
pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)
pf = pprint.pformat


class MayaShotAlembicAssetPublishPlugin(HookBaseClass):
    @property
    def icon(self):
        return os.path.join(
            self.disk_location,
            "publish_alembic.png"
        )

    @property
    def name(self):
        return "Publish Shot alembics"

    @property
    def description(self):
        return (
            'This plugin will save alembic caches for specific assets.'
        )

    @property
    def settings(self):
        # inherit the settings from the base publish plugin
        plugin_settings = super(
            MayaShotAlembicAssetPublishPlugin, self).settings or {}

        _settings = {
            'plugin_hooks': {
                'type': 'dict',
                'default': None,
                'description': ''
            },
            # 'map_of_asset_group_names': {
            #     'type': 'dict',
            #     'default': None,
            #     'description': (
            #         'Asset group names in the scene.'
            #     )
            # },
            'map_of_tasks': {
                'type': 'dict',
                'default': None,
                'description': ''
            },
            'map_of_pipeline_steps': {
                'type': 'dict',
                'default': None,
                'description': ''
            },
            # 'map_of_entities': {
            #     'type': 'dict',
            #     'default': None,
            #     'description': ''
            # },
            'plugin_hooks': {
                'type': 'dict',
                'default': None,
                'description': ''
            },
            'workarea_template': {
                'type': 'string',
                'default': None,
                'description': ''
            },
            'publish_template': {
                'type': 'string',
                'default': None,
                'description': ''
            },
            'primary_publish_template': {
                'type': 'string',
                'default': None,
                'description': ''
            },
            'publish_type': {
                'type': 'string',
                'default': None,
                'description': ''
            }
        }

        # update the base settings
        plugin_settings.update(_settings)

        return plugin_settings

    def plugin_hook(self, settings, string_key):
        _h = settings.get("plugin_hooks")
        return self.parent.create_hook_instance(_h.value.get(string_key))

    @property
    def item_filters(self):
        return ["maya.session.shot.geometryCache"]

    # ---------------------------------------------------------------------------

    def accept(self, settings, item):
        print("\n" + (">" * 120))
        print('{0}.accept'.format(self.name))

        result = {'accepted': True, 'checked': True}

        #  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
        # Temppral accept for shots tagged:
        _temp_accept_hook = self.plugin_hook(settings, 'temporal_accept')
        is_tagged_as_master_light = _temp_accept_hook.accept_shot_by_tag(
            list_of_tags=[{'type': 'Tag', 'id': 969}]
        )
        if is_tagged_as_master_light:
            return result

        #  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

        _hook_accept = self.plugin_hook(settings=settings, string_key='accept')
        if not _hook_accept.is_context_supported(settings, self.parent.context):
            result['accepted'] = False
            return result

        return result

    # ---------------------------------------------------------------------------

    def validate(self, settings, item):
        return True

    # ---------------------------------------------------------------------------

    def publish(self, settings, item):
        # self.parent.engine.logger.info("\n" + (">" * 120))
        self.parent.engine.logger.info('{0}.publish'.format(self.name))
        _hook_alembic = self.plugin_hook(settings, 'alembic')
        list_of_publishes = _hook_alembic.publish(settings, item)

        root_item = self.get_root_item(item)

        root_item.properties['sg_publish_extra_data'].extend(list_of_publishes)

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item
        return root

    def finalize(self, settings, item):
        pass
