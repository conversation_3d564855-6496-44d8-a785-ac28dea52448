#####################################################################################
#
# Copyright (c) 2021 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

import os
import sgtk

HookBaseClass = sgtk.get_hook_baseclass()

logger = sgtk.platform.get_logger("EnsureFileIsLocal")

class EnsureFileIsLocal(HookBaseClass):

    # example call:
    # execute_hook_expression(
    #   "{config}/ensure_publish_is_local.py",
    #   "execute", set_progress=set_progress,
    #   publish_id=417217
    # )

    def execute(self, set_progress=None, publish_id=None):

        if not set_progress:
            raise Exception('set_progress argument needs to be defined!')

        if not publish_id:
            raise Exception('publish_id argument needs to be defined!')

        if not hasattr(self, 'metasync'):
            self.metasync = self.load_framework("mty-framework-metasync")

        transfersManager = self.metasync.transfersManager
        syncLogsManager = self.metasync.syncLogsManager

        set_progress(10)

        logger.info("Checking publish: %s" % publish_id)

        publish = self.parent.shotgun.find_one(
            'PublishedFile',
            [['id', 'is', publish_id]],
            syncLogsManager.publish_fields
        )

        path = publish['path']['local_path']

        logger.info("Checking path: %s" % path)

        set_progress(20)

        if "%" in path:
            logger.info("path is a file sequence")

            dirname = os.path.dirname(path)
            if os.path.exists(dirname) and \
                    self._collect_sequenced_files(path):

                logger.info("sequence already exists, checking dependencies")
                set_progress(50)

                transfersManager.ensure_local_dependencies(
                    publish, show_busy=False,
                    report_progress=set_progress,
                    progress_range=(50,95)
                )

                set_progress(99)

                return path
        else:
            logger.info("path is a single file")
            if os.path.exists(path):

                logger.info("file already exists, checking dependencies")
                set_progress(50)

                transfersManager.ensure_local_dependencies(
                    publish, show_busy=False,
                    report_progress=set_progress,
                    progress_range=(50,95)
                    )

                set_progress(99)

                return path

        logger.info("file doesn't exists, triggering sync")
        set_progress(30)
        transfersManager.ensure_file_is_local(
            path, publish, show_busy=False,
            report_progress=set_progress,
            progress_range=(40,95)
        )

        set_progress(99)

        return path

    def _collect_sequenced_files(self, sequence_path):
        folder_path = os.path.dirname(sequence_path)
        name, ext = os.path.splitext(sequence_path)
        folder_files = os.listdir(folder_path)
        sequence_files = []

        if len(folder_files) > 0:
            for file in sorted(folder_files):
                if file.endswith(ext):
                    path = os.path.join(folder_path, file)
                    sequence_files.append(path)

        return sequence_files
