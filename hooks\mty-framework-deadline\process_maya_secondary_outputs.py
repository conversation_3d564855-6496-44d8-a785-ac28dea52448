import os
import shutil
import sys
import json
import logging
import traceback
import maya.cmds as cmds
import maya.mel as mel
import pymel.core as pm

logging.basicConfig(level=logging.DEBUG)


# Open the Maya scene
def open_scene(scene_path, logger):
    """
    Open the Maya scene specified by the path.
    """

    logger.info(f"Opening scene: {scene_path}")
    pm.openFile(scene_path, force=True)


# Create a new publish manager instance and publish assets
def execute_publisher(engine, items_checked, logger):
    """
    Searches for and uses the 'tk-multi-publish2' app to publish assets to ShotGrid.
    """

    try:
        # Get the 'tk-multi-publish2' app from the engine
        publish_app = engine.apps.get("tk-multi-publish2")
        tag = os.environ.get("SG_PROCESSING_SECONDARY_OUTPUTS", "empty")
        print(f"env condition: {tag}")

        if not publish_app:
            raise RuntimeError(
                "Could not find the 'tk-multi-publish2' app in the current engine."
            )

        logger.info("App 'tk-multi-publish2' found and loaded...")

        # Create the publish manager
        manager = publish_app.create_publish_manager()
        print("Publish manager initialized...")

        # Collect the current session
        items = manager.collect_session()
        print(f"Items collected: {items}")
        print("three:")
        manager.tree.pprint()
        print("Session collected by manager.")
        # un check the items
        print(f"Items checked: {items_checked}")
        if items_checked:
            primary_output = "maya.session"
            for item in items:
                print(f"item name: {item.name}")
                if item.type_spec == primary_output:
                    print(f"item name: {item.name} is primary output")
                    continue
                if not item.name in items_checked:
                    print(f"item name: {item.name} unchecked")
                    item.checked = False
                else:
                    print(f"item name: {item.name} checked")
                    item.checked = True
                    for task in item.tasks:
                        print(f"old status task name: {task.name} active: {task.active}")
                        task.active = True
                        print(f"new status task name: {task.name} active: {task.active}")
        else:
            print("export all secondary outputs")

        print(f"Items collected: {items}")
        print("three:")
        manager.tree.pprint()

        # Validate the items before publishing
        tasks_failed_validation = manager.validate()
        print(f"Tasks failed validation: {tasks_failed_validation}")

        if tasks_failed_validation:
            print("Errors found during validation. Publishing will not proceed.")
            return "validation", tasks_failed_validation

        print("Publishing...")
        publish_result = manager.publish()
        if publish_result:
            return "publish", publish_result

        print("Finalizing publish...")
        finalize_result = manager.finalize()
        if finalize_result:
            return "finalize", finalize_result



    except Exception as e:
        print(f"Error during the publishing process: {e}\n full traceback:{traceback.format_exc()}")
        return "error", str(e)

    return None, []


def initializeToolkit(logger, config_info):
    # Add location for the sgtk core

    sg_site_name = config_info.get("SgSiteName")
    sg_site_url = config_info.get("SgSiteURL")
    CORE_TOOLKIT_PATH = os.environ.get("CORE_TOOLKIT_PATH")
    logger.info("CORE_TOOLKIT_PATH: {}".format(CORE_TOOLKIT_PATH))
    sys.path.insert(0, CORE_TOOLKIT_PATH)
    import sgtk

    logger.info("Start Tookit boostrap...")

    # Initialize the logger so we get output to our terminal.
    sgtk.LogManager().initialize_custom_handler()
    # Set debugging to true so that we get more verbose output
    sgtk.LogManager().global_debug = True

    logger.info("Authenticating shotgrid user.....")
    authenticator = sgtk.authentication.ShotgunAuthenticator()

    # Custom studio api script access
    sgapi_name_deadline = os.environ.get("SGAPI_NAME_DEADLINE_{}".format(sg_site_name))
    sgapi_key_deadline = os.environ.get("SGAPI_KEY_DEADLINE_{}".format(sg_site_name))

    # Create a user programmatically using the script's key.
    user = authenticator.create_script_user(
        api_script=sgapi_name_deadline, api_key=sgapi_key_deadline, host=sg_site_url
    )
    # Tells Toolkit which user to use for connecting to ShotGrid.
    sgtk.set_authenticated_user(user)
    logger.info("Api user authenticated.")
    logger.info("user authenticated as: {}".format(user))

    return user, sgtk


def loadEngine(user, sgEntity, descriptor, sgtk, logger):
    logger.info("Loading engine: tk-maya")
    logger.info("descriptor: {}".format(descriptor))

    # instance of the ToolkitManager
    mgr = sgtk.bootstrap.ToolkitManager(user)
    mgr.plugin_id = "basic.*"
    mgr.do_shotgun_config_lookup = False
    mgr.base_configuration = descriptor
    mgr.pre_engine_start_callback = (
        lambda ctx: ctx.sgtk.synchronize_filesystem_structure()
    )
    mgr.pre_engine_start_callback = (
        lambda ctx: ctx.sgtk.synchronize_filesystem_structure()
    )

    return mgr.bootstrap_engine("tk-maya", entity=sgEntity)


def main(json_file_path):
    """
    Main function.
    """
    with open(json_file_path, "r") as file:
        data = json.load(file)
    # after reading the json file deletes
    os.remove(json_file_path)

    primary_data = data.get("primary_item_data", {})
    secondary_data = data.get("secondary_outputs_to_farm", {})
    dict_properties = data.get("config_info", {})

    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)

    # Configuración del logger
    stream_handler = logging.StreamHandler()
    stream_handler.setLevel(logging.DEBUG)
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    stream_handler.setFormatter(formatter)
    logger.addHandler(stream_handler)

    # Inicialización del toolkit
    user, sgtk = initializeToolkit(logger, dict_properties)

    logger.info("JSON parsed successfully: %s", dict_properties)
    task = dict_properties.get("SgTask", {})
    if type(task) == str:
        task = json.loads(task)

    logger.info("primary_data: %s", primary_data)
    scene_path = get_path_for_OS(primary_data)

    logger.info("Scene path: %s", scene_path)

    # Inicialización del motor y la aplicación
    logger.info("Task: %s", task)
    descriptor = dict_properties.get("ConfigDescriptor", {})
    engine = loadEngine(user, task, descriptor, sgtk, logger)

    # create the workarea file
    workarea_path = copy_to_workarea(scene_path, secondary_data, engine, logger)

    # Apertura de la escena de Maya
    open_scene(workarea_path, logger)
    logger.info("Scene opened successfully.")

    # load tk multi publish 2
    items_chekeds = secondary_data.get("items_checked", None)
    publish_status, result = execute_publisher(engine, items_chekeds, logger)
    logger.info("Publishing status: %s", publish_status)
    logger.info("Publishing result: %s", result)

    if publish_status:
        logger.info("Publishing process failed.")
        logger.info("%s completed with result: %s", publish_status, result)
        raise Exception(result)
    else:
        logger.info("Publishing process completed successfully.")


def copy_to_workarea(scene_path, secondary_data, engine, logger):
    logger.info("get path for workarea")
    pulbish_template_name = secondary_data.get("publish_template")
    work_template_name = secondary_data.get("work_template")

    pulbish_template = engine.get_template_by_name(pulbish_template_name)
    work_template = engine.get_template_by_name(work_template_name)

    fields = pulbish_template.get_fields(scene_path)

    while True:
        fields["version"] = fields["version"] + 1
        workarea_path = work_template.apply_fields(fields)
        if not os.path.exists(workarea_path):
            break
    # Copy scene to workarea
    logger.info("Copying scene to workarea: %s", workarea_path)
    shutil.copy2(scene_path, workarea_path)

    return workarea_path


def get_path_for_OS(primary_item_data):
    dict_path = primary_item_data.get("path")
    if sys.platform == "darwin":
        path = dict_path.get("local_path_mac")
    if sys.platform == "win32":
        path = dict_path.get("local_path_windows")
    if sys.platform == "linux":
        path = dict_path.get("local_path_linux")

    return path


if __name__ == "__main__":
    json_file_path = sys.argv[1]
    main(json_file_path)
