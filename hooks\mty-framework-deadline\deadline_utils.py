################################################################################
#
# Copyright (c) 2024 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
################################################################################

import sgtk
import os

from pprint import pformat

HookBaseClass = sgtk.get_hook_baseclass()


class DeliveryUtils(HookBaseClass):
    def get_python_path(self):
        #this metod get the paths from Core/interpreter_[OS].cfg
        file_path = os.path.abspath(__file__)
        config_path = os.path.dirname(os.path.dirname(os.path.dirname(file_path)))

        core_folder_path = os.path.join(config_path,  "core")
        # Listar todos los archivos en el directorio
        cfg_files = [f for f in os.listdir(core_folder_path) if f.endswith('.cfg')]

        python_paths = {}
        # Imprimir los archivos .cfg encontrados
        for name_file in cfg_files:
            cfg_file= os.path.join(core_folder_path, name_file)
            if "Windows" in name_file:
                key =  "win32"
            elif "Linux" in name_file:
                key = "linux"
            elif "Darwin" in name_file:
                key = "darwin"
            else:
                continue
            with open(cfg_file, 'r') as file:
                content = file.readline().strip()
            python_paths[key]= content
        if python_paths:
            return python_paths
        else:
            message= (
                "Could not get information from cfg files containing the path to"
                "python for different operating systems, \n\n from "
                "\{config\}/hooks/mty-framework-deadline/deadline_utils.py"
            )
            raise Exception(message)

