#####################################################################################
#
# Copyright (c) 2021 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

"""
Hook for ...
"""

from tank import Hook
from tank import TankError

import os
import re
import sys
import time
import pprint
import inspect
import datetime

class ExecuteActionHook(Hook):


    def execute(self, entity_type, entities, other_params, **kwargs):
        """
        Executes an action for the selected entities.

        :param entity_type: ...
        :param entities: ...
        :param other_params: ...
        :returns: dictionary with three values: succes, errors, messages
        """

        self.parent.log_debug("Executing hook...")

        # Queue Manager
        self.parent.log_debug("injecting queue manager in pythonpath")
        if "mty-multi-queue" in self.parent.engine.apps:
            app = self.parent.engine.apps["mty-multi-queue"]
            queueclient_path = os.path.join(app.disk_location, 'python')
            self.parent.log_debug("queueclient_path: %s" % queueclient_path)
            sys.path.append(queueclient_path)
        else:
            self.parent.log_error('Queue Manager app is not present!')

        from tk_multi_queueclient.queue_client import Client

        # first we iterate through the version entities to find out what playlist
        # they belong to. Because a version might be part of more than one playlist,
        # we create a dictionary to first get the coincidences and decide what's
        # the common playlist for the selected versions: the one with more entries
        # should be the opened playlist
        playlists_count = {}
        for version in entities:
            playlists = version["playlists"]
            for playlist in playlists:
                name = playlist.get("name")
                id_ = playlist.get("id")
                if "Editorial" in name or "Direction" in name or "Showcase" in name:
                    if "Breakdown" not in name:
                        if name not in playlists_count.keys():
                            playlists_count[name] = [1, id_]
                        else:
                            playlists_count[name][0] += 1

        self.parent.logger.info(
            "playlists_count:\n{}".format(pprint.pformat(playlists_count))
        )

        # Sort playlists by number of entries
        sorted_playlist_names = []
        for playlist_name in sorted(
            playlists_count, key=playlists_count.get, reverse=True
        ):
            playlist_id = playlists_count[playlist_name][-1]

            # Add playlist ID if is not present in the name of the playlist
            if str(playlist_id) in playlist_name:
                sorted_playlist_names.append(playlist_name)
            else:
                sorted_playlist_names.append(
                    "{} {}".format(
                        playlist_name, str(playlist_id)
                    )
                )

        self.parent.logger.info(
            "sorted_playlist_names:\n{}".format(pprint.pformat(sorted_playlist_names))
        )

        # Keep the first name as is the one with more entries
        playlist_title = sorted_playlist_names[0]

        # split by non alphanumeric characters
        title_split = re.split(r' |_|-|\.|\|', playlist_title)

        # Remove empty strings
        title_split = [word for word in title_split if word]

        # Replace [ _-] with "_"
        target_dir_name = "_".join(title_split)

        # we initialize a general timestamp for all versions
        # and the corresponding editorial folder
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d")
        # join timestamp with playlist name after formatting (target_dir_name)
        # target_dir_name = "{}_{}".format(timestamp, target_dir_name)

        timestamp_folder = os.path.join(
            self.parent.sgtk.project_path, "ProductionArea",
            "Editorial", "Dailies", timestamp, target_dir_name
        )
        self.parent.logger.info("timestamp_folder: {}".format(timestamp_folder))

        if not os.path.exists(timestamp_folder):
            os.makedirs(timestamp_folder)

        succes = []

        # in this list we suppose the entities are Versions
        # and since the ensure file is local needs PublishedFiles
        # we also suppose that those are linked to the Versions
        publishes_ids = {}
        for version in entities:
            for p in version['published_files']:
                publishes_ids[p['id']] = p['name']

        for pub_id, pub_name in publishes_ids.items():
            commands = inspect.cleandoc("""

            import os
            import shutil

            timestamp_folder = "%s"

            pub_path = execute_hook_expression(
                "{config}/ensure_publish_is_local.py",
                "execute", set_progress=set_progress,
                publish_id=%s
            )

            if os.path.exists(pub_path):
                new_path = os.path.join(
                    timestamp_folder,
                    os.path.basename(pub_path)
                )
                shutil.copy(pub_path, new_path)

            """ % (timestamp_folder.replace("\\", "\\\\"), pub_id)
            )

            job_data = {
                'name': 'Sync %s' % pub_name,
                'commands': commands,
                'priority': 50,
                'type': 'Download',
                'color': [255, 0, 255]
            }

            client = Client()
            client.submit_job(job_data)

            succes.append(pub_name)

            time.sleep(1)

        result = {
            'succes': succes,
            'errors': [],
            'messages': [
                'Publish Sync jobs where sent to Queue!'
            ]
        }

        return result
