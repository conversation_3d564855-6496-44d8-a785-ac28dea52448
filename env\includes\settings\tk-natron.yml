# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

################################################################################

includes:
- ../app_locations.yml
- ../engine_locations.yml
- ./tk-multi-loader2.yml
- ./tk-multi-publish2.yml
- ./tk-multi-screeningroom.yml
- ./tk-multi-shotgunpanel.yml
- ./tk-multi-snapshot.yml
- ./tk-multi-workfiles2.yml
- ./tk-multi-breakdown.yml

################################################################################

# asset
settings.tk-natron.asset:
  apps:
    tk-multi-about:
      location: '@apps.tk-multi-about.location'
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel'
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2.natron.project'
  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  debug_logging: false
  location: '@engines.tk-natron.location'
# asset_step
settings.tk-natron.asset_step:
  apps:
    tk-multi-about:
      location: '@apps.tk-multi-about.location'
    tk-multi-breakdown: '@settings.tk-multi-breakdown.natron'
    tk-multi-loader2: '@settings.tk-multi-loader2.natron'
    tk-multi-publish2: '@settings.tk-multi-publish2.natron.asset_step'
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel'
    tk-multi-snapshot: '@settings.tk-multi-snapshot.natron.asset_step'
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2.natron.asset_step'
  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-snapshot, name: Snapshot...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  - {app_instance: tk-multi-publish2, name: Publish...}
  template_project: asset_work_area_natron
  location: '@engines.tk-natron.location'
# project
settings.tk-natron.project:
  apps:
    tk-multi-about:
      location: '@apps.tk-multi-about.location'
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel'
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2.natron.project'
    mty-executeaction-ensure-folders:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Ensure Tasks Folders"
      action_hook: "{config}/tk-shotgun-executeaction/create_folders_for_assigned_tasks.py"
      sg_extended_fields: {}
      allowed_entities: ['Shot', 'Sequence', 'Asset', 'Project']
  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  debug_logging: true
  location: '@engines.tk-natron.location'
# sequence
settings.tk-natron.sequence:
  apps:
    tk-multi-about:
      location: '@apps.tk-multi-about.location'
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel'
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2.natron.project'
  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  location: '@engines.tk-natron.location'
# shot
settings.tk-natron.shot:
  apps:
    tk-multi-about:
      location: '@apps.tk-multi-about.location'
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel'
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2.natron.project'
  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  location: '@engines.tk-natron.location'
# shot_step
settings.tk-natron.shot_step:
  apps:
    tk-multi-about:
      location: '@apps.tk-multi-about.location'
    tk-multi-breakdown: '@settings.tk-multi-breakdown.natron'
    tk-multi-setframerange:
      #location: "@apps.tk-multi-setframerange.location"
      # location:
      #   type: dev
      #   path: "C:/Users/<USER>/Google Drive/Development/factor64/tk-multi-setframerange"
      location:
        path: https://github.com/diegogarciahuerta/tk-multi-setframerange
        version: b24a9773fdd4636c10b3bd6229b2e90d8f75eb52
        type: git_branch
        branch: enable_hooks_for_app
      hook_frame_operation: '{engine}/tk-multi-setframerange/frame_operations_tk-natron.py'
    tk-multi-loader2: '@settings.tk-multi-loader2.natron'
    tk-multi-publish2: '@settings.tk-multi-publish2.natron.shot_step'
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel.natron'
    tk-multi-snapshot: '@settings.tk-multi-snapshot.natron.shot_step'
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2.natron.shot_step'
  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-snapshot, name: Snapshot...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  - {app_instance: tk-multi-publish2, name: Publish...}
  template_project: shot_work_area_natron
  location: '@engines.tk-natron.location'
