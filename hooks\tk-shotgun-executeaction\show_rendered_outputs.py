#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

"""
An app that fixes amn image (read) node's scale and proportions, by setting the right
parameters in the node properties windows. Basically replicates the import options
when using "Project Resolution" in the alignment rules when importing image as bitmap.
Also adds the proper scaling in the transformation tab

"""

from tank import Hook
from tank import TankError
from tank.platform import Application
from tank.platform.qt import QtCore, QtGui
import tank

import os
import re
import json
import time
import pprint
import tempfile


pp = pprint.pprint
pf = pprint.pformat


QFont = QtGui.QFont
QLabel = QtGui.QLabel
QColor = QtGui.QColor
QDialog = QtGui.QDialog
QHeaderView = QtGui.QHeaderView
QPushButton = QtGui.QPushButton
QVBoxLayout = QtGui.QVBoxLayout
QTableWidget = QtGui.QTableWidget
QApplication = QtGui.QApplication
QTableWidgetItem = QtGui.QTableWidgetItem


META_SHOTGUN_PATH = "meta.shotgun.path"


class ProcessItemsHook(Hook):
    def execute(self, **kwargs):
        self.parent.engine.logger.info(
            "execute action show_rendered_outputs start. {}".format("-" * 80)
        )

        output_rendered_dict = self.find_info_about_current_shot() or {}

        self.parent.engine.logger.info(
            "rendered_outputs_dict:\n{}".format(pf(output_rendered_dict))
        )
        if output_rendered_dict:
            self.show_rendered_outputs_dialog(output_rendered_dict)
        else:
            self.no_info_windows()

        self.parent.engine.logger.info(
            "execute action show_rendered_outputs end. {}".format("-" * 80)
        )

        return {"succes": [1], "messages": [], "errors": []}

    def find_info_about_current_shot(self):
        sg_proj = self.parent.engine.context.project
        shotgun = self.parent.engine.shotgun
        current_entity = self.parent.engine.context.entity

        filters = [
            ["project", "is", sg_proj],
            ["id", "is", current_entity["id"]],
        ]
        fields = ["id", "name", "sg_rendered_outputs"]
        result = shotgun.find_one("Shot", filters, fields)
        rendered_outputs = result.get("sg_rendered_outputs",None)

        if rendered_outputs:
            # convert the content to dict
            rendered_outputs = json.loads(rendered_outputs)

        return rendered_outputs

    def show_rendered_outputs_dialog(self, rendered_outputs):
        """Shows a dialog with the rendered outputs in the shot"""

        dialog = QDialog()
        layout = QVBoxLayout()
        table = QTableWidget()

        table.setColumnCount(2)  # Adjusted for 2 columns now
        table.setHorizontalHeaderLabels(["Task", "Output Name"])
        current_shot_name = self.parent.engine.context.entity["name"]

        row = 0

        for task, outputs in rendered_outputs.items():
            table.insertRow(row)
            task_item = QTableWidgetItem(task)

            # Qt 5.x code (PySide2)
            if QtCore.__version__.startswith("5."):
                task_item.setFlags(task_item.flags() ^ 0x0004)

            # Qt 6.x code (PySide6)
            elif QtCore.__version__.startswith("6."):
                task_item.setFlags(
                    task_item.flags() & ~QtCore.Qt.ItemFlag.ItemIsEditable
                )

            task_item_font = task_item.font()
            task_item_font.setBold(True)
            task_item.setFont(task_item_font)
            table.setItem(row, 0, task_item)
            table.setSpan(row, 0, 1, 4)
            row += 1

            for output in outputs:
                table.insertRow(row)
                output_item = QTableWidgetItem(output)

                # Qt 5.x code (PySide2)
                if QtCore.__version__.startswith("5."):
                    output_item.setFlags(output_item.flags() ^ 0x0004)

                # Qt 6.x code (PySide6)
                elif QtCore.__version__.startswith("6."):
                    output_item.setFlags(
                        output_item.flags() & ~QtCore.Qt.ItemFlag.ItemIsEditable
                    )

                # Set background color for 'Type' column
                output_item.setBackground(QColor(60, 60, 60))
                table.setItem(row, 1, output_item)
                row += 1

        table.resizeColumnsToContents()
        table.horizontalHeader().setStretchLastSection(True)

        # Auto-resizing vertically
        table.verticalHeader().setSectionResizeMode(QHeaderView.ResizeToContents)
        table.verticalHeader().setMaximumSectionSize(1000)  # Limiting maximum height

        # Adjusting widget size
        table.setMinimumWidth(300)  # Minimum width
        table.setMinimumHeight(250)  # Minimum height
        table.setMaximumWidth(1900)  # Maximum width
        table.setMaximumHeight(1000)  # Maximum height

        ok_button = QPushButton("OK")
        ok_button.clicked.connect(dialog.accept)

        layout.addWidget(table)
        layout.addWidget(ok_button)

        dialog.setLayout(layout)
        dialog.adjustSize()
        dialog.setWindowTitle("{} Rendered Outputs".format(current_shot_name))

        dialog.exec_()

    def no_info_windows(self):
        dialog = QDialog()

        dialog.adjustSize()
        dialog.setWindowTitle('Shot Rendered Outputs')

        layout = QVBoxLayout()
        label = QLabel("There is no information about rendered outputs yet")

        layout.addWidget(label)
        dialog.setLayout(layout)

        dialog.exec_()
