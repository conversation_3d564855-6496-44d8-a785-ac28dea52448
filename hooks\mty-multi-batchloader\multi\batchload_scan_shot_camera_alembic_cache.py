########################################################################################
#
# Copyright (c) 2024 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
########################################################################################

import os
import re
import sys
import json
import traceback
from tank import Hook
from html import entities
from pprint import pformat
from tank.platform.qt import QtCore, QtGui


class ScanSceneHook(Hook):

    def scan_scene(self):

        self.parent.engine.logger.info(
            "Scanning publishes for Shot Camera Alembic Cache...".ljust(120, "-")
        )

        items_result = []
        warnings = []
        errors = []

        context = self.parent.context
        context_shot_id = context.entity["id"]
        context_shot_name = context.entity["name"]
        context_task = context.to_dict().get("task", {})
        context_task_name = context_task.get("name")

        # first load the all settings from the config settings as fallback in case of
        # missing value overrides
        supported_tasks_for_loading_shot_camera_cache = self.parent.get_setting(
            "supported_tasks_for_loading_shot_camera_cache"
        )
        self.parent.engine.logger.info(
            "supported_tasks_for_loading_shot_camera_cache from hook settings: {}".format(
                supported_tasks_for_loading_shot_camera_cache
            )
        )

        shot_camera_cache_task_priority_list = self.parent.get_setting(
            "shot_camera_cache_task_priority_list"
        )
        self.parent.engine.logger.info(
            "shot_camera_cache_task_priority_list from hook settings: {}".format(
                shot_camera_cache_task_priority_list
            )
        )

        shot_camera_cache_published_file_type_names = self.parent.get_setting(
            "shot_camera_cache_published_file_type_names"
        )
        self.parent.engine.logger.info(
            "shot_camera_cache_published_file_type_names from hook settings: {}".format(
                shot_camera_cache_published_file_type_names
            )
        )

        # Load value overrides framework -----------------------------------------------
        valueoverrides = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        ) or self.load_framework("mty-framework-valueoverrides")

        if not valueoverrides:
            self.parent.engine.logger.error("Couldn't load value overrides framework")

        # try to load the settings from value overrides
        if valueoverrides:
            # Get task priority list
            override_link = {
                "type": "Task",
                "id": self.parent.engine.context.task["id"],
            }

            default_value_code = (
                "mty.multi.batchloader.scan.supported_tasks_for_loading_shot_camera_cache"
            )
            supported_tasks_for_loading_shot_camera_cache_value = valueoverrides.get_value(
                default_value_code, link=override_link
            )
            if supported_tasks_for_loading_shot_camera_cache_value:
                supported_tasks_for_loading_shot_camera_cache = json.loads(
                    supported_tasks_for_loading_shot_camera_cache_value
                )
                self.parent.engine.logger.info(
                    "supported_tasks_for_loading_shot_camera_cache from override: {}".format(
                        supported_tasks_for_loading_shot_camera_cache
                    )
                )

            default_value_code = (
                "mty.multi.batchloader.scan.shot_camera_cache_task_priority_list"
            )
            shot_camera_cache_task_priority_list_value = valueoverrides.get_value(
                default_value_code, link=override_link
            )
            if shot_camera_cache_task_priority_list_value:
                shot_camera_cache_task_priority_list = json.loads(
                    shot_camera_cache_task_priority_list_value
                )
                self.parent.engine.logger.info(
                    "shot_camera_cache_task_priority_list from override: {}".format(
                        shot_camera_cache_task_priority_list
                    )
                )

            default_value_code = (
                "mty.multi.batchloader.scan.shot_camera_cache_published_file_type_names"
            )
            shot_camera_cache_published_file_type_names_value = valueoverrides.get_value(
                default_value_code, link=override_link
            )
            if shot_camera_cache_published_file_type_names_value:
                shot_camera_cache_published_file_type_names = json.loads(
                    shot_camera_cache_published_file_type_names_value
                )
                self.parent.engine.logger.info(
                    "shot_camera_cache_published_file_type_names from override: {}".format(
                        shot_camera_cache_published_file_type_names
                    )
                )

        self.parent.engine.logger.info(
            "Finished loading settings, either from hook settings or value overrides"
        )

        if context_task_name not in supported_tasks_for_loading_shot_camera_cache:
            msg = (
                "Task {} not in supported tasks list for loading shot camera caches"
            ).format(context_task_name)
            self.parent.engine.logger.warning(msg)
            warnings.append(msg)
            items_result.append({})
            return items_result, warnings, errors
        else:
            self.parent.engine.logger.info(
                (
                    "Task {} found in supported tasks list for loading shot camera "
                    "caches"
                ).format(context_task_name)
            )

        # if the current context task is in the supported tasks list, find all the shot
        #  camera caches published files linked to the current shot --------------------
        filters = [
            ["entity", "is", {"type": "Shot", "id": context_shot_id}],
            ["entity.Shot.id", "is", context_shot_id],
            ["entity.Shot.code", "is", context_shot_name],
            ["task.Task.content", "in", shot_camera_cache_task_priority_list],
            ["sg_status_list", "is", "apr"],
            [
                "published_file_type.PublishedFileType.code",
                "in",
                shot_camera_cache_published_file_type_names,
            ],
        ]

        self.parent.engine.logger.debug("Published files filters:\n{}".format(pformat(filters)))

        fields = [
            "code",
            "name",
            "published_file_type",
            "version_number",
            "task",
            "path",
            "id",
            "entity.Shot.code",
        ]

        published_files_list = self.parent.shotgun.find(
            "PublishedFile", filters, fields
        )

        if not published_files_list:
            msg = "No published files found for task(s) {} in shot {}".format(
                shot_camera_cache_task_priority_list, shot_name
            )
            self.parent.engine.logger.warning(msg)
            warnings.append(msg)
            items_result.append({})
            return items_result, warnings, errors

        # Filter by task priority --------------------------------------------------
        publishes_dict = {}
        for published_file in published_files_list:
            shot_name = published_file.get("entity.Shot.code")
            task_name = published_file.get("task", {}).get("name")
            version_number = published_file.get("version_number", 1)

            if not shot_name:
                self.parent.logger.error(
                    "Couldn't get shot name for publish:\n{}".format(
                        pformat(published_file)
                    )
                )
                continue

            if task_name not in publishes_dict.keys():
                publishes_dict[task_name] = {version_number: [published_file]}
            else:
                if version_number not in publishes_dict[task_name].keys():
                    publishes_dict[task_name][version_number] = [published_file]
                else:
                    publishes_dict[task_name][version_number].append(published_file)

        # build short dictionary exclusivrly for logging
        publishes_dict_short = {}
        for task in publishes_dict.keys():
            # if task not in publishes_dict_short:
            publishes_dict_short[task] = {
                "Versions": list(publishes_dict[task].keys())
            }
            # else:
            #     publishes_dict_short.append(task)

        self.parent.logger.info(
            "Shot camera cache publishes_dict_short (for full dict turn on debug):\n{}".format(
                pformat(publishes_dict_short)
            )
        )
        self.parent.logger.debug(
            "Shot camera cache publishes_dict:\n{}".format(pformat(publishes_dict))
        )

        # get the latest version available, having in account the task priority list, so
        # only one item will be added to the items_result list: the highest version of
        # the highest priority task
        for task in shot_camera_cache_task_priority_list:
            if publishes_dict.get(task, None):
                versions = list(publishes_dict[task].keys())
                latest_version = max(versions)
                latest_publish = publishes_dict[task][latest_version][0]

                path = latest_publish["path"]["local_path"]

                items_result.append(
                    {
                        "node": "{}_camera_alembic_cache".format(shot_name),
                        "path": path,
                        "process_hook": "batchload_process_shot_camera_alembic_cache",
                        "type": "Shot Camera Alembic Cache",
                        "sg_data": latest_publish,
                        "other_params": {"namespace": shot_name},
                    }
                )
                break

        # Ensure metasync framework is available, but only load it once.
        if not hasattr(self, "metasync"):
            self.metasync = self.parent.engine.custom_frameworks.get(
                "mty-framework-metasync"
            )
        if not self.metasync:
            self.metasync = self.load_framework("mty-framework-metasync")

        transfersManager = self.metasync.transfersManager

        for item in items_result:
            local_path = item.get("path", "")
            if not os.path.exists(local_path):
                self.parent.logger.warning(
                    "Shot camera alembic cache file not found on disk: {}".format(
                        local_path
                    )
                )
                self.parent.logger.info(
                    "Trying to download published file:\n{}".format(
                        pformat(item["sg_data"])
                    )
                )

                counter = 0
                max_download_tries = 3
                while counter < max_download_tries:
                    try:
                        transfersManager.ensure_file_is_local(
                            local_path,
                            item.get("sg_data", {}),
                            sound_notifications=False,
                        )
                        break
                    except:
                        error_str = traceback.format_exc()
                        errors.append(error_str)
                        self.parent.logger.error(
                            "Failed to download file:\n{}".format(error_str)
                        )
                        counter += 1

                counter = 0
                max_download_tries = 3
                while counter < max_download_tries:
                    try:
                        transfersManager.ensure_local_dependencies(
                            item.get("sg_data", {})
                        )
                        break
                    except:
                        error_str = traceback.format_exc()
                        errors.append(error_str)
                        self.parent.logger.error(
                            "Failed to download dependencies:\n{}".format(error_str)
                        )
                        counter += 1

        self.parent.logger.debug("items_result:\n{}".format(pformat(items_result)))

        return items_result, warnings, errors
