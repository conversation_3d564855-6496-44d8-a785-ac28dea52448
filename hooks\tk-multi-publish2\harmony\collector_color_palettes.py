########################################################################################
#
# Copyright (c) 2024 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio.
########################################################################################

import os
import re
import sys
import json
import pprint
import shutil

import sgtk

HookBaseClass = sgtk.get_hook_baseclass()


pp = pprint.pprint
pf = pprint.pformat

class HarmonyColorPalettesCollector(HookBaseClass):
    """
    Collector that operates on the Toon Boom Harmony session. Should inherit
    from the basic collector hook.
    """

    @property
    def settings(self):
        """
        Dictionary defining the settings that this collector expects to receive
        through the settings parameter in the process_current_session and
        process_file methods.
        A dictionary on the following form::
        {
            "Settings Name": {
                "type": "settings_type",
                "default": "default_value",
                "description": "One line description of the setting"
        }
        The type string should be one of the data types that toolkit accepts as
        part of its environment configuration.
        """

        # grab any base class settings
        collector_settings = super(HarmonyColorPalettesCollector, self).settings or {}

        # settings specific to this collector
        harmony_session_settings = {
            "Publish Template": {
                "type": "template",
                "default": None,
                "description": "Harmony session publish path template",
            },
            "Work Template": {
                "type": "template",
                "default": None,
                "description": "Hamrony session work template",
            },
            "Publish Color Palette Template": {
                "type": "template",
                "default": None,
                "description": "Template path where the files will be published",
            },
            "Color Palettes Allowed Tasks": {
                "type": "list",
                "default": None,
                "description": "List of tasks that will collect color palettes"
            },
            "Color Palettes Allowed Contexts": {
                "type": "list",
                "default": None,
                "description": "List of entity types that will collect color palettes"
            },
        }

        # update the base settings with these settings
        collector_settings.update(harmony_session_settings)

        return collector_settings

    def process_current_session(self, settings, parent_item):
        """
        Analyzes the current session open in Toon Boom Harmony and parents a
        subtree of items under the parent_item passed in.
        :param dict settings: Configured settings for this collector
        :param parent_item: Root item instance
        """

        super(HarmonyColorPalettesCollector, self).process_current_session(settings, parent_item)

        item = next(
            (i for i in parent_item.descendants if i.type_spec.endswith(".session")),
            None,
        )
        if item is None:
            item = self.collect_current_harmony_session(settings, parent_item)

        context = self.parent.engine.context

        # Load overrides framework -----------------------------------------------------
        valueoverrides = self.parent.engine.custom_frameworks.get(
                'mty-framework-valueoverrides'
            ) or self.load_framework('mty-framework-valueoverrides')

        # first check if we are at asset context, otherwise, skip the collector

        # get color_palettes_allowed_contexts, either from overrides or from settings
        if valueoverrides:
            value_code = 'mty.publisher.harmony.collector_color_palettes.color_palettes_allowed_contexts'
            data = valueoverrides.get_value(
                value_code, link={"type": "Task", "id": context.task["id"]}
            )

            color_palettes_allowed_contexts = json.loads(data)
            self.parent.engine.logger.info(
                "Got color_palettes_allowed_contexts from overrides: {}".format(
                    color_palettes_allowed_contexts
                )
            )
        else:
            color_palettes_allowed_contexts = settings.get("Color Palettes Allowed Tasks").value
            self.parent.engine.logger.info(
                "Got color_palettes_allowed_contexts from settings: {}".format(
                    color_palettes_allowed_contexts
                )
            )

        current_entity_type = context.entity.get("type")
        self.parent.engine.logger.info("current_context: {}".format(current_entity_type))

        if current_entity_type not in color_palettes_allowed_contexts:
            self.parent.engine.logger.warning(
                f"{current_entity_type} context is not allowed for publishing color palettes, skipping collector"
            )
            return

        current_task_name = context.task["name"]
        self.parent.engine.logger.info("current_task: {}".format(current_task_name))

        # get color_palettes_allowed_taks, either from overrides or from settings
        if valueoverrides:
            value_code = 'mty.publisher.harmony.collector_color_palettes.color_palettes_allowed_tasks'
            data = valueoverrides.get_value(
                value_code, link={"type": "Task", "id": context.task["id"]}
            )

            color_palettes_allowed_tasks = json.loads(data)
            self.parent.engine.logger.info(
                "Got color_palettes_allowed_tasks from overrides: {}".format(
                    color_palettes_allowed_tasks
                )
            )
        else:
            color_palettes_allowed_tasks = settings.get("Color Palettes Allowed Tasks").value
            self.parent.engine.logger.info(
                "Got color_palettes_allowed_tasks from settings: {}".format(
                    color_palettes_allowed_tasks
                )
            )

        if current_task_name in color_palettes_allowed_tasks:
            all_color_palette_items = self._collect_color_palettes(settings, item)

            self.parent.engine.logger.info("all_color_palette_items:")
            for item in all_color_palette_items:
                self.parent.engine.logger.info(
                    f"{item.name}:\n"
                    f"{pf(item.properties.to_dict())}"
                )
        else:
            self.parent.engine.logger.warning(
                f"{current_task_name} task is not allowed for publishing color palettes, skipping collector"
            )

    def fix_path(self, path):
        path = path.replace('\\', '/')
        path = path.replace('\\\\', '/')

        return path

    def fix_palette_name(self, palette_name, palette_prefix):
        """
        Fixes the extra suffix of a palette: removes extra characters and converts
        suffix to lowerCamelCase to avoid conflicts with the publish template. For
        example: "{Asset}_color_palette_extra_prop" -> "{Asset_}color_palette_extraProp"

        :param str palette_name: Name of the palette (only suffix)
        :param str palette_prefix: Prefix of the palette: {Asset}_color_palette_

        :return: Fixed name of the palette in lowerCamelCase (prefix + suffix)
        """

        palette_name_parts = re.split(r"[-_]", palette_name)
        if len(palette_name_parts) > 1:
            first_word = palette_name_parts[0]
            rest_of_words = "".join(x.capitalize() for x in palette_name_parts[1:])
            palette_name = f"{palette_prefix}{first_word}{rest_of_words}"
        else:
            palette_name = f"{palette_prefix}{palette_name}"

        return palette_name

    def _collect_color_palettes(self, settings, parent_item):
        self.parent.engine.logger.info("Collecting color palette files...")

        asset_name = self.parent.engine.context.entity["name"]
        asset_palette_prefix = f"{asset_name}_color_palette_"

        get_session_color_palettes_cmd = """
include("harmony_utility_functions.js");

var log = MessageLog.trace;

var asset_palette_prefix = "%s";
// var asset_color_palettes = get_asset_scene_palettes(asset_palette_prefix);

// for(var i=0; i < asset_color_palettes.length; ++i) {
    //     var palette = asset_color_palettes[i];
    //     log(palette.getName());
    // }

get_asset_scene_palettes(asset_palette_prefix);

""" % asset_palette_prefix

        self.parent.engine.logger.debug(
            "full get_scene_palettes cmd:\n{}".format(get_session_color_palettes_cmd)
        )
        asset_scene_palettes = self.parent.engine.app.custom_script(
            get_session_color_palettes_cmd
        )

        if not asset_scene_palettes:
            return None

        self.parent.engine.logger.info(
            f"asset_scene_palettes:\n{pf(asset_scene_palettes)}"
        )

        config_path = os.path.dirname(os.path.dirname(self.disk_location))
        publish_icons = os.path.join(config_path, "tk-multi-publish2", "icons")
        icon_path = os.path.join(publish_icons, "color_palette.png")

        # templates section ------------------------------------------------------------
        # get templates from settings
        publish_session_setting = settings.get("Publish Template").value
        work_template_setting = settings.get("Work Template").value
        publish_palette_setting = settings.get("Publish Color Palette Template").value

        # Templates
        work_template = self.parent.engine.get_template_by_name(work_template_setting)
        publish_session_template = self.parent.engine.get_template_by_name(publish_session_setting)
        publish_color_palette_template = self.parent.engine.get_template_by_name(publish_palette_setting)

        # Adding extra element in work_fields
        current_project_path = self.parent.engine.app.get_current_project_path()
        work_fields = work_template.get_fields(current_project_path)

        # create items -----------------------------------------------------------------
        all_color_palette_items = []
        for palette in asset_scene_palettes.keys():
            self.parent.logger.info(f"Working with palette: {palette}")
            original_palette_suffix = palette.replace(asset_palette_prefix, "")
            fixed_palette_name = self.fix_palette_name(
                original_palette_suffix, asset_palette_prefix
            )
            fixed_palette_suffix = fixed_palette_name.replace(asset_palette_prefix, "")
            display_name = f"{fixed_palette_suffix} Color Palette"

            # get publish path using current session fields
            work_fields["color_palette_identifier"] = fixed_palette_suffix
            publish_palette_path = publish_color_palette_template.apply_fields(work_fields)
            publish_palette_path = self.fix_path(publish_palette_path)

            color_palette_item = parent_item.create_item(
                "harmony.color_palette", "Collects Color Palette Files", display_name
            )

            color_palette_item.set_icon_from_path(icon_path)

            color_palette_item.properties["original_palette_name"] = palette
            color_palette_item.properties["original_palette_suffix"] = original_palette_suffix
            color_palette_item.properties["fixed_palette_name"] = fixed_palette_name
            color_palette_item.properties["fixed_palette_suffix"] = fixed_palette_suffix
            color_palette_item.properties["color_palette_obj"] = asset_scene_palettes[palette]["palette_obj"]
            color_palette_item.properties["color_palette_path"] = asset_scene_palettes[palette]["palette_path"]
            color_palette_item.properties["publish_path"] = publish_palette_path
            color_palette_item.properties["work_fields"] = work_fields
            color_palette_item.properties["template_session_publish"] = publish_session_template

            all_color_palette_items.append(color_palette_item)

        return all_color_palette_items
