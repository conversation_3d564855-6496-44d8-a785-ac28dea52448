########################################################################################
#
# Copyright (c) 2024 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
########################################################################################

import os
import sys
import json
import traceback
from tank import Hook
from html import entities
from pprint import pformat
from tank.platform.qt import QtCore, QtGui


class ScanSceneHook(Hook):

    def scan_scene(self):

        self.parent.engine.logger.info(
            "Scanning scene for 3D shaded assets...".ljust(120, "-")
        )

        items_result = []
        warnings = []
        errors = []

        context = self.parent.context
        shot_id = context.entity["id"]
        task = context.to_dict().get("task", {})
        task_name = context.to_dict().get("task", {}).get("name")

        # Find the assets linked to the current shot -----------------------------------
        filters = [["id", "is", shot_id]]
        fields = ["assets", "sg_breakdowns"]
        data_shot = self.parent.shotgun.find_one("Shot", filters, fields)

        self.parent.engine.logger.debug("data_shot:\n{}".format(pformat(data_shot)))

        asset_ids = [asset["id"] for asset in data_shot["assets"]]

        if not asset_ids:
            msg = (
                "No assets found for the current shot. "
                "Please contact your supervisor or production coordinator."
            )
            self.parent.engine.logger.error(msg)
            errors.append(msg)
            return items_result, warnings, errors

        # query shot breakdowns --------------------------------------------------------
        self.parent.engine.logger.info("Querying shot breakdowns...")

        breakdown_ids = None
        if data_shot.get("sg_breakdowns"):
            breakdown_ids = [
                breakdown["id"] for breakdown in data_shot["sg_breakdowns"]
            ]
        if not breakdown_ids:
            msg = (
                "The current shot doesn't have any breakdowns. "
                "Please contact your supervisor or production coordinator."
            )
            self.parent.engine.logger.error(msg)
            errors.append(msg)
            items_result.append({})
            return items_result, warnings, errors

        filters = [
            ["sg_shot", "is", {"id": shot_id, "type": "Shot"}],
            ["id", "in", breakdown_ids]
        ]
        fields = ["code", "sg_asset", "sg_asset.Asset.code", "sg_shot"]
        # fields = ["code", "sg_asset.Asset.code"]

        self.parent.engine.logger.debug(
            "breakdowns filters:\n{}".format(pformat(filters))
        )

        try:
            # CustomEntity30 correspond to the shot breakdown entity
            shot_breakdowns = self.parent.shotgun.find(
                "CustomEntity30", filters, fields
            )
        except Exception as e:
            msg = "Couldn't query shot breakdowns: {}, full traceback: {}".format(
                e, traceback.format_exc()
            )
            self.parent.engine.logger.error(msg)
            errors.append(msg)
            items_result.append({})
            return items_result, warnings, errors

        if not shot_breakdowns:
            msg = (
                "Couldn't query any shot breakdowns. "
                "Please contact your supervisor or production coordinator."
            )
            self.parent.engine.logger.error(msg)
            errors.append(msg)
            items_result.append({})
            return items_result, warnings, errors

        self.parent.engine.logger.info(
            "shot_breakdowns:\n{}".format(pformat(shot_breakdowns))
        )

        breakdowns_dict = {}

        for breakdown in shot_breakdowns:
            breakdown_asset = breakdown["sg_asset.Asset.code"]
            if breakdown_asset not in breakdowns_dict.keys():
                breakdowns_dict[breakdown_asset] = {"namespaces": []}

            breakdowns_dict[breakdown_asset]["namespaces"].append(
                breakdown["code"]
            )

        self.parent.engine.logger.info(
            "breakdowns_dict:\n{}".format(pformat(breakdowns_dict))
        )

        # Load value overrides framework -----------------------------------------------
        valueoverrides = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        ) or self.load_framework("mty-framework-valueoverrides")

        if not valueoverrides:
            self.parent.engine.logger.error("Couldn't load value overrides framework")

        # first load the all settings from the config settings as fallback in case of
        # missing value overrides
        supported_tasks_for_loading_shaded_assets = self.parent.get_setting(
            "supported_tasks_for_loading_shaded_assets"
        )
        self.parent.engine.logger.info(
            "supported_tasks_for_loading_shaded_assets from hook settings: {}".format(
                supported_tasks_for_loading_shaded_assets
            )
        )

        shaded_assets_task_priority_list = self.parent.get_setting(
            "shaded_assets_task_priority_list"
        )
        self.parent.engine.logger.info(
            "shaded_assets_task_priority_list from hook settings: {}".format(
                shaded_assets_task_priority_list
            )
        )

        shaded_3d_asset_type = self.parent.get_setting("shaded_3d_asset_type")
        self.parent.engine.logger.info(
            "shaded_3d_asset_type from hook settings: {}".format(
                shaded_3d_asset_type
            )
        )

        shaded_assets_published_file_type_names = self.parent.get_setting(
            "shaded_assets_published_file_type_names"
        )
        self.parent.engine.logger.info(
            "shaded_assets_published_file_type_names from hook settings: {}".format(
                shaded_assets_published_file_type_names
            )
        )

        if valueoverrides:
            # Get task priority list
            override_link = {
                "type": "Task",
                "id": self.parent.engine.context.task["id"],
            }

            default_value_code = (
                "mty.multi.batchloader.scan.supported_tasks_for_loading_shaded_assets"
            )
            supported_tasks_for_loading_shaded_assets = valueoverrides.get_value(
                default_value_code, link=override_link
            )
            if supported_tasks_for_loading_shaded_assets:
                supported_tasks_for_loading_shaded_assets = json.loads(
                    supported_tasks_for_loading_shaded_assets
                )
                self.parent.engine.logger.info(
                    "supported_tasks_for_loading_shaded_assets from override: {}".format(
                        supported_tasks_for_loading_shaded_assets
                    )
                )

            default_value_code = (
                "mty.multi.batchloader.scan.shaded_assets_task_priority_list"
            )
            shaded_assets_task_priority_list = valueoverrides.get_value(
                default_value_code, link=override_link
            )
            if shaded_assets_task_priority_list:
                shaded_assets_task_priority_list = json.loads(
                    shaded_assets_task_priority_list
                )
                self.parent.engine.logger.info(
                    "shaded_assets_task_priority_list from override: {}".format(
                        shaded_assets_task_priority_list
                    )
                )

            default_value_code = "mty.multi.batchloader.scan.shaded_3d_asset_type"
            shaded_3d_asset_type = valueoverrides.get_value(
                default_value_code, link=override_link
            )
            if shaded_3d_asset_type:
                shaded_3d_asset_type = json.loads(shaded_3d_asset_type)
                self.parent.engine.logger.info(
                    "shaded_3d_asset_type from override: {}".format(
                        shaded_3d_asset_type
                    )
                )

            default_value_code = (
                "mty.multi.batchloader.scan.shaded_assets_3d_published_file_type_names"
            )
            shaded_assets_published_file_type_names = valueoverrides.get_value(
                default_value_code, link=override_link
            )
            if shaded_assets_published_file_type_names:
                shaded_assets_published_file_type_names = json.loads(
                    shaded_assets_published_file_type_names
                )
                self.parent.engine.logger.info(
                    "shaded_assets_published_file_type_names from override: {}".format(
                        shaded_assets_published_file_type_names
                    )
                )

        # Decide if we need to scan shaded assets depending on the supported tasks
        if task_name not in supported_tasks_for_loading_shaded_assets:
            items_result.append({})
            return items_result, warnings, errors

        filters = [
            ["entity.Asset.sg_asset_type", "in", shaded_3d_asset_type],
            ["entity.Asset.id", "in", asset_ids],
            ["task.Task.content", "in", shaded_assets_task_priority_list],
            ["sg_status_list", "is", "apr"],
            [
                "published_file_type.PublishedFileType.code",
                "in",
                shaded_assets_published_file_type_names,
            ],
        ]

        fields = [
            "code",
            "name",
            "published_file_type",
            "version_number",
            "task",
            "path",
            "id",
            "entity.Asset.code",
        ]

        published_files_list = self.parent.shotgun.find(
            "PublishedFile", filters, fields
        )

        if not published_files_list:
            items_result.append({})
            return items_result, warnings, errors

        # Filter by task priority --------------------------------------------------
        publishes_dict = {}
        for published_file in published_files_list:
            asset_name = published_file.get("entity.Asset.code", None)
            task_name = published_file.get("task", {}).get("name", None)
            version_number = published_file.get("version_number", 1)

            if not asset_name:
                self.parent.logger.error(
                    "Couldn't get asset name for publish:\n{}".format(
                        pformat(published_file)
                    )
                )
                continue

            if asset_name not in publishes_dict.keys():
                publishes_dict[asset_name] = {
                    task_name: {version_number: [published_file]}
                }
            else:
                asset_dict = publishes_dict[asset_name]
                if task_name in asset_dict.keys():
                    if version_number in asset_dict[task_name].keys():
                        asset_dict[task_name][version_number].append(published_file)
                    else:
                        asset_dict[task_name][version_number] = [published_file]
                else:
                    asset_dict[task_name] = {version_number: [published_file]}

        # build short dictionary exclusivrly for logging
        publishes_dict_short = {}
        for shot_name_ in publishes_dict.keys():
            if shot_name_ not in publishes_dict_short.keys():
                publishes_dict_short[shot_name_] = []
            for task in publishes_dict[shot_name_].keys():
                publishes_dict_short[shot_name_].append(task)

        self.parent.logger.info(
            "Shaded assets publishes_dict_short (for full dict turn on debug):\n{}".format(
                pformat(publishes_dict_short)
            )
        )

        self.parent.logger.debug(
            "Shaded assets publishes_dict:\n{}".format(pformat(publishes_dict))
        )

        # Order by version number --------------------------------------------------
        for asset_name in publishes_dict.keys():
            if asset_name not in breakdowns_dict.keys():
                msg = (
                    "Couldn't find any breakdowns for asset: {}. "
                    "Please contact your supervisor or production coordinator."
                ).format(asset_name)
                self.parent.logger.error(msg)
                errors.append(msg)
                continue
            for task in shaded_assets_task_priority_list:
                if publishes_dict[asset_name].get(task, None):
                    versions = list(publishes_dict[asset_name][task].keys())
                    latest_version = max(versions)
                    version_publishes_list = publishes_dict[asset_name][task][latest_version]

                    for publish in version_publishes_list:
                        # node = publish["name"]
                        path = publish["path"]["local_path"]

                        asset_namespaces = breakdowns_dict.get(
                            asset_name, {}
                        ).get("namespaces", [])

                        items_result.append(
                            {
                                # "node": node,
                                "node": "{}_shaded".format(asset_name),
                                "path": path,
                                "process_hook": "batchload_process_shaded_assets",
                                "type": "Shaded Assets",
                                "sg_data": publish,
                                "other_params": {"namespaces": asset_namespaces},
                            }
                        )
                    break

        # Ensure metasync framework is available, but only load it once.
        if not hasattr(self, "metasync"):
            self.metasync = self.parent.engine.custom_frameworks.get(
                "mty-framework-metasync"
            )
        if not self.metasync:
            self.metasync = self.load_framework("mty-framework-metasync")

        transfersManager = self.metasync.transfersManager

        for item in items_result:
            local_path = item.get("path", "")
            if not os.path.exists(local_path):
                self.parent.logger.warning(
                    "Shaded asset file not found on disk: {}".format(local_path)
                )
                self.parent.logger.info(
                    "Trying to download published file:\n{}".format(
                        pformat(item["sg_data"])
                    )
                )

                counter = 0
                max_download_tries = 3
                while counter < max_download_tries:
                    try:
                        transfersManager.ensure_file_is_local(
                            local_path,
                            item.get("sg_data", {}),
                            sound_notifications=False,
                        )
                        break
                    except:
                        error_str = traceback.format_exc()
                        errors.append(error_str)
                        self.parent.logger.error(
                            "Failed to download file:\n{}".format(error_str)
                        )
                        counter += 1

                counter = 0
                max_download_tries = 3
                while counter < max_download_tries:
                    try:
                        transfersManager.ensure_local_dependencies(
                            item.get("sg_data", {})
                        )
                        break
                    except:
                        error_str = traceback.format_exc()
                        errors.append(error_str)
                        self.parent.logger.error(
                            "Failed to download dependencies:\n{}".format(error_str)
                        )
                        counter += 1

        self.parent.logger.debug("items_result:\n{}".format(pformat(items_result)))

        return items_result, warnings, errors
