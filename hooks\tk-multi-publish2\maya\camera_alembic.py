# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

import os
import re
import json
import pprint
import traceback

import sgtk
from sgtk.util.filesystem import copy_file, ensure_folder_exists

import maya.cmds as cmds
import maya.mel as mel
import pymel.core as pm


pp = pprint.pprint
pf = pprint.pformat


HookBaseClass = sgtk.get_hook_baseclass()


class CameraAlembicPublishPlugin(HookBaseClass):
    @property
    def icon(self):
        """
        Path to an png icon on disk
        """
        # look for icon one level up from this hook's folder in "icons" folder
        return os.path.join(
            self.disk_location,
            os.pardir,
            "icons",
            "publish_alembic.png"
        )

    @property
    def name(self):
        return "Publish Camera Alembic to Shotgun"

    @property
    def description(self):

        return 'Export camera alembics'

    @property
    def settings(self):
        plugin_settings = super(CameraAlembicPublishPlugin, self).settings or {}

        # settings specific to this class
        maya_publish_settings = {
            "Work Template": {
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "Primary Publish Template": {
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "Publish Template": {
                "type": "template",
                "default": None,
                "description": "Template path for published Scene playblast. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "Publish Type": {
                "type": "string",
                "default": "Shot Camera Alembic",
                "description": "The published file type to register.",
            },
            "camera_alembic_allowed_steps": {
                "type": "list",
                "default": None,
                "description": "Allowed pipeline steps for this plugin.",
            },
        }

        # update the base settings
        plugin_settings.update(maya_publish_settings)

        return plugin_settings

    @property
    def item_filters(self):
        return ["maya.session.cameraalembic"]

    ############################################################################
    # standard publish plugin methods

    def accept(self, settings, item):

        item.properties['accepted'] = False

        # get the main scene
        scene_name = cmds.file(query=True, sn=True)
        if not scene_name:
            raise Exception("Please Save your file before Publishing")

        # load allowed steps from hook settings
        list_of_allowed_steps = settings.get("camera_alembic_allowed_steps").value
        self.parent.engine.logger.info(
            "list_of_allowed_steps from settings: {}".format(list_of_allowed_steps)
        )

        # try to override the list of allowed steps from value overrides
        # Load value overrides framework -----------------------------------------------
        valueoverrides = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        ) or self.load_framework("mty-framework-valueoverrides")

        if not valueoverrides:
            self.parent.engine.logger.error("Couldn't load value overrides framework")

        # load settings from overrides
        if valueoverrides:
            # Get allowed pipeline steps
            override_link = {
                "type": "Task",
                "id": self.parent.engine.context.task["id"],
            }

            default_value_code = (
                "mty.multi.publisher.maya.camera_alembic_allowed_steps"
            )
            camera_alembic_allowed_steps_value = valueoverrides.get_value(
                default_value_code, link=override_link
            )
            if camera_alembic_allowed_steps_value:
                list_of_allowed_steps = json.loads(camera_alembic_allowed_steps_value)
                self.parent.engine.logger.info(
                    "list_of_allowed_steps from override: {}".format(
                        list_of_allowed_steps
                    )
                )

        if self.parent.engine.context.step['name'] not in list_of_allowed_steps:
            return {"accepted": False}

        # TODO: replace this switch with an environment setting
        validate_shot_node = False
        if validate_shot_node:
            list_of_shots = pm.ls(type='shot')
            shot_node_name = list_of_shots[0].name()

            shot_node = pm.ls(shot_node_name, type="shot")[0]

            item.properties["shot_node"] = shot_node

            shot_name = shot_node.getShotName()

            if not shot_node:
                message = "Can't find a shot node with its name set to : %s"
                raise Exception(message % shot_node_name)

            # --------------------------------------------------------------------------------

            # collect info from shot node
            camera_shape = pm.PyNode(shot_node.getCurrentCamera())
            camera_reference = pm.referenceQuery(camera_shape, referenceNode=True)
            camera_node = None

            if isinstance(camera_shape, pm.nt.Camera):
                camera_node = camera_shape.getParent()
            else:
                camera_node = camera_shape

            if camera_node:
                item.properties["camera_node"] = camera_node

                # return the accepted info
                return {"accepted": True, "checked": True}

        else:
            # list top reference nodes
            list_of_references = pm.listReferences()
            list_of_paths = [str(_ref.path) for _ref in list_of_references]

            # get their corresponding publishes
            map_of_publishes = sgtk.util.find_publish(
                self.parent.engine.context.sgtk,
                list_of_paths,
                fields=[
                    'entity',
                    'version_number',
                    'entity.Asset.code',
                    'entity.Asset.sg_asset_type',
                    'published_file_type',
                    'name',
                    'task.Task.step.Step.code'
                ]
            )

            # and check if there is one for an Asset of type camera
            camera_assets = {}
            for path in map_of_publishes:
                publish = map_of_publishes[path]
                asset_type = publish.get('entity.Asset.sg_asset_type')
                entity_name = publish.get("entity", {}).get("name")
                context_entity_name = self.parent.engine.context.entity.get("name")
                published_file_type = publish.get("published_file_type").get("name")
                if asset_type == 'Camera':
                    camera_assets[path] = publish
                elif (
                    entity_name == context_entity_name and
                    published_file_type == 'Shot Camera Alembic'
                ):
                    camera_assets[path] = publish

            if len(camera_assets) > 1:
                message = "Your scene have more than 1 Camera Asset:{}".format(
                    camera_assets
                )
                self.parent.engine.logger.error(message)
                raise Exception(message)
            elif len(camera_assets) == 0:
                message = "Your scene doesn't have any Camera Asset"
                self.parent.engine.logger.error(message)
                raise Exception(message)

            # then return the camera shape inside
            cam_path = list(camera_assets.keys())[0]
            ref = [r for r in list_of_references if str(r.path) == cam_path][0]
            cam_nodes = [n for n in ref.nodes() if n.nodeType() == 'camera']
            # giving priority to the "rigged_CAMShape"
            if len(cam_nodes) == 0 :
                message = "Your Camera Asset doesn't have any camera shapes: {}".format(
                    cam_path
                )
                self.parent.engine.logger.error(message)
                raise Exception(message)
            elif len(cam_nodes) > 1:
                camera = [c for c in cam_nodes if 'rigged_CAMShape' in c.name()]
                if len(camera) == 0:
                    message = "Your Camera Asset doesn't have a '{}' shape: {}".format(
                        'rigged_CAMShape', cam_path
                    )
                    self.parent.engine.logger.error(message)
                    raise Exception(message)
                camera_shape = camera[0]
            else:
                camera_shape = cam_nodes[0]

            camera_node = camera_shape.getParent()

            if camera_node:
                item.properties["camera_node"] = camera_node

                # return the accepted info
                return {"accepted": True, "checked": True}


        return {"accepted": False}

    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish.

        Returns a boolean to indicate validity.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process

        :returns: True if item is valid, False otherwise.
        """

        publisher = self.parent
        path = item.properties.get("path")

        work_template_name = settings["Work Template"].value
        work_template = self.parent.engine.get_template_by_name(
            work_template_name)
        scene_path = os.path.abspath(cmds.file(query=True, sn=True))
        fields = work_template.get_fields(scene_path)
        publish_version = fields["version"]

        publish_template_name = settings["Publish Template"].value
        publish_template = self.parent.engine.get_template_by_name(
            publish_template_name
        )
        publish_path = publish_template.apply_fields(fields)

        # determine the publish name:
        publish_name = None

        version_pattern = re.compile(
            r"(?P<head>.+)"
            r"(?P<version_token>_v\d{3})"
            r"(?P<tail>.+)"
        )
        version_match = re.match(
            version_pattern, os.path.basename(publish_path)
        )
        if version_match:
            publish_name = "{}{}".format(
                version_match.groupdict()["head"],
                version_match.groupdict()["tail"]
            )

        # fallback to use file name as publish name
        if not publish_name:
            publish_name = os.path.basename(publish_path)

        item.properties["publish_version"] = publish_version
        item.properties["publish_name"] = publish_name
        item.properties["publish_path"] = publish_path
        item.properties["publish_fields"] = fields

        publishes = publisher.util.get_conflicting_publishes(
            item.context,
            publish_path,
            publish_name,
            filters=["sg_status_list", "is_not", None],
        )
        if publishes:
            message = (
                "Found existing conflicting publishes for file: {}, "
                "conflicting publishes:\n{}"
            ).format(publish_path, pf(publishes))
            self.parent.engine.logger.error(message)
            raise Exception(message)

        return True

    def publish(self, settings, item):
        publish_version = item.properties["publish_version"]
        publish_name = item.properties["publish_name"]
        publish_path = item.properties["publish_path"]
        fields = item.properties["publish_fields"]

        publish_file_type = settings["Publish Type"].value
        scene_pub_template_name = settings["Primary Publish Template"].value
        scene_pub_template = self.parent.engine.get_template_by_name(
            scene_pub_template_name)
        primary_publish_path = scene_pub_template.apply_fields(fields)

        camera_name = item.properties["camera_node"].name()

        # TODO: replace this switch with an environment setting
        validate_shot_node = False
        if validate_shot_node:
            shot_node = item.properties["shot_node"]
            first_frame = shot_node.getStartTime()
            last_frame = shot_node.getEndTime()
        else:
            filters = [['id', 'is', self.parent.context.entity['id']]]
            fields = ['sg_cut_in', 'sg_cut_out']
            shot = self.parent.shotgun.find_one('Shot', filters, fields)
            first_frame = shot['sg_cut_in']
            last_frame = shot['sg_cut_out']

        self.parent.engine.ensure_folder_exists(os.path.dirname(publish_path))

        mel_command = (
            '-frameRange {0} {1} -attrPrefix fbCache -stripNamespaces '
            '-uvWrite -writeColorSets -writeFaceSets -worldSpace '
            '-writeVisibility -writeUVSets -dataFormat ogawa -eulerFilter '
            '-root {2} -file {3}'
        )

        pm.AbcExport(
            j=mel_command.format(
                first_frame,
                last_frame,
                camera_name,
                publish_path)
        )

        publisher = self.parent

        publish_dependencies_ids = []
        if "sg_publish_data" in item.parent.properties:
            publish_dependencies_ids.append(
                item.parent.properties.sg_publish_data["id"]
            )

        dependencies = [primary_publish_path]

        sg_fields_to_update = {
            "sg_status_list": "rev",
            "task.Task.sg_status_list": "rev",
        }

        publish_data = {
            "tk": self.parent.engine.sgtk,
            "context": item.context,
            "comment": item.description,
            "path": publish_path,
            "name": publish_name,
            "version_number": publish_version,
            "thumbnail_path": item.get_thumbnail_as_path(),
            "task": self.parent.engine.context.task,
            "sg_fields": sg_fields_to_update,
            "published_file_type": publish_file_type,
            "dependency_paths": dependencies,
        }
        sg_publishes = sgtk.util.register_publish(**publish_data)

        # finally just store the publish data for later retrieval
        # and upload to the host storage location
        root_item = self.get_root_item(item)
        root_item.properties.setdefault('sg_publish_extra_data', [])

        self.parent.logger.info(
            'Storing extra publish data on root item: {}'.format(root_item)
        )
        publish_extra_data = root_item.properties['sg_publish_extra_data']
        if isinstance(sg_publishes, list):
            root_item.properties['sg_publish_extra_data'].extend(sg_publishes)
        else:
            root_item.properties['sg_publish_extra_data'].append(sg_publishes)
        self.parent.logger.info(
            "Already {} elements in extra publish data".format(
                len(publish_extra_data)
            )
        )

        self.parent.logger.info(
            (
                "Added existing publish (code: {}, id:{}) as extra data "
                "for item: {}"
            ).format(sg_publishes.get("code"), sg_publishes.get("id"), root_item)
        )

    def finalize(self, settings, item):
        pass

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item
        return root

