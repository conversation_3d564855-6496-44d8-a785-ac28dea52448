# -*- coding: utf-8 -*-
# Standard library:
import pprint
# ___   ___   ___   ___   ___   ___  ___
# Third party:
import sgtk

# ___   ___   ___   ___   ___   ___  ___
# Project:
HookBaseClass = sgtk.get_hook_baseclass()
# ====================================================================
pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)


class Breakdown(HookBaseClass):
    def __init__(self, parent):
        super(Breakdown, self).__init__(parent)
        self._shotgun = self.parent.shotgun

    # ---------------------------------------------------------------------------
    @property
    def SG(self):
        return self._shotgun

    # ---------------------------------------------------------------------------
