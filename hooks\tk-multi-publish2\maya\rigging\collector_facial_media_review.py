#####################################################################################
#
# Copyright (c) 2021 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

import os
import sys
import sgtk

HookBaseClass = sgtk.get_hook_baseclass()

class FacialReviewCollector(HookBaseClass):
    """
    """

    def process_current_session(self,settings,parent_item):
        """
        Analyzes the current scene open in a DCC and parents a subtree of items
        under the parent_item passed in.

        :param dict settings: Configured settings for this collector
        :param parent_item: Root item instance
        """

        # create an item representing the current maya session
        # let parent collector do it own work
        super(FacialReviewCollector, self).process_current_session(settings, parent_item)
        if not parent_item.type_spec.endswith(".session"):
            item = next(
                (i for i in parent_item.descendants if i.type_spec.endswith(".session")),
                parent_item,
            )
        else:
            item = parent_item

        self._collect_facial_media_review(item)

    def _collect_facial_media_review(self, parent_item):
        """
        Creates item for review media to be generated.

        :param parent_item: Parent Item instance
        """
        self.logger.debug("Collecting Facial Review...")
        facial_review_item= parent_item.create_item(
                "maya.session.facial.review",
                "Facial animation ",
                "Facial Media Review"
        )

        self.parent.engine.execute_hook_expression(
            "{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py",
            "use_farm_or_local_processing",
            item = facial_review_item
        )

        # get the icon path to display for this item
        icon_path= os.path.join(
            os.path.dirname(self.disk_location),
            os.pardir,
            "icons",
            "facials.png"
        )

        facial_review_item.set_icon_from_path(icon_path)
        facial_review_item.properties["publish_type"] = "Media Review"

        return facial_review_item




