# Copyright (c) 2019 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
import re
import os
import json
import pprint
import fileseq
import tempfile
import sys

import sgtk

from tank_vendor import six

HookBaseClass = sgtk.get_hook_baseclass()


class ProjectUnsavedError(Exception):
    pass


class RenderingFailed(Exception):
    pass


class AfterEffectsUploadVersionPlugin(HookBaseClass):
    """
    Plugin for sending aftereffects documents to shotgun for review.
    """

    @property
    def settings(self):
        """
        Dictionary defining the settings that this plugin expects to recieve
        through the settings parameter in the accept, validate, publish and
        finalize methods.

        A dictionary on the following form::

            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }

        The type string should be one of the data types that toolkit accepts as
        part of its environment configuration.
        """
        # plugin_settings = super(AfterEffectsUploadVersionPlugin, self).settings or {}
        # afx_publish_settings = {
        #     "Publish Type": {
        #         "type": "string",
        #         "default": None,
        #         "description": "The published file type to register.",
        #     },
        # }

        # # update the base settings
        # plugin_settings.update(afx_publish_settings)

        plugin_settings = {
            "Publish Type": {
                "type": "string",
                "default": None,
                "description": "The published file type to register.",
            },
            "Editorial Video Opacity": {
                "type": "float",
                "default": None,
                "description": "Value of opacity for editorial overlay",
            },
        }

        return plugin_settings

    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish.

        Returns a boolean to indicate validity.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process

        :returns: True if item is valid, False otherwise.
        """

        if self.parent.context.entity.get("type") == "Shot":
            result = self.collect_editorial_preview(item)
            if not result:
                error_msg = "Can't obtain a local copy of latest editorial preview"
                self.logger.error(error_msg)

                # return False
                raise Exception(error_msg)

        # return True
        # run the base class validation
        return super(AfterEffectsUploadVersionPlugin, self).validate(settings, item)


    def publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """

        self.parent.logger.info("upload_version publishing start {}".format("-" * 80))
        publisher = self.parent

        queue_item = item.properties.get("queue_item")
        render_paths = list(item.properties.get("renderpaths"))
        self.opacity_editorial_vid = settings.get("Editorial Video Opacity").value

        self.parent.engine.logger.debug(
            "item.properties:\n{}".format(pprint.pformat(item.properties.to_dict()))
        )
        upload_path = None
        path_to_movie = item.properties["movie_target_path"]

        #Ensure folder location for movie render exists
        sgtk.util.filesystem.ensure_folder_exists(os.path.dirname(path_to_movie))
        path_to_frames = None
        while render_paths and (not all([upload_path, path_to_movie, path_to_frames])):
            each_path = render_paths.pop(0)
            if not self.parent.engine.is_adobe_sequence(each_path):
                upload_path = each_path
                self.parent.engine.logger.info("upload_path: {}".format(upload_path))
            else:
                path_to_frames = each_path
                self.parent.engine.logger.info(
                    "path_to_frames: {}".format(path_to_frames)
                )

        if path_to_movie and path_to_frames:
            self.logger.info("About to render movie...")
            upload_path = self.__render_movie_from_sequence(
                item, path_to_frames,
            )
            if not upload_path:
                raise RenderingFailed(
                    "Rendering a movie failed. Cannot upload a version of this item."
                )

        if upload_path is None:
            self.logger.error("No render path found")
            return

        # if we got a sequence, we need to set additional information
        additional_version_data = self.__get_additional_version_data(
            queue_item, path_to_frames
        )

        # use the path's filename to get the publish name
        path_info = publisher.util.get_file_path_components(path_to_movie)
        filename = path_info["filename"]
        version_name = os.path.splitext(filename)[0]
        self.parent.logger.info("version_name: {}".format(version_name))



        # populate the version data to send to SG
        self.logger.info("Creating Version...")
        version_data = {
            "project": item.context.project,
            "code": version_name,
            "description": item.description,
            "entity": self._get_version_entity(item),
            "sg_task": item.context.task,
            "sg_version_type": "Production",
            "sg_path_to_frames": path_to_frames,
            "sg_path_to_movie": path_to_movie,
        }
        version_data.update(additional_version_data)

        # ------------------------------------------------------------------------------
        # register the publish:

        # get media resolution
        media_resolution = self.parent.execute_hook_expression(
            "{config}/tk-multi-publish2/multi/resolution_from_path.py",
            "media_resolution_value",
            path=path_to_movie
        )
        self.parent.logger.info(
            "media resolution from hook: {}".format(media_resolution)
        )

        publish_name = self.parent.util.get_publish_name(path_to_movie)
        self.parent.logger.info("publish_name for movie: {}".format(publish_name))
        publish_data = {
            "tk": self.parent.engine.sgtk,
            "context": item.context,
            "comment": item.description,
            "path": path_to_movie,
            "name": publish_name,
            "version_number": item.properties["version_number"],
            "thumbnail_path": item.get_thumbnail_as_path(),
            "task": self.parent.engine.context.task,
            "sg_fields": {
                "sg_media_resolution": media_resolution,
                "sg_status_list": "rev",
            },
            "published_file_type": settings["Publish Type"].value,
            "dependency_paths": [
                item.properties["primary_publish_path"], path_to_frames
            ],
        }

        self.parent.logger.debug(
            "publish_data:\n{}".format(pprint.pformat(publish_data))
        )

        sg_publishes = sgtk.util.register_publish(**publish_data)

        # get root item to add the publish to its extra publishes
        root_item = self.get_root_item(item)
        root_item.properties.setdefault('sg_publish_extra_data', [])

        publish_extra_data = root_item.properties['sg_publish_extra_data']

        root_item.properties['sg_publish_extra_data'].append(sg_publishes)

        # ------------------------------------------------------------------------------

        # publish_data = item.properties.get("sg_publish_data")
        rendering_data = item.properties.get("published_renderings", [])

        # if the file was published, add the publish data to the version
        version_data["published_files"] = []
        if sg_publishes:
            version_data["published_files"].append(sg_publishes)
        version_data["published_files"].extend(rendering_data)

        # log the version data for debugging
        self.logger.debug(
            "Populated Version data...",
            extra={
                "action_show_more_info": {
                    "label": "Version Data",
                    "tooltip": "Show the complete Version data dictionary",
                    "text": "<pre>%s</pre>" % (pprint.pformat(version_data),),
                }
            },
        )
        self.parent.engine.logger.debug(
            "version_data:\n{}".format(pprint.pformat(version_data))
        )

        # create the version
        self.logger.info("Creating version for review...")
        version = self.parent.shotgun.create("Version", version_data)

        # stash the version info in the item just in case
        item.properties["sg_version_data"] = version

        # Ensure the path is utf-8 encoded to avoid issues with the Shotgun API.
        upload_path = six.ensure_str(upload_path)

        # upload the file to SG
        self.logger.info("Uploading content...")
        self.parent.shotgun.upload(
            "Version", version["id"], upload_path, "sg_uploaded_movie"
        )
        self.logger.info("Upload complete!")

        item.properties["upload_path"] = upload_path

        # Update task status for revision
        sg_task = self.parent.context.task
        self.parent.logger.info(
            "End of Publish and updating task with id: {} to status: 'rev'".format(
                str(sg_task['id'])
            )
        )

        try:
            self.parent.engine.shotgun.update(
                "Task", sg_task["id"], {"sg_status_list": 'rev'}
            )
        except:
            pass

    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed, and can for example be used to version up files.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """

        version = item.properties["sg_version_data"]

        self.logger.info(
            "Version uploaded for After Effects document",
            extra={
                "action_show_in_shotgun": {
                    "label": "Show Version",
                    "tooltip": "Reveal the version in ShotGrid.",
                    "entity": version,
                }
            },
        )

        # upload_path = item.properties["upload_path"]

        # remove the tmp file
        # if item.properties.get("remove_upload", False):
        #     try:
        #         os.remove(upload_path)
        #     except Exception:
        #         self.logger.warn("Unable to remove temp file: %s" % (upload_path,))
        #         pass

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item
        return root

    def _get_shot_first_and_last_frames_from_SG(self):
        """ Returns a dictionary containing two keys: first_frame and last_frame"""

        result = {}

        if self.parent.context.entity.get("type") == "Shot":
            filters = [
                ["id", "is", self.parent.engine.context.entity["id"]]
            ]

            fields = ["sg_cut_in", "sg_cut_out"]

            data = self.parent.engine.shotgun.find_one(
                entity_type="Shot",
                filters=filters,
                fields=fields
            )

            if data:
                result["first_frame"] = data.get("sg_cut_in")
                result["last_frame"] = data.get("sg_cut_out")

        return result

    def getProjectFPSfromSG(self):

        data = self.parent.engine.shotgun.find_one(
            entity_type="Project",
            filters=[["id", "is", self.parent.engine.context.project["id"]]],
            fields=["sg_fps"]
        )
        project_fps = data.get("sg_fps")

        return project_fps

    def __render_movie_from_sequence(
        self, item, sequence_path,
    ):

        output_path = None

        self.parent.logger.debug("Sequence path: {}".format(sequence_path))

        # Loading mediatools framework
        mediatools = self.load_framework("mty-framework-mediatools")

        # ------------------------------------------------------------------------------
        # Get first file sequence frame
        seq_pattern = re.compile(r"(?P<start>.+)(?P<frames_token>%\d{2}d)(?P<end>.+)")
        seq_match = re.match(seq_pattern, sequence_path)
        if seq_match:
            resolved_file_path = sequence_path.replace(
                seq_match.groupdict()["frames_token"], "@"
            )
            seq = fileseq.findSequenceOnDisk(resolved_file_path)
            first_fileseq_frame = seq.start()

        # Get necessary data for transcoding function
        mov_file_path = item.properties.get("movie_target_path")
        editorial_preview_path = item.properties.get("editorial_preview")
        if not editorial_preview_path:
            editorial_preview_path = ""
        # seq_first_frame = self._get_seq_first_frame()

        frame_range = self._get_shot_first_and_last_frames_from_SG()
        if frame_range:
            shot_first_frame = frame_range.get("first_frame")
            shot_last_frame = frame_range.get("last_frame")
        elif seq:
            shot_first_frame = seq.start()
            shot_last_frame = seq.end()

        context_name = item.properties.get("context_name")

        version = item.properties.get("version_number")
        project_fps = self.getProjectFPSfromSG()


        self.parent.logger.debug("mov_file_path: {}".format(mov_file_path))
        self.parent.logger.debug("sequence_path (playblast): {}".format(sequence_path))
        self.parent.logger.debug("editorial_preview_path: {}".format(editorial_preview_path))
        self.parent.logger.debug("first_fileseq_frame: {}".format(first_fileseq_frame))
        self.parent.logger.debug("shot_first_frame: {}".format(shot_first_frame))
        self.parent.logger.debug("shot_last_frame: {}".format(shot_last_frame))
        self.parent.logger.debug("context_name: {}".format(context_name))
        self.parent.logger.debug("version: {}".format(version))
        self.parent.logger.debug("mediatools: {}".format(mediatools))
        self.parent.logger.debug("project_fps: {}".format(project_fps))

        # ------------------------------------------------------------------------------

        self.transcode_and_format_clip(
            mov_file_path,
            sequence_path,
            editorial_preview_path,
            first_fileseq_frame,
            shot_first_frame,
            shot_last_frame,
            context_name,
            version,
            mediatools,
            project_fps
        )

        if os.path.exists(mov_file_path):
            output_path = mov_file_path

        return output_path

    def collect_editorial_preview(self, item):

        file_type_code_field = 'published_file_type.PublishedFileType.code'

        editorial_preview = self.parent.shotgun.find_one(
            'PublishedFile',
            [
                [file_type_code_field, 'is', 'Media Review'],
                ['task.Task.step.Step.code', 'is', 'Editorial'],
                ['entity', 'is', self.parent.context.entity]
            ],
            ['path'],
            order=[
                {'field_name':'version_number', 'direction':'desc'},
                {'field_name':'id', 'direction':'desc'}
            ]
        )

        if editorial_preview:

            editorial_preview_file = editorial_preview['path']['local_path']

            metasync = self.load_framework("mty-framework-metasync")
            transfersManager = metasync.transfersManager
            transfersManager.ensure_file_is_local(
                editorial_preview_file, editorial_preview)

            if os.path.exists(editorial_preview_file):
                item.properties["editorial_preview"] = editorial_preview_file
            else:
                return False

        return True

    def transcode_and_format_clip(
            self,
            mov_file_path,
            playblast_file_path,
            editorial_preview_path,
            first_fileseq_frame,
            shot_start_frame,
            shot_end_frame,
            context_name,
            version,
            mediatools,
            project_fps
    ):

        self.parent.logger.debug(
            (
                "transcode_and_format_clip:\n"
                "      mov_file_path: {0}\n"
                "      playblast_file_path: {1}\n"
                "      editorial_preview_path: {2}\n"
                "      first_fileseq_frame: {3}\n"
                "      shot_start_frame: {4}\n"
                "      shot_end_frame: {5}\n"
                "      context_name: {6}\n"
                "      version: {7}\n"
                "      mediatools: {8}\n"
                "      project_fp: {9}\n"
            ).format(
                mov_file_path,
                playblast_file_path,
                editorial_preview_path,
                first_fileseq_frame,
                shot_start_frame,
                shot_end_frame,
                context_name,
                version,
                mediatools,
                project_fps
            )
        )

        # create a custom input stream to control certain parameters
        step_name = self.parent.context.step['name']
        task_name = self.parent.context.task['name']
        engine_name = self.parent.engine.name
        # load overrides framework -----------------------------------------------------
        overrides_framework = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        )
        if not overrides_framework:
            overrides_framework = self.load_framework("mty-framework-valueoverrides")
        # # ------------------------------------------------------------------------------

        default_value_overlay = "mty.publisher.tasks_without_video_overlay"

        tasks_without_overlay_hidden = overrides_framework.get_value(
            default_value_overlay
        )
        self.parent.logger.info(
            "tasks without editorial overlay: {}, type: {}".format(
                tasks_without_overlay_hidden, type(tasks_without_overlay_hidden)
            )
        )
        if tasks_without_overlay_hidden:
            tasks_without_overlay_hidden = json.loads(tasks_without_overlay_hidden)
        else:
            tasks_without_overlay_hidden = []

        # create a custom input stream to control certain parameters
        media_stream = mediatools.Modifier.create_input_stream(
            playblast_file_path, start_number=first_fileseq_frame, r=project_fps,
            probesize=u'10M'
        )

        if task_name not in tasks_without_overlay_hidden:

            # for shot context create a tmp stream using the editorial preview
            # to extract the audio from
            if editorial_preview_path:

                # create a temp input stream to get the audio
                media_stream_temp = mediatools.Modifier.create_input_stream(
                    editorial_preview_path, r=project_fps, probesize=u'10M'
                )

                # split the video and audio streams to use them independently
                # we will only modify the video one while passing audio as is
                video_stream = media_stream.video
                if self.verify_audio_stream(editorial_preview_path):
                    audio_stream = media_stream_temp.audio
                else:
                    audio_stream = None


            # define the video overlay options using quadrants_media_overlay
            # which will automatically trim, scale and position the media
            # relative_start_frame = shot_start_frame - first_fileseq_frame
            # relative_end_frame = shot_end_frame - first_fileseq_frame

            default_value_edl_overlay = "mty.publisher.afx.tasks_without_eld_overlay"
            link = {"type": "Project", "id": self.parent.context.project["id"]}
            forbidden_tasks = overrides_framework.get_value(
                default_value_edl_overlay, link=link
            )
            self.parent.logger.info(
                "tasks without editorial overlay: {}, type: {}".format(
                    forbidden_tasks, type(forbidden_tasks)
                )
            )
            if forbidden_tasks and forbidden_tasks != "":
                forbidden_tasks = json.loads(forbidden_tasks)
            else:
                forbidden_tasks = []

            task_name = self.parent.context.task["name"]

            if editorial_preview_path and task_name not in forbidden_tasks:
                overlay_data = {
                    'supD': {
                        'file_path': editorial_preview_path
                    }
                }

                # set opacity for editorial video overlay
                opacity_override = "mty.publisher.editorial_overlay_opacity"

                opacity_override_value = overrides_framework.get_value(
                    opacity_override
                )
                self.parent.logger.info(
                    "Editorial overlay editorial: {}".format(
                        opacity_override_value
                    )
                )
                if opacity_override_value:
                    overlay_filter= {"opacity": float(opacity_override_value)}

                else:
                    overlay_filter= {"opacity":self.opacity_editorial_vid}


                media_stream = mediatools.Modifier.create_quadrants_media_overlay(
                    video_stream, overlay_data,media_filters=overlay_filter
                )

            # set start frame for overlay counter
            startframe_for_sequence = "mty.publisher.overlay_frame_number_from_sequence_dcc"

            startframe_for_sequence_dcc = overrides_framework.get_value(
                startframe_for_sequence
            )
            self.parent.logger.info(
                "list of Dcc that using start frame from sequece: {}".format(
                    startframe_for_sequence_dcc
                )
            )
            if startframe_for_sequence_dcc:
                dccs_start_with_sequence_framenumber= json.loads(startframe_for_sequence_dcc)
                if engine_name in dccs_start_with_sequence_framenumber:
                    shot_start_frame = first_fileseq_frame


            text_data = {
                'supA': str(context_name),
                'supB': '{0}-{1}'.format(step_name, task_name),
                'supC': "v{:0>3}".format(version),
                'infD':'{frame_number}:%s' % shot_start_frame,
            }

            media_stream = mediatools.Modifier.create_quadrants_text_overlay(
                text_data, media_stream, font_size=32
            )

            # finally concatenate the modified video and the original audio
            if editorial_preview_path and audio_stream:
                media_stream = mediatools.Modifier.ffmpeg.concat(
                    media_stream, audio_stream, v=1, a=1
                )

            self.parent.engine.logger.debug("media_stream: {}".format(media_stream))
            self.parent.engine.logger.debug("mov_file_path: {}".format(mov_file_path))
            self.parent.engine.logger.debug("project_fps: {}".format(project_fps))
            # and transcode it with a preset and some extra custom parameters
        mediatools.Transcoder.transcode(
            media_stream, mov_file_path, 'h264_high_yuv420p',
            r=project_fps, max_muxing_queue_size=2048
        )

    def __get_additional_version_data(self, queue_item, path_to_frames):
        if path_to_frames is None:
            return {}

        out_dict = {}
        frame_numbers = []
        for _, fn in self.parent.engine.get_render_files(path_to_frames, queue_item):
            frame_numbers.append(fn)
        out_dict["sg_first_frame"] = min(frame_numbers)
        out_dict["sg_last_frame"] = max(frame_numbers)
        out_dict["frame_range"] = "{}-{}".format(min(frame_numbers), max(frame_numbers))
        out_dict["frame_count"] = len(frame_numbers)
        match = re.search(r"[\[]?([#@]+)[\]]?", path_to_frames)
        if match:
            path_to_frames = path_to_frames.replace(
                match.group(0), "%0{}d".format(len(match.group(1)))
            )
        out_dict["sg_path_to_frames"] = path_to_frames

        # use the path's filename as the publish name
        path_components = self.parent.util.get_file_path_components(path_to_frames)
        # out_dict["code"] = path_components["filename"]
        return out_dict

    def _get_version_entity(self, item):
        """
        Returns the best entity to link the version to.
        """

        if item.context.entity:
            return item.context.entity
        elif item.context.project:
            return item.context.project
        else:
            return None
    def verify_audio_stream(self, editorial_preview_path):
        self.parent.engine.logger.info("loading mty-framework-ffmpeg")
        ffmpeg = self.load_framework("mty-framework-ffmpeg")
        FFmpegCoreTools = ffmpeg.ffmpegCore
        FFmpegCoreTools.set_binary(binary="media")
        ffprobe_path = os.path.dirname(ffmpeg.ffmpegCore.get_bin_path())

        self.parent.engine.logger.info("ffprobe path: {}".format(ffprobe_path))

        ffprobe_cmd = [
            "-v",
            "error",
            "-show_streams",
            "-select_streams",
            "a",
            "-of",
            "json",
            editorial_preview_path
        ]

        self.parent.engine.logger.info("ffprobe_cmd: {}".format(ffprobe_cmd))

        _err, _info = FFmpegCoreTools.execute_command(ffprobe_cmd)
        if not _err:
            data = dict(json.loads(_info))
            audio_streams = data.get("streams", [])
            if audio_streams:
                return True
        return False
