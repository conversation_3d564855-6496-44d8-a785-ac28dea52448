# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

description: Apps and engines loaded when an Asset is loaded. Since std VFX
  config template has a file system structure which is centered around pipeline
  steps, this environment is largely empty. Most of the work takes place on a
  level in the file system where both an asset and a pipeline step is available
  - e.g Asset Hero, modeling, so all apps for loading, publishing etc. are
  located in the asset_step environment. This environment mostly contains
  utility apps and the tank work files app, which lets you choose a task to work
  on and load associated content into an application.

################################################################################

includes:
- ./includes/frameworks.yml
- ./includes/settings/tk-3dsmax.yml
- ./includes/settings/tk-houdini.yml
- ./includes/settings/tk-mari.yml
- ./includes/settings/tk-maya.yml
- ./includes/settings/tk-motionbuilder.yml
- ./includes/settings/tk-nuke.yml
- ./includes/settings/tk-photoshopcc.yml
- ./includes/settings/tk-aftereffects.yml
- ./includes/settings/tk-alias.yml
- ./includes/settings/tk-vred.yml
- ./includes/settings/tk-shell.yml
- ./includes/settings/tk-shotgun.yml
- ./includes/settings/tk-harmony.yml
- ./includes/settings/tk-fusion.yml
- ./includes/settings/tk-krita.yml
- ./includes/settings/tk-substancepainter.yml
- ./includes/settings/tk-blender.yml

################################################################################
# configuration for all engines to load in an asset context

engines:
  tk-3dsmax: "@settings.tk-3dsmax.asset"
  tk-houdini: "@settings.tk-houdini.asset"
  tk-mari: "@settings.tk-mari.asset"
  tk-maya: "@settings.tk-maya.asset"
  tk-motionbuilder: "@settings.tk-motionbuilder.asset"
  tk-nuke: "@settings.tk-nuke.asset"
  tk-photoshopcc: "@settings.tk-photoshopcc.asset"
  tk-aftereffects: "@settings.tk-aftereffects.asset"
  tk-alias: "@settings.tk-alias.asset"
  tk-vred: "@settings.tk-vred.asset"
  tk-shell: "@settings.tk-shell.asset"
  tk-shotgun: "@settings.tk-shotgun.asset"
  tk-harmony: "@settings.tk-harmony.asset"
  tk-fusion: "@settings.tk-fusion.asset"
  tk-krita: "@settings.tk-krita.asset"
  tk-substancepainter: "@settings.tk-substancepainter.asset"
  tk-blender: "@settings.tk-blender.asset"

################################################################################
# reference all of the common frameworks

frameworks: "@frameworks"
