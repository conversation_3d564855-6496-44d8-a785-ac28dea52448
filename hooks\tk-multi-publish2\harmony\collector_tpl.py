#####################################################################################
#
# Copyright (c) 2021 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

import os
import sys
import shutil
import json

import sgtk

HookBaseClass = sgtk.get_hook_baseclass()


class HarmonyTPLCollector(HookBaseClass):
    """
    Collector that operates on the Toon Boom Harmony session. Should inherit
    from the basic collector hook.
    """

    @property
    def settings(self):
        """
        Dictionary defining the settings that this collector expects to receive
        through the settings parameter in the process_current_session and
        process_file methods.
        A dictionary on the following form::
        {
            "Settings Name": {
                "type": "settings_type",
                "default": "default_value",
                "description": "One line description of the setting"
        }
        The type string should be one of the data types that toolkit accepts as
        part of its environment configuration.
        """

        # grab any base class settings
        collector_settings = super(HarmonyTPLCollector, self).settings or {}

        # settings specific to this collector
        harmony_session_settings = {
            "Publish Template": {
                "type": "template",
                "default": None,
                "description": "Publish path template",
            },
            "Work TPL Template": {
                "type": "template",
                "default": None,
                "description": "Template path where the files are located",
            },
            "Publish TPL Template": {
                "type": "template",
                "default": None,
                "description": "Template path where the files will be published",
            },
            "TPL Allowed Tasks": {
                "type": "list",
                "default": None,
                "description": "List of tasks that will collect TPLs"
            },
        }

        # update the base settings with these settings
        collector_settings.update(harmony_session_settings)

        return collector_settings

    def process_current_session(self, settings, parent_item):
        """
        Analyzes the current session open in Toon Boom Harmony and parents a
        subtree of items under the parent_item passed in.
        :param dict settings: Configured settings for this collector
        :param parent_item: Root item instance
        """

        super(HarmonyTPLCollector, self).process_current_session(settings, parent_item)

        item = next(
            (i for i in parent_item.descendants if i.type_spec.endswith(".session")),
            None,
        )
        if item is None:
            item = self.collect_current_harmony_session(settings, parent_item)

        context = self.parent.engine.context
        current_task = context.task["name"]
        self.parent.engine.logger.info("current_task: {}".format(current_task))

        #tpl_allowed_taks
        tpl_allowed_tasks = settings.get("TPL Allowed Tasks").value

        valueoverrides = self.parent.engine.custom_frameworks.get(
                'mty-framework-valueoverrides'
            ) or self.load_framework('mty-framework-valueoverrides')

        #tpl_allowed_taks override
        if valueoverrides:
            value_code = 'mty.publisher.harmony.collector_tpl.tpl_allowed_tasks'
            data = valueoverrides.get_value(
                value_code, link={"type": "Task", "id": context.task["id"]}
            )

            data = json.loads(data)
            self.parent.engine.logger.debug("Override of tpl_allowed_tasks:{}".format(data))
            if type(data) == list:
                if tpl_allowed_tasks != data:
                    tpl_allowed_tasks = data

        self.parent.engine.logger.info("tpl_allowed_tasks: {}".format(tpl_allowed_tasks))

        if current_task in tpl_allowed_tasks:
            tpl = self._collect_tpl(settings, item)
            # return item

    def _collect_tpl(self, settings, parent_item):
        self.logger.debug("Collecting tpl files...")

        # get fileseq in a partially generic way, untill we have mty-framework-externalpython
        # we know this hook files lives in {config}/tk-multi-publish2/harmony, so we will place fileseq there
        config_path = os.path.dirname(os.path.dirname(self.disk_location))

        engine = sgtk.platform.current_engine()
        # Harmony Work Session path
        path = engine.app.get_current_project_path()
        publisher = self.parent
        template_by_name = engine.get_template_by_name

        # get templates from settings
        work_template_setting = settings.get("Work Template").value
        work_tpl_template_setting = settings.get("Work TPL Template").value
        publish_session_setting = settings.get("Publish Template").value
        publish_tpl_template_setting = settings.get("Publish TPL Template").value

        # Templates
        work_template = template_by_name(work_template_setting)
        work_tpl_template = template_by_name(work_tpl_template_setting)
        publish_session_template = template_by_name(publish_session_setting)
        publish_tpl_template = template_by_name(publish_tpl_template_setting)

        # Adding extra element in work_fields
        work_fields = work_template.get_fields(path)
        work_fields["tokenName"] = "placeHolder"
        work_tpl_path = work_tpl_template.apply_fields(work_fields)

        # harmony library app
        app_harmonylib = engine.apps.get("mty-harmony-library")
        container_folder = app_harmonylib.ensure_library_folder(engine.context)

        base_name = os.path.basename(work_tpl_path)
        base_ls = base_name.split(".")
        ext = base_ls[-1]
        name = base_ls[0]

        if not os.access(container_folder, os.F_OK):
            config_dir_path = os.path.dirname(
                os.path.dirname(
                    os.path.dirname(os.environ["SGTK_HARMONY_NEWFILE_TEMPLATE"])
                )
            )
            config_file = os.path.join(config_dir_path, "HPL.txt")

            if os.access(config_file, os.F_OK):
                with open(config_file, "r") as f:
                    for line in f:
                        container_folder = line.replace("\n", "")
                        container_folder = os.path.join(
                            container_folder, "pipelineLibrary"
                        )
            else:
                return False

        files_content = os.listdir(container_folder)
        files = []
        # ignore .lock and .deletead files
        for f in files_content:
            if not ".lock" in f or not ".deleted" in f:
                if (
                    f.lower().endswith(".tpl")
                    and os.path.isdir(os.path.join(container_folder, f))
                ):
                    files.append(f)

        display_name = "Harmony TPL File"
        session_item = parent_item.create_item(
            "harmony.tpl", "Collects TPL Files", display_name
        )

        publish_icons = os.path.join(config_path, "tk-multi-publish2", "icons")
        icon_path = os.path.join(publish_icons, "nodes.png")
        session_item.set_icon_from_path(icon_path)

        session_item.properties["TPL_directory"] = container_folder
        session_item.properties["work_fields"] = work_fields
        session_item.properties["template_tpl_work"] = work_tpl_template
        session_item.properties["template_session_publish"] = publish_session_template

        # Sub template for every plugin item
        session_item.properties["template_tpl_publish"] = publish_tpl_template
        session_item.properties["TPL_validate_exist"] = True
        if not files:
            session_item.properties["TPL_validate_exist"] = False

        return [session_item]
