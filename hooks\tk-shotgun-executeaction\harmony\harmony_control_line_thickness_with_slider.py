########################################################################################
#
# Copyright (c) 2023 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty Animation Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
########################################################################################

"""
An app that helps connect selected nodes' thickness parameter to a slider to be able to
control many drawings' thickness at once, or even animate it.
"""


import os
import pprint

from tank import Hook
from tank.platform.qt import QtCore, QtGui


QApplication = QtGui.QApplication
QMessageBox = QtGui.QMessageBox
QDialog = QtGui.QDialog
QRadioButton = QtGui.QRadioButton
QComboBox = QtGui.QComboBox
QButtonGroup = QtGui.QButtonGroup
QGroupBox = QtGui.QGroupBox
QGridLayout = QtGui.QGridLayout
QLabel = QtGui.QLabel
QLineEdit = QtGui.QLineEdit
QPushButton = QtGui.QPushButton
QVBoxLayout = QtGui.QVBoxLayout
QSpinBox = QtGui.QSpinBox


class ProcessItemsHook(Hook):
    def execute(self, **kwargs):

        self.parent.engine.logger.info(
            "execute action control_line_thickness_with_slider start. ".ljust(120, "-")
        )

        result = {"succes": [], "messages": [], "errors": []}

        # first we make sure at least one node is selected
        num_of_selected_nodes = self.get_number_of_selected_nodes()
        self.parent.engine.logger.info(
            "num_of_selected_nodes: {}".format(num_of_selected_nodes)
        )

        if int(num_of_selected_nodes) == 0:
            msg = (
                "Please select at least one node (group node or read node) "
                "and try again.\n"
            )
            self.show_message("Error", msg, icon="Critical")
            return {"succes": [], "messages": [msg], "errors": [1]}

        # filter selected nodes to see if we can find read nodes, either in the original
        # selection or in the subnodes after propagating the selection
        read_nodes = self.filter_selected_nodes_to_read_nodes()
        self.parent.engine.logger.info("read_nodes: {}".format(read_nodes))
        number_of_read_nodes = len(read_nodes)
        self.parent.engine.logger.info(
            "number_of_read_nodes: {}".format(number_of_read_nodes)
        )

        if int(number_of_read_nodes) == 0:
            msg = (
                (
                "Couldn't find any read nodes in the original selection or "
                "its subnodes."
                )
            )
            self.show_message("Error", msg, icon="Critical")
            return {"succes": [], "messages": [msg], "errors": [1]}

        slider_peg_paths = self.get_slider_paths()
        self.parent.engine.logger.info("slider_peg_paths: {}".format(slider_peg_paths))

        # get the scale independent option
        self.get_user_options(slider_peg_paths)

        # self.user_options is initialized inside of the
        # get_scale_independent_option method
        self.parent.engine.logger.info(
            "user_options: {}".format(pprint.pformat(self.user_options))
        )
        scale_independent_option = self.user_options.get("scale_independent")
        disconnect_nodes_option = self.user_options.get("disconnect_nodes")

        # if user selected to disconnect the nodes from a slider
        if disconnect_nodes_option:
            cmd_result = self.connect_nodes_to_slider(
                disconnect_only=True,
                scale_independent_option=None
            )
            self.parent.engine.logger.info(
                "disconnect_only cmd_result: {}".format(cmd_result)
            )
            if "true" in cmd_result.keys():
                # update / reset expression columns
                self.update_expr_columns()

                msg = cmd_result["true"]
                self.show_message("Success", msg, icon="Information")
                return {"success": [1], "messages": [msg], "errors": []}

            elif "false" in cmd_result.keys():
                # update / reset expression columns
                self.update_expr_columns()

                msg = cmd_result["false"]
                self.show_message("Error", msg, icon="Critical")
                return {"success": [], "messages": [msg], "errors": [1]}

        if not scale_independent_option:
            # update / reset expression columns
            self.update_expr_columns()

            msg = "Cancelled by the user"
            self.show_message("Error", msg, icon="Critical")
            return {"success": [], "messages": [msg], "errors": [1]}

        # if user selected to connect the nodes to a slider
        cmd_result = self.connect_nodes_to_slider(
            disconnect_only=False,
            scale_independent_option=scale_independent_option
        )

        self.parent.engine.logger.info(
            "cmd_result: {}, type: {}".format(cmd_result, type(cmd_result))
        )

        if "false" in cmd_result.keys():
            # update / reset expression columns
            self.update_expr_columns()

            msg = cmd_result["false"]
            self.show_message("Error", msg, icon="Critical")
            return {"success": [], "messages": [msg], "errors": [1]}
        elif "true" in cmd_result.keys():
            # update / reset expression columns
            self.update_expr_columns()

            msg = cmd_result["true"]
            self.show_message("Success", msg, icon="Information")
            return {"success": [1], "messages": [msg], "errors": []}

        # update / reset expression columns
        self.update_expr_columns()

        return result


    def show_message(self, title, msg, icon=None):
        """
        Shows a message box with the input message
        """

        icons_dict = {
            "NoIcon": QMessageBox.NoIcon,
            "Question": QMessageBox.Question,
            "Information": QMessageBox.Information,
            "Warning": QMessageBox.Warning,
            "Critical": QMessageBox.Critical,
        }

        if not icon or icon not in icons_dict.keys():
            icon_selection = icons_dict["NoIcon"]
        else:
            icon_selection = icons_dict[icon]

        msg_error = QMessageBox()
        msg_error.setWindowTitle(title)
        msg_error.setText(msg)
        msg_error.setIcon(icon_selection)
        msg_error.raise_()
        msg_error.activateWindow()
        msg_error.exec_()


    def get_number_of_selected_nodes(self):
        cmd = """
var selected_nodes = selection.selectedNodes();
selected_nodes.length;
"""
        number_of_selected_nodes = self.parent.engine.app.execute(cmd)
        return number_of_selected_nodes


    def filter_selected_nodes_to_read_nodes(self):
        cmd = """
include("harmony_utility_functions.js");
get_nodes_and_subnodes("READ", get_selected_nodes());
"""
        read_nodes = self.parent.engine.app.execute(cmd)
        return read_nodes


    def get_slider_paths(self):
        cmd = """
include("harmony_utility_functions.js");
get_nodes_and_subnodes("PEG", ["Top"]);
"""
        all_peg_nodes = self.parent.engine.app.execute(cmd)

        peg_nodes = []
        for peg in all_peg_nodes:
            if "Slider_CTR-P" in peg:
                peg_nodes.append(peg)
        return peg_nodes


    def get_user_options(self, slider_paths):
        # initialize out dictionary
        self.user_options = {
            "selected_peg_node": None,
            "scale_independent": None,
            "disconnect_nodes": None,
        }

        def accept_button_action(radio_group):
            selected_button = radio_group.checkedButton()

            if selected_button:
                self.user_options["selected_peg_node"] = combo_box.currentText()
                self.user_options["scale_independent"] = selected_button.text()
                self.user_options["disconnect_nodes"] = None
                self.parent.engine.logger.debug(
                    (
                        "accept_button pressed:\n"
                        "selected_peg_node: {}\n"
                        "scale_independent: {}\n"
                        "disconnect_nodes: {}"
                    ).format(
                        self.user_options["selected_peg_node"],
                        self.user_options["scale_independent"],
                        self.user_options["disconnect_nodes"]
                    )
                )
                dialog.accept()
            else:
                self.user_options["selected_peg_node"] = None
                self.user_options["scale_independent"] = None
                self.user_options["disconnect_nodes"] = None
                dialog.reject()

        def cancel_button_action():
            self.user_options["selected_peg_node"] = None
            self.user_options["scale_independent"] = None
            self.user_options["disconnect_nodes"] = None
            self.parent.engine.logger.debug(
                (
                    "cancel_button pressed:\n"
                    "selecte_peg_node: {}\n"
                    "scale_independent: {}\n"
                    "disconnect_nodes: {}"
                ).format(
                    self.user_options["selected_peg_node"],
                    self.user_options["scale_independent"],
                    self.user_options["disconnect_nodes"]
                )
            )
            dialog.reject()

        def disconnect_button_action():
            self.user_options["selected_peg_node"] = combo_box.currentText()
            self.user_options["scale_independent"] = None
            self.user_options["disconnect_nodes"] = True
            self.parent.engine.logger.debug(
                (
                    "disconnect_button pressed:\n"
                    "selected_peg_node: {}\n"
                    "scale_independent: {}\n"
                    "disconnect_nodes: {}"
                ).format(
                    self.user_options["selected_peg_node"],
                    self.user_options["scale_independent"],
                    self.user_options["disconnect_nodes"]
                )
            )
            dialog.accept()

        # create the dialog
        dialog = QDialog()
        dialog.setWindowTitle("User Options")
        # Set the width of the dialog (e.g., 400 pixels)
        dialog.setFixedWidth(250)

        layout = QVBoxLayout()

        radio_group = QButtonGroup(dialog)

        radio_buttons_group = QGroupBox("Scale Dependent options")
        radio_buttons_layout = QVBoxLayout()

        scale_dependent_radio = QRadioButton("Scale Dependent")
        scale_independent_radio = QRadioButton("Scale Independent")
        legacy_radio = QRadioButton("Scale Independent (legacy)")

        # Set the default selection (e.g., "Scale Dependent")
        scale_dependent_radio.setChecked(True)

        radio_group.addButton(scale_dependent_radio)
        radio_group.addButton(scale_independent_radio)
        radio_group.addButton(legacy_radio)

        radio_buttons_layout.addWidget(scale_dependent_radio)
        radio_buttons_layout.addWidget(scale_independent_radio)
        radio_buttons_layout.addWidget(legacy_radio)

        radio_buttons_group.setLayout(radio_buttons_layout)

        # Create a QGroupBox to group the combo_box
        select_slider_group = QGroupBox("Select Slider")
        select_slider_layout = QVBoxLayout()

        # Create a QComboBox for the node_paths
        combo_box = QComboBox()
        # Add items based on node_names
        combo_box.addItem("New Slider")
        # combo_box.addItems([os.path.basename(path) for path in slider_paths])
        combo_box.addItems(slider_paths)

        # Depending on the amount of sliders contained in slider_paths argument, set the
        # default selection
        if len(slider_paths) > 0:
            # Set the default combo_box selection to an existing slider
            combo_box.setCurrentIndex(1)
        else:
            # Set the default combo_box selection to "New Slider"
            combo_box.setCurrentIndex(0)

        select_slider_layout.addWidget(combo_box)

        # Set the select_slider_layout as the layout for the group
        select_slider_group.setLayout(select_slider_layout)

        # Add the select_slider_group to the main layout
        layout.addWidget(select_slider_group)

        disconnect_button = QPushButton("Disconnect nodes")
        disconnect_button.clicked.connect(disconnect_button_action)

        disconnect_button_group = QGroupBox("Disconnect nodes")
        disconnect_button_layout = QVBoxLayout()

        disconnect_button_layout.addWidget(disconnect_button)
        disconnect_button_group.setLayout(disconnect_button_layout)

        accept_button = QPushButton("Accept")
        accept_button.clicked.connect(lambda: accept_button_action(radio_group))
        accept_button.setDefault(True)

        cancel_button = QPushButton("Cancel")
        cancel_button.clicked.connect(cancel_button_action)

        layout.addWidget(radio_buttons_group)
        layout.addWidget(disconnect_button_group)
        layout.addWidget(accept_button)
        layout.addWidget(cancel_button)

        dialog.setLayout(layout)

        # app = QApplication(sys.argv)
        dialog_result = dialog.exec_()

        # return self.user_options


    def verify_node_exists(self, node_name):
        desired_node_path = "Top/{}".format(node_name)
        verify_node_cmd = (
            """
        include("harmony_utility_functions.js");

        verify_if_node_exists("%s");
        """
            % desired_node_path
        )

        node_exists = self.parent.engine.app.execute(verify_node_cmd)

        return node_exists


    def connect_nodes_to_slider(
            self, disconnect_only=False, scale_independent_option="Scale Independent"
    ):

        # Store original selection
        original_selection = self.parent.engine.app.execute(
            "selection.selectedNodes();"
        )
        # ensure we have only raw strings in the list
        if original_selection:
            original_selection = [str(node) for node in original_selection]
        slider_node_path = None

        if disconnect_only:
            disconnect_only = "true"
        else:
            disconnect_only = "false"


            # check if we need to connect nodes to an existing slider or if we need to
            # create a new one, by importing the tpl
            if (
                self.user_options["selected_peg_node"]
                and self.user_options["selected_peg_node"] != "New Slider"
            ):
                slider_node_path = self.user_options["selected_peg_node"]
            else:
                disk_location = self.disk_location
                self.parent.logger.info("disk_location: {}".format(disk_location))

                tpl_path = os.path.join(disk_location, "tpls", "Slider_Pipeline.tpl")
                tpl_path = tpl_path.replace(os.sep, "/")
                self.parent.logger.debug("tpl_path: {}".format(tpl_path))

                # import the tpl -----------------------------------------------------------
                tpl_cmd = (
                    """
                include("harmony_utility_functions.js");

                import_tpl("%s");
                """
                ) % tpl_path

                slider_nodes = self.parent.engine.app.execute(tpl_cmd)
                if not slider_nodes:
                    msg = "Couldn't import tpl from path: {}".format(tpl_path)
                    self.show_message("Error", msg, icon="Critical")
                    return {"succes": [], "messages": [msg], "errors": [1]}

                self.parent.logger.info("slider_nodes: {}".format(slider_nodes))

                # find the right slider node
                for slider_node in slider_nodes:
                    if "Slider_CTR-P" in slider_node:
                        slider_node_path = slider_node
                        break

            self.parent.logger.info("slider_node_path: {}".format(slider_node_path))

            if not slider_node_path:
                msg = (
                    "Couldn't find the 'Slider_CTR-P' node in the tpl nodes.\n\n"
                    "tpl nodes:\n{}"
                ).format(pprint.pformat(slider_nodes))
                self.show_message("Error", msg, icon="Critical")
                return {"succes": [], "messages": [msg], "errors": [1]}

        # after the slider tpl has been imported, restore the original selection
        restore_Selection_cmd = """
        selection.clearSelection();
        selection.addNodesToSelection(%s);
        """ % original_selection

        self.parent.engine.app.execute(restore_Selection_cmd)

        cmd_pt1 = """
include("harmony_utility_functions.js");

var log = MessageLog.trace;
var slider_node = "%s"
var scale_independent = "%s";

""" % (slider_node_path, scale_independent_option)


        cmd_pt2 = r"""

function get_slider_column(slider_node, target_column) {
    // usually target_column would be either "position.X" or "position.Y"
    var slider_column = node.linkedColumn(slider_node, target_column);
    var column_name = column.getDisplayName(slider_column);
    return column_name;
}

function get_column_from_slider_peg_attr(slider_peg_node, attr_name) {
    var column_attr = node.getTextAttr(slider_peg_node, frame.current(), attr_name);
    return column_attr;
}

function set_expr_column_text(expr_column, slider_column) {
    var expr_text = 'slider_ctr = value("' + slider_column + '", currentFrame);\n' +
        'if (slider_ctr < 0) {\n' +
        '    0;\n' +
        '} else if (slider_ctr < 1) {\n' +
        '    slider_ctr;\n' +
        '} else {\n' +
        '    slider_ctr * slider_ctr;\n' +
        '}';

    log(expr_text);

    var added_expr = column.setTextOfExpr(expr_column, expr_text);

    return added_expr;
}

function enable_thickness_attrs(src_node, scale_independent) {
    node.setTextAttr(src_node, "ADJUST_PENCIL_THICKNESS", 1, true);
    node.setTextAttr(src_node, "NORMAL_LINE_ART_THICKNESS", 1, true);
    // possible values:
    //   - Scale Dependent
    //   - Scale Independent
    //   - Scale Independent(legacy)
    node.setTextAttr(src_node, "ZOOM_INDEPENDENT_LINE_ART_THICKNESS", 1, scale_independent);

    // disconnect from any existing connection (Proportional in the UI)
    node.unlinkAttr(src_node, "MULT_LINE_ART_THICKNESS");
}

function connect_nodes_to_slider(slider_column, nodes_array, scale_independent) {
    // options.scale_independent comes from the user selection in the Dialog
    // var scale_independent = options.scale_independent;
    for (var i = 0; i < nodes_array.length; i++) {
        var current_node = nodes_array[i];
        // first make sure the thickness attributes are enabled
        enable_thickness_attrs(current_node, scale_independent);
        node.linkAttr(current_node, "MULT_LINE_ART_THICKNESS", slider_column);
    }
}

function main(disconnect_only) {
    var result = {};

    // common code for all scenarios ---------------------------------------------------
    // very if at least one node is selected
    var original_selection = selection.selectedNodes();
    if (original_selection.length < 1) {
        // selection.clearSelection();
        // selection.addNodesToSelection(original_selection);
        result["false"] = "No nodes selected";
        return result;
    }

    // propagate selection based on the originally selected nodes
    var selected_nodes_propagated = get_nodes_and_subnodes("READ",original_selection);
    log("selected_nodes_propagated:");
    log(JSON.stringify(selected_nodes_propagated, null, 2));

    // keep only read nodes
    var read_nodes = filter_nodes(selected_nodes_propagated, "READ");
    // log filtered read nodes. At this point, only read nodes should be included in
    // the read_nodes array.
    log("Read nodes:");
    log(JSON.stringify(read_nodes, null, 2));

    // keep only vector elements
    var vector_read_nodes = filter_vector_read_nodes(read_nodes);
    // log filtered read nodes. At this point, only vector read nodes should be
    // included in the vector_read_nodes array.
    log("Vector Read nodes:");
    log(JSON.stringify(vector_read_nodes, null, 2));

    if (vector_read_nodes.length < 1) {
        selection.clearSelection();
        selection.addNodesToSelection(original_selection);
        result["false"] = "Couldn't find any vector read nodes";
        return result;
    }
    // ---------------------------------------------------------------------------------


    // if user selected to disconnect the nodes ----------------------------------------
    if (disconnect_only === true) {
        scene.beginUndoRedoAccum("Disconnect thickness from slider");
        // disconnect all nodes
        for (var i = 0; i < vector_read_nodes.length; i++) {
            var current_node = vector_read_nodes[i];
            node.unlinkAttr(current_node, "MULT_LINE_ART_THICKNESS");
        }
        // reset thickness attributes
        modify_attr(vector_read_nodes, "ADJUST_PENCIL_THICKNESS", false);
        modify_attr(vector_read_nodes, "MULT_LINE_ART_THICKNESS", 1);

        scene.endUndoRedoAccum();
        selection.clearSelection();
        selection.addNodesToSelection(original_selection);
        result["true"] = "Successfuly disconnected thickness from slider\n\nDisconnected nodes:\n\n" + JSON.stringify(vector_read_nodes, null, 2);
        return result;
    }
    // ---------------------------------------------------------------------------------


    // if user didn't choose to disconnect nodes, then connect them --------------------
    scene.beginUndoRedoAccum("Connect thickness to slider");

    // get already connected or peg slider column
    var slider_column = get_column_from_slider_peg_attr(slider_node, "connected_anim_column")
    if (slider_column === "") {
        slider_column = get_slider_column(slider_node, "position.X");
    }
    log("slider_column: " + slider_column);

    // get existing or create a new expression column
    var expr_column = get_column_from_slider_peg_attr(slider_node, "connected_expr_column");
    if (expr_column === "") {
        expr_column = create_expression_column("Slider_value");
        // set expr_column script text
        var expression_set = set_expr_column_text(expr_column, slider_column)
        log("Expression set correctly: " + expression_set);
    }
    log("expr_column: " + expr_column);

    // connect all read nodes in the filtered array to the slider
    connect_nodes_to_slider(expr_column, vector_read_nodes, scale_independent)

    // add the expression column name as metadata to the selected slider peg to be able
    // to connect extra nodes if desired. Also to have a link between the peg and the
    // column expression, otherwise we are not able to find out to what peg a column
    // is connected, because the only connection is with an expression, with a hardcoded
    // name
    var added_slider_expr_column_metadata = set_node_metadata(slider_node, "connected_expr_column", expr_column);
    log("added_slider_expr_column_metadata: " + added_slider_expr_column_metadata);
    var added_slider_anim_column_metadata = set_node_metadata(slider_node, "connected_anim_column", slider_column);
    log("added_slider_anim_column_metadata: " + added_slider_anim_column_metadata);

    scene.endUndoRedoAccum();
    selection.clearSelection();
    selection.addNodesToSelection(original_selection);
    result["true"] = "Successfuly connected thickness to slider\n\nConnected nodes:\n\n" + JSON.stringify(vector_read_nodes, null, 2);
    return result;
    // ---------------------------------------------------------------------------------
}
"""

        # split the command into diffrent parts because we need to define an empty
        # associative arrray using {}, so the first part of the string is a raw string.
        # The other parts use string formatting, so are not raw.
        cmd_pt3 ="""

main(%s);

""" % disconnect_only

        harmony_cmd = "{}{}{}".format(cmd_pt1, cmd_pt2, cmd_pt3)

        # self.parent.engine.logger.debug(
        #     "harmony_cmd:\n\n{}\n".format(harmony_cmd)
        # )

        cmd_result = self.parent.engine.app.execute(harmony_cmd)

        return cmd_result


    def update_expr_columns(self):
        update_cmd = (
            """
                include("harmony_utility_functions.js");

                reset_expression_columns();
            """
        )

        self.parent.engine.app.execute(update_cmd)
