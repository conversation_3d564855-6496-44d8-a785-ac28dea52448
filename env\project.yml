# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

description: Apps and Engines when launching with a project only context.

################################################################################

includes:
- ./includes/frameworks.yml
- ./includes/settings/tk-3dsmax.yml
- ./includes/settings/tk-desktop.yml
- ./includes/settings/tk-flame.yml
- ./includes/settings/tk-houdini.yml
- ./includes/settings/tk-mari.yml
- ./includes/settings/tk-maya.yml
- ./includes/settings/tk-motionbuilder.yml
- ./includes/settings/tk-nuke.yml
- ./includes/settings/tk-photoshopcc.yml
- ./includes/settings/tk-aftereffects.yml
- ./includes/settings/tk-alias.yml
- ./includes/settings/tk-vred.yml
- ./includes/settings/tk-shell.yml
- ./includes/settings/tk-shotgun.yml
- ./includes/settings/tk-harmony.yml
- ./includes/settings/tk-fusion.yml
- ./includes/settings/tk-krita.yml
- ./includes/settings/tk-substancepainter.yml
- ./includes/settings/tk-premiere.yml
- ./includes/settings/tk-blender.yml

################################################################################
# configuration for all engines to load in a project context

engines:
  tk-3dsmax: "@settings.tk-3dsmax.project"
  tk-desktop: "@settings.tk-desktop.project"
  tk-flame: "@settings.tk-flame.project"
  tk-hiero: "@settings.tk-nuke.hiero.project"
  tk-houdini: "@settings.tk-houdini.project"
  tk-mari: "@settings.tk-mari.project"
  tk-maya: "@settings.tk-maya.project"
  tk-motionbuilder: "@settings.tk-motionbuilder.project"
  tk-nuke: "@settings.tk-nuke.project"
  tk-nukestudio: "@settings.tk-nuke.nukestudio.project"
  tk-photoshopcc: "@settings.tk-photoshopcc.project"
  tk-aftereffects: "@settings.tk-aftereffects.project"
  tk-alias: "@settings.tk-alias.project"
  tk-vred: "@settings.tk-vred.project"
  tk-shell: "@settings.tk-shell.project"
  tk-shotgun: "@settings.tk-shotgun.project"
  tk-harmony: "@settings.tk-harmony.project"
  tk-fusion: "@settings.tk-fusion.project"
  tk-krita: "@settings.tk-krita.project"
  tk-substancepainter: "@settings.tk-substancepainter.project"
  tk-premiere: "@settings.tk-premiere.project"
  tk-blender: "@settings.tk-blender.project"

################################################################################
# reference all of the common frameworks

frameworks: "@frameworks"
