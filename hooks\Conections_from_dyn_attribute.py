def listConectionsFromDynAtt():
    message = "\n----------------\n"
    attr_dictionary = {}

    cmds.select("*:C_*" + "*_geo_0Shape")
    scene_shapes = cmds.ls(sl=True)
    dyn_shapes = []
    for shape in scene_shapes:
        if (
            cmds.attributeQuery("fbCache_Dynamic_Texture", node=shape, exists=True)
            and not "_ex" in shape
        ):
            dyn_shapes.append(shape)

    # Now get connections
    for nodo in dyn_shapes:
        asset_name = nodo.split(":")
        if len(asset_name) > 1:
            message = "{0}\n{1}".format(message, asset_name[0])
            print(asset_name[0])
        input_conections = cmds.listConnections(nodo, c=True, scn=True)
        for cc_attribute in input_conections:
            if "Dyn" in cc_attribute:
                attr_name = cc_attribute.split(".")[1]
                message = "{0}\n  {1}".format(message, attr_name)
                print("  {0}".format(attr_name))

                cc_input_connections = cmds.listConnections(
                    "{0}.{1}".format(nodo, attr_name), s=True, p=True
                )
                cmds.select(cc_attribute)
                cc_aniAttr_connections = cmds.listConnections(
                    cc_input_connections[0], d=True, p=True
                )

                for destination_conection in cc_aniAttr_connections:
                    file_nodes_list = []
                    file_node = ""
                    if "frameExtension" in destination_conection:
                        file_nodes_list.append(destination_conection)
                    if "CTL.ANI" in destination_conection:
                        recursive_check_ctlani(
                            file_nodes_list, destination_conection, 1
                        )

                    if file_nodes_list:
                        for b in file_nodes_list:
                            if "frameExtension" in b:
                                fileNode = b.split(".")[0]
                                filename = cmds.getAttr(
                                    "{0}.fileTextureName".format(fileNode)
                                ).split("/")[-1]
                                pieceName = filename.split("_")[2]
                                message = "{0}\n    -{1}".format(message, pieceName)
                                print("    -{0}".format(pieceName))
                        attr_dictionary[attr_name] = file_nodes_list
    return attr_dictionary


def recursive_check_ctlani(node_list, dest_node, loop):
    if loop < 3:
        ele_con = cmds.listConnections(dest_node, s=True, p=True)
        for con in ele_con:
            if "CTL.ANI" in con:
                recursive_check_ctlani(node_list, con, loop + 1)
            if "frameExtension" in con:
                node_list.append(con)
