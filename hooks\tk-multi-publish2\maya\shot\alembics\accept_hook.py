import pprint
import json

import sgtk

HookBaseClass = sgtk.get_hook_baseclass()

# ====================================================================
pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)
pf = pprint.pformat


class PluginAlembicAssetAcceptHook(HookBaseClass):
    def __init__(self, parent):
        super(PluginAlembicAssetAcceptHook, self).__init__(parent)

    @property
    def name(self):
        return 'PluginAlembicAssetAcceptHook'

    def map_of_publishes_filtered(self, settings, map_of_publishes):
        result = {}
        list_of_not_supported_tasks = (
            settings['list of not supported tasks']
        )
        list_of_not_supported_pipeline_steps = (
            settings['list of not supported pipeline steps']
        )
        list_of_not_supported_entities = (
            settings['list of not supported entities']
        )

        camera_cache_publish_type = (
            settings['camera cache publish type']
        )

        for _key_path in map_of_publishes:
            _publish = map_of_publishes[_key_path]

            is_shot = (
                    _publish['entity']['type'] in
                    list_of_not_supported_entities
            )

            is_camera_cache = (
                    _publish['published_file_type']['name'] ==
                    camera_cache_publish_type

            )

            is_layout = (
                    _publish['task.Task.step.Step.code'] in
                    list_of_not_supported_pipeline_steps
            )

            if is_shot and is_camera_cache and is_layout:
                continue
            else:
                result[_key_path] = _publish

        return result

    def is_context_supported(self, settings, context):
        result = True

        # ------------------------------------------------------------------------------
        # Load value overrides framework
        valueoverrides = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        ) or self.load_framework("mty-framework-valueoverrides")

        if not valueoverrides:
            self.parent.engine.logger.error("Couldn't load value overrides framework")
            # map_of_steps
            supported_steps = settings.get('map_of_pipeline_steps').value
            # map_of_tasks
            supported_tasks = settings.get('map_of_tasks').value
            self.parent.engine.logger.info(
                "got supported_steps from config settings: {}".format(supported_steps)
            )
            self.parent.engine.logger.info(
                "got supported_tasks from config settings: {}".format(supported_tasks)
            )

        # get supported steps and tasks from value overrides
        if valueoverrides:
            override_link = {
                "type": "Task", "id": self.parent.engine.context.task["id"]
            }
            # get supported steps
            default_value_steps_code = "mty.multi.publish2.maya.alembic_geo.supported_steps"
            supported_steps = valueoverrides.get_value(
                default_value_steps_code, link=override_link
            )
            if supported_steps:
                supported_steps = json.loads(supported_steps)
                self.parent.engine.logger.info(
                    "got supported_steps from overrides: {}".format(supported_steps)
                )

            # get supported tasks
            default_value_tasks_code = "mty.multi.publish2.maya.alembic_geo.supported_tasks"
            supported_tasks = valueoverrides.get_value(
                default_value_tasks_code, link=override_link
            )
            if supported_tasks:
                supported_tasks = json.loads(supported_tasks)
                self.parent.engine.logger.info(
                    "got supported_tasks from overrides: {}".format(supported_tasks)
                )

        ctx = context

        list_of_params = [
            (supported_steps['supported'], ctx.step['name'], 'pipeline_step'),
            (supported_tasks['supported'], ctx.task['name'], 'task')
        ]

        key_name = '{type} -> {name}'

        list_of_results = [
            {
                key_name.format(type=x[2], name=x[1]):
                    self.is_supported(x[0], x[1])
            }
            for x in list_of_params
        ]

        print(
            "\n\n- {0} - {1}:\n{2}\n\n".format(
                self.name,
                self.is_context_supported.__name__,
                pf(list_of_results)
            )
        )
        for each in list_of_results:
            if list(each.values())[0] == 'not supported':
                result = False

        return result

    def is_supported(self, list_of_supported_entries, entry):
        result = 'supported'
        if entry.lower() not in list_of_supported_entries:
            result = 'not supported'
        return result
