
cTID = function(s) { return app.charIDToTypeID(s); };
sTID = function(s) { return app.stringIDToTypeID(s); };

function newGroupFromLayers(doc) {
    var desc = new ActionDescriptor();
    var ref = new ActionReference();
    ref.putClass( sTID('layerSection') );
    desc.putReference( cTID('null'), ref );
    var lref = new ActionReference();
    lref.putEnumerated( cTID('Lyr '), cTID('Ordn'), cTID('Trgt') );
    desc.putReference( cTID('From'), lref);
    executeAction( cTID('Mk  '), desc, DialogModes.NO );
};

function undo() {
   executeAction(cTID("undo", undefined, DialogModes.NO));
};

function getSelectedLayers(doc) {
  var selLayers = [];
  newGroupFromLayers();

  var group = doc.activeLayer;
  var layers = group.layers;

  for (var i = 0; i < layers.length; i++) {
    selLayers.push(layers[i]);
  }

  undo();

  return selLayers;
};


function sanitize_string(input_string) {
  // Remove trailing spaces and hyphens
  input_string = input_string.replace(/[\s-_]+$/, "");

  // Replace [/\] with -
  input_string = input_string.replace(/[\/\\]/g, "-");

  // Replace non-standard characters with their closest ASCII equivalent
  input_string = input_string.replace(/[\u00E0\u00E1\u00E2\u00E3\u00E4\u00E5]/g, "a");
  input_string = input_string.replace(/[\u00E8\u00E9\u00EA\u00EB\u0113\u0117]/g, "e");
  input_string = input_string.replace(/[\u00EC\u00ED\u00EE\u00EF]/g, "i");
  input_string = input_string.replace(/[\u00F2\u00F3\u00F4\u00F5\u00F6\u0151\u014D\u014F]/g, "o");
  input_string = input_string.replace(/[\u00F9\u00FA\u00FB\u00FC]/g, "u");
  input_string = input_string.replace(/[\u00F1]/g, "n");
  input_string = input_string.replace(/[\u00E7]/g, "c");
  input_string = input_string.replace(/[\u00C0\u00C1\u00C2\u00C3\u00C4\u00C5]/g, "A");
  input_string = input_string.replace(/[\u00C8\u00C9\u00CA\u00CB\u0112\u0116]/g, "E");
  input_string = input_string.replace(/[\u00CC\u00CD\u00CE\u00CF]/g, "I");
  input_string = input_string.replace(/[\u00D2\u00D3\u00D4\u00D5\u00D6\u0150\u014C\u014E]/g, "O");
  input_string = input_string.replace(/[\u00D9\u00DA\u00DB\u00DC]/g, "U");
  input_string = input_string.replace(/[\u00D1]/g, "N");
  input_string = input_string.replace(/[\u00C7]/g, "C");

  // Remove other non-alphanumeric characters
  input_string = input_string.replace(/[^a-zA-Z0-9\s-_]/g, "");

  return input_string;
}

function main() {
  var selectedLayers = getSelectedLayers(app.activeDocument);
  if (selectedLayers.length === 0) {
    alert("Please select one or more layer groups.");
    return;
  }

  var groupsToRename = [];
  for (var i = 0; i < selectedLayers.length; i++) {
    var layer = selectedLayers[i];
    if (layer.typename === "LayerSet") {
      groupsToRename.push(layer);
    }
  }

  if (groupsToRename.length === 0) {
    alert("No layer groups selected.");
    return;
  }

  // Sort groups bottom to top
  groupsToRename.sort(function(a, b) {
    return a.itemIndex - b.itemIndex;
  });

  var renameData = [];
  var counter = 10;
  var errors = [];

  for (var i = 0; i < groupsToRename.length; i++) {
    var group = groupsToRename[i];
    var originalName = group.name;
    var wasLocked = group.locked;

    // Ignore groups starting with DO_NOT_DELETE or ending with DONOTDELETE
    var startsWithText = "DO_NOT_DELETE";
    var endsWithText = "DONOTDELETE";

    var startsWith = (originalName.length >= startsWithText.length &&
                      originalName.substring(0, startsWithText.length) === startsWithText);

    var endsWith = (originalName.length >= endsWithText.length &&
                    originalName.substring(originalName.length - endsWithText.length) === endsWithText);

    if (startsWith || endsWith) {
      continue; // Skip to the next group
    }

    var newName = "";
    var guideMatch = originalName.match(/^([Oo]utput[ -_])?([Gg]uide(s)?)\s*$/);

    if (guideMatch) {
      // Handle "Guide(s)" pattern
      var baseName = originalName.replace(guideMatch[1], "");
      // sanitize baseName
      baseName = sanitize_string(baseName);
      newName = "Output_" + baseName;
    } else {
      // Handle the default "Output_###_" pattern
      var baseName = originalName;
      var existingMatch = originalName.match(/^(.+[_\- :.,]+)?(\d{3})([_\- :.,]+)?(.+)?/i);

      if (existingMatch) {
        // Captures the 3 digits number
        var order = existingMatch[2];

        // Captures the name part
        var existingNamePart = existingMatch[4] || "";

        var sanitizedName = sanitize_string(existingNamePart);
        newName = "Output_" + pad(counter, 3) + "_" + sanitizedName;
        counter += 10;
      } else {
        // Si no coincide con el patrón existente, aplica el renombrado por defecto
        var sanitizedName = sanitize_string(baseName);
        newName = "Output_" + pad(counter, 3) + "_" + sanitizedName;
        counter += 10;
      }
    }

    renameData.push({ original_name: originalName, new_name: newName, group: group, wasLocked: wasLocked });
  }

  // Invert the order of renameData array to be consistent with how the layers are
  // displayed in the dialog and the order in which they are shown in the layer stack
  renameData.reverse();

  if (renameData.length > 0) {
    showPreviewDialog(renameData, errors);
  } else if (errors.length > 0) {
    alert("Errors encountered during processing:\n" + errors.join("\n"));
  } else {
    alert("No applicable layer groups found for renaming.");
  }
}

function pad(num, size) {
  var s = num + "";
  while (s.length < size) s = "0" + s;
  return s;
}

function showPreviewDialog(data, errors) {
  var dialog = new Window("dialog", "Rename Preview");
  dialog.orientation = "column";
  dialog.alignChildren = "left";

  // Warning group
  var warningGroup = dialog.add("group");
  warningGroup.orientation = "column";
  warningGroup.alignChildren = "left";
  var warningText = warningGroup.add("statictext", undefined, "WARNING: Note that any non-standard character will be either replaced with a standard one or will be removed. Also, any \\ or / will be replaced with a -");

  var previewGroup = dialog.add("group");
  previewGroup.orientation = "column";
  previewGroup.alignChildren = "left";

  for (var i = 0; i < data.length; i++) {
    var item = data[i];
    var rowGroup = previewGroup.add("group");
    rowGroup.orientation = "row";
    rowGroup.alignChildren = "left";
    rowGroup.spacing = 10;

    var originalText = rowGroup.add("statictext", undefined, "Original name:");
    var originalNameText = rowGroup.add("statictext", undefined, item.original_name);
    var arrowText = rowGroup.add("statictext", undefined, "\u00A0=>\u00A0"); // Flecha con espacios
    var newNameText = rowGroup.add("statictext", undefined, "New name:");
    var newNameValueText = rowGroup.add("statictext", undefined, item.new_name);
  }

  var buttonGroup = dialog.add("group");
  buttonGroup.orientation = "row";
  buttonGroup.spacing = 10;

  var renameButton = buttonGroup.add("button", undefined, "Rename");
  renameButton.onClick = function() {
    for (var i = 0; i < data.length; i++) {
      var item = data[i];
      try {
        if (item.wasLocked) {
          item.group.locked = false;
          item.group.name = item.new_name;
          item.group.locked = true;
        } else {
          item.group.name = item.new_name;
        }
      } catch (e) {
        errors.push("Error renaming '" + item.original_name + "': " + e);
      }
    }
    dialog.close();
    if (errors.length > 0) {
      alert("Errors encountered during renaming:\n" + errors.join("\n"));
    }
  };

  var cancelButton = buttonGroup.add("button", undefined, "Cancel");
  cancelButton.onClick = function() {
    dialog.close();
    if (errors.length > 0) {
      alert("Errors encountered during processing:\n" + errors.join("\n"));
    }
  };

  dialog.show();
}

main();
