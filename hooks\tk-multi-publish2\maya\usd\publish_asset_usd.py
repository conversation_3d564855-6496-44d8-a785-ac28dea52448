# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

import os
import re
import shutil
import pprint
import tempfile
import traceback

import sgtk
from sgtk.util.filesystem import copy_file, ensure_folder_exists

import maya.cmds as cmds
import maya.mel as mel
import pymel.core as pm

HookBaseClass = sgtk.get_hook_baseclass()


class USDcAssetPublishPlugin(HookBaseClass):
    @property
    def icon(self):
        """
        Path to an png icon on disk
        """
        # look for icon one level up from main's config "icons" folder
        return os.path.join(
            self.disk_location,
            os.pardir,
            os.pardir,
            "icons",
            "usd_publish.png"
        )

    @property
    def name(self):
        return "Publish USDc Asset to Shotgun"

    @property
    def description(self):

        return 'Export USDc Asset'

    @property
    def settings(self):
        plugin_settings = super(USDcAssetPublishPlugin, self).settings or {}

        # settings specific to this class
        maya_publish_settings = {
            "Work Template": {
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "Primary Publish Template": {
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "USDc Publish Template": {
                "type": "template",
                "default": None,
                "description": "Template path for published USD file. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "USDc Publish type": {
                "type": "string",
                "default": "USDc Asset",
                "description": "The published file type to register.",
            },
            "USDa Publish Template": {
                "type": "template",
                "default": None,
                "description": "Template path for published USD file. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "USDa Publish type": {
                "type": "string",
                "default": "USDa Asset",
                "description": "The published file type to register.",
            },
        }

        # update the base settings
        plugin_settings.update(maya_publish_settings)

        return plugin_settings

    @property
    def item_filters(self):
        return ["maya.session.high_roots.usd"]

    def get_root_item(self, item):

        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root

    def _publish_name(self, publish_path):
        publish_name = self._regex_replace(
            regex=r'_v\d{3}',
            source_string=os.path.basename(publish_path),
            replace_string=''
        )
        return publish_name

    def _regex_replace(self, regex, source_string, replace_string):
        result = ''
        pattern = re.compile(regex)
        result = pattern.sub(repl=replace_string, string=source_string)
        return result

    ############################################################################
    # standard publish plugin methods

    def accept(self, settings, item):

        item.properties['accepted'] = False

        # get the main scene
        scene_name = cmds.file(query=True, sn=True)
        if not scene_name:
            raise Exception("Please Save your file before Publishing")

        if self.parent.engine.context.step['name'] != 'Model':
            return {"accepted": False}

        # --------------------------------------------------------------------------------

        # collect node info from scene
        nodes = self.getObjsByAttr('export')
        # and only process if there are actual exportable nodes
        if [k for k in nodes if nodes[k]['export']]:
            # return the accepted info
            return {"accepted": True, "checked": True}

        return {"accepted": False}

    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish.

        Returns a boolean to indicate validity.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process

        :returns: True if item is valid, False otherwise.
        """

        plugins = cmds.pluginInfo(query=True, listPlugins=True)
        if 'mayaUsdPlugin' not in plugins:
            try:
                cmds.loadPlugin('mayaUsdPlugin')
            except:
                message = (
                    "You don't have the USD plugin!\n"
                    "You can get the latest version from here:\n%s"
                )
                usd_url = "https://github.com/Autodesk/maya-usd/releases"
                raise Exception(message % usd_url)

        publisher = self.parent
        path = item.properties.get("path")

        work_template_name = settings["Work Template"].value
        work_template = self.parent.engine.get_template_by_name(
            work_template_name
        )

        scene_path = os.path.abspath(cmds.file(query=True, sn=True))
        fields = work_template.get_fields(scene_path)
        publish_version = fields["version"]

        usdc_publish_template_name = settings["USDc Publish Template"].value

        usdc_publish_template = self.parent.engine.get_template_by_name(
            usdc_publish_template_name
        )

        usdc_publish_path = usdc_publish_template.apply_fields(fields)


        usda_publish_template_name = settings["USDa Publish Template"].value

        usda_publish_template = self.parent.engine.get_template_by_name(
            usda_publish_template_name
        )

        usda_publish_path = usda_publish_template.apply_fields(fields)

        item.properties["publish_version"] = publish_version

        usdc_publish_name = self._publish_name(usdc_publish_path)
        item.properties["usdc_publish_name"] = usdc_publish_name
        item.properties["usdc_publish_path"] = usdc_publish_path

        usda_publish_name = self._publish_name(usda_publish_path)
        item.properties["usda_publish_name"] = usda_publish_name
        item.properties["usda_publish_path"] = usda_publish_path

        item.properties["publish_fields"] = fields

        # get the exportable nodes and validate them
        nodes = self.getObjsByAttr('export')
        expotable_nodes = [k for k in nodes if nodes[k]['export']]
        if len(expotable_nodes) > 1:
            raise Exception("You have more than one exportable group!")
        elif len(expotable_nodes) == 0:
            raise Exception("You don't have any exportable group!")

        item.properties["asset_node"] = expotable_nodes[0]


        publishes = publisher.util.get_conflicting_publishes(
            item.context,
            usdc_publish_path,
            usdc_publish_name,
            filters=["sg_status_list", "is_not", None],
        )

        return True

    def getObjsByAttr(self, attr):

        objs = {}

        exportableNodes = cmds.ls(
            '*.{}'.format(attr),
            objectsOnly=True,
            transforms=True
        )

        for node in exportableNodes:
            if not cmds.listRelatives(node, shapes=True):
                attrValue = cmds.getAttr('{}.{}'.format(node, attr))

                attrDIct = {attr: attrValue}
                objs[node] = attrDIct

        return objs

    def exportUSDc(self, node, usd_path):

        try:
            if cmds.getAttr("{}.subdivide".format(node)) == True:
                meshScheme = "catmullClark"
        except:
            meshScheme = "none"

        options = (
            "exportUVs=1;"
            "exportSkels=none;"
            "exportSkin=none;"
            "exportBlendShapes=0;"
            "exportColorSets=0;"
            "defaultMeshScheme={meshScheme};"
            "defaultUSDFormat=usdc;"
            "animation=0;"
            "eulerFilter=0;"
            "staticSingleSample=0;"
            "startTime=1;"
            "endTime=1;"
            "frameStride=1;"
            "frameSample=0.0;"
            "parentScope=;"
            "exportDisplayColor=0;"
            "shadingMode=none;"
            "exportInstances=0;"
            "exportVisibility=0;"
            "mergeTransformAndShape=1;"
            "stripNamespaces=1"
        )

        cmds.select(node, replace=True)

        exp = cmds.file(
            usd_path,
            force=True,
            exportSelected=True,
            type="USD Export",
            options=options.format(meshScheme=meshScheme)
        )

        cmds.select( clear=True )

    def saveAssetUsdPlaceholder(self, usdc_path, entity_name, usda_path):

        names = {'entity_name': entity_name, 'usdc_path': usdc_path}

        # TODO: the current relative paths are kind of hardcoded
        usdc_path = os.path.basename(usdc_path)

        usda_contents = ('#usda 1.0\n'
                        '(\n'
                        '    endTimeCode = 1\n'
                        '    framesPerSecond = 24\n'
                        '    metersPerUnit = 1\n'
                        '    startTimeCode = 1\n'
                        '    timeCodesPerSecond = 24\n'
                        '    upAxis = "Y"\n'
                        ')\n\n'
                        'def "{entity_name}" (\n'
                        '    prepend references = @./geo/{usdc_path}@\n'
                        ')\n'
                        '{{\n'
                        '}}\n')
        usda_contents = usda_contents.format(**names)

        with open(usda_path, "w") as usda_file:
            usda_file.write(usda_contents)

    def findCurrentUSDAssetContainer(self, context, publish_type, publish_name):

        publish = None

        filters = [
            ['project', 'is', context.project],
            ['entity', 'is', context.entity],
            ['published_file_type.PublishedFileType.code', 'is', publish_type],
            ['name', 'is', publish_name],
        ]
        publish = self.parent.shotgun.find_one(
            'PublishedFile', filters
        )

        return publish

    def publish(self, settings, item):
        publish_version = item.properties["publish_version"]

        usdc_publish_name = item.properties["usdc_publish_name"]
        usdc_publish_path = item.properties["usdc_publish_path"]

        usda_publish_name = item.properties["usda_publish_name"]
        usda_publish_path = item.properties["usda_publish_path"]

        fields = item.properties["publish_fields"]

        scene_pub_template_name = settings["Primary Publish Template"].value
        scene_pub_template = self.parent.engine.get_template_by_name(
            scene_pub_template_name)
        primary_publish_path = scene_pub_template.apply_fields(fields)

        node_name = item.properties["asset_node"]

        self.parent.engine.ensure_folder_exists(
            os.path.dirname(usdc_publish_path)
        )

        tmp_dir = tempfile.mkdtemp()
        tmp_path = os.path.join(tmp_dir, os.path.basename(usdc_publish_path))

        self.exportUSDc(node_name, tmp_path)

        shutil.move(tmp_path, usdc_publish_path)

        try:
            shutil.rmtree(tmp_dir)
        except:
            pass

        publisher = self.parent

        dependencies = [primary_publish_path]

        sg_fields_to_update = {
            "sg_status_list": "rev",
            "task.Task.sg_status_list": "rev",
        }

        publish_data = {
            "tk": self.parent.engine.sgtk,
            "context": item.context,
            "comment": item.description,
            "path": usdc_publish_path,
            "name": usdc_publish_name,
            "version_number": publish_version,
            "thumbnail_path": item.get_thumbnail_as_path(),
            "task": self.parent.engine.context.task,
            "sg_fields": sg_fields_to_update,
            "published_file_type": settings["USDc Publish type"].value,
            "dependency_paths": dependencies,
        }
        sg_publishes = sgtk.util.register_publish(**publish_data)

        item.properties.sg_publish_data = sg_publishes


        # also publish the USDa if there is not already
        currentUSDAsset = self.findCurrentUSDAssetContainer(
            self.parent.engine.context,
            settings["USDa Publish type"].value,
            usda_publish_name
        )

        if currentUSDAsset:
            # skip the export and publish, since a
            # version of the asset already exists
            return

        self.parent.engine.ensure_folder_exists(
            os.path.dirname(usda_publish_path)
        )

        self.saveAssetUsdPlaceholder(
            usdc_publish_path,
            self.parent.engine.context.entity['name'],
            usda_publish_path
        )

        dependencies = [usdc_publish_path]

        sg_fields_to_update = {
            "sg_status_list": "rev",
            "task.Task.sg_status_list": "rev",
        }

        publish_data = {
            "tk": self.parent.engine.sgtk,
            "context": item.context,
            "comment": item.description,
            "path": usda_publish_path,
            "name": usda_publish_name,
            "version_number": publish_version,
            "thumbnail_path": item.get_thumbnail_as_path(),
            "task": self.parent.engine.context.task,
            "sg_fields": sg_fields_to_update,
            "published_file_type": settings["USDa Publish type"].value,
            "dependency_paths": dependencies,
        }
        sg_publishes = sgtk.util.register_publish(**publish_data)

        # finally just store the publish data for later retrieval
        # and upload to the host storage location
        root_item = self.get_root_item(item)
        root_item.properties.setdefault('sg_publish_extra_data', [])

        publish_extra_data = root_item.properties['sg_publish_extra_data']
        publish_extra_data.append(sg_publishes)


    def finalize(self, settings, item):
        pass
