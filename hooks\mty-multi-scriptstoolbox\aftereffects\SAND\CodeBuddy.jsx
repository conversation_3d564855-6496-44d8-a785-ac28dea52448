// Polyfill for JSON object
if (typeof JSON === "undefined") {
    JSON = {
        parse: function (sJSON) {
            return eval("(" + sJSON + ")");
        },
        stringify: function (vContent) {
            if (vContent instanceof Object) {
                var sOutput = "";
                if (vContent.constructor === Array) {
                    for (var nId = 0; nId < vContent.length; sOutput += this.stringify(vContent[nId]) + ",", nId++);
                    return "[" + sOutput.substr(0, sOutput.length - 1) + "]";
                }
                if (vContent.toString !== Object.prototype.toString) {
                    return "\"" + vContent.toString().replace(/"/g, "\\$&") + "\"";
                }
                for (var sProp in vContent) {
                    sOutput += "\"" + sProp.replace(/"/g, "\\$&") + "\":" + this.stringify(vContent[sProp]) + ",";
                }
                return "{" + sOutput.substr(0, sOutput.length - 1) + "}";
            }
            return typeof vContent === "string" ? "\"" + vContent.replace(/"/g, "\\$&") + "\"" : String(vContent);
        }
    };
}

function CodeBuddy(thisObj) {
    var scriptName = "CodeBuddy";

    if (!thisObj) {
        thisObj = new Window("palette", scriptName, undefined, { resizable: true });
    }

    var expressions = {};

    if (app.settings.haveSetting(scriptName, "expressions")) {
        expressions = JSON.parse(app.settings.getSetting(scriptName, "expressions"));
    }

    var sortedExpressionKeys = [];
    for (var key in expressions) {
        if (expressions.hasOwnProperty(key)) {
            sortedExpressionKeys.push(key);
        }
    }
    sortedExpressionKeys.sort();

    thisObj.grp = thisObj.add("group");
    thisObj.grp.orientation = "column";

    var applyAndRemoveBtns = thisObj.grp.add("group");
    applyAndRemoveBtns.orientation = "row";
    applyAndRemoveBtns.add("button", undefined, "Apply").helpTip = "Apply Expression";
    applyAndRemoveBtns.add("button", undefined, "Remove").helpTip = "Remove Expression";

    var dropdown = thisObj.grp.add("dropdownlist", undefined, sortedExpressionKeys);
    dropdown.alignment = ["fill", "fill"];

    var editAndAddBtns = thisObj.grp.add("group");
    editAndAddBtns.orientation = "row";
    editAndAddBtns.add("button", undefined, "Edit").helpTip = "Edit Expression";
    editAndAddBtns.add("button", undefined, "Add New").helpTip = "Add new expression";

    editAndAddBtns.children[1].onClick = function () {
        var editWin = new Window("dialog", "Add New Expression");
        editWin.orientation = "column";
        editWin.alignChildren = ["left", "top"];
        editWin.spacing = 10;
        editWin.margins = 16;

        var nameField = editWin.add("edittext", undefined, "Expression Name");
        nameField.characters = 30;
        editWin.add("statictext", undefined, "Expression:");
        var exprField = editWin.add("edittext", undefined, "", { multiline: true });
        exprField.characters = 30;
        exprField.minimumSize.height = 100;
        var btns = editWin.add("group");
        btns.orientation = "row";
        btns.add("button", undefined, "Save").onClick = function () {
            if (nameField.text !== "") {
                expressions[nameField.text] = exprField.text;
                app.settings.saveSetting(scriptName, "expressions", JSON.stringify(expressions));
                sortedExpressionKeys = [];
                for (var key in expressions) {
                    if (expressions.hasOwnProperty(key)) {
                        sortedExpressionKeys.push(key);
                    }
                }
                sortedExpressionKeys.sort();
                dropdown.removeAll();
                for (var i = 0; i < sortedExpressionKeys.length; i++) {
                    dropdown.add("item", sortedExpressionKeys[i]);
                }
                dropdown.selection = dropdown.find(nameField.text);
                editWin.close();
            }
        };
        btns.add("button", undefined, "Close").onClick = function () {
            editWin.close();
        };
        editWin.show();
    };

    editAndAddBtns.children[0].onClick = function () {
        if (dropdown.selection) {
            var editWin = new Window("dialog", "Edit Expression");
            editWin.orientation = "column";
            editWin.alignChildren = ["left", "top"];
            editWin.spacing = 10;
            editWin.margins = 16;

            var nameField = editWin.add("edittext", undefined, dropdown.selection.text);
            nameField.characters = 30;
            editWin.add("statictext", undefined, "Expression:");
            var exprField = editWin.add("edittext", undefined, expressions[dropdown.selection.text], { multiline: true });
            exprField.characters = 30;
            exprField.minimumSize.height = 100;
            var btns = editWin.add("group");
            btns.orientation = "row";
            btns.add("button", undefined, "Save").onClick = function () {
                delete expressions[dropdown.selection.text];
                if (nameField.text !== "") {
                    expressions[nameField.text] = exprField.text;
                    app.settings.saveSetting(scriptName, "expressions", JSON.stringify(expressions));
                    sortedExpressionKeys = [];
                    for (var key in expressions) {
                        if (expressions.hasOwnProperty(key)) {
                            sortedExpressionKeys.push(key);
                        }
                    }
                    sortedExpressionKeys.sort();
                    dropdown.removeAll();
                    for (var i = 0; i < sortedExpressionKeys.length; i++) {
                        dropdown.add("item", sortedExpressionKeys[i]);
                    }
                    dropdown.selection = dropdown.find(nameField.text);
                    editWin.close();
                }
            };
            btns.add("button", undefined, "Close").onClick = function () {
                editWin.close();
            };
            btns.add("button", undefined, "Delete").onClick = function () {
                delete expressions[dropdown.selection.text];
                app.settings.saveSetting(scriptName, "expressions", JSON.stringify(expressions));
                sortedExpressionKeys = [];
                for (var key in expressions) {
                    if (expressions.hasOwnProperty(key)) {
                        sortedExpressionKeys.push(key);
                    }
                }
                sortedExpressionKeys.sort();
                dropdown.removeAll();
                for (var i = 0; i < sortedExpressionKeys.length; i++) {
                    dropdown.add("item", sortedExpressionKeys[i]);
                }
                editWin.close();
            };
            editWin.show();
        }
    };

    applyAndRemoveBtns.children[0].onClick = function () {
        if (dropdown.selection) {
            var comp = app.project.activeItem;
            if (comp && comp instanceof CompItem) {
                var selectedProps = comp.selectedProperties;
                for (var i = 0; i < selectedProps.length; i++) {
                    if (selectedProps[i] instanceof Property && selectedProps[i].canSetExpression) {
                        selectedProps[i].expression = expressions[dropdown.selection.text];
                    }
                }
            }
        }
    };

    applyAndRemoveBtns.children[1].onClick = function () {
        var comp = app.project.activeItem;
        if (comp && comp instanceof CompItem) {
            var selectedProps = comp.selectedProperties;
            for (var i = 0; i < selectedProps.length; i++) {
                if (selectedProps[i] instanceof Property && selectedProps[i].canSetExpression) {
                    selectedProps[i].expression = "";
                }
            }
        }
    };

    thisObj.layout.layout(true);
    thisObj.grp.minimumSize = thisObj.grp.size;
    thisObj.layout.resize();
    thisObj.onResizing = thisObj.onResize = function () {
        this.layout.resize();
    };

    if (thisObj != null && thisObj instanceof Window) {
        thisObj.show();
    }
}

CodeBuddy(this);
