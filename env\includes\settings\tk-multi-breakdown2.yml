# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

################################################################################

includes:
- ../app_locations.yml

################################################################################

# After effects
settings.tk-multi-breakdown2.aftereffects.shot_step:
  hook_scene_operations: '{config}/tk-multi-breakdown2/aftereffects/tk-aftereffects_scene_operations.py'
  location: '@apps.tk-multi-breakdown2.location'
  hook_get_published_files: "{config}/tk-multi-breakdown2/get_published_files.py"
  published_file_filters:
    - ["task", "is_not", null]
    - ["entity", "is_not", null]
    - ["sg_status_list", "is", "apr"]
    - ["published_file_type.PublishedFileType.code", "not_in", ["Photoshop Image", "Photoshop Proxy Image"]]
  history_published_file_filters:
    - ["sg_status_list", "is", "apr"]

# Harmony
settings.tk-multi-breakdown2.harmony.shot_step:
  # hook_get_version_number: '{config}/tk-multi-breakdown/get_publish_version_number.py'
  hook_scene_operations: '{config}/tk-multi-breakdown2/harmony/tk-harmony_scene_operations.py'
  hook_get_published_files: "{config}/tk-multi-breakdown2/get_published_files.py"
  location: '@apps.tk-multi-breakdown2.location'
  published_file_filters:
    - ["task", "is_not", null]
    - ["entity", "is_not", null]
    - ["sg_status_list", "is", "apr"]
  #   - ["published_file_type.PublishedFileType.code", "in", ["Photoshop Image", "Photoshop Proxy Image"]]
  history_published_file_filters:
    - ["sg_status_list", "is", "apr"]
