#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

import os
import re
import shutil
import traceback

from os import listdir
from os.path import isfile, join
from pprint import pformat

import hou

import sgtk
from sgtk.platform.qt import QtGui, QtCore


HookBaseClass = sgtk.get_hook_baseclass()


class HoudiniPublishFramesSeqPlugin(HookBaseClass):
    """
    Plugin for publish rendered sequence

    """

    @property
    def name(self):
        """
        One line display name describing the plugin
        """
        return "Publish sequence files"

    @property
    def description(self):
        """
        Copy/upload the render sequence to publish area
        """

        loader_url = "https://support.shotgunsoftware.com/hc/en-us/articles/219033078"

        publisher = self.parent
        engine = publisher.engine
            
        description = """
            This plugin publish copy render files to publish area and 
            register in shotgun.

            Optionally create EXR into publish area with the multiple layers appended.
            """
        
        return "<p>{}</p>".format(description)

    @property
    def settings(self):
        """
        Dictionary defining the settings that this plugin expects to recieve
        through the settings parameter in the accept, validate, publish and
        finalize methods.

        A dictionary on the following form::

            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }

        The type string should be one of the data types that toolkit accepts
        as part of its environment configuration.
        """

        # settings specific to this class
        publish_settings = {

            "Work Template": {
                "type": "template",
                "default": "harmony_shot_work",
                "description": "Harmony work session"},

            "Primary Publish Template": {
                "type": "template",
                "default": "harmony_shot_publish",
                "description": "Harmony publish session"},

            "Publish Render Template": {
                "type": "template",
                "default": "shot_flat_render_publish_exr",
                "description": "Template to publish and copy render sequence files"},

            "Publish Render Type": {
                "type": "str",
                "default": "Rendered Image",
                "description": "Published File Type name to use for the published frames"}

        }

        # inherit the settings from the base publish plugin
        plugin_settings = super(HoudiniPublishFramesSeqPlugin, self).settings or {}

        # update the base settings
        plugin_settings.update(publish_settings)

        return plugin_settings

    @property
    def item_filters(self):
        """
        List of item types that this plugin is interested in.

        Only items matching entries in this list will be presented to the
        accept() method. Strings can contain glob patters such as *, for example
        ["houdini.*", "file.houdini"]
        """
        return ["houdini.frames_sequence"]

    def accept(self, settings, item):
        """
        Method called by the publisher to determine if an item is of any
        interest to this plugin. Only items matching the filters defined via the
        item_filters property will be presented to this method.

        A submit for review task will be generated for each item accepted here. Returns a
        dictionary with the following booleans:

            - accepted: Indicates if the plugin is interested in this value at
                all. Required.
            - enabled: If True, the plugin will be enabled in the UI, otherwise
                it will be disabled. Optional, True by default.
            - visible: If True, the plugin will be visible in the UI, otherwise
                it will be hidden. Optional, True by default.
            - checked: If True, the plugin will be checked in the UI, otherwise
                it will be unchecked. Optional, True by default.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process

        :returns: dictionary with boolean keys accepted, required and enabled.
            This plugin makes use of the tk-multi-reviewsubmission app; if this
            app is not available then the item will not be accepted, this method
            will return a dictionary with the accepted field that has a value of
            False.

            Several properties on the item must also be present for it to be
            accepted. The properties are 'path', 'publish_name', 'color_space',
            'first_frame' and 'last_frame'
        """

        publisher = self.parent
        engine    = publisher.engine
        context   = engine.context

        # Verify entity type SHOT
        entity_type = str(context).split(' ')[1]
        if entity_type != "Shot":
            return {'accepted': False}
        
        return {"accepted": True,
                "checked": True}
        
    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish. Returns a
        boolean to indicate validity.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        :returns: True if item is valid and not in proxy mode, False otherwise.
        """

        sg_shot       = self.get_sg_shot_info(['sg_cut_in', 'sg_cut_out'])
        shot_duartion = int(sg_shot['sg_cut_out']) - int(sg_shot['sg_cut_in'])

        sequence         = item.properties['sequence']
        layers_sequences = item.properties.get('layers_sequences', [])

        # Harmony fames can only start at 1
        # we can only validate if the number of frames matches shot
        sequence_frames = len(sequence)
        if sg_shot['sg_cut_out'] is None or sg_shot['sg_cut_in'] is None:
            error_msg = "Shotgun missin info: Frame range  sg_cut_in sg_cut_out"
            raise Exception(error_msg)

        shot_frames = sg_shot['sg_cut_out'] - sg_shot['sg_cut_in'] + 1
        if sequence_frames != shot_frames:
            error_msg = "Shot lenght and render frames don't match: {0} - {1}"
            raise Exception(error_msg.format(shot_frames, sequence_frames))

        # Other known issue is if the render naming convention uses "-"
        # that will cause conflict with the image sequence recognition
        # and will make the numbers look like negative, which will cause problems

        beauty_start   = sequence.start()
        beauty_end     = sequence.end()
        beauty_padding = sequence.padding()
        item.properties['valid_layers_sequences'] = []

        sequence_name_format = '{basename}{padding}{extension} ({start}-{end})'
        display_name         = sequence.format(sequence_name_format)

        # Validating layers
        for layer_seq in layers_sequences:
            valid     = True
            error_msg = ''
            layer_n   = layer_seq._base[:-2] 

            # Validation 1:  Name
            if not layer_seq._base.endswith('.'):
                valid     = False
                error_msg = 'Invalid name'
            elif '_' in layer_n or '.' in layer_n or ' ' in layer_n:
                valid     = False
                error_msg = 'Invalid name'

            # Validation 2: Frames
            if layer_seq.start() != beauty_start or layer_seq.end() != beauty_end:
                valid = False
                if error_msg != '':  error_msg += ', frame range'
                else:                error_msg  = 'Invalid frame range'

            # Validation 1: Padding
            if layer_seq.padding() != beauty_padding:
                valid = False
                if error_msg != '':  error_msg += ' and invalid padding'
                else:                error_msg  = 'Invalid padding'

            sequence_name_format = '{basename}{padding}{extension} ({start}-{end})'
            display_name         = layer_seq.format(sequence_name_format)
            display_name         = display_name.replace('@@@', '%03d')
            display_name         = display_name.replace('#', '%04d')

            if not valid:
                self.logger.warning('Layer ommited. {}: {}'.format(error_msg, display_name))
                continue

            self.logger.info('Valid layer: {}'.format(display_name))
            item.properties['valid_layers_sequences'].append(layer_seq)
            continue
            
            # Checking first frame number
            if layer_seq.start() != 1:
                self.logger.warning('Layer ommited. Invalid range: {}'.format(display_name))
                continue

            # Checking sequence duration
            seq_duration = layer_seq.start() - layer_seq.end()
            if seq_duration < shot_duartion:
                self.logger.warning('Layer ommited. Invalid duration: {}'.format(display_name))
                continue

            # If layer sequence pass all validations
            self.logger.info('Valid layer: {}'.format(display_name))
            item.properties['valid_layers_sequences'].append(layer_seq)

        return True

    def publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """

        self.parent.log_debug("Publish plugin settings:\n%s" % pformat(settings))

        engine   = self.parent.engine
        
        # TODO: Find a generic way to resolve current dcc file path 
        path = hou.hipFile.path()
        #path     = engine.app.get_current_project_path()
        
        # Collect Sequence object from item
        # and set some variables from it
        sequence = item.properties['sequence']
        beauty_base   = sequence._base
        beauty_folder = sequence._dir
        beauty_ext    = sequence._ext
        padding       = len(sequence._frame_pad)
        frame_number  = sequence.start()
            
        self.parent.log_debug("Sequence beauty_base:\n%s" % beauty_base)
        self.parent.log_debug("Sequence beauty_folder:\n%s" % beauty_folder)
        self.parent.log_debug("Sequence beauty_ext:\n%s" % beauty_ext)
        self.parent.log_debug("Sequence padding:\n%s" % padding)
        self.parent.log_debug("Sequence frame_number:\n%s" % frame_number)
            
        # Getting settings
        primary_publish_template_setting = settings.get("Primary Publish Template")
        render_publish_template_setting  = settings.get("Publish Render Template")
        render_publish_type_setting      = settings.get("Publish Render Type")
        work_template_setting            = settings.get("Work Template")

        # Get templates from settings
        primary_publish_template = engine.get_template_by_name(primary_publish_template_setting.value)
        render_publish_template  = engine.get_template_by_name(render_publish_template_setting.value)
        work_template            = engine.get_template_by_name(work_template_setting.value)

        # Fields from work path
        fields = work_template.get_fields(path)

        # Dynamic paths and elements from templates and settings
        primary_publish_path     = primary_publish_template.apply_fields(fields)
        publish_version          = fields["version"] 

        # template can be dynamic for file extension, in that case we
        # need to set the proper image file extention
        ext = beauty_ext.lower().replace('.', '')
        fields['extension'] = ext

        # finally, apply the fields to resolve the proper path
        render_publish_path      = render_publish_template.apply_fields(fields) 
        render_publish_type_name = render_publish_type_setting.value

        # determine the publish name:
        publish_name = fields["name"]

        # Ensure folder exists
        self.parent.engine.ensure_folder_exists(os.path.dirname(render_publish_path))

        # Shot frame range
        sg_shot      = self.get_sg_shot_info(['sg_cut_in', 'sg_cut_out'])
        offset       = sg_shot['sg_cut_in'] - frame_number
        
        created_multi_exr = False
        layer_names       = []

        valid_layers_count = len(item.properties['valid_layers_sequences'])
        if valid_layers_count and beauty_ext.lower() == '.exr':
            # Multi process creation
            message = 'Creating multilayer exr: {} Layers'
            self.logger.info(message.format(valid_layers_count))

            format_str        = '{}:{}::{}'
            openExr_framework = self.load_framework("mty-framework-openexr")
            openExr           = openExr_framework.exrCoreTools
            openExr.set_binary("multipart")

            publish_parts  = render_publish_path.split('.')

            # Register names of all layer to add to the comments.
            layer_names.append(beauty_base[:-1])
            for sub_layer in item.properties['valid_layers_sequences']:
                layer_names.append(sub_layer._base[:-1])

            # Create folder if does not exists
            container_folder = os.path.dirname(render_publish_path)
            self.parent.engine.ensure_folder_exists(container_folder)

            for source_frame in sequence:
                # we will iterate over all frames, from first to last
                # and we will rename them using shot range
                final_frame       = frame_number + offset
                destination_frame = render_publish_path % final_frame
                cmd               = ['-combine', '-i']
                padding           = len(source_frame.split('.')[1])
                frame_str         = str(frame_number).zfill(padding)
                
                beauty_file       = ''.join(beauty_base, frame_str, beauty_ext)
                beauty_path       = join(beauty_folder, beauty_file)

                # First layer
                cmd.append(format_str.format(source_frame, 0, item.properties['beauty_output']))

                for layer_sequence in item.properties['valid_layers_sequences']:
                    base_name  = layer_sequence._base
                    aov_folder = layer_sequence._dir
                    aov_ext    = layer_sequence._ext
                    aov_file   = '{}{}{}'.format(base_name, frame_str , aov_ext)
                    aov_path   = join(aov_folder, aov_file)
                    # Layer N
                    cmd.append(format_str.format(aov_path, 0, base_name[:-1]))

                cmd.append('-o')
                cmd.append(destination_frame)
                cmd.append('-override')

                _err, _info = openExr.execute_command_list(cmd)
                if _err:
                    created_multi_exr = False
                    message = "Failed to create multi exr: {}\ncommand: {}"
                    message = message.format(_info, cmd)
                    self.logger.error(message)
                    raise Exception(message)

                frame_number +=1
            self.logger.info('Creating multilayer exr: Complete')

        # If the multi layer fails, then we will copy the beauty 
        if not created_multi_exr:
            msg_ = 'Copy sequence to publish area. {}'
            msg_ = msg_.format(os.path.basename(render_publish_path))
            self.logger.info(msg_)
            frame_number = sg_shot['sg_cut_in']
            for source_frame in sequence:

                source_frame = os.path.normpath(source_frame)

                # we will iterate over all frames, from first to last
                # and we will rename them using shot range
                destination_frame = render_publish_path % frame_number

                message = "Copying image sequence frame:\nfrom: {0}\nTo: {1}"
                self.parent.log_debug(
                    message.format(source_frame, destination_frame)
                )

                shutil.copyfile(source_frame, destination_frame)
                frame_number += 1

            msg_ = 'Copy sequence to publish area. Complete'
            self.logger.info(msg_)

        description_updated = item.description
        if len(layer_names):
            layers_str = ', '.join(layer_names)
            if description_updated is None:
                description_updated = ""
            description_updated += '\nLayers included: ' + layers_str

        # finally register the sequence publish
        self.logger.info('Publish sequence in shotgun')
        self.logger.info('path: {}'.format(render_publish_path))

        args = {
            "tk":                  self.parent.engine.sgtk,
            "context":             item.context,
            "comment":             description_updated,
            "path":                render_publish_path,
            "name":                publish_name,
            "version_number":      publish_version,
            "thumbnail_path":      item.get_thumbnail_as_path(),
            "task":                self.parent.engine.context.task,
            "dependency_paths":    [primary_publish_path],
            "sg_status_list":      "rev",
            "published_file_type": render_publish_type_name
        }

        sg_publishes = sgtk.util.register_publish(**args)

        root_item = self.get_root_item(item)
        root_item.properties.setdefault('sg_publish_extra_data', [])
        publish_extra_data = root_item.properties['sg_publish_extra_data']
        publish_extra_data.append(sg_publishes)

        item.properties.sg_publish_data = sg_publishes
        item.properties.sg_publish_path = sg_publishes["path"]["local_path"]

        self.parent.log_debug(
            "Adding existing publish " +
            "(id:{}) ".format(sg_publishes['id']) +
            "as extra data for item: {}".format(item)
        )

        self.logger.info('Publish sequence in shotgun. Complete')
        
        #return True

    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        pass

    def get_sg_shot_info(self, shot_fields):
        engine         = self.parent.engine
        sg             = engine.shotgun
        sg_proj        = engine.context.project

        context_tokens = str(engine.context).split(' ')
        entity_name    = context_tokens[2]
        shot_filter    = [['project', 'is', sg_proj],
                          ['code', 'is', entity_name]]
        # shot_fields  = ['sg_cut_in', 'sg_cut_out']
        sg_shot = sg.find_one('Shot', shot_filter, shot_fields)
        return sg_shot

    def get_root_item(self, item):

        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root