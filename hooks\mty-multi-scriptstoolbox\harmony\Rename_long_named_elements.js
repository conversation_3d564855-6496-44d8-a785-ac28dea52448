include("long_names_validation_utils.js");


var log = MessageLog.trace;

function compute_shorter_node_name(nodeName) {
    var newName = nodeName;

    // start by extracting any version token and getting rid of it
    var versionToken = nodeName.match(/v[0-9]+/);
    if (versionToken) {
        newName = nodeName.replace(versionToken[0], "");
    }

    // also separate any tokens separated by underscores
    var nameTokens = newName.split(/[_-]/);

    // we will title them to join them later
    // but preserving existing uppercase characters
    nameTokens = nameTokens.map(function(t) {
        return t.charAt(0).toUpperCase() + t.slice(1);
    });

    // try to only keep the first 4 characters of each token
    // in the case the token is 8 characters len or shorter, but
    // if its longer, then keep 4 from start and 4 from end, or
    // the corresponding amount to not duplicate characters
    var copyTokens = [];
    for (var i = 0; i < nameTokens.length; i++) {
        copyTokens.push(nameTokens[i]);
    }

    nameTokens = [];
    var char_amount = 5;
    for (var i = 0; i < copyTokens.length; i++) {
        var t = copyTokens[i];
        var tokenLen = t.length;
        if (tokenLen <= 8) {
            nameTokens.push(t.substring(0, char_amount));
        } else {
            var keep = Math.floor(tokenLen / 2);
            var tokenStart = t.substring(0, char_amount);
            var tokenEnd = t.substring(t.length - 4);
            var newToken = tokenStart + tokenEnd.charAt(0).toUpperCase() + tokenEnd.substring(1);
            nameTokens.push(newToken);
        }
    }

    // add the versionToken as nameToken
    if (versionToken) {
        nameTokens.push(versionToken[0])
    }

    // build the new name after the previous removals
    newName = nameTokens.join("");

    var nodeNamesCache = [];
    // and if the length is still too much, remove from the middle
    // while loop to ensure name is not bigger than 20 characters
    while (newName.length > 20) {
        // Modify logging to QtScript compatible logging
        log("new_name: " + newName);
        var remove = newName.length - 10;
        var middle = Math.floor(newName.length / 2);
        var starting = newName.slice(0, middle - Math.floor(remove / 2));
        var ending = newName.slice(middle + Math.floor(remove / 2));
        ending = ending.charAt(0).toUpperCase() + ending.slice(1);
        newName = starting + ending;
    }

    // // and return the version token
    // if (versionToken) {
    //     newName += versionToken[0];
    // }

    // finally using a cache, make sure that there are no duplicates
    var current = 2;
    var baseName = newName;
    while (nodeNamesCache.indexOf(newName) !== -1) {
        newName = baseName + "_" + current.toString();
        current++;
    }
    nodeNamesCache.push(newName);

    return newName;
}

function get_long_named_elements() {
    var id_elements_mapping = get_elements_mapping(null);

    var long_named_elements = {};
    if (id_elements_mapping) {
        for (var elem_id in id_elements_mapping) {
            var elem_vector_type = id_elements_mapping[elem_id]["element_vector_type"];
            var element_name = id_elements_mapping[elem_id]["element_name"];
            var element_id = id_elements_mapping[elem_id]["element_id"];
            var node_name = id_elements_mapping[elem_id]["node_name"];
            var node_path = id_elements_mapping[elem_id]["node_path"];

            // elem_vector_type:
            //   0 means it's an image
            //   1 means it's an obsolete vector (.pnt)
            //   2 means it's a vector (.tvg)
            var check_elem_type_list = [0, 1, 2];

            // fill in long named elements dict
            if (check_elem_type_list.indexOf(elem_vector_type) != -1 || element_name.indexOf("_sgtk") != -1) {
                if (element_name.length > 20 && node_name && node_path) {
                    var new_elem_name = compute_shorter_node_name(element_name);
                    if (node_name) {
                        var new_node_name = compute_shorter_node_name(node_name);
                    } else {
                        var new_node_name = element_name
                    }

                    long_named_elements[node_path] = {
                        "node_path": node_path,
                        "node_name": node_name,
                        "new_node_name": new_node_name,
                        "element_name": element_name,
                        "new_element_name": new_elem_name,
                        "element_id": element_id
                    }
                }
            }
        }
    }

    return long_named_elements;
}

function rename_long_named_elements() {
    var long_named_elements = get_long_named_elements();
    log("long_named_elements:\n" + JSON.stringify(long_named_elements, null, 4));

    var renamed_nodes = {};

    for (var elem in long_named_elements) {
        var element_id = long_named_elements[elem]["element_id"]
        var node_path = long_named_elements[elem]["node_path"]
        var node_name = long_named_elements[elem]["node_name"]
        var new_node_name = long_named_elements[elem]["new_node_name"]
        var element_name = long_named_elements[elem]["element_name"]
        var new_element_name = long_named_elements[elem]["new_element_name"]

        // renameById doesn't update node displayname, so need to rename
        // display name first then renameById

        try {
            // rename node
            node.rename(node_path, new_element_name);
            // rename element
            element.renameById(element_id, new_element_name);
            renamed_nodes[node_name] = new_node_name;
        } catch (error) {
            log("couldn't rename node or element: " + error);
            log("element_name: " + typeof element_name);
            log("typeof element_id: " + typeof element_id);
            log("typeof new_element_name: " + typeof new_element_name);
        }
    }

    log("------------------------------------");
    log("Renamed nodes: ");
    log(JSON.stringify(renamed_nodes, null, 4));

    return renamed_nodes;
}

function main() {
    var renamed_nodes = rename_long_named_elements();
    log("renamed_nodes:\n" + JSON.stringify(renamed_nodes, null, 4));

    // this return_dict will be used in the scripts toolbox app to display a message
    // box with the results
    var return_dict = {
        "successful_execution": false,
        "message": ""
    }
    if (Object.keys(renamed_nodes).length > 0) {
        var message = "Renamed nodes:\n" + JSON.stringify(renamed_nodes, null, 4);
        return_dict["successful_execution"] = true;
        return_dict["message"] = message;
    } else {
        var message = "No long named nodes/elements found";
        return_dict["successful_execution"] = false;
        return_dict["message"] = message;
    }
    return return_dict;
}

main();
