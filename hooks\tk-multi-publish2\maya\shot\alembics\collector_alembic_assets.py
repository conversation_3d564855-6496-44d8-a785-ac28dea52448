# -*- coding: utf-8 -*-
# Standard library:
import os
import json
import pprint

# ___   ___   ___   ___   ___   ___  ___
# Third party:
import sgtk
import pymel.core as pm

# ___   ___   ___   ___   ___   ___  ___
# Project:
from tank_vendor import six

# ====================================================================
pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)
pf = pprint.pformat

HookBaseClass = sgtk.get_hook_baseclass()


class CollectorShotGeometryCache(HookBaseClass):
    @property
    def name(self):
        return "CollectorShotGeometryCache"

    @property
    def settings(self):
        collector_settings = super(CollectorShotGeometryCache, self).settings or {}

        maya_sequence_settings = {
            "Work Template": {
                "type": "template",
                "default": "",
                "description": (
                    "Template path for artist work files."
                    "Should correspond to a template defined in "
                    "templates.yml. If configured, is made available"
                    "to publish plugins via the collected item's "
                    "properties. "
                ),
            },
            "Asset Geometry Cache Export Supported Types": {
                "type": "list",
                "default": [],
                "description": (
                    "Only alembics/USD for this asset types should be "
                    "considered to export as caches."
                ),
            },
        }

        collector_settings.update(maya_sequence_settings)

        return collector_settings

    # ---------------------------------------------------------------------------

    def process_current_session(self, settings, parent_item):
        print("\n" + (">" * 120))
        print("\n{0}.process_current_session".format(self.name))
        super(CollectorShotGeometryCache, self).process_current_session(settings, parent_item)
        if not parent_item.type_spec.endswith(".session"):
            item = next(
                (i for i in parent_item.descendants if i.type_spec.endswith(".session")),
                parent_item,
            )
        else:
            item = parent_item

        self._collect_shot_assets(settings, item)

    # ---------------------------------------------------------------------------

    def _collect_shot_assets(self, settings, parent_item):
        # ------------------------------------------------------------------------------
        # Load value overrides framework
        valueoverrides = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        ) or self.load_framework("mty-framework-valueoverrides")

        if os.environ.get("SG_PROCESSING_SECONDARY_OUTPUTS", False):
            secondary_outputs_on_local = True
        else:
            secondary_outputs_on_local = False
            if valueoverrides:
                value_code = (
                    "mty.engine.maya.multi-publish.process_secondary_outputs_on_local"
                )
                value_secondary_outputs_on_local = valueoverrides.get_value(value_code)
                self.parent.logger.info(
                    "mty.engine.maya.multi-publish.process_secondary_outputs_on_local: {}".format(
                    value_secondary_outputs_on_local
                    )
                )

                dict_secondary_outputs_on_local = json.loads(value_secondary_outputs_on_local)
                secondary_outputs_on_local = dict_secondary_outputs_on_local.get(
                    "maya.session.shot.geometryCache", {}
                ).get(
                    "value", True
                )

        list_of_references = pm.listReferences()
        list_of_paths = [str(_ref.path) for _ref in list_of_references]

        map_of_publishes = sgtk.util.find_publish(
            self.parent.engine.context.sgtk,
            list_of_paths,
            fields=[
                "entity",
                "version_number",
                "entity.Asset.code",
                "entity.Asset.sg_asset_type",
                "published_file_type",
                "name",
                "task.Task.step.Step.code",
            ],
        )

        map_of_filtered_publishes = self._filter_assets_by_type(
            settings, map_of_publishes
        )

        icon_path = os.path.join(
            self.disk_location,
            os.pardir,
            os.pardir,
            os.pardir,
            "icons",
            "geo_publish.png",
        )

        print(icon_path)
        if secondary_outputs_on_local:

            asset_item = parent_item.create_item(
                "maya.session.shot.geometryCache", "Geometry Cache", "Shot Geometry Assets"
            )

            self.parent.engine.execute_hook_expression(
                "{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py",
                "use_farm_or_local_processing",
                item = asset_item
            )

            asset_item.set_icon_from_path(icon_path)

            list_of_filtered_paths = map_of_filtered_publishes.keys()
            list_of_references = self.filter_references_by_paths(
                list_of_references, list_of_filtered_paths
            )
            if list_of_references:
                list_of_references = list(list_of_references)

                asset_item.properties["map_of_publishes_by_path"] = map_of_filtered_publishes
                asset_item.properties["list_of_references"] = list_of_references

        else:
            second_outputs_to_farm = parent_item.properties.get(
                "secondary_outputs_to_farm", {}
            )
            dict_to_primary ={
                "maya.session.shot.geometryCache":{
                    "maps_of_publishes_by_path": map_of_filtered_publishes,
                    "list_of_references": list_of_references,
                }
            }
            second_outputs_to_farm.update(dict_to_primary)
            parent_item.properties["secondary_outputs_to_farm"] = second_outputs_to_farm

    # ---------------------------------------------------------------------------

    def filter_references_by_paths(self, list_of_references, list_of_paths):
        result = filter(lambda ref: str(ref.path) in list_of_paths, list_of_references)
        if result:
            result = list(result)

        return result

    def _filter_assets_by_type(self, settings, map_of_publishes):
        result = {}
        list_of_valid_asset_types = settings[
            "Asset Geometry Cache Export Supported Types"
        ].value

        for _key in map_of_publishes:
            if (
                map_of_publishes[_key]["entity.Asset.sg_asset_type"]
                in list_of_valid_asset_types
            ):
                result[_key] = map_of_publishes[_key]

        return result
