import os
import time
import json
import shutil
import pprint
import traceback

import sgtk

HookBaseClass = sgtk.get_hook_baseclass()

class PublishCopyPlugin(HookBaseClass):
    '''
    Plugin for customizing standalone publisher.
    This hook relies on functionality found in the base file publisher hook in
    the publish2 app and should inherit from it in the configuration. The hook
    setting for this plugin should look something like this::
        hook: '{self}/publish_file.py:{config}/tk-multi-publish2/standalone/publish_copy.py'

    It allows to skip defined extensions for files that will be handled by other plugins.
    This is configured by adding th following list in settings:
        skip_extensions: a list of extensions, ex: ['mov']

    It allows to define a limited list of files to be published in certain context.
    Context mapping is defined in override 'mty.publisher.standalone.context_mapping'
    with a dictionary following this structure:
        entity['type']:
            task['name']:
                extension:
                    file_type: 'str'
                    template: 'str'
                    is_primary: bool (main file is True, all other files is False)
    For example(yml):
        context_mapping:
            Episode:
                Storyboard:
                    sbpz:
                        file_type: 'Storyboard Pro Packed Project'
                        template: 'episode_storyboard_publish'
                        is_primary: true
                    mov:
                        file_type: 'Media Review'
                        template: 'episode_storyboard_video_review_publish'
                        is_primary: false
    '''

    @property
    def settings(self):
        # inherit the settings from the base publish plugin
        plugin_settings = super(PublishCopyPlugin,
                                self).settings or {}

        publish_copy_settings = {
            'skip_extensions': {
                'type': 'list',
                'default': [],
                'description': 'List of files processed in other publish plugin',
            },
        }

        # update the base settings
        plugin_settings.update(publish_copy_settings)

        return plugin_settings

    def accept(self, settings, item):
        self.parent.engine.logger.info('{} Publish Copy'.format('-' * 20))

        path = item.get_property('path')
        if path is None:
            raise AttributeError('Publish item has no property \'path\'')

        self.parent.engine.logger.info('file: {}'.format(os.path.basename(path)))

        skip_extensions = settings['skip_extensions'].value
        self.parent.engine.logger.info('skip_extensions: {}'.format(skip_extensions))
        # If skip_extensions exists
        if len(skip_extensions) > 0:
            _, item_ext = os.path.splitext(path)
            item_ext = item_ext.replace('.', '')
            self.parent.engine.logger.info('ext: {}'.format(item_ext))
            if item_ext.lower() in skip_extensions:
                # log the rejected file
                self.parent.engine.logger.info(
                    "Rejected: publish_copy plugin did not accept {}".format(os.path.basename(path))
                )
                return {'accepted': False}
        else:
            self.parent.engine.logger.info('Not skipping extensions')

        # log the accepted file
        self.parent.engine.logger.info(
                'Accepted: publish_copy plugin accepted {}'.format(os.path.basename(path))
            )
        # return the accepted info
        return {'accepted': True}


    def validate(self, settings, item):
        '''
        Validates it has entity and task assigned.
        '''
        # super(PublishCopyPlugin, self).validate(settings, item)

        self.parent.engine.logger.info('///*** Validating Standalone Publish Custom ***///')
        engine = sgtk.platform.current_engine()
        app = engine.apps['tk-multi-publish2']
        publisher = self.parent

        # Get context_mapping from overrides
        valueoverrides = self.parent.engine.custom_frameworks.get(
            'mty-framework-valueoverrides'
        ) or self.load_framework('mty-framework-valueoverrides')
        override_code = 'mty.publisher.standalone.context_mapping'
        context_mapping = json.loads(valueoverrides.get_value(override_code))

        if context_mapping:
            self.parent.engine.logger.info(
                ' Got context_mapping from overrides:\n{}'.format(
                    pprint.pformat(context_mapping)
                )
            )
        else:
            context_mapping = settings.get('context_mapping').value
            self.parent.engine.logger.info(
                ' Got context_mapping from settings:\n{}'.format(
                    pprint.pformat(context_mapping)
                )
            )

        if context_mapping == None or len(context_mapping) == 0:
            error_msg = ('Context_mapping is empty in shotgrid, validation cannot continue.')
            self.logger.error('Error: ' + error_msg)
            raise Exception(error_msg)

        # ---- determine the information required to validate
        path = item.properties.get('path')
        item_entity = item.context.entity
        item_task = item.context.task
        item_extension = os.path.splitext(path)[-1].replace('.', '')
        self.item_info = {}

        # Validating entity link exists
        if item_entity == None:
            error_msg = ('File does not have entity link assigned.')
            self.parent.engine.logger.info('Error: ' + error_msg)
            raise Exception(error_msg)

        # Validating task exists
        if item_task == None:
            error_msg = ('File does not have task assigned.')
            self.parent.engine.logger.info('Error: ' + error_msg)
            raise Exception(error_msg)

        # Validating they have entity and task assigned
        # save valid info from dictionary
        valid = False
        for entity, task in context_mapping.items():
            if item_entity['type'] == entity:
                self.parent.engine.logger.info('Entity valid: ' + entity)
                for t, ext in task.items():
                    if item_task['name'] == t:
                        self.parent.engine.logger.info('Task valid: ' + t)
                        for e, values in ext.items():
                            if e == item_extension:
                                self.parent.engine.logger.info('Extension valid: ' + e)
                                valid = True
                                self.item_info = values
                                self.parent.engine.logger.info('item_info:' + str(self.item_info))
                                item.properties['template'] = values['template']
                                item.properties['file_type'] = values['file_type']

        if not valid:
            error_msg = ('Entity, task or extension not valid to publish for item: ' + item.properties['path'])
            self.parent.engine.logger.info('Error: ' + error_msg)
            raise Exception(error_msg)

        # Validating is primary
        self._get_primary_incidencies_item_tree(item, context_mapping)

        self.parent.engine.logger.debug('item project: ' + str(item.context.project)
            + '\nitem entity: ' + str(item.context.entity)
            + '\nitem step: ' + str(item.context.step)
            + '\nitem task: ' + str(item.context.task)
            + '\nitem_info: ' + str(self.item_info)
        )

        # add info that stays the same an all items
        self.item_info['context'] = publisher.sgtk.context_from_entity_dictionary(item.context.task)
        self.item_info['publish_version'] = self._get_publish_version(item)

        # create timestamp to validate items UI order
        current_ts = time.time()
        item.properties['last_validate'] = current_ts
        self.parent.engine.logger.info('timestamp: {}'.format(current_ts))
        primary_item = self.get_primary_item_tree(item)
        self.parent.engine.logger.info('Primary item is: ' + primary_item.name)
        self.parent.engine.logger.info('Current item is: ' + item.name)

        root_item = self.get_root_item(item)

        if primary_item.get_property('last_validate'):

            # create internal counter to only proces las item por validating items order
            if root_item.get_property('iterate_item_num'):
                root_item.properties['iterate_item_num'] = root_item.properties['iterate_item_num'] + 1
            else:
                root_item.properties['iterate_item_num'] = 1

            root_item_len = 0
            for child in root_item.children:
                root_item_len += 1
            self.parent.engine.logger.info('root item len: {}'.format(root_item_len))
            self.parent.engine.logger.info('stored iterating root item: {}'.format(root_item.properties['iterate_item_num']))

            if root_item_len == root_item.properties['iterate_item_num']:
                self.parent.engine.logger.info('**validating order')
                # reset counter
                root_item.properties['iterate_item_num'] = 0

                self.parent.engine.logger.info('timestamps:')

                self.parent.engine.logger.info('\t primary: {}'.format(primary_item.properties['last_validate']))
                if item.get_property('last_validate'):
                    self.parent.engine.logger.info('\t current: {}'.format(current_ts))

                # check primary validate is smaller than other items on tree

                self.parent.engine.logger.info('CHILDS:')
                not_ordered = False
                for child in root_item.children:
                    self.parent.engine.logger.info('\t {}: {}'.format(child.name, child.properties['last_validate']))
                    if child.properties['last_validate'] < primary_item.properties['last_validate']:
                        not_ordered = True

                self.parent.engine.logger.info('not ordered = {}'.format(not_ordered))
                if not_ordered:
                    error_msg = 'Please reorder items with {} appearing first on list.'.format(primary_item.name)
                    self.parent.engine.logger.info(error_msg)
                    raise Exception(error_msg)
        else:
            root_item.properties['iterate_item_num'] = 1
            error_msg = 'Please reorder items with {} appearing first on list.'.format(primary_item.name)
            self.parent.engine.logger.info(error_msg)
            raise Exception(error_msg)


        # We allow the information to be pre-populated by the collector or a
        # base class plugin. They may have more information than is available
        # here such as custom type or template settings.
        publish_path = self.get_publish_path(settings, item)
        publish_name = 'master'

        # ---- check for conflicting publishes of this path with a status

        # Note the name, context, and path *must* match the values supplied to
        # register_publish in the publish phase in order for this to return an
        # accurate list of previous publishes of this file.
        publishes = publisher.util.get_conflicting_publishes(
            item.context,
            publish_path,
            publish_name,
            filters=['sg_status_list', 'is_not', None],
        )

        if publishes:

            self.logger.debug(
                'Conflicting publishes: %s' % (pprint.pformat(publishes),)
            )

            publish_template = self.get_publish_template(settings, item)

            if 'work_template' in item.properties or publish_template:

                # templates are in play and there is already a publish in SG
                # for this file path. We will raise here to prevent this from
                # happening.
                error_msg = (
                    'Can not validate file path. There is already a publish in '
                    'ShotGrid that matches this path. Please uncheck this '
                    'plugin or save the file to a different path.'
                )
                self.logger.error(error_msg)
                raise Exception(error_msg)

            else:
                conflict_info = (
                    'If you continue, these conflicting publishes will no '
                    'longer be available to other users via the loader:<br>'
                    '<pre>%s</pre>' % (pprint.pformat(publishes),)
                )
                self.logger.warn(
                    'Found %s conflicting publishes in ShotGrid' % (len(publishes),),
                    extra={
                        'action_show_more_info': {
                            'label': 'Show Conflicts',
                            'tooltip': 'Show conflicting publishes in ShotGrid',
                            'text': conflict_info,
                        }
                    },
                )

        self.logger.info('A Publish will be created in ShotGrid and linked to:')
        self.logger.info('  %s' % (path,))

        return True

    def publish(self, settings, item):
        '''
        Executes the publish logic for the given item and settings.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        '''
        self.parent.engine.logger.info(' ///*** Publishing...publish_copy ///*** ')
        publisher = self.parent


        root_item = self.get_root_item(item)
        self.parent.logger.debug('item.tasks: {}'.format(item.tasks))
        self.parent.logger.debug('root_item: {}'.format(root_item))
        self.parent.logger.debug('root_item.tasks: {}'.format(root_item.tasks))

        # ---- determine the information required to publish

        publish_type = item.properties['file_type']
        self.item_info['publish_name'] = 'master'
        publish_path = self.get_publish_path(settings, item)
        # publish_fields = self.get_publish_fields(settings, item)
        # # catch-all for any extra kwargs that should be passed to register_publish.
        # publish_kwargs = self.get_publish_kwargs(settings, item)

        # Check if item is primary file and get dependencies if not
        self.parent.engine.logger.info('checking primary and dependencies for publish')
        publish_dependencies_ids = []
        primary_item = self.get_primary_item_tree(item)
        self.parent.engine.logger.info('Primary item is: ' + primary_item.name)
        self.parent.engine.logger.info('Current item is: ' + item.name)

        if primary_item.name != item.name:
            self.parent.engine.logger.info('no im not primary :(')
            self.parent.engine.logger.info('sg_publish_data: ' + str(primary_item.properties.sg_publish_data))
            publish_dependencies_ids.append(primary_item.properties.sg_publish_data['id'])

        # handle copying of work to publish
        self._copy_file_to_publish(publish_path, item)

        # arguments for publish registration
        self.parent.engine.logger.info('Registering publish...')
        publish_data = {
            'tk': publisher.sgtk,
            'context': self.item_info['context'],
            'comment': item.description,
            'path': publish_path,
            'name': self.item_info['publish_name'],
            'version_number': self.item_info['publish_version'],
            'thumbnail_path': item.get_thumbnail_as_path(),
            'published_file_type': publish_type,
            'dependency_ids': publish_dependencies_ids,
        }

        self.parent.engine.logger.info('publish data: ' + str(publish_data))

        # add extra kwargs
        # publish_data.update(publish_kwargs)

        # log the publish data for debugging
        self.parent.engine.logger.info(
            'Populated Publish data...',
            extra={
                'action_show_more_info': {
                    'label': 'Publish Data',
                    'tooltip': 'Show the complete Publish data dictionary',
                    'text': '<pre>%s</pre>' % (pprint.pformat(publish_data),),
                }
            },
        )

        # create the publish and stash it in the item properties for other
        # plugins to use.
        item.properties.sg_publish_data = sgtk.util.register_publish(**publish_data)
        self.parent.engine.logger.info('Publish registered!')
        self.parent.engine.logger.info(
            'ShotGrid Publish data...',
            extra={
                'action_show_more_info': {
                    'label': 'ShotGrid Publish Data',
                    'tooltip': 'Show the complete ShotGrid Publish Entity dictionary',
                    'text': '<pre>%s</pre>'
                    % (pprint.pformat(item.properties.sg_publish_data),),
                }
            },
        )

    def _get_primary_incidencies_item_tree(self, item, context_mapping):
        '''
        Validate first item is primary
        Validate there is only one is primary file
        '''
        item_entity = item.context.entity['type']
        item_task = item.context.task['name']

        root_item = self.get_root_item(item)
        is_primary_incidencies = []
        self.parent.logger.debug('root_item.children: {}'.format('-' * 80))

        # Validate first one has to be primary
        for child in root_item.children:
            self.parent.logger.debug('First item: ' + child.name)
            child_path = child.properties['path']
            child_extension = os.path.splitext(child_path)[-1].replace(".", "")

            # get value of key 'is_primary'
            key_valid = None
            try:
                key_valid = context_mapping[item_entity][item_task][child_extension]['is_primary']
            except:
                pass

            # check if it is primary
            if key_valid is not None and key_valid:
                self.parent.logger.debug('Primary file is: ' + child.name)
            else:
                primary_item = self.get_primary_item_tree(item)
                error_msg = ''
                if primary_item == None:
                    error_msg = 'You need a primary file to publish.'
                else:
                    error_msg = ('Please load {} file first on UI'.format(primary_item.name))
                self.parent.engine.logger.info('Error: ' + error_msg)
                raise Exception(error_msg)
            break

        # print debug info
        # iterate tree to find is_primary incidencies
        for child in root_item.children:
            self.parent.logger.debug('-' * 40)
            self.parent.logger.debug('child.name: {}'.format(child.name))
            child_path = child.properties['path']
            self.parent.logger.debug('\tchild path: ' + child_path)
            child_extension = os.path.splitext(child_path)[-1].replace('.', '')
            self.parent.logger.debug('\tchild extension: ' + child_extension)

            # if it containts a primary extension, add to incidencies list
            try:
                if context_mapping[item_entity][item_task][child_extension]['is_primary']:
                    is_primary_incidencies.append(child_path)
                    self.parent.logger.debug('\tis primary')
            except KeyError:
                self.parent.logger.debug('\tnot primary')

        # validate count of primary files
        self.parent.logger.info('*is_primary count: ' + str(len(is_primary_incidencies)))
        if len(is_primary_incidencies) > 1:
            incidencies_names = [os.path.basename(x) for x in is_primary_incidencies]
            error_msg = ('You can only publish one primary file at the time. '
                        + '\n Please reload files with only one of the folowing: \n{}'.format(pprint.pformat(incidencies_names)))
            self.parent.engine.logger.info('Error: ' + error_msg)
            raise Exception(error_msg)

    def get_primary_item_tree(self, item):

        root_item = self.get_root_item(item)
        item_entity = item.context.entity['type']
        item_task = item.context.task['name']

        engine = sgtk.platform.current_engine()
        app = engine.apps['tk-multi-publish2']

         # Get override or app settings for context mapping
        context_mapping = app.get_setting('context_mapping')
        valueoverrides = self.parent.engine.custom_frameworks.get(
            'mty-framework-valueoverrides'
        ) or self.load_framework('mty-framework-valueoverrides')
        override_code = 'mty.publisher.standalone.context_mapping'
        data = valueoverrides.get_value(override_code)

        if data is not None:
            context_mapping = json.loads(data)

        self.parent.logger.debug("root_item.children: {}".format("-" * 80))

        for child in root_item.children:

            self.parent.logger.debug("-" * 40)
            self.parent.logger.debug("child.name: {}".format(child.name))
            child_path = child.properties['path']
            self.parent.logger.debug('\tchild path: ' + child_path)
            child_extension = os.path.splitext(child_path)[-1].replace(".", "")
            self.parent.logger.debug('\tchild extension: ' + child_extension)

            try:
                if context_mapping[item_entity][item_task][child_extension]['is_primary']:
                    self.parent.logger.debug('\tis primary')
                    return child
            except KeyError:
                self.parent.logger.debug('\tnot primary')

    def get_root_item(self, item):
        '''
        Param: item
        Returns: item tree's root item
        '''

        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root

    def _get_publish_version(self, item):

        self.parent.engine.logger.debug('Getting publish version...')

        engine = sgtk.platform.current_engine()

        filters = [
            ['project.Project.name', 'is', item.context.project['name']],
            ['entity', 'name_is', item.context.entity['name']],
            ['task.Task.content', 'is', item.context.task['name']],
        ]

        fields = ['id', 'code', 'version_number']

        order = [{'field_name': 'version_number', 'direction': 'desc'}]
        result = engine.shotgun.find('PublishedFile', filters, fields, order=order)

        if len(result)==0:
            version = 1
        else:
            version = (result[0]['version_number']) + 1

        return version

    def get_publish_path(self, settings, item):

        self.parent.engine.logger.info('Getting path... ')

        result = None
        engine = sgtk.platform.current_engine()

        self.parent.engine.logger.info('Current item: ' + item.name)
        template = engine.get_template_by_name(item.properties['template'])
        # template = None
        # if item.get_property('template'):
        #     template = engine.get_template_by_name(item.properties['template'])
        # else:
        #     self.logger.debug(
        #         "Could not validate a publish template. Publishing in place."
        #     )
        #     return item.properties['path']

        # ensure folders exist
        engine = sgtk.platform.current_engine()
        tk = engine.sgtk
        entity_type = item.context.entity['type']
        entity_id = item.context.entity['id']
        tk.create_filesystem_structure(entity_type, entity_id)

        dir_path = self.item_info['context'].as_template_fields(template)
        dir_path['name'] = 'master'
        dir_path['version'] = self.item_info['publish_version']

        self.parent.engine.logger.info('dir path: ' + str(dir_path))

        path = template.apply_fields(dir_path)
        self.parent.engine.logger.info('publish_path path: '+path)

        validate = template.validate(path)

        if validate == True:
            result = path
        return result

    def _copy_file_to_publish(self, destination_path, item):
        '''
        Param:
            Publish path
            item
        '''
        # File path: C:\Users\<USER>\Desktop\images\backgroundLayer_beauty_1001.sbpz
        # File path publish area: P:\sandbox\WorkArea\shots\e106\episode\Story\Storyboard\storyboard\e106_str_Storyboard_master_v001.sbpz
        # Publish path: P:\sandbox\PublishArea\shots\e106\episode\Story\Storyboard\storyboard\e106_str_Storyboard_master_v001.sbpz
        self.parent.engine.logger.info('Copying file...')
        path_file = item.properties['path']

        self.parent.engine.logger.info('File path: ' + path_file)
        self.parent.engine.logger.info('Publish path: ' + destination_path)

        try:
            # Create the destination directory if it doesn't exist
            if os.path.exists(os.path.dirname(destination_path)) != True:
                os.makedirs(os.path.dirname(destination_path))
            shutil.copy2(path_file, destination_path)
        except Exception:
            self.parent.engine.logger.info('Could not copy file to publish area.')
            self.parent.engine.logger.info(traceback.format_exc())
