import logging
import base64
import os
import sgtk


# Hash message log
class MessageHash(logging.Formatter):
    def format(self, record):
        """
        Encodes a logging message to base64.

        Args:
            record (logging.LogRecord): The record to format.

        Returns:
            str: The formatted string.
        """

        original_message = record.getMessage()
        encoded_message = base64.b64encode(original_message.encode("utf-8")).decode(
            "utf-8"
        )
        return f"{self.formatTime(record)} - {record.levelname} - {encoded_message}"


# De-hash message log
def dehash_text(encoded_text):
    """
    Decodes a Base64 encoded text string back to its original form.

    Args:
        encoded_text (str): The Base64 encoded text string to decode.

    Returns:
        str: The decoded string.
    """

    encoded_bytes = encoded_text.encode("utf-8")
    decoded_bytes = base64.b64decode(encoded_bytes)
    decoded_text = decoded_bytes.decode("utf-8")
    return decoded_text


# De-hash log file and save to a text file
def dehash_log_and_save(log_file_path, output_file_path=None):
    """
    Dehashes a log file and saves the decoded content to a text file.

    Reads a log file, decodes the Base64 encoded log messages to their original form,
    and writes the decoded content to a text file.

    Args:
        log_file_path (str): The path to the log file to decode.

        output_file_path (str, optional): The path to the output text file to save the
        decoded content to. Defaults to None.

    Returns:
        None
    """
    if not output_file_path:
        output_file_path = os.path.splitext(log_file_path)[0] + "_decoded.txt"

    try:
        with open(log_file_path, "r") as infile, open(output_file_path, "w") as outfile:
            for line in infile:
                parts = line.strip().split(" - ")
                if len(parts) == 3:
                    timestamp, level, encoded_message = parts
                    try:
                        decoded_message = dehash_text(encoded_message)
                        outfile.write(f"{timestamp} - {level} - {decoded_message}\n")
                    except Exception as e:
                        outfile.write(f"Error decoding line: {line.strip()} - {e}\n")
                else:
                    outfile.write(f"Skipping malformed line: {line.strip()}\n")
        print(
            f"Successfully decoded '{log_file_path}' and saved to '{output_file_path}'"
        )
    except FileNotFoundError:
        print(f"Error: Log file '{log_file_path}' not found.")
    except Exception as e:
        print(f"An error occurred during decoding: {e}")


def create_log_file(log_name):
    """
    Creates a logger that writes to a file in the Shotgun log directory.

    The logger is configured to use the MessageHash formatter to encode log messages to
    base64.

    Args:
        log_file_path (str): The path to the log file, relative to the Shotgun log
        directory.

    Returns:
        logging.Logger: The configured logger.
    """
    # Set up logger
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)

    # Initializes the log manager if it hasn't been initialized
    sgtk.LogManager()

    # Get the log folder
    if ".log" not in log_name:
        log_name += ".log"

    log_folder = sgtk.LogManager().log_folder
    logger_path = os.path.join(log_folder, log_name)

    # Logggin handlers and formatter
    file_handler = logging.FileHandler(logger_path, mode="a")
    formatter = MessageHash("%(asctime)s - %(levelname)s - %(message)s")
    file_handler.setFormatter(formatter)

    logger.addHandler(file_handler)

    return logger
