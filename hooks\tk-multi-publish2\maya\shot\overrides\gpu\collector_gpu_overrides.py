# -*- coding: utf-8 -*-
# Standard library:
import pprint
import os
from pprint import pformat
# ___   ___   ___   ___   ___   ___  ___
# Third party:
import sgtk
import pymel.core as pm

# ___   ___   ___   ___   ___   ___  ___
# Project:
pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)
HookBaseClass = sgtk.get_hook_baseclass()


# ====================================================================


class EnvironmentsCollector(HookBaseClass):

    @property
    def settings(self):
        collector_settings = super(
            EnvironmentsCollector, self).settings or {}

        maya_settings = {
            "Work Template": {
                "type": "template",
                "default": None,
                "description":
                    "Template path for artist work files. Should "
                    "correspond to a template defined in "
                    "templates.yml. If configured, is made available"
                    "to publish plugins via the collected item's "
                    "properties. "
            },
            "Primary Publish Template": {
                "type": "template",
                "default": None,
                "description": "",
            },
            "Environment Asset Type": {
                "type": "string",
                "default": None,
                "description": "",
            },
            "Environment Publish Types": {
                "type": "list",
                "default": None,
                "description": "",
            },
            'Environment Overrides Supported Types': {
                'type': 'list',
                'default': None,
                'description': (
                    'Supported asset types for overrides'
                )
            }
        }

        collector_settings.update(maya_settings)

        return collector_settings

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def process_current_session(self, settings, parent_item):
        inerent_parent = super(EnvironmentsCollector, self)
        items = inerent_parent.process_current_session(settings,
                                                       parent_item) or []

        self.parent.log_debug("Items:\n%s" % pformat(items))

        env_items = self._collect_env_overrides(settings, parent_item)

        items.extend(env_items)

        return items

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def _collect_env_overrides(self, settings, parent_item):
        list_of_gpuCaches = pm.ls(type='gpuCache')
        list_of_items = []

        list_of_gpuCache_paths = [
            str(x.cacheFileName.get()) for x in list_of_gpuCaches
        ]

        map_of_publishes = self.query_mighty_for_publishes_with_paths(
            list_of_gpuCache_paths
        )

        list_of_supported_publish_types = \
            settings.get('Environment Publish Types').value

        list_of_supported_asset_types = \
            settings.get('Environment Overrides Supported Types').value

        #  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

        for element in list_of_gpuCaches:
            element_path = str(element.cacheFileName.get())

            #  -   -   -   -   -   -   -   -   -   -   -   -   -   -   -   -   -

            if not self.is_a_published_path(element, map_of_publishes):
                continue

            publish_type_field = 'published_file_type.PublishedFileType.code'
            element_publish_type = (
                map_of_publishes
                [element_path]
                [publish_type_field]
            )

            if not self.its_element_publish_type_supported(
                    element,
                    element_publish_type,
                    list_of_supported_publish_types
            ):
                continue

            asset_type_field = 'entity.Asset.sg_asset_type'
            element_asset_type = (
                map_of_publishes
                [element_path]
                [asset_type_field]
            )

            if not self.its_element_asset_type_supported(
                    element,
                    element_asset_type,
                    list_of_supported_asset_types
            ):
                continue

            #  -   -   -   -   -   -   -   -   -   -   -   -   -   -   -   -   -

            item = parent_item.create_item(
                'maya.session.gpu.environments',
                'GPU Environment Overrides',
                'GPU Environment Overrides Publish'
            )

            self.parent.engine.execute_hook_expression(
                "{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py",
                "use_farm_or_local_processing",
                item = item
            )

            path_to_icon = os.path.join(
                self.disk_location,
                'icon_gpu_overrides.png'
            )


            item.set_icon_from_path(path_to_icon)

            item.properties['gpu_node'] = element
            item.properties['publish_entity'] = map_of_publishes[element_path]
            list_of_items.append(item)

        return list_of_items

    # ---------------------------------------------------------------------------
    def its_element_asset_type_supported(
            self,
            element,
            asset_type,
            list_of_supported_asset_types
    ):
        result = True

        if not asset_type in list_of_supported_asset_types:
            self.parent.log_debug(
                'Skipping {0} because asset type {1} its not supported.'
                    .format(element.name(), asset_type)
            )
            result = False

        return result

    def its_element_publish_type_supported(
            self,
            element,
            publish_type,
            list_of_supported_publish_types
    ):
        result = True

        if not publish_type in list_of_supported_publish_types:
            self.parent.log_debug(
                'Skipping {0} because publish type {1} its not supported.'.format(
                    element.name(), publish_type
                )
            )
            result = False

        return result

    # ---------------------------------------------------------------------------

    def is_a_published_path(self, gpuCache_node, list_of_publishes):
        result = True
        _path = str(gpuCache_node.cacheFileName.get())
        if not _path in list_of_publishes.keys():
            self.parent.log_debug(
                'Skipping {0} gpuCache because its not a published file.'
                    .format(gpuCache_node.name())
            )
            result = False
        return result

    # ---------------------------------------------------------------------------

    def query_mighty_for_publishes_with_paths(self, list_of_paths):
        __list_of_publishes = sgtk.util.find_publish(
            self.parent.sgtk,
            list_of_paths,
            fields=[
                'entity.Asset.sg_asset_type',
                'entity.Asset.code',
                'published_file_type',
                'published_file_type.PublishedFileType.code'
            ]
        )
        return __list_of_publishes
