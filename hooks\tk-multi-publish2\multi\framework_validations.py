# -*- coding: utf-8 -*-
# Standard library:
import pprint
#   .   .   .   .   .   .   .   .   .   .   .
# Third party:
import sgtk
#   .   .   .   .   .   .   .   .   .   .   .
# Project:
# ================================================================
pp = pprint.PrettyPrinter(indent=3).pprint

HookBaseClass = sgtk.get_hook_baseclass()


class Framework(HookBaseClass):
    def __init__(self, parent):
        super(Framework, self).__init__(parent)
        self.parent.engine.logger.info("start validation from framework")
        self.framework = self.load_framework("mty-framework-validations")
        self.framework.apply_config(self.parent.engine, self.parent.context, self.parent, self.logger)

    @property
    def settings(self):
        # inherit the settings from the base publish plugin
        plugin_settings = super(
            Framework, self).settings or {}
        return plugin_settings

    def validate(self, settings, item):
        previous = super(Framework, self).validate(settings, item) or True

        item = self.framework.validate(item)

        if not previous:
            return False

        return True
