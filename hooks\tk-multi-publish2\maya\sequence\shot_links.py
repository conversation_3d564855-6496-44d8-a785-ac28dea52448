# -*- coding: utf-8 -*-
# Standard library:
#   .   .   .   .   .   .   .   .   .   .   .
# Third party:
import sgtk
import pymel.core as pm

#   .   .   .   .   .   .   .   .   .   .   .
# Project:
# ================================================================

HookBaseClass = sgtk.get_hook_baseclass()


class MayaShotDataValidationPlugin(HookBaseClass):

    def __init__(self, parent):
        super(
            MayaShotDataValidationPlugin,
            self
        ).__init__(parent)
        self.core_maya_tools = \
            self.load_framework("mty-framework-coremayatools")
        self.composer = self.core_maya_tools.checks.composer

    @property
    def description(self):
        return """
            <p>
            This plugin handles the validation of the
            shot node data neccesary for publishing
            a sequence.
            </p>
        """

    # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

    @property
    def item_filters(self):
        return ["maya.session.shot"]

    # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

    def accept(self, settings, item):

        shot_nodes = item.properties["nodes"]

        if not shot_nodes:
            self.logger.debug(
                "The shot_node, shot_name or the assets_string " +
                "was not set for the collected shot items."
            )
            return {
                "accepted": False,
                "enabled": False,
                "visible": True,
                "checked": False
            }
        else:
            return {
                "accepted": True,
                "enabled": True,
                "visible": True,
                "checked": True
            }

    def validate(self, settings, item):
        return True

    def publish(self, settings, item):
        return True

    def finalize(self, settings, item):
        pass

    # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

    def list_of_assets_in_session(self, gpuCache=True):
        result = []
        self.list_of_references = pm.listReferences()
        self.list_of_gpu_caches = pm.ls(type="gpuCache")

        for reference in self.list_of_references:
            result.append(reference.refNode)

        if gpuCache:
            for gpu_cache in self.list_of_gpu_caches:
                result.append(gpu_cache.getParent())

        return result
