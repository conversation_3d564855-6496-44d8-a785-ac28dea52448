# Copyright (c) 2015 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

"""
Hook that loads defines all the available actions, broken down by publish type.
"""
import os
import re
import json
import glob
import sgtk
import pprint
import traceback

from tank_vendor import six
from tank.platform.qt import QtCore, QtGui

QLabel = QtGui.QLabel
QDialog = QtGui.QDialog
QPushButton = QtGui.QPushButton
QVBoxLayout = QtGui.QVBoxLayout
QApplication = QtGui.QApplication

HookBaseClass = sgtk.get_hook_baseclass()

pp = pprint.pprint
pf = pprint.pformat

class AfterEffectsActions(HookBaseClass):

    ##############################################################################################################
    # public interface - to be overridden by deriving classes

    def generate_actions(self, sg_publish_data, actions, ui_area):
        """
        Returns a list of action instances for a particular publish.
        This method is called each time a user clicks a publish somewhere in the UI.
        The data returned from this hook will be used to populate the actions menu for a publish.

        The mapping between Publish types and actions are kept in a different place
        (in the configuration) so at the point when this hook is called, the loader app
        has already established *which* actions are appropriate for this object.

        The hook should return at least one action for each item passed in via the
        actions parameter.

        This method needs to return detailed data for those actions, in the form of a list
        of dictionaries, each with name, params, caption and description keys.

        Because you are operating on a particular publish, you may tailor the output
        (caption, tooltip etc) to contain custom information suitable for this publish.

        The ui_area parameter is a string and indicates where the publish is to be shown.
        - If it will be shown in the main browsing area, "main" is passed.
        - If it will be shown in the details area, "details" is passed.
        - If it will be shown in the history area, "history" is passed.

        Please note that it is perfectly possible to create more than one action "instance" for
        an action! You can for example do scene introspection - if the action passed in
        is "character_attachment" you may for example scan the scene, figure out all the nodes
        where this object can be attached and return a list of action instances:
        "attach to left hand", "attach to right hand" etc. In this case, when more than
        one object is returned for an action, use the params key to pass additional
        data into the run_action hook.

        :param sg_publish_data: Shotgun data dictionary with all the standard publish fields.
        :param actions: List of action strings which have been defined in the app configuration.
        :param ui_area: String denoting the UI Area (see above).
        :returns List of dictionaries, each with keys name, params, caption and description
        """
        app = self.parent
        # app.logger.info(
        #     (
        #         "Generate actions called for UI element {}. "
        #         "\nParameters:\n{}.\nPublish Data:\n{}"
        #     ).format(ui_area, pf(actions), pf(sg_publish_data))
        # )
        app.logger.info("Generate actions called for UI element: '{}'.".format(ui_area))

        action_instances = []

        """
        is_comp_selected = False
        app.logger.info("initial is_comp_selected: {}".format(is_comp_selected))
        try:
            active_item = self.parent.engine.selected_item
            if active_item:
                app.logger.info("active_item: {}".format(active_item))
                is_comp_selected = self.parent.engine.is_item_of_type(
                    active_item, self.parent.engine.AdobeItemTypes.COMP_ITEM
                )
                app.logger.info("is_comp_selected: {}".format(is_comp_selected))
        except:
            app.logger.info("couldn't get active_item")
            error = traceback.format_exc()
            app.logger.error("error while trying to get active item: \n{}".format(error))

        app.logger.info("final is_comp_selected: {}".format(is_comp_selected))

        if "add_to_comp" in actions and is_comp_selected:
            app.logger.info("found 'add_to_comp' action")
            action_instances.append(
                {
                    "name": "add_to_comp",
                    "params": None,
                    "caption": "Add to current composition",
                    "description": (
                        "Adds the current item to the currently selected "
                        "comp as a layer.",
                    )
                }
            )
            app.logger.info("added action 'add_to_comp'")
        """

        if "add_to_project" in actions:
            action_instances.append(
                {
                    "name": "add_to_project",
                    "params": None,
                    "caption": "Add to project",
                    "description": "Adds the current item to the active project.",
                }
            )

        if "download_publish" in actions:
            action_instances.append(
                {
                    "name": "download_publish",
                    "params": None,
                    "caption": "Download published file",
                    "description": (
                        "This will verify if the file exists locally on disk, "
                        "if not, it will be downloaded."
                    )
                }
            )

        if "import_Harmony_camera" in actions:
            action_instances.append(
                {
                    "name": "import_Harmony_camera",
                    "params": None,
                    "caption": "Import Harmony camera",
                    "description": (
                        "Creates a new camera from the Harmony published jsonx file."
                    )
                }
            )

        if "import_Harmony_tracker_peg" in actions:
            action_instances.append(
                {
                    "name": "import_Harmony_tracker_peg",
                    "params": None,
                    "caption": "Import Harmony tracker peg",
                    "description": (
                        "Creates a new null from the Harmony published jsonx file."
                    )
                }
            )
        if "Import_psd_output_layers_ordered" in actions:
            action_instances.append(
                {
                    "name": "Import_psd_output_layers_ordered",
                    "params": None,
                    "caption": "Import Ordered Output layer Groups from PSD",
                    "description": (
                        "Imports layers from the psd published jsonx file."
                    )
                }
            )
        return action_instances

    def execute_multiple_actions(self, actions):
        """
        Executes the specified action on a list of items.

        The default implementation dispatches each item from ``actions`` to
        the ``execute_action`` method.

        The ``actions`` is a list of dictionaries holding all the actions to execute.
        Each entry will have the following values:

            name: Name of the action to execute
            sg_publish_data: Publish information coming from Shotgun
            params: Parameters passed down from the generate_actions hook.

        .. note::
            This is the default entry point for the hook. It reuses the ``execute_action``
            method for backward compatibility with hooks written for the previous
            version of the loader.

        .. note::
            The hook will stop applying the actions on the selection if an error
            is raised midway through.

        :param list actions: Action dictionaries.
        """
        for single_action in actions:
            name = single_action["name"]
            sg_publish_data = single_action["sg_publish_data"]
            params = single_action["params"]

            try:
                self.execute_action(name, params, sg_publish_data)
                # play sound from config hook 'play_sounds'
                self.parent.logger.info("Playing sound, reached end of execute_action")
                self.parent.engine.execute_hook_expression(
                    "{config}/notifications.py",
                    "success_sound",
                )
            except:
                # play sound from config hook 'play_sounds'
                self.parent.logger.info("Error raised in execute_action,\n Traceback:")
                self.parent.logger.warning(traceback.format_exc())
                self.parent.logger.info("Playing sound, error raised in execute_action")
                self.parent.engine.execute_hook_expression(
                    "{config}/notifications.py",
                    "error_sound",
                )

    def execute_action(self, name, params, sg_publish_data):
        """
        Execute a given action. The data sent to this be method will
        represent one of the actions enumerated by the generate_actions method.

        :param name: Action name string representing one of the items returned by generate_actions.
        :param params: Params data, as specified by generate_actions.
        :param sg_publish_data: Shotgun data dictionary with all the standard publish fields.
        :returns: No return value expected.
        """
        app = self.parent
        # app.logger.info(
        #     (
        #         "Execute action called for action {}. "
        #         "\nParameters:\n{}.\nPublish Data:\n{}"
        #     ).format(name, pf(params), pf(sg_publish_data))
        # )
        app.logger.info("Execute action called for action: '{}'.".format(name))

        # resolve path
        path = self.get_publish_path(sg_publish_data)

        # toolkit uses utf-8 encoded strings internally and the After Effects API expects unicode
        # so convert the path to ensure filenames containing complex characters are supported
        path = six.ensure_text(path)

        # if not os.path.exists(path):
        #     raise IOError("File not found on disk - '%s'" % path)

        """
        if name == "add_to_comp":
            app.logger.info("About to execute add_to_comp action")
            self._add_to_comp(path, sg_publish_data)
            app.logger.info("Finished executing add_to_comp action")
        """

        if name == "add_to_project":
            self._add_to_project(path, sg_publish_data)

        if name == "download_publish":
            self.ensure_file_is_local(path, sg_publish_data)

        if name == "import_Harmony_camera":
            self._import_harmony_camera(path, sg_publish_data)

        if name == "import_Harmony_tracker_peg":
            self._import_harmony_tracker_peg(path, sg_publish_data)

        if name == "Import_psd_output_layers_ordered":
            self.import_layer_group(path, sg_publish_data)

    ##############################################################################################################
    # helper methods which can be subclassed in custom hooks to fine tune the behaviour of things

    # def _add_to_comp(self, file_path, sg_publish_data):
    #     """
    #     Helper method to add the footage described by path to a comp
    #     """

    #     app = self.parent

    #     app.logger.info("add_to_comp action start")

    #     # file_path = self.ensure_file_is_local(path, sg_publish_data)
    #     # app.logger.info("add_to_comp finished downloading file: {}".format(file_path))

    #     if file_path:
    #         file_path = self._get_seq_first_frame(file_path)
    #         adobe = self.parent.engine.adobe
    #         comp_item = adobe.app.project.activeItem
    #         app.logger.info("add_to_comp comp_item: {}".format(comp_item))
    #         if not comp_item or not self.parent.engine.is_item_of_type(
    #             comp_item, self.parent.engine.AdobeItemTypes.COMP_ITEM
    #         ):
    #             return False

    #         new_items = self.parent.engine.import_filepath(file_path)
    #         app.logger.info("add_to_comp new items: {}".format(new_items))

    #         for item in new_items:
    #             if self.parent.engine.is_item_of_type(
    #                 item, self.parent.engine.AdobeItemTypes.FOLDER_ITEM
    #             ):
    #                 self.parent.engine.add_items_to_comp(item.items, comp_item)
    #                 continue
    #             comp_item.layers.add(item)

    #         return True

    def _add_to_project(self, path, sg_publish_data):
        """
        Helper method to add the footage described by path to a project
        """
        app = self.parent

        file_path = self.ensure_file_is_local(path, sg_publish_data)

        if file_path:
            file_path = self._get_seq_first_frame(file_path)
            self.parent.engine.import_filepath(file_path)

            return True

        return False

    '''
    # moved to /tk-multi-loader2/tk-multi-loader2_multi_transfer_actions.py

    def ensure_file_is_local(self, path, sg_publish_data):

        app = self.parent

        if not hasattr(self, 'metasync'):
            self.metasync = self.load_framework("mty-framework-metasync")

        transfersManager = self.metasync.transfersManager
        if "%" in path:
            dirname = os.path.dirname(path)
            if os.path.exists(dirname) and self._collect_sequenced_files(path):
                transfersManager.ensure_local_dependencies(sg_publish_data)
                return path
        else:
            if os.path.exists(path):
                transfersManager.ensure_local_dependencies(sg_publish_data)
                return path

        transfersManager.ensure_file_is_local(path, sg_publish_data)
        transfersManager.ensure_local_dependencies(sg_publish_data)

        return path
    '''
    ####################################################################################0

    def _get_seq_first_frame(self, path):
        # get sequence fisrt frame
        if self.parent.engine.is_adobe_sequence(path):
            frame_range = self.parent.engine.find_sequence_range(path)
            if frame_range:
                glob_path = re.sub(
                    r"[\[]?([#@]+|%0\d+d)[\]]?", "*{}".format(frame_range[0]), path
                )
                for each_path in sorted(glob.glob(glob_path)):
                    path = each_path
                    # break

        return path

    def _collect_sequenced_files(self, sequence_path):
        app = self.parent

        folder_path = os.path.dirname(sequence_path)
        folder_files = os.listdir(folder_path)

        name, ext = os.path.splitext(sequence_path)
        sequence_files = []

        if len(folder_files) > 0:
            for file in sorted(folder_files):
                if file.endswith(ext):
                    path = os.path.join(folder_path, file)
                    sequence_files.append(path)

        return sequence_files

    def create_import_camera_command(self, file_path, version):
        app = self.parent
        adobe = self.parent.engine.adobe

        hooks_location = os.path.dirname(os.path.dirname(self.disk_location))

        # TODO: find a way to run an external script instead of adding the whole script
        # here.
        import_cam_cmd = """
function importTBCamera(camera_path, version) {

    var project = app.project;
    if (!project)
        return -1;

    var activeItem = project.activeItem;
    if (!activeItem)
        return -2;

    var activeComp = activeItem;
    var poi = [activeComp.width/2, activeComp.height/2];

    /*
    var filterWin = "*.jsonx";
    var filterMac = function(file) {
        return file.name.search(".jsonx") != -1;
    };
    var filter = system.osName == "MacOS" ? filterMac : filterWin;
    */

    // var exportedFile = File.openDialog("Select an exported camera from Harmony/Storyboard.",  filter);
    var exportedFile = new File(camera_path);
    if (exportedFile == null)
        return -3;

    var opened = exportedFile.open ("r", "TEXT", "????");
    if (!opened)
        return -4;

    var data = exportedFile.read();
    var parsed = JSON.parse(data);
    exportedFile.close();

    var frameRate = 60.0;
    var arX = 4;
    var arY = 3;
    var fov = 41.112;
    var resolutionRatio = poi[0] / poi[1];

    app.beginUndoGroup("Import Toon Boom Camera");

    var nCreated = 0;
    var createdNodes = [];
    for(var i=0 ; i < parsed.length ; ++i)
    {
        $.writeln(i);

        var obj = parsed[i];
        if (obj.type == "Settings")
        {
            if (obj.frameRate !== "undefined")
                frameRate = obj.frameRate;
            if (obj.unitsAspectRatioX !== "undefined")
                arX = obj.unitsAspectRatioX;
            if (obj.unitsAspectRatioY !== "undefined")
                arY = obj.unitsAspectRatioY;
            if (obj.defaultResolutionFOV !== "undefined" )
                fov = obj.defaultResolutionFOV;
        }
        if (obj.type != "CAMERA")
            continue;

        var dar = arX / arY;
        $.writeln("Framerate: " + frameRate);
        $.writeln("Design aspect ratio: " + dar);

        var camName = obj.name;
        if (frameRate<=0)
            frameRate = 1;

        var camera = activeComp.layers.addCamera(camName, poi);
        createdNodes.push(camera);

        // add original path as the layer comment for later use in the breakdown app
        camera.comment = camera_path;

        camera.autoOrient = AutoOrientType.NO_AUTO_ORIENT;
        camera.startTime = 0;

        var currentZ = - poi[1] / Math.tan(fov * Math.PI / 360.0);
        $.writeln("Z = " + currentZ);

        camera.property("Camera Options").property("Zoom").setValue(Math.abs(currentZ));
        current_camera_zoom = camera.property("Camera Options").property("Zoom").value;
        $.writeln("new camera zoom = " + current_camera_zoom);

        var cameraNullObj = activeComp.layers.addNull();
        createdNodes.push(cameraNullObj);
        var cameraNullObjName = camName + version + '_transforms';
        $.writeln("cameraNullObjName: " + cameraNullObjName);
        cameraNullObj.name = cameraNullObjName;
        cameraNullObj.source.name = cameraNullObjName;

        // add original path as the layer comment for later use in the breakdown app
        cameraNullObj.comment = camera_path;
        cameraNullObj.source.comment = camera_path;

        // Have to make it a 3D object because of the Z translation
        cameraNullObj.threeDLayer = true;
        camera.parent = cameraNullObj;

        // position camera at 0,0,0 because the null obj contains all the information
        camera.position.setValuesAtTimes([0], [[0, 0, 0]]);

        if (obj.position)
        {
            var keys = [];
            var pos = [];

            for(var k = 0; k < obj.position.length; ++k)
            {
                keys.push(obj.position[k][0] / frameRate);
                var x = obj.position[k][1] / resolutionRatio * poi[0] + poi[0];
                var y = (-obj.position[k][2] * poi[1])+ poi[1];
                var z = obj.position[k][3] / (2 * dar) * currentZ;
                pos.push([x, y, z]);
            }
            cameraNullObj.position.setValuesAtTimes(keys, pos);
        }
        else
        {
            cameraNullObj.position.setValuesAtTimes([0], [[poi[0], poi[1], currentZ]]);
        }

        if (obj.rotation)
        {
            var keys = [];
            var rotZ = [];
            var allZeros = true;

            for(var k =0; k < obj.rotation.length; ++k)
            {
                keys.push(obj.rotation[k][0] / frameRate);
                if (obj.rotation[k][3] < -0.00001 || obj.rotation[k][3] > 0.00001)
                    allZeros = false;
                rotZ.push(-obj.rotation[k][3]);
            }
            if (!allZeros)
                cameraNullObj.rotation.setValuesAtTimes(keys, rotZ);

            if (obj.is3D)
            {
                var rotX = [];
                allZeros = true;

                for(var k =0; k < obj.rotation.length; ++k)
                {
                    if (obj.rotation[k][1] < -0.00001 || obj.rotation[k][1] > 0.00001)
                        allZeros = false;
                    rotX.push(obj.rotation[k][1]);
                }
                if (!allZeros)
                    cameraNullObj.rotationX.setValuesAtTimes(keys, rotX);

                var rotY = [];
                allZeros = true;

                for(var k =0; k < obj.rotation.length; ++k)
                {
                    if (obj.rotation[k][2] < -0.00001 || obj.rotation[k][2] > 0.00001)
                        allZeros = false;
                    rotY.push(-obj.rotation[k][2]);
                }
                if (!allZeros)
                    cameraNullObj.rotationY.setValuesAtTimes(keys, rotY);
            }
        }

        /*
        // Scale is ignored because it shouldn't be modified at all from within Harmony

        if (obj.scale)
        {
            var keys = [];
            var scales = [];
            var allOnes = true;

            // Read all scales and makew sure there is a scaling not equal to 1
            for(var k =0 ; k<obj.scale.length ; ++k)
            {
                keys.push(obj.scale[k][0] / frameRate);
                var x = obj.scale[k][1];
                var y = obj.scale[k][2];
                var z = obj.scale[k][3];
                if (x < 0.9999 || x > 1.0001 || y < 0.9999 || y > 1.0001 || z < 0.9999 || z > 1.0001   )
                    allOnes = false;
                scales.push([x, y, z]);
            }

            // If one is not 1 then apply the scaling
            if (!allOnes)
            {
                cameraNullObj.scale.setValuesAtTimes(keys, scales);
            }
        }
        */

        ++nCreated;
    }
    app.endUndoGroup();

    // return nCreated;
    return createdNodes;
};

importTBCamera("%s", "%s");
""" % (file_path, version)

        self.parent.logger.info("version: {}, file_path: {}".format(version, file_path))
        self.parent.logger.debug("import_cam_cmd:\n{}".format(import_cam_cmd))

        return import_cam_cmd

    def create_import_peg_command(self, file_path, version):
        app = self.parent
        adobe = self.parent.engine.adobe

        hooks_location = os.path.dirname(os.path.dirname(self.disk_location))

        # TODO: find a way to run an external script instead of adding the whole script
        # here.
        import_peg_cmd = """
function import_TB_peg(peg_path, version) {

    var project = app.project;
    if (!project)
        return -1;

    var activeItem = project.activeItem;
    if (!activeItem)
        return -2;

    var activeComp = activeItem;
    var poi = [activeComp.width/2, activeComp.height/2];

    /*
    var filterWin = "*.jsonx";
    var filterMac = function(file) {
        return file.name.search(".jsonx") != -1;
    };
    var filter = system.osName == "MacOS" ? filterMac : filterWin;
    */

    // var exportedFile = File.openDialog("Select an exported camera from Harmony/Storyboard.",  filter);
    var exportedFile = new File(peg_path);
    if (exportedFile == null)
        return -3;

    var opened = exportedFile.open ("r", "TEXT", "????");
    if (!opened)
        return -4;

    var data = exportedFile.read();
    var parsed = JSON.parse(data);
    exportedFile.close();

    var frameRate = 60.0;
    var arX = 4;
    var arY = 3;
    var fov = 41.112;
    var resolutionRatio = poi[0] / poi[1];

    app.beginUndoGroup("Import Toon Boom Peg");

    var nCreated = 0;
    var created_nodes = [];
    for(var i=0 ; i < parsed.length ; ++i)
    {
        $.writeln(i);

        var obj = parsed[i];
        if (obj.type == "Settings")
        {
            if (obj.frameRate !== "undefined")
                frameRate = obj.frameRate;
            if (obj.unitsAspectRatioX !== "undefined")
                arX = obj.unitsAspectRatioX;
            if (obj.unitsAspectRatioY !== "undefined")
                arY = obj.unitsAspectRatioY;
            if (obj.defaultResolutionFOV !== "undefined" )
                fov = obj.defaultResolutionFOV;
        }
        if (obj.type != "PEG")
            continue;

        var dar = arX / arY;
        $.writeln("Framerate: " + frameRate);
        $.writeln("Design aspect ratio: " + dar);

        var peg_name = obj.name;
        if (frameRate<=0)
            frameRate = 1;

        // var camera = activeComp.layers.addCamera(peg_name, poi);
        // createdNodes.push(camera);

        // add original path as the layer comment for later use in the breakdown app
        // camera.comment = camera_path;

        // camera.autoOrient = AutoOrientType.NO_AUTO_ORIENT;
        // camera.startTime = 0;

        var currentZ = - poi[1] / Math.tan(fov * Math.PI / 360.0);
        $.writeln("Z = " + currentZ);

        var peg_null_obj = activeComp.layers.addNull();
        created_nodes.push(peg_null_obj);
        var peg_null_obj_name = peg_name + version + '_transforms';
        $.writeln("peg_null_obj_name: " + peg_null_obj_name);
        peg_null_obj.name = peg_null_obj_name;
        peg_null_obj.source.name = peg_null_obj_name;

        // add original path as the layer comment for later use in the breakdown app
        peg_null_obj.comment = peg_path;
        peg_null_obj.source.comment = peg_path;

        // Have to make it a 3D object because of the Z translation
        peg_null_obj.threeDLayer = true;
        // camera.parent = peg_null_obj;

        // position camera at 0,0,0 because the null obj contains all the information
        // camera.position.setValuesAtTimes([0], [[0, 0, 0]]);

        if (obj.position)
        {
            var keys = [];
            var pos = [];

            for(var k = 0; k < obj.position.length; ++k)
            {
                keys.push(obj.position[k][0] / frameRate);
                var x = obj.position[k][1] / resolutionRatio * poi[0] + poi[0];
                var y = (-obj.position[k][2] * poi[1])+ poi[1];
                var z = obj.position[k][3] / (2 * dar) * currentZ;
                pos.push([x, y, z]);
            }
            peg_null_obj.position.setValuesAtTimes(keys, pos);
        }
        else
        {
            peg_null_obj.position.setValuesAtTimes([0], [[poi[0], poi[1], currentZ]]);
        }

        if (obj.rotation)
        {
            var keys = [];
            var rotZ = [];
            var allZeros = true;

            for(var k =0; k < obj.rotation.length; ++k)
            {
                keys.push(obj.rotation[k][0] / frameRate);
                if (obj.rotation[k][3] < -0.00001 || obj.rotation[k][3] > 0.00001)
                    allZeros = false;
                rotZ.push(-obj.rotation[k][3]);
            }
            if (!allZeros)
                peg_null_obj.rotation.setValuesAtTimes(keys, rotZ);

            if (obj.is3D)
            {
                var rotX = [];
                allZeros = true;

                for(var k =0; k < obj.rotation.length; ++k)
                {
                    if (obj.rotation[k][1] < -0.00001 || obj.rotation[k][1] > 0.00001)
                        allZeros = false;
                    rotX.push(obj.rotation[k][1]);
                }
                if (!allZeros)
                    peg_null_obj.rotationX.setValuesAtTimes(keys, rotX);

                var rotY = [];
                allZeros = true;

                for(var k =0; k < obj.rotation.length; ++k)
                {
                    if (obj.rotation[k][2] < -0.00001 || obj.rotation[k][2] > 0.00001)
                        allZeros = false;
                    rotY.push(-obj.rotation[k][2]);
                }
                if (!allZeros)
                    peg_null_obj.rotationY.setValuesAtTimes(keys, rotY);
            }
        }

        /*
        // Scale is ignored because it shouldn't be modified at all from within Harmony

        if (obj.scale)
        {
            var keys = [];
            var scales = [];
            var allOnes = true;

            // Read all scales and makew sure there is a scaling not equal to 1
            for(var k =0 ; k<obj.scale.length ; ++k)
            {
                keys.push(obj.scale[k][0] / frameRate);
                var x = obj.scale[k][1];
                var y = obj.scale[k][2];
                var z = obj.scale[k][3];
                if (x < 0.9999 || x > 1.0001 || y < 0.9999 || y > 1.0001 || z < 0.9999 || z > 1.0001   )
                    allOnes = false;
                scales.push([x, y, z]);
            }

            // If one is not 1 then apply the scaling
            if (!allOnes)
            {
                cameraNullObj.scale.setValuesAtTimes(keys, scales);
            }
        }
        */

        ++nCreated;
    }
    app.endUndoGroup();

    // return nCreated;
    return created_nodes;
};

import_TB_peg("%s", "%s");

""" % (file_path, version)

        self.parent.logger.info("version: {}, file_path: {}".format(version, file_path))
        self.parent.logger.debug("import_peg_cmd:\n{}".format(import_peg_cmd))

        return import_peg_cmd

    def execute_import_obj_command(self, import_cam_cmd):
        """ Executes the import_obj_cmd command """
        app = self.parent
        adobe = self.parent.engine.adobe

        created_nodes = adobe.rpc_eval(import_cam_cmd)
        self.parent.logger.info("created_nodes: {}".format(created_nodes))

    def _import_harmony_camera(self, path, sg_publish_data):
        """ Creates a new camera using the data from a published jsonx from Harmony """

        self.parent.engine.logger.info("Start action _import_harmony_camera")

        app = self.parent
        adobe = self.parent.engine.adobe

        file_path = self.ensure_file_is_local(path, sg_publish_data)

        # get version token from path and template
        fields = {}

        harmony_camera_template = self.parent.sgtk.template_from_path(file_path)
        if harmony_camera_template:
            fields = harmony_camera_template.get_fields(file_path)

        version = fields.get("version", "")
        if version:
            version = "_v{}".format(str(version).zfill(3))
        self.parent.logger.debug("version: {}".format(version))

        # Fix path
        file_path = file_path.replace("\\\\", "\\")
        file_path = file_path.replace("\\", "/")

        import_cam_cmd = self.create_import_camera_command(file_path, version)
        # execute the command
        self.execute_import_obj_command(import_cam_cmd)

    def _import_harmony_tracker_peg(self, path, sg_publish_data):
        """ Creates a new peg using the data from a published jsonx from Harmony """

        self.parent.engine.logger.info("Start action peg")

        app = self.parent
        adobe = self.parent.engine.adobe

        file_path = self.ensure_file_is_local(path, sg_publish_data)

        # get version token from path and template
        fields = {}

        harmony_peg_template = self.parent.sgtk.template_from_path(file_path)
        if harmony_peg_template:
            fields = harmony_peg_template.get_fields(file_path)

        version = fields.get("version", "")
        if version:
            version = "_v{}".format(str(version).zfill(3))
        self.parent.logger.info("version: {}".format(version))

        # Fix path
        file_path = file_path.replace("\\\\", "\\")
        file_path = file_path.replace("\\", "/")

        import_peg_cmd = self.create_import_peg_command(file_path, version)
        # execute the command
        self.execute_import_obj_command(import_peg_cmd)

    def import_layer_group(self, path, sg_publish_data):
        self.parent.engine.logger.info("start import layers")
        # reaft json
        with open(path, "r") as f:
            layer_group = json.load(f)
        self.parent.engine.logger.info("read json")
        # Collect the paths of the layers
        layers = []
        for layer_value in layer_group.values():
            layer_path = layer_value["layer_group_path"]
            layer_proxy_path = layer_value["layer_group_proxy_path"]

            layers.append(layer_path)
            layers.append(layer_proxy_path)


        # try to find if this layers exist in shotgun
        # beaceause if this layers were exporter with the farm
        # could not be exported yet
        self.parent.engine.logger.info(layers)

        fields = ["entity", "project", "path", "sg_source_location", "published_file_type"]
        sg_layers_data = sgtk.util.find_publish(self.parent.tank, layers, fields=fields)
        self.parent.engine.logger.info(sg_layers_data)

        proxy_layers_data = []
        layer_groups_data = []
        if sg_layers_data:
            for sg_layer_data in sg_layers_data.values():

                path = self.get_publish_path(sg_layer_data)

                path = six.ensure_text(path)

                if os.path.exists(path):
                    if sg_layer_data["published_file_type"]["name"] == "Photoshop Layer Group Image":
                        layer_groups_data.append(path)
                    if sg_layer_data["published_file_type"]["name"] == "Photoshop Layer Group Proxy Image":
                        proxy_layers_data.append(path)

        # self.parent.engine.logger.info(proxy_layers_data)
        # self.parent.engine.logger.info(layer_groups_data)


        # show dialog and wait for response
        selection = self.select_layer_type_to_import(layer_groups_data, proxy_layers_data)

        if selection == 0:
            return

        adobe = self.parent.engine.adobe

        ae_project = adobe.app.project
        project_items = ae_project.items
        compositions = []
        footages = []
        footages_paths = []

        # get the infomation from the project
        # and create a lists of compositions and footages
        for i, item in enumerate(self.parent.engine.iter_collection(project_items)):
            self.parent.engine.logger.info("{}: {}".format(item.name, item.typeName))
            if item.typeName == "Composition":
                compositions.append(item)
            if item.typeName == "Footage":
                self.parent.engine.logger.info(item.file.fsName)
                footages.append(item)
                footages_paths.append(item.file.fsName)

        self.parent.engine.logger.info(footages_paths)

        # execute the import using the selected type
        import_files = []

        if selection == 1:
            subfijer = "layers"
            resolution = self.parent.execute_hook_expression(
            "{config}/tk-multi-publish2/multi/resolution_from_path.py",
            "media_resolution_value",
            path=layer_groups_data[0],
        )
            for file_path in layer_groups_data:
                #skype the master layer
                if "_master_v" in os.path.basename(file_path):
                    continue
                if not file_path in footages_paths:
                    import_image = self.parent.engine.import_filepath(file_path)
                    import_files.append(import_image)
                else:
                    for footage in footages:
                        if footage.file.fsName == file_path:
                            import_files.append(footage)

        if selection == 2:
            subfijer = "proxies"
            resolution = self.parent.execute_hook_expression(
            "{config}/tk-multi-publish2/multi/resolution_from_path.py",
            "media_resolution_value",
            path=proxy_layers_data[0],
        )
            for file_path in proxy_layers_data:
                #skype the master layer proxy
                if "_master_proxy_v" in os.path.basename(file_path):
                    continue
                if not file_path in footages_paths:
                    import_image = self.parent.engine.import_filepath(file_path)
                    import_files.append(import_image)
                else:
                    for footage in footages:
                        if footage.file.fsName == file_path:
                            import_files.append(footage)

        # get info from shotgun
        project_data = self.get_project_data_from_SG()
        shot_data = self.get_shot_data_from_SG()

        # create a name of the composition
        comp_name = sg_publish_data.get("name", "NewComp").replace(".json", "") or "NewComp"
        comp_name = ("{}_{}".format(comp_name, subfijer))

        #get the resolution from image
        width, height = map(int, resolution.split("x"))

        #try to create a new composition with sg info
        if project_data and shot_data:
            pixel_aspect = float(1.0)
            duration =  float(shot_data.get("sg_cut_duration_in_seconds"))
            frame_rate = float(project_data.get("sg_fps"))

        # if not try to use the master composition
        else:
            master_composition = None
            for composition in compositions:
                self.parent.engine.logger.info(composition.name)
                if composition.name in "OutputRender":
                    master_composition = composition
                    break

            if master_composition:
                pixel_aspect = master_composition.pixelAspect
                duration = master_composition.duration
                frame_rate = master_composition.frameRate

        new_comp = ae_project.items.addComp(
        comp_name,
        width,
        height,
        pixel_aspect,
        duration,
        frame_rate)

        self.parent.engine.logger.info("width: {}".format(new_comp.width))
        self.parent.engine.logger.info("height: {}".format(new_comp.height))
        self.parent.engine.logger.info("pixelAspect: {}".format(new_comp.pixelAspect))
        self.parent.engine.logger.info("duration: {}".format(new_comp.duration))
        self.parent.engine.logger.info("frameRate: {}".format(new_comp.frameRate))

        for file in import_files:
            new_comp.layers.add(file)



    def select_layer_type_to_import(self, layers=[], proxy_layers=[]):
        if not layers and not proxy_layers:
            message = (

                "This psd file has no layer groups yet. ",
                "if your project uses a farm, maybe arent not exported yet.",
                "plese try again later or contact your project manager."
            )
        else:
            message = (
                "Plase select the type of layer group you want to import: "
            )


        # Create the dialog
        dialog = QDialog()
        dialog.setWindowTitle("Select Layer Type")

        # Layout
        layout = QVBoxLayout()
        dialog.setLayout(layout)

        # Message label
        label = QLabel(message)
        layout.addWidget(label)

        # Buttons
        layer_button = QPushButton("Photoshop Layer Group")
        proxy_button = QPushButton("Photoshop Layer Group Proxy")
        cancel_button = QPushButton("Cancel")

        # Function for Delete button click
        def on_layer_button():
            dialog.done(1)  # Return 1 for Delete button

        def on_proxy_button():
            dialog.done(2)  # Return 1 for Delete button

        # Function for Cancel button click
        def on_cancel_clicked():
            dialog.done(0)  # Return 0 for Cancel button

        cancel_button.setDefault(True)  # Set Cancel button as default
        if layers:
            layout.addWidget(layer_button)
            layer_button.clicked.connect(on_layer_button)
        if proxy_layers:
            layout.addWidget(proxy_button)
            proxy_button.clicked.connect(on_proxy_button)
        layout.addWidget(cancel_button)
        # Connect button signals to functions
        cancel_button.clicked.connect(on_cancel_clicked)

        # Execute the dialog
        dialog.exec_()

        # Return the result
        return dialog.result()


    def get_shot_data_from_SG(self):
        """
        Returns a dictionary of shot data from SG if the current context
        is a shot of the following form:

        {
            'id': (int),
             'sg_cut_duration': (int),
             'sg_cut_duration_in_seconds': (float),
             'sg_cut_in': (int),
             'sg_cut_out': (int),
             'type': (str),
         }
        """

        shot_data = {}

        if self.parent.engine.context.entity["type"] == "Shot":
            filters = [
                ["id", "is", self.parent.engine.context.entity["id"]],
            ]
            fields = [
                "sg_cut_in",
                "sg_cut_out",
                "sg_cut_duration",
                "sg_cut_duration_in_seconds",
            ]

            shot_data = self.parent.engine.shotgun.find_one(
                entity_type="Shot", filters=filters, fields=fields
            )

        return shot_data


    def get_project_data_from_SG(self):
        """
        Returns a dictionary of project data from SG if the current context
        is a project of the following form:

        {
            'id': (int),
            'code': (str),
            'name': (str),
            'type': (str),
        }
        """


        filters = [
            ["id", "is", self.parent.engine.context.project["id"]],
        ]
        fields = ["code", "name", "type", "sg_fps", "sg_resolution"]

        project_data = self.parent.engine.shotgun.find_one(
            entity_type="Project", filters=filters, fields=fields
        )

        return project_data