# -*- coding: utf-8 -*-
# Standard library:
#   .   .   .   .   .   .   .   .   .   .   .
# Third party:
import sgtk
import pymel.core as pm
#   .   .   .   .   .   .   .   .   .   .   .
# Project:
# ================================================================

HookBaseClass = sgtk.get_hook_baseclass()


class MayaShotDataValidationPlugin(HookBaseClass):

    def __init__(self, parent):
        super(
            MayaShotDataValidationPlugin,
            self
        ).__init__(parent)
        self.core_maya_tools = \
            self.load_framework("mty-framework-coremayatools")
        self.composer = self.core_maya_tools.checks.composer

    @property
    def description(self):
        return """
            <p>
            This plugin handles the validation of the
            shot node data neccesary for publishing
            a sequence.
            </p>
        """

    # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

    @property
    def item_filters(self):
        return["maya.session.shot"]

    # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

    def accept(self, settings, item):

        shot_nodes = item.properties["nodes"]

        if not shot_nodes:
            self.logger.debug(
                "The shot_node, shot_name or the assets_string " +
                "was not set for the collected shot items."
            )
            return {
                "accepted": False,
                "enabled": False,
                "visible": True,
                "checked": False
            }
        else:
            return{
                "accepted": True,
                "enabled": True,
                "visible": True,
                "checked": True
            }

    def validate(self, settings, item):

        state = {
            "errors_found": 0,
            "errors": {},
            "callbacks": {}
        }

        list_of_assets = self.list_of_assets_in_session()

        for shot in item.properties["nodes"]:
            self.composer.add(
                name="{0} assets".format(shot.getShotName()),
                list_of_errors=self.list_of_assets_link_errors(
                    shot, list_of_assets
                ),
                state=state,
                action={"enabled": False}
            )

        check_hooks = \
            self.parent.get_setting("check_hooks")

        checks = \
            self.parent.create_hook_instance(check_hooks)

        checks.generate_actions(state)

        return True

    def publish(self, settings, item):
        return True

    def finalize(self, settings, item):
        pass

    # ================================================================

    def list_of_assets_link_errors(self, shot, list_of_assets):
        result = []
        list_of_shot_assets = shot.assets.get()

        if list_of_shot_assets == "" \
                or list_of_shot_assets is None:

            message = "{0} has no assets linked.".format(shot.getShotName())
            result.append(message)
            return result

        else:
            list_of_shot_assets = list_of_shot_assets.split(";")

        for asset in list_of_shot_assets:
            if asset not in list_of_assets:

                message = \
                    "\"{0}\" asset linking error: \n\n".format(
                        shot.getShotName()
                    ) + \
                    "This could happen either because \n" + \
                    "the linked asset was renamed, \n" + \
                    "no longer exists, or it was replaced \n" + \
                    "by a new reference but the shot assets \n" + \
                    "attribute was not updated in the \n" + \
                    "shot linking process."

                result.append(message)

        return result

    # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

    def list_of_assets_in_session(self, gpuCache=True):
        result = []
        self.list_of_references = pm.listReferences()
        self.list_of_gpu_caches = pm.ls(type="gpuCache")

        for reference in self.list_of_references:
            result.append(reference.refNode)

        if gpuCache:
            for gpu_cache in self.list_of_gpu_caches:
                result.append(gpu_cache.getParent())

        return result
