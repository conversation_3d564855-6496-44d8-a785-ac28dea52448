#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

"""
An app that syncs the frame range between a scene and a shot in Shotgun.
Plus adding and modifiying the scene Ren<PERSON> Settings

"""

import sys
import os

from tank import Hook
from tank import TankError
from tank.platform import Application
from tank.platform.qt import QtCore, QtGui
import tank

import maya.cmds as cmds
import pymel.core as pm


class ProcessItemsHook(Hook):

	def execute(self, entity_type, entities, other_params, **kwargs):
		#self.pathReplacer()
		self.autoLoadFaceRig()
		self.LoadTextures()
		print("Funciona?")
		message =  "Textures have been refreshed! \n"
		message += "please check everything looks fine\n"
		message += "before start working."
		QtGui.QMessageBox.information(None, "Texture Loaded!", message)

		return {'succes': [1], 'messages': [], 'errors': []}

	def pathReplacer(self):
		fileNodes = cmds.ls(type = "file")

		for f in fileNodes:
			cmds.select(f, replace=True)
			imageNameFull = cmds.getAttr(".fileTextureName")
			imageNameBase = os.path.basename(imageNameFull)
			cmds.setAttr(f+".fileTextureName",imageNameFull.replace("julio.pacheco","julio-pacheco"),type="string")

	def autoLoadFaceRig(self):
		import maya.mel as mel
		mel.eval('ActivateViewport20;')
		mel.eval('{  global string $gViewport2;   string $currentPanel = `getPanel -withFocus`;   string $panelType = `getPanel -to $currentPanel`;  if ($panelType ==  "modelPanel") {      setRendererInModelPanel $gViewport2 $currentPanel;  } else if ($panelType ==  "scriptedPanel") { 	string $cmd = "setRendererInModelPanel $gViewport2 "; scriptedPanelRunTimeCmd( $cmd, $currentPanel ); }};')
		mel.eval('DisplayShadedAndTextured;')
		mel.eval('generateAllUvTilePreviews;')
		cmds.select (cl=True)

	def filterCurvesANI(self):
		ls = cmds.ls(type='nurbsCurve')

		Ctls_ANI = []

		for each in ls:
			each = cmds.listRelatives(each, parent=True)[0]

			if cmds.objExists(each + '.ANI') and cmds.getAttr(each + '.ANI', lock=True) == False:
				if not each in Ctls_ANI:
					Ctls_ANI.append(each)

		return Ctls_ANI

	def getConnectionANI(self, Ctrl):
		if cmds.objExists(Ctrl + '.ANI'):
			anim_Curve = cmds.listConnections(Ctrl + '.ANI', plugs=True, type='animCurve', source=True, destination=False)
			return anim_Curve
		else:
			return None

	def getMinMaxRangeANI(self, Ctrl):
		if cmds.objExists(Ctrl + '.ANI'):
			Min_Max = [0, 0]

			if cmds.attributeQuery('ANI', node=Ctrl, minExists=True):
				Min_Max[0] = cmds.attributeQuery('ANI', node=Ctrl, minimum=True)[0]
			if cmds.attributeQuery('ANI', node=Ctrl, maxExists=True):
				Min_Max[1] = cmds.attributeQuery('ANI', node=Ctrl, maximum=True)[0]

			return Min_Max



	def LoadTextures(self):
		locator = cmds.spaceLocator( name='load_Locator' )[0]
		cmds.setAttr( locator+'.v', 0 )
		ctrls = self.filterCurvesANI()

		ctrls_connection = []
		clamps_Nodes = []

		if len( ctrls ) > 0:
			for each in ctrls:
				each_connection = self.getConnectionANI( each )

				if not each_connection is None:
					ctrls_connection.append( [ self.getConnectionANI( each )[0], each ] )

				each_MinMax = self.getMinMaxRangeANI( each )
				clamp_Ani = cmds.shadingNode( 'clamp', name='ClampANI_Value_#', au=True )
				cmds.setAttr( clamp_Ani+'.minR', each_MinMax[0] )
				cmds.setAttr( clamp_Ani+'.maxR', each_MinMax[1] )
				clamps_Nodes.append( clamp_Ani )
				cmds.connectAttr( locator+'.tx', clamp_Ani+'.inputR' )

				cmds.connectAttr( clamp_Ani+'.outputR', each+'.ANI', f=True )

			for i in range(1,93):
				cmds.setAttr( locator+'.tx', i )
				cmds.setKeyframe( locator, attribute='translateX', value=i, time=i )


			cmds.bakeResults( locator, time=(1,93), sb=1, simulation=1 )
			cmds.delete( locator, clamps_Nodes )

			for connection in ctrls_connection:
				cmds.connectAttr( connection[0], connection[1]+'.ANI', f=True )
		print('Script By Breiner Yabichella\n Thanks Breiner\n')
