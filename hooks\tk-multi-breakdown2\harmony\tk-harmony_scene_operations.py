# Copyright (c) 2021 Autodesk, Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Autodesk, Inc.

import os
import re
import sgtk
import json
import time
import pprint
import fileseq
import tempfile
import traceback
# from pprint import pformat as pf

pp = pprint.pprint
pf = pprint.pformat

HookBaseClass = sgtk.get_hook_baseclass()

META_SHOTGUN_PATH = "meta.shotgun.path"


class BreakdownSceneOperations(HookBaseClass):
    """
    Breakdown operations for Harmony.

    This implementation handles detection of composition items, footage items, etc.
    """

    def scan_scene(self):
        """
        Scan the Harmony project and return a list of items to be operated on.
        The return data structure is a list of dictionaries. Each scene reference
        that is returned should be represented by a dictionary with keys:
        - "node_name": The name of the 'node' that is to be operated on.
        - "node_type": The object type that this is. This is later passed to the
          update method so that it knows how to handle the object.
        - "path": Path on disk to the referenced object.
        - "extra_data": Optional key to pass some extra data to the update method.
        Toolkit will scan the list of items, see if any of the objects matches
        a published file, and try to determine if there is a more recent version
        available. Any such versions are then displayed in the UI as out of date.
        """

        items_result = []

        try:
            self.parent.engine.logger.info("The scan function started. ".ljust(88, "-"))

            collect_nodes_cmd = """
var log = MessageLog.trace;

// function to get the list of READ type nodes
function get_nodes_list(node_type_list) {
    // var node_type_list = ["READ"]
    var nodes_list = node.getNodes(node_type_list);

    return nodes_list;
}

// function to filter nodes by attribute
function filter_nodes_by_attribute(nodes_array, attr_name) {
    var filtered_nodes = [];

    for (var i = 0; i < nodes_array.length; i++) {
        var current_node = nodes_array[i];
        var attr_value = node.getAttr(current_node, frame.current(), attr_name);

        if (attr_value !== null && attr_value.textValue() !== "") {
            filtered_nodes.push(current_node);
            log("Node: " + current_node + ", Attr Value: " + attr_value.textValue());
        }
    }

    return filtered_nodes;
}

// function to get the path of each node
function get_paths(nodes_array, attr_name) {   
    var paths_array = [];
    
    for (var i = 0; i < nodes_array.length; i++) {
        var current_node = nodes_array[i];
        var path = node.getAttr(current_node, frame.current(), attr_name).textValue();
        paths_array.push(path);
    }

    return paths_array;
}

function get_structure_of_nodes(nodes_array, paths) {
    var structure = {};

    for (var i = 0; i < nodes_array.length; i++) {
        var current_node = nodes_array[i];
        var numInput = node.numberOfInputPorts(current_node);
        var numOutput = node.numberOfOutputPorts(current_node);
        var numberOfOutputLinks = node.numberOfOutputLinks(current_node, 0);
        structure[current_node] = {
            "node_path": current_node,
            "path": paths[i],
        };
    }

    log("Structure: " + JSON.stringify(structure, null, 4));
    return structure;
}

var META_SHOTGUN_PATH = "meta.shotgun.path"
var attr_name = META_SHOTGUN_PATH
var node_type_list = ["READ"]
var nodes_list = get_nodes_list(node_type_list)
var filtered_nodes = filter_nodes_by_attribute(nodes_list, attr_name)
var paths = get_paths(filtered_nodes, attr_name)
get_structure_of_nodes(filtered_nodes, paths)

    """ % ()
            result = self.parent.engine.app.execute(collect_nodes_cmd)
            self.parent.engine.logger.info(f"result:\n{pf(result)}")

            items_result = []
            for value in result.values():
                path = value['path']
                items_result.append(
                    {
                        'node_name': os.path.basename(path),
                        'path': path,
                        'node_type': 'Bg file',
                        'extra_data': value,
                    }
                )

        except:
            self.parent.engine.logger.info("End of scan function.")

        self.parent.engine.logger.info(f"items_result:\n{pf(items_result)}")
        self.parent.engine.logger.info("End of scan function.")

        return items_result  

    def update(self, item):
        """
        Perform replacements given a number of scene items passed from the app.

        Once a selection has been performed in the main UI and the user clicks
        the update button, this method is called.

        :param item: Dictionary on the same form as was generated by the scan_scene hook above.
                     The path key now holds the path that the node should be updated *to*
                     rather than the current path.
        """

        self.parent.engine.logger.info("The update function started. ".ljust(88, "-"))
        
        self.parent.engine.logger.info(pf(item))
        self.parent.engine.logger.info(pf(item.get("path")))

        node_name = item.get("node_name")
        node_type = item.get("node_type")
        path = item.get("path")
        extra_data = item.get("extra_data")
        old_path = extra_data.get("old_path")


        path = self.fix_path(path)
        old_path = self.fix_path(old_path)

        node_path = extra_data.get("node_path")
        new_element_name = os.path.basename(path)

        self.parent.engine.logger.info(
            f"node_path:\n{node_path}\nnew_element_name:\n{new_element_name}"
        )

        self.parent.engine.logger.info(
            f"The update function started for item {node_name}.".ljust(120, "-")
        )
        
        self.parent.engine.logger.debug(f"Item:\n{pf(item)}")
        self.parent.engine.logger.debug(f"path:\n{path}")

        # Replace attribute value
        change_attr_value_cmd = """

        include("harmony_utility_functions.js");

        node_path = "%s";
        nodes_array = [node_path]
        attr_name = "meta.shotgun.path"
        value = "%s"

        modify_attr(nodes_array, attr_name, value)
  

        """ % (
            node_path,
            path,
        )
        
        change_attr_value_result = self.parent.engine.app.execute(change_attr_value_cmd)
        self.parent.engine.logger.info(f"change_attr_value_result:\n{pf(change_attr_value_result)}")

        # Replace element in the scene
        new_element_name_cmd = """

        var nodePath = "%s";
        var elementId = node.getElementId(nodePath);
        var newElementName = "%s";
        element.renameById(elementId, newElementName);

        """ % (
            node_path,
            new_element_name,
        )

        new_element_name_result = self.parent.engine.app.execute(new_element_name_cmd)
        self.parent.engine.logger.info(f"new_element_name:\n{pf(new_element_name_result)}")

        # Replace drawing file path
        new_drawing_file_path_cmd = """

        include("harmony_utility_functions.js");
        
        var nodePath = "%s";
        var elementId = node.getElementId(nodePath);
        var drawingFilePath = Drawing.filename(elementId, 1)

        copy_file("%s", drawingFilePath)

        """ % (
            node_path, 
            path
        )

        new_drawing_file_path_result = self.parent.engine.app.execute(new_drawing_file_path_cmd)
        self.parent.engine.logger.info(f"new_drawing_file_path:\n{pf(new_drawing_file_path_result)}")

        new_element_name = self.remove_extension(new_element_name)
        self.parent.engine.logger.info(f"new_element_name without extension:\n{new_element_name}")

        # Rename node
        new_node_name_cmd = """

        include("harmony_utility_functions.js");

        rename_node("%s", "%s");

        """ % (
            node_path,
            new_element_name,
        )

        new_node_name_result = self.parent.engine.app.execute(new_node_name_cmd)
        self.parent.engine.logger.info(f"new_node_name:\n{pf(new_node_name_result)}")



        items_result = []

        dict_of_items = {
            "node_name": new_element_name,
            "node_type": node_type,
            "path": path,
        }

        items_result.append(dict_of_items)

        self.parent.engine.logger.debug("items_result:\n{}".format(pf(items_result)))

        return items_result

    def fix_path(self, path):
        """Replace all backward slashes with forward slashes."""

        path = path.replace("\\\\", "/")
        path = path.replace("\\", "/")

        return path

    def remove_extension(self, path):
        """Remove the extension from the path."""

        return os.path.splitext(path)[0]