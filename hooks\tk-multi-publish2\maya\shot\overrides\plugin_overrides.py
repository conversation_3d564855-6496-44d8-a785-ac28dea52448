# -*- coding: utf-8 -*-
# Standard library:
import os
import re
import json
import traceback
# ___   ___   ___   ___   ___   ___  ___
# Third party:
import sgtk
# ___   ___   ___   ___   ___   ___  ___
# Project:
import pymel.core as pm
import pprint

# ===============================================================================
HookBaseClass = sgtk.get_hook_baseclass()
pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)
pf = pprint.pformat


class MayaOverridesPublishPlugin(HookBaseClass):
    def __init__(self, parent):
        super(MayaOverridesPublishPlugin, self).__init__(parent)

    # ---------------------------------------------------------------------------
    @property
    def icon(self):
        return os.path.join(self.disk_location, 'icon_overrides.png')

    @property
    def name(self):
        return 'Environment Overrides Publish Plugin'

    @property
    def description(self):
        return (
            'This plugin help us to share nested transformation for '
            'EnvLocation and EnvModules'
        )

    @property
    def settings(self):
        _settings = {
            'Publish Template': {
                'type': 'template',
                'default': None,
                'description': (
                    'Template for overrides publish'
                )
            },
            'Work Template': {
                'type': 'template',
                'default': None,
                'description': (
                    'Template path for the current scene file.'

                )
            },
            'Primary Publish Template': {
                'type': 'template',
                'default': None,
                'description': (
                    'Template path for the current scene file for a '
                    'publish path.'
                )
            },
            'Publish Type': {
                'type': 'string',
                'default': None,
                'description': 'Publish type name'
            },
            'plugin_hooks': {
                'type': 'dict',
                'default': None,
                'description': ''
            }
        }


        plugin_settings = super(MayaOverridesPublishPlugin, self).settings or {}
        plugin_settings.update(_settings)

        return plugin_settings

    @property
    def item_filters(self):
        return ['maya.session.asset.environment.overrides']

    def plugin_hook(self, settings, string_key):
        _h = settings.get("plugin_hooks")
        return self.parent.create_hook_instance(_h.value.get(string_key))

    # ---------------------------------------------------------------------------

    def accept(self, settings, item):
        print("\n" + (">" * 120))
        print('\n{0}.accept'.format(self.name))
        result = {
            'accepted': True, 'checked': True
        }

        if not item.properties['list_of_references']:
            result['accepted'] = False
            result['checked'] = False
            return result

        item.properties['map_of_publishes_by_path'] = (
            sgtk.util.find_publish(
                tk=self.parent.sgtk,
                list_of_paths=[
                    str(x.path) for x in item.properties['list_of_references']
                ],
                fields=[
                    'task',
                    'task.Task.step',
                    'published_file_type',
                    'code',
                    'entity',
                    'entity.Asset.code'
                ]
            )
        )

        return result

    # ---------------------------------------------------------------------------
    def validate(self, settings, item):
        print("\n" + (">" * 120))
        print('\n{0}.validate'.format(self.name))
        list_of_errors = []
        return True

    # ---------------------------------------------------------------------------
    def publish(self, settings, item):
        print("\n" + (">" * 120))
        print('\n{0}.publish'.format(self.name))
        _hook = self.plugin_hook(settings, 'overrides')
        list_of_publishes = _hook.publish(settings, item)

        # The following three lines make possible for the post_phase hook
        # to upload data
        root_item = self.get_root_item(item)
        root_item.properties.setdefault('sg_publish_extra_data', [])
        root_item.properties['sg_publish_extra_data'].extend(list_of_publishes)

        return True

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item
        return root

    # ---------------------------------------------------------------------------
    def finalize(self, settings, item):
        pass
