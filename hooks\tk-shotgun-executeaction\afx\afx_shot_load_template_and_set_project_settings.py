#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

"""
An app that loads a template specific to the project and sets all relevant
project settings found in SG:
    shot settings:
        - shot sg_cut_in (first shot frame)
        - shot sg_cut_out (last shot frame)
    project settings:
        - frame rate
        - resolution
        - working color space

"""

from tank import Hook
from tank import TankError
from tank.platform import Application
from tank.platform.qt import QtCore, QtGui
import tank

import os
import re
import shutil
import pprint


class ProcessItemsHook(Hook):
    def execute(self, **kwargs):

        self.parent.engine.logger.info(
            "execute action load_template_and_apply_settings start. {}".format("-" * 80)
        )

        result = self.load_template_and_apply_settings()

        self.parent.engine.logger.info(
            "execute action load_template_and_apply_settings end. {}".format("-" * 80)
        )

        return result

    def load_template_and_apply_settings(self):

        adobe = self.parent.engine.adobe

        result = {"succes": [1], "messages": [], "errors": []}

        # Get relevant data for the hook
        shot_data = self.get_shot_data_from_SG()
        project_data = self.get_project_data_from_SG()
        templates_dict = self.find_template(project_data)

        self.parent.engine.logger.info(
            "shot_data:\n{}".format(pprint.pformat(shot_data))
        )
        self.parent.engine.logger.info(
            "project_data:\n{}".format(pprint.pformat(project_data))
        )
        self.parent.engine.logger.info(
            "templates_dict:\n{}".format(pprint.pformat(templates_dict))
        )

        if not templates_dict:
            msg = "Couldn't find any template for this project."
            self.parent.engine.logger.error(msg)
            self.show_message(msg, icon="Critical")
            # alert_box = adobe.alert(msg)

            return {"succes": [], "messages": [msg], "errors": [1]}

        # If we are dealing with one single template for the project, we directly
        # set the template path and continue to open it
        if len(templates_dict.keys()) == 1:
            template_path = templates_dict.get(
                list(templates_dict.keys())[0], {}
            ).get("file_path", "")

            if not os.path.exists(template_path):
                msg = "Template {} doesn't exist on disk".format(template_path)
                self.parent.engine.logger.error(msg)
                self.show_message(msg, icon="Critical")
                # alert_box = adobe.alert(msg)

                return {"succes": [], "messages": [msg], "errors": [1]}
        else:
            # This means we have multiple templates for this project, so we
            # let the user choose
            template_path = self.open_template_selection_dialog(templates_dict)

        # Copy template to a temporary location and open it from there instead of
        # opening the template itself
        template_copy_path = self.copy_template_to_user_area(template_path)
        self.parent.engine.logger.info(
            "template_copy_path: {}".format(template_copy_path)
        )

        # Open the afx project
        afx_project = self.open_template_project(template_copy_path)
        self.parent.engine.logger.info("afx_project: {}".format(afx_project))

        if not afx_project:
            msg = "Couldn't open afx project '{}'".format(template_copy_path)
            self.parent.engine.logger.error(msg)
            self.show_message(msg, icon="Critical")
            # alert_box = adobe.alert(msg)

            return {"succes": [], "messages": [msg], "errors": [1]}

        # set project settings
        self.set_colorspace(project_data.get("sg_working_color_space"))
        self.set_color_depth()
        self.set_time_display_type()

        compositions = self.collect_all_compositions_in_project()
        self.parent.engine.logger.info("found {} compositions".format(len(compositions)))
        # compositions = list(set(compositions))
        # self.parent.engine.logger.info("found {} compositions after set".format(len(compositions)))

        # set shot settings
        if compositions:
            list_of_output_comps = self.filter_output_compositions(compositions)
            # list_of_output_comps = list(set(list_of_output_comps))
            self.parent.engine.logger.info("found {} output compositions".format(len(list_of_output_comps)))

            if list_of_output_comps:
                frame_rate = project_data.get("sg_fps")
                shot_first_frame = shot_data.get("sg_cut_in")
                shot_last_frame = shot_data.get("sg_cut_out")
                shot_duration = (shot_last_frame - shot_first_frame) + 1
                # resolution = project_data.get("sg_resolution")

                # get resolution from SG
                hook_expression = "{config}/get_entity_resolution.py"
                resolution = self.parent.engine.execute_hook_expression(
                    hook_expression,
                    "get_resolution",
                    engine=self.parent.engine,
                )

                for comp in list_of_output_comps:
                    self.set_composition_settings(
                        comp,
                        frame_rate,
                        shot_first_frame,
                        shot_duration,
                        resolution,
                    )

        # remove render queue items
        render_queue_items = self.collect_all_render_items_in_render_queue()
        self.remove_items(render_queue_items)

        msg = (
            "Finished loading the project settings.\nPlease "
            "save your file using SG's File Save."
        )
        self.show_message(msg, icon="Information")
        # alert_box = adobe.alert(msg)

        return {"succes": [1], "messages": [msg], "errors": []}

    def get_shot_data_from_SG(self):
        """
        Returns a dictionary of shot data from SG if the current context
        is a shot of the following form:

        {
            'id': (int),
             'sg_cut_duration': (int),
             'sg_cut_duration_in_seconds': (float),
             'sg_cut_in': (int),
             'sg_cut_out': (int),
             'type': (str),
         }
        """

        shot_data = {}

        if self.parent.engine.context.entity["type"] == "Shot":
            filters = [
                ["id", "is", self.parent.engine.context.entity["id"]],
            ]
            fields = [
                "sg_cut_in",
                "sg_cut_out",
                "sg_cut_duration",
                "sg_cut_duration_in_seconds",
            ]

            shot_data = self.parent.engine.shotgun.find_one(
                entity_type="Shot", filters=filters, fields=fields
            )

        return shot_data

    def get_project_data_from_SG(self):
        """
        Returns a dictionary of project data from SG relevant to a
        shot of the following form:

            {
                'code': (str),
                 'id': (int),
                 'sg_fps': (float),
                 'sg_output_color_space': (str),
                 'sg_working_color_space': (str),
                 'type': (str),
             }
        """

        project_data = {}

        filters = [
            ["id", "is", self.parent.engine.context.project["id"]],
        ]
        fields = [
            "code",
            "id",
            "sg_fps",
            "sg_working_color_space",
            "sg_output_color_space",
        ]

        project_data = self.parent.engine.shotgun.find_one(
            entity_type="Project", filters=filters, fields=fields
        )

        return project_data

    def get_matching_files(self, folder_path, pattern):
        if isinstance(pattern, str):
            regex = re.compile(pattern)

        matching_files = {}

        for file_name in os.listdir(folder_path):
            # Ensure we're only dealing with files, not directories
            file_path = os.path.join(folder_path, file_name)
            if not os.path.isfile(file_path):
                continue

            match = regex.match(file_name)
            if not match:
                continue

            # Determine the key for the dictionary: template_name or file_name
            # without extension
            if match.group("template_name"):
                key = match.group("template_name")
            else:
                key = os.path.splitext(file_name)[0]

            matching_files[key] = {
                "file_path": self.fix_path(file_path),
                "regex_dict": match.groupdict()
            }

        return matching_files

    def find_template(self, project_data):
        """
        Tries to find a project template in the configuration.

        The name of the template must have the following structure:
        Template_{project code}.aep

        and must be located in:
        {config}/tk-shotgun-executeaction/afx/templates/{project code}/

        {project code} is a special token that must match with the code
        of the current project

        if the template has been found, it returns the full path,
        otherwise it returns None
        """

        project_code = project_data.get("code")

        hooks_location = os.path.dirname(os.path.dirname(self.disk_location))
        self.parent.engine.logger.info("hooks_location: {}".format(hooks_location))

        templates_location = os.path.join(
            hooks_location,
            "tk-shotgun-executeaction",
            "afx",
            "templates",
            project_code,
        )
        self.parent.engine.logger.info(
            "templates_location: {}".format(templates_location)
        )

        if not templates_location:
            msg = "Couldn't get the templates location"
            self.parent.engine.logger.error(msg)
            self.show_message(msg, icon="Critical")
            return None

        pattern = (
            r"(?P<head>Template_)"
            r"(?P<project>[a-zA-Z]+)"
            r"_?"
            r"(?P<template_name>.+)?"
            r"(?P<extension>\.[aepx]+)"
        )

        templates_dict = self.get_matching_files(templates_location, pattern)

        # Return None if no template was found for the current project
        if not templates_dict:
            msg = "Couldn't find any template for project {}".format(project_code)
            self.parent.engine.logger.error(msg)
            return None

        return templates_dict

    def copy_template_to_user_area(self, template_path):
        """
        Copies the template located in the config to another location, in the
        user area of the current project, under a folder called as the current
        SG user
        """

        result = None

        # get afx base template from SG's templates file. We are using a custom
        # template: "aftereffects_shot_template_user"
        user_template = self.parent.get_setting("user_template")
        # currEngine = sgtk.platform.current_engine()
        # tk = currEngine.tank
        # mayaWorkTempl = tk.templates['maya_shot_scene_work']
        afx_user_template = self.parent.engine.tank.templates[user_template]
        user_entity = self.parent.shotgun.find_one(
            "HumanUser",
            [["id", "is", self.parent.context.user.get("id")]],
            ["login"]
        )
        current_user_name = user_entity.get("login")

        fields = {
            "current_user_name": current_user_name
        }

        tmp_template_path = afx_user_template.apply_fields(fields)
        self.parent.engine.logger.info("tmp_template_path: {}".format(tmp_template_path))

        # make sure the folders structure exists
        if not os.path.exists(os.path.dirname(tmp_template_path)):
            os.makedirs(os.path.dirname(tmp_template_path))

        # make sure the file itself doesn't exist. If it does, try to delete it
        if os.path.exists(tmp_template_path):
            try:
                os.remove(tmp_template_path)
                self.parent.engine.logger.info(
                    "Existing file '{}' has been removed".format(
                        tmp_template_path
                    )
                )

            except:
                self.parent.engine.logger.info(
                    "Couldn't remove existing file '{}'".format(
                        tmp_template_path
                    )
                )
                return result

        # try to copy the file to the tmp path
        try:
            shutil.copy2(template_path, tmp_template_path)
            self.parent.engine.logger.info(
                "Copied template file to '{}'".format(
                    tmp_template_path
                )
            )
            return tmp_template_path
        except:
            self.parent.engine.logger.info(
                "Couldn't copy template file to '{}'".format(
                    tmp_template_path
                )
            )
            return result

    def open_template_project(self, template_path):
        """Opens the project template"""

        adobe = self.parent.engine.adobe

        # afx_project = adobe.app.open(adobe.File(template_path))
        afx_project = adobe.app.open(adobe.File(template_path))

        return afx_project

    def set_colorspace(self, working_colorspace):

        adobe = self.parent.engine.adobe

        if working_colorspace:
            colorspace_mappings = {
                "sRGB": "sRGB IEC61966-2.1",
                "rec709 2.4": "Rec.709 Gamma 2.4",
                "rec709": "HDTV (Rec. 709)",
            }

            if working_colorspace in colorspace_mappings.keys():
                afx_colorspace = colorspace_mappings[working_colorspace]

                adobe.app.project.workingSpace = afx_colorspace
                self.parent.engine.logger.info(
                    "Set working colorspace to {}".format(afx_colorspace)
                )
            else:
                self.parent.engine.logger.info(
                    "Couldn't find an afx colorspace mapping for colorspace '{}'".format(
                        working_colorspace
                    )
                )

    def set_color_depth(self):
        """
        Sets the project color depth per channel. Valid choices are:
            8
            16
            32
        """

        adobe = self.parent.engine.adobe

        # Read color_depth value from tk-aftereffects.yml
        color_depth = self.parent.get_setting("color_depth")
        adobe.app.project.bitsPerChannel = color_depth

    def set_time_display_type(self):
        """
        Sets display time to frames instead of using the default: timecode.

        There are two different choices:
            2012 = Timecode
            2013 = Frames
        """

        adobe = self.parent.engine.adobe

        adobe.app.project.timeDisplayType = 2013

    def collect_all_compositions_in_project(self):
        compositions = []

        adobe = self.parent.engine.adobe

        project_items = adobe.app.project.items

        for i, item in enumerate(self.parent.engine.iter_collection(project_items)):
            if item.typeName == "Composition":
                if item not in compositions:
                    compositions.append(item)

        return compositions

    def filter_output_compositions(self, list_of_comps):
        """
        Filters a list of comps to get only comps which name start with 'Output'
        It is NOT case sensitive so either 'Output' or 'output' should work.
        """

        output_comps = []

        for comp in list_of_comps:
            original_comp_name = comp.name
            match_pattern = re.compile(r"(?P<output_tag>^[Oo]utput)")
            match = re.match(match_pattern, original_comp_name)
            if match:
                if comp not in output_comps:
                    output_comps.append(comp)


        return output_comps

    def set_composition_settings(
        self,
        comp,
        frame_rate,
        shot_first_frame,
        shot_duration,
        resolution,
    ):

        adobe = self.parent.engine.adobe

        if frame_rate:
            comp.frameRate = frame_rate

        if frame_rate and shot_first_frame and shot_duration:
            comp_start_in_secs = float(shot_first_frame) / float(frame_rate)
            comp.displayStartFrame = shot_first_frame
            comp.workAreaStart = comp_start_in_secs

            comp_duration = float(shot_duration) / float(frame_rate)
            comp.duration = comp_duration
            comp.workAreaDuration = comp_duration

        if resolution:
            pattern = re.compile(r"(?P<width>\d+)x(?P<height>\d+)")
            match = re.match(pattern, str(resolution))
            if match:
                width = int(match.groupdict()["width"])
                height = int(match.groupdict()["height"])

                comp.width = width
                comp.height = height

    def collect_all_render_items_in_render_queue(self):
        render_queue_items = []

        adobe = self.parent.engine.adobe

        for i, queue_item in enumerate(
            self.parent.engine.iter_collection(adobe.app.project.renderQueue.items)
        ):
            render_queue_items.append(queue_item)

        return render_queue_items

    def remove_items(self, list_of_items):
        for item in list_of_items:
            item.remove()

    def show_message(self, msg, icon=None):
        """
        Shows a message box with the input message
        """

        from sgtk.platform.qt import QtCore, QtGui

        icons_dict = {
            "NoIcon": QtGui.QMessageBox.NoIcon,
            "Question": QtGui.QMessageBox.Question,
            "Information": QtGui.QMessageBox.Information,
            "Warning": QtGui.QMessageBox.Warning,
            "Critical": QtGui.QMessageBox.Critical,
        }

        if not icon or icon not in icons_dict.keys():
            icon_selection = icons_dict["NoIcon"]
        else:
            icon_selection = icons_dict[icon]

        msg_error = QtGui.QMessageBox()
        msg_error.setWindowTitle("Finished execution.")
        msg_error.setText(msg)
        msg_error.setIcon(icon_selection)
        msg_error.exec_()

    def fix_path(self, path):
        """Replace all backward slashes with forward slashes."""

        path = path.replace("\\\\", "/")
        path = path.replace("\\", "/")

        return path

    def open_template_selection_dialog(self, templates_dict):
        # Create the dialog
        dialog = QtGui.QDialog()
        dialog.setWindowTitle("Select Template")
        dialog.setModal(True)  # Make dialog modal

        # Set up the main layout
        layout = QtGui.QVBoxLayout(dialog)

        # Group box with label
        group_box = QtGui.QGroupBox("Please choose a template")
        group_layout = QtGui.QVBoxLayout(group_box)
        layout.addWidget(group_box)

        # Dropdown menu
        dropdown = QtGui.QComboBox()
        dropdown.addItems(templates_dict.keys())  # Populate with dictionary keys
        group_layout.addWidget(dropdown)

        # Buttons for Select and Cancel
        button_layout = QtGui.QHBoxLayout()
        select_button = QtGui.QPushButton("Select")
        cancel_button = QtGui.QPushButton("Cancel")
        button_layout.addWidget(select_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)

        # Define a container for the selected template
        selected_template = {"path": None}

        # Define button actions
        def on_select():
            selected_key = dropdown.currentText()
            selected_template["path"] = templates_dict[selected_key]["file_path"]
            dialog.accept()  # Close dialog and return accepted state

        select_button.clicked.connect(on_select)
        cancel_button.clicked.connect(dialog.reject)

        # Execute the dialog
        result = dialog.exec_()

        # Return the selected template path or None if canceled
        return selected_template["path"] if result == QtGui.QDialog.Accepted else None
