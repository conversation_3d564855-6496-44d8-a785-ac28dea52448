#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

import os
import json
import pprint
import traceback

import sgtk
import bpy

from tank import Hook
from tank import TankError

pp = pprint.pprint
pf = pprint.pformat

class ProcessItemsHook(Hook):
    """
    Process items to load it into the scene
    """

    def load_item(self, item, update_progress):
        """
        The load item method is executed once for each defined item and its purpose is
        to process the loading of the item into the scene.

        The structure of each item is a dictionary with three keys:

        - "node": The name of the 'node' that is to be operated on. Most DCCs have
            a concept of a node, path or some other way to address a particular
            object in the scene.
        - "type": The object type that this is. This is to
            knows how to handle the object.
        - "path": Path on disk to the object to be loaded.
        """

        if item['type'] == 'Audio file':
            update_progress({'progress': 30, 'message': 'Loading audio...'})

            try:
                self.parent.logger.info("audio item:\n{}".format(pf(item)))
                file_path = item["path"]
                sg_data = item.get("other_params", {})

                # Use the actions hook from the loader to load the audio
                hook_expression = self.parent.get_setting('actions_hook')
                actions_method = self.parent.get_setting('actions_method', 'execute_action')

                # Load the item into the scene using the loader's action
                self.parent.execute_hook_expression(
                    hook_expression,
                    actions_method,
                    name='add_audio',
                    params=None,
                    sg_publish_data=sg_data,
                    path=file_path
                )

                self.parent.logger.info("Audio loaded successfully using loader actions")

            except Exception as e:
                self.parent.logger.error("Failed to load audio: {}".format(e))
                self.parent.logger.error(
                    "Full traceback:\n{}".format(traceback.format_exc())
                )
                raise