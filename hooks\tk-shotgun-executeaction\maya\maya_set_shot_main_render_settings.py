########################################################################################
#
# Copyright (c) 2024 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
########################################################################################

"""
An app that reads the shot resolution from SG and sets it in the scene render settings

"""

import sys
import os
import time

from tank import Hook
import os
from maya import cmds
import sgtk

class ProcessItemsHook(Hook):

    def execute(self, entity_type, entities, other_params, **kwargs):

        context = self.parent.context
        current_entity = context.entity

        if current_entity.get("type") != "Shot":
            msg = "Current entity is not a shot: {}, skipping-".format(
                current_entity.get("type")
            )
            self.parent.engine.logger.warning(msg)
            return {'succes': [], 'messages': [msg], 'errors': [1]}

        resolution_width, resolution_height = self.get_shot_resolution_from_sg()
        if not resolution_width or not resolution_height:
            msg = "Couldn't get shot resolution: width: {}, height: {}".format(
                resolution_width, resolution_height
            )
            self.parent.engine.logger.warning(msg)
            return {'succes': [], 'messages': [msg], 'errors': [1]}

        shot_info = self.get_shot_info_from_sg()
        if not shot_info:
            msg = "Couldn't get shot info"
            self.parent.engine.logger.warning(msg)
            return {'succes': [], 'messages': [msg], 'errors': [1]}

        # TODO: implement overrides for file_name_prefix and pixelAspect
        file_name_prefix = "<scene>/<renderLayer>/<aov>/<renderLayer>"
        animation_toggle = True
        sg_cut_in = shot_info.get("sg_cut_in")
        sg_cut_out = shot_info.get("sg_cut_out")
        frame_interval = shot_info.get("sg_animation_exposition")
        if not frame_interval:
            frame_interval = 1
        frame_padding = 4
        device_aspect = float(resolution_width / resolution_height)
        pixel_aspect = 1

        if not all(
            [
                file_name_prefix,
                animation_toggle,
                sg_cut_in,
                sg_cut_out,
                frame_interval,
                frame_padding,
            ]
        ):
            msg = "Couldn't get shot info"
            self.parent.engine.logger.warning(msg)
            return {'succes': [], 'messages': [msg], 'errors': [1]}

        # debug logs
        logger = self.parent.engine.logger
        logger.info("file_name_prefix: {}".format(file_name_prefix))
        logger.info("animation_toggle: {}".format(animation_toggle))
        logger.info("sg_cut_in: {}".format(sg_cut_in))
        logger.info("sg_cut_out: {}".format(sg_cut_out))
        logger.info("frame_interval: {}".format(frame_interval))
        logger.info("frame_padding: {}".format(frame_padding))
        logger.info("device_aspect: {}".format(device_aspect))
        logger.info("pixel_aspect: {}".format(pixel_aspect))
        logger.info("resolution_width: {}".format(resolution_width))
        logger.info("resolution_height: {}".format(resolution_height))

        # Set the render settings
        cmds.setAttr(
            "defaultRenderGlobals.imageFilePrefix",
            file_name_prefix,
            type="string"
        )
        # 1 for True, 0 for False
        cmds.setAttr("defaultRenderGlobals.animation", int(animation_toggle))
        cmds.setAttr("defaultRenderGlobals.startFrame", sg_cut_in)
        cmds.setAttr("defaultRenderGlobals.endFrame", sg_cut_out)
        cmds.setAttr("defaultRenderGlobals.byFrameStep", frame_interval)
        cmds.setAttr("defaultRenderGlobals.extensionPadding", frame_padding)

        # Set resolution
        cmds.setAttr("defaultResolution.width", resolution_width)
        cmds.setAttr("defaultResolution.height", resolution_height)
        cmds.setAttr("defaultResolution.deviceAspectRatio", device_aspect)

        # finally try to set the pixel aspect. It's needed to do it at the end otherwise
        # maya won't set this value correctly
        time.sleep(2)
        cmds.setAttr("defaultResolution.pixelAspect", pixel_aspect)


        msg = "All render settings have been set correctly"
        logger.info(msg)

        # ensure images folder exists for the render outputs
        try:
            self.create_images_folder()
        except Exception as e:
            msg = "Couldn't create images folder: {}".format(e)
            logger.error(msg)

        return {'succes': [1], 'messages': [msg], 'errors': []}


    def get_shot_resolution_from_sg(self):
        hook_expression = "{config}/get_entity_resolution.py"
        entity_resolution = self.parent.engine.execute_hook_expression(
            hook_expression,
            "get_resolution",
            engine=self.parent.engine,
        )
        if entity_resolution:
            resolution_width = int(entity_resolution.split("x")[0])
            resolution_height = int(entity_resolution.split("x")[1])
        else:
            # Fallback to HD resolution
            resolution_width = None
            resolution_height = None

        return resolution_width, resolution_height

    def get_shot_info_from_sg(self):

        context = self.parent.context
        current_entity = context.entity
        sg_proj = context.project
        shotgun = self.parent.engine.shotgun

        if not current_entity.get("type") == "Shot":
            msg = "Current entity is not a shot: {}, skipping-".format(
                current_entity.get("type")
            )
            self.parent.engine.logger.warning(msg)
            return None

        filters = [
            ["project", "is", sg_proj],
            ["id", "is", current_entity["id"]],
        ]
        fields = ["sg_cut_in", "sg_cut_out", "sg_animation_exposition", "code"]
        shot_info = shotgun.find_one("Shot", filters, fields)

        return shot_info


    def create_images_folder(self):
        # Get the current scene file path
        scene_path = cmds.file(query=True, sceneName=True)

        if scene_path:
            scene_dir = os.path.dirname(scene_path)
            images_dir = os.path.join(scene_dir, "images")
            images_dir = self.fix_path(images_dir)

            # Create the "images" folder if it doesn't exist
            if not os.path.exists(images_dir):
                os.makedirs(images_dir)
                self.parent.engine.logger.info(f"Created folder: {images_dir}")
            else:
                self.parent.engine.logger.info(f"Folder already exists: {images_dir}")
        else:
            self.parent.engine.logger.info(
                "Scene is not saved. Cannot determine location for 'images' folder."
            )

    def fix_path(self, path):
        path = path.replace("\\\\", "/")
        path = path.replace("\\", "/")

        return path
