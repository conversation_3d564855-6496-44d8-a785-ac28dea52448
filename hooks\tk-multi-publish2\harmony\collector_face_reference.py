#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

import os
import sys
import shutil

import sgtk

HookBaseClass = sgtk.get_hook_baseclass()

class HarmonyFaceReferenceCollector(HookBaseClass):
    """
    Collector that operates on the Toon Boom Harmony session. Should inherit 
    from the basic collector hook.
    """

    @property
    def settings(self):
        """
        Dictionary defining the settings that this collector expects to receive
        through the settings parameter in the process_current_session and
        process_file methods.
        A dictionary on the following form::
            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }
        The type string should be one of the data types that toolkit accepts as
        part of its environment configuration.
        """

        # grab any base class settings
        collector_settings = super(HarmonyFaceReferenceCollector, self).settings or {}

        # settings specific to this collector
        harmony_session_settings = {
            "Publish Template": {
                "type": "template",
                "default": None,
                "description": "Publish path template"
            },
            "Work Face Ref Template": {
                "type": "template",
                "default": None,
                "description": "Template path where the files are located"
            },
            "Publish Face Ref Seq Template": {
                "type": "template",
                "default": None,
                "description": "Template path where the files will be published"
            },
            "Publish Face Video Seq Template": {
                "type": "template",
                "default": None,
                "description": "Template path where the video will be published"
            }}

        # update the base settings with these settings
        collector_settings.update(harmony_session_settings)

        return collector_settings

    def process_current_session(self, settings, parent_item):
        """
        Analyzes the current session open in Toon Boom Harmony and parents a 
        subtree of items under the parent_item passed in.
        :param dict settings: Configured settings for this collector
        :param parent_item: Root item instance
        """
        
        super(HarmonyFaceReferenceCollector, self).process_current_session(settings, parent_item)

        item = next((i for i in parent_item.descendants if i.type_spec.endswith('.session')), None)
        if item is None:
            item = self.collect_current_harmony_session(settings, parent_item)


        face_refs = self._collect_face_refs(settings, item)
        #return item

    def _collect_face_refs(self, settings, parent_item):

        self.logger.debug("Collecting face references...")

        # get fileseq in a partially generic way, untill we have mty-framework-externalpython
        # we know this hook files lives in {config}/tk-multi-publish2/harmony, so we will place fileseq there
        config_path = os.path.dirname(os.path.dirname(self.disk_location))

        engine           = sgtk.platform.current_engine()
        path             = engine.app.get_current_project_path()
        publisher        = self.parent
        template_by_name = engine.get_template_by_name

        # get templates from settings
        work_template_setting    = settings.get("Work Template").value
        work_face_tpl_setting    = settings.get("Work Face Ref Template").value
        publish_face_seq_setting = settings.get("Publish Face Ref Seq Template").value
        publish_face_vid_setting = settings.get("Publish Face Video Seq Template").value
        publish_session_setting  = settings.get("Publish Template").value

        # Templates
        work_template             = template_by_name(work_template_setting)
        work_face_template        = template_by_name(work_face_tpl_setting)
        publish_session_template  = template_by_name(publish_session_setting)
        publish_face_seq_template = template_by_name(publish_face_seq_setting)
        publish_face_vid_template = template_by_name(publish_face_vid_setting)
        
        #Adding extra element in work_fields
        work_fields                = work_template.get_fields(path)
        work_fields['shape.frame'] = 1
        work_face_path             = work_face_template.apply_fields(work_fields)


        container_folder = os.path.dirname(work_face_path)
        base_name        = os.path.basename(work_face_path)
        base_parts       = base_name.split('.')
        ext              = base_parts[-1]
        name             = base_parts[0]
        name_prefix      = name[:-3]
        nums             = []

        if not os.path.exists(container_folder):
            return
        for f in os.listdir(container_folder):
           if name_prefix in f:
               num = f[len(name_prefix):].split('.')[0]
               if num.isdigit():
                   nums.append(int(num))

        if not len(nums):
            return

        display_name = 'Face reference {start}-{end} .png'.format(
            start=str(min(nums)).zfill(3), end=str(max(nums)).zfill(3))
        
        session_item = parent_item.create_item(
                                    "harmony.face_references",
                                    "Face references", display_name)

        publish_icons = os.path.join(config_path, 'tk-multi-publish2', 'icons')
        icon_path     = os.path.join(publish_icons, "video.png")
        session_item.set_icon_from_path(icon_path)

        session_item.properties["face_reference_nums"]      = nums
        session_item.properties["work_fields"]              = work_fields
        session_item.properties["template_faceRef_work"]    = work_face_template
        session_item.properties["template_session_publish"] = publish_session_template

        # Sub template for every plugin item
        session_item.properties["template_faceRef_video_publish"] = publish_face_vid_template
        session_item.properties["template_faceRef_seq_publish"]   = publish_face_seq_template


        return [session_item]
