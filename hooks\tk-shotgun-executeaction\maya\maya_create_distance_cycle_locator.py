#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

"""
An app that creates an animated locator based on SG speed values. It uses custom
fields to retrieve the data: sg_mty_floatvalue1 = walk, sg_mty_floatvalue2 = run
"""

from tank import Hook
from tank import TankError
from tank.platform import Application
from tank.platform.qt import QtCore, QtGui
import tank
import sgtk

import os
import re
import pprint

import maya.cmds as cmds


engine = sgtk.platform.current_engine()
context = engine.context
shotgun = engine.shotgun
project = engine.context.project
entity = engine.context.entity


# ------------------------------------------------------------------------------


class ProcessItemsHook(Hook):


    def execute(self, **kwargs):

        self.window_UI()
        return {'succes': [1], 'messages': [], 'errors': []}


    # --------------------------------------------------------------------------


    def get_fps_from_sg(self):
        result = None

        filters = [["id", "is", project["id"]]]
        fields = ["sg_fps"]
        data = shotgun.find_one("Project", filters, fields)

        if data:
            result = data.get("sg_fps")

        # fallback
        if not result:
            result = 25.0

        return result


    def get_asset_data(self):
        """get character data as a dict"""

        asset_data = {}

        selected_obj = cmds.ls(selection=True, long=True)
        if not selected_obj:
            return asset_data
        else:
            selected_obj = selected_obj[0]

        if cmds.referenceQuery(selected_obj, isNodeReferenced=True):
            # Get root node
            root_node = cmds.referenceQuery(selected_obj, nodes=True, dagPath=True)[0]

            # Get namespace ----------------------------------------------------------------
            namespace = cmds.referenceQuery(root_node, namespace=True)
            if not namespace[0].isalnum():
                namespace = namespace[1:]

            # Get global scale llocator ----------------------------------------------------
            supported_names = ["LOC_Global_Scale", "Global_Scale_LOC"]
            global_scale_loc = None
            xform_nodes = cmds.listRelatives(
                root_node, allDescendents=True, type="transform", fullPath=True
            )
            for xform_node in xform_nodes:
                for name in supported_names:
                    if name in xform_node:
                        global_scale_loc = xform_node
                        break

            # Get referenced path ----------------------------------------------------------
            file_path = cmds.referenceQuery(selected_obj, filename=True)
            if file_path.endswith("}"):
                matched = re.match(r"(.*)(\{\d+\})", file_path)
                if matched:
                    file_path = matched.groups()[0]

            # Get referenced path ----------------------------------------------------------
            fps = self.get_fps_from_sg()

            # Reformat file_path to match path_cache
            path_cache = os.path.splitdrive(file_path)[-1]
            if not path_cache[0].isalnum():
                path_cache = path_cache[1:]

            # Fill dict data ---------------------------------------------------------------
            asset_data["root_node"] = root_node
            asset_data["namespace"] = namespace
            asset_data["global_scale_loc"] = global_scale_loc
            asset_data["file_path"] = file_path
            asset_data["fps"] = fps
            asset_data["path_cache"] = path_cache

            # Get asset name from SG -------------------------------------------------------
            filters = [["project", "is", project], ["path_cache", "is", path_cache]]
            fields = ["entity"]

            sg_data = shotgun.find_one("PublishedFile", filters, fields)
            # print("sg_data from get_asset_data:\n{}".format(pprint.pformat(sg_data)))

            if not sg_data:
                return None
            else:
                asset_data["asset_name"] = sg_data.get("entity", {}).get("name")

        return asset_data


    def none_asset_error(self, cycle_type, asset_data):

        asset_namespace = asset_data.get("namespace")
        message = ("Can't create a distance locator for asset {}. Most likely this asset "
                   "doesn't have a {} speed set in SG").format(asset_namespace, cycle_type)
        cmds.warning(message)

        cmds.confirmDialog(
            title="Can't create distance locator.",
            message=message,
            icon="warning"
        )


    def get_distances_from_sg(self, asset_data):

        walk_value = None
        run_value = None

        if asset_data:
            asset_name = asset_data.get("asset_name")

            filters = [["project", "is", project], ["code", "is", asset_name]]
            fields = ["code", "sg_mty_floatvalue1", "sg_mty_floatvalue2"]

            sg_data = shotgun.find_one("Asset", filters, fields)

            if sg_data:
                walk_value = sg_data.get("sg_mty_floatvalue1")
                run_value = sg_data.get("sg_mty_floatvalue2")

        return walk_value, run_value


    def make_master_grp(self):

        result = None
        name_ctrl = "distance_ctrls_grp"

        if cmds.objExists(name_ctrl):
            return name_ctrl
        else:
            result = cmds.group(empty=True, name=name_ctrl, world=True)

        return result


    def cycle_distance(self, cycle_type):

        asset_data = self.get_asset_data()
        # print("asset_data from cycle_distance:\n{}".format(pprint.pformat(asset_data)))

        if not asset_data:
            self.none_asset_error(cycle_type, asset_data)
        else:
            walk_dist, run_dist = self.get_distances_from_sg(asset_data)
            asset_data["walk_dist"] = walk_dist
            asset_data["run_dist"] = run_dist

            self.make_ctrls(asset_data, cycle_type)


    def make_ctrls(self, asset_data, cycle_type):

        asset_root_node = asset_data.get("root_node")
        asset_namespace = asset_data.get("namespace")
        asset_global_scale_loc = asset_data.get("global_scale_loc")
        asset_file_path = asset_data.get("file_path")
        fps = asset_data.get("fps")

        if cycle_type == "walk":
            cycle_distance = asset_data.get("walk_dist")
        elif cycle_type == "run":
            cycle_distance = asset_data.get("run_dist")
        else:
            cycle_distance = None

        current_check = cmds.checkBoxGrp("current", query=True, value1=True)
        asset_cycle_ctrl = "distance_ctrl_{}_{}".format(asset_namespace, cycle_type)
        asset_cycle_loc = "distance_locator_{}_{}".format(asset_namespace, cycle_type)

        if not cycle_type:
            self.none_asset_error(cycle_type, asset_data)
        elif asset_namespace and fps and cycle_distance:
            try:
                if current_check == True:
                    cycle_start_frame = cmds.currentTime(query=True)
                else:
                    cycle_start_frame = cmds.playbackOptions(query=True, animationStartTime=True)

                # If object already exists, delete it so it can be created from scratch ----
                if cmds.objExists(asset_cycle_ctrl):
                    cmds.delete(asset_cycle_ctrl)
                if cmds.objExists(asset_cycle_loc):
                    cmds.delete(asset_cycle_loc)

                # Create ctrls main group, main controller and cycle locator ---------------
                ctrls_main_grp = self.make_master_grp()

                asset_cycle_ctrl = cmds.circle(
                    name=asset_cycle_ctrl, normal=(0, 1, 0), center=(0, 0, 0), radius=7
                )[0]
                asset_cycle_loc = cmds.spaceLocator(name=asset_cycle_loc)[0]

                cmds.setAttr("{}Shape.overrideEnabled".format(asset_cycle_ctrl), 1)
                cmds.setAttr("{}Shape.overrideColor".format(asset_cycle_ctrl), 9)
                cmds.setAttr("{}Shape.overrideEnabled".format(asset_cycle_loc), 1)
                cmds.setAttr("{}Shape.overrideColor".format(asset_cycle_loc), 17)

                cmds.parent(asset_cycle_loc, asset_cycle_ctrl)
                cmds.parent(asset_cycle_ctrl, ctrls_main_grp)

                # Set keyframes on cycle locator -------------------------------------------
                cmds.setKeyframe(
                    asset_cycle_loc,
                    outTangentType="linear",
                    inTangentType="linear",
                    value=0,
                    attribute="translateZ",
                    breakdown=0,
                    preserveCurveShape=0,
                    time=cycle_start_frame,
                )

                cmds.setKeyframe(
                    asset_cycle_loc,
                    outTangentType="linear",
                    inTangentType="linear",
                    attribute="translateZ",
                    value=cycle_distance,
                    breakdown=0,
                    preserveCurveShape=0,
                    time=cycle_start_frame + fps,
                )

                # Set animation postinfinity to relative cycle -----------------------------
                cmds.setInfinity(
                    asset_cycle_loc, attribute="translateZ", postInfinite="cycleRelative"
                )

                # Match transformations between new cycle controller and asset globale LOC -
                if asset_global_scale_loc:
                    self.match_asset_transforms(asset_global_scale_loc, asset_cycle_ctrl)
                # cmds.select(characterobjs)

                # Show confirmation dialog -------------------------------------------------
                message = "Created {} distance locator for asset {}".format(
                    cycle_type, asset_namespace
                )
                print(message)
                cmds.confirmDialog(title="Done", message=message, icon="information")
            except:
                message = ("There was an error while trying to create {} "
                           "distance locator for asset {}").format(
                    cycle_type, asset_namespace
                )
                print(message)
                cmds.confirmDialog(title="Error", message=message, icon="warning")
        else:
            try:
                self.none_asset_error(cycle_type, asset_data)
            except:
                message = ("Can't create a distance "
                           "locator for asset {}").format(asset_namespace)
                print(message)
                cmds.confirmDialog(title="Error", message=message, icon="warning")


    def match_asset_transforms(self, asset_global_scale_loc, asset_cycle_ctrl):

        matrix_locator = cmds.xform(
            asset_global_scale_loc, query=True, matrix=True, worldSpace=True
        )
        cmds.xform(asset_cycle_ctrl, matrix=matrix_locator, worldSpace=True)


    def window_UI(self):
        """Create the window for artist"""

        window_name_short = "cycle_distance"
        window_name_long = "Select cycle distance"
        window_height = 100
        window_width = 300

        # check if window exist
        if cmds.window(window_name_short, exists=True):
            cmds.deleteUI(window_name_short)

        main_window = cmds.window(
            window_name_short,
            title=window_name_long,
            height=window_height,
            width=window_width,
        )

        cmds.window(main_window, edit=True, width=window_width, height=window_height)

        cmds.showWindow(main_window)

        # Layout main window
        cmds.columnLayout("MainLayout", adjustableColumn=True)

        cmds.separator(height=10)
        cmds.text(label="Select cycle distance", align="center", font="fixedWidthFont")

        cmds.separator(height=15, width=window_width, style="double")
        # cmds.text(label="Options time", align="center", font="fixedWidthFont")
        cmds.checkBoxGrp(
            "current", numberOfCheckBoxes=1, label="Create at Current Time?", value1=True
        )

        cmds.separator(height=15, width=window_width, style="double")
        cmds.button(label="Walk", command=lambda x: self.cycle_distance("walk"))
        cmds.button(label="Run", command=lambda x: self.cycle_distance("run"))

        cmds.button(
            label="Close",
            command=('cmds.deleteUI("{}", window=True)'.format(main_window)),
        )

        return main_window


