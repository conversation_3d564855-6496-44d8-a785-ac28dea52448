#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

"""
An app that syncs the frame range between a scene and a shot in Shotgun.
Plus adding and modifiying the scene Render Settings

"""

import sys
import os

from tank import Hook
from tank import TankError
from tank.platform import Application
from tank.platform.qt import QtCore, QtGui
import tank

import hou
import re


class ProcessItemsHook(Hook):
    def execute(self, entity_type, entities, other_params, **kwargs):
        # Main function to create a flipbook

        self.parent.engine.logger.info("Creating ropNode...")
        rop_node = self.get_rop_node()

        self.parent.engine.logger.info("Collecting camera...")
        shot_cam_node = self.get_shot_cam()
        if not shot_cam_node:
            msg = "Couldn't get shot camera node."
            self.show_message(msg, icon="Critical")
            return {"succes": [], "messages": [msg], "errors": [1]}

        self.parent.engine.logger.info("Setting rope parameters...")
        # Set ROP parameters
        self.set_rop_params(rop_node, shot_cam_node)

        self.parent.engine.logger.info("Rendering Flipbook...")
        self.render_flipbook(rop_node)

        return {"succes": [1], "messages": [], "errors": []}

    def convert_path_relative_to_job(self, path):
        """Converts incoming path to relative to $JOB"""

        job = hou.getenv("JOB")

        if path:
            if job[-1].isalnum():
                path = path.replace(job, "$JOB")
            else:
                path = path.replace(job[:-1], "$JOB")

            return path

    def fix_path(self, path):
        """removes backslashes and replace them with forwardd slashes"""

        path = path.replace("\\", "/")
        path = path.replace("\\\\", "/")
        path = path.replace("//", "/")
        path = path.replace("%04d", "$F4")

        return path

    def get_shot_name(self, path):
        """Get the shot name fro SG"""

        try:
            context = self.parent.engine.context
            shot_name = context.entity.get("name")
            self.parent.engine.logger.info("shot_name from sgtk: {}".format(shot_name))
        except Exception:
            # TODO: modify the pattern so it's much more generic
            pattern = re.compile(r"(\e{1}\d{3}\_\d{3}\_\d{4})")
            match_pattern = re.search(pattern, path)
            if match_pattern is not None:
                shot_name = match_pattern.groups()[0]
                self.parent.engine.logger.info(
                    "shot_name from regex: {}".format(shot_name)
                )
            else:
                shot_name = "GenericShot"
                self.parent.engine.logger.info(
                    "shot_name from fallback: {}".format(shot_name)
                )

        return shot_name

    def get_color_space(self):
        """Gets the project color space from SG's sg_output_color_space"""

        color_space_mappings = {"sRGB": "out_srgb", "rec709": "out_rec709"}

        try:
            engine = self.parent.engine
            data = engine.shotgun.find_one(
                entity_type="Project",
                filters=[["id", "is", engine.context.project.get("id")]],
                fields=["sg_output_color_space"],
            )

            sg_colorspace = data.get("sg_output_color_space")
            project_colorspace = color_space_mappings.get(sg_colorspace)

            if not project_colorspace:
                project_colorspace = "out_srgb"
                self.parent.engine.logger.info(
                    "Couldn't get ColorSpace from SG. Falling back to: sRGB (out_srgb)"
                )
            else:
                self.parent.engine.logger.info(
                    "ColorSpace from SG: {} ({})".format(
                        sg_colorspace, project_colorspace
                    )
                )
        except Exception:
            project_colorspace = "out_srgb"
            self.parent.engine.logger.info(
                "Couldn't get ColorSpace from SG. Falling back to: sRGB (out_srgb)"
            )

        return project_colorspace

    def get_shot_frame_range(self):
        engine = self.parent.engine
        try:
            data = engine.shotgun.find_one(
                entity_type="Shot",
                filters=[["id", "is", engine.context.entity["id"]]],
                fields=["sg_cut_in", "sg_cut_out"],
            )

            shot_frame_range = (int(data.get("sg_cut_in")), int(data.get("sg_cut_out")))

            self.parent.engine.logger.info(
                "shot_frame_range from sgtk: {}".format(shot_frame_range)
            )

            return shot_frame_range

        except Exception:
            start_frame = ""
            end_frame = ""
            message = "Input start and end frames for this shot"
            input_labels = ["start frame", "end frame"]
            title = "Shot Frame Range"

            # persistent loop until the user defines a frame range. The range itself
            # is not validated, but its contents: must be integers
            is_digit = False
            while is_digit is False:
                multi_result = hou.ui.readMultiInput(
                    message=message, input_labels=input_labels, title=title
                )
                start_frame = multi_result[-1][0]
                end_frame = multi_result[-1][1]
                if start_frame.isdigit():
                    if end_frame.isdigit():
                        is_digit = True

            shot_frame_range = (int(start_frame), int(end_frame))
            self.parent.engine.logger.info(
                "shot_frame_range from user: {}".format(shot_frame_range)
            )

            return shot_frame_range

    def get_node_instances(self, category, node_type):
        """Find all instances of the specified node type. Returns a list.

        category must be one of the following:

        hou.chopNetNodeTypeCategory()
        hou.chopNodeTypeCategory()
        hou.cop2NetNodeTypeCategory()
        hou.cop2NodeTypeCategory()
        hou.dopNodeTypeCategory()
        hou.lopNodeTypeCategory()
        hou.managerNodeTypeCategory()
        hou.nodeTypeCategories()
        hou.objNodeTypeCategory()
        hou.rootNodeTypeCategory()
        hou.ropNodeTypeCategory()
        hou.shopNodeTypeCategory()
        hou.sopNodeTypeCategory()
        hou.topNodeTypeCategory()
        hou.vopNetNodeTypeCategory()
        hou.vopNodeTypeCategory()


        node_type must be a string describing the node type (node.type().name())
        """

        nodeType = hou.nodeType(category, node_type)
        nodeInstances = nodeType.instances()

        return nodeInstances

    def get_rop_node(self):
        """Get the openGL rop node, or create a new one if it doesn't exist"""

        out_context = hou.node("/out")
        opengl_node_comment = "OpenGL ROP for Flipbook SG Version. DON'T MODIFY"

        # get all opengl rop instances
        opengl_node_instances = self.get_node_instances(
            hou.ropNodeTypeCategory(), "opengl"
        )

        # find the (first) node with the "opengl_node_comment"
        rop_node = None
        if len(opengl_node_instances) > 0:
            for rop in opengl_node_instances:
                if rop.comment() == opengl_node_comment:
                    rop_node = rop
                    break

        # if there's no rop node with the comment, then create one
        if rop_node is None:
            rop_node = out_context.createNode("opengl")
            rop_node.setName("SG_OpenGL_ROP", unique_name=True)
            rop_node.setComment(opengl_node_comment)
            rop_node.moveToGoodPosition()

        # colorize the node
        rop_node.setColor(hou.Color(0.8, 0.15, 0.15))

        return rop_node

    def get_shot_cam(self):
        """Find all cameras in scene and ask user to select the right one for the
        flipbook"""

        shot_cam_node = None
        cam_node_instances = list(
            self.get_node_instances(hou.objNodeTypeCategory(), "cam")
        )

        named_cams_list = []
        cams_dict = {}
        max_str = ""

        # if there's only one cam in the scene, return that cam
        if len(cam_node_instances) == 1:
            shot_cam_node = cam_node_instances[0]

        # if there's more cameras in the scene, ask the user to select the right
        # cam. The dialog box default to the one with the longest name
        elif len(cam_node_instances) > 0:
            for i, cam in enumerate(cam_node_instances):
                named_cams_list.append(cam.name())
                cams_dict[cam.name()] = {"cam_node": cam, "index": i}
                if len(cam.name()) > len(max_str):
                    max_str = cam.name()
            default_choice = cams_dict.get(max_str).get("index")

            selection = hou.ui.selectFromList(
                named_cams_list,
                default_choices=(default_choice,),
                exclusive=True,
                message="Select shot camera:",
                title=None,
                column_header="Cameras",
                num_visible_rows=10,
                clear_on_cancel=False,
                width=0,
                height=0,
            )

            if len(selection) == 1:
                shot_cam = named_cams_list[selection[0]]
                shot_cam_node = cams_dict.get(shot_cam).get("cam_node")

        return shot_cam_node

    def get_output_path(self):
        """Build output path"""

        current_path = hou.hipFile.path()  # .replace('.hipnc', '.hip')
        get_template_func = self.parent.engine.get_template_by_name

        work_template_name = self.parent.get_setting("scene_work_template")

        scene_work_template = get_template_func(work_template_name)
        template_fields = scene_work_template.get_fields(current_path)

        review_template_name = self.parent.get_setting("review_work_template")
        review_template = get_template_func(review_template_name)

        # use image format file extension from settings
        review_image_format = self.parent.get_setting("review_image_format")
        template_fields["extension"] = review_image_format

        render_path = review_template.apply_fields(template_fields)

        self.parent.engine.logger.info("Template Preview Path: {0}".format(render_path))

        output_path = self.fix_path(render_path)

        self.parent.engine.logger.info("Actual Preview Path: {0}".format(output_path))

        return output_path

    def set_rop_params(self, rop_node, cam_node):
        """Set the Rop values"""

        # get scene directory to use it as fallback if the function fails to get it
        # from SG
        path = hou.expandString("$HIP")
        ocio = hou.expandString("$OCIO")

        shot_frame_range = self.get_shot_frame_range()

        # Shot range
        start_frame = shot_frame_range[0]
        end_frame = shot_frame_range[1]

        # Render path
        output_path = self.get_output_path()

        width = self.parent.get_setting("width")
        height = self.parent.get_setting("height")

        project_colorspace = self.get_color_space()

        rop_params = {
            "trange": 1,
            "f1": start_frame,
            "f2": end_frame,
            "f3": 1,
            "camera": cam_node.path(),
            "tres": True,
            "res1": width,
            "res2": height,
            "picture": output_path,
            "saveretry": 1,
            "gamma": 2.2,
            "vm_image_jpeg_quality": 80,
            "vm_image_exr_compression": 2,
            "volumequality": 2,
        }
        rop_node.setParms(rop_params)

        if ocio != "":
            rop_node.setParms({"colorcorrect": 2, "ociocolorspace": project_colorspace})

        self.parent.engine.logger.info("")
        self.parent.engine.logger.info("start_frame: {}".format(start_frame))
        self.parent.engine.logger.info("end_frame: {}".format(end_frame))
        self.parent.engine.logger.info("camera: {}".format(cam_node.name()))
        self.parent.engine.logger.info("resolution: {}x{}".format(1920, 1080))
        self.parent.engine.logger.info("output_path: {}".format(output_path))
        self.parent.engine.logger.info("gamma: {}".format(2.2))

    def render_flipbook(self, rop_node):
        """Executes the rop"""

        render_button = rop_node.parm("execute")
        render_button.pressButton()

    def show_message(self, msg, icon=None):
        """
        Shows a message box with the input message
        """

        icons_dict = {
            "NoIcon": QtGui.QMessageBox.NoIcon,
            "Question": QtGui.QMessageBox.Question,
            "Information": QtGui.QMessageBox.Information,
            "Warning": QtGui.QMessageBox.Warning,
            "Critical": QtGui.QMessageBox.Critical,
        }

        if not icon or icon not in icons_dict.keys():
            icon_selection = icons_dict["NoIcon"]
        else:
            icon_selection = icons_dict[icon]

        msg_error = QtGui.QMessageBox()
        msg_error.setWindowTitle("Finished execution.")
        msg_error.setText(msg)
        msg_error.setIcon(icon_selection)
        msg_error.raise_()
        msg_error.activateWindow()
        msg_error.exec_()
