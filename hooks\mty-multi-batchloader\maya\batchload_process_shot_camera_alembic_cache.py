########################################################################################
#
# Copyright (c) 2024 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
########################################################################################


import os
import json
import pprint
import traceback

import sgtk

from tank import Hook
from tank import TankError
from tank.platform.qt import QtCore, QtGui

import maya.cmds as cmds
import maya.mel as mel
import pymel.core as pm


class ProcessItemsHook(Hook):
    """
    Process items to load them into the scene
    """

    def load_item(self, item, update_progress):
        """
        The load item method is executed once for each defined item and its purpose is
        to process the loading of the item into the scene.

        The structure of each item is a dictionary with three keys:

        - "node": The name of the 'node' that is to be operated on. Most DCCs have
            a concept of a node, path, or some other way to address a particular
            object in the scene.
        - "type": The object type that this is. This is to
            know how to handle the object.
        - "path": Path on disk to the object to be loaded.
        """


        CoreMayaTools = self.load_framework("mty-framework-coremayatools")
        MayaMiscUtils = CoreMayaTools.MayaMiscUtils

        if item["type"] == "Shot Camera Alembic Cache":
            update_progress(
                {"progress": 30, "message": "Referencing Shot Camera Alembic Cache..."}
            )

            camera_alembic_cache_path = item["path"]
            camera_alembic_cache_path = self.fix_path(camera_alembic_cache_path)
            namespace = item.get("other_params", {}).get("namespace", "")

            self.parent.logger.info(
                "Loading shot camera alembic cache: {}".format(
                    camera_alembic_cache_path
                )
            )

            # reference the camera alembic file
            try:
                cmds.file(
                    camera_alembic_cache_path,
                    reference=True,
                    loadReferenceDepth="all",
                    mergeNamespacesOnClash=False,
                    namespace=namespace,
                )
            except Exception as e:
                msg = (
                    "Error loading shot camera alembic cache: {}\n{}".format(
                        camera_alembic_cache_path, e
                    )
                )
                self.parent.logger.error(msg)
                self.parent.logger.error(traceback.format_exc())
                self.show_message(msg, "Critical")
                return

            self.parent.logger.info(
                "Successfully imported shot camera alembic cache: {}".format(
                    camera_alembic_cache_path
                )
            )

    def fix_path(self, path):
        """Replace all backward slashes with forward slashes."""

        path = path.replace("\\\\", "/")
        path = path.replace("\\", "/")

        return path

    def show_message(self, msg, icon=None):
        """
        Displays a message box with a specified message and icon.

        This method creates a QMessageBox to display a message to the user.
        The appearance of the message box can be customized using the 'icon' parameter.

        Args:
            msg (str): The message to be displayed in the message box.
            icon (str, optional): The icon to be displayed in the message box.
                Valid options are "NoIcon", "Question", "Information", "Warning", and "Critical".
                Defaults to "NoIcon" if not provided or invalid.
        """

        icons_dict = {
            "NoIcon": QtGui.QMessageBox.NoIcon,
            "Question": QtGui.QMessageBox.Question,
            "Information": QtGui.QMessageBox.Information,
            "Warning": QtGui.QMessageBox.Warning,
            "Critical": QtGui.QMessageBox.Critical,
        }

        if not icon or icon not in icons_dict.keys():
            icon_selection = icons_dict["NoIcon"]
        else:
            icon_selection = icons_dict[icon]

        msg_error = QtGui.QMessageBox()
        msg_error.setWindowTitle("Finished execution.")
        msg_error.setText(msg)
        msg_error.setIcon(icon_selection)
        msg_error.exec_()
