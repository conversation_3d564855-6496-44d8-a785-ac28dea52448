# -*- coding: utf-8 -*-
# Standard library:
import mimetypes
import os
#   .   .   .   .   .   .   .   .   .   .   .
# Third party:
import sgtk
from tank_vendor import six
import pymel.core as pm
#   .   .   .   .   .   .   .   .   .   .   .
# Project:
# ================================================================


HookBaseClass = sgtk.get_hook_baseclass()


class AttrsCollector(HookBaseClass):

    def __init__(self, parent):
        super(AttrsCollector, self).__init__(parent)
        context = self.parent.context
        entity = "Asset"
        filters = [["id", "is", context.entity["id"]]]
        fields = ["sg_asset_type"]
        asset_data = \
            self.parent.shotgun.find_one(entity, filters, fields)

        self.ASSET = asset_data

    # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

    def process_current_session(
        self, settings, parent_item

    ):
        # let parent collector do it own work
        super(AttrsCollector, self).process_current_session(settings, parent_item)
        
        if not parent_item.type_spec.endswith(".session"):
            item = next(
                (i for i in parent_item.descendants if i.type_spec.endswith(".session")),
                parent_item,
            )
        else:
            item = parent_item


        self.collect_controllers(item)

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def collect_controllers(self, parent_item):

        step_name = self.parent.context.step['name'].lower()

        if step_name == "model":
            return

        icon_path = os.path.join(
            self.disk_location,
            os.pardir,
            os.pardir,
            "icons",
            "mesh.png"
        )

        if self.ASSET["sg_asset_type"] != "Camera":

            # collect for rig ROM alembic
            rom_item = parent_item.create_item(
                "maya.anim.roms",
                "Animation Data",
                "ROMS"
            )

            self.parent.engine.execute_hook_expression(
                "{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py",
                "use_farm_or_local_processing",
                item = rom_item
            )

            rom_item.set_icon_from_path(icon_path)
