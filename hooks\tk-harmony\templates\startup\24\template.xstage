<?xml version="1.0" encoding="UTF-8"?>
<project source="Harmony Premium (HarmonyPremium.exe) version 21.1.1 build 18815 2022-09-18 10:48:34" version="2111" build="18815" creator="harmony">
 <elements>
  <element id="2" elementName="mighty" elementFolder="mighty" pixmapFormat="4" scanType="2" fieldChart="12" vectorType="0" rootFolder="elements">
   <drawings>
    <dwg name="1"/>
   </drawings>
  </element>
 </elements>
 <options>
  <metrics unitAspectRatioX="4" unitAspectRatioY="3" numberOfUnitsX="24" numberOfUnitsY="24" numberOfUnitsZ="12"/>
  <resolution name="HDTV_1080p24" size="1920,1080" fovFit="VerticalFitFov" fov="41.112090439166927" projection="PerspectiveProjection"/>
  <framerate val="24"/>
  <zdragging val="true"/>
  <zOrderCompatibilityWith7_3 val="false"/>
  <pixelPerModelUnitForVectorLayers val="0.28800000000000009"/>
  <pixelPerModelUnitForBitmapLayers val="0.28800000000000009"/>
  <canvasForBitmapLayers size="3840,2160"/>
  <cameraInSymbols val="true"/>
  <scaleFactor val="1"/>
  <convertPalettesColorSpace val="false"/>
 </options>
 <timelineMarkers/>
 <scenes>
  <scene name="Top" id="09e1298841d01049" nbframes="60" startFrame="1" stopFrame="60">
   <columns>
    <column type="0" name="ATV-0BDC80B5FBE01717" color="#FFFFFFFF" displayOrder="0" width="100" anonymous="true" id="2">
     <elementSeq exposures="1-60" val="1" id="2"/>
    </column>
   </columns>
   <options>
    <defaultDisplay val="Display"/>
   </options>
   <rootgroup name="Top">
    <options>
     <collapsed val="false"/>
    </options>
    <nodeslist>
     <module type="DISPLAY" name="Display" pos="77,39,2" publishUnderTab="Display">
      <options>
       <collapsed val="false"/>
       <version val="1"/>
      </options>
     </module>
     <module type="READ" name="mighty" pos="59,-82,4" publishUnderTab="mighty">
      <options>
       <collapsed val="false"/>
       <version val="1"/>
      </options>
      <attrs>
       <enable3d val="false"/>
       <faceCamera val="false"/>
       <cameraAlignment val="NO_CAMERA_ALIGNMENT"/>
       <offset>
        <separate val="true"/>
        <x val="0" defaultValue="0"/>
        <y val="0" defaultValue="0"/>
        <z val="0" defaultValue="0"/>
       </offset>
       <scale>
        <separate val="true"/>
        <inFields val="false"/>
        <xy val="1" defaultValue="1"/>
        <x val="1" defaultValue="1"/>
        <y val="1" defaultValue="1"/>
        <z val="1" defaultValue="1"/>
       </scale>
       <rotation>
        <separate val="false"/>
        <anglex val="0" defaultValue="0"/>
        <angley val="0" defaultValue="0"/>
        <anglez val="0" defaultValue="0"/>
       </rotation>
       <angle val="0" defaultValue="0"/>
       <skew val="0" defaultValue="0"/>
       <pivot>
        <x val="0" defaultValue="0"/>
        <y val="0" defaultValue="0"/>
        <z val="0" defaultValue="0"/>
       </pivot>
       <splineOffset>
        <x val="0" defaultValue="0"/>
        <y val="0" defaultValue="0"/>
        <z val="0" defaultValue="0"/>
       </splineOffset>
       <ignoreParentPegScaling val="false"/>
       <disableFieldRendering val="false"/>
       <depth val="0"/>
       <enableMinMaxAngle val="false"/>
       <minAngle val="-360" defaultValue="-360"/>
       <maxAngle val="360" defaultValue="360"/>
       <nailForChildren val="false"/>
       <ikHoldOrientation val="false"/>
       <ikHoldX val="false"/>
       <ikHoldY val="false"/>
       <ikExcluded val="false"/>
       <ikCanRotate val="true"/>
       <ikCanTranslateX val="false"/>
       <ikCanTranslateY val="false"/>
       <ikBoneX val="0.20000000000000001" defaultValue="0.20000000000000001"/>
       <ikBoneY val="0" defaultValue="0"/>
       <ikStiffness val="1" defaultValue="1"/>
       <drawing>
        <elementMode val="true"/>
        <element col="ATV-0BDC80B5FBE01717">
         <layer/>
        </element>
        <customName>
         <name/>
         <extension val="tga"/>
         <fieldChart val="12" defaultValue="12"/>
        </customName>
       </drawing>
       <readOverlay val="true"/>
       <readLineArt val="true"/>
       <readColorArt val="true"/>
       <readUnderlay val="true"/>
       <overlayArtDrawingMode val="VectorDrawingMode"/>
       <lineArtDrawingMode val="VectorDrawingMode"/>
       <colorArtDrawingMode val="VectorDrawingMode"/>
       <underlayArtDrawingMode val="VectorDrawingMode"/>
       <pencilLineDeformationPreserveThickness val="false"/>
       <pencilLineDeformationQuality val="Low"/>
       <pencilLineDeformationSmooth val="1"/>
       <pencilLineDeformationFitError val="3" defaultValue="3"/>
       <readColor val="true"/>
       <readTransparency val="true"/>
       <colorTransformation val="Linear"/>
       <colorSpace val="sRGB"/>
       <applyMatteToColor val="W"/>
       <enableLineTexture val="true"/>
       <antialiasingQuality val="HIGH"/>
       <antialiasingExponent val="1" defaultValue="1"/>
       <opacity val="100" defaultValue="100"/>
       <textureFilter val="NEAREST_FILTERED"/>
       <adjustPencilThickness val="false"/>
       <normalLineArtThickness val="true"/>
       <zoomIndependentLineArtThickness val="zoomIndependent"/>
       <multLineArtThickness val="1" defaultValue="1"/>
       <addLineArtThickness val="0" defaultValue="0"/>
       <minLineArtThickness val="0" defaultValue="0"/>
       <maxLineArtThickness val="0" defaultValue="0"/>
       <useDrawingPivot val="APPLY_ON_READ_TRANSFORM"/>
       <flipHor val="false"/>
       <flipVert val="false"/>
       <turnBeforeAlignment val="false"/>
       <noClipping val="false"/>
       <xClipFactor val="0"/>
       <yClipFactor val="0"/>
       <alignmentRule val="ASIS"/>
       <morphingVelo val="0" defaultValue="0"/>
       <canAnimate val="true"/>
       <tileHorizontal val="false"/>
       <tileVertical val="false"/>
      </attrs>
     </module>
     <module type="COMPOSITE" name="Composite" pos="-12,-18,6" publishUnderTab="Composite">
      <options>
       <collapsed val="false"/>
       <version val="1"/>
      </options>
      <attrs>
       <compositeMode val="compositeBitmap"/>
       <flattenOutput val="true"/>
       <flattenVector val="false"/>
       <composite2d val="false"/>
       <composite3d val="false"/>
       <outputZ val="LEFTMOST"/>
       <outputZInputPort val="1"/>
       <applyFocus val="true"/>
       <multiplier val="1" defaultValue="1"/>
       <tvgPalette val="compositedPalette"/>
       <mergeVector val="false"/>
      </attrs>
     </module>
     <module type="WRITE" name="Write_final" pos="-103,110,7" publishUnderTab="Write_final">
      <options>
       <color val="#FA5005FF"/>
       <collapsed val="false"/>
       <version val="1"/>
      </options>
      <metas>
       <meta keyword="mtyWriteExport" displayName="mtyWriteExport" flags="15" type="BOOL" linkable="false"/>
      </metas>
      <attrs>
       <exportToMovie val="false"/>
       <drawingName val="frames/final."/>
       <moviePath val="frames/output"/>
       <movieFormat val="com.toonboom.quicktime.legacy"/>
       <movieAudio/>
       <movieVideo/>
       <movieVideoaudio/>
       <leadingZeros val="3"/>
       <start val="1"/>
       <drawingType val="PNG"/>
       <enabling>
        <filter val="ALWAYS"/>
        <filterName/>
        <filterResX val="720"/>
        <filterResY val="540"/>
       </enabling>
       <scriptMovie val="false"/>
       <scriptEditor>
        <val>
         <![CDATA[// Following code will be called at the end of Write Node rendering
// operations to create a movie file from rendered images.

// ...]]>
        </val>
       </scriptEditor>
       <colorSpace/>
       <compositePartitioning val="NoCompositePartitioning"/>
       <zPartitionRange val="1" defaultValue="1"/>
       <cleanUpPartitionFolders val="true"/>
       <mtywriteexport val="true"/>
      </attrs>
     </module>
     <module type="COMPOSITE" name="final" pos="-87,39,1" publishUnderTab="final">
      <options>
       <color val="#05FA05FF"/>
       <collapsed val="false"/>
       <version val="1"/>
      </options>
      <metas>
       <meta keyword="mtyPassthrough" displayName="mtyPassthrough" flags="15" type="BOOL" linkable="false"/>
      </metas>
      <attrs>
       <compositeMode val="compositeBitmap"/>
       <flattenOutput val="true"/>
       <flattenVector val="false"/>
       <composite2d val="false"/>
       <composite3d val="false"/>
       <outputZ val="LEFTMOST"/>
       <outputZInputPort val="1"/>
       <applyFocus val="true"/>
       <multiplier val="1" defaultValue="1"/>
       <tvgPalette val="compositedPalette"/>
       <mergeVector val="false"/>
       <mtypassthrough val="true"/>
      </attrs>
     </module>
     <module type="BurnIn" name="Scene" pos="-84,-83,3" publishUnderTab="Burn-In">
      <options>
       <collapsed val="false"/>
       <version val="1"/>
      </options>
      <attrs>
       <drawbackgroundbox val="true"/>
       <textcolor>
        <red val="255"/>
        <green val="255"/>
        <blue val="255"/>
        <alpha val="255"/>
        <preferredUi val="separate"/>
       </textcolor>
       <backgroundcolor>
        <red val="0"/>
        <green val="0"/>
        <blue val="0"/>
        <alpha val="128"/>
        <preferredUi val="separate"/>
       </backgroundcolor>
       <size val="36"/>
       <frameoffset val="0"/>
       <drawframenumber val="false"/>
       <drawtimecode val="false"/>
       <printinfo val="Scene:"/>
       <alignment val="Left"/>
       <font val="arial"/>
      </attrs>
     </module>
     <module type="PEG" name="Scene-P" pos="-83,-135,5" publishUnderTab="Scene-P">
      <options>
       <collapsed val="false"/>
       <version val="1"/>
      </options>
      <attrs>
       <enable3d val="false"/>
       <faceCamera val="false"/>
       <cameraAlignment val="NO_CAMERA_ALIGNMENT"/>
       <position>
        <separate val="false"/>
        <x val="0" defaultValue="0"/>
        <y val="0" defaultValue="0"/>
        <z val="0" defaultValue="0"/>
       </position>
       <scale>
        <separate val="true"/>
        <inFields val="false"/>
        <xy val="1" defaultValue="1"/>
        <x val="1" defaultValue="1"/>
        <y val="1" defaultValue="1"/>
        <z val="1" defaultValue="1"/>
       </scale>
       <rotation>
        <separate val="false"/>
        <anglex val="0" defaultValue="0"/>
        <angley val="0" defaultValue="0"/>
        <anglez val="0" defaultValue="0"/>
       </rotation>
       <angle val="0" defaultValue="0"/>
       <skew val="0" defaultValue="0"/>
       <pivot>
        <x val="0" defaultValue="0"/>
        <y val="0" defaultValue="0"/>
        <z val="0" defaultValue="0"/>
       </pivot>
       <splineOffset>
        <x val="0" defaultValue="0"/>
        <y val="0" defaultValue="0"/>
        <z val="0" defaultValue="0"/>
       </splineOffset>
       <ignoreParentPegScaling val="false"/>
       <disableFieldRendering val="false"/>
       <depth val="0"/>
       <enableMinMaxAngle val="false"/>
       <minAngle val="-360" defaultValue="-360"/>
       <maxAngle val="360" defaultValue="360"/>
       <nailForChildren val="false"/>
       <ikHoldOrientation val="false"/>
       <ikHoldX val="false"/>
       <ikHoldY val="false"/>
       <ikExcluded val="false"/>
       <ikCanRotate val="true"/>
       <ikCanTranslateX val="false"/>
       <ikCanTranslateY val="false"/>
       <ikBoneX val="0.20000000000000001" defaultValue="0.20000000000000001"/>
       <ikBoneY val="0" defaultValue="0"/>
       <ikStiffness val="1" defaultValue="1"/>
       <groupAtNetworkBuilding val="false"/>
       <addCompositeToGroup val="true"/>
      </attrs>
     </module>
    </nodeslist>
    <linkedlist>
     <link out="mighty" in="Composite" inport="0"/>
     <link out="Composite" in="final"/>
     <link out="Composite" in="Display"/>
     <link out="final" in="Write_final"/>
     <link out="Scene" in="Composite" inport="1"/>
     <link out="Scene-P" in="Scene"/>
    </linkedlist>
   </rootgroup>
   <unconnectedComposite>
    <module type="COMPOSITE" name="Unconnected_Composite" pos="0,0,0" publishUnderTab="Unconnected_Composite">
     <options>
      <collapsed val="false"/>
      <version val="1"/>
     </options>
     <attrs>
      <compositeMode val="compositeBitmap"/>
      <flattenOutput val="true"/>
      <flattenVector val="false"/>
      <composite2d val="false"/>
      <composite3d val="false"/>
      <outputZ val="LEFTMOST"/>
      <outputZInputPort val="1"/>
      <applyFocus val="true"/>
      <multiplier val="1" defaultValue="1"/>
      <tvgPalette val="compositedPalette"/>
      <mergeVector val="false"/>
     </attrs>
    </module>
   </unconnectedComposite>
   <metas>
    <meta type="guideList" name="guideList" creator="Harmony Premium" version="1.0">
     <guideList selectedGuide="-1"/>
    </meta>
    <meta type="alignmentGuideList" name="alignmentGuideList" creator="Harmony Premium" version="1.0">
     <alignmentGuideList selectedGuide="-1"/>
    </meta>
   </metas>
   <timelineOrderer/>
  </scene>
 </scenes>
 <symbols>
  <folder name="Symbols">
   <scene id="09e1298841d01049"/>
  </folder>
 </symbols>
 <timeline>
  <scene id="09e1298841d01049"/>
 </timeline>
</project>
