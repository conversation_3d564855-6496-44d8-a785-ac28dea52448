# Copyright (c) 2016 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

################################################################################

includes:
- ./frameworks.yml

################################################################################
# location descriptors for apps used in this configuration

# ---- Mighty Meta Pipeline Apps

apps.mty-multi-scriptstoolbox.location:
  type: github_release
  organization: mightyanimation
  repository: mty-multi-scriptstoolbox
  version: v0.3.0
  private: true
  # type: dev
  # path: Q:\mty-multi-scriptsToolBox

apps.mty-publisharchiver.location:
  type: github_release
  organization: mightyanimation
  repository: mty-publisharchiver
  version: v0.2.1-3
  private: true
  # type: dev
  # path: Q:\mty-publisharchiver

apps.mty-multi-queue.location:
  type: github_release
  organization: mightyanimation
  repository: mty-multi-queue
  version: v0.2.2
  private: true
  # type: dev
  # path: Q:\mty-multi-queue

apps.mty-multi-deliveries.location:
  type: github_release
  organization: mightyanimation
  repository: mty-multi-deliveries
  version: v0.0.0-6
  private: true
  # type: dev
  # path: Q:\mty-multi-deliveries
  # path: D:\Development\Mighty\apps\mty-multi-deliveries

apps.mty-maya-layouttoolkit.location:
  type: github_release
  organization: mightyanimation
  repository: mty-maya-layouttoolkit
  version: v1.2.0-1
  private: true
  # type: dev
  # path: Q:\mty-maya-layouttoolkit

apps.mty-multi-noteimporter.location:
  type: github_release
  organization: mightyanimation
  repository: mty-multi-noteimporter
  version: v0.0.1-9
  private: true
  # type: dev
  # path: Q:\mty-multi-noteimporter
  # path: D:\Development\Mighty\apps\mty-multi-noteimporter

# apps.mty-maya-layoutsequencer.location:
#  type: github_release
#  organization: mightyanimation
#  repository: mty-maya-layoutsequencer
#  version: vx.x.x-x
#  private: true
#  # type: dev
#  # path: P:\development\mty-maya-layoutsequencer

apps.mty-multi-batchloader.location:
  type: github_release
  organization: mightyanimation
  repository: mty-multi-batchloader
  version: v0.0.5-2
  private: true
  # type: dev
  # path: Q:\mty-multi-batchloader

apps.mty-maya-playblast.location:
  type: github_release
  organization: mightyanimation
  repository: mty-maya-playblast
  version: v1.3.1-1
  private: true
  # type: dev
  # path: Q:\mty-maya-playblast

# ---- Multi apps

# about
apps.tk-multi-about.location:
  type: app_store
  name: tk-multi-about
  version: v0.5.2

# breakdown
apps.tk-multi-breakdown.location:
  type: app_store
  name: tk-multi-breakdown
  version: v1.9.1

# breakdown2
apps.tk-multi-breakdown2.location:
  type: app_store
  name: tk-multi-breakdown2
  version: v0.4.3

  # type: github_release
  # organization: mightyanimation
  # repository: tk-multi-breakdown2
  # version: v0.2.9-01
  # private: true

  # type: dev
  # path: Q:\tk-multi-breakdown2

# demo
apps.tk-multi-demo.location:
  type: app_store
  name: tk-multi-demo
  version: v1.5.2

# launchapp
apps.tk-multi-launchapp.location:
  type: app_store
  name: tk-multi-launchapp
  version: v0.13.3

# loader2
apps.tk-multi-loader2.location:
  type: app_store
  name: tk-multi-loader2
  version: v1.25.2

# publish2
apps.tk-multi-publish2.location:
  type: app_store
  name: tk-multi-publish2
  version: v2.10.3

# pythonconsole
apps.tk-multi-pythonconsole.location:
  type: app_store
  name: tk-multi-pythonconsole
  version: v1.4.3

# reviewsubmission
apps.tk-multi-reviewsubmission.location:
  type: app_store
  name: tk-multi-reviewsubmission
  version: v1.3.1

# screeningroom
apps.tk-multi-screeningroom.location:
  type: app_store
  name: tk-multi-screeningroom
  version: v0.6.2

# setframerange
apps.tk-multi-setframerange.location:
  type: app_store
  name: tk-multi-setframerange
  version: v0.6.2

# shotgun panel
apps.tk-multi-shotgunpanel.location:
  type: app_store
  name: tk-multi-shotgunpanel
  version: v1.9.2

# snapshot
apps.tk-multi-snapshot.location:
  type: app_store
  name: tk-multi-snapshot
  version: v0.10.1

# workfiles (required for Mari)
apps.tk-multi-workfiles.location:
  type: app_store
  name: tk-multi-workfiles
  version: v0.7.4

# # workfiles2
# apps.tk-multi-workfiles2.location:
#   type: app_store
#   name: tk-multi-workfiles2
#   version: v0.15.5
#   # type: dev
#   # path: Q:\tk-multi-workfiles2

# workfiles2 fork
apps.tk-multi-workfiles2.location:
  type: github_release
  organization: mightyanimation
  repository: tk-multi-workfiles2
  version: v0.15.6
  private: true
  # type: dev
  # path: Q:\tk-multi-workfiles2


# dev utils
apps.tk-multi-devutils.location:
  type: app_store
  name: tk-multi-devutils
  version: v1.3.1

# import cut
apps.tk-multi-importcut.location:
  name: tk-multi-importcut
  type: app_store
  version: v2.5.1

# ---- Shotgun apps

# shotgun folders
apps.tk-shotgun-folders.location:
  type: app_store
  name: tk-shotgun-folders
  version: v0.4.0

# shotgun launchfolder
apps.tk-shotgun-launchfolder.location:
  type: app_store
  name: tk-shotgun-launchfolder
  version: v0.4.0

# shotgun launchpublish
apps.tk-shotgun-launchpublish.location:
  type: app_store
  name: tk-shotgun-launchpublish
  version: v0.7.1

# ---- Hiero apps

# hiero export
apps.tk-hiero-export.location:
  type: app_store
  name: tk-hiero-export
  version: v0.8.1

# hiero openinshotgun
apps.tk-hiero-openinshotgun.location:
  type: app_store
  name: tk-hiero-openinshotgun
  version: v0.5.0

# ---- Houdini apps

# houdini alembicnode
apps.tk-houdini-alembicnode.location:
  type: app_store
  name: tk-houdini-alembicnode
  version: v0.6.0

# houdini mantranode
apps.tk-houdini-mantranode.location:
  type: app_store
  name: tk-houdini-mantranode
  version: v0.6.0

# ---- Nuke apps

# quickreview
apps.tk-nuke-quickreview.location:
  type: app_store
  name: tk-nuke-quickreview
  version: v1.3.2

# writenode
apps.tk-nuke-writenode.location:
  name: tk-nuke-writenode
  type: app_store
  version: v1.7.2

# ---- Flame apps

# flame export
apps.tk-flame-export.location:
  name: tk-flame-export
  type: app_store
  version: v1.11.3

# flame review
apps.tk-flame-review.location:
  name: tk-flame-review
  type: app_store
  version: v1.5.4

# ---- Mari apps

# project manager
apps.tk-mari-projectmanager.location:
  name: tk-mari-projectmanager
  type: app_store
  version: v1.4.1

# ---- Fusion apps
apps.mty-fusion-autoloaders.location:
  type: github_release
  organization: mightyanimation
  repository: mty-fusion-autoloaders
  version: v0.0.2-0
  private: true
  # type: dev
  # path: C:\Dev\Fidel\apps\mty-fusion-autoloaders

# ---- Community apps

# sb-shotgun-schema-introspection
apps.sb-shotgun-schema-introspection.location:
  type: git
  path: https://github.com/scottb08/sb-shotgun-schema-introspection.git
  version: v1.0.3

# ---- Animation Apps
#apps.mty-app-anitools.location:
#  type: github_release
#  organization: mightyanimation
#  repository: mty-app-anitools
#  version: v0.1.0-3
#  private: true
#  #type: dev
#  #path: C:\Users\<USER>\Documents\DEV\MightyTools\mty-app-anitools

# ---- ExecuteAction Apps
apps.tk-shotgun-executeaction.location:
  type: git
  path: https://github.com/hasielhassan/tk-shotgun-executeaction.git
  version: v0.0.1-4
  #path: D:\Development\HasielHassan\tk-shotgun-executeaction
  #type: dev

# ---- Scene Setup
apps.mty-multi-scenesetup.location:
  type: github_release
  organization: mightyanimation
  repository: mty-multi-scenesetup
  version: v0.2.2
  private: true
  # type: dev
  # path: Q:\mty-multi-scenesetup

#---- Write Node
apps.mty-multi-write-node.location:
  type: github_release
  organization: mightyanimation
  repository: mty-multi-write-node
  version: v0.0.1-0
  private: true
  # type: dev
  # path: C:\Dev\Julio\mty-multi-write-node

# ----- Harmony Apps
# apps.mty-harmony-assettemplate.location:
#   type: github_release
#   organization: mightyanimation
#   repository: mty-harmony-assettemplate
#   version: v0.1.0-3
#   private: true
#   # type: dev
#   # path: C:\Dev\Julio\mty-harmony-assettemplate

apps.mty-harmony-library.location:
  type: github_release
  organization: mightyanimation
  repository: mty-harmony-library
  version: v0.2.2-2
  private: true
  # type: dev
  # path: Q:\mty-harmony-library

apps.mty-harmony-video-importer.location:
  type: github_release
  organization: mightyanimation
  repository: mty-harmony-video-importer
  version: v0.1.0-1
  private: true
  # type: dev
  # path: C:\Dev\Julio\Apps_dev\mty-harmony-video-importer

################################################################################
# reference all of the common frameworks
frameworks: '@frameworks'
