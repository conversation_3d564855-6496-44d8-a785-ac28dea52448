########################################################################################
#
# Copyright (c) 2024 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
########################################################################################

import os
import re
import sys
import json
import pprint
import fileseq
import traceback

import sgtk

import maya.cmds as cmds

pp = pprint.pprint
pf = pprint.pformat

HookBaseClass = sgtk.get_hook_baseclass()

class MayaRenderLayersCollector(HookBaseClass):
    """
    Collector that operates on maya session. Should inherit
    from the basic collector hook.
    """

    @property
    def settings(self):

        # grab any base class settings
        collector_settings = super(MayaRenderLayersCollector, self).settings or {}

        # settings specific to this collector
        maya_session_settings = {
            "Work Template": {
                "type": "template",
                "default": None,
                "description": "Template path for artist work files. Should "
                               "correspond to a template defined in "
                               "templates.yml. If configured, is made available"
                               "to publish plugins via the collected item's "
                               "properties. ",
            },
            # "Work Frames Folder Template": {
            #     "type": "template",
            #     "default": None,
            #     "description": "Template path for artist work frames. Should "
            #                    "correspond to a template defined in "
            #                    "templates.yml. If configured, is made available"
            #                    "to publish plugins via the collected item's "
            #                    "properties. ",
            # },
            "Publish Frames Template": {
                "type": "template",
                "default": None,
                "description": "Template path for artist published frames. Should "
                               "correspond to a template defined in "
                               "templates.yml. If configured, is made available"
                               "to publish plugins via the collected item's "
                               "properties. ",
            },
            "Image Sequence Workflow": {
                "type": "str",
                "default": "individual",
                "description": "Define the workflow to collect image sequences,"
                               " which can be either 'individual' or 'grouped'"
            },
            # "Beauty rendered name": {
            #     "type": "str",
            #     "default": None,
            #     "description": "Name of the base layer to create multi exr"
            # },

            # "Beauty register layer": {
            #     "type": "str",
            #     "default": None,
            #     "description": "Name of the base layer user as layer 0 in multiexr"
            # }
        }

        # update the base settings with these settings
        collector_settings.update(maya_session_settings)

        return collector_settings

    def process_current_session(self, settings, parent_item):
        """
        Analyzes the current session open Maya and parents a
        subtree of items under the parent_item passed in.
        :param dict settings: Configured settings for this collector
        :param parent_item: Root item instance
        """
        super(MayaRenderLayersCollector, self).process_current_session(settings, parent_item)
        # session_item = next((item for item in parent_item.descendants if item.type_spec == 'maya.session'), None)

        self.parent.logger.info("Collecting render layers started... ".ljust(88, "-"))

        # load overrides framework
        overrides_framework = self.load_framework("mty-framework-valueoverrides")
        default_value = "mty.publisher.maya.collect_render_layers"
        link = {"type": "Task", "id": self.parent.context.task["id"]}
        override_render_layers = overrides_framework.get_value(default_value, link=link)

        if override_render_layers:
            override_render_layers = json.loads(override_render_layers)

        # skip collect render layers if task is on override list
        task = self.parent.context.task['name']
        self.parent.logger.debug("task: {}".format(task))
        if task in override_render_layers:
            self._collect_frames(settings, parent_item)
        else:
            self.parent.logger.info(
                "Task is {} and does not collect render layers".format(task)
            )

    # def find_all_sequences_on_disk(self):
    #     images_dir = self.get_scene_images_dir()

    #     all_sequences = {}

    #     for root, _, _ in os.walk(images_dir):
    #         # Use fileseq to find sequences in the current directory
    #         sequences = fileseq.findSequencesOnDisk(root)
    #         if sequences:
    #             all_sequences[root] = sequences

    #     return all_sequences

    def _collect_frames(self, settings, parent_item):

        self.logger.info("Collecting rendered frames...")
        self.parent.logger.info("Collecting rendered frames...")

        # get fileseq in a partially generic way, untill we have mty-framework-externalpython
        # we know this hook files lives in {config}/tk-multi-publish2/harmony, so we will place fileseq there

        config_path = os.path.dirname(os.path.dirname(self.disk_location))
        # python_modules_path = os.path.join(config_path, "external_python_modules")
        # python_future_path  = os.path.join(python_modules_path, "python-future")

        # sys.path.append(python_modules_path)
        # sys.path.append(python_future_path)

        engine = sgtk.platform.current_engine()
        publisher = self.parent
        publish_icons = os.path.join(config_path, 'tk-multi-publish2', 'icons')
        img_icon_path = os.path.join(publish_icons, "image_publish.png")
        seq_icon_path = os.path.join(publish_icons, "sequence.png")

        # get templates from settings
        self.parent.engine.logger.debug(
            "Render layers collector settings:\n{}".format(pf(settings))
        )

        # Collector settings
        work_template_setting = settings.get("Work Template").value
        publish_frames_template_setting = settings.get("Publish Frames Template").value
        image_sequence_workflow = settings.get("Image Sequence Workflow").value

        # Creating templates from settings
        # work_template = publisher.engine.get_template_by_name(work_template_setting)
        publish_frames_template = publisher.engine.get_template_by_name(publish_frames_template_setting)

        ## Workaround for the '_1' added in default beauty pass name
        # Changes Merge aovs state to True, when rendering again _1 disappears
        # https://forums.autodesk.com/t5/arnold-for-3ds-max/mtoa-appending-quot-1-quot-to-filename-when-using-render/td-p/11039597
        # https://forums.autodesk.com/t5/maya-shading-lighting-and/maya-and-arnold-s-frame-numbering-convention-on-the-aov-s-beauty/td-p/10141092

        ## Get all maya info needed
        # # Get the current renderer being used
        # current_renderer = cmds.getAttr("defaultRenderGlobals.currentRenderer")

        scene_path = cmds.file(query=True, sn=True)
        render_settings_node = cmds.ls(type="renderGlobals")[0]

        # # Get the output file name
        # output_file = cmds.getAttr("defaultRenderGlobals.imageFilePrefix")
        # if output_file == None or '<RenderPass>' not in output_file:
        #     # no output name
        #     # maya default is: '<Scene>_<Layer>_<RenderLayer>_<RenderPass>_<Version>.<Frame>.<Extension>'
        #     self.logger.warning("Your 'File name prefix' does not include <RenderPass> token. Please check your file output settings.")

        # Get the start and end frame values
        start_frame = int(cmds.getAttr(render_settings_node + ".startFrame"))
        end_frame = int(cmds.getAttr(render_settings_node + ".endFrame"))

        # Get the current animation extension format
        ext_pad = cmds.getAttr("defaultRenderGlobals.extensionPadding")

        # # Get a list of all render layers in the scene
        # render_layers = cmds.ls(type='renderLayer')
        # # Filter out referenced render layers
        # render_layers = [
        #     layer
        #     for layer in render_layers
        #     if not cmds.referenceQuery(layer, isNodeReferenced=True)
        # ]
        # # Filter render layers that are only connecter to a renderSetupLayer, ignore any
        # # any existing layer that is not visible in the render setup
        # filtered_render_layers = [u"defaultRenderLayer"]
        # for layer in render_layers:
        #     connections = cmds.listConnections(
        #         layer + ".message",
        #         type="renderSetupLayer",
        #         exactType=True,
        #         source=False,
        #         destination=True,
        #         plugs=True
        #     )
        #     if connections:
        #         filtered_render_layers.append(layer)
        # render_layers = filtered_render_layers

        # avos_name_list = []

        # # Get a list of all the arnold aovs
        # all_arnold_aovs = cmds.ls(type='aiAOV')
        # # filter out all referenced aovs
        # arnold_aovs = [
        #     aov
        #     for aov in all_arnold_aovs
        #     # if not cmds.referenceQuery(aov, isNodeReferenced=True)
        # ]

        # # Validate aov names
        # for aov in arnold_aovs:
        #     aov_name = cmds.getAttr(aov + '.name')
        #     avos_name_list.append(aov_name)
        # # Add deafult beauty pass to list
        # avos_name_list.append('beauty')

        # # Get a list of all the redshift aovs
        # all_rs_aovs = cmds.ls(type='RedshiftAOV')
        # # filter out all referenced aovs
        # rs_aovs = [
        #     aov
        #     for aov in all_rs_aovs
        #     # if not cmds.referenceQuery(aov, isNodeReferenced=True)
        # ]
        # # Validate aov names
        # for aov in rs_aovs:
        #     aov_name = cmds.getAttr(aov + '.name')
        #     avos_name_list.append(aov_name)
        # # Add deafult beauty pass to list
        # avos_name_list.append('beauty')

        # if nothing was collected, probably we are using a different renderer

        # self.parent.engine.logger.info(
        #     (
        #         "COLLECTOR RESULTS:\n"
        #         "Current renderer: {0}\n"
        #         "Output file: {1}\n"
        #         "Start frame: {2}\n"
        #         "End frame: {3}\n"
        #         "Frame/Animation Extension Format: {4}\n"
        #         "Render layers: {5}\n"
        #         "AOVs: {6}"
        #     ).format(
        #         current_renderer,
        #         output_file,
        #         start_frame,
        #         end_frame,
        #         ext_pad,
        #         render_layers,
        #         avos_name_list
        #     )
        # )

        # sequences_dict = {}

        # all_sequences = self.find_all_sequences_on_disk()
        # self.parent.logger.info("All sequences on disk:\n{}".format(pf(all_sequences)))

        # # Getting path for iterating each layer
        # for layer in render_layers:
        #     try:
        #         sequences_dict = self.get_render_layers_and_aovs()
        #     except Exception as e:
        #         self.parent.logger.warning(
        #             "Couldn't build sequences dict:\n{}\nFull error:\n{}".format(
        #                 e, traceback.format_exc()
        #             )
        #         )

        render_layers_info = self.get_render_layers_info()
        # self.parent.engine.logger.info("\n{}".format("- " * 40))
        self.parent.engine.logger.debug(f"render_layers_info:\n{pf(render_layers_info)}")
        sequences_dict = self.get_image_sequences(render_layers_info)
        self.parent.engine.logger.debug(f"sequences_dict:\n{pf(sequences_dict)}")

        # return if no sequences were found
        if not sequences_dict:
            return

        # update the field on SG
        layers_names = list(sequences_dict.keys())
        self.update_output_render_in_shot_field(layers_names)

        # # Iterate each layer and filter corresponding sequences according to render settings
        # for key in sequences_dict:
        #     path = sequences_dict[key]['path']
        #     sequences = fileseq.findSequencesOnDisk(path)
        #     self.parent.logger.info(
        #         "render layer '{}' AOV sequences':\n{}".format(
        #             key, pprint.pformat(sequences)
        #         )
        #     )

        #     for aov in avos_name_list:
        #         # Build name for filtering
        #         check_basename = output_file
        #         # resolving for '<RenderLayer>' in name
        #         if '<RenderLayer>' in check_basename:
        #             check_basename = self._get_basename_renderLayer(key, check_basename)

        #         # skip duplicated default render layers that might exist just because a
        #         # scene merge or import, but they are not really connected to the render
        #         # setup
        #         if "defaultRenderLayer" in key and key[-1].isdigit():
        #             continue

        #         sequences_dict[key][aov] = self._get_file_sequence(check_basename, sequences, aov)

        #         # workaround for collecting sequences with suffix '_1' in aov(in beauty pass)
        #         if sequences_dict[key][aov] != None:
        #             continue
        #         sequences_dict[key][aov] = self._get_file_sequence(check_basename, sequences, aov+'_1')

        all_aovs_count = 0
        for layer_name in sequences_dict:
            for aov in sequences_dict[layer_name]["aovs"].keys():
                all_aovs_count += 1

        self.parent.logger.info(
            "Found {} render layers with {} AOVs in total (already rendered)".format(
                len(sequences_dict.keys()), all_aovs_count
            )
        )

        # Info of the publish path render to compare in each iteration
        valid_extentions  = publish_frames_template.keys['extension'].choices
        session_items    = []
        layers_sequences = []

        for render_layer, render_layer_dict in sequences_dict.items():
            render_layer_item = parent_item.create_item(
                "maya.render_layer",
                "Render Layer",
                render_layer
            )

            self.parent.engine.execute_hook_expression(
                "{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py",
                "use_farm_or_local_processing",
                item = render_layer_item
            )

            render_layer_item.properties['render_layer_dict'] = render_layer_dict
            render_layer_item.set_icon_from_path(img_icon_path)
            session_items.append(render_layer_item)

            for aov_name, aov_dict in render_layer_dict.get("aovs", {}).items():
                aov_fileseq_obj = aov_dict.get("fileseq_obj", None)
                aov_seq_path = aov_dict.get("path", None)

                # skip path key and empty sequences from sequences dictionary
                if not aov_seq_path or not aov_fileseq_obj:
                    continue

                # sequence_name = f"{render_layer}.{aov_name}"
                sequence_name = os.path.basename(aov_seq_path)

                display_name = aov_fileseq_obj.format(
                        '{basename}{padding}{extension} ({start}-{end})'
                    )

                # self.parent.logger.debug("display_name: {}".format(display_name))

                # check valid file extensions
                _, sequence_ext = os.path.splitext(sequence_name)
                sequence_ext = sequence_ext.replace('.', '')
                if (
                    sequence_ext.lower() not in valid_extentions
                    and sequence_ext.lower() != "db"
                ):
                    message = f"Ignoring incorrect sequence extension: {display_name}"
                    self.parent.logger.warning(message)
                    self.parent.engine.logger.warning(message)
                    continue

                # Check for missing frames
                all_frames = set(range(start_frame, end_frame + 1))
                existing_frames = set(aov_fileseq_obj.frameSet())
                missing_frames = sorted(all_frames - existing_frames)
                if missing_frames:
                    message = "Missing frames in {}: {}".format(
                        display_name, missing_frames
                    )
                    self.parent.logger.warning(message)
                    self.parent.engine.logger.warning(message)

                # We support two different workflows here:
                #
                # 1- We collect a single main layer item and it
                #    contains all layers grouped in its properties
                #    under the "layers_sequences" key
                #
                # 2- We collect all layers as individual items
                #
                # The main difference is related to how we can publish
                # the image sequences, where in the first case we can
                # do it ideally as a single published file, for example
                # as a single multi layer exr that we can combine in the
                # publish hook, and in a second case, where we are not
                # able to do a single publish, but we are forced to do
                # one publish per image sequence
                #
                # The collection workflow will be defined by a setting
                # which is set in the environment settings:
                # - Image Sequence Workflow
                #   "individual" or "grouped"
                #
                # TODO: Ideally we should control using an SG override
                #       we need to add the mty-framework-valueoverrides
                #       and define a setting for this case and update
                #       the code acoordingly
                #
                # TODO: All this collection logic should also be a separate
                #       hook, something that can be reused by other per dcc
                #       hooks in a hierarchical way so that code is not duplicated

                if image_sequence_workflow == "grouping":
                    raise ValueError('Currently grouping is not supported')

                aov_item = render_layer_item.create_item(
                    "maya.frames_sequence",
                    "Render Frames",
                    display_name
                )

                self.parent.engine.execute_hook_expression(
                    "{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py",
                    "use_farm_or_local_processing",
                    item = aov_item
                )

                for source_frame in list(aov_fileseq_obj):
                    # self.logger.info(source_frame)
                    if not os.path.exists(source_frame):
                        raise Exception('{} does not exist'.format(source_frame))

                # sequence MUST be a fileseq object
                aov_item.properties['sequence'] = aov_fileseq_obj
                # sequence_text_id corresponds to the render layer name
                aov_item.properties['sequence_text_id'] = render_layer
                # sequence_text2_id corresponds to the aov name
                aov_item.properties['sequence_text2_id'] = aov_name
                aov_item.properties['scene_path'] = scene_path
                # # Create 'custom_validations' property
                # # contains custom validation and fix methods to be used as callback
                # aov_item.properties['custom_validations'] = {
                #     'validate_callback': self._validate_name,
                #     'autofix_callback': self._change_sequence_name
                # }
                aov_item.set_icon_from_path(seq_icon_path)
                session_items.append(aov_item)

        # # For the "grouping" workflow, add layers to the item if it exists
        # if image_sequence_workflow == "grouping":
        #     raise ValueError('Currently grouping not supported')

        return session_items

    def fix_path(self, path):
        """
        Replaces all back slashes with forward slashes
        """

        path = path.replace("\\\\", "/")
        path = path.replace("\\", "/")

        return path

    def get_images_dir(self):
        workspace = cmds.workspace(q=True, rd=True)
        images_dir = os.path.join(workspace, "images")

        return images_dir

    def get_all_render_layers(self):
        # Get all render layers
        render_layers = cmds.ls(type="renderLayer")
        self.parent.engine.logger.debug(f"All render layers: {render_layers}")  # Debug print

        return render_layers

    def get_valid_renderable_render_layers(self):
        render_layers = self.get_all_render_layers()
        # Filter enabled render layers (ignore referenced layers)
        valid_render_layers = [
            rl for rl in render_layers
            if cmds.getAttr(f"{rl}.renderable")
            and not cmds.referenceQuery(rl, isNodeReferenced=True)
        ]

        return valid_render_layers

    def cleanup_render_layer_name(self, renderer, render_layer):
        if renderer == "redshift":
            if render_layer.startswith("rs_"):
                render_layer = render_layer.replace("rs_", "")
        elif renderer == "arnold":
            pass
        else:
            pass

        return render_layer

    def get_resolved_render_layer_path(self, file_prefix, scene_name, images_dir, render_layer):
        # self.parent.engine.logger.info("-" * 40)
        # self.parent.engine.logger.info(f"initial prefix: {file_prefix}")
        resolved_path = file_prefix.replace("<scene>", scene_name)
        # self.parent.engine.logger.info(f"resolved_path after <scene> replacement: {resolved_path}")
        resolved_path = resolved_path.replace("<renderLayer>", render_layer)
        # self.parent.engine.logger.info(f"resolved_path after <renderLayer> replacement: {resolved_path}")
        resolved_path = os.path.join(images_dir, resolved_path)
        # self.parent.engine.logger.info(f"resolved_path after images_dir join: {resolved_path}")
        resolved_path = self.fix_path(resolved_path)
        # self.parent.engine.logger.info(f"resolved_path after first fix_path: {resolved_path}")

        # Fix for the default render layer naming convention
        if render_layer == "defaultRenderLayer":
            render_layer = "masterLayer"  # Replace with masterLayer for disk path
            resolved_path = resolved_path.replace("defaultRenderLayer", "masterLayer")

        if resolved_path.endswith(f"/{render_layer}"):
            resolved_path_split = resolved_path.split("/")
            resolved_path = "/".join(resolved_path_split[:-1])

        resolved_path = self.fix_path(resolved_path)

        return resolved_path

    def collect_aovs(self, renderer, resolved_path):
        aov_info = {}

        if renderer == "redshift":
            # Redshift AOVs
            aovs = cmds.ls(type="RedshiftAOV")
            # for redshift, we need to force the default aov: Beauty
            aovs.append("Beauty")
            self.parent.engine.logger.debug(f"Redshift AOVs: {aovs}\n")  # Debug print
        elif renderer == "arnold":
            # Arnold AOVs
            aovs = cmds.ls(type="aiAOV")
            self.parent.engine.logger.debug(f"Arnold AOVs: {aovs}\n")  # Debug print
        else:
            pass

        for aov in aovs:
            if aov == "Beauty":
                aov_name = "Beauty"
                # Replace <aov> token with the actual AOV name
                aov_path = resolved_path.replace("<aov>", aov_name)
                aov_path = self.fix_path(aov_path)
                aov_info[aov_name] = aov_path
                # self.parent.engine.logger.info(f"  AOV: {aov_name}, Path: {aov_info[aov_name]}")  # Debug print
            else:
                if cmds.getAttr(f"{aov}.enabled"):
                    aov_name = cmds.getAttr(f"{aov}.name")
                    # Replace <aov> token with the actual AOV name
                    aov_path = resolved_path.replace("<aov>", aov_name)
                    aov_path = self.fix_path(aov_path)
                    aov_info[aov_name] = aov_path
                    # self.parent.engine.logger.info(f"  AOV: {aov_name}, Path: {aov_info[aov_name]}")  # Debug print

        return aov_info

    def get_render_layers_info(self):
        # self.parent.engine.logger.info("\n{}".format("=" * 80))

        # Determine the renderer
        renderer = cmds.getAttr("defaultRenderGlobals.currentRenderer")
        self.parent.engine.logger.info(f"Current renderer: {renderer}")  # Debug print

        # Get the workspace images directory
        images_dir = self.get_images_dir()
        self.parent.engine.logger.info(f"Images dir: {images_dir}")  # Debug print

        # Get the resolved file prefix for the render layer
        file_prefix = (
            cmds.getAttr("defaultRenderGlobals.imageFilePrefix")
            or "<scene>/<renderLayer>/<aov>"
        )
        self.parent.engine.logger.info(f"file_prefix: {file_prefix}")  # Debug print

        scene_name = cmds.file(q=True, sn=True, shortName=True).split('.')[0]
        self.parent.engine.logger.info(f"scene_name: {scene_name}")  # Debug print

        valid_render_layers = self.get_valid_renderable_render_layers()
        self.parent.engine.logger.info(f"Valid render layers: {valid_render_layers}")  # Debug print

        render_layers_info = {}

        # Save the current render layer to restore later
        original_layer = cmds.editRenderLayerGlobals(query=True, currentRenderLayer=True)

        for render_layer in valid_render_layers:
            self.parent.engine.logger.info("-" * 40)
            # Set current render layer
            cmds.editRenderLayerGlobals(currentRenderLayer=render_layer)
            # get rid of the redshift prefix in render layers
            clean_render_layer = self.cleanup_render_layer_name(renderer, render_layer)
            self.parent.engine.logger.info(f"Cleaned render layer name: {clean_render_layer}")

            resolved_path = self.get_resolved_render_layer_path(
                file_prefix, scene_name, images_dir, clean_render_layer
            )
            self.parent.engine.logger.info(
                f"Resolved path for {clean_render_layer}: {resolved_path}"
            )

            # Collect AOVs
            aov_info = self.collect_aovs(renderer, resolved_path)
            if not aov_info:
                self.parent.engine.logger.info(
                    f"No AOVs found in {render_layer}. Skipping to next layer."
                )
                continue
            self.parent.engine.logger.info(f"layer aov names: {list(aov_info.keys())}")
            self.parent.engine.logger.debug(f"aov_info:\n{pf(aov_info)}")

            # remove aov suffix from render_layer path
            if resolved_path.endswith("/<aov>"):
                resolved_path = resolved_path.replace("/<aov>", "")

            render_layers_info[render_layer] = {
                "path": os.path.join(images_dir, resolved_path),
                "aovs": aov_info
            }

        # Restore the original render layer
        cmds.editRenderLayerGlobals(currentRenderLayer=original_layer)

        return render_layers_info

    def get_image_sequences(self, render_layers_info):
        all_sequences = {}

        renderer = cmds.getAttr("defaultRenderGlobals.currentRenderer")

        # Iterate through render layers and resolve image sequences
        for render_layer, data in render_layers_info.items():
            main_dir = data["path"]
            self.parent.engine.logger.info("-" * 40)
            self.parent.engine.logger.info(f"Looking for sequences in: {main_dir}")  # Debug print

            # Remove any render specific prefix in the layer name
            render_layer = self.cleanup_render_layer_name(renderer, render_layer)

            # Fix for the default render layer naming convention
            if render_layer == "defaultRenderLayer":
                render_layer = "masterLayer"

            # Collect sequences from the main directory and subdirectories up to 2 levels deep
            sequences = []
            for root, dirs, files in os.walk(main_dir):
                if root.count(os.sep) - main_dir.count(os.sep) >= 2:
                    # Skip directories beyond 2 levels deep
                    continue

                found_sequences = fileseq.findSequencesOnDisk(root)
                if found_sequences:
                    sequences.extend(found_sequences)
                self.parent.engine.logger.warning(
                    f"Unable to find sequences in: {root}"
                )

            self.parent.engine.logger.info(f"{len(sequences)} Sequences found in {main_dir} (and subdirectories):\n{pf(sequences)}")  # Debug print

            # save a list of the aov names, for later use
            aov_names = list(data["aovs"].keys())

            # skip next part if no sequences were found on disk
            if not sequences:
                continue

            # Match sequences to the respective AOVs
            for aov_name, aov_path in data["aovs"].items():
                self.parent.engine.logger.debug(f"Checking sequences for AOV: {aov_name}")  # Debug print

                for seq in sequences:
                    # convert padding to printf style
                    seq_first_frame = seq.start()
                    seq_first_frame_path = list(seq)[0]
                    seq_padding = seq.getPaddingNum("#")

                    seq_path = seq_first_frame_path.replace(
                        str(seq_first_frame), "%0{}d".format(seq_padding)
                    )
                    seq_path = self.fix_path(seq_path)
                    seq_basename = os.path.basename(seq_path)

                    self.parent.engine.logger.debug(f"  Checking sequence: {seq_basename}")  # Debug print
                    # self.parent.engine.logger.debug(f"  aov_name: {aov_name}, seq_basename: {seq_basename}")
                    pattern = re.compile(
                        (
                            r"(?P<head>{})"
                            r"[._]"
                            r"(?P<aov>{})"
                            r"[._]"
                            r"(?P<tail>.+)"
                        ).format(render_layer, aov_name)
                    )
                    match = re.match(pattern, seq_basename)
                    if match:
                    # if aov_name in seq_basename:  # Match AOV name to the sequence
                        self.parent.engine.logger.debug(f"    Match found for aov {aov_name}: {seq_basename}")  # Debug print
                        if render_layer not in all_sequences:
                            all_sequences[render_layer] = {
                                "main_directory": main_dir,
                                # "path": seq_path,
                                "aovs": {}
                            }
                        all_sequences[render_layer]["aovs"][aov_name] = {
                            "path": seq_path, "fileseq_obj": seq
                        }
                        break
                    # handle the special case of the Beauty aov which doesn't explicitely
                    # contain the aov name in the sequence name
                    elif not any(aov in seq_basename for aov in aov_names) and aov_name.lower() == "beauty":
                        # we assume this means we are dealing with the beauty aov as none of
                        # the other aovs are in the sequence name
                        self.parent.engine.logger.debug(f"    Match found for aov {aov_name}: {seq_basename}")  # Debug print
                        if render_layer not in all_sequences:
                            all_sequences[render_layer] = {
                                "main_directory": main_dir,
                                # "path": seq_path,
                                "aovs": {}
                            }
                        all_sequences[render_layer]["aovs"][aov_name] = {
                            "path": seq_path, "fileseq_obj": seq
                        }
                        break

        return all_sequences


    # def get_scene_images_dir(self):
    #     # Get the current scene file path
    #     scene_path = cmds.file(q=True, sceneName=True)
    #     if not scene_path:
    #         raise RuntimeError("The scene has not been saved. Please save the scene to resolve the path.")

    #     # Get the directory of the scene and append the images folder
    #     scene_dir = os.path.dirname(scene_path)
    #     images_dir = os.path.join(scene_dir, "images")

    #     return images_dir

    def resolve_arnold_path(self, aov_name):
        images_dir = self.get_scene_images_dir()
        file_prefix = cmds.getAttr("defaultArnoldDriver.prefix")
        resolved_prefix = file_prefix.replace("<RenderPass>", aov_name)
        resolved_prefix = resolved_prefix.replace(
            "<RenderLayer>", cmds.editRenderLayerGlobals(
                query=True, currentRenderLayer=True
            )
        )
        resolved_prefix = resolved_prefix.replace(
            "<Scene>", cmds.file(q=True, sceneName=True, shortName=True).split('.')[0]
        )
        file_extension = cmds.getAttr("defaultArnoldDriver.ai_translator")

        arnold_path = os.path.join(images_dir, f"{resolved_prefix}.{file_extension}")
        arnold_path = self.fix_path(arnold_path)

        return arnold_path

    def resolve_redshift_image_format(self, image_format_int):
        """
        Resolve the image format (extension) using a map of supported formats by Redshift
        """

        formats_mapping = {
            0: "iff",
            1: "exr",
            2: "png",
            3: "tga",
            4: "jpg",
            5: "tif",
        }

        return formats_mapping.get(image_format_int)

    # def resolve_redshift_path(self, prefix, layer, aov_name):
    #     """
    #     Resolves the full path for a Redshift AOV file using the workspace and the filename prefix.
    #     """
    #     # Get the workspace's images directory
    #     images_dir = self.get_scene_images_dir()

    #     # Replace tokens in the prefix
    #     resolved_prefix = prefix
    #     scene_basename = cmds.file(q=True, sceneName=True, shortName=True)
    #     scene_basename = os.path.splitext(scene_basename)[0]
    #     image_format_int = cmds.getAttr("redshiftOptions.imageFormat")
    #     image_format = self.resolve_redshift_image_format(image_format_int)

    #     self.parent.logger.info("-" * 50)
    #     self.parent.logger.info("prefix: {}, type: {}".format(resolved_prefix, type(resolved_prefix)))
    #     self.parent.logger.info("scene_basename: {}, type: {}".format(scene_basename, type(scene_basename)))
    #     self.parent.logger.info("layer: {}, type: {}".format(layer, type(layer)))
    #     self.parent.logger.info("aov_name: {}, type: {}".format(aov_name, type(aov_name)))
    #     self.parent.logger.info("image_format: {}, type: {}".format(image_format, type(image_format)))

    #     resolved_prefix = resolved_prefix.replace('<scene>', scene_basename)
    #     resolved_prefix = resolved_prefix.replace('<renderLayer>', layer)
    #     resolved_prefix = resolved_prefix.replace('<RenderPass>', aov_name)
    #     resolved_prefix = resolved_prefix.replace('<aov>', aov_name)
    #     resolved_prefix = resolved_prefix.replace('<Extension>', image_format)

    #     self.parent.logger.info("resolved_prefix: {}".format(resolved_prefix))

    #     # Construct the full path
    #     resolved_path = os.path.join(images_dir, resolved_prefix)
    #     resolved_path = self.fix_path(resolved_path)

    #     self.parent.logger.info("resolved_path: {}".format(resolved_path))

    #     return resolved_path


    # def get_render_layers_and_aovs(self):
    #     result = {}
    #     render_layers = cmds.ls(type="renderLayer")

    #     for layer in render_layers:
    #         # Skip non-renderable layers and referenced render layers
    #         if cmds.referenceQuery(layer, isNodeReferenced=True):
    #             continue  # Skip referenced render layers
    #         if not cmds.getAttr(f"{layer}.renderable"):
    #             continue  # Skip non-renderable layers

    #         # Set the current render layer
    #         cmds.editRenderLayerGlobals(currentRenderLayer=layer)

    #         # Determine the renderer
    #         current_renderer = cmds.getAttr("defaultRenderGlobals.currentRenderer")

    #         if current_renderer == "arnold":
    #             aovs = cmds.ls(type="aiAOV")
    #             main_path = self.get_scene_images_dir()
    #             aov_dict = {"path": main_path}
    #             for aov in aovs:
    #                 if cmds.getAttr(f"{aov}.enabled"):
    #                     aov_name = cmds.getAttr(f"{aov}.name")
    #                     resolved_path = self.resolve_arnold_path(aov_name)
    #                     aov_dict[aov_name] = resolved_path
    #         elif current_renderer == "redshift":
    #             # Redshift AOV resolution
    #             aovs = cmds.ls(type="RedshiftAOV")
    #             main_path = self.get_scene_images_dir()
    #             aov_dict = {"path": main_path}
    #             for aov in aovs:
    #                 aov_name = cmds.getAttr(f"{aov}.name")
    #                 file_prefix = cmds.getAttr("redshiftOptions.imageFilePrefix")
    #                 resolved_path = self.resolve_redshift_path(file_prefix, layer, aov_name)
    #                 aov_dict[aov_name] = resolved_path
    #         else:
    #             aov_dict = {}

    #         result[layer] = aov_dict

    #     return result


    def _validate_name(self, item):
        """
        Filter for validating non alphanumeric characters in layer and aovs names
            Parameters:
                Item
            Returns:
                True or False
        """
        layer = item.properties['sequence_text_id']
        aov = item.properties['sequence_text2_id']
        self.parent.engine.logger.info(
            "Validating layer '{}' and aov '{}' names".format(layer, aov)
        )

        regex = r"[^0-9a-zA-Z]"
        # validating layer
        matches = re.findall(regex, layer, re.MULTILINE)
        # validating aov
        matches.extend(re.findall(regex, aov, re.MULTILINE))
        matches_len =len(list(matches))

        if matches_len > 0:
            self.parent.engine.logger.info('Found {} invalid non-alphanumeric characters in names'.format(matches_len))
            return True
        else:
            return False

    def _change_sequence_name(self, item):
        """
        Receives an item and filters non-alphanumeric characters in layer and aov names
        Renames files in disk and names in maya file
            Parameters:
                Item
        """
        self.parent.engine.logger.info('Autofix change sequence names'
            + '\nOriginal item: '
            + '\n\t' + str(item.properties['aov_fileseq_obj'])
            + '\n\t' + item.properties['render_layer']
            + '\n\t' + item.properties['aov_name']
        )

        # Filter names and create clean version
        regex = r"[^0-9a-zA-Z]"
        sub = ''
        seq = item.properties['aov_fileseq_obj']
        layer_name = item.properties['render_layer']
        aov_name = item.properties['aov_name']
        layer_clean = re.sub(regex, sub, layer_name, 0, re.MULTILINE)
        aov_clean = re.sub(regex, sub, aov_name, 0, re.MULTILINE)

        # Get maya data
        output_file = cmds.getAttr("defaultRenderGlobals.imageFilePrefix")
        ext_pad = cmds.getAttr("defaultRenderGlobals.extensionPadding")
        ext_format = cmds.getAttr("defaultRenderGlobals.imfkey")

        # # Get sequence name according to fixes and folders
        # # if render layer exists in folder instead of file name, we have to update
        # # the path if it has already been fixed because item path has old name
        # seq = None
        # if not '<RenderLayer>' in output_file:
        #     # update path if the folder has been already changed
        #     try:
        #         seq = fileseq.findSequenceOnDisk(item.properties['sequence'])
        #     except:
        #         path = str(item.properties['sequence'])
        #         new_seq_path = path.replace(layer_name, layer_clean, 1)
        #         seq = fileseq.findSequenceOnDisk(new_seq_path)
        # else:
        #     seq = fileseq.findSequenceOnDisk(item.properties['sequence'])

        old_basename = seq.basename()
        new_basename = output_file

        # Gets matching aov used for updating name in maya file
        current_renderer = cmds.getAttr("defaultRenderGlobals.currentRenderer")
        if current_renderer == "arnold":
            aovs = cmds.ls(type='aiAOV')
        elif current_renderer == "redshift":
            aovs = cmds.ls(type='RedshiftAOV')
        else:
            aovs = []

        aovs = [aov for aov in aovs if not cmds.referenceQuery(aov, isNodeReferenced=True)]
        aov = None
        for a in aovs:
            current_name = cmds.getAttr(a + ".name")
            if current_name == aov_name:
                aov = a

        # If change is already done, return and dont repeat process
        if layer_name == layer_clean and aov_name == aov_clean:
            self.parent.engine.logger.info('No changes to make')
            return

        # Build clean name
        new_dirname = seq.dirname()
        # files in one folder or diferent render layer folders
        if '<RenderLayer>' in new_basename:
            if layer_clean == 'defaultRenderLayer':
                # key returned by maya is different from name used in files for default layer
                new_basename = new_basename.replace('<RenderLayer>', 'masterLayer')
                new_basename = new_basename.replace('<defaultRenderLayer>', 'masterLayer')
            else:
                new_basename = new_basename.replace('<RenderLayer>', layer_clean)
        else:
            old_dirname = seq.dirname()
            new_dirname = old_dirname.replace(layer_name, layer_clean)
            os.rename(old_dirname, new_dirname)
        # if '<RenderPass>' in new_basename:
        #     new_basename = new_basename.replace('<RenderPass>', aov_clean)

        # Construct the new frame path with the updated base name
        new_seq_path = os.path.join(new_dirname, new_basename)

        # Renaming files
        self.parent.engine.logger.info('Renaming Files.')
        for frame in seq.frameSet():
            original_frame_path = seq.frame(frame)
            # update path name if it has been fixed
            if not os.path.exists(original_frame_path):
                original_frame_path = original_frame_path.replace(layer_name, layer_clean)
            new_frame_path = '{}_{}.{}'.format(new_seq_path, frame, ext_format)
            os.rename(original_frame_path, new_frame_path)

        # Renaming names in maya file
        if cmds.objExists(layer_name) and layer_name != 'defaultRenderLayer':
            # rename node
            cmds.select('rs_'+layer_name)
            cmds.rename('rs_'+layer_name, 'rs_' + layer_clean)
            # rename display
            cmds.rename(layer_name, layer_clean)
            self.parent.engine.logger.info('Changed layer name in maya file: ' + layer_clean)
        if aov != None and aov_name != aov_clean:
            cmds.setAttr(aov + ".name", aov_clean, type="string")
            self.parent.engine.logger.info('Changed aov name in maya file: ' + aov_clean)

        # Update item properties
        item.properties['sequence'] = fileseq.findSequenceOnDisk(new_seq_path + '_' + '@'*ext_pad + '.' + ext_format)
        item.properties['sequence_text_id'] = layer_clean
        item.properties['sequence_text2_id'] = aov_clean
        self.parent.engine.logger.info('Fixed item:'
            + '\n\t' + str(item.properties['sequence'])
            + '\n\t' + item.properties['sequence_text_id']
            + '\n\t' + item.properties['sequence_text2_id']
        )

    # def _get_basename_renderLayer(self, key, check_basename):
    #     if key == 'defaultRenderLayer' or key == 'masterLayer':
    #         # key returned by maya is different from name used in files for default layer
    #         return check_basename.replace('<RenderLayer>', 'masterLayer')
    #     else:
    #         return check_basename.replace('<RenderLayer>', key)

    # def _get_file_sequence(self, basename, sequences, aov):
    #     '''
    #         Parameters
    #             - basename: file basename according to render settings
    #             - sequences: list of sequences on folder
    #             - aov: aov currently iterating
    #         Returns
    #             - Sequence: only matching sequence with basename and aov received
    #             - None: if none of them is a match
    #     '''
    #     basename = basename.replace('<RenderPass>', aov)
    #     basename = basename.replace('<aov>', aov)
    #     for seq in sequences:
    #         seq_basename = seq.basename().rstrip('._')
    #         if basename == seq_basename:
    #             return seq
    #     return None

    def update_output_render_in_shot_field(self, list_rendered_outpus):
        """This method update the field sg_rendered_outputs,
        with the information obtained from the maya"""

        self.parent.engine.logger.info(
            "Updating sg_rendered_outputs for the current shot"
        )
        self.parent.engine.logger.debug(
            "list_rendered_outpus:\n{}".format(pf(list_rendered_outpus))
        )

        shotgun = self.parent.engine.shotgun
        current_task = self.parent.context.task
        current_entity = self.parent.context.entity

        # If no outputs can be found, skip
        if not list_rendered_outpus:
            return

        # If current context is not a shot, skip
        if current_entity.get("type") != "Shot":
            return

        shot_info = self.find_info_about_current_shot()
        self.parent.engine.logger.debug(pf(shot_info))

        rendered_outputs = shot_info["sg_rendered_outputs"]
        current_task_name = current_task["name"]

        # Check if already exists content in the field
        if rendered_outputs:
            # convert the content to dict
            rendered_outputs = json.loads(rendered_outputs)

            # check if already the task name in the keys
            if current_task_name in list(rendered_outputs.keys()):
                old_task_value = rendered_outputs[current_task_name]

                # if the output already exists in the task name value in the dict
                # skip this process
                if all(elem in old_task_value for elem in list_rendered_outpus):
                    self.parent.engine.logger.info(
                    "The current rendered outputs already exists in the field"
                    )
                    return

                ### add the new outputs to the old list
                #conver list to set
                set_old_task_value = set(old_task_value)
                set__rendered_outpus = set(list_rendered_outpus)

                #union sets and convert to list
                new_lst_value = list(set_old_task_value.union(set__rendered_outpus))
                new_task_value = {current_task_name: new_lst_value}

                # update the dictionary
                rendered_outputs.update(new_task_value)
            else:
                # if the task name not exists in the keys, creat new item
                new_task_value = {current_task_name: list_rendered_outpus}
                rendered_outputs.update(new_task_value)
        else:
            # if not exists the value on the field create the dict with info.
            rendered_outputs = {}
            new_task_value = {current_task_name: list_rendered_outpus}
            rendered_outputs.update(new_task_value)

        # convert dict to json string
        update_outputs = json.dumps(rendered_outputs)

        self.parent.engine.logger.info("update_outputs: \n{}".format(update_outputs))
        result = shotgun.update(
            "Shot", current_entity["id"], {"sg_rendered_outputs": update_outputs}
        )
        self.parent.engine.logger.info(
            "Rendered Outputs update info:\n{}".format(pf(result))
        )

    def find_info_about_current_shot(self):
        """get info of the shot"""
        sg_proj = self.parent.engine.context.project
        shotgun = self.parent.engine.shotgun
        current_entity = self.parent.engine.context.entity

        filters = [
            ["project", "is", sg_proj],
            ["id", "is", current_entity["id"]],
        ]
        fields = ["id", "name", "sg_rendered_outputs"]
        result = shotgun.find_one("Shot", filters, fields)

        return result