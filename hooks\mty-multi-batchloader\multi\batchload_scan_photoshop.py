#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

import os
import sys
import json
import traceback
from tank import Hook
from html import entities
from pprint import pformat
from tank.platform.qt import QtCore, QtGui

class ScanSceneHook(Hook):

    def scan_scene(self):

        self.parent.engine.logger.info("")
        self.parent.engine.logger.info(
            "Scanning scene for Photoshop files...".ljust(120, "-")
        )

        items_result = []
        warnings = []
        errors = []

        shot_id = self.parent.context.entity['id']

        # ------------------------------------------------------------------------------
        # Load value overrides framework
        valueoverrides = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        ) or self.load_framework("mty-framework-valueoverrides")

        if not valueoverrides:
            self.parent.engine.logger.error("Couldn't load value overrides framework")
            photoshop_tasks_list = self.parent.get_setting(
                "photoshop_tasks_list"
            )
            self.parent.engine.logger.info(
                "photoshop_tasks_list from hook settings: {}".format(
                    photoshop_tasks_list
                )
            )

        # Get task priority list from override
        if valueoverrides:
            default_value_code = "mty.multi.batchloader.scan.photoshop_priority_list"
            override_link = {"type": "Task", "id": self.parent.engine.context.task["id"]}
            self.parent.engine.logger.info("override_link: {}".format(override_link))
            photoshop_tasks_list = valueoverrides.get_value(
                default_value_code, link=override_link
            )
            self.parent.engine.logger.info(
                "photoshop_tasks_list: {}".format(photoshop_tasks_list)
            )
            if photoshop_tasks_list:
                photoshop_tasks_list = json.loads(photoshop_tasks_list)
                self.parent.engine.logger.info(
                    "photoshop_tasks_list from override: {}".format(
                        photoshop_tasks_list
                    )
                )

        # ------------------------------------------------------------------------------
        # Published File Types used by default
        published_file_type_names = ['Photoshop Image']

        # # Get curreent project ID
        # project_id = self.parent.context.project['id']

        # # Set override for published file type
        # asset_type_override_code = 'mty.multi.batchloader.scan.photoshop.asset_type'
        # asset_type_override_link = {
        #     'type': 'Project',
        #     'id': project_id
        # }

        # # Get override value
        # custom_asset_types = valueoverrides.get_value(
        #     asset_type_override_code, link=asset_type_override_link
        # )

        # if custom_asset_types:
        #     custom_asset_types = json.loads(custom_asset_types)
        #     self.parent.engine.logger.info(
        #         "custom_asset_types from override: {}".format(
        #             custom_asset_types
        #         )
        #     )


        # self.parent.engine.logger.info(
        #     "custom_asset_types for project {}: {}".format(
        #         project_id, custom_asset_types
        #     )
        # )

        filters = [
            ['entity.Shot.id', 'is', shot_id],
            ['sg_status_list', 'is', 'apr'],
            ['published_file_type.PublishedFileType.code', 'in', published_file_type_names],
            # ['entity.Asset.sg_asset_type', 'in', custom_asset_types],
            ['task.Task.content', 'in', photoshop_tasks_list],
        ]

        fields = [
            "code", "name", "published_file_type", "version_number", "task",
            "path", 'id', "entity.Asset.code"
        ]

        try:

            self.parent.logger.info("Scanning scene to get the reference needed for the scene.")

            special_pose_list = self.parent.shotgun.find(
                "PublishedFile", filters, fields
            )

            self.parent.logger.info('special_pose_list: ' + pformat(special_pose_list))

            if not special_pose_list:
                items_result.append({})
                return items_result, warnings, errors

            for pose in special_pose_list:
                node = pose["name"]
                path = pose["path"]["local_path"]
                sg_data = pose

                items_result.append(
                    {
                        'node': node,
                        'path': path,
                        'process_hook': 'batchload_process_photoshop',
                        'type': 'Photoshop Image',
                        'sg_data': sg_data,
                    }
                )

            self.parent.logger.info("items_result:\n{}".format(pformat(items_result)))


            # Ensure metasync framework is available, but only load it once.
            if not hasattr(self, 'metasync'):
                self.metasync = self.parent.engine.custom_frameworks.get(
                "mty-framework-metasync"
            )
            if not self.metasync:
                self.metasync = self.load_framework("mty-framework-metasync")

            transfersManager = self.metasync.transfersManager

            for bg in items_result:
                # if "path" in bg and "sg_data" in bg:
                bg_local_path = bg.get("path", "")
                if not os.path.exists(bg_local_path):
                    self.parent.logger.info(
                        "Bg not found on disk: %s" % bg_local_path
                    )
                    self.parent.logger.info(
                        "sg_data:\n{}".format(pformat(bg['sg_data']))
                    )
                    transfersManager.ensure_file_is_local(
                        # bg_local_path, bg['sg_data']["sg_data"]
                        bg_local_path, bg.get('sg_data', {}), sound_notifications=False
                    )
                # transfersManager.ensure_local_dependencies(bg.get('sg_data', {}).get('sg_data', None))
                transfersManager.ensure_local_dependencies(bg.get('sg_data', {}))

        except:
            error_str = traceback.format_exc()
            errors.append(error_str)
            self.parent.logger.error(error_str)

        self.parent.logger.info("items_result:\n{}".format(items_result))

        return items_result, warnings, errors
