# Copyright (c) 2015 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

"""
Hook that loads defines all the available actions, broken down by publish type.
"""

import os
import re
import six
import sys
import json
import pprint
import shutil
import fileseq
import tempfile
import traceback
import subprocess

import sgtk
from sgtk.errors import TankError
from tank.platform.qt import QtCore, QtGui

QLabel = QtGui.QLabel
QDialog = QtGui.QDialog
QPushButton = QtGui.QPushButton
QVBoxLayout = QtGui.QVBoxLayout
QApplication = QtGui.QApplication


__author__ = "Diego Garcia Huert<PERSON>"
__contact__ = "https://www.linkedin.com/in/diegogh/"


META_SHOTGUN_PATH = "meta.shotgun.path"

HookBaseClass = sgtk.get_hook_baseclass()


class HarmonyActions(HookBaseClass):
    ###########################################################################
    # public interface - to be overridden by deriving classes

    def generate_actions(self, sg_publish_data, actions, ui_area):
        """
        Returns a list of action instances for a particular publish. This
        method is called each time a user clicks a publish somewhere in the UI.
        The data returned from this hook will be used to populate the actions
        menu for a publish.

        The mapping between Publish types and actions are kept in a different
        place (in the configuration) so at the point when this hook is called,
        the loader app has already established *which* actions are appropriate
        for this object.

        The hook should return at least one action for each item passed in via
        the actions parameter.

        This method needs to return detailed data for those actions, in the
        form of a list of dictionaries, each with name, params, caption and
        description keys.

        Because you are operating on a particular publish, you may tailor the
        output  (caption, tooltip etc) to contain custom information suitable
        for this publish.

        The ui_area parameter is a string and indicates where the publish is to
        be shown.
        - If it will be shown in the main browsing area, "main" is passed.
        - If it will be shown in the details area, "details" is passed.
        - If it will be shown in the history area, "history" is passed.

        Please note that it is perfectly possible to create more than one
        action "instance" for an action!
        You can for example do scene introspectionvif the action passed in
        is "character_attachment" you may for examplevscan the scene, figure
        out all the nodes where this object can bevattached and return a list
        of action instances: "attach to left hand",v"attach to right hand" etc.
        In this case, when more than  one object isvreturned for an action, use
        the params key to pass additional data into the run_action hook.

        :param sg_publish_data: Shotgun data dictionary with all the standard
                                publish fields.
        :param actions: List of action strings which have been
                        defined in the app configuration.
        :param ui_area: String denoting the UI Area (see above).
        :returns List of dictionaries, each with keys name, params, caption
         and description
        """

        app = self.parent
        app.log_debug(
            "Generate actions called for UI element %s. "
            "Actions: %s. Publish Data: %s" % (ui_area, actions, sg_publish_data)
        )

        published_file_type = sg_publish_data["published_file_type"]["name"]
        app.log_debug("published_file_type: %s" % published_file_type)

        action_instances = []

        if actions:
            for action in actions:
                action_instances.append(
                    {
                        "name": "Import %s as %s"
                        % (published_file_type, action.capitalize()),
                        "params": {
                            "action": action,
                            "published_file_type": published_file_type,
                        },
                        "caption": "Import %s as %s"
                        % (published_file_type, action.capitalize()),
                        "description": (
                            "This will import the %s %s inside the current project."
                            % (published_file_type, action)
                        ),
                    }
                )

        return action_instances

    def execute_multiple_actions(self, actions):
        """
        Executes the specified action on a list of items.

        The default implementation dispatches each item from ``actions`` to
        the ``execute_action`` method.

        The ``actions`` is a list of dictionaries holding all the actions to
        execute.
        Each entry will have the following values:

            name: Name of the action to execute
            sg_publish_data: Publish information coming from Shotgun
            params: Parameters passed down from the generate_actions hook.

        .. note::
            This is the default entry point for the hook. It reuses the
            ``execute_action`` method for backward compatibility with hooks
             written for the previous version of the loader.

        .. note::
            The hook will stop applying the actions on the selection if an
            error is raised midway through.

        :param list actions: Action dictionaries.
        """
        app = self.parent
        for single_action in actions:
            app.log_debug("Single Action: %s" % single_action)
            name = single_action["name"]
            sg_publish_data = single_action["sg_publish_data"]
            params = single_action["params"]

            # self.execute_action(name, params, sg_publish_data)
            try:
                self.execute_action(name, params, sg_publish_data)
                # play sound from config hook 'play_sounds'
                self.parent.logger.info("Playing sound, reached end of execute_action")
                self.parent.engine.execute_hook_expression(
                    "{config}/notifications.py",
                    "success_sound",
                )
            except:
                # play sound from config hook 'play_sounds'
                self.parent.logger.info("Playing sound, error raised in execute_action")
                self.parent.engine.execute_hook_expression(
                    "{config}/notifications.py",
                    "error_sound",
                )

    def execute_action(self, name, params, sg_publish_data):
        """
        Execute a given action. The data sent to this be method will
        represent one of the actions enumerated by the generate_actions method.

        :param name: Action name string representing one of the items returned
                     by generate_actions.
        :param params: Params data, as specified by generate_actions.
        :param sg_publish_data: Shotgun data dictionary with all the standard
                                publish fields.
        :returns: No return value expected.
        """
        engine = sgtk.platform.current_engine()
        app = self.parent
        app.log_debug(
            "Execute action called for action %s. "
            "Parameters: %s. Publish Data: %s" % (name, params, sg_publish_data)
        )

        # first ensure file is local, always
        regular_path = self.get_publish_path(sg_publish_data)
        app.log_debug("regular_path: {}".format(regular_path))
        self.ensure_file_is_local(regular_path, sg_publish_data)

        # resolve path
        # toolkit uses utf-8 encoded strings internally and Harmony API
        # expects unicode so convert the path to ensure filenames containing
        # complex characters are supported
        harmony_path = regular_path.replace(os.path.sep, "/")

        action = params["action"]

        #        -----------------------------       #

        if action == "copy_path":
            self._copy_publish_path(regular_path)

        elif action == "download_publish":
            pass

        elif action == "image_sequence":
            tmp_image_seq = self._convert_img_seq_to_png_img_seq(regular_path)
            regular_path = self.fix_path(regular_path)
            self._import_image_sequence(regular_path, tmp_image_seq)

        elif action == "movie":
            self._import_movie(regular_path)

        elif action == "tpl":
            self._import_tpl(regular_path)

        elif action == "camera":
            self._import_camera(regular_path)

        elif action == "image_as_project_resolution":
            self._import_image_project_resolution(regular_path)

        elif action == "image_as_vector":
            self._import_image_as_vector(regular_path)

        elif action == "3d_mesh":
            self._import_3d_mesh(regular_path)

        elif action == "color_palette":
            self._import_color_palette(regular_path)

        else:
            result = engine.app.import_project_resource(harmony_path, action)

    # ================================================================
    """
    # moved to /tk-multi-loader2/tk-multi-loader2_multi_transfer_actions.py

    def ensure_file_is_local(self, path, published_file):

        if not hasattr(self, 'metasync'):
            self.metasync = self.load_framework("mty-framework-metasync")

        transfersManager = self.metasync.transfersManager
        #Sequence
        if "%" in path:
            dirname = os.path.dirname(path)
            if os.path.exists(dirname) and self._collect_sequenced_files(path):
                transfersManager.ensure_local_dependencies(published_file)
                return path
        else:
            if os.path.exists(path):
                transfersManager.ensure_local_dependencies(published_file)
                return path

        transfersManager.ensure_file_is_local(path, published_file)
        transfersManager.ensure_local_dependencies(published_file)

        return path
    """
    # ================================================================

    def fix_path(self, path):
        """
        Fixes the path to be compatible with the Harmony API, which only accepts forward
        slashes
        """
        # fix image_path
        path = path.replace("\\\\", "/")
        path = path.replace("\\", "/")

        return path

    def _collect_sequenced_files(self, sequence_path):
        """Returns a list containing all of the sequence files"""
        # folder_path = os.path.dirname(sequence_path)
        # folder_files = os.listdir(folder_path)

        # name, ext = os.path.splitext(sequence_path)
        # sequence_files = []

        # if len(folder_files) > 0:
        #     for file in sorted(folder_files):
        #         if file.endswith(ext):
        #             path = os.path.join(folder_path, file)
        #             sequence_files.append(path)

        fileseq_obj = fileseq.findSequenceOnDisk(sequence_path)
        if fileseq_obj:
            self.parent.log_info("Found sequence on disk: {}".format(fileseq_obj))
            sequence_files = list(fileseq_obj)
        else:
            self.parent.log_warning(
                "Could not find sequence on disk: {}".format(sequence_path)
            )
            sequence_files = []

        return sequence_files, fileseq_obj

    # ================================================================

    def _copy_publish_path(self, path):
        import os
        import sys

        python_modules_path = os.path.join(
            os.path.dirname(self.disk_location), "external_python_modules"
        )
        sys.path.append(python_modules_path)
        import pyperclip

        pyperclip.copy(path)

        self.parent.engine.logger.info("Path copied to clipboard: {}".format(path))

    # ================================================================

    def _import_image_sequence(self, publish_path, sequence_path):
        engine = self.parent.engine
        sequence_files_list, fileseq_obj = self._collect_sequenced_files(sequence_path)

        if not sequence_files_list:
            msg = "No image sequence found on disk: {}".format(sequence_path)
            self.parent.engine.logger.error(msg)
            self.parent.engine.show_message(msg, level="error")
            return

        # files_str= ""

        # if len(sequence_files_list) > 0:
        #     for file in sorted(sequence_files_list):
        #         if file != sorted(sequence_files_list)[-1]:
        #             file_str= file+ ";"
        #             files_str+= file_str
        #         else:
        #             file_str= file
        #             files_str+= file_str

        # files=[files_str]

        # #Import to Toon Boom Harmony
        # backUp_imported_files= engine.app.execute('preferences.getString("IMPORTIMGDLG_IMAGE_LASTIMPORT","");')
        # #Set the preference to autofill the import dialog, and trigger it
        # engine.app.execute('preferences.setString("IMPORTIMGDLG_IMAGE_LASTIMPORT", {0});'.format(files))
        # engine.app.execute('Action.perform("onActionImportDrawings()");')

        # #Restore preference with previously captured value.
        # engine.app.execute('preferences.setString("IMPORTIMGDLG_IMAGE_LASTIMPORT", backupFilelist)')

        # Show a new busy message
        self.parent.engine.clear_busy()
        self.parent.engine.show_busy(
            "Importing image sequence", "Importing image sequence..."
        )

        img_folder = fileseq_obj.dirname()
        img_name = fileseq_obj.basename()
        img_extension = fileseq_obj.extension()
        img_count = len(sequence_files_list)

        # fix path with forward slashes only
        img_folder = self.fix_path(img_folder)

        # remove extra head or tail non alphanumeric characters
        img_name = self._remove_head_non_alphanumeric(img_name)
        img_name = self._remove_tail_non_alphanumeric(img_name)
        img_extension = self._remove_head_non_alphanumeric(img_extension)
        img_extension = self._remove_tail_non_alphanumeric(img_extension)

        # get image scale multiplier
        img_scale_multiplier = self._get_image_scale_multiplier(sequence_files_list[0])

        try:
            import_cmd = """
include("harmony_utility_functions.js");

var publish_path = "%s";
var img_folder = "%s";
var img_name = "%s";
var img_extension = "%s";
var img_count = %s;
var img_scale_multiplier = %s;

import_image_sequence(publish_path, img_folder, img_name, img_extension, img_count, img_scale_multiplier);
            """ % (
                publish_path,
                img_folder,
                img_name,
                img_extension,
                img_count,
                img_scale_multiplier,
            )

            sequence_read_node = engine.app.execute(import_cmd)
            self.parent.engine.logger.info(
                "succesfully created node: {}".format(sequence_read_node)
            )
        except:
            self.parent.engine.logger.error(
                "Could not import image sequence: {},\nError:\n{}".format(
                    sequence_path, traceback.format_exc()
                )
            )
            msg = "Could not import image sequence: {}".format(sequence_path)
            self.parent.engine.show_message(msg, level="error")
        finally:
            self.parent.engine.clear_busy()

    def _import_tpl(self, tpl_path):
        engine = sgtk.platform.current_engine()
        tpl_path = self.fix_path(tpl_path)

        import_tpl_cmd = """
include("harmony_utility_functions.js");

var tpl_path = "%s";
var tpl_nodes = import_tpl(tpl_path);
""" % (
            # META_SHOTGUN_PATH,
            tpl_path,
        )

        self.parent.engine.logger.debug("import_tpl_cmd:\n{}".format(import_tpl_cmd))
        engine.app.execute(import_tpl_cmd)

    # ==================================================================================

    def _import_camera(self, camera_path):
        """
        Create a new camera and its peg using the data from the previously exported
        jsonx file.

        Applies position (as 3dpath) and rotation (as individual bezier curves) but
        ignores scale values as the camera should never be scaled.

        It forces the camera to be a 3d camera and also it forces rotation to be in
        Euler angles instead of quaternions. This is required because that's how
        the camera is exported.
        """

        self.parent.engine.logger.info("Importing camera start...")

        # General cam node settings
        camera_node_color = [5, 5, 255, 255]
        camera_node_color_str = str(camera_node_color)[1:-1]
        camera_attr_name = "mtyShotCamera"
        camera_attr_type = "BOOL"

        # Replace back slashes with forward slashes in original path
        camera_path = self.fix_path(camera_path)

        # Get pos and rot keyframes data from json file
        with open(camera_path, "r") as json_file:
            camera_data = json.load(json_file)

        pos_keyframes = camera_data[1].get("position")
        rot_keyframes = camera_data[1].get("rotation")

        pos_keyframes_str = "{};".format(str(pos_keyframes))
        rot_keyframes_str = "{};".format(str(rot_keyframes))
        self.parent.engine.logger.debug(
            "pos_keyframes_str: {}".format(pos_keyframes_str)
        )
        self.parent.engine.logger.debug(
            "rot_keyframes_str: {}".format(rot_keyframes_str)
        )

        context = self.parent.engine.context
        self.parent.engine.logger.debug("context:\n{}".format(pprint.pformat(context)))
        if context.entity["type"] == "Shot":
            shot_name = context.entity["name"]
            camera_node_name = "{}-Camera".format(shot_name)
        else:
            error_msg = (
                "Couldn't get shot name from context. Please make sure you "
                "are working in an Animation task of a shot."
            )
            # self.parent.engine.show_message(error_msg)
            QtGui.QMessageBox.warning(
                None, "Couldn't get shot name from context", error_msg
            )

            raise Exception(error_msg)

        create_shot_camera_node_cmd = """
include("harmony_utility_functions.js");

/*
function setNodeMetadata(nodeName, attrName, value)
{
    var visualAttrName = attrName;
    var idx = attrName.lastIndexOf(".");
    if (idx >= 0)
    {
      visualAttrName = attrName.substr(idx + 1);
    }

    var attr = node.getAttr(nodeName, 1.0, attrName);
    if(attr.keyword() == "")
    {
        if (node.createDynamicAttr(nodeName, "STRING", attrName, visualAttrName, false))
        {
          attr = node.getAttr(nodeName, 1.0, attrName);
        }

        if (attr.keyword() != "")
        {
            node.setTextAttr(nodeName, attrName, 1.0, value || visualAttrName);
        }
    }
    else
    {
        node.setTextAttr(nodeName, attrName, 1.0, value || visualAttrName);
    }
};
*/

function createNode(nodeType, prevNode, color, nodeName, xPos, yPos)
{
    var prevNodeParent = node.parentNode(prevNode);

    if (nodeType == "PEG")
    {
        var newNode = node.add(
            prevNode,
            nodeName,
            nodeType,
            xPos,
            yPos,
            0
        );
    } else if (nodeType == "CAMERA")
    {
        var prevNodeXpos = node.coordX(prevNode);
        var prevNodeYpos = node.coordY(prevNode);
        var prevNodeZpos = node.coordZ(prevNode);
        var newNode = node.add(
            prevNodeParent,
            nodeName,
            nodeType,
            prevNodeXpos,
            prevNodeYpos + 100,
            prevNodeZpos
        );
    };

    if (nodeType != "PEG")
    {
        node.link(prevNode, 0, newNode, 0);
    }
    node.setColor(newNode, color);
    MessageLog.trace("Created node: " + node.getName(newNode));

    return newNode;
};

/*
function create_attr(nodeName, attrName, attrType)
{
    var attr = node.getAttr(nodeName, 1.0, attrName);
    if (attr.keyword() == "")
    {
        var visualAttrName = attrName;
        if (node.createDynamicAttr(nodeName, attrType, attrName, visualAttrName, false))
        {
            attr = node.getAttr(nodeName, 1.0, attrName);
        };
        if (attr.keyword() != "")
        {
            node.setTextAttr(nodeName, attrName, 1.0, "true");
        };
    };
    return attr
};
*/

function createShotCameraNode(nodeName, filepath)
{
    // var selectedNodes = selection.numberOfNodesSelected();

    var createdNodesArray = [];

    var good_position = get_good_scene_position(0, 100);

    // var xPos = -600;
    // var yPos = -150;
    var xPos = good_position.x;
    var yPos = good_position.y;

    // var cameraNodeColor = new ColorRGBA(0, 0, 255, 255);
    var cameraNodeColor = new ColorRGBA(%s);

    // create parent peg for the camera
    var pegNode = createNode(
        "PEG",
        "Top",
        cameraNodeColor,
        nodeName + "-PEG",
        xPos,
        yPos
    );

    createdNodesArray.push(pegNode);

    // create the camera itself
    var cameraNode = createNode(
        "CAMERA",
        pegNode,
        cameraNodeColor,
        nodeName,
        null,
        null
    );

    //add SG metadata (path) as a string attr
    set_node_metadata(cameraNode, META_SHOTGUN_PATH, filepath);

    // var mty_attr = create_attr(cameraNode, "mtyShotCamera", "BOOL")
    var mty_attr = create_attr(cameraNode, "%s", "%s")
    // MessageLog.trace("attr name: " + mty_attr.name())
    // MessageLog.trace("attr value: " + mty_attr.boolValue())

    // move camera into initial position: 0, 0, 12
    var pos_attr = node.getAttr(cameraNode, 1, "OFFSET");
    var pos_value = pos_attr.pos3dValue();
    pos_value.setXYZ(0.0, 0.0, 0.0);
    pos_attr.setValue(pos_value);

    // Lock the camera node
    node.setLocked(cameraNode, true);

    createdNodesArray.push(cameraNode);

    return createdNodesArray;
};

function collect_shot_camera_node(nodeName)
{
    // Get the passthrough nodes by attr
    var array_of_node_types = ["CAMERA"];
    var array_of_nodes = node.getNodes(array_of_node_types);

    var array_of_shot_camera_nodes = [];
    for (var i = 0; i < array_of_nodes.length; ++i)
    {
        var node_path = array_of_nodes[i];
        MessageLog.trace("checking node: " + node_path)
        // var attr = node.getAttr(node_path, 1.0, "mtyShotCamera");
        var attr = node.getAttr(node_path, 1.0, "%s");
        // MessageLog.trace(attr.name())
        // MessageLog.trace(attr.typeName())
        // MessageLog.trace(attr.boolValue())
        if (attr != null)
        {
            // if (attr.keyword() != "" && attr.typeName() == "BOOL")
            if (attr.keyword() != "" && attr.typeName() == "%s")
            {
                if (attr.boolValue() == true)
                {
                    if (node.getName(node_path) == nodeName)
                    {
                        MessageLog.trace("Found shot camera node: " + node_path);
                        array_of_shot_camera_nodes.push(node_path);
                    }
                };
            };
        };
    };
    return array_of_shot_camera_nodes;
};

function set_cam_peg_attributes(cam_peg_node)
{
    var is_3d_attr = node.getAttr(cam_peg_node, 1, "ENABLE_3D");
    is_3d_attr.setValue(true);

    var rot_separate_attr = node.getAttr(cam_peg_node, 1, "ROTATION.SEPARATE");
    rot_separate_attr.setValue(true);
};

function add_position_keyframes(cam_peg_node, pos_keyframes, pos_col_name)
{
    for (var i = 0; i < pos_keyframes.length; ++i)
    {
        var frame = pos_keyframes[i][0] + 1;
        var pos_x = pos_keyframes[i][1];
        var pos_y = pos_keyframes[i][2];
        var pos_z = pos_keyframes[i][3];

        // Convert from OGL coordinates to field coordinates
        pos_x = scene.fromOGLX(pos_x);
        pos_y = scene.fromOGLY(pos_y);
        pos_z = scene.fromOGLZ(pos_z);

        func.addKeyFramePath3d(
            pos_col_name, frame, pos_x, pos_y, pos_z, 0, 0, 0
        );
        MessageLog.trace(
            "Added position key for frame " + frame + ": " + pos_x + ", " + pos_y + ", " + pos_z
        );
    };

    node.linkAttr(cam_peg_node, "POSITION.3DPATH", pos_col_name);
};

function add_rotation_keyframes(
    cam_peg_node,
    rot_keyframes,
    rot_x_col_name,
    rot_y_col_name,
    rot_z_col_name
)
{
    for (var i = 0; i < rot_keyframes.length; ++i)
    {
        var frame = rot_keyframes[i][0];
        var rot_x = rot_keyframes[i][1];
        var rot_y = rot_keyframes[i][2];
        var rot_z = rot_keyframes[i][3];

        func.setBezierPoint(
            rot_x_col_name, frame, rot_x, 0, 0, 0, 0, false, "SMOOTH"
        );
        func.setBezierPoint(
            rot_y_col_name, frame, rot_y, 0, 0, 0, 0, false, "SMOOTH"
        );
        func.setBezierPoint(
            rot_z_col_name, frame, rot_z, 0, 0, 0, 0, false, "SMOOTH"
        );
        MessageLog.trace(
            "Added rotation key for frame " + frame + ": " + rot_x + ", " + rot_y + ", " + rot_z
        );
    };

    node.linkAttr(cam_peg_node, "ROTATION.ANGLEX", rot_x_col_name);
    node.linkAttr(cam_peg_node, "ROTATION.ANGLEY", rot_y_col_name);
    node.linkAttr(cam_peg_node, "ROTATION.ANGLEZ", rot_z_col_name);
};


// var nodeName = "testShot_Camera";
var nodeName = "%s";

var array_of_shot_camera_nodes = collect_shot_camera_node(nodeName);
MessageLog.trace("found " + array_of_shot_camera_nodes.length + " shot cameras");

if (array_of_shot_camera_nodes.length == 0)
{
    var createdNodesArray = createShotCameraNode(nodeName, "%s");

    var cam_peg_node = createdNodesArray[0];
    MessageLog.trace("cam_peg_node: " + cam_peg_node);

    // set peg attributes
    set_cam_peg_attributes(cam_peg_node);

    // Columns shoudn't be connected to anything if the camera nodes don't exist in
    // the scene, so we need to first remove the columns and then recreate them again
    // to have clean empty columns before adding the keys from the pubished camera

    var pos_col_name = "cam_pos_col";

    try {
        column.removeUnlinkedFunctionColumn(pos_col_name);
    } catch (error) {
        // pass if the column doesn't exist or if it can't be removed
    };

    // Create anim column
    var pos_col = column.add(pos_col_name, "3DPATH");

    var rot_x_col_name = "cam_rot_x_col";
    var rot_y_col_name = "cam_rot_y_col";
    var rot_z_col_name = "cam_rot_z_col";

    try {
        column.removeUnlinkedFunctionColumn(rot_x_col_name);
        column.removeUnlinkedFunctionColumn(rot_y_col_name);
        column.removeUnlinkedFunctionColumn(rot_z_col_name);
    } catch (error) {
        // pass if the columns don't exist or if they can't be removed
    };

    // Create anim columns
    var rot_x_col = column.add(rot_x_col_name, "BEZIER");
    var rot_y_col = column.add(rot_y_col_name, "BEZIER");
    var rot_z_col = column.add(rot_z_col_name, "BEZIER");

    // Get keyframes arrays
    var pos_keyframes = %s
    var rot_keyframes = %s

    // Add position keyframes and connect the peg's POSITION attr to the anim column
    //  where we added those keys
    add_position_keyframes(cam_peg_node, pos_keyframes, pos_col_name);

    // Add rotation keyframes and connect the individual peg's ROTATION attrs (as
    // individual BEZIER function curves) to the anim columns where we added those keys
    add_rotation_keyframes(
        cam_peg_node,
        rot_keyframes,
        rot_x_col_name,
        rot_y_col_name,
        rot_z_col_name
    );

    node.setLocked(cam_peg_node, true);

    selection.clearSelection();
    selection.addNodesToSelection(createdNodesArray);

    MessageBox.information("Imported published camera.");
}else{
    selection.clearSelection();
    selection.addNodesToSelection(array_of_shot_camera_nodes);
    MessageBox.information(
        "Shot camera already exists, you don't need to create it again! \
  If you want to create it again from scratch, you need to delete the existing \
camera first."
    );
};

""" % (
            # META_SHOTGUN_PATH,
            camera_node_color_str,
            camera_attr_name,
            camera_attr_type,
            camera_attr_name,
            camera_attr_type,
            camera_node_name,
            camera_path,
            pos_keyframes_str,
            rot_keyframes_str,
        )

        self.parent.engine.logger.debug(create_shot_camera_node_cmd)
        self.parent.engine.app.execute(create_shot_camera_node_cmd)

        self.parent.engine.logger.info(
            "Imported published camera '{}'".format(camera_path)
        )

    # ==================================================================================

    def _get_image_width(self, image_path):
        """
        Get's the original image resolution from input path.
        """

        result = None

        self.parent.engine.logger.info("loading mty-framework-ffmpeg")
        ffmpeg = self.load_framework("mty-framework-ffmpeg")
        FFmpegCoreTools = ffmpeg.ffmpegCore
        FFmpegCoreTools.set_binary(binary="media")
        ffprobe_path = os.path.dirname(ffmpeg.ffmpegCore.get_bin_path())

        self.parent.engine.logger.info("ffprobe path: {}".format(ffprobe_path))

        ffprobe_cmd = (
            "-hide_banner "
            "-loglevel panic "
            "-of json "
            "-show_entries stream=width "
            "{}"
        ).format(image_path)

        self.parent.engine.logger.info("ffprobe_cmd: {}".format(ffprobe_cmd))

        _err, _info = FFmpegCoreTools.execute_command(ffprobe_cmd)

        if not _err:
            data = dict(json.loads(_info))
            streams = data.get("streams")
            if streams:
                result = streams[0].get("width")
            self.parent.engine.logger.info("width: {}".format(result))

        return result

    # def _get_project_resolution_from_SG(self):
    #     project_data = {}

    #     filters = [
    #         ["id", "is", self.parent.engine.context.project["id"]],
    #     ]
    #     fields = ["sg_resolution"]

    #     project_data = self.parent.engine.shotgun.find_one(
    #         entity_type="Project", filters=filters, fields=fields
    #     )

    #     return project_data

    def _get_image_scale_multiplier(self, image_path):
        # Return if the image_path doesn't exist on disk
        if not image_path or not os.path.exists(image_path):
            error_msg = "Couldn't get image path or it doesn't exist on disk."
            self.parent.engine.logger.error(error_msg)
            # self.parent.engine.show_message(error_msg)
            QtGui.QMessageBox.error(None, "Invalid image path", error_msg)
            return None

        image_width = self._get_image_width(image_path)
        # Return if image_width couldn't be obtained using ffprobe
        if not image_width:
            error_msg = "Couldn't get image width."
            self.parent.engine.logger.error(error_msg)
            # self.parent.engine.show_message(error_msg)
            QtGui.QMessageBox.error(None, "Invalid image width", error_msg)
            return None

        # Get project resolution from SG
        # project_data = self._get_project_resolution_from_SG()
        # project_resolution = project_data.get("sg_resolution")
        hook_expression = "{config}/get_entity_resolution.py"
        project_resolution = self.parent.engine.execute_hook_expression(
            hook_expression,
            "get_resolution",
            engine=self.parent.engine,
        )

        if not project_resolution:
            error_msg = "Couldn't get project resolution from SG."
            self.parent.engine.logger.error(error_msg)
            # self.parent.engine.show_message(error_msg)
            QtGui.QMessageBox.error(None, "Invalid project data", error_msg)
            return None

        pattern = re.compile(r"(?P<width>\d+)x(?P<height>\d+)")
        match = re.match(pattern, str(project_resolution))
        if not match:
            error_msg = "Couldn't get image resolution."
            self.parent.engine.logger.error(error_msg)
            # self.parent.engine.show_message(error_msg)
            QtGui.QMessageBox.error(None, "Invalid image resolution", error_msg)
            return None

        project_width = int(match.groupdict()["width"])
        project_height = int(match.groupdict()["height"])

        # image_scale_multiplier = 1
        image_scale_multiplier = float(image_width) / float(project_width)

        return image_scale_multiplier

    def _convert_image_to_tvg(self, image_path):
        # Return if the image_path doesn't exist on disk
        if not image_path or not os.path.exists(image_path):
            error_msg = "Couldn't get image path or it doesn't exist on disk."
            self.parent.engine.logger.error(error_msg)
            # self.parent.engine.show_message(error_msg)
            QtGui.QMessageBox.error(None, "Invalid image path", error_msg)
            return None

        # Get pix2vec executable path
        software_path = os.environ.get("CURRENT_SOFTWARE_PATH")
        pix_to_vector_path = os.path.join(os.path.dirname(software_path), "Pix2vec")
        self.parent.engine.logger.info("path to Pix2vec: {}".format(pix_to_vector_path))
        if "win" in sys.platform:
            pix_to_vector_path = "{}.exe".format(pix_to_vector_path)

        # Return if the executable path doesn't exist on disk
        # if not os.path.exists(pix_to_vector_path):
        #     error_msg = ("Couldn't get Pix2vec path to convert image to tvg (vector).")
        #     self.parent.engine.logger.error(error_msg)
        #     # self.parent.engine.show_message(error_msg)
        #     QtGui.QMessageBox.error(
        #         None, "Invalid Pix2vec path", error_msg
        #     )
        #     return None

        # Solve temp tvg path based on the image_path basename
        image_basename = os.path.basename(image_path)
        image_split_ext = os.path.splitext(image_basename)
        tmp_tvg_basename = "{}.tvg".format(image_split_ext[0])

        tmp_dir = tempfile.mkdtemp()
        tmp_tvg_path = os.path.join(tmp_dir, tmp_tvg_basename)

        # fix paths
        # pix_to_vector_path = pix_to_vector_path.replace("\\\\", "/")
        # pix_to_vector_path = pix_to_vector_path.replace("\\", "/")
        # image_path = image_path.replace("\\\\", "/")
        # image_path = image_path.replace("\\", "/")
        # tmp_tvg_path = tmp_tvg_path.replace("\\\\", "/")
        # tmp_tvg_path = tmp_tvg_path.replace("\\", "/")

        self.parent.engine.logger.info(
            "pix_to_vector_path: {}".format(pix_to_vector_path)
        )
        self.parent.engine.logger.info("image_path: {}".format(image_path))
        self.parent.engine.logger.info("tmp_tvg_path: {}".format(tmp_tvg_path))

        # Build command to convert image to tvg
        command = [
            # '"{}"'.format(pix_to_vector_path),
            pix_to_vector_path,
            "-infile",
            # '"{}"'.format(image_path),
            image_path,
            "-color_vectorize",
            "-asBitmap",
            "-outfile",
            # '"{}"'.format(tmp_tvg_path)
            tmp_tvg_path,
        ]
        self.parent.engine.logger.info("convert_to_tvg cmd: {}".format(command))

        # Convert image to tvg (vector) using command line
        proc = subprocess.Popen(
            command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True
        )
        stdout, stderr = proc.communicate()
        stdout = six.ensure_str(stdout)
        stderr = six.ensure_str(stderr)

        self.parent.engine.logger.info("convert to tvg stdout: {}".format(stdout))
        self.parent.engine.logger.info("convert to tvg stderr: {}".format(stderr))

        if os.path.exists(tmp_tvg_path):
            return tmp_dir, tmp_tvg_path
        else:
            return tmp_dir, None

    def _import_image_project_resolution(self, image_path):
        image_scale_multiplier = self._get_image_scale_multiplier(image_path)

        # fix image_path
        image_path = self.fix_path(image_path)

        # Arguments guide:
        # filename, parent, transparency, alignmentRule,
        # vectorFormat, extension, scale_multiplier
        import_image_proj_res_cmd = """
include("loader_import_images.js");
custom_import_image(
  "%s", "%s", %s, "%s", %s, %s, %s
);
""" % (
            image_path,
            "Top",
            "true",
            "As Is",
            "null",
            "null",
            image_scale_multiplier,
        )

        self.parent.engine.logger.debug(import_image_proj_res_cmd)
        imported_image_node = self.parent.engine.app.execute(import_image_proj_res_cmd)

        self.parent.engine.logger.info(
            "Imported image as bitmap (project resolution): {}".format(
                imported_image_node
            )
        )

        return imported_image_node

    def _import_image_as_vector(self, image_path):
        # fix image_path
        image_path = image_path.replace("\\\\", "/")
        image_path = image_path.replace("\\", "/")

        import_vector_image_cmd = (
            """
// include("vectorize_utils_mty.js");
var Utils = require("vectorize_utils_mty.js");


// create options dict for utils file
/*
explanation:

var options = {
   elementId : 1, node : "",                             // elementId or node must be set to either the elementId to importTo or the Element Module linked to the element to import to.
   showProgressUI : true,                                // If set to false, will not show progress bar. Useful if doing batch processing
   noScale : true,                                       // If true, will not scale the bitmap when importing to TVG. Will use the default scaling.
   importType : Utils.IMPORT_TYPE.TVG_VECTOR,            // One of the IMPORT_TYPE enum
   bitmapAlignment : Utils.ALIGNMENT.FIT,                // One of the ALIGNMENT enum
   premultiply : Utils.PREMULTIPLY.STRAIGHT,             // One of the PREMULTIPLY enum
   timing: "",                                           // The timing of the drawing in case of single layer file
   timingPrefix : "myfile-",                             // The timing prefix to which the layer name will be appended in case of multi layer file
   forceSingleLayer : true,                              // If true, will for multi-layer bitmap (e.g. PSD) to single layer
   vectorizeOptions : ["-color_vectorize", "-asBitmap"]  // The options to pass to the Vectorize function.
};
*/

var options = {
   showProgressUI : true,
   noScale : true,
   importType : Utils.IMPORT_TYPE.TVG_VECTOR,
   bitmapAlignment : Utils.ALIGNMENT.FIT,
   premultiply : Utils.PREMULTIPLY.STRAIGHT,
   timing: 1,
   forceSingleLayer : true,
   vectorizeOptions : ["-color_vectorize", "-asBitmap"]
};


Utils.importDrawingInNewElementNode("%s", options);

"""
            % image_path
        )

        self.parent.engine.logger.debug(import_vector_image_cmd)
        self.parent.engine.app.execute(import_vector_image_cmd)

        self.parent.engine.logger.info("Imported image as vector")

    def _convert_movie_to_image_seq(self, movie_path):
        """
        Convert movie to image sequence in a temporary directory
        """

        self.parent.engine.logger.info("Converting movie to sequence...")

        if not os.path.exists(movie_path):
            msg = "Movie does not exist on disk: {}".format(movie_path)
            self.parent.engine.logger.error(msg)
            self.parent.engine.show_message(msg, level="error")
            return None

        self.parent.engine.logger.info("loading mty-framework-ffmpeg")
        ffmpeg = self.parent.engine.custom_frameworks.get(
            "mty-framework-ffmpeg"
        ) or self.load_framework("mty-framework-ffmpeg")
        FFmpegCoreTools = ffmpeg.ffmpegCore
        FFmpegCoreTools.set_binary(binary="convert")
        ffmpeg_path = os.path.dirname(ffmpeg.ffmpegCore.get_bin_path())

        movie_basename = os.path.basename(movie_path)
        movie_basename, extension = os.path.splitext(movie_basename)
        sequence_basename = "{}-%01d.png".format(movie_basename)

        tmp_dir = tempfile.mkdtemp()
        sequence_path = os.path.join(tmp_dir, sequence_basename)
        sequence_path = self.fix_path(sequence_path)

        self.parent.engine.logger.info("ffmpeg path: {}".format(ffmpeg_path))

        ffmpeg_cmd = ("-hide_banner " "-i {} " "{}").format(movie_path, sequence_path)

        self.parent.engine.logger.info("ffmpeg_cmd: {}".format(ffmpeg_cmd))
        _err, _info = FFmpegCoreTools.execute_command(ffmpeg_cmd)

        if _err:
            msg = "Error converting movie to sequence: {}".format(_err)
            self.parent.engine.logger.error(msg)
            self.parent.engine.show_message(msg, level="error")
            return None

        return sequence_path

    def _convert_img_seq_to_png_img_seq(self, sequence_path):
        """
        Convert existing image sequence to a pngsequence in a temporary directory,
        using Harmony's naming convention
        """

        self.parent.engine.logger.info("Converting image sequence to png sequence...")

        engine = self.parent.engine
        sequence_files_list, fileseq_obj = self._collect_sequenced_files(sequence_path)

        if not sequence_files_list:
            msg = "No image sequence found on disk: {}".format(sequence_path)
            self.parent.engine.logger.error(msg)
            self.parent.engine.show_message(msg, level="error")
            return

        self.parent.engine.logger.info("loading mty-framework-ffmpeg")
        ffmpeg = self.parent.engine.custom_frameworks.get(
            "mty-framework-ffmpeg"
        ) or self.load_framework("mty-framework-ffmpeg")
        FFmpegCoreTools = ffmpeg.ffmpegCore
        FFmpegCoreTools.set_binary(binary="convert")
        ffmpeg_path = os.path.dirname(ffmpeg.ffmpegCore.get_bin_path())

        self.parent.engine.logger.info("ffmpeg path: {}".format(ffmpeg_path))

        seq_first_frame = fileseq_obj.start()

        sequence_basename = fileseq_obj.basename()
        # remove last characters if they are not an alphanumeric
        sequence_basename = self._remove_head_non_alphanumeric(sequence_basename)
        sequence_basename = self._remove_tail_non_alphanumeric(sequence_basename)

        # get_tmp sequence path
        tmp_dir = tempfile.mkdtemp()
        output_seq_basename = "{}-%01d.png".format(sequence_basename)
        output_sequence_path = os.path.join(tmp_dir, output_seq_basename)

        # fix paths with forward slashes only
        sequence_path = self.fix_path(sequence_path)
        output_sequence_path = self.fix_path(output_sequence_path)

        # build ffmpeg command
        ffmpeg_cmd = ("-hide_banner " "-start_number {} " "-i {} " "{}").format(
            seq_first_frame, sequence_path, output_sequence_path
        )

        self.parent.engine.logger.info("ffmpeg_cmd: {}".format(ffmpeg_cmd))
        _err, _info = FFmpegCoreTools.execute_command(ffmpeg_cmd)

        if _err:
            msg = "Error converting image sequence to png sequence: {}".format(_err)
            self.parent.engine.logger.error(msg)
            self.parent.engine.show_message(msg, level="error")
            return None

        return output_sequence_path

    def _import_movie(self, movie_path):
        engine = self.parent.engine

        # fix path with forward slashes only
        movie_path = self.fix_path(movie_path)

        # Show a new busy message
        engine.clear_busy()
        engine.show_busy("Importing movie", "Converting movie to file sequence...")

        try:
            sequence_path = self._convert_movie_to_image_seq(movie_path)
        except:
            msg = "Error converting movie to sequence, error:\n{}".format(
                traceback.format_exc()
            )
            self.parent.engine.logger.error(msg)
            self.parent.engine.show_message(msg, level="error")
        finally:
            engine.clear_busy()

        if not sequence_path:
            msg = "Error converting movie to sequence, error:\n{}".format(
                traceback.format_exc()
            )
            self.parent.engine.logger.error(msg)
            self.parent.engine.show_message(msg, level="error")
            return None

        # Show a new busy message
        engine.show_busy("Importing movie", "Importing image sequence...")

        try:
            self._import_image_sequence(movie_path, sequence_path)
        except:
            msg = "Error importing image sequence, error:\n{}".format(
                traceback.format_exc()
            )
            self.parent.engine.logger.error(msg)
            self.parent.engine.show_message(msg, level="error")
        finally:
            engine.clear_busy()
            return None

    def _import_3d_mesh(self, objet3d_path):
        # fix image_path
        objet3d_path = self.fix_path(objet3d_path)

        # Arguments guide:
        # filename, parent, transparency, alignmentRule,
        # vectorFormat, extension, scale_multiplier
        import_image_proj_res_cmd = """
include("loader_import_3dObjet.js");
custom_import_3dObjet(
  "%s", "%s"
);
""" % (
            objet3d_path,
            "Top",
        )

        self.parent.engine.logger.debug(import_image_proj_res_cmd)
        self.parent.engine.app.execute(import_image_proj_res_cmd)

        self.parent.engine.logger.info("Imported 3D objet into the scene")

    def _import_color_palette(self, color_palette_path):
        current_project_path = self.parent.engine.app.get_current_project_path()
        scene_palettes_path = os.path.join(
            os.path.dirname(current_project_path), "palette-library"
        )

        # fix paths with forward slashes only
        color_palette_path = self.fix_path(color_palette_path)
        scene_palettes_path = self.fix_path(scene_palettes_path)
        local_palette_path = os.path.join(
            scene_palettes_path, os.path.basename(color_palette_path)
        )
        local_palette_path = self.fix_path(local_palette_path)
        palette_name, _ = os.path.splitext(os.path.basename(color_palette_path))
        self.parent.engine.logger.info(f"scene_palettes_path: {scene_palettes_path}")
        self.parent.engine.logger.info(f"local_palette_path: {local_palette_path}")
        self.parent.engine.logger.info(f"palette_name: {palette_name}")

        if os.path.exists(local_palette_path):
            # button clicked describes which button has been pressed:
            # 0 means cancel, 1 means delete
            button_clicked = self.create_existing_palette_dialog(palette_name)

            self.parent.engine.logger.info(f"button_clicked: {button_clicked}")
            if button_clicked == 0:
                return None
            elif button_clicked == 1:
                # remove existing palette from the scene
                delete_palette_cmd = """
include("harmony_utility_functions.js");

var log = MessageLog.trace;
var palette_name = "%s";

var scene_palette_list = get_scene_palette_list();

function remove_palette_from_scene() {
    for (var i = 0; i < scene_palette_list.numPalettes; i++) {
        var palette = scene_palette_list.getPaletteByIndex(i);
        log("--------------------------------------");
        log("Palette name: " + palette.getName());
        log("Palette path: " + palette.getPath());
        if (palette.getName() == palette_name) {
            var palette_id = palette.id;
            scene_palette_list.removePaletteById(palette_id);
            log("Palette " + palette_name + " removed");
            return true;
        }
    }
    return false;
}

remove_palette_from_scene();

""" % palette_name

                self.parent.engine.logger.debug(delete_palette_cmd)
                deleted_palette = self.parent.engine.app.execute(delete_palette_cmd)
                self.parent.engine.logger.info(
                    f"Deleted existing palette from the scene: {deleted_palette}"
                )

                # delete existing palette from disk
                os.remove(local_palette_path)
                self.parent.engine.logger.info(
                    f"Deleted existing palette from disk: {local_palette_path}"
                )

        shutil.copy(color_palette_path, scene_palettes_path)

        if not os.path.exists(local_palette_path):
            self.parent.engine.logger.error("Failed to copy palette file")
            return None

        import_color_palette_cmd = """
include("harmony_utility_functions.js");

var log = MessageLog.trace;
var palette_name = "%s";

var scene_palette_list = get_scene_palette_list();
var num_of_palettes = scene_palette_list.numPalettes;

var scene_palettes_location = PaletteObjectManager.Constants.Location.SCENE;

scene_palette_list.addPaletteAtLocation(scene_palettes_location, null, palette_name, num_of_palettes);

""" % palette_name

        self.parent.engine.logger.debug(import_color_palette_cmd)
        self.parent.engine.app.execute(import_color_palette_cmd)

        self.parent.engine.logger.info("Imported color palette into the scene")

    def _remove_head_non_alphanumeric(self, input_string):
        while not input_string[0].isalnum():
            input_string = input_string[1:]
        return input_string

    def _remove_tail_non_alphanumeric(self, input_string):
        while not input_string[-1].isalnum():
            input_string = input_string[:-1]
        return input_string

    def create_existing_palette_dialog(self, palette_name):
        message = (
            f"Color palette '{palette_name}' already exists. "
            f"Do you want to overwrite it?"
        )
        # Create the dialog
        dialog = QDialog()
        dialog.setWindowTitle("Confirmation")

        # Layout
        layout = QVBoxLayout()
        dialog.setLayout(layout)

        # Message label
        label = QLabel(message)
        layout.addWidget(label)

        # Buttons
        delete_button = QPushButton("Delete")
        cancel_button = QPushButton("Cancel")
        cancel_button.setDefault(True)  # Set Cancel button as default
        layout.addWidget(delete_button)
        layout.addWidget(cancel_button)

        # Function for Delete button click
        def on_delete_clicked():
            dialog.done(1)  # Return 1 for Delete button

        # Function for Cancel button click
        def on_cancel_clicked():
            dialog.done(0)  # Return 0 for Cancel button

        # Connect button signals to functions
        delete_button.clicked.connect(on_delete_clicked)
        cancel_button.clicked.connect(on_cancel_clicked)

        # Execute the dialog
        dialog.exec_()

        # Return the result
        return dialog.result()
