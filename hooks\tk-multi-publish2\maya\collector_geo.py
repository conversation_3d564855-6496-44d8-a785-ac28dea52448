# -*- coding: utf-8 -*-
# Standard library:
import os
import sys

#   .   .   .   .   .   .   .   .   .   .   .
import sgtk

#   .   .   .   .   .   .   .   .   .   .   .
# Project:
# ================================================================


HookBaseClass = sgtk.get_hook_baseclass()


class geometryCollector(HookBaseClass):
    """
    Collector that operates on Autodesk Maya session. Should inherit
    from the basic collector hook.
    """

    def __init__(self, parent):
        super(geometryCollector, self).__init__(parent)

    def process_current_session(self, settings, parent_item):
        super(geometryCollector, self).process_current_session(settings, parent_item)

        if not parent_item.type_spec.endswith(".session"):
            item = next(
                (i for i in parent_item.descendants if i.type_spec.endswith(".session")),
                parent_item,
            )
        else:
            item = parent_item

        geo = self._collect_geometry(settings, item)

    def _collect_geometry(self, settings, parent_item):
        self.logger.debug("Collecting geo files...")
        context = self.parent.context

        icon_path = os.path.join(
            self.disk_location, os.pardir, "icons", "geo_publish.png"
        )

        self.parent.logger.debug("icon_path: {}".format(icon_path))

        step_name = context.step["name"].lower()

        # if step_name == "model":
        if step_name in "model":
            geometry_node_item = parent_item.create_item(
                "maya.session.geometry", "Geometry in session", "Geometry Collector"
            )

            self.parent.engine.execute_hook_expression(
                "{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py",
                "use_farm_or_local_processing",
                item = geometry_node_item
            )

            geometry_node_item.set_icon_from_path(icon_path)

            self.logger.debug("Collected Geometry")
