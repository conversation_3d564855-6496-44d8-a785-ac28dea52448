########################################################################################
#
# Copyright (c) 2024 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty Animation Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
########################################################################################

import os
import imp
import sgtk

HookBaseClass = sgtk.get_hook_baseclass()


class GenericHook(HookBaseClass):
    """Class to wrap execution of external scripts as hook expressions. Uses keyword
    arguments to pass in the script arguments.
    """

    def execute_script(self, **kwargs):
        """
        Executes a script passing the specified arguments.

            Args:
                **kwargs: Any arguments that should be passed to the script
                        One of the keywords MUST be "path_to_script" to be able to run
                        the script. Should be a path visible by the computer that will
                        execute the script (ex, Deadline workers). As safety measure,
                        the script checks if the script exists with
                        os.path.exists(path_to_script).

                        Any other arguments needed by the script should exist as
                        keywords, and the script itself is responsible for extracting
                        them from kwargs. The script itself (external_module) MUST have
                        a main() function, because that's what we call. Kwargs are
                        are passed as they are to the main() function.

            Returns:
                None
        """

        if not kwargs:
            self.parent.logger.error("generic_hook, Couldn't get kwargs")
            return

        # get the path to the script that we want to execute from the kwargs
        path_to_script = kwargs.get("path_to_script", None)
        if not path_to_script:
            self.parent.logger.error("generic_hook - Couldn't get path to script")
            return

        # ensure the script exists on disk
        if not os.path.exists(path_to_script):
            self.parent.logger.error(
                f"generic_hook - Script doesn't exist on disk: {path_to_script}"
            )
            return

        # add logger to kwargs so the script can still log using the existing logger
        kwargs["logger"] = self.parent.logger

        # load the script as a module to run the main() function
        # https://www.geeksforgeeks.org/how-to-import-a-python-module-given-the-full-path/

        # load the script as a module
        external_module = imp.load_source('external_module', path_to_script)

        # finally call the main funtion in the script, passing kwargs as unique argument
        # Any argument parsing from kwargs must be done in the script itself
        external_module.main(**kwargs)
