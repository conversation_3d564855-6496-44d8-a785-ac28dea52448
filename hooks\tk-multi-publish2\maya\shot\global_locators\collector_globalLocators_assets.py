# -*- coding: utf-8 -*-
# Standard library:
import os
import re
import pprint

# ___   ___   ___   ___   ___   ___  ___
# Third party:
import sgtk
import maya.mel as mel
import maya.cmds as cmds

# ___   ___   ___   ___   ___   ___  ___
# Project:
from tank_vendor import six

# ====================================================================
pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)
pf = pprint.pformat

HookBaseClass = sgtk.get_hook_baseclass()

class CollectorShotGlobalLocators(HookBaseClass):
    @property
    def name(self):
        return "CollectorShotGlobalLocators"

    @property
    def settings(self):
        collector_settings = super(CollectorShotGlobalLocators, self).settings or {}

        maya_shot_settings = {
            "Work Template": {
                "type": "template",
                "default": "",
                "description": (
                    "Template path for artist work files."
                    "Should correspond to a template defined in "
                    "templates.yml. If configured, is made available"
                    "to publish plugins via the collected item's "
                    "properties. "
                ),
            },
            "global_locators_supported_names": {
                "type": "list",
                "default": [],
                "description": (
                    "Only assets for this asset types should be "
                    "considered to bake locators and export as cache."
                ),
            },
            "global_locators_supported_assets": {
                "type": "list",
                "default": [],
                "description": (
                    "Only assets for this asset types should be "
                    "considered to bake locators and export as cache."
                ),
            },
        }

        collector_settings.update(maya_shot_settings)

        return collector_settings

    # ----------------------------------------------------------------------------------

    def process_current_session(self, settings, parent_item):
        global collector_globalLocators_settings_object

        super(CollectorShotGlobalLocators, self).process_current_session(settings, parent_item)
        self.parent.logger.info("\n" + (">" * 120))
        self.parent.logger.info("\n{0}.process_current_session".format(self.name))
        if not parent_item.type_spec.endswith(".session"):
            item = next(
                (i for i in parent_item.descendants if i.type_spec.endswith(".session")),
                parent_item,
            )
        else:
            item = parent_item

        self._collect_global_locators(settings, item)

        collector_globalLocators_settings_object = settings


    # ----------------------------------------------------------------------------------

    def _collect_global_locators(self, settings, parent_item):
        # self.parent.logger.info(
        #     "settings in collector method:\n{}".format(pf(settings))
        # )
        locators = self.get_global_scale_locators(settings)
        self.parent.logger.info(
            "Found {} global locators:\n\n{}\n".format(len(locators), pf(locators))
        )

        scene_path = os.path.abspath(cmds.file(query=True, sceneName=True))

        if locators and scene_path:

            icon_path = os.path.join(
                self.disk_location,
                os.pardir,
                os.pardir,
                os.pardir,
                "icons",
                "locators.png",
            )

            asset_item = parent_item.create_item(
                type_spec="maya.session.shotAlembicLocators",
                type_display="Alembic Locators Cache",
                name="Alembic Locators Cache",
            )

            self.parent.engine.execute_hook_expression(
                "{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py",
                "use_farm_or_local_processing",
                item = asset_item
            )

            asset_item.set_icon_from_path(icon_path)

            asset_item.properties["scene_path"] = scene_path
            asset_item.properties["already_baked"] = False
            asset_item.properties["locators_group"] = []
            asset_item.properties["locators_dict"] = {}
            asset_item.properties["accept"] = {"accepted": True, "checked": True}
            asset_item.properties["execute"] = self.execute
            asset_item.properties["abc_export_alembic"] = self.abc_export_alembic

    def get_global_scale_locators(self, settings):
        locators = []

        supported_names = settings.get("global_locators_supported_names").value

        for name in supported_names:
            found_locators = cmds.ls("*:{}".format(name), long=True)
            for locator in found_locators:
                valid_locator = self.get_asset_type(settings, locator)
                if valid_locator:
                    locators.append(valid_locator)

        return locators

    def get_asset_type(self, settings, scene_obj):
        result = None

        supported_assets = settings.get("global_locators_supported_assets").value
        is_referenced = cmds.referenceQuery(scene_obj, isNodeReferenced=True)
        # self.parent.logger.info("is_referenced: {}".format(is_referenced))
        if is_referenced:
            reference_filepath = cmds.referenceQuery(scene_obj, filename=True)
            if reference_filepath.endswith("}"):
                matched = re.match(r"(.*)(\{\d+\})", reference_filepath)
                if matched:
                    reference_filepath = matched.groups()[0]

            # self.parent.logger.info("tk: {}".format(tk))
            template = self.parent.engine.sgtk.template_from_path(reference_filepath)
            # self.parent.logger.info("template: {}".format(template))
            fields = template.get_fields(reference_filepath)
            # self.parent.logger.info("fields: {}".format(fields))
            asset_type = fields.get("sg_asset_type")
            # self.parent.logger.info("asset_type: {}".format(asset_type))

            if asset_type in supported_assets:
                result = scene_obj

        return result

    def get_char_locators_grp(self):
        locs_grp = None
        existing_group = cmds.ls("global_locators_grp", long=True)

        if len(existing_group) > 0:
            cmds.delete(existing_group)

        cmds.createNode("transform", name="global_locators_grp")
        locs_grp = cmds.ls("global_locators_grp", long=True)[0]

        return locs_grp

    def create_new_locator(self, locator, **kwargs):
        locs_dict = kwargs.get("locs_dict")
        locs_grp = kwargs.get("locs_grp")

        split_name = locator.split("|")[-1]
        split_name = split_name.split(":")
        new_name = "_".join(split_name)
        long_name = "|".join([locs_grp, new_name])

        if new_name not in locs_dict.keys() and len(cmds.ls(long_name)) == 0:
            locs_dict[new_name] = {"object": long_name, "target": locator}

            new_loc = cmds.spaceLocator(name=new_name)
            cmds.setAttr("{}.overrideEnabled".format(new_loc[0]), True, keyable=True)
            cmds.setAttr("{}.overrideColor".format(new_loc[0]), 14, keyable=True)
            cmds.parent(new_name, locs_grp)

        return locs_dict

    def bake_locators(self, locs_dict):
        startF = cmds.playbackOptions(query=True, min=True)
        endF = cmds.playbackOptions(query=True, max=True)

        # self.parent.logger.info("start frame: {}, end frame: {}".format(startF, endF))

        for loc in locs_dict.keys():
            self.parent.logger.info("-" * 80)
            self.parent.logger.info("baking locator: {}".format(loc))
            obj = locs_dict.get(loc).get("object")
            target = locs_dict.get(loc).get("target")

            constraint = cmds.parentConstraint(target, obj, maintainOffset=False)
            # self.parent.logger.info("constraint: {}".format(constraint))

            constraint_connections = cmds.listConnections(
                constraint,
                source=False,
                destination=True,
                skipConversionNodes=True,
                type="transform",
            )

            cmds.bakeResults(
                constraint_connections,
                simulation=False,
                time=(startF, endF),
                disableImplicitControl=True,
                preserveOutsideKeys=False,
                sparseAnimCurveBake=False,
                removeBakedAttributeFromLayer=False,
                removeBakedAnimFromLayer=False,
                bakeOnOverrideLayer=False,
                minimizeRotation=False,
                controlPoints=False,
                shape=False,
            )

            cmds.delete(constraint)

    def execute(self):
        locators_dict = {}
        settings = collector_globalLocators_settings_object
        # self.parent.logger.info("settings in execute method:\n{}".format(pf(settings)))

        locators = self.get_global_scale_locators(settings)
        if locators:
            locs_grp = self.get_char_locators_grp()
            # self.parent.logger.info("locators:\n{}".format(pf(locators)))

            for locator in locators:
                locators_dict = self.create_new_locator(
                    locator, locs_dict=locators_dict, locs_grp=locs_grp
                )
            # self.parent.logger.info("locators_dict: {}".format(pf(locators_dict)))
            self.bake_locators(locators_dict)

            return locs_grp, locators_dict

        return None

    def _abc_command(self, root_node, path_to_publish):
        startF = cmds.playbackOptions(query=True, min=True)
        endF = cmds.playbackOptions(query=True, max=True)
        list_of_arguments = [
            "-frameRange {0} {1}".format(startF, endF),
            "-stripNamespaces",
            "-worldSpace",
            "-dataFormat ogawa",
            "-eulerFilter",
            "-root {0}".format(root_node),
            "-file {0}".format(path_to_publish.replace("\\", "//")),
        ]

        result = ' -j "{}"'.format(" ".join(list_of_arguments))

        return result

    def abc_export_alembic(self, root_node, publish_path):
        command = self._abc_command(root_node, publish_path)
        full_cmd_str = "AbcExport{}".format(command)
        self.parent.logger.info("Export alembic command: {}".format(full_cmd_str))

        mel.eval(full_cmd_str)
