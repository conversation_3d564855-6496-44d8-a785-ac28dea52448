# -*- coding: utf-8 -*-
# Standard library:
import pprint
import re
#   .   .   .   .   .   .   .   .   .   .   .
# Third party:
import sgtk

#   .   .   .   .   .   .   .   .   .   .   .
# Project:
# ================================================================
pp = pprint.PrettyPrinter(indent=3).pprint
HookBaseClass = sgtk.get_hook_baseclass()


class Checks(HookBaseClass):

    def __init__(self, parent):
        super(Checks, self).__init__(parent)
        self.categories = self.core_maya_tools.checks.categories
        self.engine = sgtk.platform.current_engine()

        self.list_of_substrings_to_excude_textures = [
            'placeholder', 'dartInteriorIcons'
        ]

    @property
    def layout_sequencer_app(self):
        def callback(): return "NotValid"

        if self.parent.context.entity["type"] == "Sequence":
            callback = \
                self.engine \
                    .commands["Layout Toolkit"]["callback"]

        return callback

    @property
    def breakdown_app(self):
        return self.engine.commands["Scene Breakdown..."]["callback"]

    # ================================================================

    def session(self, state, **kwargs):
        app = kwargs["app"]

        self.categories.system.pool \
            .no_unknown_plugins_check(state)
        #   . . . . . . . . . . . . . . . . . . . . . .

        self.categories.general.pool \
            .no_unknown_nodes_check(state)
        #   . . . . . . . . . . . . . . . . . . . . . .

        self.categories.references.pool \
            .not_loaded_references_check(state)

        self.categories.references.pool \
            .references_without_nodes_check(state)
        #   . . . . . . . . . . . . . . . . . . . . . .

        # self.categories.animation.pool \
        #    .no_unused_constraints_check(state)

        self.use_latest_published_references(
            state,
            app=app,
            callback=self.breakdown_app
        )

        self.unknown_reference_nodes(state)

        self.limit_transform_distance_from_origin(state)

        # Temporal validations:
        self._temporal_validation(
            state=state,
            app=app,
            list_of_episodes=['e135'],
            list_of_environments=['aliceBedroom'],
            list_of_steps=['Animation', 'Layout']
        )

    # ================================================================

    def layout(self, state, **kwargs):
        app = kwargs["app"]

        set_of_required_huds = {
            'HUDFocalLength',
            'HUDShotCode',
            'HUDFocusDistance',
            'HUDFStop',
            'HUDCurrentFrame'
        }

        self.categories.animation.pool \
            .no_locked_shot_nodes_check(state)
        self.categories.animation.pool \
            .no_unused_constraints_check(state)
        #   . . . . . . . . . . . . . . . . . . . . . .

        self.categories.references.pool \
            .references_without_nodes_check(state)
        #   . . . . . . . . . . . . . . . . . . . . . .

        self.categories.rendering.pool \
            .no_non_default_render_layers_check(state)
        #   . . . . . . . . . . . . . . . . . . . . . .

        self.shot_length_error_check(state, **kwargs)

        excuded_reference_edit_wildcard = [".pan"]
        operation_type = "exclude"

        self.categories.references.pool \
            .no_alembic_camera_reference_edits(
            state,
            app=app,
            wildcard_list=excuded_reference_edit_wildcard,
            operation_type=operation_type
        )

        if app.context.entity["type"] == "Sequence":
            self.not_enabled_huds_check(
                state,
                set_of_required_huds=set_of_required_huds,
                message="The following button will " +
                        "open the Layout|Sequencer " +
                        "so that you can activate them.",
                callback=self.layout_sequencer_app
            )

            for hud in set_of_required_huds:
                self.not_visible_huds_check(state, hud=hud)

        self.not_required_huds_check(state, set_of_required_huds)
        self.no_locked_version_without_description(state)
        self.no_audio_source_check(state)
        self.script_nodes_jobs_vaccine(state)
        self.unsupported_reference_nodes(state)

        # self.categories.animation.pool.animation_exposition_type_check(state=state)

    # ===============================================================================

    def animation(self, state, **kwargs):
        app = kwargs["app"]
        asset_template = "maya_asset_publish"
        camera_template = "shot_camera_publish"
        gpuCache_template = "asset_gpu_cache"

        set_of_required_huds = {
            "HUDCurrentFrame",
            "HUDShotCode",
            "HUDUser",
            "HUDStep"
        }

        #   . . . . . . . . . . . . . . . . . . . . . .
        # TODO: Update mty-framework-coremayatools
        # The validations of the breakdown items are not correct
        # they are mixing breakdown names, and asset names
        # proper rules should be:
        #   - breakdown names should match scene namespaces
        #   - breakdown assets should match references asets
        #self.categories.shotgun.pool \
        #    .matching_breakdown_and_assets(state, app.context)

        self.categories.shotgun.pool \
            .no_unpublished_texture_files(state, app,
                                          self.list_of_substrings_to_excude_textures)

        #   . . . . . . . . . . . . . . . . . . . . . .

        self.categories.references.pool \
            .references_without_nodes_check(state)

        self.categories.references.pool \
            .no_alembic_camera_reference_edits(
            state,
            app=app,
            wildcard_list=[
                '-lock on',
                '.overscan',
                '.farClipPlane',
                '.nearClipPlane'
            ],
            operation_type='include'
        )

        self.categories.references.pool.no_proxy_rigs_reference(state, app=app)

        #   . . . . . . . . . . . . . . . . . . . . . .
        self.breakdown_assets_in_session_check(
            state,
            app=app,
            asset_template=asset_template,
            camera_template=camera_template,
            gpuCache_template=gpuCache_template
        )

        self.animation_publish_references_check(
            state,
            app=app,
            valid_templates=[
                "shot_camera_publish",
                "maya_asset_publish",
                "asset_gpu_cache"
            ]
        )

        self.not_required_huds_check(state, set_of_required_huds)

        self.not_loaded_reference_nodes(state)

        self.reference_edits_for_texture_check(
            state,
            app=app.sgtk
        )

        #self.no_camera_rig_for_animation(
        #    state,
        #    app=app
        #)

        self.no_audio_source_check(state)

        engine = sgtk.platform.current_engine()
        if engine.context.task["name"] != "Blocking":
            self.categories.animation.pool.animation_exposition_type_check(
                state=state)

        self.fps_time_unit(state, app=app)

        self.constraints_targeted_to_camera(state)

        self.script_nodes_jobs_vaccine(state)

    # ================================================================

    def model(self, state, **kwargs):

        app = kwargs["app"]

        self.categories.namespace.pool \
            .no_namespaces_check(state)

        # #   . . . . . . . . . . . . . . . . . . . . . .

        self.categories.shotgun.pool \
            .no_unpublished_texture_files(
            state,
            app,
            []
        )

        # #   . . . . . . . . . . . . . . . . . . . . . .

        self.categories.modeling.pool \
            .no_smooth_geometry_display_check(state)

        # #   . . . . . . . . . . . . . . . . . . . . . .

        self.categories.general.pool \
            .no_duplicated_node_names_check(state)
        self.categories.general.pool \
            .no_non_default_display_layers_check(state)
        self.categories.general.pool \
            .no_non_default_cameras_check(state)

        # #   . . . . . . . . . . . . . . . . . . . . . .

        self.categories.animation.pool \
            .no_sequencer_nodes_check(state)
        self.categories.animation.pool \
            .no_unused_constraints_check(state)

        # #   . . . . . . . . . . . . . . . . . . . . . .

        self.categories.rendering.pool \
            .no_image_planes_check(state)
        self.categories.rendering.pool \
            .no_non_default_render_layers_check(state)

        # #   . . . . . . . . . . . . . . . . . . . . . .

        self.no_shape_deformed_shapes_check(state)
        # self.mesh_cache_color_attribute_check(state)
        self.model_high_root_check(state)

        #   . . . . . . . . . . . . . . . . . . . . . .

        self.categories.modeling.pool \
            .no_smooth_geometry_display_check(state)
        #   . . . . . . . . . . . . . . . . . . . . . .

        self.categories.general.pool \
            .no_duplicated_node_names_check(state)
        self.categories.general.pool \
            .no_non_default_display_layers_check(state)
        self.categories.general.pool \
            .no_non_default_cameras_check(state)

        #   . . . . . . . . . . . . . . . . . . . . . .

        self.categories.animation.pool \
            .no_sequencer_nodes_check(state)
        self.categories.animation.pool \
            .no_unused_constraints_check(state)

        #   . . . . . . . . . . . . . . . . . . . . . .

        self.categories.rendering.pool \
            .no_image_planes_check(state)
        self.categories.rendering.pool \
            .no_non_default_render_layers_check(state)
        #   . . . . . . . . . . . . . . . . . . . . . .

        self.no_shape_deformed_shapes_check(state)

        self.model_high_root_check(state)

        self.script_nodes_jobs_vaccine(state)

    # ================================================================

    def rig(self, state, **kwargs):
        app = kwargs['app']

        self.categories.namespace.pool \
            .no_namespaces_check(state)

        self.categories.modeling.pool \
            .no_smooth_geometry_display_check(state)

        #   . . . . . . . . . . . . . . . . . . . . . .

        self.categories.shotgun.pool \
            .no_unpublished_texture_files(state, app=app,
                                          substring_list=self.list_of_substrings_to_excude_textures)

        #   . . . . . . . . . . . . . . . . . . . . . .

        self.categories.general.pool \
            .no_duplicated_node_names_check(state)
        self.categories.general.pool \
            .no_non_default_display_layers_check(state)
        self.categories.general.pool \
            .no_non_default_cameras_check(state)
        #   . . . . . . . . . . . . . . . . . . . . . .

        self.categories.rendering.pool \
            .no_image_planes_check(state)
        self.categories.rendering.pool \
            .no_non_default_render_layers_check(state)
        self.categories.animation.pool \
            .no_sequencer_nodes_check(state)
        self.categories.system.pool \
            .invalid_node_types_check(
            state,
            list_of_node_types=["ngSkinLayerData"]
        )
        self.categories.animation.pool \
            .no_unused_constraints_check(state)

        self.categories.animation.pool.no_time_animation_curves(state)

        #   . . . . . . . . . . . . . . . . . . . . . .

        self.all_controllers_in_animControls_set_check(state)
        self.mesh_reference_override(state)
        self.no_shape_deformed_shapes_check(state)
        self.attributes_in_controllers_check(state)

        # self.mesh_cache_color_attribute_check(state)

        self.model_high_root_check(state)
        engine = sgtk.platform.current_engine()
        kwargs = {'task': engine.context.task["name"]}
        self.rig_root_check(state, **kwargs)

        self.non_blendshapes_textures(state)

        self.script_nodes_jobs_vaccine(state)

    # ================================================================

    def rig_camera(self, state, **kwargs):

        self.categories.namespace.pool \
            .no_namespaces_check(state)
        #   . . . . . . . . . . . . . . . . . . . . . .

        self.categories.general.pool \
            .no_useless_transform_nodes_check(state)
        self.categories.general.pool \
            .no_duplicated_node_names_check(state)
        self.categories.general.pool \
            .no_non_default_display_layers_check(state)
        #   . . . . . . . . . . . . . . . . . . . . . .

        self.categories.rendering.pool \
            .no_image_planes_check(state)
        self.categories.rendering.pool \
            .no_non_default_render_layers_check(
            state)
        #   . . . . . . . . . . . . . . . . . . . . . .

        self.categories.animation.pool \
            .no_sequencer_nodes_check(state)
        self.categories.animation.pool \
            .no_unused_constraints_check(state)

        self.limit_transform_distance_from_origin(state)

        self.script_nodes_jobs_vaccine(state)

    # ================================================================

    def rig_proxy(self, state, **kwargs):

        self.categories.modeling.pool \
            .no_smooth_geometry_display_check(state)
        #   . . . . . . . . . . . . . . . . . . . . . .

        self.categories.namespace.pool \
            .no_namespaces_check(state)
        #   . . . . . . . . . . . . . . . . . . . . . .

        self.categories.general.pool \
            .no_duplicated_node_names_check(state)

        self.categories.general.pool \
            .no_non_default_cameras_check(state)
        #   . . . . . . . . . . . . . . . . . . . . . .

        self.categories.rendering.pool \
            .no_image_planes_check(state)
        self.categories.rendering.pool \
            .no_non_default_render_layers_check(state)
        #   . . . . . . . . . . . . . . . . . . . . . .

        self.categories.animation.pool \
            .no_sequencer_nodes_check(state)

        self.categories.system.pool \
            .invalid_node_types_check(
            state, list_of_node_types=["ngSkinLayerData"]
        )

        #   . . . . . . . . . . . . . . . . . . . . . .

        self.categories.animation.pool \
            .no_unused_constraints_check(state)
        #   . . . . . . . . . . . . . . . . . . . . . .

        self.all_controllers_in_animControls_set_check(state)
        self.attributes_in_controllers_check(state)
        self.model_high_root_check(state)

        engine = sgtk.platform.current_engine()
        kwargs = {'task': engine.context.task["name"]}
        self.rig_root_check(state, **kwargs)

        self.script_nodes_jobs_vaccine(state)

    # ================================================================
