from PySide2.QtWidgets import (
    QDialog,
    QComboBox,
    QLabel,
    QPushButton,
    QVBoxLayout,
)

import os
import json
import sgtk
import pprint
import traceback
from tank import Hook
from html import entities
from tank.platform.qt import <PERSON><PERSON><PERSON><PERSON>, QtGui

class ProcessItemsHook(Hook):

    def execute(self, **kwargs):
        result = {"success": [], "messages": [], "errors": []}

        def check_selection():
            command = """
            function checkSelection() {
                var selectedNode = selection.selectedNode(0);
                if (selectedNode) {
                    return true;
                } else {
                    MessageBox.information("Please select the main Composite node and try again.");
                    return false;
                }
            }
            checkSelection();
            """
            answer = self.parent.engine.app.execute(command)
            return answer

        if check_selection():

            # get the project color overrides
            this_hook_location = self.disk_location

            project_data = self.get_project_data_from_SG()
            project_code = project_data.get("code")

            # get a list of tpls contained int the tpls folder for this project
            project_tpls_path = os.path.join(
                this_hook_location, "tpls", project_code
            )
            tpls_list = self.get_tpls_list(project_tpls_path)

            # Create the dialog
            dialog = QDialog()
            dialog.setWindowTitle("Color Override")
            layout = QVBoxLayout()
            dialog.setLayout(layout)

            # Create the combo box
            input_label = QLabel("Select the color override to Import:")
            combo_box = QComboBox()

            # add all project tpls found to the combo box
            for tpl in tpls_list:
                combo_box.addItem(tpl)

            layout.addWidget(input_label)
            layout.addWidget(combo_box)

            # Create the button
            import_button = QPushButton("Import")

            # Connect the button "Import" to the function that calls _import_tpl
            import_button.clicked.connect(
                lambda: self.import_color_override(combo_box.currentText())
            )
            import_button.clicked.connect(dialog.accept)
            layout.addWidget(import_button)

            dialog.exec_()

        return result

    def get_project_data_from_SG(self):
        """
        Returns a dictionary of project data from SG relevant to a
        shot of the following form:

            {
                'code': (str),
                'id': (int),
                'sg_fps': (float),
                'sg_output_color_space': (str),
                'sg_working_color_space': (str),
                'type': (str),
            }
        """

        project_data = {}

        filters = [
            ["id", "is", self.parent.engine.context.project["id"]],
        ]
        fields = [
            "code",
            "id",
            "sg_fps",
            "sg_working_color_space",
            "sg_output_color_space",
        ]

        project_data = self.parent.engine.shotgun.find_one(
            entity_type="Project", filters=filters, fields=fields
        )

        return project_data

    def get_tpls_list(self, project_tpls_path):

        all_items = os.listdir(project_tpls_path)

        # Filter out directories that end with ".tpl"
        filtered_folders = [
            folder.rstrip(".tpl")
            for folder in all_items
            if os.path.isdir(os.path.join(project_tpls_path, folder))
            and folder.endswith(".tpl")
        ]

        return filtered_folders or []

    def import_color_override(self, override_name):
        this_hook_location = self.disk_location

        color_override_path = os.path.join(
            this_hook_location, "tpls", "BNA2", override_name + ".tpl"
        )

        self.parent.logger.debug("color_override_path: {}".format(color_override_path))
        color_override_path = color_override_path.replace("\\\\", "\\")
        color_override_path = color_override_path.replace("\\", "/")

        cmd = """
        function import_tpl(color_override_path) {
            var selectedNode = selection.selectedNode(0);
            if (selectedNode) {
                var numberOfOutputLinks = node.numberOfOutputLinks(selectedNode, 0);

                var copyOpt = copyPaste.getCurrentCreateOptions();
                var pasteOpt = copyPaste.getCurrentPasteOptions();
                var templateCopy = copyPaste.copyFromTemplate(color_override_path, 0, 0, copyOpt);
                copyPaste.pasteNewNodes(templateCopy, "", pasteOpt);

                var tplNode = selection.selectedNode(0);

                for (var i = 0; i < numberOfOutputLinks; i++) {
                    var connectedNode = node.dstNode(selectedNode, 0, 0);
                    node.unlink(connectedNode, 0);
                    node.link(tplNode, 0, connectedNode, 0);
                }

                var x = node.coordX(selectedNode)
                var y = node.coordY(selectedNode)

                node.setCoord(tplNode, x, y + 50);

                node.link(selectedNode, 0, tplNode, 0);
                var splitName = color_override_path.split("/")
                var splitName = splitName[splitName.length - 1]
                node.rename(tplNode, splitName.split(".")[0]);

            }
            return tplNode;
        }

        var color_override_path = "%s";

        import_tpl(color_override_path);

        // return "";
        """ % color_override_path

        if os.path.exists(color_override_path):
            self.parent.engine.app.execute(cmd)
        else:
            error_msg =(
                "Color Override {} not found. Please contact Pipeline Support"
            ).format(override_name)

            # QtGui.QMessageBox.error(
            #     None, "Couldn't find Color Override", error_msg
            # )
            self.parent.logger.error(error_msg)
            self.parent.engine.show_message(error_msg)
