#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################


import os
import json
import pprint
import traceback

import sgtk

from tank import Hook
from tank import TankError


class ProcessItemsHook(Hook):
    """
    Process items to load them into the scene
    """

    def load_item(self, item, update_progress):
        """
        The load item method is executed once for each defined item and its purpose is
        to process the loading of the item into the scene.

        The structure of each item is a dictionary with three keys:

        - "node": The name of the 'node' that is to be operated on. Most DCCs have
            a concept of a node, path, or some other way to address a particular
            object in the scene.
        - "type": The object type that this is. This is to
            know how to handle the object.
        - "path": Path on disk to the object to be loaded.
        """


        image_path = item['path']

        try:
            if item['type'] == 'Bg file':
                update_progress({'progress': 30, 'message': 'Referencing bg...'})
                self.parent.log_debug("loading %s" % image_path)

                # Load the item into the scene:
                hook_expression = self.parent.get_setting('actions_hook')
                self.parent.execute_hook_expression(
                    hook_expression,
                    '_import_image_project_resolution',
                    image_path=image_path
                )

        except:
            self.parent.logger.error("Error loading item %s" % image_path)
            self.parent.logger.error(traceback.format_exc())

        self.logger.info("Template imported: %s" % image_path)
