################################################################################
#
# Copyright (c) 2024 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
################################################################################

import sgtk

from pprint import pformat

HookBaseClass = sgtk.get_hook_baseclass()


class DeliveryOnFarm(HookBaseClass):
    def testing_job(self, *args, **kwargs):
        self.logger.info("Testing Delivery on farm hook is being executed")

        self.logger.info(pformat(kwargs))

        self.logger.info(pformat(args))

        self.logger.info("Testing Delivery on farm hook is done")
        return True