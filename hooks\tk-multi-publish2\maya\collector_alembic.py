# -*- coding: utf-8 -*-
# Standard library:
import os
# import sys

#   .   .   .   .   .   .   .   .   .   .   .
# Third party:
import sgtk
import maya.cmds as cmds


HookBaseClass = sgtk.get_hook_baseclass()


class alembicCollector(HookBaseClass):
    def __init__(self, parent):
        super(alembicCollector, self).__init__(parent)

    # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

    def process_current_session(self, settings, parent_item):
        super(alembicCollector, self).process_current_session(settings, parent_item)
        if not parent_item.type_spec.endswith(".session"):
            item = next(
                (i for i in parent_item.descendants if i.type_spec.endswith(".session")),
                parent_item,
            )
        else:
            item = parent_item

        self.alembic(item)

    def alembic(self, parent_item):
        icon_path = os.path.join(
            self.disk_location, os.pardir, "icons", "publish_alembic.png"
        )

        ctx = self.parent.context
        step_name = ctx.step["name"].lower

        if self.ASSET["sg_asset_type"] != "Camera":
            alembic_item = parent_item.create_item(
                "maya.session.asset.alembic",
                "Alembic Asset  in session",
                "Alembic Node",
            )

            self.parent.engine.execute_hook_expression(
                "{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py",
                "use_farm_or_local_processing",
                item = alembic_item
            )

            print(icon_path)
            alembic_item.set_icon_from_path(icon_path)

            if step_name == "model":
                alembic_item.properties["model"] = "model_high_root"
                self.logger.debug(
                    "Collected high_roots: {0}".format(
                        str(alembic_item.properties["model"])
                    )
                )

    def get_maya_version(self):
        """
        Get the current Maya version.
        """
        maya_version = cmds.about(version=True)

        return maya_version