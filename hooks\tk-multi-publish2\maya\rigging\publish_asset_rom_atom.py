# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

import os
import sys
import sgtk
import maya.cmds as cmds


HookBaseClass = sgtk.get_hook_baseclass()


class MayaAssetRomAnimationPublishPlugin(HookBaseClass):
    """
    Plugin for publish Scene playblast Publish

    """

    @property
    def icon(self):
        """
        Path to an png icon on disk
        """

        # look for icon one level up from this hook's folder in "icons" folder
        return os.path.join(
            self.disk_location,
            os.pardir,
            "icons",
            "publish_alembic.png"
        )

    @property
    def name(self):
        """
        One line display name describing the plugin
        """
        return "Publish Rom Animation"

    @property
    def description(self):
        """
        Verbose, multi-line description of what the plugin does. This can
        contain simple html for formatting.
        """

        loader_url = "https://support.shotgunsoftware.com/hc/en-us/articles/219033078"

        return """
        <p>This plugin publish an alembic with dynamic attributes baked on every frame.</p>

        """

    @property
    def settings(self):
        """
        Dictionary defining the settings that this plugin expects to recieve
        through the settings parameter in the accept, validate, publish and
        finalize methods.

        A dictionary on the following form::

            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }

        The type string should be one of the data types that toolkit accepts
        as part of its environment configuration.
        """
        # inherit the settings from the base publish plugin
        plugin_settings = super(
            MayaAssetRomAnimationPublishPlugin, self).settings or {}

        # settings specific to this class
        maya_publish_settings = {
            "Work Template": {
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "Primary Publish Template": {
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "Publish Template": {
                "type": "template",
                "default": None,
                "description": "Template path for published Scene playblast. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "Publish type": {
                "type": "string",
                "default": "Animation Data",
                "description": "The published file type to register.",
            },
            "Allowed groups": {
                "type": "list",
                "default": ["model_high_root"],
                "description": "The valid secondary groups.",
            },
            "Allowed steps": {
                "type": "list",
                "default": ["rig"],
                "description": "Allowed asset steps for the plugin.",
            },
            "Allowed tasks": {
                "type": "list",
                "default": ["romanimation"],
                "description": "Allowed asset tasks for the plugin.",
            },
            "Allowed asset types": {
                 "type": "list",
                 "default": ["character", "prop"],
                 "description": "Allowed asset type to export dyn attrs alembic.",
             },
        }

        # update the base settings
        plugin_settings.update(maya_publish_settings)

        return plugin_settings

    @property
    def item_filters(self):
        """
        List of item types that this plugin is interested in.

        Only items matching entries in this list will be presented to the
        accept() method. Strings can contain glob patters such as *, for example
        ["maya.*", "file.maya"]
        """

        return ["maya.anim.roms"]

    def accept(self, settings, item):

        step_name = self.parent.context.step['name'].lower()
        task_name = self.parent.context.task['name'].lower()

        allowed_steps = settings['Allowed steps'].value
        allowed_tasks = settings['Allowed tasks'].value

        if step_name in allowed_steps and task_name in allowed_tasks:
            allowed_types = settings['Allowed asset types'].value
            current_asset = self.parent.engine.shotgun.find_one(
                "Asset",
                [["id", "is", self.parent.context.entity["id"]]],
                ["sg_asset_type"])
            current_asset_type = current_asset["sg_asset_type"].lower()
            valid_asset_type = current_asset_type in allowed_types
            if valid_asset_type:
                return {"accepted": True, "checked": True}

        return {"accepted": False, "checked": True}

    def get_rig_controls(self):

        nurbs_curves = cmds.ls(type='nurbsCurve', ni=1, o=1, r=1)
        curve_transforms = [cmds.listRelatives(i, p=1, type='transform', fullPath=True)[0] for i in nurbs_curves]

        return curve_transforms


    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish. Returns a
        boolean to indicate validity.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        :returns: True if item is valid and not in proxy mode, False otherwise.
        """

        rig_controls = self.get_rig_controls()
        if not rig_controls:
            raise Exception("Not found any rig with nurbsCurve controls.")

        cmds.loadPlugin("atomImportExport.py")

        return True

    def get_root_item(self, item):
        """ A handy recursive function to get an item's root parent
            This is mostly useful because the root is the only one
            where we can store extra data
        """
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root


    def publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        work_template_name = settings["Work Template"].value
        work_template = self.parent.engine.get_template_by_name(work_template_name)
        scene_path = cmds.file(q=True, sn=True)
        fields = work_template.get_fields(scene_path)

        atom_pub_name = settings["Publish Template"].value
        atom_pub_template = self.parent.engine.get_template_by_name(atom_pub_name)
        atom_published_path = atom_pub_template.apply_fields(fields)

        rig_controls = self.get_rig_controls()
        cmds.select(rig_controls)

        self.parent.engine.ensure_folder_exists(os.path.dirname(atom_published_path))

        # Export atom file to curves
        cmds.file(
            atom_published_path,
            es=True,
            force=True,
            type="atomExport",
            options='"precision=8","statics=1","baked=1","sdk=1","constraint=1","animLayers=1","selected=selectedOnly","whichRange=1","range=1:10","hierarchy=none","controlPoints=1","useChannelBox=1","options=keys","opyKeyCmd=-animation objects -option keys -hierarchy none -controlPoints 1 "'
        )

        roms_dependencies = []

        scene_pub_template_name = settings["Primary Publish Template"].value
        scene_pub_template = self.parent.engine.get_template_by_name(scene_pub_template_name)
        primary_publish_path = scene_pub_template.apply_fields(fields)
        pub_fields = scene_pub_template.get_fields(primary_publish_path)

        roms_dependencies.append(primary_publish_path)

        # and a publish name
        publish_name = self.parent.util.get_publish_name(atom_published_path)
        publish_type = settings["Publish type"].value

        sg_fields_to_update = {
            "sg_status_list": "rev",
            "task.Task.sg_status_list": "rev",
        }

        # Register the publish of the Alembic File:
        args = {
            "tk": self.parent.engine.sgtk,
            "context": item.context,
            "comment": item.description,
            "path": atom_published_path,
            "name": publish_name,
            "version_number": pub_fields["version"],
            "thumbnail_path": item.get_thumbnail_as_path(),
            "task": self.parent.engine.context.task,
            "dependency_paths": roms_dependencies,
            "sg_fields": sg_fields_to_update,
            "published_file_type": publish_type
        }
        sg_publish = sgtk.util.register_publish(**args)

        root_item = self.get_root_item(item)
        root_item.properties.setdefault('sg_publish_extra_data', [])
        publish_extra_data = root_item.properties['sg_publish_extra_data']
        publish_extra_data.append(sg_publish)

        self.parent.log_info("Published file 'Animation Data' for atom animation registered.")

        return sg_publish

    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        pass



