# Module created to replace default regex.
# It should be used by all publishers, usually wrapped by 'name_from_path_info.py'.

import os
import re
import pprint

import sgtk

# ---- globals

# # a regular expresion to find the version token of the form [-_.]v000
# REGEX_VERSION = re.compile(r"([.\-_](v\d+)?)[.\-_]")
# # a regular expression to get just the number of the version. Needed for
# # publishes' version_number field
# VERSION_REGEX = re.compile(r"(.*)([._-])v(\d+)\.?(.+)?$", re.IGNORECASE)
# # a regular expresion to find the sequence pattern token of one the forms:
# # %00d  ###, @@@, 000     in the last 3 paterns the amount of characters doesn't matter,
# # so it should find either #, or ##, or ###, and so on. The last part of the regex
# # assumes that ALWAYS after the sequences pattern, there's a . (removed the previous
# # version [.\-_] to avoid confusion with shot names, which usually contain 4 digits)
# REGEX_SEQUENCE = re.compile(r"([.\-_](%[0-9]{2}d)?([0-9]+)?(@+)?(#+)?)[.]")

REGEX_GLOBAL = re.compile(
  r"(?P<basename>.+)[._-]"
  r"(?P<version_token>v(?P<version_number>\d{3,}))[._-]?"
  r"(?P<sequence_token>"
    r"(?:%\d{2}d)|"
    r"(?:\d{3,})|"
    r"(?:@{1,})|"
    r"(?:#{1,}))?"
  r"(?P<extension>[.].+$)"
)


pp = pprint.pprint
pf = pprint.pformat


def get_publish_name(path, sequence=False):
    """
    Given a file path, return the display name to use for publishing.

    Typically, this is a name where the path and any version number are
    removed in order to keep the publish name consistent as subsequent
    versions are published.

    Example::

        # versioned file. remove the version
        in: /path/to/the/file/scene.v001.ma
        out: scene.ma

    :param path: The path to a file, likely one to be published.

    :return: A publish display name for the provided path.
    """

    engine = sgtk.platform.current_engine()
    engine.logger.info("engine: {}".format(engine))

    filename = os.path.basename(path)

    # if filename contains afx sequence formatting of the form [####], remove
    # the square brackets
    if "[" in filename:
        filename = filename.replace("[", "")
    if "]" in filename:
        filename = filename.replace("]", "")

    # # search for version token and if it can be found, remove it from the original string
    # search_version = re.search(REGEX_VERSION, filename)
    # if search_version:
    #     filename = filename.replace(search_version.groups()[0], "")

    # # search for sequence token and if it can be found, remove it from the original string
    # search_sequence = re.search(REGEX_SEQUENCE, filename)
    # if search_sequence:
    #     filename = filename.replace(search_sequence.groups()[0], "")

    global_match = re.match(REGEX_GLOBAL, filename)
    if global_match:
        basename = global_match.groupdict().get("basename", None)
        extension = global_match.groupdict().get("extension", None)
        if basename and extension:
            filename = "{}{}".format(basename, extension)

    return filename


def get_version_number(path):
    """
    Extract a version number from the supplied path.

    This is used by plugins that need to know what version number to
    associate with the file when publishing.

    :param path: The path to a file, likely one to be published.

    :return: An integer representing the version number in the supplied
        path. If no version found, ``None`` will be returned.
    """

    filename = os.path.basename(path)

    # default if no version number detected
    version_number = None

    # if there's a version in the filename, extract it
    # version_pattern_match = re.search(VERSION_REGEX, filename)
    # if version_pattern_match:
    #     version_number = int(version_pattern_match.group(3))

    global_match = re.match(REGEX_GLOBAL, filename)
    if global_match:
        version_number = global_match.groupdict().get("version_number", None)
        if version_number:
            version_number = int(version_number)

    return version_number
