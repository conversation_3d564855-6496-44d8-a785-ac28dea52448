#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

import os
import pprint
import traceback

import sgtk
from tank import Hook
from tank import TankError

pp = pprint.pprint
pf = pprint.pformat

class ScanSceneHook(Hook):
    """
    Hook to scan scene for items to load
    """

    def scan_scene(self):
        """
        The scan scene method is executed once at the start of the app. Its purpose is
        to look at the current scene and return a list of items that are loaded in the
        scene. This list will be used to display what is currently loaded in the scene
        and to determine what needs to be loaded.

        The return data structure is a list of dictionaries. Each dictionary contains
        three keys:

        - "node": The name of the 'node' that is to be operated on. Most DCCs have
            a concept of a node, path, or some other way to address a particular
            object in the scene.
        - "type": The object type that this is. This is to
            know how to handle the object.
        - "path": Path on disk to the object to be loaded.
        """

        items = []

        try:
            # Get current context
            current_context = self.parent.context
            
            # Get the ShotGrid connection
            sg = self.parent.shotgun
            
            # Define the published file types we're looking for
            blend_types = [
                "Blender Scene",
                "Maya Scene",
                "Blend File"
            ]
            
            # Build filters for the query
            filters = [
                ["project", "is", current_context.project],
                ["published_file_type.PublishedFileType.code", "in", blend_types],
                ["sg_status_list", "is_not", "na"]
            ]
            
            # Add entity-specific filters based on context
            if current_context.entity:
                if current_context.entity["type"] == "Shot":
                    # For shots, look for blend files linked to the shot or its assets
                    filters.append(
                        {
                            "filter_operator": "any",
                            "filters": [
                                ["entity", "is", current_context.entity],
                                ["entity.Asset.shots", "is", current_context.entity]
                            ]
                        }
                    )
                elif current_context.entity["type"] == "Asset":
                    # For assets, look for blend files linked to the asset
                    filters.append(["entity", "is", current_context.entity])
            
            # Query for published files
            fields = [
                "id",
                "code", 
                "path",
                "published_file_type",
                "entity",
                "task",
                "version_number",
                "sg_status_list"
            ]
            
            published_files = sg.find("PublishedFile", filters, fields)
            
            self.parent.logger.info("Found {} blend published files".format(len(published_files)))
            
            # Convert to the expected format
            for pub_file in published_files:
                if pub_file.get("path") and pub_file["path"].get("local_path"):
                    local_path = pub_file["path"]["local_path"]
                    
                    # Check if file exists locally
                    if os.path.exists(local_path):
                        item = {
                            "node": pub_file["code"],
                            "type": pub_file["published_file_type"]["name"],
                            "path": local_path,
                            "other_params": pub_file  # Include full SG data
                        }
                        items.append(item)
                        self.parent.logger.debug("Added blend item: {}".format(item["node"]))
            
            self.parent.logger.info("Scan complete. Found {} blend items to load".format(len(items)))
            
        except Exception as e:
            self.parent.logger.error("Error scanning for blend files: {}".format(e))
            self.parent.logger.error("Full traceback:\n{}".format(traceback.format_exc()))
        
        return items
