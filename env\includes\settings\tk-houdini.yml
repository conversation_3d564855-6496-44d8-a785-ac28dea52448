# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

################################################################################

includes:
- ../app_locations.yml
- ../engine_locations.yml
- ./tk-houdini-alembicnode.yml
- ./tk-houdini-mantranode.yml
- ./tk-multi-loader2.yml
- ./tk-multi-publish2.yml
- ./tk-multi-screeningroom.yml
- ./tk-multi-shotgunpanel.yml
- ./tk-multi-snapshot.yml
- ./tk-multi-workfiles2.yml

################################################################################

# asset
settings.tk-houdini.asset:
  apps:
    tk-multi-about:
      location: '@apps.tk-multi-about.location'
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel'
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2.launch_at_startup'
    tk-multi-pythonconsole:
      location: '@apps.tk-multi-pythonconsole.location'
  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  location: '@engines.tk-houdini.location'

# asset_step
settings.tk-houdini.asset_step:
  apps:
    tk-multi-about:
      location: '@apps.tk-multi-about.location'
    tk-multi-breakdown:
      location: '@apps.tk-multi-breakdown.location'
    tk-multi-loader2: '@settings.tk-multi-loader2.houdini'
    tk-multi-publish2: '@settings.tk-multi-publish2.houdini.asset_step'
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel.houdini'
    tk-multi-snapshot: '@settings.tk-multi-snapshot.houdini.asset_step'
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2.houdini.asset_step'
    tk-houdini-alembicnode: '@settings.tk-houdini-alembicnode.asset_step'
    tk-houdini-mantranode: '@settings.tk-houdini-mantranode.asset_step'
    tk-multi-pythonconsole:
      location: '@apps.tk-multi-pythonconsole.location'
    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset" ]

  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-snapshot, name: Snapshot...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  - {app_instance: tk-multi-publish2, name: Publish...}
  location: '@engines.tk-houdini.location'

# project
settings.tk-houdini.project:
  apps:
    tk-multi-about:
      location: '@apps.tk-multi-about.location'
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel'
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2'
    tk-multi-pythonconsole:
      location: '@apps.tk-multi-pythonconsole.location'
    mty-executeaction-ensure-folders:
      location: '@apps.tk-shotgun-executeaction.location'
      display_name: Ensure Tasks Folders
      action_hook: '{config}/tk-shotgun-executeaction/create_folders_for_assigned_tasks.py'
      sg_extended_fields: {}
      allowed_entities: [Shot, Sequence, Asset, Project]
  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  location: '@engines.tk-houdini.location'

# sequence
settings.tk-houdini.sequence:
  apps:
    tk-multi-about:
      location: '@apps.tk-multi-about.location'
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel'
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2'
    tk-multi-pythonconsole:
      location: '@apps.tk-multi-pythonconsole.location'
  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  location: '@engines.tk-houdini.location'

# shot
settings.tk-houdini.shot:
  apps:
    tk-multi-about:
      location: '@apps.tk-multi-about.location'
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel'
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2'
    tk-multi-pythonconsole:
      location: '@apps.tk-multi-pythonconsole.location'
  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  location: '@engines.tk-houdini.location'

# shot_step
settings.tk-houdini.shot_step:
  apps:
    tk-multi-about:
      location: '@apps.tk-multi-about.location'
    tk-multi-breakdown:
      location: '@apps.tk-multi-breakdown.location'
    tk-multi-setframerange:
      location: '@apps.tk-multi-setframerange.location'
    tk-multi-loader2: '@settings.tk-multi-loader2.houdini'
    tk-multi-publish2: '@settings.tk-multi-publish2.houdini.shot_step'
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel.houdini'
    tk-multi-snapshot: '@settings.tk-multi-snapshot.houdini.shot_step'
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2.houdini.shot_step'
    tk-houdini-alembicnode: '@settings.tk-houdini-alembicnode.shot_step'
    tk-houdini-mantranode: '@settings.tk-houdini-mantranode.shot_step'
    tk-multi-pythonconsole:
      location: '@apps.tk-multi-pythonconsole.location'
    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'

    mty-executeaction-create-flipbook:
      location: '@apps.tk-shotgun-executeaction.location'
      display_name: Create Flipbook...
      action_hook: '{config}/tk-shotgun-executeaction/houdini/houdini_create_flipbook.py'
      sg_extended_fields: {}
      allowed_entities: [Shot]
      scene_work_template: houdini_shot_work
      review_work_template: shot_review_work_proxy
      review_image_format: jpg

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Shot" ]

  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-snapshot, name: Snapshot...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  - {app_instance: tk-multi-publish2, name: Publish...}
  location: '@engines.tk-houdini.location'
frameworks:
  tk-framework-widget_v1.x.x:
    location:
      type: app_store
      name: tk-framework-widget
      version: v1.2.0
