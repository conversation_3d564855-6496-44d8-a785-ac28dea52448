#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

"""
An app that loads a template specific to the project and sets all relevant
project settings found in SG:
    shot settings:
        - shot sg_cut_in (first shot frame)
        - shot sg_cut_out (last shot frame)
    project settings:
        - frame rate
        - resolution
        - working color space

"""

from tank import Hook
from tank import TankError
from tank.platform import Application
from tank.platform.qt import QtCore, QtGui
import tank

import os
import re
import pprint


QApplication = QtGui.QApplication
QDialog = QtGui.QDialog
QWidget = QtGui.QWidget
QGridLayout = QtGui.QGridLayout
QLabel = QtGui.QLabel
QFont = QtGui.QFont
QComboBox = QtGui.QComboBox
QPushButton = QtGui.QPushButton
QHBoxLayout = QtGui.QHBoxLayout
QPixmap = QtGui.QPixmap
Qt = QtGui.Qt


pp = pprint.pprint
pf = pprint.pformat


class ProcessItemsHook(Hook):
    selected_preset = None

    def execute(self, **kwargs):
        self.adobe = self.parent.engine.adobe
        self.parent.engine.logger.info(
            "execute action apply_presets start. {}".format("-" * 80)
        )

        result = self.apply_presets_main()

        self.parent.engine.logger.info(
            "execute action apply_presets end. {}".format("-" * 80)
        )

        return result

    def apply_presets_main(self):

        result = {"succes": [1], "messages": [], "errors": []}

        # Get relevant data for the hook
        project_data = self.get_project_data_from_SG()
        presets_dict = self.get_presets_dict(project_data)

        self.parent.engine.logger.debug("project_data:\n{}".format(pf(project_data)))

        if not presets_dict:
            msg = "Couldn't resolve afx presets"
            self.parent.engine.logger.error("error: {}".format(msg))
            self.show_message(msg, icon="Critical")

            return {"succes": [], "messages": [msg], "errors": [1]}

        self.parent.engine.logger.debug("presets_dict:\n{}".format(pf(presets_dict)))

        # get the currently selected layer
        selected_layer = self.get_currently_selected_layer()
        if not selected_layer:
            msg = "Couldn't get current layer"
            self.parent.engine.logger.error("error: {}".format(msg))
            self.show_message(msg, icon="Critical")

            return {"succes": [], "messages": [msg], "errors": [1]}

        # ask the user to select a preset from the ui
        # selected_asset, selected_preset = self.get_selected_preset(
        #     presets_dict, project_data
        # )
        self.get_selected_preset(presets_dict, project_data)

        if not self.selected_asset:
            msg = "Couldn't get selected asset"
            self.parent.engine.logger.error("error: {}".format(msg))
            self.show_message(msg, icon="Critical")

            return {"succes": [], "messages": [msg], "errors": [1]}

        if not self.selected_preset:
            msg = "Couldn't get selected preset"
            self.parent.engine.logger.error("error: {}".format(msg))
            self.show_message(msg, icon="Critical")

            return {"succes": [], "messages": [msg], "errors": [1]}

        self.parent.engine.logger.info("selected_asset: {}".format(self.selected_asset))
        self.parent.engine.logger.info(
            "selected_preset: {}".format(self.selected_preset)
        )

        # get the path of the preset
        preset_path = self.get_preset_path(presets_dict)
        if not preset_path:
            msg = "Couldn't get preset path"
            self.parent.engine.logger.error("error: {}".format(msg))
            self.show_message(msg, icon="Critical")

            return {"succes": [], "messages": [msg], "errors": [1]}

        self.parent.engine.logger.info("preset_path: {}".format(preset_path))

        # apply the preset to the layer
        success = self.apply_preset_to_layer(selected_layer, preset_path)

        if success:
            msg = "Successfully applied preset '{}' to layer '{}'".format(
                self.selected_preset, selected_layer.name
            )
            self.parent.engine.logger.info(msg)
            # self.show_message(msg, icon="Information")

            return {"succes": [1], "messages": [msg], "errors": []}
        else:
            msg = "Failed to apply preset '{}' to layer '{}'".format(
                self.selected_preset, selected_layer.name
            )
            self.parent.engine.logger.error(msg)
            self.show_message(msg, icon="Critical")

            return {"succes": [], "messages": [msg], "errors": [1]}

    def get_project_data_from_SG(self):
        """
        Returns a dictionary of project data from SG relevant to a
        shot of the following form:

            {
                'code': (str),
                'id': (int),
                'sg_fps': (float),
                'sg_output_color_space': (str),
                'sg_working_color_space': (str),
                'type': (str),
            }
        """

        project_data = {}

        filters = [
            ["id", "is", self.parent.engine.context.project["id"]],
        ]
        fields = [
            "code",
            "id",
            "sg_fps",
            "sg_working_color_space",
            "sg_output_color_space",
        ]

        project_data = self.parent.engine.shotgun.find_one(
            entity_type="Project", filters=filters, fields=fields
        )

        return project_data

    def get_presets_dict(self, project_data):
        """
        Tries to find presets and images per project in the configuration.

        The name of the presets must have the following structure:
        {project code}_{assetName}_{rigName}_{presetname}.ffx

        The name of the images must have the following structure:
        {project code}_{assetName}_{rigName}_{presetname}.png

        As can be noticed in the above lines, the name struccture is the same for
        bot presets and images. Images MUST be png files, and both and must be
        located in:

        {config}/tk-shotgun-executeaction/afx/presets/{project code}/{asset name}

        {asset name} not necessarily needs to match a SG asset name, is just a reference
        to what that preset needs to be applied to, for instance something like
        'character' is enough if the same preset applies to all characters.

        {project code} is a special token that must match with the code
        of the current SG project

        if at least some presets and images have been found, it returns a dictionary
        with the following structure, otherwise it returns None

            Args:
                project_data (dict): SG dictionary containing project data

            Returns:
                A dict mapping asset names with their corresponding presets and images,
                as well as other relevant keys gathered from the file names

                {
                    asset1: {
                        preset_name_1: {
                            preset: path/to/preset/file/{project code}_asset1_{rigName}_{presetname}.ffx.ffx,
                            image: path/to/image/file/{project code}_asset1_{rigName}_{presetname}.png.png,
                            project_code: {project code},
                            rig_name: {rigName},
                            preset_name: {presetname},
                        },
                        preset_name_2: {
                            preset: path/to/preset/file/{project code}_asset1_{rigName}_{presetname}.ffx.ffx,
                            image: path/to/image/file/{project code}_asset1_{rigName}_{presetname}.png.png,
                            project_code: {project code},
                            rig_name: {rigName},
                            preset_name: {presetname},
                        },
                    },
                    asset2: {
                        preset_name_1: {
                            preset: path/to/preset/file/{project code}_asset1_{rigName}_{presetname}.ffx.ffx,
                            image: path/to/image/file/{project code}_asset1_{rigName}_{presetname}.png.png,
                            project_code: {project code},
                            rig_name: {rigName},
                            preset_name: {presetname},
                        },
                    },
                    ...
                }

        """

        presets_dict = {}

        project_code = project_data.get("code")
        self.parent.engine.logger.info("project_code: {}".format(project_code))

        hooks_location = os.path.dirname(os.path.dirname(self.disk_location))
        self.parent.engine.logger.info("hooks_location: {}".format(hooks_location))

        presets_location = os.path.join(
            hooks_location,
            "tk-shotgun-executeaction",
            "afx",
            "presets",
            project_code,
        )
        self.parent.engine.logger.info("presets_location: {}".format(presets_location))

        if not presets_location or not os.path.exists(presets_location):
            return None

        # regex pattern to split the files into the right keys
        pattern = re.compile(
            r"(?P<project_code>[a-zA-Z0-1]*)_"
            r"(?P<asset>[a-zA-Z0-1]*)_"
            r"(?P<rig_name>[a-zA-Z0-1]*)_"
            r"(?P<preset_name>[a-zA-Z0-1]*)"
            r"(?P<extension>\.[a-zA-Z0-1]*)$"
        )

        # get the names of subfolders, aka asset names
        subfolders = [
            f
            for f in os.listdir(presets_location)
            if os.path.isdir(os.path.join(presets_location, f))
        ]

        for subfolder in subfolders:
            subfolder_path = os.path.join(presets_location, subfolder)
            subfolder_files = os.listdir(subfolder_path)

            # create a nested dictionary for this subfolder
            subfolder_dict = {
                "asset_display_preset_names": [],
            }

            # iterate through files in the subfolder
            for file in subfolder_files:
                file_path = os.path.join(subfolder_path, file)

                # try to match the extra keys
                match = re.match(pattern, file)
                if not match:
                    self.parent.engine.logger.warning(
                        "Couldn't match file name: {}".format(file)
                    )
                    continue

                rig_name = match.groupdict().get("rig_name", None)
                preset_name = match.groupdict().get("preset_name", None)

                preset_full_name = "{}_{}_{}_{}".format(
                    project_code, subfolder, rig_name, preset_name
                )
                preset_display_name = "{}_{}_{}".format(
                    subfolder,
                    rig_name,
                    preset_name,
                )

                if not subfolder_dict.get(preset_full_name, None):
                    subfolder_dict[preset_full_name] = {
                        "project_code": project_code,
                        "asset": subfolder,
                        "rig_name": rig_name,
                        "preset_name": preset_name,
                    }

                # check the file extension
                if file.lower().endswith(".ffx"):
                    subfolder_dict[preset_full_name]["preset_path"] = file_path
                    subfolder_dict["asset_display_preset_names"].append(
                        preset_display_name
                    )

                elif file.lower().endswith(".png"):
                    subfolder_dict[preset_full_name]["image_path"] = file_path

            # add the subfolder's dictionary to the presets_dict
            presets_dict[subfolder] = subfolder_dict

        if presets_dict:
            return presets_dict
        else:
            return None

    def get_currently_selected_layer(self):
        """
        Returns the currently selected layer.

        Returns:
            The currently selected layer, if one is selected. None otherwise.
        """

        # adobe = self.parent.engine.adobe
        current_layer = self.adobe.app.project.activeItem

        if current_layer:
            return current_layer
        else:
            return None

    def get_selected_preset(self, presets_dict, project_data):
        project_code = project_data.get("code")

        # function to update the presets combo
        def update_gof_light_combo(selection):
            presets_combo.clear()

            # get selection items. 'selection' is the name of the asset, so from there,
            # we need to get the names of the presets
            asset_presets = presets_dict[selection]["asset_display_preset_names"]
            presets_combo.addItems(asset_presets)

        # function to update the image based on the selection
        def update_image(asset):
            selected_item = presets_combo.currentText()

            # from the selected preset, which is the display name, we need to solve
            # the full name of the preset to get the path of the image
            for preset in presets_dict[asset].keys():
                if preset.endswith(selected_item):
                    image_path = presets_dict[asset][preset]["image_path"]
                    break

            # fix path
            image_path = image_path.replace("\\", "/")
            pixmap = QPixmap(image_path)

            # calculate the maximum width while maintaining the aspect ratio
            max_width = 300
            scaled_image = pixmap.scaledToWidth(
                max_width, QtCore.Qt.TransformationMode.FastTransformation
            )
            image_label.setPixmap(scaled_image)

        # function to capture the selected value when "Apply" is pressed
        def apply_button_pressed():
            self.selected_asset = rig_names_combo.currentText()
            self.selected_preset = presets_combo.currentText()
            window.close()

        # function to set selected_gof_light to None when "Cancel" is pressed or the window is closed
        def cancel_button_pressed():
            self.selected_asset = None
            self.selected_preset = None
            window.close()

        # ----------------------------------------------------------------------------------

        # get assets list and presets display names dict
        assets = [asset for asset in presets_dict.keys()]
        assets.sort()
        first_asset_preset_names = presets_dict[assets[0]]["asset_display_preset_names"]

        app = QApplication.instance()

        # create a main window
        window = QDialog()
        window.setWindowModality(Qt.ApplicationModal)
        window.setWindowTitle("AFX Apply Preset")
        window.setGeometry(
            100, 100, 400, 300
        )  # Adjusted the window's size to accommodate the image

        # create a grid layout
        grid = QGridLayout()

        # create and configure the "Category" label
        rig_label = QLabel("Category")
        rig_label.setFont(QFont("Arial", 14, QFont.Bold))

        # create and configure the "Preset" label
        preset_label = QLabel("Preset")
        preset_label.setFont(QFont("Arial", 14, QFont.Bold))

        # create the rig_names combo box and add options
        rig_names_combo = QComboBox()
        rig_names_combo.addItems(assets)

        # create the presets combo box
        presets_combo = QComboBox()
        # Set default values
        presets_combo.addItems(presets_dict[assets[0]]["asset_display_preset_names"])

        # create a label for the image
        image_label = QLabel()

        # load an image and set it in the label
        default_image = (
            presets_dict[assets[0]]
            .get("{}_{}".format(project_code, first_asset_preset_names[0]), {})
            .get("image_path")
        )
        pixmap = QPixmap(default_image)

        # calculate the maximum width while maintaining the aspect ratio
        max_width = 300
        scaled_image = pixmap.scaledToWidth(
            max_width, QtCore.Qt.TransformationMode.FastTransformation
        )
        image_label.setPixmap(scaled_image)

        # connect the update functions to the appropriate signals
        rig_names_combo.currentIndexChanged.connect(
            lambda: update_gof_light_combo(rig_names_combo.currentText())
        )
        presets_combo.currentIndexChanged.connect(
            lambda: update_image(rig_names_combo.currentText())
        )
        # create a horizontal layout for buttons
        button_layout = QHBoxLayout()

        # create the Apply button
        apply_button = QPushButton("Apply")

        # create the Cancel button
        cancel_button = QPushButton("Cancel")

        # add buttons to the button layout and connect them to their respective functions
        button_layout.addWidget(apply_button)
        button_layout.addWidget(cancel_button)
        apply_button.clicked.connect(apply_button_pressed)
        cancel_button.clicked.connect(cancel_button_pressed)

        image_label.setPixmap(scaled_image)

        # add the labels, combo boxes, and image to the grid layout
        grid.addWidget(rig_label, 0, 0)  # Rig label in top left
        grid.addWidget(preset_label, 0, 1)  # Preset label in top right
        grid.addWidget(rig_names_combo, 1, 0)  # Lights/Shadows combo in bottom left
        grid.addWidget(presets_combo, 1, 1)  # GOF Light combo in bottom right
        grid.addWidget(
            image_label, 2, 0, 1, 2
        )  # Image in the middle, spanning two columns

        # add the button layout at the bottom
        grid.addLayout(button_layout, 3, 0, 1, 2)

        # set the main window's layout to the grid layout
        window.setLayout(grid)

        # show the main window
        window.exec_()

        return self.selected_asset, self.selected_preset

    def get_preset_path(self, presets_dict):
        # from the selected preset, which is the display name, we need to solve
        # the full name of the preset to get the path of the image
        for preset in presets_dict[self.selected_asset].keys():
            if preset.endswith(self.selected_preset):
                preset_path = presets_dict[self.selected_asset][preset]["preset_path"]

                return preset_path
        # return None if the template path couldn't be resolved
        return None

    def apply_preset_to_layer(self, layer, preset_path):
        # adobe = self.parent.engine.adobe

        # first create a file object
        preset_file = self.adobe.File(preset_path)
        if preset_file:
            layer.applyPreset(preset_file)
            return True
        else:
            msg = "Couldn't apply preset '{}' to layer '{}'".format(
                os.path.basename(preset_path), layer.name
            )
            self.parent.engine.logger.error(msg)
            self.parent.engine.logger.info("error: {}".format(msg))
            self.show_message(msg, icon="Critical")
            return False
        return False

    def show_message(self, msg, icon=None):
        """
        Shows a message box with the input message
        """

        icons_dict = {
            "NoIcon": QtGui.QMessageBox.NoIcon,
            "Question": QtGui.QMessageBox.Question,
            "Information": QtGui.QMessageBox.Information,
            "Warning": QtGui.QMessageBox.Warning,
            "Critical": QtGui.QMessageBox.Critical,
        }

        if not icon or icon not in icons_dict.keys():
            icon_selection = icons_dict["NoIcon"]
        else:
            icon_selection = icons_dict[icon]

        msg_error = QtGui.QMessageBox()
        msg_error.setWindowTitle("Finished execution.")
        msg_error.setText(msg)
        msg_error.setIcon(icon_selection)
        msg_error.exec_()
