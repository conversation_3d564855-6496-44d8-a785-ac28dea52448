includes:
  - ../app_locations.yml


# MAYA: shot_step
settings.mty-multi-batchloader.maya.shot_step:
  location: "@apps.mty-multi-batchloader.location"
  loaders_definitions:
    - {
      name: load_rigs,
      process_hook: "{config}/mty-multi-batchloader/maya/batchload_reference_rigs.py",
      scan_hook: "{config}/mty-multi-batchloader/maya/batchload_validate_rigs.py",
    }
    - {
      name: load_shaded_assets,
      process_hook: "{config}/mty-multi-batchloader/maya/batchload_process_shaded_assets.py",
      scan_hook: "{config}/mty-multi-batchloader/multi/batchload_scan_shaded_assets.py",
    }
    - {
      name: load_animation_caches,
      process_hook: "{config}/mty-multi-batchloader/maya/batchload_process_animation_caches.py",
      scan_hook: "{config}/mty-multi-batchloader/multi/batchload_scan_animation_caches.py",
    }
    - {
      name: load_camera_alembic_cache,
      process_hook: "{config}/mty-multi-batchloader/maya/batchload_process_shot_camera_alembic_cache.py",
      scan_hook: "{config}/mty-multi-batchloader/multi/batchload_scan_shot_camera_alembic_cache.py",
    }
    - {
      name: load_audios,
      scan_hook: "{config}/mty-multi-batchloader/multi/batchload_scan_audios.py",
      process_hook: "{config}/mty-multi-batchloader/maya/batchload_process_audios.py",
    }

#  location: '@apps.mty-multi-batchloader.location'
#  loaders_definitions:
#    - {
#      name: load_rigs,
#      process_hook: "{config}/mty-multi-batchloader/reference_rigs.py",
#      scan_hook: "{config}/mty-multi-batchloader/sceneScanner.py",
#    }
#  map_of_utilities_hooks: {
#    assets: {
#      scanner: "{config}/mty-multi-batchloader/assets/assetsScanner.py",
#      loader: "{config}/mty-multi-batchloader/assets/rigLoader.py",
#      filters: '{config}/mty-multi-batchloader/assets/assetsFilter.py'
#    }
#  }
#  queries_hook: '{config}/mty-multi-batchloader/sgQueries.py'
#  transfers_hook: '{config}/mty-multi-batchloader/transfers.py'
#  map_of_publish_types: {
#    overrides: Shot Environment Overrides
#  }
#  list_of_rigged_asset_types:
#    - Character
#    - Prop
#    - EnvProp
#  list_of_environment_types:
#    - EnvLocation
#    - EnvModule


  tasks_for_avoiding_asset_scanner:
    - lgt3dLighting
    - rnd3dLighting

  tasks_for_avoiding_scanning_for_rigs_and_envs:
    - lgt3dLighting
    - rnd3dLighting

  rigging_task_names:
    - fullrig
    - bodyrig
    - proxyrig
    - rig3dRigging

  modeling_task_names:
    - mdl
    - modeling
    - mdlModeling
    - mdlUVs

  supported_tasks_for_loading_shaded_assets:
    - lgt3dLighting
    - rnd3dLighting

  shaded_assets_task_priority_list:
    - sfcShading

  shaded_3d_asset_type:
    - Character
    - Prop
    - EnvProp

  shaded_assets_3d_published_file_type_names:
    - Maya Scene

  supported_tasks_for_loading_anim_caches:
    - lgt3dLighting
    - rnd3dLighting

  anim_caches_task_priority_list:
    - anm3dPolish
    - fx3dFX

  anim_caches_published_file_type_names:
    - Maya Shot anm Alembic
    - Maya Shot Geo Alembic

  supported_tasks_for_loading_shot_camera_cache:
    - lgt3dLighting
    - rnd3dLighting

  shot_camera_cache_task_priority_list:
    - anm3dPolish
    - anm3dBlocking
    - lyt3dLayout
    - trk3dTracking

  shot_camera_cache_published_file_type_names:
    - Shot Camera Alembic
    - Shot Camera Cache

# HARMONY: shot_step
settings.mty-multi-batchloader.harmony.shot_step:
  actions_hook: '{config}/tk-multi-loader2/tk-harmony_actions.py'
  actions_method: 'execute_action'
  loaders_definitions:
    - {
      name: load_rigs,
      scan_hook: "{config}/mty-multi-batchloader/multi/batchload_scan_rigs.py",
      process_hook: "{config}/mty-multi-batchloader/harmony/batchload_process_rigs.py",
    }
    - {
      name: load_bgs,
      scan_hook: "{config}/mty-multi-batchloader/multi/batchload_scan_bgs.py",
      process_hook: "{config}/mty-multi-batchloader/harmony/batchload_process_bgs.py",
    }
    - {
      name: load_audios,
      scan_hook: "{config}/mty-multi-batchloader/multi/batchload_scan_audios.py",
      process_hook: "{config}/mty-multi-batchloader/harmony/batchload_process_audios.py",
    }
    - {
      name: load_animatics,
      scan_hook: "{config}/mty-multi-batchloader/multi/batchload_scan_animatics.py",
      process_hook: "{config}/mty-multi-batchloader/harmony/batchload_process_animatics.py",
    }
    - {
      name: load_fx_assets,
      scan_hook: "{config}/mty-multi-batchloader/multi/batchload_scan_fx_assets.py",
      process_hook: "{config}/mty-multi-batchloader/harmony/batchload_process_fx_assets.py",
    }
    - {
      name: load_3d_assets,
      scan_hook: "{config}/mty-multi-batchloader/multi/batchload_scan_3d_assets.py",
      process_hook: "{config}/mty-multi-batchloader/harmony/batchload_process_3d_assets.py",
    }
    - {
      name: load_photoshop,
      scan_hook: "{config}/mty-multi-batchloader/multi/batchload_scan_photoshop.py",
      process_hook: "{config}/mty-multi-batchloader/harmony/batchload_process_photoshop.py",
    }
  location: "@apps.mty-multi-batchloader.location"
  rigs_tasks_list:
    - rig2DRigging
    - refReference

  rigs_asset_type:
    - Character
    - Prop
    - Production

  animatics_tasks_list:
    - edlAnimatic

  audios_tasks_list:
    - edlAnimatic

  backgrounds_tasks_list:
    - bgColor
    - bgClean
    - bgRough

  backgrounds_asset_type:
    - Background

  fx_assets_tasks_list:
    - artConcept
    - artDesign

  fx_asset_type:
    - FX

  object_3d_task_priority_list:
    - mdlModeling
    - Rig

  object_3d_asset_type:
    - Character
    - Prop
    - EnvProp
    - Production

  assets_3d_published_file_type_names:
    - OBJ Mesh
    - FBX Mesh

  photoshop_tasks_list:
    - refReference

  photoshop_asset_type:
    - Photoshop Image

# BLENDER: shot_step
settings.mty-multi-batchloader.blender.shot_step:
  location: "@apps.mty-multi-batchloader.location"
  actions_hook: '{config}/tk-multi-loader2/tk-blender_actions.py'
  actions_method: 'execute_action'
  loaders_definitions:
    - {
      name: load_camera_alembic_cache,
      scan_hook: "{config}/mty-multi-batchloader/multi/batchload_scan_shot_camera_alembic_cache.py",
      process_hook: "{config}/mty-multi-batchloader/blender/batchload_process_shot_camera_alembic_cache.py",
    }
    - {
      name: load_audios,
      scan_hook: "{config}/mty-multi-batchloader/multi/batchload_scan_audios.py",
      process_hook: "{config}/mty-multi-batchloader/blender/batchload_process_audios.py",
    }
    - {
      name: load_textures,
      scan_hook: "{config}/mty-multi-batchloader/multi/batchload_scan_textures.py",
      process_hook: "{config}/mty-multi-batchloader/blender/batchload_process_textures.py",
    }
    - {
      name: load_alembic_caches,
      scan_hook: "{config}/mty-multi-batchloader/multi/batchload_scan_alembic_caches.py",
      process_hook: "{config}/mty-multi-batchloader/blender/batchload_process_alembic_caches.py",
    }
    - {
      name: load_blend_files,
      scan_hook: "{config}/mty-multi-batchloader/multi/batchload_scan_blend_files.py",
      process_hook: "{config}/mty-multi-batchloader/blender/batchload_process_blend_files.py",
    }
  audios_tasks_list:
    - edlAnimatic

  shot_camera_cache_task_priority_list:
    - anm3dPolish
    - anm3dBlocking
    - lyt3dLayout
    - trk3dTracking

  shot_camera_cache_published_file_type_names:
    - Shot Camera Alembic
    - Shot Camera Cache
