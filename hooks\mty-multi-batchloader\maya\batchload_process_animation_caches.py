########################################################################################
#
# Copyright (c) 2024 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
########################################################################################


import os
import json
import pprint
import traceback

import sgtk

from tank import Hook
from tank import TankError
from tank.platform.qt import QtCore, QtGui

import maya.cmds as cmds
import maya.mel as mel
import pymel.core as pm


class ProcessItemsHook(Hook):
    """
    Process items to load them into the scene
    """

    def load_item(self, item, update_progress):
        """
        The load item method is executed once for each defined item and its purpose is
        to process the loading of the item into the scene.

        The structure of each item is a dictionary with three keys:

        - "node": The name of the 'node' that is to be operated on. Most DCCs have
            a concept of a node, path, or some other way to address a particular
            object in the scene.
        - "type": The object type that this is. This is to
            know how to handle the object.
        - "path": Path on disk to the object to be loaded.
        """

        CoreMayaTools = self.load_framework("mty-framework-coremayatools")
        MayaMiscUtils = CoreMayaTools.MayaMiscUtils

        if item["type"] == "Animation Cache":
            update_progress({"progress": 30, "message": "Referencing Shaded Assets..."})

            anim_cache_path = item["path"]
            anim_cache_path = self.fix_path(anim_cache_path)
            namespace = item.get("other_params", {}).get("namespace", "")

            self.parent.logger.info("Loading anim cache: {}".format(anim_cache_path))

            # query scene references to find all existing namespaces
            scene_references = pm.listReferences()
            existing_namespaces = [ref.namespace for ref in scene_references]

            # return if there is no a reference with the needed namespace and show an
            # error message
            if not namespace in existing_namespaces:
                msg = (
                    "Couldn't find a reference with namespace: '{}', so the animation "
                    "cache cannot be applied."
                ).format(namespace)
                self.parent.logger.error(msg)
                self.show_message(msg, "Critical")
                return

            # If there is a reference with the same name space, we assume this is the
            # shaded model, but we need to find the top cached node, usually called
            # model_high_root
            for ref in scene_references:
                top_most_cached_transform = None
                if ref.namespace == namespace:
                    first_node = ref.nodes()[0]
                    top_most_cached_transform = self.get_model_high_root_node(first_node)
                    break

            if not top_most_cached_transform:
                msg = (
                    "Couldn't find the top_most_cached_transform node of a reference "
                    "with namespace: '{}', so the animation cache cannot be applied."
                ).format(namespace)
                self.parent.logger.error(msg)
                self.show_message(msg, "Critical")
                return

            self.parent.logger.info("first_node: {}".format(first_node))
            self.parent.logger.info(
                "top_most_cached_transform: {}".format(top_most_cached_transform)
            )

            # do the abc import. It will be applied to the existing geometry
            original_selection = cmds.ls(selection=True)
            # cmds.select(first_node)

            # make sure the AbcImport plugin is loaded
            if not cmds.pluginInfo("AbcImport", loaded=True, query=True):
                cmds.loadPlugin("AbcImport")

            command_args = [
                "AbcImport",
                "-mode",
                "import",
                "-connect",
                str(top_most_cached_transform),
                '"{}"'.format(anim_cache_path),
            ]

            cmd_string = " ".join(command_args)
            self.parent.logger.info("AbcImport command: {}".format(cmd_string))

            try:
                cmds.AbcImport(
                    anim_cache_path,
                    mode="import",
                    connect=str(top_most_cached_transform),
                )
                # mel.eval(" ".join(command_args))
            except Exception as e:
                msg = (
                    "Error loading animation cache: {}\n{}".format(anim_cache_path, e)
                )
                self.parent.logger.error(msg)
                self.parent.logger.error(traceback.format_exc())
                self.show_message(msg, "Critical")
                return

            self.parent.logger.info(
                "Successfully imported animation cache: {}".format(anim_cache_path)
            )


    def get_model_high_root_node(self, transform_node):
        result = list(
            filter(
                lambda x: 'model_high_root' in x.name(),
                transform_node.listRelatives(type='transform', allDescendents=True)
            )
        )

        if result:
            # get the top most transform node
            self.parent.engine.logger.info("filtered descendants: {}".format(result))
            top_most_transform = min(
                result, key=lambda x: x.longName().count("|")
            )
            self.parent.engine.logger.info(
                'top_most_transform: {}'.format(top_most_transform)
            )
            return top_most_transform
        else:
            return None


    def fix_path(self, path):
        """Replace all backward slashes with forward slashes."""

        path = path.replace("\\\\", "/")
        path = path.replace("\\", "/")

        return path


    def show_message(self, msg, icon=None):
        """
        Displays a message box with a specified message and icon.

        This method creates a QMessageBox to display a message to the user.
        The appearance of the message box can be customized using the 'icon' parameter.

        Args:
            msg (str): The message to be displayed in the message box.
            icon (str, optional): The icon to be displayed in the message box.
                Valid options are "NoIcon", "Question", "Information", "Warning", and "Critical".
                Defaults to "NoIcon" if not provided or invalid.
        """

        icons_dict = {
            "NoIcon": QtGui.QMessageBox.NoIcon,
            "Question": QtGui.QMessageBox.Question,
            "Information": QtGui.QMessageBox.Information,
            "Warning": QtGui.QMessageBox.Warning,
            "Critical": QtGui.QMessageBox.Critical,
        }

        if not icon or icon not in icons_dict.keys():
            icon_selection = icons_dict["NoIcon"]
        else:
            icon_selection = icons_dict[icon]

        msg_error = QtGui.QMessageBox()
        msg_error.setWindowTitle("Finished execution.")
        msg_error.setText(msg)
        msg_error.setIcon(icon_selection)
        msg_error.exec_()
