#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

import os
import traceback
import contextlib

import sgtk
from sgtk import TankError
from tempfile import NamedTemporaryFile
from sgtk.util.version import is_version_older
from sgtk.util.filesystem import copy_file, ensure_folder_exists

from krita import Krita, InfoObject

HookBaseClass = sgtk.get_hook_baseclass()

@contextlib.contextmanager
def _batch_mode(state):
    """
    A handy context for running things in Krita in batch mode
    """
    krita_app = Krita.instance()
    current_state = krita_app.batchmode()
    krita_app.setBatchmode(state)

    try:
        yield
    finally:
        krita_app.setBatchmode(current_state)


class KritaLayerPublishPlugin(HookBaseClass):
    """
    Plugin for publishing an open krita session.

    This hook relies on functionality found in the base file publisher hook in
    the publish2 app and should inherit from it in the configuration. The hook
    setting for this plugin should look something like this::

        hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py"

    """


    def get_path_from_work_template(self, settings, item, template):
        """
        Handy method to use the work template to resolve a different template.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        :param template: Target template to apply fields for.

        :returns: resolved path given the target template
        """
        path = None

        node_name = item.properties["node_name"]
        session_path = item.properties["session_path"]
        work_template = item.properties.get("work_template")
        layer_name = item.properties.get("publish_name")
        image_extension = "exr"

        # a bit of spaghetti code here, but we need to be throughout with
        # the way we report to the artists.
        if work_template and template:
            template_name = template.name

            if work_template.validate(session_path):
                work_fields = work_template.get_fields(session_path)
                work_fields["krita.layer_name"] = layer_name
                work_fields["krita.image_extension"] = image_extension

                missing_keys = template.missing_keys(work_fields, skip_defaults=True)
                if not missing_keys:
                    # best case, we have everything we need to export using a template
                    path = template.apply_fields(work_fields)
                else:
                    self.logger.warning(
                        "Work file '%s' missing keys required for the '%s' "
                        "template: %s." % (session_path, template_name, missing_keys)
                    )
            else:
                self.logger.warning(
                    "Work file '%s' did not match work template '%s'. "
                    % (session_path, template_name)
                )

        return path


    def _export_layer(self, node, export_layer_path, active_doc):
        krita_app = Krita.instance()
        active_doc_bounds = active_doc.rootNode().bounds()

        # old versions of Krita had a different signature for this function
        # unfortunately since this function is not a python one we cannot
        # inspect it, so we do have to check the Krita version to know
        # how to approach this export
        if is_version_older(krita_app.version(), "4.2.0"):
            self.parent.log_debug("Exporting as 4.2.0 to: %s" % export_layer_path)
            node.save(export_layer_path, active_doc.width(), active_doc.height())
        else:
            self.parent.log_debug("Exporting to: %s" % export_layer_path)

            settings = InfoObject()
            settings.setProperty("flatten", True)

            node.save(
                export_layer_path,
                active_doc.width(),
                active_doc.height(),
                settings,
                active_doc_bounds,
            )

def _session_document():
    """
    Return the current active document
    :return:
    """
    krita_app = Krita.instance()
    active_doc = krita_app.activeDocument()
    return active_doc


def _session_path():
    """
    Return the path to the current session
    :return:
    """
    path = None

    active_doc = _session_document()
    if active_doc:
        path = active_doc.fileName()

    return path


# TODO: method duplicated in all the krita hooks
def _get_save_as_action():
    """
    Simple helper for returning a log action dict for saving the session
    """

    engine = sgtk.platform.current_engine()

    callback = _save_as

    # if workfiles2 is configured, use that for file save
    if "tk-multi-workfiles2" in engine.apps:
        app = engine.apps["tk-multi-workfiles2"]
        if hasattr(app, "show_file_save_dlg"):
            callback = app.show_file_save_dlg

    return {
        "action_button": {
            "label": "Save As...",
            "tooltip": "Save the current session",
            "callback": callback,
        }
    }


def _save_as():
    krita_app = Krita.instance()
    krita_app.action("file_save_as").trigger()
