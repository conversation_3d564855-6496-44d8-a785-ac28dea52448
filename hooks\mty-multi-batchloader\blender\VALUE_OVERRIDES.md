# Value Overrides Configuration for Blender Batchloader

This document explains how to configure value overrides for the Blender batchloader hooks using the `mty-framework-valueoverrides` framework.

## Overview

Value overrides allow you to dynamically configure batchloader behavior based on context (Task, Shot, Project, etc.) without modifying the hook code. This is particularly useful for:

- Different texture requirements per project
- Task-specific loading limits
- Context-dependent file type filtering

## Implemented Overrides

### Texture Scan Hook (`batchload_scan_textures.py`)

The following override codes are available:

#### `mty.multi.batchloader.scan.texture_types`
**Type:** JSON Array  
**Default:** `["Texture", "Image", "Rendered Image", "Photoshop Image", "Photoshop Proxy Image"]`  
**Description:** Defines which published file types to scan for textures.

**Example:**
```json
["Texture", "Image", "Rendered Image"]
```

#### `mty.multi.batchloader.scan.max_textures_to_load`
**Type:** Integer  
**Default:** `50`  
**Description:** Maximum number of textures to load in a single batch operation.

**Example:**
```
100
```

#### `mty.multi.batchloader.scan.include_proxy_textures`
**Type:** Boolean String  
**Default:** `"true"`  
**Description:** Whether to include proxy textures in the scan results.

**Example:**
```
false
```

## How to Set Up Overrides

### 1. Using ShotGrid Interface

1. Navigate to the entity you want to override (Task, Shot, Project, etc.)
2. Create a new Value Override record
3. Set the `Code` field to one of the override codes above
4. Set the `Value` field to your desired configuration
5. Link it to the appropriate entity

### 2. Override Context Types

The texture scan hook supports overrides linked to:
- **Task**: Override applies to specific tasks
- **Shot**: Override applies to specific shots  
- **Project**: Override applies to entire projects

### 3. Override Priority

The system follows this priority order:
1. Task-specific overrides (highest priority)
2. Shot-specific overrides
3. Project-specific overrides
4. Default values from hook settings (lowest priority)

## Example Scenarios

### Scenario 1: Limit Textures for Animation Tasks
For animation tasks that don't need many textures:

- **Override Code:** `mty.multi.batchloader.scan.max_textures_to_load`
- **Value:** `10`
- **Link to:** Animation tasks

### Scenario 2: Exclude Proxy Textures for Final Renders
For lighting/rendering tasks that need full resolution:

- **Override Code:** `mty.multi.batchloader.scan.include_proxy_textures`
- **Value:** `false`
- **Link to:** Lighting tasks

### Scenario 3: Project-Specific Texture Types
For a project that uses custom texture types:

- **Override Code:** `mty.multi.batchloader.scan.texture_types`
- **Value:** `["Texture", "Custom Texture", "HDR Image"]`
- **Link to:** Specific project

## Adding New Overrides

To add new override support to other hooks, follow this pattern:

```python
# Load value overrides framework
valueoverrides = self.parent.engine.custom_frameworks.get(
    "mty-framework-valueoverrides"
) or self.load_framework("mty-framework-valueoverrides")

if valueoverrides:
    override_link = {
        "type": "Task",  # or "Shot", "Project", etc.
        "id": current_context.task["id"],
    }
    
    # Define your override code
    override_code = "mty.multi.batchloader.scan.your_setting_name"
    override_value = valueoverrides.get_value(override_code, link=override_link)
    
    if override_value:
        # Apply the override
        your_setting = json.loads(override_value)  # if JSON
        # or
        your_setting = override_value  # if simple string/number
```

## Override Code Naming Convention

Use this naming pattern for consistency:
```
mty.multi.batchloader.[hook_type].[setting_name]
```

Where:
- `hook_type`: `scan` or `process`
- `setting_name`: descriptive name of the setting

Examples:
- `mty.multi.batchloader.scan.texture_types`
- `mty.multi.batchloader.scan.max_items_to_load`
- `mty.multi.batchloader.process.default_load_method`

## Testing Overrides

1. Set up your override in ShotGrid
2. Open Blender in the appropriate context
3. Run the batchloader
4. Check the logs for messages like:
   ```
   Using texture types from override: ["Texture", "Image"]
   ```
5. Verify the behavior matches your override configuration
