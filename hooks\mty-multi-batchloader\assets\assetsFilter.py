# -*- coding: utf-8 -*-
# Standard library:
# ___   ___   ___   ___   ___   ___  ___
# Third party:
import sgtk

# ___   ___   ___   ___   ___   ___  ___
# Project:
HookBaseClass = sgtk.get_hook_baseclass()


# ====================================================================


class AssetsFilter(HookBaseClass):
    def __init__(self, parent):
        super(AssetsFilter, self).__init__(parent)

    def published_files_by_names_and_types(
            self, list_of_publishes, list_of_asset_names, list_of_types
    ):
        result = []
        list_of_already_processed_nodes = []

        type_field = 'entity.Asset.sg_asset_type'
        list_of_publishes_filtered = [
            _p for _p in list_of_publishes
            if _p[type_field] in list_of_types
        ]

        for _pub in list_of_publishes_filtered:
            _name = _pub['entity']['name']
            if (
                    _name in list_of_asset_names and
                    _name not in list_of_already_processed_nodes
            ):
                result.append({
                    'name': _pub['entity.Asset.code'],
                    'path': _pub['path']['local_path'],
                    'publish': _pub
                })
                list_of_already_processed_nodes.append(_name)

        return result
