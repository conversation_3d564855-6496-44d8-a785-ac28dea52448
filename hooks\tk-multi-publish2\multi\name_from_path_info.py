import os
import pprint
from imp import reload
from tank.util import sgre as re

import sgtk

HookBaseClass = sgtk.get_hook_baseclass()

class NameFromPathInfo(HookBaseClass):
    """
    Methods for basic file path parsing.
    """

    def __init__(self, parent):
        super(NameFromPathInfo, self).__init__(parent)

    def get_publish_name(self, path, sequence=False):
        """
        Wrapper for returning the display name used for publishing.

        Typically, this is a name where the path and any version number are
        removed in order to keep the publish name consistent as subsequent
        versions are published.

        Example::

            # versioned file. remove the version
            in: /path/to/the/file/scene.v001.ma
            out: scene.ma

        :param path: The path to a file, likely one to be published.

        :return: A publish display name for the provided path.
        """

        publisher = self.parent

        logger = publisher.logger
        self.parent.engine.logger.debug("{}".format("-" * 80)
        )
        self.parent.engine.logger.debug(
            "Getting publish name for path: {} ...".format(path)
        )

        import name_from_path_module
        reload(name_from_path_module)
        filename = name_from_path_module.get_publish_name(path, sequence=False)

        self.parent.engine.logger.debug("Returned publish name: {}".format(filename))

        return filename


    def get_version_number(self, path):
        """
        Wrapper to extract a version number from the supplied path.

        This is used by plugins that need to know what version number to
        associate with the file when publishing.

        :param path: The path to a file, likely one to be published.

        :return: An integer representing the version number in the supplied
            path. If no version found, ``None`` will be returned.
        """

        publisher = self.parent

        logger = publisher.logger
        logger.debug("Getting version number for path: %s ..." % (path,))

        import name_from_path_module
        reload(name_from_path_module)
        version_number = name_from_path_module.get_version_number(path)

        self.parent.engine.logger.debug("Returned version number: {}".format(version_number))

        return version_number
