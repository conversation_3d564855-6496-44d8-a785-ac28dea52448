#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

import os
import sys
import json
import traceback
from tank import Hook
from html import entities
from pprint import pformat
from tank.platform.qt import QtCore, QtGui



class ScanSceneHook(Hook):

    def scan_scene(self):

        self.parent.engine.logger.info("")
        self.parent.engine.logger.info(
            "Scanning scene for rigs...".ljust(120, "-")
        )

        items_result = []
        warnings = []
        errors = []

        versions = []
        last_versions = {}
        last_versions_list = []

        shot_id = self.parent.context.entity['id']

        filters = [['id', 'is', shot_id]]
        fields = ['assets']
        data_shot = self.parent.shotgun.find_one('Shot', filters, fields)

        assets_id = [asset["id"] for asset in data_shot["assets"]]

        rigs_asset_type = self.parent.get_setting("rigs_asset_type")

        # ------------------------------------------------------------------------------
        # Load value overrides framework
        valueoverrides = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        ) or self.load_framework("mty-framework-valueoverrides")

        if not valueoverrides:
            self.parent.engine.logger.error("Couldn't load value overrides framework")
            rigs_tasks_list = self.parent.get_setting(
                "rigs_tasks_list"
            )
            self.parent.engine.logger.info(
                "rigs_tasks_list from hook settings: {}".format(
                    rigs_tasks_list
                )
            )

        # Get task priority list
        if valueoverrides:
            default_value_code = "mty.multi.batchloader.scan.rigs_priority_list"
            override_link = {"type": "Task", "id": self.parent.engine.context.task["id"]}
            rigs_tasks_list = valueoverrides.get_value(
                default_value_code, link=override_link
            )
            if rigs_tasks_list:
                rigs_tasks_list = json.loads(rigs_tasks_list)
                self.parent.engine.logger.info(
                    "rigs_tasks_list from override: {}".format(
                        rigs_tasks_list
                    )
                )

        published_file_type_names = ['Harmony TPL']

        if not assets_id:
            items_result.append({})
            return items_result, warnings, errors

        filters = [
            ["entity.Asset.sg_asset_type", "in", rigs_asset_type],
            ['entity.Asset.id', 'in', assets_id],
            ['task.Task.content', 'in', rigs_tasks_list],
            ['sg_status_list', 'is', 'apr'],
            ['published_file_type.PublishedFileType.code', 'in', published_file_type_names]
        ]

        fields = [
            "code", "name", "published_file_type", "version_number", "task",
            "path", 'id'
        ]

        try:

            self.parent.log_debug("Scanning scene to get the rigs needed for the scene.")

            rigs = self.parent.shotgun.find(
                "PublishedFile", filters, fields
            )

            if not rigs:
                items_result.append({})
                return items_result, warnings, errors

            # Order by version number
            # ====================

            for rig in rigs:
                versions.append(
                    {
                        'version': rig['version_number'],
                        'name': rig['name'],
                        'path': rig['path']['local_path'],
                        'type': rig['published_file_type']['name'],
                        'task': rig['name'],
                        'sg_data': rig,
                    }
                )

            last_versions = {}

            for version in versions:
                name = version['name']
                version_number = version['version']
                path = version['path']
                sg_data = version

                if name in last_versions:
                    if version_number > last_versions[name]['version']:
                        last_versions[name] = {'version': version_number, 'path': path, 'sg_data': sg_data}
                else:
                    last_versions[name] = {'version': version_number, 'path': path, 'sg_data': sg_data}

            for name, version in last_versions.items():
                last_versions_list.append(
                    {
                        'name': name,
                        'version': version['version'],
                        'path': version['path'],
                        'sg_data': version['sg_data'],
                    }
                )

            for name, version in last_versions.items():
                items_result.append(
                    {
                        'node': name,
                        'path': version["path"],
                        'process_hook': 'batchload_process_rigs',
                        'type': 'Rig File',
                        'sg_data': version['sg_data'],
                    }
                )

            # Ensure metasync framework is available, but only load it once.
            if not hasattr(self, 'metasync'):
                self.metasync = self.parent.engine.custom_frameworks.get(
                "mty-framework-metasync"
            )
            if not self.metasync:
                self.metasync = self.load_framework("mty-framework-metasync")

            transfersManager = self.metasync.transfersManager

            for rig in items_result:
                if "path" in rig and "sg_data" in rig:
                    if not os.path.exists(rig['path']):
                        self.parent.logger.info("Rig not found: %s" % rig.get('path', None))
                        self.parent.logger.info("sg_data:\n{}".format(pformat(rig['sg_data'])))
                        transfersManager.ensure_file_is_local(
                            rig['path'], rig['sg_data']["sg_data"]
                        )
                    transfersManager.ensure_local_dependencies(rig.get('sg_data', {}).get('sg_data', None))

        except:
            import traceback

            error_str = traceback.format_exc()
            errors.append(error_str)
            self.parent.log_debug(error_str)

        return items_result, warnings, errors
