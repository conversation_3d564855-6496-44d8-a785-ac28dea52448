# Copyright (c) 2018 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

################################################################################

includes:
- ../app_locations.yml
- ../engine_locations.yml
- ./tk-multi-shotgunpanel.yml
- ./tk-multi-launchapp.yml
- ./tk-multi-publish2.yml
- ./tk-multi-screeningroom.yml
- ./tk-multi-loader2.yml
- ./sb-shotgun-schema-introspection.yml

################################################################################

# site
settings.tk-desktop.site:
  apps:
  location: '@engines.tk-desktop.location'
# project
settings.tk-desktop.project:
  apps:
    tk-multi-pythonconsole:
      location: '@apps.tk-multi-pythonconsole.location'
    tk-multi-devutils:
      location: '@apps.tk-multi-devutils.location'
    tk-multi-importcut:
      location: '@apps.tk-multi-importcut.location'
    tk-multi-loader2: '@settings.tk-multi-loader2.standalone'
    mty-publisharchiver:
      location: '@apps.mty-publisharchiver.location'
      deny_permissions: [Client, Vendor, Supervisor Legacy, Artist]
      cleanup_hook: '{config}/mty-publisharchiver/cleanup.py'
    mty-multi-queue:
      location: '@apps.mty-multi-queue.location'
    mty-multi-noteimporter:
      deny_permissions: [Client, Vendor, Supervisor Legacy]
      resolve_backends_hook: '{config}/mty-noteimporter/collect_backends.py'
      custom_playlist_filters: []
      custom_playlist_fields: []
      submitted_file_entity: CustomThreadedEntity10
      submitted_file_field: custom_threaded_entity10_sg_versions_custom_threaded_entity10s
      external_id_field: sg_external_id_1
      secondary_external_id_field: sg_external_id_2
      location: '@apps.mty-multi-noteimporter.location'
    mty-multi-deliveries:
      deny_permissions: [Artist, Client, Supervisor, Vendor, Supervisor Legacy]
      backend_framework_hook: '{config}/mty-deliveries/backend_collector.py'
      location: '@apps.mty-multi-deliveries.location'
    # mty-multi-metasync:
    #  location:
    #    type: dev
    #    path: D:\Development\Mighty\apps\mty-multi-metasync

    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel'
    sb-shotgun-schema-introspection: '@settings.sb-shotgun-schema-introspection'
    tk-multi-launchapp: '@settings.tk-multi-launchapp'
    tk-multi-launchhiero: '@settings.tk-multi-launchapp.hiero'
    tk-multi-launchmari: '@settings.tk-multi-launchapp.mari'
    tk-multi-launchmotionbuilder: '@settings.tk-multi-launchapp.motionbuilder'
    tk-multi-publish2: '@settings.tk-multi-publish2.standalone'
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
  groups:
  - matches:
    - '*Fla*'
    - '*Houdini*'
    - '*Mari*'
    - '*Max*'
    - '*Maya*'
    - '*Motion*'
    - '*Nuke*'
    - '*Photoshop*'
    - '*After*'
    - '*Effects*'
    - '*Harmony*'
    - '*Fusion*'
    - '*Krita*'
    - '*Substance*'
    - '*Blender*'
    name: Creative Tools
  - matches:
    - '*Hiero*'
    - '*Resolve*'
    - '*Premiere*'
    - '*Import Cut*'
    name: Editorial Tools
  - matches:
    - '*Alias*'
    - '*VRED*'
    name: Automotive Tools
  location: '@engines.tk-desktop.location'
  run_at_startup:
  - {app_instance: '', name: Apps}
  - {app_instance: tk-multi-shotgunpanel, name: Shotgun}
frameworks:
  tk-framework-editorial_v2.x.x:
    location:
      type: app_store
      name: tk-framework-editorial
      version: v2.4.0
