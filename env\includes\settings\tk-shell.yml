# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

################################################################################

includes:
- ../app_locations.yml
- ../engine_locations.yml
- ./tk-multi-launchapp.yml
- ./tk-multi-publish2.yml
- ./tk-multi-screeningroom.yml

################################################################################

# asset
settings.tk-shell.asset:
  apps:
    tk-multi-launchapp: '@settings.tk-multi-launchapp'
    tk-multi-launchmari: '@settings.tk-multi-launchapp.mari'
    tk-multi-launchmotionbuilder: '@settings.tk-multi-launchapp.motionbuilder'
    tk-multi-publish2: "@settings.tk-multi-publish2.standalone"
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
  location: '@engines.tk-shell.location'

# asset_step
settings.tk-shell.asset_step:
  apps:
    tk-multi-launchapp: '@settings.tk-multi-launchapp'
    tk-multi-launchmari: '@settings.tk-multi-launchapp.mari'
    tk-multi-launchmotionbuilder: '@settings.tk-multi-launchapp.motionbuilder'
    tk-multi-publish2: "@settings.tk-multi-publish2.standalone"
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
  location: '@engines.tk-shell.location'

# asset_step_notask
settings.tk-shell.asset_step_notask:
  apps:
    tk-multi-launchapp: '@settings.tk-multi-launchapp'
    tk-multi-launchmari: '@settings.tk-multi-launchapp.mari'
    tk-multi-launchmotionbuilder: '@settings.tk-multi-launchapp.motionbuilder'
    tk-multi-publish2: "@settings.tk-multi-publish2.standalone"
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
  location: '@engines.tk-shell.location'

# project
settings.tk-shell.project:
  apps:
    tk-multi-demo:
      location: "@apps.tk-multi-demo.location"
    tk-multi-launchapp: '@settings.tk-multi-launchapp'
    tk-multi-launchhiero: '@settings.tk-multi-launchapp.hiero'
    tk-multi-launchmari: '@settings.tk-multi-launchapp.mari'
    tk-multi-launchmotionbuilder: '@settings.tk-multi-launchapp.motionbuilder'
    tk-multi-publish2: "@settings.tk-multi-publish2.standalone"
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
  location: '@engines.tk-shell.location'


# episode
settings.tk-shell.episode:
  apps:
    tk-multi-launchapp: '@settings.tk-multi-launchapp'
    tk-multi-launchmotionbuilder: '@settings.tk-multi-launchapp.motionbuilder'
    tk-multi-publish2: "@settings.tk-multi-publish2.standalone"
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
  location: '@engines.tk-shell.location'

# episode_step
settings.tk-shell.episode_step:
  apps:
    tk-multi-launchapp: '@settings.tk-multi-launchapp'
    tk-multi-launchmotionbuilder: '@settings.tk-multi-launchapp.motionbuilder'
    tk-multi-publish2: "@settings.tk-multi-publish2.standalone"
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
  location: '@engines.tk-shell.location'

# sequence
settings.tk-shell.sequence:
  apps:
    tk-multi-launchapp: '@settings.tk-multi-launchapp'
    tk-multi-launchmotionbuilder: '@settings.tk-multi-launchapp.motionbuilder'
    tk-multi-publish2: "@settings.tk-multi-publish2.standalone"
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
  location: '@engines.tk-shell.location'

# sequence_step
settings.tk-shell.sequence_step:
  apps:
    tk-multi-launchapp: '@settings.tk-multi-launchapp'
    tk-multi-launchmotionbuilder: '@settings.tk-multi-launchapp.motionbuilder'
    tk-multi-publish2: "@settings.tk-multi-publish2.standalone"
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
  location: '@engines.tk-shell.location'


# shot
settings.tk-shell.shot:
  apps:
    tk-multi-launchapp: '@settings.tk-multi-launchapp'
    tk-multi-launchmotionbuilder: '@settings.tk-multi-launchapp.motionbuilder'
    tk-multi-publish2: "@settings.tk-multi-publish2.standalone"
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
  location: '@engines.tk-shell.location'

# shot_step
settings.tk-shell.shot_step:
  apps:
    tk-multi-launchapp: '@settings.tk-multi-launchapp'
    tk-multi-launchmotionbuilder: '@settings.tk-multi-launchapp.motionbuilder'
    tk-multi-publish2: "@settings.tk-multi-publish2.standalone"
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
  location: '@engines.tk-shell.location'
