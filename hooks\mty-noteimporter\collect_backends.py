################################################################################
#
# Copyright (c) 2024 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
################################################################################

import sgtk

from pprint import pformat

HookBaseClass = sgtk.get_hook_baseclass()


class NoteImporterResolveBackends(HookBaseClass):

    def collect(self):
        """
        A hook to define the required frameworks for the backend functionality

        This backends are just a generic classes with commond methods that 
        perform different depending on the project requierements

        The backends can be defined directly in the hook or can also be loaded 
        from other frameworks, which can works as follows:

        ```python
        sample = self.load_framework("mty-framework-sampletransferbackend")
        backends = {
            'sample': sample.sampleCore,
        }
        ```

        The current minimum requierement is to provide a `credentials` framework
        which should be an object with the following methods:

        - get_credential(location_dict, credential_type)
            a method to the apropiate credential data for matching location and type

        """

        credentials = self.load_framework("mty-framework-credentials")

        backends = {
            "credentials": credentials.credentialsManager
        }

        return backends
