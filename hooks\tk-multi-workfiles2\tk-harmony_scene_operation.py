# Copyright (c) 2013 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

import os
import shutil
import time
import subprocess

import sgtk
from sgtk.platform.qt import QtGui
from sgtk.errors import TankError

HookClass = sgtk.get_hook_baseclass()


class SceneOperation(HookClass):
    """
    Hook called to perform an operation with the
    current scene
    """

    pre_save_context = None

    def execute(
        self, operation, file_path, context, parent_action, file_version, read_only, **kwargs
    ):
        """
        Main hook entry point
        :param operation:       String
                                Scene operation to perform
        :param file_path:       String
                                File path to use if the operation
                                requires it (e.g. open)
        :param context:         Context
                                The context the file operation is being
                                performed in.
        :param parent_action:   This is the action that this scene operation is
                                being executed for.  This can be one of:
                                - open_file
                                - new_file
                                - save_file_as
                                - version_up
        :param file_version:    The version/revision of the file to be opened.  If this is 'None'
                                then the latest version should be opened.
        :param read_only:       Specifies if the file should be opened read-only or not
        :returns:               Depends on operation:
                                'current_path' - Return the current scene
                                                 file path as a String
                                'reset'        - True if scene was reset to an empty
                                                 state, otherwise False
                                all others     - None
        """

        app     = self.parent
        engine  = sgtk.platform.current_engine()
        tk      = engine.sgtk
        dcc_app = engine.app

        app.logger.info("-" * 50)
        app.logger.info("operation: %s" % operation)
        app.logger.info("file_path: %s" % file_path)
        app.logger.info("context: %s" % context)
        app.logger.info("app context: %s" % app.context)
        app.logger.info("engine context: %s" % engine.context)
        app.logger.info("parent_action: %s" % parent_action)
        app.logger.info("SceneOperation.pre_save_context: %s" % SceneOperation.pre_save_context)
        app.logger.info("file_version: %s" % file_version)
        app.logger.info("read_only: %s" % read_only)
        app.logger.info("kwargs: %s" % kwargs)
        app.logger.info("-" * 50)

        prev_save_context = context

        if operation == "current_path":
            # Note this is a little bit of a trick for when the artist saves
            # into a different context,
            # Hamorny WIP file versions are all kept in the same
            # folder, so when a context is changed, we need to
            # actually copy the whole project to a different location.
            # This is a way to know the context has changed, as the action
            # at the time "save_as" operation is called does not provide
            # this information
            if parent_action == "save_file_as":
                SceneOperation.pre_save_context = context
            return dcc_app.get_current_project_path()

        elif operation == "open":
            app.logger.info("harmony is ready before open_project: {}".format(self.harmony_is_ready(engine)))
            dcc_app.open_project(file_path)
            # wait the new scene
            self.is_dirty(engine)
            app.logger.info("harmony is ready after open_project: {}".format(self.harmony_is_ready(engine)))
            self.create_pipeline_library(app, engine, context)
            #self.set_env_settings(app, engine, context)
            return True

        elif operation == "save":
            dcc_app.save_project()

        elif operation == "save_as":
            app.logger.info("saving as " + file_path)
            if context == SceneOperation.pre_save_context:
                _, file_path_filename = os.path.split(file_path)
                version_name, _ = os.path.splitext(file_path_filename)
                dcc_app.save_new_version(version_name)
            else:
                # need to copy the project into a different location
                app.logger.info("saving as in a different context!")
                source_file = dcc_app.get_current_project_path()
                app.logger.info("source_file: %s" % source_file)
                app.logger.info("target_file: %s" % file_path)
                dcc_app.save_project_as(file_path, source_file=source_file, open_project=True)

        elif operation == "reset":
            if parent_action not in ("new_file", "open_file"):
                dcc_app.close_project()
            return True

        elif operation == "prepare_new":
            app.logger.info("Creating new file for context: {}".format(context))
            app.logger.info("harmony is ready before creat new file: {}".format(self.harmony_is_ready(engine)))
            new_file_path = dcc_app.new_file(app, context)
            app.logger.info("Created new file: {}".format(new_file_path))
            app.logger.info("harmony is ready after creat new file: {}".format(self.harmony_is_ready(engine)))
            # wait the new scene
            self.is_dirty(engine)
            self.set_env_settings(app, engine, context)
            dcc_app.save_project()
            app.logger.info("harmony is ready before pipeline library: {}".format(self.harmony_is_ready(engine)))
            self.create_pipeline_library(app, engine, context)
            return True

    def harmony_is_ready(self, engine):
        time_out = 20
        current_try = 0
        result = False

        while current_try < time_out:
            result = engine.app.custom_script(
            """
            include("harmony_utility_functions.js")
            harmony_is_ready()"""
            )
            self.parent.logger.info("harmony_is_ready result: {}".format(result))
            if result == True:
                break
            else:
                time.sleep(1)
                current_try += 1

        return result

    def is_dirty(self, engine):
        # if the scene is not dirty, don have issue to change
            trys = 0
            while trys < 20:
                time.sleep(3)
                old_scene_is_open = engine.app.custom_script(
            """
            include("harmony_utility_functions.js")
            scene_is_dirty()"""
            )
                engine.logger.info(
                    "Scene is dirty {} try :{}".format(old_scene_is_open,trys))
                if old_scene_is_open == False:
                    break
                trys += 1
                if trys > 20:
                    break
            ###

    def set_env_settings(self, app, engine, context):
        app_scenesetup    = engine.apps.get("mty-multi-scenesetup")
        module_scenesetup = app_scenesetup.import_module("scene_setup")
        manager           = module_scenesetup.get_manager(app_scenesetup)

        if context.entity['type'] == 'Shot':
            manager.setup_shot_settings(start_at=1, context=context, ui_d=False)

        elif context.entity['type'] == 'Asset':
            manager.setup_asset_settings(context=context, ui_d=False)

    def create_pipeline_library(self, app, engine, context):

        if not context.entity:
            app.logger.warning(
                "Couldn't get engine.context.entity for the pipeline library creation"
            )
            return

        app_harmonylib = engine.apps.get("mty-harmony-library")
        if not app_harmonylib:
            app.logger.warning(
                "Couldn't get mty-harmony-library from engine.apps"
            )
            return

        entity_type = context.entity["type"].lower()

        app.logger.info("Creating pipeline library: {}".format(entity_type))

        if entity_type == "asset":
            app_harmonylib.ensure_library_folder(context)
