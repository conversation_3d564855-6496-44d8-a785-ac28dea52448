#####################################################################################
#
# Copyright (c) 2021 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

"""
Hook for ...
"""

from tank import Hook
from tank import TankError

import os
import sys
import time
import inspect

class ExecuteActionHook(Hook):
    
    
    def execute(self, entity_type, entities, other_params, **kwargs):
        """
        Executes an action for the selected entities.
        
        :param entity_type: ...
        :param entities: ...
        :param other_params: ...
        :returns: dictionary with three values: succes, errors, messages
        """

        self.parent.log_debug("Executing hook...")

        # Queue Manager
        self.parent.log_debug("injecting queue manager in pythonpath")
        if "mty-multi-queue" in self.parent.engine.apps:
            app = self.parent.engine.apps["mty-multi-queue"]
            queueclient_path = os.path.join(app.disk_location, 'python')
            self.parent.log_debug("queueclient_path: %s" % queueclient_path)
            sys.path.append(queueclient_path)
        else:
            self.parent.log_error('Queue Manager app is not present!')

        from tk_multi_queueclient.queue_client import Client
        

        succes = []
        for entity in entities:
            commands = inspect.cleandoc("""
            execute_hook_expression(
                "{config}/ensure_publish_is_local.py",
                "execute", set_progress=set_progress,
                publish_id=%s
            )
            """ % entity['id']
            )

            job_data = {
                'name': 'Sync %s' % entity['code'],
                'commands': commands,
                'priority': 50,
                'type': 'Download',
                'color': [255, 0, 255]
            }

            client = Client()
            client.submit_job(job_data)

            succes.append(entity)

            time.sleep(1)

        result = {
            'succes': succes,
            'errors': [],
            'messages': [
                'Publish Sync jobs where sent to Queue!'
            ]
        }

        return result