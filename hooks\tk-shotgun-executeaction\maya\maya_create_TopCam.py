#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

"""
An app that creates an ortho top cam for the sequence topview media reviews
"""

from tank import Hook
import sgtk

import pprint
import traceback

import maya.cmds as cmds


# --------------------------------------------------------------------------------------

pp = pprint.pprint
pf = pprint.pformat


class ProcessItemsHook(Hook):

    # def execute(self, entity_type, entities, other_params, **kwargs):
    def execute(self, **kwargs):
        try:
            self.create_TopCam()
            msg = "TopCam created successfully."
            cmds.confirmDialog(
                title="Success",
                message=msg,
                button=["Close"],
                defaultButton="Close",
                cancelButton="No",
                dismissString="Success",
            )
            return {"succes": [1], "messages": [msg], "errors": []}
        except Exception as e:
            msg = "Create TopCam failed:\n{}\nFull traceback:\n{}".format(
                e, traceback.format_exc()
            )
            cmds.confirmDialog(
                title="Error",
                message=msg,
                button=["Close"],
                defaultButton="Close",
                cancelButton="No",
                dismissString="Error",
            )
            return {"succes": [], "messages": [msg], "errors": [1]}

    # ----------------------------------------------------------------------------------

    def create_TopCam(self):
        self.parent.engine.logger.info("\n\n")
        self.parent.engine.logger.info("-" * 80)

        engine = sgtk.platform.current_engine()
        original_selection = cmds.ls(selection=True, long=True)
        seq_name = self.get_sequence_name(engine)
        locators = self.get_global_scale_locators()

        if len(locators) != 0:
            top_cam = self.create_topCam(seq_name)
            animate_cam_dialog = self.ask_to_animate_the_camera()

            if animate_cam_dialog == "Yes":
                self.parent.engine.logger.info("\ntopCam will follow these locators:\n")
                self.parent.engine.logger.info(
                    "Found locators:\n{}".format(pf(locators))
                )

                frames_list = self.get_sequences_frames_list(engine)
                self.animate_cam_position_by_bounding_box(
                    locators, frames_list, top_cam
                )
            else:
                self.parent.engine.logger.info("\ntopCam will frame these locators:\n")
                self.parent.engine.logger.info(
                    "Found locators:\n{}".format(pf(locators))
                )
                self.set_cam_position_by_bounding_box(locators, top_cam)

            cmds.select(original_selection)
        else:
            top_cam = self.create_topCam(seq_name)
            cmds.select(original_selection)

    def context_latest_cut(self, engine):
        result = None

        entity = "Cut"
        filters = [["entity", "is", engine.context.entity]]
        fields = ["revision_number"]
        order = [{"field_name": "revision_number", "direction": "desc"}]

        # result = tk.shotgun.find_one(
        result = engine.shotgun.find_one(entity, filters, fields, order)
        return result

    def context_latest_cutItems(self, engine):
        result = {}

        latest_cut = self.context_latest_cut(engine)

        entity = "CutItem"
        filter = [["cut", "is", latest_cut]]

        fields = ["cut_item_in", "cut_item_out", "code", "cut_item_duration"]

        list_of_cutItems = engine.shotgun.find(entity, filter, fields)

        # self.parent.engine.logger.info(
        #     "list_of_cutItems:\n{}".format(list_of_cutItems)
        # )

        result["start_frame"] = list_of_cutItems[0].get("cut_item_in")
        result["end_frame"] = list_of_cutItems[-1].get("cut_item_out")

        return result

    def get_sequence_name(self, engine):
        result = None

        entity = engine.context.entity
        entity_type = entity.get("type", {})

        if entity_type == "Sequence":
            result = entity.get("name", {})

        return result

    def get_global_scale_locators(self):
        locators = []

        dialog = cmds.confirmDialog(
            title="",
            message="Do you want to use " "the current selection?",
            button=["Yes", "No", "Cancel"],
            defaultButton="Yes",
            cancelButton="Cancel",
            dismissString="Cancel",
        )

        # self.parent.engine.logger.info("dialog: {}".format(dialog))

        if dialog == "No":
            locators.extend(cmds.ls("*:LOC_Global_Scale", long=True))
            locators.extend(cmds.ls("*:Global_Scale_LOC", long=True))
        elif dialog == "Yes":
            for obj in cmds.ls(selection=True, long=True):
                # self.parent.engine.logger.info("obj: {}".format(obj))
                obj_descendants = cmds.listRelatives(
                    obj, allDescendents=True, fullPath=True, type="transform"
                )
                # self.parent.engine.logger.info(
                #     "obj_descendants:\n{}".format(pf(obj_descendants))
                # )
                for loc in obj_descendants:
                    if "LOC_Global_Scale" in loc or "Global_Scale_LOC" in loc:
                        # self.parent.engine.logger.info("loc: {}".format(loc))
                        locators.append(loc)

        # self.parent.engine.logger.info("locators:\n{}".format(pf(locators)))
        return locators

    def create_topCam(self, seq_name):

        cam_name = "{}_TopCam".format(seq_name)
        top_cam = cmds.camera(
            aspectRatio=1.778,
            displayFilmGate=True,
            displayGateMask=True,
            displayResolution=True,
            farClipPlane=300,
            nearClipPlane=120,
            # name=cam_name,
            orthographic=True,
            position=(0, 200, 0),
            rotation=(-90, 0, 0),
            orthographicWidth=200,
        )

        cam_shape = cmds.listRelatives(top_cam, type="shape", fullPath=True)

        cmds.addAttr(cam_shape[0], longName="assetType", dataType="string")
        cmds.setAttr(
            "{}.{}".format(cam_shape[0], "assetType"),
            "TopCam",
            type="string",
            keyable=False,
            channelBox=True,
            lock=True,
        )
        cmds.setAttr(
            "{}.{}".format(cam_shape[0], "orthographicWidth"),
            200,
            keyable=True,
            channelBox=True,
            lock=False,
        )
        cmds.setAttr(
            "{}.{}".format(cam_shape[0], "nearClipPlane"),
            120,
            keyable=True,
            channelBox=True,
            lock=False,
        )
        cmds.setAttr(
            "{}.{}".format(cam_shape[0], "farClipPlane"),
            300,
            keyable=True,
            channelBox=True,
            lock=False,
        )

        top_cam_name = cmds.rename(top_cam[0], cam_name)
        top_cam = cmds.ls(top_cam_name, long=True)[0]

        return top_cam

    def ask_to_animate_the_camera(self):
        animate_cam_dialog = cmds.confirmDialog(
            title="",
            message="Do you want to animate " "the newly created TopCam?",
            button=["Yes", "No"],
            defaultButton="Yes",
            cancelButton="No",
            dismissString="No",
        )

        return animate_cam_dialog

    def get_sequences_frames_list(self, engine):
        """Gets a list of frames every 25th based on the seq start and end frames.
        End frame will always be added to the end of the list regardless of the
        at what frame the previous key was set"""

        frames_list = []

        range_dict = self.context_latest_cutItems(engine)
        # start_frame = cmds.playbackOptions(query=True, animationStartTime=True)
        # end_frame = cmds.playbackOptions(query=True, animationEndTime=True)
        start_frame = range_dict.get("start_frame")
        end_frame = range_dict.get("end_frame")

        # self.parent.engine.logger.info(
        #     "start: {}, end: {}".format(start_frame, end_frame)
        # )

        frames_list.extend([i for i in range(int(start_frame), int(end_frame), 25)])
        frames_list.append(end_frame)

        return frames_list

    def set_cam_position_by_bounding_box(self, locators, camera):
        """Set position of the camera based on the bounding
        box position of an objects list (locators)"""

        y_value = 200.0

        x_bbox_list = []
        z_bbox_list = []

        for loc in locators:
            obj_translation = cmds.xform(
                loc, query=True, translation=True, worldSpace=True
            )
            x_bbox_list.append(obj_translation[0])
            z_bbox_list.append(obj_translation[2])

        x_value = sum(x_bbox_list) / len(x_bbox_list)
        z_value = sum(z_bbox_list) / len(z_bbox_list)

        cmds.setAttr(
            "{}.translateX".format(camera),
            x_value,
        )
        cmds.setAttr(
            "{}.translateY".format(camera),
            y_value,
        )
        cmds.setAttr(
            "{}.translateZ".format(camera),
            z_value,
        )

    def animate_cam_position_by_bounding_box(self, locators, frames_list, camera):
        """Animates position of the camera in a range of frames based on the
        bounding box position of an objects list (locators)"""

        original_current_frame = cmds.currentTime(query=True)
        y_value = 200.0

        for frame in frames_list:
            x_bbox_list = []
            z_bbox_list = []
            cmds.currentTime(frame)

            for loc in locators:
                obj_translation = cmds.xform(
                    loc, query=True, translation=True, worldSpace=True
                )
                x_bbox_list.append(obj_translation[0])
                z_bbox_list.append(obj_translation[2])

            x_value = sum(x_bbox_list) / len(x_bbox_list)
            z_value = sum(z_bbox_list) / len(z_bbox_list)

            cmds.setKeyframe(camera, value=x_value, attribute="translateX", time=frame)
            cmds.setKeyframe(camera, value=y_value, attribute="translateY", time=frame)
            cmds.setKeyframe(camera, value=z_value, attribute="translateZ", time=frame)

        cmds.currentTime(original_current_frame)
