import os
import pprint

import sgtk

HookBaseClass = sgtk.get_hook_baseclass()

class PublishVideoPlugin(HookBaseClass):
    '''
    Plugin for creating mov versions in shotgun
        Accepts only 'mov' extension
        Relies on other plugins to create sg_publish_data
        Submits version
    '''

    def accept(self, settings, item):
        self.parent.engine.logger.info('{} Publish Video'.format('- ' * 10))

        path = item.get_property('path')
        if path is None:
            raise AttributeError('Publish item has no property \'path\'') 
        
        if not path.lower().endswith('.mov'):
            # log the rejected file
            self.parent.engine.logger.info(
                'Rejected: publish_video plugin did not accept {}'.format(os.path.basename(path))
            )
            return {'accepted': False}

        # log the accepted file
        self.parent.engine.logger.info(
                'Accepted: publish_video plugin accepted {}'.format(os.path.basename(path))
            )
        # return the accepted info
        return {'accepted': True}
    
    def publish(self, settings, item):

        self.parent.engine.logger.info(" ///*** Publishing...publish_video ***///")

        # execute publish file first, we need sg_publish_data
        super(PublishVideoPlugin, self).publish(settings, item)

        # create version for video file linked to publish in Shotgun
        self.submit_version(item)
        
    
    def submit_version(self, item):

        engine = sgtk.platform.current_engine()

        sg_publish = item.properties.sg_publish_data
        name = os.path.splitext(sg_publish['code'])[0]
        current_user = self.parent.engine.context.user
        movie_path = item.properties['path']

        dict_upload_version = {
            'project': sg_publish['project'],
            'code': name,
            # "created_by": current_user,
            'description': 'Uploaded from Publisher',
            'sg_status_list': 'rev',
            'entity': sg_publish['entity'],
            'sg_task': sg_publish['task'],
            # 'sg_version_type': 'Production',
            'user': current_user,
            'sg_path_to_movie': movie_path,
        }

        # create the version entity
        create_version = engine.shotgun.create('Version', dict_upload_version)
        self.parent.engine.logger.info('Version created successfully')

        # upload the version mov
        engine.shotgun.upload(
            'Version', create_version['id'], movie_path, 'sg_uploaded_movie'
        )
        self.parent.engine.logger.info('Version uploaded successfully')
        # link published file with version
        update_filed_dict = {
            'published_files': [sg_publish],
            "sg_version_type": "Production",
        }
        engine.shotgun.update('Version', create_version['id'], update_filed_dict)