from tank import Hook
from tank.platform.qt import QtCore, QtGui

QDialog = QtGui.QDialog
QVBoxLayout = QtGui.QVBoxLayout
QListWidget = QtGui.QListWidget
QPushButton = QtGui.QPushButton
QLabel = QtGui.QLabel
QComboBox = QtGui.QComboBox
QFrame = QtGui.QFrame
QMessageBox = QtGui.QMessageBox
QHBoxLayout = QtGui.QHBoxLayout
QDoubleSpinBox = QtGui.QDoubleSpinBox

import pprint

pf = pprint.pformat


class ProcessItemsHook(Hook):
    def execute(self, **kwargs):
        self.parent.engine.logger.info(
            "Execute action reorder_pegs_in_z start.".ljust(120, "-")
        )
        result = {"succes": [1], "messages": [], "errors": []}

        # define the function that will be called from Qt
        def get_valid_selected_nodes():
            cmd = """
include("harmony_utility_functions.js");

function get_valid_selected_nodes() {
    var filtered_valid_selected_nodes = [];
    var valid_node_types = ["READ", "GROUP"];

    var selected_nodes = get_selected_nodes();

    for (var i = 0; i < selected_nodes.length; i++) {
        var current_node = selected_nodes[i];
        var current_node_type = node.type(current_node);
        if (valid_node_types.indexOf(current_node_type) !== -1) {
            filtered_valid_selected_nodes.push(current_node);
        }
    }
    return filtered_valid_selected_nodes;
}

get_valid_selected_nodes();
"""

            # execute the script in the engine
            valid_selected_nodes = self.parent.engine.app.execute(cmd)

            return valid_selected_nodes

        def reorder_selected_nodes(nodes_list, z_direction, offset_amount):
            # offset_amount = 0.0001

            reorder_cmd = """
include("harmony_utility_functions.js");

var OFFSET_ATTRIBUTE_NAME = "mtyZOffsetPeg";
var Z_OFFSET_AMOUNT = %s;

function get_valid_selected_nodes() {
    var filtered_valid_selected_nodes = [];
    var valid_node_types = ["READ", "GROUP"];

    var selected_nodes = get_selected_nodes();

    for (var i = 0; i < selected_nodes.length; i++) {
        var current_node = selected_nodes[i];
        var current_node_type = node.type(current_node);
        if (valid_node_types.indexOf(current_node_type) !== -1) {
            filtered_valid_selected_nodes.push(current_node);
        }
    }
    return filtered_valid_selected_nodes;
}

function get_waypoint(current_node) {
    var source_waypoint = waypoint.parentWaypoint(current_node, 0);
    if (source_waypoint !== "") {
        return source_waypoint;
    } else {
        var source_node = node.srcNode(current_node, 0);
        if (node.getName(source_node) !== "") {
            return create_parent_waypoint(current_node);
        } else {
            return null;
        }
    }
}

function create_parent_waypoint(current_node) {
    var list_of_destination_nodes = [];
    var source_node = node.srcNode(current_node, 0);
    var source_node_type = node.type(source_node);
    if (source_node_type === "MULTIPORT_IN") {
        return null;
    }
    var out_port_num = node.numberOfOutputPorts(source_node);
    for(var i = 0; i<out_port_num; i++) {
      var portIdx = i;
      var numLinks = node.numberOfOutputLinks(source_node, portIdx);
      for(var j = 0; j < numLinks; j++) {
        var linkIdx = j;
        log("----------------------------------------------");
        log("source_node:" + source_node);
        log("portIdx: " + portIdx);
        log("linkIdx: " + linkIdx);
        var dest_node = node.dstNode(source_node, portIdx, linkIdx)
        log("dstNode: " + dest_node);
        list_of_destination_nodes.push(dest_node);
      }
    }

    // create waypoint below the source node
    var parent_node = node.parentNode(current_node);
    var source_x_pos = node.coordX(source_node);
    var source_y_pos = node.coordY(source_node);
    var waypoint_name = node.getName(source_node) + "-Waypoint";
    var new_waypoint = waypoint.add(parent_node, waypoint_name, source_x_pos, source_y_pos + 50);
    var new_waypoint_path = parent_node + "/" + waypoint_name;
    waypoint.linkOutportToWaypoint(source_node, 0, new_waypoint_path)

    // now we need to reconnect every previously disconnected destination node
    for (var i = 0; i < list_of_destination_nodes.length; i++) {
        var dest_current_node = list_of_destination_nodes[i];
        log("==========================================");
        // disconnect all destination nodes
        log("Disconnecting node: " + dest_current_node);
        node.unlink(dest_current_node, 0);
        log("current_node: " + current_node);
        log("source_node: " + source_node);
        log("dest_current_node: " + dest_current_node);
        log("new_waypoint: " + new_waypoint);
        log("new_waypoint_path: " + new_waypoint_path);
        log("==========================================");
        waypoint.linkWaypointToInport(new_waypoint_path, dest_current_node, 0, false);
        // break;
    }
    return new_waypoint_path;
}

function create_base_dict_from_nodes_array(nodes_array) {
    var nodes_dict = {};

    log("============================================");
    log("Generating base dictionary from nodes array");

    for (var i = 0; i < nodes_array.length; i++) {
        log("--------------------------------------------");
        var current_node = nodes_array[i];
        var current_node_name = node.getName(current_node);
        log("current_node_name: " + current_node_name);
        // Define peg name in case is needed for a new PEG node
        var peg_node_name = current_node_name + "-Z_Offset_PEG";
        if (!nodes_dict[current_node_name]) {
            nodes_dict[current_node_name] = {};
        }
        nodes_dict[current_node_name]["node_path"] = current_node;
        var source_node = node.srcNode(current_node, 0);
        var source_node_type = node.type(source_node);
        var parent_node = node.parentNode(current_node);
        var parent_waypoint = get_waypoint(current_node);

        // regardless of the source_node type, we will alway create a new PEG node for
        // the Z offset, unless we can find a pipeline offset peg, which can be
        // identified by the boolean attr 'mtyZOffsetPeg' set to true. In that case, we
        // use this same node for the offset.
        if (node.getName(parent_node) !== "") {
            var filtered_source_node = filter_nodes_by_attribute([source_node], OFFSET_ATTRIBUTE_NAME);
            if (filtered_source_node.length > 0) {
                var source_node_type = node.type(source_node);
                nodes_dict[current_node_name]["offset_peg_node_path"] = source_node;
            } else {
                nodes_dict[current_node_name]["offset_peg_node_path"] = null;
            }
        }

        var source_node_name = node.getName(source_node);
        if (source_node_name !== "") {
            nodes_dict[current_node_name]["source_node"] = source_node;
        } else {
            nodes_dict[current_node_name]["source_node"] = null;
        }

        if (source_node_type !== "") {
            nodes_dict[current_node_name]["source_node_type"] = source_node_type;
        } else {
            nodes_dict[current_node_name]["source_node_type"] = null;
        }

        nodes_dict[current_node_name]["source_node_path"] = source_node;
        nodes_dict[current_node_name]["parent_node"] = node.getName(parent_node);
        if (parent_waypoint === null) {
            nodes_dict[current_node_name]["parent_node_path"] = null;
            nodes_dict[current_node_name]["parent_waypoint"] = null;
        } else {
            nodes_dict[current_node_name]["parent_node_path"] = parent_node;
            nodes_dict[current_node_name]["parent_waypoint"] = parent_waypoint;
        }
    }
    return nodes_dict;
}

function create_peg_node(current_node, node_name, xOffset, yOffset) {
    var new_peg_node_color = new ColorRGBA(115, 153, 195, 255);
    var current_node_parent = node.parentNode(current_node);

    // if (node_type == "PEG") {
    try {
        var current_node_Xpos = node.coordX(current_node);
        var current_node_Ypos = node.coordY(current_node);
        var current_node_Zpos = node.coordZ(current_node);
        var new_node = node.add(
            current_node_parent,
            node_name,
            "PEG",
            current_node_Xpos - xOffset,
            current_node_Ypos - yOffset,
            current_node_Zpos
        );
    } catch (error) {
        log("Couldn't create node:\\n" + error);
        return null;
    }
    // };

    node.setColor(new_node, new_peg_node_color);

    log("Created node: " + node.getName(new_node));
    return new_node;
}

function create_new_peg_nodes(nodes_dict) {
    var new_peg_node_color = new ColorRGBA(115, 153, 195, 255);
    var xOffset = 0;
    var yOffset = 15;

    // we should have all the necessary info in the nodes dict, so we start by iterating
    // through the nodes dict

    log("============================================");
    log("Creating new PEG nodes");

    for (var current_node_name in nodes_dict) {
        var current_node = nodes_dict[current_node_name]["node_path"];
        var source_node = nodes_dict[current_node_name]["source_node_path"];
        var source_node_type = nodes_dict[current_node_name]["source_node_type"];
        var parent_node_path = nodes_dict[current_node_name]["parent_node_path"];
        var parent_waypoint = nodes_dict[current_node_name]["parent_waypoint"];

        var current_node_x_pos = node.coordX(current_node);
        var current_node_y_pos = node.coordY(current_node);
        var peg_node_name = current_node_name + "-Z_Offset_PEG";

        /*
        log("--------------------------------------------");
        log("current_node_name: " + current_node_name);
        log("current_node: " + current_node);
        log("source_node: " + source_node);
        log("source_node_type: " + source_node_type);
        log("parent_node_path: " + parent_node_path);
        log("parent_waypoint: " + parent_waypoint);
        log("current_node_x_pos: " + current_node_x_pos);
        log("current_node_y_pos: " + current_node_y_pos);
        log("peg_node_name: " + peg_node_name);
        */

        // create the peg node itself
        var offset_peg_node = create_peg_node(current_node, peg_node_name, xOffset, yOffset);
        // add custom bool attr to the new peg node
        create_attr(offset_peg_node, OFFSET_ATTRIBUTE_NAME, "BOOL");

        nodes_dict[current_node_name]["offset_peg_node_path"] = offset_peg_node;

        // first of all, we handle nodes that are not connected to anything (in its
        // input ports)
        if (source_node === null || source_node === "") {
            log("node is not connected to anything");
            node.link(offset_peg_node, 0, current_node, 0, false, false);
            continue;
        }

        // handle any other whch is not connected to a MULTIPORT node
        if (source_node_type !== "MULTIPORT_IN") {
            // handle nodes that are not connected to a WAYPOINT node
            if (parent_waypoint !== null) {
                log("node is connected to a waypoint");
                // first we disconnect the current node from its input connection
                node.unlink(current_node, 0);
                // then we connect the offset peg node to the current node
                node.link(offset_peg_node, 0, current_node, 0, false, false);
                // finally connect waypoint to the newly created offset peg node
                try {
                    waypoint.linkWaypointToInport(parent_waypoint, offset_peg_node, 0, false);
                } catch (error) {
                    log("Couldn't link waypoint to inport:\\n" + error);
                }
            } else {
                log("node is not connected to a waypoint");
                // node is not connected to a waypoint, but it has a source node
                // first, we connect the source node to the newly created peg node
                node.link(source_node, 0, offset_peg_node, 0, false, false);
                // then we connect the offset peg node to the current node
                node.link(offset_peg_node, 0, current_node, 0, false, false);
            }
        } else {
            log("node is connected to a multiport node (inside a group)");
            // handle nodes that are connected to a MULTIPORT node
            // first we disconnect the current node from its input connection
            node.unlink(current_node, 0);
            // then we connect the offset peg node to the current node
            node.link(offset_peg_node, 0, current_node, 0, false, false);
            // finally we connect multiport in node to the offset peg node
            node.link(source_node, 0, offset_peg_node, 0, false, false);
        }
    }
    return nodes_dict;
}

function set_z_position(nodes_dict, z_direction) {
    var counter = 0;
    var failed_nodes = [];
    for (var current_node_name in nodes_dict) {
        var offset_peg_node = nodes_dict[current_node_name]["offset_peg_node_path"];
        var offset_peg_node_name = node.getName(offset_peg_node);

        if (z_direction === "plus") {
            var current_offset = Z_OFFSET_AMOUNT * counter;
        } else if (z_direction === "minus") {
            var current_offset = Z_OFFSET_AMOUNT * -counter;
        }

        try {
            // set z attribute in the offset peg
            var z_attribute = node.getAttr(offset_peg_node, 0, "POSITION.Z");
            z_attribute.setValue(current_offset);
        } catch (error) {
            log("Couldn't set z attribute for node " + offset_peg_node_name + ":\\n" + error);
            failed_nodes.push(current_node_name);
        }
        counter++;
    }
    return failed_nodes;
}


scene.beginUndoRedoAccum("Reorder nodes in Z");

var filtered_valid_nodes = %s;
var z_direction = "%s";
var nodes_dict = create_base_dict_from_nodes_array(filtered_valid_nodes);
nodes_dict = create_new_peg_nodes(nodes_dict);
var failed_nodes = set_z_position(nodes_dict, z_direction);

log("failed_nodes.length: " + failed_nodes.length);
log("nodes_dict.length: " + nodes_dict.length);

scene.endUndoRedoAccum();

// log(JSON.stringify(nodes_dict, null, 4));
// log("filtered_valid_nodes:\\n" + JSON.stringify(filtered_valid_nodes, null, 4));
// log("z_direction: " + z_direction);


""" % (offset_amount, nodes_list, z_direction)

            self.parent.engine.logger.debug("reorder_cmd:\n{}".format(reorder_cmd))
            # execute the command
            self.parent.engine.app.execute(reorder_cmd)


        valid_selected_nodes = get_valid_selected_nodes()
        self.parent.engine.logger.info(
            "valid_selected_nodes: \n{}".format(pf(valid_selected_nodes))
        )
        if not valid_selected_nodes:
            msg = (
                "Couldn't get valid selected nodes, please select the nodes again in "
                "the node view and try again.\n\n"
                "Hint: Valid selected nodes must be Read or Group nodes."
            )
            self.show_message("Couldn't get selected nodes", msg, icon="Warning")
            result = {"succes": [], "messages": [msg], "errors": [1]}
            return result

        dialog = ReorderDialog(valid_selected_nodes)
        dialog.exec_()
        self.parent.engine.logger.info(
            "dialog.result:\n{}".format(pf(dialog.result))
        )

        if not dialog.result:
            msg = "Couldn't get result from ReorderDialog. Please try again."
            self.show_message("Cancelled", msg, icon="Warning")
            result = {"succes": [], "messages": [msg], "errors": [1]}
            return result

        valid_nodes_list = dialog.result[0]
        reorder_direction = dialog.result[1]
        offset_amount = dialog.result[2]
        if reorder_direction == "Move nodes to the back":
            z_direction = "minus"
        elif reorder_direction == "Move nodes to the front":
            z_direction = "plus"
        else:
            z_direction = None

        self.parent.engine.logger.info(
            "valid_nodes_list:\n{}".format(pf(valid_nodes_list))
        )
        self.parent.engine.logger.info(
            "z_direction: {}".format(z_direction)
        )

        reorder_selected_nodes(valid_nodes_list, z_direction, offset_amount)

        return result

    def show_message(self, title, msg, icon=None):
        """
        Shows a message box with the input message
        """

        icons_dict = {
            "NoIcon": QMessageBox.NoIcon,
            "Question": QMessageBox.Question,
            "Information": QMessageBox.Information,
            "Warning": QMessageBox.Warning,
            "Critical": QMessageBox.Critical,
        }

        if not icon or icon not in icons_dict.keys():
            icon_selection = icons_dict["NoIcon"]
        else:
            icon_selection = icons_dict[icon]

        msg_error = QMessageBox()
        msg_error.setWindowTitle(title)
        msg_error.setText(msg)
        msg_error.setIcon(icon_selection)
        msg_error.raise_()
        msg_error.activateWindow()
        msg_error.exec_()


class ReorderDialog(QDialog):
    def __init__(self, items):
        super().__init__()

        self.items = items
        self.result = None  # Initialize result attribute

        self.setWindowTitle("Reorder nodes in Z")

        layout = QVBoxLayout()

        instruction_label = QLabel(
            "Please reorder the list of nodes as needed.\n\n"
            "The first node (node on top) will be used as a reference\n"
            "for the Z position of the rest of the nodes.\n"
        )
        layout.addWidget(instruction_label)

        # Add separator
        separator1 = QFrame()
        separator1.setFrameShape(QFrame.HLine)
        separator1.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator1)

        self.list_widget = QListWidget()
        self.list_widget.addItems(self.items)
        self.list_widget.setDragEnabled(True)  # Enable dragging
        self.list_widget.setDropIndicatorShown(True)  # Show drop indicator
        self.list_widget.setDragDropMode(QListWidget.InternalMove)  # Enable internal move
        layout.addWidget(self.list_widget)

        # Add separator
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.HLine)
        separator2.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator2)

        reorder_title_label = QLabel("Reorder direction")
        layout.addWidget(reorder_title_label)

        self.order_combo = QComboBox()
        self.order_combo.addItems(["Move nodes to the front", "Move nodes to the back"])
        layout.addWidget(self.order_combo)

        # Add separator
        separator3 = QFrame()
        separator3.setFrameShape(QFrame.HLine)
        separator3.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator3)

        # Add Z distance input field
        z_distance_layout = QHBoxLayout()
        z_distance_label = QLabel("Z distance")
        z_distance_layout.addWidget(z_distance_label)

        self.z_distance_input = QDoubleSpinBox()
        self.z_distance_input.setDecimals(4)
        self.z_distance_input.setSingleStep(0.0001)
        self.z_distance_input.setValue(0.0001)
        z_distance_layout.addWidget(self.z_distance_input)

        layout.addLayout(z_distance_layout)

        # Add separator
        separator4 = QFrame()
        separator4.setFrameShape(QFrame.HLine)
        separator4.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator4)

        button_layout = QVBoxLayout()
        self.btn_accept = QPushButton("Accept")
        self.btn_accept.clicked.connect(self.accept_changes)
        button_layout.addWidget(self.btn_accept)

        self.btn_cancel = QPushButton("Cancel")
        self.btn_cancel.clicked.connect(self.reject)
        button_layout.addWidget(self.btn_cancel)

        layout.addLayout(button_layout)

        self.setLayout(layout)

    def closeEvent(self, event):
        if self.result is None:  # Check if result is not set (dialog was closed without accepting or rejecting)
            self.reject()

    def accept_changes(self):
        # Get the ordered items from the list widget
        ordered_items = [self.list_widget.item(i).text() for i in range(self.list_widget.count())]
        order = self.order_combo.currentText()
        z_distance = self.z_distance_input.value()
        self.result = ordered_items, order, z_distance
        self.accept()  # Close the dialog after accepting changes

