# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

import os
import sys
import sgtk
import maya.cmds as cmds
import maya.mel as mel
import pymel.core as pm
import json

import re
import traceback
import subprocess
from sgtk.platform.qt import QtGui, QtCore
from pprint import pprint as pprint
from pprint import pformat as pformat

HookBaseClass = sgtk.get_hook_baseclass()


class MayaAssetAlembicDynamicAttributesPublishPlugin(HookBaseClass):
    """
    Plugin for publish Scene playblast Publish

    """

    @property
    def icon(self):
        """
        Path to an png icon on disk
        """

        # look for icon one level up from this hook's folder in "icons" folder
        return os.path.join(
            self.disk_location,
            os.pardir,
            "icons",
            "publish_alembic.png"
        )

    @property
    def name(self):
        """
        One line display name describing the plugin
        """
        return "Publish Dyn-Atts alembic"

    @property
    def description(self):
        """
        Verbose, multi-line description of what the plugin does. This can
        contain simple html for formatting.
        """

        loader_url = "https://support.shotgunsoftware.com/hc/en-us/articles/219033078"

        return """
        <p>This plugin publish an alembic with dynamic attributes baked on every frame.</p>

        """

    @property
    def settings(self):
        """
        Dictionary defining the settings that this plugin expects to recieve
        through the settings parameter in the accept, validate, publish and
        finalize methods.

        A dictionary on the following form::

            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }

        The type string should be one of the data types that toolkit accepts
        as part of its environment configuration.
        """
        # inherit the settings from the base publish plugin
        plugin_settings = super(
            MayaAssetAlembicDynamicAttributesPublishPlugin, self).settings or {}

        # settings specific to this class
        maya_publish_settings = {
            "Work Template": {
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "Primary Publish Template": {
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "Publish Template": {
                "type": "template",
                "default": None,
                "description": "Template path for published Scene playblast. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "Publish type": {
                "type": "string",
                "default": "Maya Shot Geo Alembic",
                "description": "The published file type to register.",
            },
            "Allowed groups": {
                "type": "list",
                "default": ["model_high_root"],
                "description": "The valid secondary groups.",
            },
            "Allowed asset types": {
                "type": "list",
                "default": ["character"],
                "description": "Allowed asset type to export dyn attrs alembic.",
            },
            "Export mode": {
                "type": "string",
                "default": 'alembic',
                "description": "The alembic export to use.",
            },
            "Top root": {
                "type": "string",
                "default": "rig_high_root",
                "description": "The valid top root name for a reference.",
            },
            "Start value": {
                "type": "number",
                "default": 1,
                "description": "Starting value to start alembic",
            },
            "Task dynamic Attrs":{
                "type": "list",
                "default": ["fullrig"],
                "description": "Add properties dyn_attrs in this task"
            }
        }

        # update the base settings
        plugin_settings.update(maya_publish_settings)

        return plugin_settings

    @property
    def item_filters(self):
        """
        List of item types that this plugin is interested in.

        Only items matching entries in this list will be presented to the
        accept() method. Strings can contain glob patters such as *, for example
        ["maya.*", "file.maya"]
        """

        return ["maya.alembic.attrs"]

    def is_required_node(self, ref_node, referenced_list):
        tk = self.parent.engine.context.sgtk
        ref_file = cmds.referenceQuery(ref_node, filename=True)
        fields = ['entity', 'task.Task.step.Step.code', 'published_file_type']
        publish = sgtk.util.find_publish(tk, [ref_file], fields=fields)
        if publish:
            publish = publish[ref_file]
            isAsset = publish['entity']['type'] == 'Asset'
            isCurrentAsset = publish['entity']['name'] == self.parent.engine.context.entity.get("name")
            isMayaScene = publish['published_file_type']['name'] == 'Maya Scene'
            isModel = publish['task.Task.step.Step.code'] == 'Model'
            file_is_top = ref_file in referenced_list

            if isAsset and isCurrentAsset and isMayaScene and isModel and file_is_top:
                return True

        return False

    #
    def get_top_level_referenced_nodes(self):
        # Get the current TOP references objects in the scene
        all_refs = cmds.ls(objectsOnly=True, references=True)
        top_ref_files = cmds.file(query=True, reference=True)

        top_referenced_nodes = [ref_node for ref_node in all_refs if self.is_required_node(ref_node, top_ref_files)]

        return top_referenced_nodes

    def get_root_node(self, ref_obj):
        """
        From a reference object collects the top root node name.
        returns: A string corresponding to the top root node name.
        """

        ref_nodes = cmds.referenceQuery(ref_obj, nodes=True, dagPath=True)

        transform_list = cmds.ls(ref_nodes, type="transform", long=True)

        # get main parent of current reference's transform
        if len(transform_list) == 0:
            return None

        lastParent = transform_list[0]
        while True:
            newParent = cmds.listRelatives(
                lastParent, parent=True, fullPath=True)
            if newParent == None:
                break
            lastParent = newParent[0]

        return lastParent

    def listConectionsFromDynAtt(slef):

        message = "\n----------------\n"
        attr_dictionary = {}
        # Isolate geometry with Dyn Attributes.
        scene_shapes = cmds.ls(type="mesh")
        dyn_shapes = []
        for shape in scene_shapes:
            if cmds.attributeQuery('fbCache_Dynamic_Texture', node=shape, exists=True) and not "_ex" in shape:
                dyn_shapes.append(shape)

        # Now get connections
        for nodo in dyn_shapes:
            input_conections = cmds.listConnections(nodo, c=True, scn=True)
            for cc_attribute in input_conections:
                if "Dyn" in cc_attribute:
                    attr_name = cc_attribute.split(".")[1]
                    message = "{0}\n  {1}".format(message, attr_name)
                    # Since the rig structure is conditional the frame output in rig (meaning the Ani value is not the Dyn value, out_Ani
                    # is the one that goes into Dyn). We need  to go something like this:
                    #   dyn_attr --> facial_Control --> Out_Ani --> frameExtension (fileNode Attribute)
                    # Gets the controls
                    cc_input_connections = cmds.listConnections("{0}.{1}".format(nodo, attr_name), s=True, p=True)
                    if cmds.attributeQuery("ANI", node=cc_input_connections[0], exists=True):
                        if nodo not in attr_dictionary:
                            attr_dictionary[nodo] = [cc_input_connections[0]]
                        else:
                            attr_dictionary[nodo].append(cc_input_connections[0])

        return attr_dictionary


    def set_key_framecontrols(self, list_of_elements):
        conte = True
        iteration = 0
        # set current time to first frame
        cmds.currentTime(1, edit=True)

        while len(list_of_elements) > 0:
            updated_list = []
            for prop in list_of_elements:
                try:
                    ctrl_min = (int((cmds.attributeQuery('ANI', node=prop.split('.')[0], min=True))[0]))
                    ctrl_max = (int((cmds.attributeQuery('ANI', node=prop.split('.')[0], max=True))[0]))
                    # get the current control value to set
                    new_value = ctrl_min + iteration
                    # set value to control
                    cmds.setAttr(prop.split('.')[0] + '.ANI', new_value)
                    # set kyeframe
                    cmds.setKeyframe(prop.split('.')[0], attribute='ANI', time=iteration + 1)
                    if new_value < ctrl_max:
                        updated_list.append(prop)
                except:
                    self.parent.logger.info('\t\t> {}  !!! No Tiene contrtoles min max: '.format(prop.split('.')[0]))
                    import traceback
                    self.parent.logger.info(traceback.format_exc())

            list_of_elements = updated_list
            iteration += 1

        return iteration

    def set_keys_for_dyn_attrs(self, dynamic_attr_list):

        top_value = 0

        for dyn_shape in dynamic_attr_list:
            max_value = self.set_key_framecontrols(dynamic_attr_list[dyn_shape])
            if max_value > top_value:
                top_value = max_value

        return top_value

    def accept(self, settings, item):

        step_name = self.parent.context.step['name'].lower()
        if step_name != "rig":
            return {"accepted": False, "checked": True}
        else:

            task_name = self.parent.context.task['name'].lower()
            dyn_attrs = self.listConectionsFromDynAtt()
            allowed_types = settings['Allowed asset types'].value

            current_asset = self.parent.engine.shotgun.find_one("Asset", [["id", "is", self.parent.context.entity["id"]]], ["sg_asset_type"])
            current_asset_type = current_asset["sg_asset_type"].lower()
            valid_asset_type = current_asset_type in allowed_types
            task_with_dyn_attrs = self.settings.get("Task dynamic Attrs").value

            valueoverrides = self.parent.engine.custom_frameworks.get(
                'mty-framework-valueoverrides'
            ) or self.load_framework('mty-framework-valueoverrides')

            #task_with_dyn_attrs override
            if valueoverrides:
                value_code = 'mty.publisher.maya.asset_dynattr.task_with_dyn_attrs'
                data = valueoverrides.get_value(value_code)
                data = json.loads(data)
                self.parent.engine.logger.debug("task with dyn attrs:{}".format(data))
                if type(data) == list:
                    if task_with_dyn_attrs != data:
                        task_with_dyn_attrs = data

            if task_name in task_with_dyn_attrs and dyn_attrs and valid_asset_type:
                item.properties["dyn_attrs"] = dyn_attrs

                return {"accepted": True, "checked": True}

        return {"accepted": False, "checked": True}

    def validate_groups(self, allowed, obj_name):
        """
        Validates if the child relatives for an objec name have any of the allowed names from the given list.

        Returns: Boolean for the validated groups found.
        """
        secondary_groups = cmds.listRelatives(obj_name, children=True, fullPath=True)
        for sec_grp in secondary_groups:
            # Get the last name group and remove namespace
            secondary_node_name = sec_grp.split('|')[-1]
            if secondary_node_name in allowed:
                return sec_grp


        return False

    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish. Returns a
        boolean to indicate validity.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        :returns: True if item is valid and not in proxy mode, False otherwise.
        """

        # The must exist the current asset modeling reference in the scene
        asset_refs = self.get_top_level_referenced_nodes()
        if len(asset_refs) > 1:
            raise Exception("Multiple references for the asset in the scene, there must be only one asset reference.")

        if not asset_refs:
            raise Exception("Required published modeling reference not found in the scene.")

        asset_ref_node = asset_refs[0]
        self.parent.logger.info('asset_ref_node: {} '.format(asset_ref_node))
        item.properties['asset_ref'] = asset_ref_node
        # validate asset node reference to be a published file
        pb_fields = [
            'entity',
            'version_number',
            'entity.Asset.code',
            'entity.Asset.sg_asset_type',
            'published_file_type',
            'name']
        reference_file = cmds.referenceQuery(asset_ref_node, filename=True, withoutCopyNumber=True)
        item.properties['asset_ref_file'] = reference_file
        filters_refs = [['entity', 'type_is', self.parent.engine.context.entity.get("type")]]
        tk = self.parent.engine.sgtk
        ref_publish = sgtk.util.find_publish(tk, [reference_file], filters=filters_refs, fields=pb_fields)

        if not ref_publish:
            raise Exception("Referenced file for {} node, is not a publishedFile.".format(asset_ref_node))

        # []  #
        allowed_grps = settings["Allowed groups"].value
        root_object = self.get_root_node(asset_ref_node)
        item.properties['root_object'] = root_object
        # Validate secondary groups to have at least any of the valid groups within
        allowed_groups_found = self.validate_groups(allowed_grps, root_object)
        item.properties['export_group'] = allowed_groups_found
        if not allowed_groups_found:
            raise Exception("Root allowed group name is missing, Valid secondary groups are: {}.".format(allowed_grps))

        return True

    def publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """

        dyn_attrs = item.properties["dyn_attrs"]

        start_value = settings["Start value"].value
        top_value = self.set_keys_for_dyn_attrs(dyn_attrs)

        root_object = item.properties['root_object']

        work_template_name = settings["Work Template"].value
        work_template = self.parent.engine.get_template_by_name(work_template_name)
        scene_path = cmds.file(q=True, sn=True)
        fields = work_template.get_fields(scene_path)

        abc_pub_name = settings["Publish Template"].value
        abc_pub_template = self.parent.engine.get_template_by_name(abc_pub_name)
        abc_published_path = abc_pub_template.apply_fields(fields)

        export_group = item.properties['export_group']

        exported_alembics = self.abc_export_alembic(export_group, abc_published_path, start_value, top_value)

        publish_type = settings["Publish type"].value

        referenced_file = item.properties['asset_ref_file']

        scene_pub_template_name = settings["Primary Publish Template"].value
        scene_pub_template = self.parent.engine.get_template_by_name(scene_pub_template_name)
        primary_publish_path = scene_pub_template.apply_fields(fields)

        self.register_abc_publishes(root_object, item, fields, publish_type, abc_published_path, primary_publish_path)

        return []

    def get_root_item(self, item):
        """ A handy recursive function to get an item's root parent
            This is mostly useful because the root is the only one
            where we can store extra data
        """

        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root


    def register_abc_publishes(self, asset_name, p_item, p_fields, p_type, publish_path, prim_pub_path):

        root_item = self.get_root_item(p_item)
        root_item.properties.setdefault('sg_publish_extra_data', [])
        publish_extra_data = root_item.properties['sg_publish_extra_data']

        publishes = []
        alembic_dependencies = []
        alembic_dependencies.append(prim_pub_path)
        self.parent.logger.info('alembic_dependencies: {}'.format(alembic_dependencies))

        self.parent.log_info("Publish alembic for:  {}".format(asset_name))

        # and a publish name
        publish_name = self.parent.util.get_publish_name(publish_path)

        sg_fields_to_update = {
            "sg_status_list": "rev",
            "task.Task.sg_status_list": "rev",
        }

        # Register the publish of the Alembic File:
        args = {
            "tk": self.parent.engine.sgtk,
            "context": p_item.context,
            "comment": p_item.description,
            "path": publish_path,
            "name": publish_name,
            "version_number": p_fields["version"],
            "thumbnail_path": p_item.get_thumbnail_as_path(),
            "task": self.parent.engine.context.task,
            "dependency_paths": alembic_dependencies,
            "sg_fields": sg_fields_to_update,
            "published_file_type": p_type.replace("{step_code}", p_fields['step_code'])
        }
        sg_publish = sgtk.util.register_publish(**args)

        publish_extra_data.append(sg_publish)

        #self.parent.tank.shotgun.update("PublishedFile", sg_publish["id"],{"sg_status_list": 'rev'})
        self.parent.log_info("Published file registered.")

        return sg_publish


    def remove_key_framecontrols(self, list_of_elements):
        conte = True
        iteration = 0
        # set current time to first frame
        cmds.currentTime(1, edit=True)
        for prop in list_of_elements:
            ctrl_min = (int((cmds.attributeQuery('ANI', node=prop.split('.')[0], min=True))[0]))
            ctrl_max = (int((cmds.attributeQuery('ANI', node=prop.split('.')[0], max=True))[0]))
            self.parent.logger.info("Deleting keyframes for: {}, frames: {}-{}".format(prop.split('.')[0], ctrl_min, ctrl_max))
            cmds.cutKey(prop.split('.')[0], time=(ctrl_min, ctrl_max), attribute='ANI', option="keys")

    def remove_animation_from_controls(self, dynamic_attr_list):
        # remove animation for dynamic attr controls
        for dyn_shape in dynamic_attr_list:
            self.remove_key_framecontrols(dynamic_attr_list[dyn_shape])

    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """

        dyn_attrs = item.properties["dyn_attrs"]
        self.remove_animation_from_controls(dyn_attrs)

    def abc_export_alembic(self, export_items, abc_publish_path, export_in_frame, export_out_frame):
        startFrame = export_in_frame
        endFrame = export_out_frame

        alembic_frame_relative_samples = (-0.2500, 0, 0.2500)
        abcCachePrefix = 'fbCache'
        alembic_args = [
            '-frameRange %d %d' % (startFrame, endFrame),
            '-attrPrefix %s' % abcCachePrefix,
            '-attr fbCache_Dyn_Text_L_EyeBrow_CTL_Id',
            '-stripNamespaces',
            '-uvWrite',
            '-writeColorSets',
            '-writeFaceSets',
            '-worldSpace',
            '-writeVisibility',
            '-writeUVSets',
            '-dataFormat ogawa',
            '-eulerFilter',
            # '-attr \"cacheColor\"',
            # '-attr \"xgen_Pref\"',
            # '-attr \"SubDivisionMesh\"',
        ]

        if not cmds.pluginInfo("AbcExport", loaded=True, query=True):
            cmds.loadPlugin("AbcExport")

        abc_export_cmd = "AbcExport"

        # start with fresh args for each asset
        the_args = list(alembic_args)

        export_filename = abc_publish_path
        self.parent.engine.ensure_folder_exists(os.path.dirname(export_filename))

        # Verify if the cachNodes is a list type, then add every node to the root list
        if isinstance(export_items, list):
            for node in export_items:
                the_args.append('-root %s' % node)
        else:
            the_args.append('-root %s' % export_items)

        if startFrame != endFrame:
            for sample in alembic_frame_relative_samples:
                the_args.append('-frs %.4f' % sample)
                the_args.append('-step %d' % 1.0)

        the_args.append('-file ' + export_filename.replace('\\', '/'))

        abc_export_cmd += (" -j \"%s\"" % " ".join(the_args))

        self.parent.logger.info("*" * 60)
        self.parent.logger.info(abc_export_cmd)
        self.parent.logger.info("*" * 60)
        self.parent.logger.info("Exporting alembic with command:\n{}".format(abc_export_cmd))

        mel.eval(abc_export_cmd)
