import os
import sgtk
import json
import pprint
from tank_vendor import six

from tank.platform.qt import QtGui

HookBaseClass = sgtk.get_hook_baseclass()


class BasicSceneCollector(HookBaseClass):
    '''
    Customization that allows to limit file extensions to collect.
    Context mapping is defined in override 'mty.publisher.standalone.context_mapping'
    with a dictionary following this structure:
        entity['type']:
            task['name']:
                extension:
                    file_type: 'str'
                    template: 'str'
                    is_primary: bool (main file is True, all other files is False)
    For example(yml):
        context_mapping:
            Episode:
                Storyboard:
                    sbpz:
                        file_type: 'Storyboard Pro Packed Project'
                        template: 'episode_storyboard_publish'
                        is_primary: true
                    mov:
                        file_type: 'Media Review'
                        template: 'episode_storyboard_video_review_publish'
                        is_primary: false
    '''

    def process_file(self, settings, parent_item, path):
        """
        Analyzes the given file and creates one or more items
        to represent it.

        :param dict settings: Configured settings for this collector
        :param parent_item: Root item instance
        :param path: Path to analyze

        :returns: The main item that was created, or None if no item was created
            for the supplied path or if it is rejected by context_mapping settings
        """

        #  get context_mapping from overrides
        valueoverrides = self.parent.engine.custom_frameworks.get(
            'mty-framework-valueoverrides'
        ) or self.load_framework('mty-framework-valueoverrides')
        override_code = 'mty.publisher.standalone.context_mapping'
        context_mapping = valueoverrides.get_value(override_code)
        if context_mapping:
            context_mapping = json.loads(context_mapping)

            self.parent.engine.logger.info(
                ' Got context_mapping from overrides:\n{}'.format(
                    pprint.pformat(context_mapping)
                )
            )
        else:
            # get context_mapping from hook settings
            context_mapping = settings.get('context_mapping').value
            self.parent.engine.logger.info(
                'Got context_mapping from settings:\n{}'.format(
                    pprint.pformat(context_mapping)
                )
            )

        # Validate context mapping
        if context_mapping == None or len(context_mapping) == 0:
            error_msg = (
                'Context_mapping is empty in shotgrid, cannot collect files with '
                'current project settings.'
            )
            self.logger.error('Error: {}'.format(error_msg))
            raise Exception(error_msg)

        # Check for extension match in context_mapping
        context_accept = False
        root_path, item_ext = os.path.splitext(path)
        item_ext = item_ext.replace('.', '')
        for entity, task in context_mapping.items():
            for t, extension_info in task.items():
                for extension in extension_info:
                    # comparing extensions
                    if item_ext == extension:
                        context_accept = True
                        break

        # if no match found, reject file and notice user in pop up
        if context_accept == False:
            # log the rejected file
            self.parent.engine.logger.info(
                'Rejected: collector plugin with context_mapping did not accept {}'.format(os.path.basename(path))
            )
            message = 'File {} is not allowed to be published on this project'.format(os.path.basename(path))
            QtGui.QMessageBox.warning(None, ' File not valid', message)
            return None

        # handle files and folders differently
        if os.path.isdir(path):
            self._collect_folder(parent_item, path)
            primary_item = None
        else:
            primary_item = self._collect_file(parent_item, path)

        # get secondary items with the same name but different extension
        override_sec_items = 'mty.publisher.standalone.secondary_items'
        secondary_items = valueoverrides.get_value(override_sec_items)
        if secondary_items:
            secondary_items = json.loads(secondary_items)

        if secondary_items:
            self.parent.engine.logger.info(
                ' Got secondary_items from overrides:\n{}'.format(
                    pprint.pformat(secondary_items)
                )
            )
        else:
            # get context_mapping from hook settings
            secondary_items = settings.get('secondary_items').value
            self.parent.engine.logger.info(
                'Got secondary_items from settings:\n{}'.format(
                    pprint.pformat(secondary_items)
                )
            )
        # check if there are any secondary items
        # example: {"sbpz": ["mov"]}
        # if so, check if there are any files with the same name but different extension
        list_secondary_items = secondary_items.get(item_ext, [])

        # if there are secondary items, collect them
        if list_secondary_items:
            last_item = None
            # iterate over secondary items list
            for sec_item in list_secondary_items:
                sec_path = "{}.{}" .format(root_path, sec_item)
                # only collect secondary items that exist
                if os.path.exists(sec_path):
                    # collect secondary item
                    last_item = self._collect_file(primary_item, sec_path)

            if last_item:
                # return last item only if it exists
                return last_item

        return primary_item

