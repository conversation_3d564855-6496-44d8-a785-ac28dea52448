import os
import shutil
import sys
import json
import logging
import traceback
import maya.cmds as cmds
import maya.mel as mel
import pymel.core as pm

logging.basicConfig(level=logging.DEBUG)


def initializeToolkit(logger, config_info):
    # Add location for the sgtk core

    sg_site_name = config_info.get("SgSiteName")
    sg_site_url = config_info.get("SgSiteURL")
    CORE_TOOLKIT_PATH = os.environ.get("CORE_TOOLKIT_PATH")
    logger.info("CORE_TOOLKIT_PATH: {}".format(CORE_TOOLKIT_PATH))
    sys.path.insert(0, CORE_TOOLKIT_PATH)
    import sgtk

    logger.info("Start Tookit boostrap...")

    # Initialize the logger so we get output to our terminal.
    sgtk.LogManager().initialize_custom_handler()
    # Set debugging to true so that we get more verbose output
    sgtk.LogManager().global_debug = True

    logger.info("Authenticating shotgrid user.....")
    authenticator = sgtk.authentication.ShotgunAuthenticator()

    # Custom studio api script access
    sgapi_name_deadline = os.environ.get("SGAPI_NAME_DEADLINE_{}".format(sg_site_name))
    sgapi_key_deadline = os.environ.get("SGAPI_KEY_DEADLINE_{}".format(sg_site_name))

    # Create a user programmatically using the script's key.
    user = authenticator.create_script_user(
        api_script=sgapi_name_deadline, api_key=sgapi_key_deadline, host=sg_site_url
    )
    # Tells Toolkit which user to use for connecting to ShotGrid.
    sgtk.set_authenticated_user(user)
    logger.info("Api user authenticated.")
    logger.info("user authenticated as: {}".format(user))

    return user, sgtk


def loadEngine(user, sgEntity, descriptor, sgtk, logger):
    logger.info("Loading engine: tk-maya")
    logger.info("descriptor: {}".format(descriptor))

    # instance of the ToolkitManager
    mgr = sgtk.bootstrap.ToolkitManager(user)
    mgr.plugin_id = "basic.*"
    mgr.do_shotgun_config_lookup = False
    mgr.base_configuration = descriptor
    mgr.pre_engine_start_callback = (
        lambda ctx: ctx.sgtk.synchronize_filesystem_structure()
    )
    mgr.pre_engine_start_callback = (
        lambda ctx: ctx.sgtk.synchronize_filesystem_structure()
    )

    return mgr.bootstrap_engine("tk-maya", entity=sgEntity)


def main(json_file_path):
    """
    Main function.
    """
    with open(json_file_path, "r") as file:
        data = json.load(file)
    # after reading the json file deletes
    os.remove(json_file_path)

    dict_properties = data.get("config_info", {})

    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)

    # Configuración del logger
    stream_handler = logging.StreamHandler()
    stream_handler.setLevel(logging.DEBUG)
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    stream_handler.setFormatter(formatter)
    logger.addHandler(stream_handler)

    # Inicialización del toolkit
    user, sgtk = initializeToolkit(logger, dict_properties)

    logger.info("JSON parsed successfully: %s", dict_properties)
    task = dict_properties.get("SgTask", {})
    if type(task) == str:
        task = json.loads(task)

    descriptor = dict_properties.get("ConfigDescriptor", {})
    engine = loadEngine(user, task, descriptor, sgtk, logger)

    splitter = engine.apps.get("mty-executeaction-sequence-shot-splitter")

    entity_type = data.get("entity_type")
    entities = data.get("entities")
    other_params = data.get("other_params")

    engine.logger.info("Starting Splitter execution...".rjust(120, "*"))
    result = splitter.execute_hook(
        "action_hook",
        entity_type=entity_type,
        entities=entities,
        other_params=other_params
)

    if result.get("errors"):
        raise Exception("Error while Splitting.")

    engine.logger.info("Finished Executing Splitter hook.".rjust(120, "*"))




if __name__ == "__main__":
    json_file_path = sys.argv[1]
    main(json_file_path)
