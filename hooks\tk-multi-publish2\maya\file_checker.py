# -*- coding: utf-8 -*-
# Standard library:
import pprint
from pprint import pformat
# ___   ___   ___   ___   ___
# Third party:
import pymel.core as pm
import maya.mel as mel
import sgtk
from sgtk.util.filesystem import ensure_folder_exists

# ___   ___   ___   ___   ___
# Project:
# ====================================================================

pp = pprint.PrettyPrinter(indent=3).pprint
HookBaseClass = sgtk.get_hook_baseclass()


#      ....................................................


class FileChecks(HookBaseClass):
    """
    Plugin for checking certain things in the maya file

    """

    # NOTE: The plugin icon and name are defined by the base file plugin.

    def __init__(self, parent):
        super(FileChecks, self).__init__(parent)

        try:
            command = \
                "hyperShadePanelMenuCommand" + \
                "(\"hyperShadePanel1\",\"deleteUnusedNodes\")"

            mel.eval(command)
        except:
            print("Unable to delete unused nodes.")

    @property
    def settings(self):

        # inherit the settings from the base publish plugin
        plugin_settings = super(
            FileChecks, self).settings or {}

        # settings specific to this class
        maya_publish_settings = {
            "Enforced Publish Plugins": {
                "type": dict,
                "default": {},
                "description": (
                    "{ 'plugin_name': <plugin name>, "
                    "'pipeline_steps': {<step_name> : [<task_list] }   "
                    "Describes a key - value pair for enforcements "
                    "task that must be executed along with the primary publish"
                )
            }
        }

        # update the base settings
        plugin_settings.update(maya_publish_settings)

        return plugin_settings

    def get_enforced_item(self, enforced_name, item):

        for each in item.parent.descendants:
            print(each)

        enforced_item = [i for i in item.parent.descendants if
                         i.name == enforced_name]


        # print('item parent descendants: {0}'.format(
        #     pformat(item.parent.descendants)))
        if enforced_item:
            return enforced_item[0]

        return None

    def has_breakdowns_registered(self):

        filters = [
            ['sg_shot', 'is', self.parent.engine.context.entity],
            [
                'sg_asset.Asset.sg_asset_type', 'not_in',
                ['EnvLocation', "Camera", "EnvModule"]
            ]
        ]
        list_of_breakdowns = self.parent.engine.shotgun.find(
            "CustomEntity30",
            filters, ['code']
        )

        print("item breakdowns: {0}".format(pformat(list_of_breakdowns)))

        if list_of_breakdowns:
            return True

        return False

    def validate_enforcements(self, plugin_list, item):
        """
        Validate enforcements plugins to be executed along
        the main publish on certain Step and task name.
        """

        current_step = self.parent.engine.context.step.get("name")
        current_task = self.parent.engine.context.task.get("name")

        plugins_missing = []

        print("\n" + (">" * 120))

        for plugin_name in plugin_list:
            plugin_pipeline_step = plugin_list[plugin_name]
            list_of_tasks = plugin_pipeline_step.get(current_step) or []
            do_enforce_for_current_task = current_task in list_of_tasks

            if do_enforce_for_current_task:

                enforced_item = self.get_enforced_item(plugin_name, item)

                plugin_checked_status = (
                        not enforced_item.properties
                        .get('accepted') or not enforced_item.checked
                )

                print("enforced_item: {0}".format(pformat(enforced_item)))
                print("plugin checked status: {0}".format(
                    pformat(plugin_checked_status)))

                if enforced_item and plugin_checked_status:
                    pp(plugin_name)
                    if (
                            plugin_name != "Shot Geometry Assets" and
                            self.has_breakdowns_registered()
                    ):
                        plugins_missing.append(plugin_name)

        return plugins_missing

    # ________________________________________________________________

    def validate(self, settings, item):

        enforced_plugins = settings.get("Enforced Publish Plugins").value

        pp('enforced plugins: {0}'.format(pformat(enforced_plugins)))

        if enforced_plugins:
            missing_validate_enforcements = (
                self.validate_enforcements(enforced_plugins, item)
            )

            pp('missing validate enforcements: {0}'.format(
                pformat(missing_validate_enforcements)))

            if missing_validate_enforcements:
                raise Exception(
                    "Plugins items must be present and checked " +
                    "to publish scene: " +
                    "{0}".format(missing_validate_enforcements)
                )

        #           _  _  _  _  _  _  _  _  _  _  _

        previous = super(FileChecks, self).validate(settings, item)

        if not previous:
            return False

        # Check for the maya version
        full_installed_version = \
            pm.about(installedVersion=True).split(' ')

        installed_version = full_installed_version[-1]
        if not installed_version in ['2020.4']:
            raise Exception(
                "Current Maya version is " +
                "{0}, 2020.4 is needed for this project"
                .format(installed_version)
            )

        #           _  _  _  _  _  _  _  _  _  _  _

    # ________________________________________________________________

    def paths_referenced(self):

        list_of_file_nodes = pm.ls(type="file")
        paths = []
        list_of_meshes = pm.ls(type="mesh")
        mesh_list = []

        for _path in list_of_file_nodes:
            if 'placeholder' in _path.fileTextureName.get():
                list_of_file_nodes.remove(_path)

        for mesh in list_of_meshes:
            nodo = pm.listRelatives(mesh, allParents=True)

            if (
                    not nodo[0] in mesh_list
                    and not "Orig" in mesh
                    and pm.objectType(nodo[0]) == "transform"
            ):
                mesh_list.append(nodo[0])
                try:
                    thispath = pm.referenceQuery(nodo[0], filename=True)
                    thispathfull = thispath.split('{')
                    if thispath not in paths and not thispathfull[0] in paths:
                        # This if gotta be  eliminated on a future update when enviroments can be auto ingested
                        if not 'sets' in thispathfull[
                            0].lower() and not 'cache' in thispathfull[
                            0].lower():
                            paths.append(thispathfull[0])
                except:
                    print('*  {0} not a referenced file'.format(nodo[0]))
        for file in list_of_file_nodes:
            try:
                pm.referenceQuery(file, filename=True)
            except:
                pm.select(file, replace=True)
                imageName = pm.getAttr(".fileTextureName")
                imageNameFull = imageName.split('{')
                paths.append(imageNameFull[0])
        tk = self.parent.context.sgtk
        returned = sgtk.util.find_publish(tk, paths)
        problems = []
        for p in paths:
            if not p in returned:
                problems.append(p)
        return problems
