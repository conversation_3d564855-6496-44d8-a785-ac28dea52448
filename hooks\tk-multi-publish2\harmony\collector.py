#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

import os
import sys
import shutil

import sgtk

#template = engine.get_template_by_name('harmony_asset_work')
#path ='P:\\sandbox\\WorkArea\\daniel-andrade\\assets\\Character\\cooler_ball\\Texture\\harmony\\scenes\\cooler_ball_TXT_scene.v001.xstage'
#ctx = tk.context_from_path(path)
#fields = template.get_fields(path)

#work_template = engine.get_template_by_name('asset_work_proxy_custom_shape_image')
#render_work_template = engine.get_template_by_name('asset_work_render_custom_shape_image')
#render_publish_template = engine.get_template_by_name('asset_publish_render_custom_shape_image')
#publish_template = engine.get_template_by_name('asset_publish_proxy_custom_shape_image')


#schema = shotgun.schema_field_read('Blendshape','sg_piece')
#properties = schema.get('sg_piece', {}).get('properties', {})
#allowed_pieces = properties.get('valid_values', {}).get('value', [])
#padding = int(work_template.keys['shape.frame'].format_spec)

import os

import sgtk

HookBaseClass = sgtk.get_hook_baseclass()


SESSION_PUBLISHED_TYPE = "Toon Boom Harmony Project File"
PIECES_PUBLISHED_TYPE = "Harmony Pieces Proxy Sequence"

class HarmonySessionCollector_2(HookBaseClass):
    """
    Collector that operates on the Toon Boom Harmony session. Should inherit 
    from the basic collector hook.
    """

    

    @property
    def settings(self):
        """
        Dictionary defining the settings that this collector expects to receive
        through the settings parameter in the process_current_session and
        process_file methods.
        A dictionary on the following form::
            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }
        The type string should be one of the data types that toolkit accepts as
        part of its environment configuration.
        """

        # grab any base class settings
        collector_settings = super(HarmonySessionCollector_2, self).settings or {}

        # settings specific to this collector
        harmony_session_settings = {
            "Work Template": {
                "type": "template",
                "default": None,
                "description": "Template path for artist work files. Should "
                "correspond to a template defined in "
                "templates.yml. If configured, is made available"
                "to publish plugins via the collected item's "
                "properties. ",
            },
            "Shape Proxy Work Template": {
                "type": "template",
                "default": None,
                "description": "Template path for artist work files. Should "
                "correspond to a template defined in "
                "templates.yml. If configured, is made available"
                "to publish plugins via the collected item's "
                "properties. ",
            },
            "Shape Render Work Template": {
                "type": "template",
                "default": None,
                "description": "Template path for artist work files. Should "
                "correspond to a template defined in "
                "templates.yml. If configured, is made available"
                "to publish plugins via the collected item's "
                "properties. ",
            },
            "Shape Proxy Publish Library Template": {
                "type": "template",
                "default": None,
                "description": "Template path for artist work files. Should "
                "correspond to a template defined in "
                "templates.yml. If configured, is made available"
                "to publish plugins via the collected item's "
                "properties. ",
            },
            "Shape Render Publish Library Template": {
                "type": "template",
                "default": None,
                "description": "Template path for artist work files. Should "
                "correspond to a template defined in "
                "templates.yml. If configured, is made available"
                "to publish plugins via the collected item's "
                "properties. ",
            },
            "Shape Proxy Publish Version Template": {
                "type": "template",
                "default": None,
                "description": "Template path for artist work files. Should "
                "correspond to a template defined in "
                "templates.yml. If configured, is made available"
                "to publish plugins via the collected item's "
                "properties. ",
            },
            "Shape Render Publish Version Template": {
                "type": "template",
                "default": None,
                "description": "Template path for artist work files. Should "
                "correspond to a template defined in "
                "templates.yml. If configured, is made available"
                "to publish plugins via the collected item's "
                "properties. ",
            }
        }

        # update the base settings with these settings
        collector_settings.update(harmony_session_settings)

        return collector_settings

    def process_current_session(self, settings, parent_item):
        """
        Analyzes the current session open in Toon Boom Harmony and parents a 
        subtree of items under the parent_item passed in.
        :param dict settings: Configured settings for this collector
        :param parent_item: Root item instance
        """
        super(HarmonySessionCollector_2, self).process_current_session(settings, parent_item)

        item = next((i for i in parent_item.descendants if i.type_spec.endswith('.session')), None)
        
        if item is None:
            item = self.collect_current_harmony_session(settings, parent_item)

        #if parent_item.name == '__root__':
        #    item = self.collect_current_harmony_session(settings, parent_item)
        #else:
        #    item = parent_item

        # create an item representing the current Toon Boom Harmony session
        #item = self.collect_current_harmony_session(settings, session_item)
        pieces = self._collect_pieces(settings, item)
        #return item


    def get_export_path(self, settings):
        publisher = self.parent

        work_template = None
        work_template_setting = settings.get("Work Template")
        if work_template_setting:
            work_template = publisher.engine.get_template_by_name(work_template_setting.value)

            self.logger.debug("Work template defined for Toon Boom Harmony collection.")

        work_export_template = None
        work_export_template_setting = settings.get("Work Export Template")
        if work_export_template_setting:
            self.logger.debug(
                "Work Export template settings: %s" % work_export_template_setting
            )

            work_export_template = publisher.engine.get_template_by_name(
                work_export_template_setting.value
            )

            self.logger.debug("Work Export template defined for Toon Boom Harmony collection.")

        if work_export_template and work_template:
            path = publisher.engine.app.get_current_project_path()
            fields = work_template.get_fields(path)
            export_path = work_export_template.apply_fields(fields)

            self.logger.debug("Work Export Path is: %s " % export_path)

            return export_path

    def _collect_pieces(self, settings, parent_item):

        # get pyseq in a partially generic way, untill we have mty-framework-externalpython
        # we know this hook files lives in {config}/tk-multi-publish2/harmony, so we will place pyseq there
        config_path = os.path.dirname(os.path.dirname(self.disk_location))
        python_modules_path = os.path.join(config_path, "external_python_modules")
        sys.path.append(python_modules_path)
        import pyseq

        engine = sgtk.platform.current_engine()
        path = engine.app.get_current_project_path()
        publisher = self.parent

        # get templates from settings

        self.parent.log_debug(settings)

        session_work_template_setting = settings.get("Work Template").value
        session_work_template = publisher.engine.get_template_by_name(session_work_template_setting)
        fields = session_work_template.get_fields(path)
        
        proxy_work_template_setting = settings.get("Shape Proxy Work Template").value
        proxy_work_template = engine.get_template_by_name(proxy_work_template_setting)
        
        render_work_template_setting = settings.get("Shape Render Work Template").value
        render_work_template = engine.get_template_by_name(render_work_template_setting)
        
        render_publish_ver_template_setting = settings.get("Shape Render Publish Version Template").value
        render_publish_ver_template = engine.get_template_by_name(render_publish_ver_template_setting)
        
        proxy_publish_ver_template_setting = settings.get("Shape Proxy Publish Version Template").value
        proxy_publish_ver_template = engine.get_template_by_name(proxy_publish_ver_template_setting)

        render_publish_lib_template_setting = settings.get("Shape Render Publish Library Template").value
        render_publish_lib_template = engine.get_template_by_name(render_publish_lib_template_setting)
        
        proxy_publish_lib_template_setting = settings.get("Shape Proxy Publish Library Template").value
        proxy_publish_lib_template = engine.get_template_by_name(proxy_publish_lib_template_setting)

        templates_by_piece_type = {'proxy': {'work': proxy_work_template,
                                             'proxy_version': proxy_publish_ver_template,
                                             'aces_version' : render_publish_ver_template,
                                             'publish_library': proxy_publish_lib_template},
        #                           'render': {'work': render_work_template,
        #                                      'publish_version': render_publish_ver_template,
        #                                      'publish_library': render_publish_lib_template}}
                                            }
        # check allowed piece names from shotgun schema
        schema = publisher.shotgun.schema_field_read('Blendshape','sg_piece')
        properties = schema.get('sg_piece', {}).get('properties', {})
        allowed_pieces = properties.get('valid_values', {}).get('value', [])    
        self.logger.debug(allowed_pieces)
        self.parent.log_debug("allowed_pieces: {0}".format(allowed_pieces))
        self.logger.debug("Allowed Pieces found!")

        session_items = [] 
        for piece in allowed_pieces:
            fields['shape.piece'] = piece
            fields['shape.frame'] = 0
            
            for piece_type in ['proxy']:

                templates = templates_by_piece_type[piece_type]

                test_file_path = templates['work'].apply_fields(fields)
                piece_sequence = pyseq.get_sequences(os.path.dirname(test_file_path))
                
                if len(piece_sequence) > 0:
                    sequence_name = str(piece_sequence[0])
                    seqID = 0
                    if "Thumbs.db" in sequence_name:
                        if len(piece_sequence) == 2:
                            seqID = 1
                            sequence_name = str(piece_sequence[1])
                            self.logger.debug("{0} found".format(sequence_name))
                        else:
                            break
                    self.logger.debug("{0} found".format(piece))        

                    _, template_ext = os.path.splitext(test_file_path)
                    _, sequence_ext = os.path.splitext(sequence_name)

                    if not template_ext.lower() == sequence_ext.lower():
                        self.parent.log_debug("Ignoring incorrect sequence file: %s" % sequence_name)
                        continue

                    self.logger.debug("Found {0} piece".format(piece))
                    display_name = sequence_name
                    session_item = parent_item.create_item(
                    "harmony.pieces", "Asset {0} Shape Piece".format(piece_type.title()), display_name
                    )

                    session_item.properties["piece_sequence"] = piece_sequence[seqID]
                    session_item.properties["piece_piece"] = piece
                    session_item.properties["piece_type"] = piece_type
                    session_item.properties["piece_work_template"] = templates['work']
                    session_item.properties["piece_publish_version_template"] = templates['proxy_version']
                    session_item.properties["piece_publish_aces_template"] = templates['aces_version']
                    session_item.properties["piece_publish_library_template"] = templates['publish_library']
                    session_item.properties["piece_sequence_path"] = os.path.dirname(test_file_path)
                    session_item.properties["publish_type"] = PIECES_PUBLISHED_TYPE

                    session_items.append(session_item)

                    icon_path = os.path.join(self.disk_location, "sg_blendshape_icon.png")
                    session_item.set_icon_from_path(icon_path)
                
        return session_items
