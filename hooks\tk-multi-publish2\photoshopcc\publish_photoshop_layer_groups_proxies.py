# -*- coding: utf-8 -*-
# Standard library:
import os
import re
import json
import pprint
import tempfile

import sgtk

# ======================================================================================

HookBaseClass = sgtk.get_hook_baseclass()


pp = pprint.pprint
pf = pprint.pformat


class LayerGroupProxyImagesPublishPlugin(HookBaseClass):
    @property
    def icon(self):

        return os.path.join(
            self.disk_location, os.pardir, "icons", "proxy_image_publish.png"
        )

    @property
    def name(self):

        return "Publish Layer Group Proxy Images"

    @property
    def description(self):

        return """
        <p>This plugin publishes one proxy image for each the output layer group.</p>

        """

    @property
    def settings(self):

        # inherit the settings from the base publish plugin
        plugin_settings = super(LayerGroupProxyImagesPublishPlugin, self).settings or {}

        # settings specific to this class
        review_publish_settings = {
            "Available Steps": {
                "type": "list",
                "default": [],
                "description": "A list of Step names to filter.",
            },
            "Available Tasks": {
                "type": "list",
                "default": [],
                "description": "A list of tasks names to filter.",
            },
            # "Document Width": {
            #     "type": "int",
            #     "default": 1024,
            #     "description": "Photoshop document width size."
            # },
            # "Document Height": {
            #     "type": "int",
            #     "default": 768,
            #     "description": "Photoshop document height size."
            # },
            # "Image Resolution": {
            #     "type": "int",
            #     "default": 72,
            #     "description": "Image resolution in dpi.",
            # },
            "Proxy Image Size Percentage": {
                "type": "int",
                "default": 20,
                "description": "Percentage for resizing the original image size.",
            },
            "Layer Group Proxy Image Format": {
                "type": "string",
                "default": "png",
                "description": "Extension to define the output proxy images format.",
            },
            "Layer Group Image Format": {
                "type": "string",
                "default": "png",
                "description": "Extension to define the output images format.",
            },
        }

        # update the base settings
        plugin_settings.update(review_publish_settings)

        return plugin_settings

    @property
    def item_filters(self):
        return ["photoshop.layergroupsproxies"]

    def accept(self, settings, item):

        self.parent.logger.info("\nstart accept plugin -----------------------------\n")
        self.photoshop = self.parent.engine.adobe
        # self.collect_layer_groups(item)

        context = self.parent.context

        pipelineSteps = settings["Available Steps"].value
        pipelineTasks = settings["Available Tasks"].value

        self.valueoverrides = self.parent.engine.custom_frameworks.get(
            'mty-framework-valueoverrides'
        ) or self.load_framework('mty-framework-valueoverrides')
        override_code_tasks = 'mty.publisher.photoshop.layer_groups_proxies.tasks'
        data_tasks = self.valueoverrides.get_value(override_code_tasks)
        self.parent.logger.info("\n data tasks {}".format(data_tasks))
        if data_tasks is not None:
            pipelineTasks = json.loads(data_tasks)

        override_code_steps = 'mty.publisher.photoshop.layer_groups_proxies.steps'
        data_steps = self.valueoverrides.get_value(override_code_steps)
        if data_steps is not None:
            pipelineSteps = json.loads(data_steps)

        self.parent.logger.info("\n available steps {}".format(pipelineSteps))
        self.parent.logger.info("\n available tasks {}".format(pipelineTasks))

        non_filter_steps = pipelineSteps == []
        non_filter_tasks = pipelineTasks == []

        accepted_tasks = False
        accepted_steps = False
        if not non_filter_tasks:
            step_filter_tasks = (
                context.task["name"] in pipelineTasks
            )
            if step_filter_tasks:
                accepted_tasks = True
        else:
            accepted_tasks = True

        if not non_filter_steps:
            step_filter_steps = (
                context.step["name"] in pipelineSteps
            )
            if step_filter_steps:
                accepted_steps = True
        else:
            accepted_steps = True

        if accepted_tasks and accepted_steps:
            return {"accepted": True, "checked": True}
        else:
            return {"accepted": False, "checked": True}

    def validate_actions(self, state, **kwargs):

        if state["errors_found"] > 0:
            self.logger.error(
                "There are {0} errors in the scene".format(state["errors_found"])
            )

            for key in state["errors"]:
                error_message = "{0} ({1}):".format(key, len(state["errors"][key]))
                problems_message = ""

                for element in state["errors"][key]:
                    problems_message += "{0}\n".format(element)

                self.logger.error(
                    error_message,
                    extra={
                        "action_show_more_info": {
                            "label": "Show details",
                            "tooltip": "Show all information from the error",
                            "text": "<h3>{0}</h3><pre>{1}</pre>".format(
                                error_message, problems_message
                            ),
                        }
                    },
                )

                #   . . . . . . . . . . . . . . . . . . . . . .

                if state["callbacks"][key]["enabled"]:
                    log = None
                    message_type = state["callbacks"][key]["type"]

                    if message_type == "error":
                        log = self.logger.error
                    elif message_type == "warning":
                        log = self.logger.warning
                    elif message_type == "debug":
                        log = self.logger.debug
                    else:
                        log = self.logger.info

                    message = state["callbacks"][key]["message"]
                    callback = state["callbacks"][key]["callback"]
                    label = state["callbacks"][key]["label"]
                    tooltip = state["callbacks"][key]["tooltip"]

                    log(
                        message,
                        extra={
                            "action_button": {
                                "label": label,
                                "tooltip": tooltip,
                                "callback": callback(),
                            }
                        },
                    )
                #   . . . . . . . . . . . . . . . . . . . . . .

        return state["errors_found"]

    def composer_set(self, name, list_of_errors, state, action):
        if len(list_of_errors) > 0:
            state["errors_found"] += len(list_of_errors)
            state["errors"][name] = list_of_errors

            if "enabled" not in action.keys():
                raise Exception(
                    "Error in {0}: \n".format(name)
                    + '"enabled" key is not present in action dictionary.\n'
                    + "provided action dictionary: {0}".format(str(action))
                )

            #   . . . . . . . . . . . . . . . . . . . . . .

            set_of_keys = set(action.keys())

            set_of_required_keys = {
                "enabled",
                "type",
                "callback",
                "label",
                "tooltip",
                "message",
            }

            #   . . . . . . . . . . . . . . . . . . . . . .

            if not set_of_required_keys.issuperset(set_of_keys):
                raise Exception(
                    "Error in {0}: \n".format(name)
                    + "Missing keys in action dictionary.\n"
                    + "provided action dictionary: {0}".format(str(action))
                )

            #   . . . . . . . . . . . . . . . . . . . . . .

            if action["enabled"]:

                state["callbacks"][name] = {
                    "enabled": action["enabled"],
                    "type": action["type"],
                    "callback": action["callback"],
                    "label": action["label"],
                    "tooltip": action["tooltip"],
                    "message": action["message"],
                }
            else:
                state["callbacks"][name] = {"enabled": False}

    def validate(self, settings, item):

        self.parent.logger.info(
            "start layer group proxy images validation {}".format("-" * 80)
        )
        state = {"errors_found": 0, "errors": {}, "callbacks": {}}

        # ------------------------------------------------------------------------------
        root_item = self.get_root_item(item)

        # ------------------------------------------------------------------------------

        # Get templates and file types from item
        templates_and_file_types = item.properties.get("templates_and_file_types", None)

        if not templates_and_file_types:
            templates_and_file_types_errors = [
                "Couldn't get templates and publish file types from item"
            ]
            self.composer_set(
                name="Templates and file types error",
                list_of_errors=templates_and_file_types_errors,
                state=state,
                action={"enabled": False},
            )

        state_result = self.validate_actions(state)

        if state_result:
            there_is_error = (
                "Validation checks have not been passed, "
                + "found {0} problems".format(state_result)
            )

            raise Exception(there_is_error)

        else:
            self.logger.debug("All checks passed!")

        """ omited by coordinator request, they are gonna use frame numbers

        # Validate continuos layer naming numbering
        numbers_list = []
        for name in sorted(groups_names):
            numbers_list.append(int(name))

        numbers_list = sorted(numbers_list)

        self.parent.log_debug('Total layer groups: ' + str(len(numbers_list)))
        self.parent.log_debug('Smallest number: ' + str(numbers_list[0]))
        self.parent.log_debug('Bigest number: ' + str(numbers_list[-1]))

        if (numbers_list[-1] - numbers_list[0] + 1) != len(numbers_list):
            raise Exception(
                "Layers are not named correctly, they need to be continued numbers..."
            )
        """

        return True

    # ----------------------------------------------------------------------------------

    def _get_image_width(self, image_path):
        """
        Get's the image original width resolution using ffprobe.
        """

        result = None

        if image_path and os.path.exists(image_path):
            self.parent.engine.logger.info("loading mty-framework-ffmpeg")
            ffmpeg = (
                self.parent.engine.custom_frameworks.get("mty-framework-ffmpeg", None)
                or self.load_framework("mty-framework-ffmpeg")
            )
            FFmpegCoreTools = ffmpeg.ffmpegCore
            # "media" binary means ffprobe executable will be used
            FFmpegCoreTools.set_binary(binary='media')
            ffprobe_path = os.path.dirname(ffmpeg.ffmpegCore.get_bin_path())

            self.parent.engine.logger.info("ffprobe path: {}".format(ffprobe_path))

            ffprobe_cmd = (
                # "{} "
                "-hide_banner "
                "-loglevel panic "
                "-of json "
                "-show_entries stream=width "
                "{}"
            ).format(image_path)

            self.parent.engine.logger.info("ffprobe_cmd: {}".format(ffprobe_cmd))

            _err, _info = FFmpegCoreTools.execute_command(ffprobe_cmd)

            if  not _err:
                data = dict(json.loads(_info))
                streams = data.get("streams")
                if streams:
                    result = int(streams[0].get("width"))
                self.parent.engine.logger.info("original image width: {}".format(result))

        return result

    def _get_project_resolution_width(self):
        result = None

        # Get project resolution from SG
        hook_expression = "{config}/get_entity_resolution.py"
        project_resolution = self.parent.execute_hook_expression(
            hook_expression,
            "get_resolution",
            engine=self.parent.engine,
        )

        if project_resolution:
            pattern = re.compile(r"(?P<width>\d+)x(?P<height>\d+)")
            match = re.match(pattern, str(project_resolution))
            if match:
                result = int(match.groupdict()["width"])

        return result

    def export_copy_image(self, source_path, dest_path, cmd=[]):

        if not os.path.exists(os.path.dirname(dest_path)):
            os.makedirs(os.path.dirname(dest_path))

        if not cmd:
            # default copy source to dest path command
            cmd = [source_path, "-resize", "20%", dest_path]

        self.ImageMagickCoreTools.set_binary("convert")
        imgagemagick_bin_path = self.ImageMagickCoreTools.get_bin_path()
        self.parent.logger.info(
            "imgagemagick_bin_path: {}".format(imgagemagick_bin_path)
        )

        self.parent.logger.info("EXPORT COPY: magick convert {}".format(cmd))
        _err, _out, executed_cmd = self.ImageMagickCoreTools.execute_command(cmd)
        if _err:
            raise Exception(
                (
                    "Failed to export image: {0} to {1}\n"
                    "Error: {2}\n"
                    "Executed command: {3}"
                ).format(
                    source_path, dest_path, _out, executed_cmd
                )
            )

    # ----------------------------------------------------------------------------------

    def register_publish(
        self,
        comment,
        path,
        name,
        version_number,
        thumbnail,
        published_file_type,
        dependencies=[],
    ):
        # get media resolution
        media_resolution = self.parent.execute_hook_expression(
            "{config}/tk-multi-publish2/multi/resolution_from_path.py",
            "media_resolution_value",
            path=path
        )
        self.parent.logger.info(
            "media resolution from hook: {}".format(media_resolution)
        )

        # register the publish:
        publish_data = {
            "tk": self.parent.engine.sgtk,
            "context": self.parent.engine.context,
            "comment": comment,
            "path": path,
            "name": name,
            "version_number": version_number,
            "thumbnail_path": thumbnail,
            "task": self.parent.engine.context.task,
            "published_file_type": published_file_type,
            "dependency_paths": dependencies,
            "sg_fields":{"sg_media_resolution": media_resolution},
        }
        sg_publish = sgtk.util.register_publish(**publish_data)

        self.parent.tank.shotgun.update(
            "PublishedFile", sg_publish["id"], {"sg_status_list": "ip"}
        )

        return sg_publish

    # ----------------------------------------------------------------------------------

    def publish_layer_group_proxy_images(self, item, scene_data, settings):

        self.sg_project_resolution = None
        self.layer_group_image_width = None

        self.parent.logger.info(
            "publish_layer_group_proxy_images start {}".ljust(120, "-")
        )

        sg_publishes = []

        layer_group_proxy_image_format = settings[
            "Layer Group Proxy Image Format"
        ].value

        root_item = self.get_root_item(item)
        layer_group_paths = root_item.properties.get("layer_group_paths", {})
        layer_group_image_format = settings["Layer Group Image Format"].value
        self.parent.logger.info(
                "previous export proxies: {}".format(layer_group_paths)
                )

        if layer_group_paths:

            for layer_group_path in layer_group_paths.keys():
                self.parent.logger.info("layer_group_path: {}".format(layer_group_path))

                fields = scene_data["image_publish_template"].get_fields(layer_group_path)
                fields["extension"] = layer_group_proxy_image_format

                self.parent.logger.debug("fields:\n{}".format(pprint.pformat(fields)))

                image_review_publish_template = scene_data["publish_template"]
                image_review_publish_path = image_review_publish_template.apply_fields(
                    fields
                )
                self.logger.info(
                    "Publishing layer group proxy '{}', please wait.".format(
                        os.path.basename(image_review_publish_path)
                    )
                )
                self.parent.logger.info(
                    "Publishing layer group proxy '{}'".format(
                        os.path.basename(image_review_publish_path)
                    )
                )

                self.create_proxy_image(
                    layer_group_path, image_review_publish_path, settings
                )
                # Get publish name
                publish_name = self.parent.util.get_publish_name(
                    image_review_publish_path, sequence=True
                )

                # Create publish data dict for registering the publish
                publish_data = {
                    "comment": item.description,
                    "path": image_review_publish_path,
                    "name": publish_name,
                    "version_number": scene_data["publish_version_number"],
                    "thumbnail": layer_group_path,
                    "published_file_type": scene_data["publish_type"],
                    "dependencies": [scene_data["primary_publish_path"]],
                }

                sg_publish = self.register_publish(**publish_data)
                sg_publishes.append(sg_publish)
        else:
            layer_groups_data = item.properties.get("layer_group_data", None)

            for layergroup in layer_groups_data:
                self.parent.logger.info(
                    "LAYERGROUP: {}".format(
                        layer_groups_data[layergroup]["original_name"]
                    )
                )
                layer_original_name = layer_groups_data[layergroup]["original_name"]
                layer_reformat_name = layer_groups_data[layergroup]["reformat_name"]
                layer_order = layer_groups_data[layergroup]["order"]
                layer_index = layer_groups_data[layergroup]["index"]
                layer_group_name = layer_groups_data[layergroup]["layer_group"]

                self.logger.info(
                    "Publishing layer group '{}', please wait.".format(
                        layer_reformat_name
                    )
                )
                self.parent.logger.info(
                    "Publishing layer group '{}'".format(layer_reformat_name)
                )

                fields = scene_data["fields"]
                fields["photoshop.layer_name"] = layer_reformat_name
                fields["extension"] = layer_group_image_format
                self.parent.logger.debug("fields:\n{}".format(pprint.pformat(fields)))

                image_publish_template = scene_data["publish_template"]
                image_publish_path = image_publish_template.apply_fields(fields)
                image_publish_basename = os.path.basename(image_publish_path)

                image_temp_path = os.sep.join(
                    (scene_data["temp_folder"], image_publish_basename)
                )

                # delete file if exists previous created images
                self.delete_if_exists(image_temp_path)

                # Set layers visibility for exporting the current layer group.
                # If group layer is "master" then the original visibility is restored for
                # all document layers.
                self.set_visible_output_flatten(
                    scene_data["PS_document"], layer_original_name, layer_group_name
                )

                # ---------------------------------------------------------------------------
                # set photoshop to save image as png
                png_file = self.photoshop.File(image_temp_path)
                png_options = self.photoshop.PNGSaveOptions()
                png_options.compression = (
                    5  # valid values are [0..9], default is 0, which means no compression
                )
                png_options.interlaced = False

                # photoshop save as a copy
                self.parent.logger.info(
                    "Saving PNG file to tmp location: {}".format(image_temp_path)
                )
                self.photoshop.app.activeDocument.saveAs(png_file, png_options, True)
                if not os.path.exists(image_temp_path):
                    # Restore layers visibility based on the saved state within the collector
                    self.restore_visibility_state_in_all_layers(
                        scene_data["PS_document"], self.all_layers_visibility_state
                    )
                    raise Exception(
                        "Error saving PNG file from Photoshop: {}".format(png_file)
                    )

                # ---------------------------------------------------------------------------

                # ensure publish path exists and copy png to publish path
                if not os.path.exists(os.path.dirname(image_publish_path)):
                    os.makedirs(os.path.dirname(image_publish_path))
                self.create_proxy_image(image_temp_path, image_publish_path, settings)


                # Restore layers visibility based on the saved state within the collector
                self.restore_visibility_output_flatten(
                    scene_data["PS_document"], layer_group_name
                )

                # delete temp files if exists
                self.delete_if_exists(image_temp_path)

                # Get publish name
                publish_name = self.parent.util.get_publish_name(
                    image_publish_path, sequence=False
                )
                self.parent.logger.info(
                    "publish_name from util (not used): {}".format(publish_name)
                )

                publish_name = self.parent.execute_hook_expression(
                    "{config}/tk-multi-publish2/multi/name_from_path_info.py",
                    "get_publish_name",
                    path=image_publish_path
                )
                self.parent.logger.info(
                    "publish_name from hook: {}".format(publish_name)
                )

                # Create publish data dict for registering the publish
                publish_data = {
                    "comment": item.description,
                    "path": image_publish_path,
                    "name": publish_name,
                    "version_number": scene_data["publish_version_number"],
                    "thumbnail": image_publish_path,
                    "published_file_type": scene_data["publish_type"],
                    "dependencies": [scene_data["primary_publish_path"]],
                }

                sg_publish = self.register_publish(**publish_data)
                sg_publishes.append(sg_publish)
                layer_group_paths.update({image_publish_path: layer_index})
            # set layer group paths in root item
            root_item.properties["layer_group_paths"] = layer_group_paths

            self.restore_visibility_state_in_all_layers(
                scene_data["PS_document"], self.all_layers_visibility_state
            )

        return sg_publishes

    def create_proxy_image(self, source_path, dest_path, settings):
        # --------------------------------------------------------------------------</span>
                # Create proxy png image using imagemagick
                                # Get project resolution width
                if not self.sg_project_resolution:
                    self.sg_project_resolution = self._get_project_resolution_width()

                # Get image width using ffprobe
                if not self.layer_group_image_width:
                    self.layer_group_image_width = self._get_image_width(source_path)

                override_code_steps = 'mty.publisher.photoshopcc.layer_proxy_resize_percentage'
                scale_percentage = self.valueoverrides.get_value(override_code_steps)
                if not scale_percentage:
                    scale_percentage = settings["Proxy Image Size Percentage"].value

                # check if scaling at scale_percentage is enough for the project resolution
                # if not, then we calculate a new percentage.
                proxy_image_width = self.layer_group_image_width / scale_percentage
                if proxy_image_width < self.sg_project_resolution:
                    scale_percentage = int(
                        ((self.sg_project_resolution * 1.2) * 100) / self.layer_group_image_width
                    )

                self.parent.logger.info(
                    "proxy image scale_percentage: {}".format(scale_percentage)
                )

                #cmd = ('{1} -resize {0}% {2}').format(
                #    scale_percentage, layer_group_path, image_review_publish_path
                #)
                cmd = [
                    source_path, "-resize",
                    "{}%".format(scale_percentage),
                    dest_path,
                ]
                self.export_copy_image(source_path, dest_path, cmd)


    def publish(self, settings, item):

        # Get all layer visibility state saved originally in the collector
        self.all_layers_visibility_state = item.properties.get(
            "all_layers_visibility_state", None
        )

        self.imagemagick = self.parent.engine.custom_frameworks.get(
            "mty-framework-imagemagick", None
        )
        if not self.imagemagick:
            self.parent.engine.logger.error(
                "Couldn't get imagemagick framework from engine.custom_frameworks"
            )
            self.imagemagick = self.load_framework("mty-framework-imagemagick")
        self.ImageMagickCoreTools = self.imagemagick.imageMagickCore

        root_item = self.get_root_item(item)
        root_item.properties.setdefault("sg_publish_extra_data", [])
        sg_publish_extra_data = root_item.properties["sg_publish_extra_data"]

        # get templates, file types and layer group data from item (from collector)
        templates_and_file_types = item.properties.get("templates_and_file_types", None)

        if not templates_and_file_types:
            raise Exception("Couldn't get templates from item.")

        # Get PS document data from templates_and_file_types dict (gathered from collector)
        # PS_document = templates_and_file_types.get("PS_document")
        PS_document_path = templates_and_file_types.get("PS_document_path")
        # instead of getting the document from the templates and file types dict,
        # get it from the current opened document, because the previous document
        # has been already closed by the main publisher
        PS_document = self.parent.engine.adobe.get_active_document()

        for child in root_item.children:
            ps_doc_basename = os.path.basename(PS_document_path)
            if child.name == ps_doc_basename:
                child.properties["document"] = PS_document

        primary_publish_template = templates_and_file_types.get(
            "primary_publish_template"
        )
        work_template = templates_and_file_types.get("primary_work_template")
        image_publish_template = templates_and_file_types.get("image_publish_template")
        proxy_image_publish_template = templates_and_file_types.get(
            "proxy_image_publish_template"
        )

        # get template fields and apply them to the relevant templates
        fields = work_template.get_fields(PS_document_path)
        primary_publish_path = primary_publish_template.apply_fields(fields)

        curent_scene_data = {
            "PS_document": PS_document,
            "PS_document_path": PS_document_path,
            "temp_folder": tempfile.gettempdir(),
            "primary_publish_template": primary_publish_template,
            "primary_publish_path": primary_publish_path,
            "work_template": work_template,
            "publish_type": templates_and_file_types.get("proxy_image_file_type"),
            "publish_template": proxy_image_publish_template,
            "image_publish_template": image_publish_template,
            "fields": fields,
            "publish_version_number": fields["version"],
        }

        # Create images from layer groups
        sg_publishes = self.publish_layer_group_proxy_images(
            item, curent_scene_data, settings
        )
        sg_publish_extra_data.extend(sg_publishes)
        # self.parent.logger.info(
        #     "layer_groups_proxies sg_publish_extra_data: {}".format(
        #         pf(sg_publish_extra_data)
        #     )
        # )

        # Update task status for revision
        sg_task = self.parent.context.task
        self.parent.logger.info(
            "End of Publish and updating task with id: {} to status: 'rev'".format(
                str(sg_task['id'])
            )
        )

        try:
            self.parent.engine.shotgun.update(
                "Task", sg_task["id"], {"sg_status_list": 'rev'}
            )
        except:
            pass

    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        pass

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root

    def fix_path(self, path):
        """
        Fixes the path to be compatible with the Harmony API, which only accepts forward
        slashes
        """
        # fix image_path
        path = path.replace("\\\\", "/")
        path = path.replace("\\", "/")

        return path

    def delete_if_exists(self, path):

        if os.path.exists(path):
            os.remove(path)

    def disable_guides_group(self, PS_document, layer_groups_data):
        for layerset in PS_document.layerSets:
            orig_name = layerset.name
            if "guides" in orig_name or "Guides" in orig_name:
                # guides_output_group = layer_groups_data[layer]["layer_group"]
                layerset.visible = False
                self.parent.logger.info(
                    "Successfully disabled guides group: {}".format(orig_name)
                )

    def set_visible_output_flatten(
        self,
        PS_document,
        layer_name=None,
        layer_groups_name=None
    ):
        """
        Isolate layer group: all layers will be turned off except the one called as
        the layer_group_name argument.
        """
        for layer in PS_document.layers:
                layer.visible = False

        for layerset in PS_document.layerSets:
            if layerset.name in layer_groups_name:
                layerset.visible = True
                for layer in layerset.layers:
                    if layer.name == layer_name:
                        layer.visible = True
                    else:
                        layer.visible = False

    def restore_visibility_output_flatten(self, PS_document, layer_groups_name):
        #restore visibility of output flatten layers
        for layerset in PS_document.layerSets:
            if layerset.name in layer_groups_name:
                layerset.visible = True
                for layer in layerset.layers:
                        layer.visible = True

    def restore_visibility_state_in_all_layers(
        self, PS_document, saved_layers_state=None
    ):
        """
        Restore the visibility of each layer based on the visibility saved in
        the collector as "all_layers_visibility_state" property in the root_item
        """
        if not saved_layers_state:
            return

        for layer in PS_document.layers:
            layer_name = layer.name
            saved_visible_state = saved_layers_state.get(layer_name, {}).get(
                "visible", None
            )
            # used this condition because the saved_visible_state can be False
            if saved_visible_state == None:
                continue

            layer.visible = saved_visible_state
