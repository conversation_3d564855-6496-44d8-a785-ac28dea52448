# -*- coding: utf-8 -*-
# Standard library:
import os
import re
import sys
import json
import pprint
import shutil
import tempfile
import traceback

import sgtk

from sgtk.platform.qt import QtCore

# ======================================================================================

HookBaseClass = sgtk.get_hook_baseclass()


pp = pprint.pprint
pf = pprint.pformat


class PSProxyImagePublishPlugin(HookBaseClass):
    @property
    def icon(self):
        icon_path = os.path.join(
            self.parent.engine.disk_location,
            "hooks",
            "tk-multi-publish2",
            "icons",
            "photoshop.png",
        )

        return icon_path

    @property
    def name(self):
        return "Publish Photoshop Proxy Image"

    @property
    def description(self):
        return """
        <p>This plugin publishes one sclaed down photosop proxy image.</p>

        """

    @property
    def settings(self):
        # inherit the settings from the base publish plugin
        plugin_settings = super(PSProxyImagePublishPlugin, self).settings or {}

        # settings specific to this class
        ps_proxy_publish_settings = {}

        # update the base settings
        plugin_settings.update(ps_proxy_publish_settings)

        return plugin_settings

    @property
    def item_filters(self):
        # return ["photoshop.layergroups.image", "photoshop.layergroups"]
        return ["photoshop.proxy"]

    def accept(self, settings, item):
        self.parent.logger.info("\nstart accept plugin -----------------------------\n")
        self.photoshop = self.parent.engine.adobe
        context = self.parent.engine.context

        return {"accepted": True, "checked": True}

    def validate_actions(self, state, **kwargs):
        if state["errors_found"] > 0:
            self.logger.error(
                "There are {0} errors in the scene".format(state["errors_found"])
            )

            for key in state["errors"]:
                error_message = "{0} ({1}):".format(key, len(state["errors"][key]))
                problems_message = ""

                for element in state["errors"][key]:
                    problems_message += "{0}\n".format(element)

                self.logger.error(
                    error_message,
                    extra={
                        "action_show_more_info": {
                            "label": "Show details",
                            "tooltip": "Show all information from the error",
                            "text": "<h3>{0}</h3><pre>{1}</pre>".format(
                                error_message, problems_message
                            ),
                        }
                    },
                )

                #   . . . . . . . . . . . . . . . . . . . . . .

                if state["callbacks"][key]["enabled"]:
                    log = None
                    message_type = state["callbacks"][key]["type"]

                    if message_type == "error":
                        log = self.logger.error
                    elif message_type == "warning":
                        log = self.logger.warning
                    elif message_type == "debug":
                        log = self.logger.debug
                    else:
                        log = self.logger.info

                    message = state["callbacks"][key]["message"]
                    callback = state["callbacks"][key]["callback"]
                    label = state["callbacks"][key]["label"]
                    tooltip = state["callbacks"][key]["tooltip"]

                    log(
                        message,
                        extra={
                            "action_button": {
                                "label": label,
                                "tooltip": tooltip,
                                "callback": callback(),
                            }
                        },
                    )
                #   . . . . . . . . . . . . . . . . . . . . . .

        return state["errors_found"]

    def composer_set(self, name, list_of_errors, state, action):
        if len(list_of_errors) > 0:
            state["errors_found"] += len(list_of_errors)
            state["errors"][name] = list_of_errors

            if "enabled" not in action.keys():
                raise Exception(
                    "Error in {0}: \n".format(name)
                    + '"enabled" key is not present in action dictionary.\n'
                    + "provided action dictionary: {0}".format(str(action))
                )

            #   . . . . . . . . . . . . . . . . . . . . . .

            set_of_keys = set(action.keys())

            set_of_required_keys = {
                "enabled",
                "type",
                "callback",
                "label",
                "tooltip",
                "message",
            }

            #   . . . . . . . . . . . . . . . . . . . . . .

            if not set_of_required_keys.issuperset(set_of_keys):
                raise Exception(
                    "Error in {0}: \n".format(name)
                    + "Missing keys in action dictionary.\n"
                    + "provided action dictionary: {0}".format(str(action))
                )

            #   . . . . . . . . . . . . . . . . . . . . . .

            if action["enabled"]:
                state["callbacks"][name] = {
                    "enabled": action["enabled"],
                    "type": action["type"],
                    "callback": action["callback"],
                    "label": action["label"],
                    "tooltip": action["tooltip"],
                    "message": action["message"],
                }
            else:
                state["callbacks"][name] = {"enabled": False}

    def validate(self, settings, item):
        self.parent.logger.info(
            "start ps proxy validation {}".format("-" * 80)
        )
        state = {"errors_found": 0, "errors": {}, "callbacks": {}}

        # ------------------------------------------------------------------------------

        # Get templates and file types from item
        templates_and_file_types = item.properties.get("templates_and_file_types", None)

        if not templates_and_file_types:
            templates_and_file_types_errors = [
                "Couldn't get templates and publish file types from item"
            ]
            self.composer_set(
                name="Templates and file types error",
                list_of_errors=templates_and_file_types_errors,
                state=state,
                action={"enabled": False},
            )

        state_result = self.validate_actions(state)

        if state_result:
            there_is_error = (
                "Validation checks have not been passed, "
                + "found {0} problems".format(state_result)
            )

            raise Exception(there_is_error)

        else:
            self.logger.debug("All checks passed!")

        return True

    def delete_if_exists(self, path):
        if os.path.exists(path):
            os.remove(path)

    def register_publish(
        self,
        comment,
        path,
        name,
        version_number,
        # thumbnail,
        published_file_type,
        tk,
        context,
        task,
        item,
        media_resolution,
        dependencies=[],
    ):

        self.parent.logger.debug("ps proxy publish tk: {}".format(tk))
        self.parent.logger.debug("ps proxy publish context: {}".format(context))
        self.parent.logger.debug("ps proxy publish task: {}".format(task))


        if media_resolution:
            # Add resolution to publish publish_fields
            publish_fields = item.properties.get("publish_fields", {})
            publish_fields.update({"sg_media_resolution": media_resolution})
            item.properties["publish_fields"] = publish_fields
            self.parent.logger.info(
                "media resolution from Photoshop: {}".format(media_resolution)
            )

        # Register the publish:
        publish_data = {
            "tk": tk,
            "context": context,
            "comment": comment,
            "path": path,
            "name": name,
            "version_number": version_number,
            # "thumbnail_path": thumbnail,
            "task": task,
            "published_file_type": published_file_type,
            "dependency_paths": dependencies,
            "sg_fields": publish_fields,
        }
        sg_publish = sgtk.util.register_publish(**publish_data)

        self.parent.tank.shotgun.update(
            "PublishedFile", sg_publish["id"], {"sg_status_list": "ip"}
        )

        return sg_publish

    def save_ps_file_copy(self, photoshop, ps_document, ps_image_temp_path):
        tmp_photoshop_file = photoshop.File(ps_image_temp_path)

        # photoshop save as a copy
        self.parent.logger.info(
            "Saving copy to tmp location: {}".format(ps_image_temp_path)
        )
        self.parent.engine.save_to_path(ps_document, ps_image_temp_path)

        return tmp_photoshop_file

    def resize_psd_document(self, photoshop, file_obj, resize, output_path):
        # ensure publish path exists and copy png to publish path
        if not os.path.exists(os.path.dirname(output_path)):
            os.makedirs(os.path.dirname(output_path))

        # Open the document
        doc = photoshop.app.open(file_obj)

        # Calculate the new dimensions based on the provided resize value
        size_multiplier = resize / 100.0
        self.parent.logger.info("psd proxy size_multiplier: {}".format(size_multiplier))

        new_width = int(doc.width.value * size_multiplier)
        new_height = int(doc.height.value * size_multiplier)
        self.parent.logger.info("new_width: {}".format(new_width))
        self.parent.logger.info("new_height: {}".format(new_height))

        media_resolution = "{}x{}".format(new_width, new_height)
        self.parent.logger.info("media_resolution: {}".format(media_resolution))

        # Resize the document while maintaining its aspect ratio
        doc.resizeImage(new_width, new_height)

        # Save the resulting document to the output path
        save_file = photoshop.File(output_path)
        # doc.saveAs(save_file)
        self.parent.engine.save_to_path(doc, output_path)

        # Close the document
        doc.close(photoshop.SaveOptions.DONOTSAVECHANGES)

        return media_resolution

    def publish_photoshop_proxy(self, item_ps_proxy, scene_data, settings):
        self.parent.logger.info("publish_photoshop_proxy start {}".ljust(120, "-"))

        engine = self.parent.engine
        context = engine.context

        photoshop = engine.adobe
        PS_document = scene_data.get("PS_document")
        # PS_document_path = scene_data.get("PS_document")

        main_document_path = (
            scene_data.get("PS_document_path")
            or item_ps_proxy.properties["templates_and_file_types"]["PS_document_path"].value
            or PS_document.fullName.fsName
        )
        self.parent.logger.info(
            "main_document_path: {}".format(main_document_path)
        )
        _, extension = os.path.splitext(main_document_path)
        # remove . from extension
        ps_proxy_image_format = extension.strip(".")

        fields = scene_data["fields"]
        fields["photoshop_extension"] = ps_proxy_image_format

        publish_photoshop_proxy_template = scene_data["publish_template"]
        ps_proxy_publish_path = publish_photoshop_proxy_template.apply_fields(fields)
        image_publish_basename = os.path.basename(ps_proxy_publish_path)

        ps_proxy_image_temp_path = os.sep.join(
            (scene_data["temp_folder"], image_publish_basename)
        )

        self.parent.logger.info(
            "ps_proxy_publish_path: {}".format(ps_proxy_publish_path)
        )
        self.parent.logger.info(
            "ps_proxy_image_temp_path: {}".format(ps_proxy_image_temp_path)
        )


        # delete file if exists previous created images
        self.delete_if_exists(ps_proxy_image_temp_path)

        # ---------------------------------------------------------------------------
        # save temporary photoshop file in a temp location
        tmp_photoshop_file_obj = self.save_ps_file_copy(
            photoshop, PS_document, ps_proxy_image_temp_path
        )

        # check if newly created temporary copy exists on disk
        if not os.path.exists(ps_proxy_image_temp_path) or not tmp_photoshop_file_obj:
            self.delete_if_exists(ps_proxy_image_temp_path)
            raise Exception(
                "Error saving Photoshop file copy: {}".format(ps_proxy_image_temp_path)
            )

        # get photoshop_proxy_data from item_ps_proxy
        photoshop_proxy_data = item_ps_proxy.properties.get("photoshop_proxy_data", {})
        resize_percentage = photoshop_proxy_data.get("resize_percentage", None)

        self.parent.logger.info("psd proxy resize_percentage: {}".format(resize_percentage))

        if not tmp_photoshop_file_obj or not resize_percentage:
            raise Exception(
                (
                    "Couldn't resize document.\n"
                    "tmp_photoshop_file_obj: {}\n"
                    "resize_percentage: {}"
                ).format(tmp_photoshop_file_obj, resize_percentage)
            )


        # open temporary ps document, resize it and save it in the publish location
        media_resolution = self.resize_psd_document(
            photoshop, tmp_photoshop_file_obj, resize_percentage, ps_proxy_publish_path
        )

        # reopen the original ps document
        orig_photoshop_file = photoshop.File(main_document_path)
        reopened_document = photoshop.app.open(orig_photoshop_file)

        # check if newly created copy exists on disk
        if not os.path.exists(ps_proxy_publish_path):
            raise Exception(
                "Error saving proxy Photoshop file to publish path: {}".format(
                    ps_proxy_publish_path
                )
            )

        # publish file -----------------------------------------------------------------

        # Get publish name
        publish_name = self.parent.util.get_publish_name(
            ps_proxy_publish_path, sequence=False
        )

        # Create publish data dict for registering the publish
        publish_data = {
            "comment": item_ps_proxy.description,
            "path": ps_proxy_publish_path,
            "name": publish_name,
            "version_number": scene_data["publish_version_number"],
            # "thumbnail": ps_proxy_publish_path,
            "published_file_type": scene_data.get("publish_type"),
            "tk": scene_data.get("tk"),
            "context": scene_data.get("context"),
            "task": scene_data.get("task"),
            "item": item_ps_proxy,
            "media_resolution": media_resolution,
            "dependencies": [scene_data["primary_publish_path"]],
        }

        sg_publish = self.register_publish(**publish_data)
        # layer_group_items.update({image_publish_path: layer_index})

        # removing temp ps file
        self.delete_if_exists(ps_proxy_image_temp_path)

        self.parent.logger.info("publish_photoshop_proxy ended {}".format("-" * 80))

        return sg_publish, ps_proxy_publish_path

    def publish(self, settings, item):

        root_item = self.get_root_item(item)
        root_item.properties.setdefault("sg_publish_extra_data", [])
        sg_publish_extra_data = root_item.properties["sg_publish_extra_data"]

        # get templates, file types and layer group data from item (from collector)
        templates_and_file_types = item.properties.get("templates_and_file_types", None)

        if not templates_and_file_types:
            raise Exception("Couldn't get templates from item.")

        # Get PS document data from templates_and_file_types dict (gathered from collector)
        PS_document = templates_and_file_types.get("PS_document")
        PS_document_path = templates_and_file_types.get("PS_document_path")

        primary_publish_template = templates_and_file_types.get(
            "primary_publish_template"
        )
        work_template = templates_and_file_types.get("primary_work_template")
        proxy_image_publish_template = templates_and_file_types.get(
            "publish_photoshop_proxy_template"
        )

        # get template fields and apply them to the relevant templates
        fields = work_template.get_fields(PS_document_path)
        primary_publish_path = primary_publish_template.apply_fields(fields)

        curent_scene_data = {
            "PS_document": PS_document,
            "PS_document_path": PS_document_path,
            "temp_folder": tempfile.gettempdir(),
            "primary_publish_template": primary_publish_template,
            "primary_publish_path": primary_publish_path,
            "work_template": work_template,
            "publish_type": templates_and_file_types.get("ps_proxy_image_file_type"),
            "publish_template": proxy_image_publish_template,
            "fields": fields,
            "publish_version_number": fields["version"],
            "tk": templates_and_file_types.get("tk"),
            "context": templates_and_file_types.get("context"),
            "task": templates_and_file_types.get("task"),
        }

        # Publish ps proxy
        sg_publish, ps_proxy_publish_path = self.publish_photoshop_proxy(
            item, curent_scene_data, settings
        )

        # if sg_publish, added to the main root item extra data (to add this publish)
        # as downstream dependency
        if sg_publish:
            sg_publish_extra_data.extend([sg_publish])

        # self.parent.logger.info(
        #     "layer_groups sg_publish_extra_data: {}".format(pf(sg_publish_extra_data))
        # )

        PS_document = self.parent.engine.adobe.get_active_document()

        for child in root_item.children:
            ps_doc_basename = os.path.basename(PS_document_path)
            if child.name == ps_doc_basename:
                child.properties["document"] = PS_document


        # add publish path to the root item for later use. At least it is used to set
        # set this publish as dependency of the media review publish
        root_item.properties["ps_proxy_publish_path"] = ps_proxy_publish_path

        # Update task status for revision
        sg_task = self.parent.context.task
        self.parent.logger.info(
            "End of Publish and updating task with id: {} to status: 'rev'".format(
                str(sg_task["id"])
            )
        )

        try:
            self.parent.engine.shotgun.update(
                "Task", sg_task["id"], {"sg_status_list": "rev"}
            )
        except:
            pass

    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        pass

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root
