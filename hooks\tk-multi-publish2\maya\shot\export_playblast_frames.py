import os
import time
import pprint
import shutil
import fileseq
import traceback

import sgtk
import maya.cmds as cmds
import maya.mel as mel
import pymel.core as pm


# --------------------------------------------------------------------------------------
HookBaseClass = sgtk.get_hook_baseclass()

pp = pprint.pformat
pf = pprint.pformat

# ======================================================================================

class PlayblastExporter(HookBaseClass):
    def __init__(self, parent):
        super(PlayblastExporter, self).__init__(parent)

    @property
    def item_filters(self):
        """
        List of item types that this plugin is interested in.

        Only items matching entries in this list will be presented to the
        accept() method. Strings can contain glob patters such as *, for example
        ["harmony.*", "file.harmony"]
        """
        return ["maya.session.shot.review"]

    def validate(self, settings, item):
        """
        Validates that the playblast export process has been successfully completed.

        This method attempts to export playblast frames for the current shot maya scene
        and logs the result. If the playblast frames are successfully exported,
        it logs a success message and returns True. Otherwise, it logs a failure
        message and returns False.

        :param item: The SG Publisher item to validate
        :returns: True if the playblast frames were successfully exported, False otherwise.
        """

        exported_frames = self.export_playblast_frames(item)
        if exported_frames:
            msg = "Successfully exported playblast frames"
            self.parent.logger.info(msg)
            self.parent.engine.logger.info(msg)
            # return True
        else:
            msg = "Failed to export playblast frames"
            self.parent.logger.info(msg)
            self.parent.engine.logger.info(msg)
            raise Exception(msg)
            # return False

        return super(PlayblastExporter, self).validate(settings, item)

    # ----------------------------------------------------------------------------------

    def fix_path(self, path):
        """
        Replace all backslashes with forward slashes and all double backslashes
        with a single forward slash in the given path.

        :param str path: The path to fix
        :return str: The fixed path
        """
        path = path.replace('\\', '/')
        path = path.replace('\\\\', '/')

        return path

    def get_shot_info_from_SG(self):
        """
        Retrieves shot information from Shotgrid for the current context entity.

        This method queries Shotgrid for information related to the shot in the
        current context. It uses the entity ID and project from the current context
        to filter the shot data and retrieves specified fields such as cut in/out
        frames, animation exposition, shot code, and project frames per second (fps).

        :returns: A dictionary containing the shot information with keys corresponding
                to the specified fields, or None if no matching shot is found.
        """

        filters = [
            ['id', 'is', self.parent.context.entity['id']],
            ["project", "is", self.parent.context.project],
        ]

        fields = [
            "sg_cut_in",
            "sg_cut_out",
            "sg_animation_exposition",
            "code",
            "project.Project.sg_fps"
        ]

        shot_info = self.parent.shotgun.find_one("Shot", filters, fields)

        return shot_info

    def get_project_resolution_from_sg(self):
        """
        Retrieves the project resolution from Shotgrid using the common hook
        get_entity_resolution.py

        :returns: The project resolution as a (width, height) tuple
        """

        hook_expression = "{config}/get_entity_resolution.py"
        project_resolution = self.parent.engine.execute_hook_expression(
            hook_expression,
            "get_resolution",
            engine=self.parent.engine,
        )
        if project_resolution:
            resolution_width = int(project_resolution.split("x")[0])
            resolution_height = int(project_resolution.split("x")[1])
        else:
            # Fallback to HD resolution
            resolution_width = 1920
            resolution_height = 1080

        return resolution_width, resolution_height

    def get_scene_camera(self, shot_name):
        """
        Finds the camera node for a given shot name, prioritizing
        referenced cameras.

        Args:
            shot_name (str): The name of the shot (used to match the
                             reference namespace).

        Returns:
            pm.nodetypes.Camera: The matching camera node, or None if no
                                 camera is found.

        Raises:
            Exception: If multiple cameras match the criteria.
                       The exception includes the full traceback.
        """
        if not shot_name:
            return None

        matching_cameras = []
        rigged_camera = None

        for cam_shape in pm.rendering.listCameras(perspective=True):
            if not pm.PyNode(cam_shape).isReferenced():
                continue

            cam_transform = pm.PyNode(cam_shape).getParent()
            if cam_transform.namespace()[:-1] == shot_name:
                matching_cameras.append(cam_transform)

        # Return if only one camera matches
        if len(matching_cameras) == 1:
            return matching_cameras[0]

        # Exit loop if "rigged" camera found
        elif len(matching_cameras) > 1:
            for cam in matching_cameras:
                if "rigged" in cam.name():
                    rigged_camera = cam
                    break

            # Return the rigged camera
            if rigged_camera:
                return rigged_camera
            else:
                msg = (
                    f"Multiple cameras found for shot '{shot_name}', "
                    f"matching cameras: {matching_cameras}"
                )
                raise Exception(msg)
        else:
            return None

    def get_capture_options(self, mayacapture):
        # camera options
        cam_opts = mayacapture.CameraOptions.copy()
        cam_opts['displayResolution'] = True

        # set capture custom viewport settings
        view_opts = mayacapture.ViewportOptions.copy()
        view_opts['grid'] = False
        view_opts['polymeshes'] = True
        view_opts['displayAppearance'] = "smoothShaded"
        view_opts['nurbsCurves'] = False
        view_opts['locators'] = False
        view_opts['joints'] = False
        view_opts['pluginShapes'] = True
        view_opts['pluginObjects'] = ("gpuCacheDisplayFilter", True)
        view_opts['ikHandles'] = False
        view_opts['textures'] = True

        # Enable viewport2 AmbientOclusion
        view2_opts = mayacapture.Viewport2Options.copy()
        view2_opts['ssaoEnable'] = True
        view2_opts['maxHardwareLights'] = 8
        view2_opts['textureMaxResolution'] = 2048
        view2_opts['enableTextureMaxRes'] = False
        view2_opts["singleSidedLighting"] = False
        view2_opts["multiSampleEnable"] = False
        view2_opts["transparentShadow"] = True

        return cam_opts, view_opts, view2_opts

    def get_fields_from_scene_path(self, scene_path, logger):
        """
        Retrieves the fields from the scene path using either the
        maya_shot_work or maya_sequence_work templates.

        :param str scene_path: The path to the scene file

        :param logger: The logger instance to use

        :returns: The fields from the scene path, or None if the fields
            could not be retrieved
        """
        engine = self.parent.engine

        shot_work_template = engine.get_template_by_name("maya_shot_work")
        sequence_work_template = engine.get_template_by_name("maya_sequence_work")
        fields = (
            shot_work_template.validate_and_get_fields(scene_path)
            or sequence_work_template.validate_and_get_fields(scene_path)
        )

        if not fields:
            logger.error(
                "Unable to get fields from scene path: {}".format(scene_path)
            )
            return None

        logger.info("fields_from_scene_path: {}\n{}".format(scene_path, fields))

        return fields

    def get_current_scene_version_number (self, scene_path, logger):
        """
        Returns the current version number from the scene path.

        :param str scene_path: The scene path to get the version from
        :param logger: The logger to use for logging messages

        :return: The current version number as a string, for example "v001"
        """
        if not scene_path:
            logger.error("Invalid scene_path provided: {}".format(scene_path))
            return None

        fields = self.get_fields_from_scene_path(scene_path, logger)
        if not fields or not fields.get("version"):
            logger.error("Unable to determine version from scene path")
            return None

        version = "v{0}".format(str(fields.get('version', 1)).zfill(3))

        return version

    def get_playblast_basename(self, scene_path, shot_name, logger):
        """
        Returns the playblast basename for the given scene path and shot name.

        The playblast basename is built from the shot name, task name, name id, and version
        number from the scene path.

        :param str scene_path: The path to the scene file
        :param str shot_name: The name of the shot to get the playblast for
        :param logger: The logger to use for logging messages

        :return: The playblast basename as a string, or False if the basename could not be built
        """
        engine = self.parent.engine
        # get playblaset filename (basename, without sequence token or extension) ------
        fields = self.get_fields_from_scene_path(scene_path, logger)
        if not fields:
            logger.error("Unable to get fields from current scene path")
            return False

        # get the playblast basename elements
        task_name = fields.get("Task") or engine.context.task["name"]
        version = fields.get("version")
        name_id = fields.get("name")

        if not task_name or not name_id or not version:
            logger.error(
                (
                    "Missing fields for building playblast basename:\n"
                    "shot_name: {0}\n"
                    "task_name: {1}\n"
                    "name_id: {2}\n"
                    "version: {3}"
                ).format(shot_name, task_name, name_id, version)
            )
            return False

        basename = "{0}_{1}_{2}_v{3:03}".format(shot_name, task_name, name_id, version)

        return basename

    def get_playblast_path(self, scene_path, shot_name, logger):
        """
        Returns the path to the playblast folder for the given scene path and shot name.

        The playblast path is built from the scene path, version number, and shot name. The
        playblast path is created if it does not already exist.

        :param str scene_path: The path to the scene file
        :param str shot_name: The name of the shot to get the playblast for
        :param logger: The logger to use for logging messages

        :return: The path to the playblast folder as a string, or None if the path could not be built
        """
        if not scene_path:
            logger.error("Invalid scene_path provided: {}".format(scene_path))
            return None

        version = self.get_current_scene_version_number(scene_path, logger)
        if not version:
            logger.error("Unable to determine version from scene path")
            return None

        playblast_root_path = os.path.join(
            os.path.dirname(scene_path),
            'temp',
            version
        )

        basename = self.get_playblast_basename(scene_path, shot_name, logger)
        if not basename:
            logger.error("Unable to determine basename from scene path")
            return None

        playblast_path = os.path.join(playblast_root_path, basename)
        playblast_path = self.fix_path(playblast_path)

        # make sure the target directory exists
        if not os.path.exists(os.path.dirname(playblast_path)):
            os.makedirs(os.path.dirname(playblast_path))

        return playblast_path

    def export_playblast_frames(self, item):
        """
        Exports playblast frames for the specified SG Publisher item.

        This method generates playblast frames for the current Maya scene's
        shot and logs the result. If the playblast frames are successfully
        exported, it logs a success message and updates the item's properties
        with playblast-related data.

        :param item: The SG Publisher item for which to export playblast frames.
        :return: True if the playblast frames were successfully exported,
                False otherwise.
        """

        engine = self.parent.engine
        logger = engine.logger

        logger.info("Exporting playblast frames... ".ljust(120, "-"))

        # Get scene path ---------------------------------------------------------------
        scene_path = cmds.file(query=True, sn=True)
        if not scene_path:
            msg = "Unable to determine current scene path"
            logger.error(msg)
            self.parent.logger.error(msg)
            raise Exception(msg)
            # return False

        # Load frameworks --------------------------------------------------------------
        mayacapture = self.load_framework("mty-framework-mayacapture")

        # Store current scene options --------------------------------------------------
        # Save the current value of cached playback pref
        cache_opt_orig_value = cmds.optionVar(query="cachedPlaybackEnable")

        # Disable cached playback in case it is enabled
        if cache_opt_orig_value == 1:
            cmds.optionVar(intValue=("cachedPlaybackEnable", 0))

        # Clear selection
        cmds.select(clear=True)

        # Get project and shot data from SG --------------------------------------------
        engine.show_busy("Please wait...", "Collecting project and shot data...")
        res_width, res_height = self.get_project_resolution_from_sg()
        logger.info(
            "project resolution: {0}, {1}".format(res_width, res_height)
        )

        shot_data = self.get_shot_info_from_SG()
        if not shot_data:
            msg = "Unable to get shot data from Shotgun"
            logger.error(msg)
            self.parent.logger.error(msg)
            raise Exception(msg)
            # return False

        shot_name = shot_data.get("code")
        shot_start_frame = shot_data.get("sg_cut_in")
        shot_end_frame = shot_data.get("sg_cut_out")
        list_of_frames = []
        for i in range(int(shot_start_frame), int(shot_end_frame) + 1):
            list_of_frames.append(i)
        engine.clear_busy()

        # Build temp playblast root path -----------------------------------------------
        playblast_path = self.get_playblast_path(scene_path, shot_name, logger)
        if not playblast_path:
            msg = "Unable to determine playblast path"
            logger.error(msg)
            self.parent.logger.error(msg)
            raise Exception(msg)
            # return False

        playblast_img_seq_fullpath = "{}.%04d.jpg".format(playblast_path)
        playblast_img_seq_fullpath = self.fix_path(playblast_img_seq_fullpath)
        logger.info("playblast_path filename: {}".format(playblast_path))
        logger.info("playblast full path: {}".format(playblast_img_seq_fullpath))

        # check if the playblast is already exported and if is not older than 5 minutes
        # check if a sequence playblast_img_seq_fullpath already exists on disk,
        # and if it exists, check that is not older than 5 minutes. If it's older,
        # delete it and re-export it. the playblast_img_seq_fullpath contains a %04d
        # token in its name, so a simple os.path.exists won't work. Probably it's a
        # better idea to use filesequence.findSequenceOnDisk to also be able to check
        # that the exported range (number of files in the sequence) matches the
        # expected tange (shot_end_frame - shot_start_frame + 1)

        # check if the playblast is already exported and if is not older than 5 minutes
        try:
            existing_image_seq = fileseq.findSequenceOnDisk(playblast_img_seq_fullpath)
        except:
            existing_image_seq = None


        if existing_image_seq:
            # check if the existing sequence matches the expected range
            correct_range = False

            existing_range = existing_image_seq.end() - existing_image_seq.start() + 1
            expected_range = shot_end_frame - shot_start_frame + 1

            if existing_range == expected_range:
                correct_range = True

            # if the ranges match, then we check if the existing sequence is not
            # older than 5 minutes
            if correct_range:
                seq_dates = [
                    os.path.getmtime(existing_image_seq[i])
                    for i in range(len(existing_image_seq))
                ]
                if max(seq_dates) > (time.time() - 5 * 60):
                    logger.info(
                        (
                            "Playblast already exported and not older than 5 minutes: "
                            "{}"
                        ).format(playblast_img_seq_fullpath)
                    )
                    # if it's not older than 5 minutes, then we don't need to export the
                    # playblast again, BUT we need to add the relevant properties to
                    # the item
                    # add properties to the parent item and the item itself ------------

                    item.properties["scene_path"] = scene_path
                    # "sequence" MUST BE a fileseq object
                    item.properties["sequence"] = existing_image_seq
                    # "sequence_text_id" is the render layer name, in our case, "default"
                    item.properties["sequence_text_id"] = "default"
                    # "sequence_text_id2" is the aov name, in our case, "playblast"
                    item.properties["sequence_text2_id"] = "playblast"

                    return True
                else:
                    logger.info(
                        (
                            "Playblast already exported but older than 5 minutes. "
                            "Deleting and re-exporting: {}"
                        ).format(playblast_img_seq_fullpath)
                    )
                    try:
                        shutil.rmtree(os.path.dirname(playblast_img_seq_fullpath))
                    except:
                        logger.error(
                            "Unable to delete playblast folder: {}".format(
                                os.path.dirname(playblast_img_seq_fullpath)
                            )
                        )

        # get scene camera -------------------------------------------------------------
        camera = self.get_scene_camera(shot_name)
        if not camera:
            msg = "Unable to get camera for shot: {}".format(shot_name)
            logger.error(msg)
            self.parent.logger.error(msg)
            raise Exception(msg)
            # return False

        # convert camera to string if it's a pymel object
        if (
            isinstance(camera, pm.nodetypes.Transform)
            or isinstance(camera, pm.nodetypes.Camera)
        ):
            camera = camera.name()

        # Create capture options -------------------------------------------------------
        cam_opts, view_opts, view2_opts = self.get_capture_options(mayacapture)

        # TODO: Implement custom render settings and add them to the item when collecting it
        enable_two_sides = True
        if "two_side_lightning" in item.properties.get("custom_render_settings", {}).get("others", {}):
            enable_two_sides = item.properties.get("custom_render_settings", {}).get("others", {}).get("two_side_lightning", True)

        # build playblast config dictionary, which will be used to create the
        # playblasts.
        playblast_config = {
            'camera': camera,
            'width': res_width,
            'height': res_height,
            'filename': playblast_path,
            'frames': list_of_frames,
            'format': "image",
            'compression': "jpg",
            'viewer': False,
            'overwrite': True,
            'viewport_options': view_opts,
            'viewport2_options': view2_opts,
            'camera_options': cam_opts,
            'two_sides': enable_two_sides,
            "off_screen": True,
        }

        # Create playblast ---------------------------------------------------------
        logger.info(
            "About to create playblast for shot: {}".format(shot_name)
        )
        engine.show_busy(
            "Please wait...", "Creating playblast for shot {}".format(shot_name)
        )
        try:
            mayacapture.capture(**playblast_config)
            logger.info("Successfully created playblast for shot: {}".format(shot_name))

            # add properties to the parent item and the item itself --------------------
            fileseq_obj = fileseq.findSequenceOnDisk(playblast_img_seq_fullpath)

            item.properties["scene_path"] = scene_path
            # "sequence" MUST BE a fileseq object
            item.properties["sequence"] = fileseq_obj
            # "sequence_text_id" is the render layer name, in our case, "default"
            item.properties["sequence_text_id"] = "default"
            # "sequence_text_id2" is the aov name, in our case, "playblast"
            item.properties["sequence_text2_id"] = "playblast"

            item.properties["playblast_dir"] = playblast_path
            item.properties["playblast_frames"] = playblast_img_seq_fullpath
            engine.clear_busy()
            return True
        except Exception as e:
            msg = (
                "Failed to create playblast for shot: {}, error: {}, "
                "full traceback:\n{}"
            ).format(shot_name, e, traceback.format_exc())
            logger.error(msg)
            self.parent.logger.error(msg)
            engine.clear_busy()
            raise Exception(msg)
            # return False
