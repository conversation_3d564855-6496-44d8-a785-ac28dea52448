#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

import os
import pprint
import traceback

import maya.cmds as cmds
import pymel.core as pm
import maya.mel as mel
import sgtk
from sgtk.util.filesystem import ensure_folder_exists

import sgtk
from sgtk.util.filesystem import ensure_folder_exists
from tank_vendor import six

pp = pprint.pprint
pf = pprint.pformat
HookBaseClass = sgtk.get_hook_baseclass()


class MayaSessionPublishSessionUpdate(HookBaseClass):
    """
    Plugin for publishing an open maya session.

    This hook relies on functionality found in the base file publisher hook in
    the publish2 app and should inherit from it in the configuration. The hook
    setting for this plugin should look something like this::

        hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py"

    """

    def __init__(self, parent):
        super(MayaSessionPublishSessionUpdate, self).__init__(parent)

        context = self.parent.context
        entity = "Asset"
        filters = [["id", "is", context.entity["id"]]]
        fields = ["sg_asset_type"]
        asset_data = \
            self.parent.shotgun.find_one(entity, filters, fields)

        self.ASSET = asset_data

        try:
            command = \
                "hyperShadePanelMenuCommand" + \
                "(\"hyperShadePanel1\",\"deleteUnusedNodes\")"

            mel.eval(command)
        except:
            print("Unable to delete unused nodes.")

    @property
    def settings(self):

        # inherit the settings from the base publish plugin
        plugin_settings = super(
            MayaSessionPublishSessionUpdate, self).settings or {}

        # settings specific to this class
        maya_publish_settings = {
            "Enforced Publish Plugins": {
                "type": dict,
                "default": {},
                "description": (
                    "{ 'plugin_name': <plugin name>, "
                    "'pipeline_steps': {<step_name> : [<task_list] }   "
                    "Describes a key - value pair for enforcements "
                    "task that must be executed along with the primary publish"
                )
            }
        }

        # update the base settings
        plugin_settings.update(maya_publish_settings)

        return plugin_settings

    def get_enforced_item(self, enforced_name, item):

        for each in item.parent.descendants:
            print(each)

        enforced_item = [i for i in item.parent.descendants if
                         i.name == enforced_name]


        # print('item parent descendants: {0}'.format(
        #     pf(item.parent.descendants)))
        if enforced_item:
            return enforced_item[0]

        return None

    def has_breakdowns_registered(self):

        filters = [
            ['sg_shot', 'is', self.parent.engine.context.entity],
            [
                'sg_asset.Asset.sg_asset_type', 'not_in',
                ['EnvLocation', "Camera", "EnvModule"]
            ]
        ]
        list_of_breakdowns = self.parent.engine.shotgun.find(
            "CustomEntity30",
            filters, ['code']
        )

        print("item breakdowns: {0}".format(pf(list_of_breakdowns)))

        if list_of_breakdowns:
            return True

        return False

    def validate_enforcements(self, plugin_list, item):
        """
        Validate enforcements plugins to be executed along
        the main publish on certain Step and task name.
        """

        current_step = self.parent.engine.context.step.get("name")
        current_task = self.parent.engine.context.task.get("name")

        plugins_missing = []

        print("\n" + (">" * 120))

        for plugin_name in plugin_list:
            plugin_pipeline_step = plugin_list[plugin_name]
            list_of_tasks = plugin_pipeline_step.get(current_step) or []
            do_enforce_for_current_task = current_task in list_of_tasks

            if do_enforce_for_current_task:

                enforced_item = self.get_enforced_item(plugin_name, item)

                plugin_checked_status = (
                        not enforced_item.properties
                        .get('accepted') or not enforced_item.checked
                )

                print("enforced_item: {0}".format(pf(enforced_item)))
                print("plugin checked status: {0}".format(
                    pf(plugin_checked_status)))

                if enforced_item and plugin_checked_status:
                    pp(plugin_name)
                    if (
                            plugin_name != "Shot Geometry Assets" and
                            self.has_breakdowns_registered()
                    ):
                        plugins_missing.append(plugin_name)

        return plugins_missing

    # ________________________________________________________________

    def validate(self, settings, item):

        enforced_plugins = settings.get("Enforced Publish Plugins").value

        pp('enforced plugins: {0}'.format(pf(enforced_plugins)))

        if enforced_plugins:
            missing_validate_enforcements = (
                self.validate_enforcements(enforced_plugins, item)
            )

            pp('missing validate enforcements: {0}'.format(
                pf(missing_validate_enforcements)))

            if missing_validate_enforcements:
                raise Exception(
                    "Plugins items must be present and checked " +
                    "to publish scene: " +
                    "{0}".format(missing_validate_enforcements)
                )

        # Allowed software versions for the current project. It uses SG's software
        # entities
        restricted_maya_versions = self.parent.execute_hook_expression(
            "{config}/tk-multi-launchapp/before_app_launch.py",
            "get_project_versions",
            engine_name=self.parent.engine.name,
        )

        if restricted_maya_versions:
            self.parent.logger.info(
                "Session validation, Maya versions allowed: {}".format(
                    restricted_maya_versions
                )
            )
        else:
            restricted_maya_versions = []

        # Check for the maya version
        full_current_version = pm.about(installedVersion=True).split(' ')
        current_version = full_current_version[-1]
        self.parent.logger.info(
            "Current full Maya version: {}".format(full_current_version)
        )
        self.parent.logger.info("Current Maya version: {}".format(current_version))

        if not current_version in restricted_maya_versions:
            raise Exception(
                (
                    "Current Maya version is "
                    "{}, the allowed maya versions for this project are: {}"
                ).format(current_version, restricted_maya_versions)
            )
        # self.logger.debug("pre validatio f")
        #           _  _  _  _  _  _  _  _  _  _  _
        previous = super(MayaSessionPublishSessionUpdate, self).validate(settings, item)
        # self.logger.debug("pos validatio f")
        return previous
        #            _  _  _  _  _  _  _  _  _  _  _

    # ________________________________________________________________

    def paths_referenced(self):

        list_of_file_nodes = pm.ls(type="file")
        paths = []
        list_of_meshes = pm.ls(type="mesh")
        mesh_list = []

        for _path in list_of_file_nodes:
            if 'placeholder' in _path.fileTextureName.get():
                list_of_file_nodes.remove(_path)

        for mesh in list_of_meshes:
            nodo = pm.listRelatives(mesh, allParents=True)

            if (
                    not nodo[0] in mesh_list
                    and not "Orig" in mesh
                    and pm.objectType(nodo[0]) == "transform"
            ):
                mesh_list.append(nodo[0])
                try:
                    thispath = pm.referenceQuery(nodo[0], filename=True)
                    thispathfull = thispath.split('{')
                    if thispath not in paths and not thispathfull[0] in paths:
                        # This if gotta be  eliminated on a future update when enviroments can be auto ingested
                        if not 'sets' in thispathfull[
                            0].lower() and not 'cache' in thispathfull[
                            0].lower():
                            paths.append(thispathfull[0])
                except:
                    print('*  {0} not a referenced file'.format(nodo[0]))
        for file in list_of_file_nodes:
            try:
                pm.referenceQuery(file, filename=True)
            except:
                pm.select(file, replace=True)
                imageName = pm.getAttr(".fileTextureName")
                imageNameFull = imageName.split('{')
                paths.append(imageNameFull[0])
        tk = self.parent.context.sgtk
        returned = sgtk.util.find_publish(tk, paths)
        problems = []
        for p in paths:
            if not p in returned:
                problems.append(p)
        return problems

    def publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.
        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """

        # get the path in a normalized state. no trailing separator, separators
        # are appropriate for current os, no double separators, etc.
        path = sgtk.util.ShotgunPath.normalize(_session_path())

        # ensure the session is saved
        _save_session(path)

        # update the item with the saved session path
        item.properties["path"] = path

        # add dependencies for the base class to register when publishing
        logger = self.parent.engine.logger
        dependency_paths = _maya_find_additional_session_dependencies(logger)

        for path in dependency_paths:
            self.parent.log_debug(
                'Publish: Resolved dependency paths: %s' % path)

        item.properties["publish_dependencies"] = dependency_paths

        # let the "base class" register the publish
        # we are actually using a copied methid since we can't use super here
        # that will execute the mayas publish which will override the dependencies again
        # an we just implemented this method for that purpose, set custom dependencies
        self.app_publish(settings, item)

    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed, and can for example be used to version up files.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """

        # do the base class finalization
        super(MayaSessionPublishSessionUpdate, self).finalize(settings, item)

        # get the data for the publish that was just created in SG
        publish_data = item.properties.sg_publish_data

        # set corresponding publish dependencies as Hard
        logger = self.parent.engine.logger
        dependency_paths = _maya_find_additional_session_dependencies(logger)

        for path in dependency_paths:
            self.parent.log_debug(
                'Finalize: Resolved dependency paths: %s' % path)

        dependencies = sgtk.util.find_publish(
            self.parent.sgtk, dependency_paths)
        dependencies = [dependencies[path] for path in dependencies]

        self.parent.log_debug(
            "Resolved dependency publishes:\n%s" % pf(dependencies))

        if dependencies:
            filters = [['published_file', 'is', publish_data],
                       ['dependent_published_file', 'in', dependencies]]
            publish_dependencies = self.parent.shotgun.find(
                'PublishedFileDependency', filters)

            batch_data = []
            for publish in publish_dependencies:
                batch_data.append({'request_type': 'update',
                                   'entity_type': 'PublishedFileDependency',
                                   'entity_id': publish['id'],
                                   'data': {'sg_type': 'Hard'}})

            self.parent.shotgun.batch(batch_data)

    # this app_publish method is litteraly a copy of the publish one from the base publish_file hook
    # https://github.com/shotgunsoftware/tk-multi-publish2/blob/master/hooks/publish_file.py

    def app_publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.
        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """

        publisher = self.parent

        # ---- determine the information required to publish

        # We allow the information to be pre-populated by the collector or a
        # base class plugin. They may have more information than is available
        # here such as custom type or template settings.

        publish_type = self.get_publish_type(settings, item)
        publish_name = self.get_publish_name(settings, item)
        publish_version = self.get_publish_version(settings, item)
        publish_path = self.get_publish_path(settings, item)
        publish_dependencies_paths = self.get_publish_dependencies(settings, item)
        publish_dependencies_paths.extend(item.properties["publish_dependencies"])
        publish_user = self.get_publish_user(settings, item)
        publish_fields = self.get_publish_fields(settings, item)
        # catch-all for any extra kwargs that should be passed to register_publish.
        publish_kwargs = self.get_publish_kwargs(settings, item)

        # if the parent item has publish data, get it id to include it in the list of
        # dependencies
        publish_dependencies_ids = []
        if "sg_publish_data" in item.parent.properties:
            publish_dependencies_ids.append(
                item.parent.properties.sg_publish_data["id"]
            )

        # handle copying of work to publish if templates are in play
        self._copy_work_to_publish(settings, item)

        # update the publish_fields dict to change the status of the task
        # and publishes to Pending Review (rev)
        update_status = {
            "sg_status_list": "rev",
            "task.Task.sg_status_list": "rev",
        }
        publish_fields.update(update_status)

        # arguments for publish registration
        self.logger.info("Registering publish...")
        publish_data = {
            "tk": publisher.sgtk,
            "context": item.context,
            "comment": item.description,
            "path": publish_path,
            "name": publish_name,
            "created_by": publish_user,
            "version_number": publish_version,
            "thumbnail_path": item.get_thumbnail_as_path(),
            "published_file_type": publish_type,
            "dependency_paths": list(set(publish_dependencies_paths)),
            "dependency_ids": publish_dependencies_ids,
            "sg_fields": publish_fields,
        }

        validation_tags = item.properties.get('checklist_validations')

        if validation_tags:
            publish_data["sg_checked_validations"] = validation_tags
            publish_data["tags"] = validation_tags


        # add extra kwargs
        publish_data.update(publish_kwargs)

        # log the publish data for debugging
        self.parent.engine.logger.info(
            "publish_data dict before registering:\n{}".format(pf(publish_data))
        )

        self.logger.debug(
            "Populated Publish data...",
            extra={
                "action_show_more_info": {
                    "label": "Publish Data",
                    "tooltip": "Show the complete Publish data dictionary",
                    "text": "<pre>%s</pre>" % (pf(publish_data),),
                }
            },
        )

        # create the publish and stash it in the item properties for other
        # plugins to use.
        item.properties.sg_publish_data = sgtk.util.register_publish(**publish_data)

        validation_tags = item.properties.get('checklist_validations')
        # update tags
        if validation_tags:
            self.parent.shotgun.update("PublishedFile",
                                       item.properties.sg_publish_data['id'],
                                       {'tags': validation_tags})

        # Try to update the current task
        try:
            sg_task = self.parent.context.task
            update_status = self.parent.engine.shotgun.update(
                "Task", sg_task["id"], {"sg_status_list": "rev"}
            )
            self.parent.engine.logger.info(
                "successfully updated Task '{}' status:\n{}.".format(
                    sg_task["name"], pf(update_status)
                )
            )
        except Exception as e:
            self.logger.error("Error updating Task status: {}".format(e))
            self.parent.engine.logger.error(
                "Error updating Task '{}' status: {}. Full traceback: {}".format(
                    sg_task["name"], e, traceback.format_exc()
                )
            )

        self.logger.info("Publish registered!")
        self.logger.debug(
            "Shotgun Publish data...",
            extra={
                "action_show_more_info": {
                    "label": "Shotgun Publish Data",
                    "tooltip": "Show the complete Shotgun Publish Entity dictionary",
                    "text": "<pre>%s</pre>"
                    % (pf(item.properties.sg_publish_data),),
                }
            },
        )


# Sadly the original maya hook doesn't implement the following methods inside the class
# so, we are unable to use the dynamic heritance, and then we need to duplicate the methods
# somethiogn to check later with Shotgun support

def _maya_find_additional_session_dependencies(logger):
    """
    Find additional dependencies from the session
    """

    # default implementation looks for references and
    # textures (file nodes) and returns any paths that
    # match a template defined in the configuration
    ref_paths = set()

    # first let's look at maya references
    logger.info("Looking for referenced dependencies...")
    ref_nodes = cmds.ls(references=True)
    for ref_node in ref_nodes:
        try:
            # get the path:
            ref_path = cmds.referenceQuery(ref_node, filename=True, withoutCopyNumber=True)
            # make it platform dependent
            # (maya uses C:/style/paths)
            ref_path = _use_platform_path_separator(ref_path)
            logger.info("Found reference: {}".format(ref_path))
            if ref_path:
                ref_paths.add(ref_path)
            else:
                logger.warning("Skipping reference: {}".format(ref_node))
        except:
            import traceback
            err = traceback.format_exc()
            print(err)


    # now look at file texture nodes
    logger.info("Looking for texture dependencies...")
    for file_node in cmds.ls(long=True, type="file"):
        # ensure this is actually part of this session and not referenced
        if cmds.referenceQuery(file_node, isNodeReferenced=True):
            # this is embedded in another reference, so don't include it in
            # the dependencies list
            continue

        # get path and make it platform dependent
        # (maya uses C:/style/paths)
        texture_path = cmds.getAttr("{}.fileTextureName".format(file_node))
        if texture_path:
            texture_path = _use_platform_path_separator(texture_path)
            logger.info("Found texture: {}".format(texture_path))
            if "shapes" in texture_path:
                texture_path = texture_path.replace("_000.","_%03d.")

            if "<UDIM>" in texture_path:
                texture_path = texture_path.replace('<UDIM>', '%04d')

            ref_paths.add(texture_path)
        else:
            logger.warning("Skipping texture: {}".format(texture_path))

    # also look for audio nodes
    logger.info("Looking for audio dependencies...")
    audio_nodes = cmds.ls(long=True, type="audio")
    for audio_node in audio_nodes:
        # ensure this is actually part of this session and not referenced
        if cmds.referenceQuery(audio_node, isNodeReferenced=True):
            # this is embedded in another reference, so don't include it in
            # the dependencies list
            continue

        # get path and make it platform dependent
        # (maya uses C:/style/paths)
        audio_path = cmds.getAttr("{}.filename".format(audio_node))
        if audio_path:
            audio_path = _use_platform_path_separator(audio_path)
            logger.info("Found audio: {}".format(audio_path))

            ref_paths.add(audio_path)
        else:
            logger.warning("Skipping audio: {}".format(audio_node))

    # also look for gpu cache nodes
    logger.info("Looking for gpu cache dependencies...")
    gpu_nodes = cmds.ls(long=True, type="gpuCache")
    for gpu_node in gpu_nodes:
        # ensure this is actually part of this session and not referenced
        if cmds.referenceQuery(gpu_node, isNodeReferenced=True):
            # this is embedded in another reference, so don't include it in
            # the dependencies list
            continue

        # get path and make it platform dependent
        # (maya uses C:/style/paths)
        gpu_cache_path = cmds.getAttr("{}.cacheFileName".format(gpu_node))
        if gpu_cache_path:
            logger.info("Found gpu cache: {}".format(gpu_cache_path))
            gpu_cache_path = _use_platform_path_separator(gpu_cache_path)

            ref_paths.add(gpu_cache_path)
        else:
            logger.warning("Skipping gpu cache: {}".format(gpu_node))

    # also look for imagePlane nodes
    logger.info("Looking for image plane dependencies...")
    image_plane_nodes = cmds.ls(long=True, type="imagePlane")
    for image_plane_node in image_plane_nodes:
        # ensure this is actually part of this session and not referenced
        if cmds.referenceQuery(image_plane_node, isNodeReferenced=True):
            # this is embedded in another reference, so don't include it in
            # the dependencies list
            continue

        # get path and make it platform dependent
        image_plane_path = cmds.getAttr(f"{image_plane_node}.imageName")
        if image_plane_path:
            logger.info("Found image plane: {}".format(image_plane_path))
            image_plane_path = _use_platform_path_separator(image_plane_path)

            ref_paths.add(image_plane_path)
        else:
            logger.warning("Skipping image plane: {}".format(image_plane_node))

    # also look for alembic nodes
    logger.info("Looking for alembic dependencies...")
    alembic_nodes = cmds.ls(long=True, type="AlembicNode")
    for alembic_node in alembic_nodes:
        # ensure this is actually part of this session and not referenced
        if cmds.referenceQuery(alembic_node, isNodeReferenced=True):
            # this is embedded in another reference, so don't include it in
            # the dependencies list
            continue

        # get path and make it platform dependent
        alembic_path = cmds.getAttr(f"{alembic_node}.abc_File")
        if alembic_path:
            alembic_path = _use_platform_path_separator(alembic_path)
            logger.info("Found alembic: {}".format(alembic_path))

            ref_paths.add(alembic_path)
        else:
            logger.warning("Skipping alembic: {}".format(alembic_node))

    # check for extra dependencies, like special renderer nodes
    logger.info("Looking for RS normalmap dependencies...")
    rs_normalmap_nodes = cmds.ls(long=True, type="RedshiftNormalMap")
    for normalmap_node in rs_normalmap_nodes:
        if cmds.attributeQuery("tex0", node=normalmap_node, exists=True):
            normalmap_path = cmds.getAttr("{}.tex0".format(normalmap_node))
            if normalmap_path:
                normalmap_path = _use_platform_path_separator(normalmap_path)
                logger.info("Found normalmap: {}".format(normalmap_path))

                ref_paths.add(normalmap_path)
            else:
                logger.warning("Skipping normalmap: {}".format(normalmap_node))

    logger.info("Looking for RS domelight dependencies...")
    rs_dome_lights = cmds.ls(long=True, type="RedshiftDomeLight")
    for dome_light in rs_dome_lights:
        if cmds.attributeQuery("tex0", node=dome_light, exists=True):
            domelight_texture_path = cmds.getAttr("{}.tex0".format(dome_light))
            if domelight_texture_path:
                domelight_texture_path = _use_platform_path_separator(
                    domelight_texture_path
                )
                logger.info(
                    "Found domelight texture: {}".format(domelight_texture_path)
                )
                ref_paths.add(domelight_texture_path)
            else:
                logger.warning(
                    "Skipping domelight: {}".format(dome_light)
                )

    # special case for dynamic per shot asset textures
    try:
        transforms = cmds.ls(type='transform')
        for node in transforms:
            attributeFlag = "fbShotTextureControl"
            isShotTextureControl = cmds.attributeQuery(attributeFlag, node=node, exists=True)

            if not isShotTextureControl:
                continue

            texture_path = cmds.getAttr("%s.fbCache_Dyn_Text_Path_Id" % node)

            if not texture_path:
                continue

            # As a temporal workaround and to avoid validations but only auto fix it
            # we enforce the corresponding attributes to that node so that it have
            # the corresponding metadata related to the used texture, this to ready
            # for the next processing of alembic export, and yes this need to be improoved
            # this is nastly hardcoded, but it works at the moment
            # TODO: replace current functionality with template based one
            name = os.path.basename(texture_path)
            tokens = name.split('_')
            if len(tokens) == 7:
                epi, seq, sht, dep, asset, section, version_pad_ext = tokens
                version, pad, ext = version_pad_ext.split('.')
                shot_code = "%s_%s_%s" % (epi, seq, sht)
                cmds.setAttr("%s.fbCache_Dyn_Text_Shot_Id" % node, shot_code, type="string")
                cmds.setAttr("%s.fbCache_Dyn_Text_Asset_Id" % node, asset, type="string")
                cmds.setAttr("%s.fbCache_Dyn_Text_Section_Id" % node, section, type="string")
                cmds.setAttr("%s.fbCache_Dyn_Text_Version_Id" % node, version, type="string")

                # Finally, collect the texture path as dependency
                # we expect this texture paths to be pointing to frame 0000
                # and we need to replace that with the from %04d so that
                # we can find the corresponding publish file dependency
                # yes this is nastly hardcoded, but it should work for now
                # TODO: make this validation dynamic to relly either
                # in a filesequence object or in template fields
                texture_path = texture_path.replace(".0000.", ".%04d.")

                texture_path = _use_platform_path_separator(texture_path)
                ref_paths.add(texture_path)
    except:
        traceback.print_exc()


    return list(ref_paths)


def _session_path():
    """
    Return the path to the current session
    :return:
    """
    path = cmds.file(query=True, sn=True)

    if path is not None:
        path = six.ensure_str(path)

    return path


def _save_session(path):
    """
    Save the current session to the supplied path.
    """

    # Maya can choose the wrong file type so we should set it here
    # explicitly based on the extension
    maya_file_type = None
    if path.lower().endswith(".ma"):
        maya_file_type = "mayaAscii"
    elif path.lower().endswith(".mb"):
        maya_file_type = "mayaBinary"

    # Maya won't ensure that the folder is created when saving, so we must make sure it exists
    folder = os.path.dirname(path)
    ensure_folder_exists(folder)

    cmds.file(rename=path)

    # save the scene:
    if maya_file_type:
        cmds.file(save=True, force=True, type=maya_file_type)
    else:
        cmds.file(save=True, force=True)


def _use_platform_path_separator(path):
    """
    Ensure the provided path is using the platform specific path separator
    """
    path = path.replace("/", os.path.sep)
    path = path.replace("\\", os.path.sep)

    return path
