from pprint import pprint, pformat

# -*- coding: utf-8 -*-
# Standard library:
import os
import sys
import sgtk
import pprint
import re
import traceback
import subprocess

# ___   ___   ___   ___   ___   ___  ___
# Third party:
from sgtk.platform.qt import QtGui, QtCore
import maya.cmds as cmds
import maya.mel as mel
import pymel.core as pm

# ___   ___   ___   ___   ___   ___  ___
# Project:
HookBaseClass = sgtk.get_hook_baseclass()
# ====================================================================
pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)
pf = pprint.pformat


class MayaShotAlembicAssetPublishPlugin(HookBaseClass):
    @property
    def icon(self):
        return os.path.join(
            self.disk_location, os.pardir, "icons", "publish_alembic.png"
        )

    @property
    def name(self):
        return "Publish Shot alembics"

    @property
    def description(self):
        return "This plugin will save alembic caches for specific assets."

    @property
    def settings(self):
        # inherit the settings from the base publish plugin
        plugin_settings = super(MayaShotAlembicAssetPublishPlugin, self).settings or {}

        _settings = {
            # 'plugin_hooks': {
            #     'type': 'dict',
            #     'default': None,
            #     'description': ''
            # },
            "workarea_template": {"type": "string", "default": None, "description": ""},
            "publish_template": {"type": "string", "default": None, "description": ""},
            "primary_publish_template": {
                "type": "string",
                "default": None,
                "description": "",
            },
            "publish_type": {"type": "string", "default": None, "description": ""},
            "supported_tasks": {
                "type": "list",
                "default": [],
                "description": "List of supported tasks.",
            },
            "supported_pipeline_steps": {
                "type": "list",
                "default": [],
                "description": "List of supported pipeline steps.",
            },
        }

        # update the base settings
        plugin_settings.update(_settings)

        return plugin_settings

    # def plugin_hook(self, settings, string_key):
    #     _h = settings.get("plugin_hooks")
    #     return self.parent.create_hook_instance(_h.value.get(string_key))

    @property
    def item_filters(self):
        return ["maya.session.shotAlembicLocators"]

    # ---------------------------------------------------------------------------

    def accept(self, settings, item):
        print("\n" + (">" * 120))
        print("{0}.accept".format(self.name))

        result = {"accepted": False, "checked": False}

        # If scene has not been saved, raise an exception as we need the scene name to
        # resolve the workarea template
        scene_name = item.properties.get("scene_path")
        if not scene_name:
            raise Exception("Please Save your file before Publishing")

        result = item.properties["accept"]

        supported_pipeline_steps = settings.get("supported_pipeline_steps").value
        if self.parent.engine.context.step.get("name") not in supported_pipeline_steps:
            result["accepted"] = False

        supported_tasks = settings.get("supported_tasks").value
        if self.parent.engine.context.task.get("name") not in supported_tasks:
            result["accepted"] = False

        return result

    # ---------------------------------------------------------------------------

    def validate(self, settings, item):
        work_template_name = settings.get("workarea_template").value
        work_template = self.parent.engine.get_template_by_name(work_template_name)
        if not work_template:
            return False

        scene_path = item.properties.get("scene_path")
        fields = work_template.get_fields(scene_path)
        publish_version = fields.get("version")

        publish_template_name = settings.get("publish_template").value
        publish_template = self.parent.engine.get_template_by_name(
            publish_template_name
        )
        if not publish_template:
            return False
        publish_path = publish_template.apply_fields(fields)
        if not publish_path:
            return False
        self.parent.logger.info("global locators publish_path: {}".format(publish_path))

        publish_name = fields.get("name")
        if not publish_name:
            publish_name = os.path.basename(publish_path)

        item.properties["publish_version"] = publish_version
        item.properties["publish_name"] = publish_name
        item.properties["publish_path"] = publish_path
        item.properties["publish_fields"] = fields

        # read placeholder properties. They will be overwritten
        # once the locators bake runs.
        already_baked = item.properties.get("already_baked")
        locators_group = item.properties.get("locators_group")
        locators_dict = item.properties.get("locators_dict")

        if not already_baked:
            # item.properties["execute"] is a method located in the
            # collector_globalLocators_assets file. It  doesn't require
            # any argument. It returns the locators group and locators dict
            locators_group, locators_dict = item.properties["execute"]()
            self.parent.logger.info("locators_group: {}".format(locators_group))

        if locators_group and locators_dict:
            # if bake has been already processed in this session, save the properties
            # to avoid re-processing of the locators bake
            item.properties["already_baked"] = True
            item.properties["locators_group"] = locators_group
            item.properties["locators_dict"] = locators_dict

            return True
        else:
            raise Exception("There's a problem with the character locators.")

        return False

    # ---------------------------------------------------------------------------

    def publish(self, settings, item):
        print("\n" + (">" * 120))
        print("{0}.publish".format(self.name))

        # create a pointer to the parent root item to store
        # any extra publishes apart form the primary one
        root_item = self.get_root_item(item)
        root_item.properties.setdefault("sg_publish_extra_data", [])
        self.parent.logger.info(
            "Storing extra publish data on root item: {}".format(root_item)
        )
        publish_extra_data = root_item.properties.get("sg_publish_extra_data")

        publish_version = item.properties.get("publish_version")
        publish_name = item.properties.get("publish_name")
        publish_path = item.properties.get("publish_path")
        fields = item.properties.get("publish_fields")

        scene_pub_template_name = settings.get("primary_publish_template").value
        scene_pub_template = self.parent.engine.get_template_by_name(
            scene_pub_template_name
        )
        primary_publish_path = scene_pub_template.apply_fields(fields)

        self.parent.engine.ensure_folder_exists(os.path.dirname(publish_path))

        locators_group = item.properties.get("locators_group")
        publish_path = item.properties.get("publish_path")
        # item.properties["abc_export_alembic"] is a method located in the
        # collector_globalLocators_assets file. It requires the root node and
        # the publish_path
        item.properties["abc_export_alembic"](locators_group, publish_path)

        publisher = self.parent

        publish_dependencies_ids = []
        if "sg_publish_data" in item.parent.properties:
            publish_dependencies_ids.append(
                item.parent.properties.sg_publish_data["id"]
            )

        dependencies = [primary_publish_path]

        sg_fields_to_update = {
            "sg_status_list": "rev",
            "task.Task.sg_status_list": "rev",
        }

        publish_data = {
            "tk": self.parent.engine.sgtk,
            "context": item.context,
            "comment": item.description,
            "path": publish_path,
            "name": publish_name,
            "version_number": publish_version,
            "thumbnail_path": item.get_thumbnail_as_path(),
            "task": self.parent.engine.context.task,
            "sg_fields": sg_fields_to_update,
            "published_file_type": settings.get("publish_type").value,
            "dependency_paths": dependencies,
        }
        self.parent.logger.info("publish_data:\n{}".format(pf(publish_data)))
        sg_publishes = sgtk.util.register_publish(**publish_data)

        # self.parent.logger.info(
        #     "Adding existing publish "
        #     + "(id:{0}) ".format(sg_publishes["id"])
        #     + "as extra data for item: {0}".format(item)
        # )

        publish_extra_data.append(sg_publishes)

        # root_item.properties['sg_publish_extra_data'].extend(sg_publishes)

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item
        return root

    def finalize(self, settings, item):
        pass
