# -*- coding: utf-8 -*-
# Standard library:
import os
import ast
import pprint

# ___   ___   ___   ___   ___   ___  ___
# Third party:
import sgtk
import pymel.core as pm
import maya.cmds as cmds

# ___   ___   ___   ___   ___   ___  ___
# Project:
from tank_vendor import six

# ==============================================================================
pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)
pf = pprint.pformat

HookBaseClass = sgtk.get_hook_baseclass()


class CollectorShotRigsOnTheFly(HookBaseClass):
    @property
    def name(self):
        return "CollectorShotRigsOnTheFly"

    @property
    def settings(self):
        collector_settings = super(CollectorShotRigsOnTheFly, self).settings or {}

        maya_shot_settings = {
            "Work Template": {
                "type": "template",
                "default": "",
                "description": (
                    "Template path for artist work files."
                    "Should correspond to a template defined in "
                    "templates.yml. If configured, is made available"
                    "to publish plugins via the collected item's "
                    "properties. "
                ),
            },
            "schema_version": {
                "type": "string",
                "default": None,
                "description": (
                    "First schema for on the fly rigs version. "
                    "It consists of a dictionary with two main keys:\n"
                    "- version: which represents this value\n"
                    "- data: which contains a list of dictionaries with "
                    "the abstraction data"
                ),
            },
            "asset_type": {
                "type": "string",
                "default": None,
                "description": (
                    "Only objects of this asset types should be "
                    "considered to export in the json abstraction."
                ),
            },
            "common_attributes": {
                "type": "list",
                "default": [],
                "description": (
                    "Common attributes created on the rig on the fly controller. "
                    "Extra attributes are also created depending on the used assets"
                ),
            },
        }

        collector_settings.update(maya_shot_settings)

        # self.parent.logger.info(
        #     "collector_settings:\n{}".format(pf(collector_settings))
        # )

        return collector_settings

    # ----------------------------------------------------------------------------------

    def process_current_session(self, settings, parent_item):
        print("\n" + (">" * 120))
        print("\n{0}.process_current_session".format(self.name))

        super(CollectorShotRigsOnTheFly, self).process_current_session(settings, parent_item)
        if not parent_item.type_spec.endswith(".session"):
            item = next(
                (i for i in parent_item.descendants if i.type_spec.endswith(".session")),
                parent_item,
            )
        else:
            item = parent_item

        self._collect_on_the_fly_controllers(settings, item)


    # ----------------------------------------------------------------------------------

    def _collect_on_the_fly_controllers(self, settings, parent_item):
        result = []

        settings_asset_type = settings.get("asset_type").value

        list_of_rigsOnTheFly_controllers = []
        list_of_curves = cmds.ls(type="nurbsCurve", long=True)

        # Filter curves by asset type: we are only interested in "onTheFlyController"
        if list_of_curves:
            for curve_ in list_of_curves:
                curve_transform = cmds.listRelatives(
                    curve_, parent=True, fullPath=True
                )[0]

                if cmds.attributeQuery("assetType", node=curve_transform, exists=True):
                    asset_type = cmds.getAttr("{}.assetType".format(curve_transform))
                    if asset_type == settings_asset_type:
                        list_of_rigsOnTheFly_controllers.append(curve_transform)

        # Sort list results to avoid namespaces conflicts when the rigs are recreated in
        # animation (might not be needed)
        list_of_rigsOnTheFly_controllers.sort()
        # self.parent.logger.info(
        #     "list_of_rigsOnTheFly_controllers:\n{}".format(
        #         pf(list_of_rigsOnTheFly_controllers)
        #     )
        # )

        list_of_rig_dicts = []
        for ctl_obj in list_of_rigsOnTheFly_controllers:
            rig_dict = self._get_rigs_dictionaries(settings, ctl_obj)
            list_of_rig_dicts.append(rig_dict)

        # self.parent.logger.info("list_of_rig_dicts:\n\n{}\n".format(pf(list_of_rig_dicts)))

        icon_path = os.path.join(
            self.disk_location, os.pardir, os.pardir, os.pardir, "icons", "json.png"
        )

        asset_item_name = "Rigs on the Fly Abstraction"
        asset_item = parent_item.create_item(
            type_spec="maya.session.rigsOnTheFlyAbstraction",
            type_display=asset_item_name,
            name=asset_item_name,
        )

        self.parent.engine.execute_hook_expression(
            "{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py",
            "use_farm_or_local_processing",
            item = asset_item
        )

        asset_item.set_icon_from_path(icon_path)

        abstraction_dictionary = self._create_main_dictionary(
            settings, list_of_rig_dicts
        )
        # self.parent.logger.info(
        #     "abstraction_dictionary:\n\n{}\n".format(pf(abstraction_dictionary))
        # )

        # Add item properties
        asset_item.properties["abstraction_dictionary"] = abstraction_dictionary
        asset_item.properties["scene_path"] = os.path.abspath(
            cmds.file(query=True, sceneName=True)
        )

        return asset_item

    def _get_rigs_dictionaries(self, settings, ctl_obj):
        result = {}

        common_attributes = settings.get("common_attributes").value

        result["controller"] = ctl_obj

        for attribute in cmds.listAttr(ctl_obj):
            if "mty_" in attribute:
                if attribute == "mty_constraints":
                    ctl_constraints = cmds.getAttr(
                        "{}.{}".format(ctl_obj, attribute)
                    ).split(";")
                    result[attribute] = ctl_constraints
                elif attribute not in common_attributes:
                    asset_name = attribute.split("_")[-1]
                    asset_attr_str = cmds.getAttr("{}.{}".format(ctl_obj, attribute))
                    asset_attr_dict = ast.literal_eval(asset_attr_str)
                    if "assets" not in result.keys():
                        result["assets"] = {asset_name: asset_attr_dict}
                    else:
                        result["assets"][asset_name] = asset_attr_dict
                else:
                    result[attribute] = cmds.getAttr("{}.{}".format(ctl_obj, attribute))

        return result

    def _create_main_dictionary(self, settings, list_of_rig_dicts):
        result = {}

        result["schema_version"] = settings.get("schema_version").value
        result["data"] = list_of_rig_dicts

        return result
