########################################################################################
#
# Copyright (c) 2023 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty Animation Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
########################################################################################

"""
An app that helps to reconnect and existing tones setup from another scene. It
automates the connections once the user has selected the proper nodes
"""

from tank import Hook
from tank import Tank<PERSON>rror
from tank.platform import Application
from tank.platform.qt import QtCore, QtGui

import pprint


pp = pprint.pprint
pf = pprint.pformat


class ProcessItemsHook(Hook):
    def execute(self, **kwargs):

        self.parent.engine.logger.info(
            "execute action reconnect_existing_tones_setup start. {}".format("-" * 80)
        )

        result = {"succes": [1], "messages": [], "errors": []}

        scripted_nodes = self.get_valid_scripted_nodes()
        self.parent.engine.logger.info(
            "valid scripted_nodes:\n{}".format(pf(scripted_nodes))
        )
        if not scripted_nodes:
            return result

        for scripted_node in scripted_nodes:
            self.parent.engine.logger.info(
                "Processing scripted node: {}".format(scripted_node)
            )
            self.reprocess_scripted_node(scripted_node)

        self.parent.engine.logger.info(
            "execute action reconnect_existing_tones_setup end. {}".format("-" * 80)
        )

        return result


    def get_valid_scripted_nodes(self):

        cmd = r"""
var log = MessageLog.trace;
var NODES_ROOT = node.root() + "/";

var selected_nodes = selection.selectedNodes();

log("selected_nodes: " + selected_nodes);

function filter_existing_nodes(selected_nodes) {
    var scripted_nodes = [];
    var missing_groups = [];

    for (var i = 0; i < selected_nodes.length; i++) {
        var node_path = selected_nodes[i];
        if (node.type(node_path) != "SCRIPT_MODULE") {
            continue;
        } else {
            var node_name = node.getName(node_path);
            log("node_name: " + node_name);
            var group_name = node_name.substring(15);
            var parts = node_name.split("_");
            var group_name = parts.slice(1).join("_");
            log("group_name: " + group_name);
            if (node.getName(NODES_ROOT + group_name) == "") {
                missing_groups.push(NODES_ROOT + group_name);
            } else {
                scripted_nodes.push(node_path);
            }
        }
    }
    if (missing_groups.length > 0) {
        var msg = "Found " + missing_groups.length + " missing node(s):\n\n" + JSON.stringify(
            missing_groups, null, 4
        );
        MessageBox.information(msg);
    }
    log("scripted_nodes: " + scripted_nodes);
    return scripted_nodes

}

filter_existing_nodes(selected_nodes);

"""

        scripted_nodes = self.parent.engine.app.execute(cmd)
        return scripted_nodes


    def reprocess_scripted_node(self, scripted_node):

        cmd = """
var log = MessageLog.trace;

function reprocess_scripted_node(scripted_node) {
    var ui_script_attr = node.getAttr(scripted_node, 1, "uiScript.editor");
    var cmd = ui_script_attr.textValue();
    eval(cmd.toString());
}


var scripted_node = "%s";

reprocess_scripted_node(scripted_node);

""" % scripted_node

        self.parent.engine.logger.debug("reprocess_scripted_node cmd:\n{}".format(cmd))
        self.parent.engine.app.execute(cmd)


    def fix_path(self, path):
        """Replace all backward slashes with forward slashes. Also adds the P: drive if it's
        no pressent"""

        path = path.replace("\\\\", "/")
        path = path.replace("\\", "/")

        return path
