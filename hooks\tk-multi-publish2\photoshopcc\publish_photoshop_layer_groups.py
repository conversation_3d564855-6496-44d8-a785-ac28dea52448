# -*- coding: utf-8 -*-
# Standard library:
import os
import re
import json
import pprint
import shutil
import tempfile

import sgtk


# ======================================================================================

HookBaseClass = sgtk.get_hook_baseclass()


pp = pprint.pprint
pf = pprint.pformat


class LayerGroupImagesPublishPlugin(HookBaseClass):
    @property
    def icon(self):

        return os.path.join(self.disk_location, os.pardir, "icons", "image_publish.png")

    @property
    def name(self):

        return "Publish Layer Group Images"

    @property
    def description(self):

        return """
        <p>This plugin publishes one image for each the output layer group.</p>

        """

    @property
    def settings(self):

        # inherit the settings from the base publish plugin
        plugin_settings = super(LayerGroupImagesPublishPlugin, self).settings or {}

        # settings specific to this class
        review_publish_settings = {
            "Available Steps": {
                "type": "list",
                "default": [],
                "description": "A list of Step names to filter.",
            },
            "Available Tasks": {
                "type": "list",
                "default": [],
                "description": "A list of tasks names to filter.",
            },
            # "Document Width": {
            #     "type": "int",
            #     "default": 1024,
            #     "description": "Photoshop document width size."
            # },
            # "Document Height": {
            #     "type": "int",
            #     "default": 768,
            #     "description": "Photoshop document height size."
            # },
            # "Image Resolution": {
            #     "type": "int",
            #     "default": 72,
            #     "description": "Image resolution in dpi.",
            # },
            "Layer Group Image Format": {
                "type": "string",
                "default": "png",
                "description": "Extension to define the output images format.",
            },
        }

        # update the base settings
        plugin_settings.update(review_publish_settings)

        return plugin_settings

    @property
    def item_filters(self):
        # return ["photoshop.layergroups.image", "photoshop.layergroups"]
        return ["photoshop.layergroups"]

    def accept(self, settings, item):

        self.parent.logger.info("\nstart accept plugin -----------------------------\n")
        self.photoshop = self.parent.engine.adobe
        context = self.parent.context
        # self.collect_layer_groups(item)

        pipelineSteps = settings["Available Steps"].value
        pipelineTasks = settings["Available Tasks"].value

        valueoverrides = self.parent.engine.custom_frameworks.get(
            'mty-framework-valueoverrides'
        ) or self.load_framework('mty-framework-valueoverrides')
        override_code_tasks = 'mty.publisher.photoshop.layer_groups.tasks'
        data_tasks = valueoverrides.get_value(override_code_tasks)
        self.parent.logger.info("\n data tasks {}".format(data_tasks))
        if data_tasks is not None:
            pipelineTasks = json.loads(data_tasks)

        override_code_steps = 'mty.publisher.photoshop.layer_groups.steps'
        data_steps = valueoverrides.get_value(override_code_steps)
        if data_steps is not None:
            pipelineSteps = json.loads(data_steps)

        self.parent.logger.info("\n Available steps {}".format(pipelineSteps))
        self.parent.logger.info("\n Available tasks {}".format(pipelineTasks))

        non_filter_steps = pipelineSteps == []
        non_filter_tasks = pipelineTasks == []

        accepted_tasks = False
        accepted_steps = False
        if not non_filter_tasks:
            step_filter_tasks = (
                context.task["name"] in pipelineTasks
            )
            if step_filter_tasks:
                accepted_tasks = True
        else:
            accepted_tasks = True

        if not non_filter_steps:
            step_filter_steps = (
                context.step["name"] in pipelineSteps
            )
            if step_filter_steps:
                accepted_steps = True
        else:
            accepted_steps = True

        if accepted_tasks and accepted_steps:
            return {"accepted": True, "checked": True}
        else:
            return {"accepted": False, "checked": True}

    # ---------------------------------------------------------------------

    def validate_actions(self, state, **kwargs):

        if state["errors_found"] > 0:
            self.logger.error(
                "There are {0} errors in the scene".format(state["errors_found"])
            )

            for key in state["errors"]:
                error_message = "{0} ({1}):".format(key, len(state["errors"][key]))
                problems_message = ""

                for element in state["errors"][key]:
                    problems_message += "{0}\n".format(element)

                self.logger.error(
                    error_message,
                    extra={
                        "action_show_more_info": {
                            "label": "Show details",
                            "tooltip": "Show all information from the error",
                            "text": "<h3>{0}</h3><pre>{1}</pre>".format(
                                error_message, problems_message
                            ),
                        }
                    },
                )

                #   . . . . . . . . . . . . . . . . . . . . . .

                if state["callbacks"][key]["enabled"]:
                    log = None
                    message_type = state["callbacks"][key]["type"]

                    if message_type == "error":
                        log = self.logger.error
                    elif message_type == "warning":
                        log = self.logger.warning
                    elif message_type == "debug":
                        log = self.logger.debug
                    else:
                        log = self.logger.info

                    message = state["callbacks"][key]["message"]
                    callback = state["callbacks"][key]["callback"]
                    label = state["callbacks"][key]["label"]
                    tooltip = state["callbacks"][key]["tooltip"]

                    log(
                        message,
                        extra={
                            "action_button": {
                                "label": label,
                                "tooltip": tooltip,
                                "callback": callback(),
                            }
                        },
                    )
                #   . . . . . . . . . . . . . . . . . . . . . .

        return state["errors_found"]

    def composer_set(self, name, list_of_errors, state, action):
        if len(list_of_errors) > 0:
            state["errors_found"] += len(list_of_errors)
            state["errors"][name] = list_of_errors

            if "enabled" not in action.keys():
                raise Exception(
                    "Error in {0}: \n".format(name)
                    + '"enabled" key is not present in action dictionary.\n'
                    + "provided action dictionary: {0}".format(str(action))
                )

            #   . . . . . . . . . . . . . . . . . . . . . .

            set_of_keys = set(action.keys())

            set_of_required_keys = {
                "enabled",
                "type",
                "callback",
                "label",
                "tooltip",
                "message",
            }

            #   . . . . . . . . . . . . . . . . . . . . . .

            if not set_of_required_keys.issuperset(set_of_keys):
                raise Exception(
                    "Error in {0}: \n".format(name)
                    + "Missing keys in action dictionary.\n"
                    + "provided action dictionary: {0}".format(str(action))
                )

            #   . . . . . . . . . . . . . . . . . . . . . .

            if action["enabled"]:

                state["callbacks"][name] = {
                    "enabled": action["enabled"],
                    "type": action["type"],
                    "callback": action["callback"],
                    "label": action["label"],
                    "tooltip": action["tooltip"],
                    "message": action["message"],
                }
            else:
                state["callbacks"][name] = {"enabled": False}
                if state.get("message", None):
                    state["callbacks"][name].update({"message": state["message"]})

    def validate(self, settings, item):

        self.parent.logger.info(
            "start layer group images validation ".ljust(120, "-")
        )
        state = {"errors_found": 0, "errors": {}, "callbacks": {}}

        master_flatten_layerset_name = item.properties.get(
            "layer_set_flattens_name", "DO_NOT_DELETE_Output_flatten_group"
        )
        # ------------------------------------------------------------------------------
        root_item = self.get_root_item(item)

        # first of all, check if there are wrongly named layer groups
        wrongly_named_layer_groups = item.properties.get(
            "wrongly_named_layer_groups", None
        )
        if wrongly_named_layer_groups:
            wrongly_named_layer_groups_errors = (
                 "At least one layer group is wrongly named:\n{}\n\n"
                 "The correct naming convention is as follows:\n"
                 "    Output_[order][name]\n\n"
                 "For instance:\n"
                 "    Output_010FG\n"
                 "    Output_010Pose\n\n"
                 "where the order shouuldn't repeat and should be incremental from "
                 "background to foreground."
            ).format(pprint.pformat(wrongly_named_layer_groups))

            self.composer_set(
                name="Wrongly named layer groups",
                list_of_errors=wrongly_named_layer_groups,
                state=state,
                action={
                    "enabled": False,
                    "message": wrongly_named_layer_groups_errors,
                },
            )


        # Debug logging
        self.parent.logger.debug("root_item.children: ".ljust(120, "-"))
        for prop in root_item.properties:
            self.parent.logger.debug("-" * 20)
            self.parent.logger.debug("property: {}".format(prop))
            try:
                self.parent.logger.debug(
                    "pp:\n{}".format(pprint.pformat(root_item.properties[prop]))
                )
            except:
                self.parent.logger.debug("log: {}".format(root_item.properties[prop]))
        for child in root_item.children:
            self.parent.logger.info("-" * 40)
            self.parent.logger.info("child.name: {}".format(child.name))
            self.parent.logger.info("child.children: {}".format(list(child.children)))
            self.parent.logger.info("child.parent: {}".format(child.parent))
            self.parent.logger.info(
                "child.properties:\n{}".format(pprint.pformat(list(child.properties)))
            )
            for prop in child.properties:
                self.parent.logger.info("-" * 20)
                self.parent.logger.info("property: {}".format(prop))
                try:
                    self.parent.logger.info(
                        "pp:\n{}".format(pprint.pformat(child.properties[prop]))
                    )
                except:
                    self.parent.logger.info("log: {}".format(child.properties[prop]))
        # ------------------------------------------------------------------------------

        # Get all layer visibility state saved originally in the collector
        self.all_layers_visibility_state = root_item.properties.get(
            "all_layers_visibility_state", None
        )

        # Get templates and file types from item
        templates_and_file_types = item.properties.get("templates_and_file_types", None)
        layer_group_data = item.properties.get("layer_group_data", None)
        if layer_group_data:
            self.parent.logger.info(
                "layer_group_data:\n{}".format(pf(layer_group_data))
            )

        if not templates_and_file_types:
            templates_and_file_types_errors = [
                "Couldn't get templates and publish file types from item"
            ]
            self.composer_set(
                name="Templates and file types error",
                list_of_errors=templates_and_file_types_errors,
                state=state,
                action={"enabled": False},
            )

        if not layer_group_data:
            layer_group_data_errors = ["Couldn't get layer groups data from item"]
            self.composer_set(
                name="Layer groups data error",
                list_of_errors=layer_group_data_errors,
                state=state,
                action={"enabled": False},
            )

        if not self.all_layers_visibility_state:
            layers_visibility_state_errors = [
                "Couldn't get layers visibility state from item"
            ]
            self.composer_set(
                name="Layer groups data error",
                list_of_errors=layers_visibility_state_errors,
                state=state,
                action={"enabled": False},
            )

        # layer group spaces in names validation ---------------------------------------
        self.parent.logger.info(
            "Layers spaces in names validation start. ".ljust(120, "-")
        )

        if layer_group_data:
            # reformat name should be the layers without any spaces, not at the end of
            # the string, and not in the middle of the string
            layers_with_space_in_name = []
            for layer in layer_group_data.keys():
                layer_reformat_name = layer_group_data[layer].get("reformat_name", None)
                layer_original_name = layer_group_data[layer].get("original_name", None)
                if re.search(r"\s", layer_original_name):
                    if re.search(r"\s", layer_reformat_name):
                        layers_with_space_in_name.append(layer_original_name)
                        self.parent.logger.error(
                            "Found layer with space in name: {}".format(
                                layer_original_name
                            )
                        )
                    else:
                        self.parent.logger.info(
                            "Correctly reformatted name from: {} to: {}".format(
                                layer_original_name, layer_reformat_name
                            )
                        )

            if layers_with_space_in_name:
                layers_with_spaces_state_errors = [
                    "Found layers with spaces in name\n\n{}".format(
                        layers_with_space_in_name
                    )
                ]
                self.parent.logger.error(layers_with_spaces_state_errors)
                self.composer_set(
                    name="Layer groups with spaces in name error",
                    list_of_errors=layers_with_spaces_state_errors,
                    state=state,
                    action={"enabled": False},
                )
        else:
            self.parent.logger.error("No layer groups found.")

        # layer group order validation -------------------------------------------------
        self.parent.logger.info("Layers order validation start. ".ljust(120, "-"))

        if layer_group_data:
            # order value means the prefix for each layer name, which should be a 3
            # digit number, starting from 010 for the far most layer, progresing in
            # increments of 10 for each closer layer
            layers_without_order_value = []
            for layer in layer_group_data.keys():
                layer_order = layer_group_data[layer].get("order", None)
                layer_original_name = layer_group_data[layer].get("original_name", None)
                if not layer_order:
                    layers_without_order_value.append(layer_original_name)
                    self.parent.logger.error(
                        "Found layer without order prefix: {}".format(layer_original_name)
                    )

            if layers_without_order_value:
                layers_order_state_errors = [
                    (
                        "Found some output groups without order prefix.\n"
                        "Output layer groups must must follow this rule:\n"
                        "\tOutput_###name\n\n"
                        "Where ### represents the Z depth order of the layer,\n"
                        "atarting with the farest layer (010) and progressing in \n"
                        "increments of 10 for the closer layers.\n\n"
                        "Layers without order prefix:\n{}"
                    ).format(pf(layers_without_order_value))
                ]
                self.parent.logger.error(layers_order_state_errors)
                self.composer_set(
                    name="Layer groups order prefix error",
                    list_of_errors=layers_order_state_errors,
                    state=state,
                    action={"enabled": False},
                )
        else:
            self.parent.logger.error("No layer groups found.")

        # locked layers validation -----------------------------------------------------
        self.parent.logger.info("Locked layers validation start. ".ljust(120, "-"))

        locked_layers = {}
        for layer in self.all_layers_visibility_state.keys():
            layer_name = layer
            if layer_name == master_flatten_layerset_name:
                continue
            layer_obj = self.all_layers_visibility_state[layer].get("layer_obj", None)
            is_locked = self.all_layers_visibility_state[layer].get("is_locked", None)
            is_background = self.all_layers_visibility_state[layer].get(
                "is_background", None
            )

            self.parent.logger.info(
                "layer: {}, is_locked: {}, is_background: {}, layer_obj: {}".format(
                    layer_name, is_locked, is_background, layer_obj
                )
            )
            if is_locked or is_background:
                locked_layers.update({layer_name: layer_obj})

        if locked_layers:
            locked_layers_state_errors = [
                "Found locked layers: {}".format(locked_layers.keys())
            ]
            self.parent.logger.error(locked_layers_state_errors)
            self.composer_set(
                name="Locked layers error",
                list_of_errors=locked_layers_state_errors,
                state=state,
                action={"enabled": False},
            )


        state_result = self.validate_actions(state)

        if state_result:
            there_is_error = (
                "Validation checks have not been passed, "
                + "found {0} problems".format(state_result)
            )

            raise Exception(there_is_error)

        else:
            self.logger.debug("All checks passed!")

        """ omited by coordinator request, they are gonna use frame numbers

        # Validate continuos layer naming numbering
        numbers_list = []
        for name in sorted(groups_names):
            numbers_list.append(int(name))

        numbers_list = sorted(numbers_list)

        self.parent.loggers.debug(
            'Total layer groups: {}'.format(str(len(numbers_list)))
        )
        self.parent.loggers.debug('Smallest number: {}'.format(str(numbers_list[0])))
        self.parent.loggers.debug('Bigest number: {}'.format(str(numbers_list[-1])))

        if (numbers_list[-1] - numbers_list[0] + 1) != len(numbers_list):
            raise Exception(
                "Layers are not named correctly, they need to be continued numbers..."
            )
        """

        # item.properties["layer_groups"] = layer_groups

        return True

    def delete_if_exists(self, path):

        if os.path.exists(path):
            os.remove(path)

    def disable_guides_group(self, PS_document, layer_groups_data):
        for layerset in PS_document.layerSets:
            orig_name = layerset.name
            if "guides" in orig_name or "Guides" in orig_name:
                # guides_output_group = layer_groups_data[layer]["layer_group"]
                layerset.visible = False
                self.parent.logger.info(
                    "Successfully disabled guides group: {}".format(orig_name)
                )

    def set_visible_output_flatten(
        self,
        PS_document,
        layer_name=None,
        layer_groups_name=None
    ):
        """
        Isolate layer group: all layers will be turned off except the one called as
        the layer_group_name argument.
        """
        for layer in PS_document.layers:
                layer.visible = False

        for layerset in PS_document.layerSets:
            if layerset.name in layer_groups_name:
                layerset.visible = True
                for layer in layerset.layers:
                    if layer.name == layer_name:
                        layer.visible = True
                    else:
                        layer.visible = False


    def restore_visibility_state_in_all_layers(
        self, PS_document, saved_layers_state=None
    ):
        """
        Restore the visibility of each layer based on the visibility saved in
        the collector as "all_layers_visibility_state" property in the root_item
        """
        if not saved_layers_state:
            return

        for layer in PS_document.layers:
            layer_name = layer.name
            saved_visible_state = saved_layers_state.get(layer_name, {}).get(
                "visible", None
            )
            # used this condition because the saved_visible_state can be False
            if saved_visible_state == None:
                continue

            layer.visible = saved_visible_state

    # ----------------------------------------------------------------------------------

    def restore_visibility_output_flatten(self, PS_document, layer_groups_name):
        #restore visibility of output flatten layers
        for layerset in PS_document.layerSets:
            if layerset.name in layer_groups_name:
                layerset.visible = True
                for layer in layerset.layers:
                        layer.visible = True

    def register_publish(
        self,
        comment,
        path,
        name,
        version_number,
        thumbnail,
        published_file_type,
        dependencies=[],
    ):
        # get media resolution
        media_resolution = self.parent.execute_hook_expression(
            "{config}/tk-multi-publish2/multi/resolution_from_path.py",
            "media_resolution_value",
            path=path
        )
        self.parent.logger.info(
            "media resolution from hook: {}".format(media_resolution)
        )

        # Register the publish:
        publish_data = {
            "tk": self.parent.engine.sgtk,
            "context": self.parent.engine.context,
            "comment": comment,
            "path": path,
            "name": name,
            "version_number": version_number,
            "thumbnail_path": thumbnail,
            "task": self.parent.engine.context.task,
            "published_file_type": published_file_type,
            "dependency_paths": dependencies,
            "sg_fields":{"sg_media_resolution": media_resolution}
        }
        sg_publish = sgtk.util.register_publish(**publish_data)

        self.parent.tank.shotgun.update(
            "PublishedFile", sg_publish["id"], {"sg_status_list": "ip"}
        )

        return sg_publish

    # ----------------------------------------------------------------------------------

    def publish_layer_group_images(self, item, layer_groups_data, scene_data, settings):

        self.parent.logger.info("publish_layer_group_images start {}".format("-" * 80))

        sg_publishes = []
        layer_group_items = {}

        layer_group_image_format = settings["Layer Group Image Format"].value

        for layergroup in layer_groups_data:
            self.parent.logger.info(
                "LAYERGROUP: {}".format(layer_groups_data[layergroup]["original_name"])
            )
            layer_original_name = layer_groups_data[layergroup]["original_name"]
            layer_reformat_name = layer_groups_data[layergroup]["reformat_name"]
            layer_order = layer_groups_data[layergroup]["order"]
            layer_index = layer_groups_data[layergroup]["index"]
            layer_group_name = layer_groups_data[layergroup]["layer_group"]

            self.logger.info(
                "Publishing layer group '{}', please wait.".format(layer_reformat_name)
            )
            self.parent.logger.info(
                "Publishing layer group '{}'".format(layer_reformat_name)
            )

            fields = scene_data["fields"]
            fields["photoshop.layer_name"] = layer_reformat_name
            fields["extension"] = layer_group_image_format
            self.parent.logger.debug("fields:\n{}".format(pprint.pformat(fields)))

            image_publish_template = scene_data["publish_template"]
            image_publish_path = image_publish_template.apply_fields(fields)
            image_publish_basename = os.path.basename(image_publish_path)

            image_temp_path = os.sep.join(
                (scene_data["temp_folder"], image_publish_basename)
            )

            # delete file if exists previous created images
            self.delete_if_exists(image_temp_path)

            # Set layers visibility for exporting the current layer group.
            # If group layer is "master" then the original visibility is restored for
            # all document layers.
            self.set_visible_output_flatten(
                scene_data["PS_document"], layer_original_name, layer_group_name
            )

            # ---------------------------------------------------------------------------
            # set photoshop to save image as png
            png_file = self.photoshop.File(image_temp_path)
            png_options = self.photoshop.PNGSaveOptions()
            png_options.compression = (
                5  # valid values are [0..9], default is 0, which means no compression
            )
            png_options.interlaced = False

            # photoshop save as a copy
            self.parent.logger.info(
                "Saving PNG file to tmp location: {}".format(image_temp_path)
            )
            self.photoshop.app.activeDocument.saveAs(png_file, png_options, True)
            if not os.path.exists(image_temp_path):
                # Restore layers visibility based on the saved state within the collector
                self.restore_visibility_state_in_all_layers(
                    scene_data["PS_document"], self.all_layers_visibility_state
                )
                raise Exception(
                    "Error saving PNG file from Photoshop: {}".format(png_file)
                )

            # ---------------------------------------------------------------------------

            # ensure publish path exists and copy png to publish path
            if not os.path.exists(os.path.dirname(image_publish_path)):
                os.makedirs(os.path.dirname(image_publish_path))
            shutil.copyfile(image_temp_path, image_publish_path)


            # Restore layers visibility based on the saved state within the collector
            self.restore_visibility_output_flatten(
                scene_data["PS_document"], layer_group_name
            )

            # delete temp files if exists
            self.delete_if_exists(image_temp_path)

            # Get publish name
            publish_name = self.parent.util.get_publish_name(
                image_publish_path, sequence=False
            )
            self.parent.logger.info(
                "publish_name from util (not used): {}".format(publish_name)
            )

            publish_name = self.parent.execute_hook_expression(
                "{config}/tk-multi-publish2/multi/name_from_path_info.py",
                "get_publish_name",
                path=image_publish_path
            )
            self.parent.logger.info(
                "publish_name from hook: {}".format(publish_name)
            )


            # Create publish data dict for registering the publish
            publish_data = {
                "comment": item.description,
                "path": image_publish_path,
                "name": publish_name,
                "version_number": scene_data["publish_version_number"],
                "thumbnail": image_publish_path,
                "published_file_type": scene_data["publish_type"],
                "dependencies": [scene_data["primary_publish_path"]],
            }

            sg_publish = self.register_publish(**publish_data)
            sg_publishes.append(sg_publish)
            layer_group_items.update({image_publish_path: layer_index})

        # Restore layers visibility based on the saved state within the collector
        self.restore_visibility_state_in_all_layers(
            scene_data["PS_document"], self.all_layers_visibility_state
        )

        return layer_group_items, sg_publishes

    def publish(self, settings, item):

        self.ffmpeg = self.load_framework("mty-framework-ffmpeg")

        root_item = self.get_root_item(item)
        root_item.properties.setdefault("sg_publish_extra_data", [])
        sg_publish_extra_data = root_item.properties["sg_publish_extra_data"]

        # get templates, file types and layer group data from item (from collector)
        templates_and_file_types = item.properties.get("templates_and_file_types", None)
        layer_group_data = item.properties.get("layer_group_data", None)

        if not templates_and_file_types:
            raise Exception("Couldn't get templates from item.")
        if not layer_group_data:
            raise Exception("Couldn't get layer group data from item.")

        # Get PS document data from templates_and_file_types dict (gathered from collector)
        # PS_document = templates_and_file_types.get("PS_document")
        PS_document_path = templates_and_file_types.get("PS_document_path")
        # instead of getting the document from the templates and file types dict,
        # get it from the current opened document, because the previous document
        # has been already closed by the main publisher
        PS_document = self.parent.engine.adobe.get_active_document()
        # update the document object in the main item to allow the publisher to
        # the version number of the working file after the publish finishes
        for child in root_item.children:
            ps_doc_basename = os.path.basename(PS_document_path)
            if child.name == ps_doc_basename:
                child.properties["document"] = PS_document

        primary_publish_template = templates_and_file_types.get(
            "primary_publish_template"
        )
        work_template = templates_and_file_types.get("primary_work_template")
        image_publish_template = templates_and_file_types.get("image_publish_template")
        proxy_image_publish_template = templates_and_file_types.get(
            "proxy_image_publish_template"
        )
        image_review_publish_template = templates_and_file_types.get(
            "image_review_publish_template"
        )
        video_publish_template = templates_and_file_types.get("video_publish_template")

        # get template fields and apply them to the relevant templates
        fields = work_template.get_fields(PS_document_path)
        primary_publish_path = primary_publish_template.apply_fields(fields)

        curent_scene_data = {
            "PS_document": PS_document,
            "PS_document_path": PS_document_path,
            "temp_folder": tempfile.gettempdir(),
            "primary_publish_template": primary_publish_template,
            "primary_publish_path": primary_publish_path,
            "work_template": work_template,
            "publish_type": templates_and_file_types.get("image_file_type"),
            "publish_template": image_publish_template,
            "fields": fields,
            "publish_version_number": fields["version"],
        }

        # Create images from layer groups
        layer_group_paths, sg_publishes = self.publish_layer_group_images(
            item, layer_group_data, curent_scene_data, settings
        )
        sg_publish_extra_data.extend(sg_publishes)
        # self.parent.logger.info(
        #     "layer_groups sg_publish_extra_data: {}".format(pf(sg_publish_extra_data))
        # )

        # Save png paths in root item for later use, especially with the proxy images
        # hook
        root_item.properties["layer_group_paths"] = layer_group_paths

        # Update task status for revision
        sg_task = self.parent.context.task
        self.parent.logger.info(
            "End of Publish and updating task with id: {} to status: 'rev'".format(
                str(sg_task['id'])
            )
        )

        try:
            self.parent.engine.shotgun.update(
                "Task", sg_task["id"], {"sg_status_list": 'rev'}
            )
        except:
            pass

    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        pass

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root
