################################################################################
#
# Copyright (c) 2021 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
################################################################################

import json
import sgtk
import traceback

from pprint import pformat

HookBaseClass = sgtk.get_hook_baseclass()


class TransferBackends(HookBaseClass):

    def collect(self):
        """
        A hook to define the different schema mappings between shotgun instances
        of two Locations, being the studio one and the client/vendor

        These mappings define the equivalent project name, task names,
        entity types, field types, etc

        """

        # get supported task names from overrides or form config settings
        self.valueoverrides = self.load_framework("mty-framework-valueoverrides")
        deliveries_mappings_value = "mty.framework.deliveries.mappings"
        mappings = self.valueoverrides.get_value(deliveries_mappings_value)
        if mappings:
            mappings = json.loads(mappings)
            self.parent.engine.logger.info("Got deliveries mappings from overrides.")
        else:
            mappings = {
                "general": {
                    "client_site_url": {
                        # 'FlyingBark': 'https://thebark.shotgunstudio.com',
                        # 'PipelineTests': 'https://artbutter.shotgrid.autodesk.com',
                        # 'GoldenWolf': 'https://goldenwolf.shotgrid.autodesk.com',
                        # 'Gasolina': 'https://gasolinastudios.shotgunstudio.com',
                        "SomethingElse": "https://somethingelse.shotgunstudio.com",
                    },
                    "client_location_projects": {
                        # 'FlyingBark': {'type': 'Project', 'id':435},
                        # 'PipelineTests': {'type': 'Project', 'id':619},
                        # 'GoldenWolf': {'type': 'Project', 'id':163},
                        # 'Gasolina': {'type': 'Project', 'id':784 },
                        "SomethingElse": {"type": "Project", "id": 123},
                    },
                    "client_location_studio_location_records": {
                        "FlyingBark": {"id": 516, "type": "CustomEntity44"}
                    },
                    "studio_client_name_fields": {
                        "Episode": "sg_episode_client_name",
                        "Sequence": "sg_sequence_client_name",
                        "Shot": "sg_shot_client_name",
                        "Asset": "sg_entity_client_name",
                    },
                },
                "outgest": {
                    "entity_type": "PublishedFile",
                    "entity_fields": [
                        "published_file_type",
                        "entity",
                        "task",
                        "code",
                        "path",
                        "path_cache",
                        "sg_deliveries",
                        "upstream_published_files",
                    ],
                    "supported_task_names": {
                        "cmpComp": "Compositing",
                        "bgColor": "Color",
                        "rigRig": "Rig",
                        "designColor": "artDesign",
                        "mdlModeling": "mdlModeling",
                        "sfcShading": "mxSfcShading",
                        "rig2dRigging": "mxRig2dRigging",
                        "bgRough": "mxBgRough",
                        "bgClean": "mxbgClean",
                        "bgColor": "mxBgColor",
                        "chlClean": "mxChlClean",
                        "plsRough": "mxPlsRough",
                        "plsClean": "mxPlsClean",
                        "plsColor": "mxPlsColor",
                        "plsRetake": "mxPlsRetake",
                        "plsFix": "mxPlsFix",
                        "cutPosing": "mxCutPosing",
                        "cutInBetween": "mxCutInBetween",
                        "cutRetake": "mxCutRetake",
                        "cutFix": "mxCutFix",
                        "lgtFills": "mxLgtFills",
                        "fx2dFinish": "mxFx2dFinish",
                        "fx2dRetake": "mxFxRetake",
                        "fx2dFix": "mxFxFix",
                        "cmpComp": "mxCmpComp",
                        "cmpRetake": "mxCmpRetake",
                        "cmpFix": "mxCmpFix"
                    },
                    "entity_plugin_identifier_field": "published_file_type",
                    "replace_studio_name_with_client_name": False,
                    "version_extra_fields": {},
                    "update_client_task_status": False,
                    "desired_client_task_status": "rev",
                    "create_playlist_on_client_SG": False,
                    "playlist_name_dict": {"studio_name": "VENDOR"},
                    "playlist_name_template": "{studio_name}_{todays_date}_{delivery_title}_{delivery_id}",
                    "use_custom_version_name_template": False,
                    "custom_version_name_template": "{episode}_{sequence}_{shot}_{step}_{task}_{scene_name}_{rendered_name}_{version}",
                    "custom_published_file_name_templates": {
                        "Asset": "",
                        "Shot": "",
                        "Sequence": "",
                        "Episode": ""
                    },
                    "custom_names_client_remapping": {
                        "current_name": "desired_name",
                        # example:
                        "episode": "customName",
                        "sequences": {
                            "sequence_name": "customName",
                        },
                        "shots": {
                            "shot_name": "customName",
                        },
                        "tasks": {
                            "task_name": "customName",
                        },
                    },
                },
                "ingest": {
                    "entity_type": "CustomThreadedEntity08",
                    "entity_fields": [
                        "code",
                        "description",
                        "content",
                        "sg_publish_location",
                        "sg_locations",
                        "sg_path_cache",
                        "addressings_to",
                        "sg_published_files",
                        "project",
                        "sg_task",
                    ],
                    "entity_plugin_identifier_field": "sg_task",
                    "entity_manifest_path_field": "sg_path_cache",
                    "main_remote_file_path_key": "aws_source_path",
                    "remote_files_list_key": "reference_files",
                    "supported_entity_types_subtypes": {
                        # Keys are Studio Entity Types
                        # Values are manifest additional_metadata key that contains
                        # the corresponding subtype value for this entity type
                        "Asset": {
                            "metadata_key": "asset_type",
                            "filters_field": "sg_asset_type",
                            "mappings": {
                                "Prop": "Prop",
                            },
                        },
                    },
                    "studio_default_task_templates": {
                        # Keys are Studio Entity Types
                        # Values are corresponding entity subtypes which
                        # in turn have the Task Templates entity ids
                        "Asset": {
                            "Prop": 835,
                        },
                        "Sequence": {
                            "GENERIC": 807,
                        },
                    },
                },
            }
            self.parent.engine.logger.info(
                "Got deliveries mappings from configuration."
            )

        # update mappings with different overrides.
        # IMPORTANT: all overrides or default values must follow the same structure
        # as the mappings dict, because later we are merging the dictionaries obtained
        # from the overrides with the mappings dict.
        overrides_list = [
            "mty.framework.deliveries.outgest.replace_studio_name_with_client_name",
            "mty.framework.deliveries.outgest.use_custom_version_name_template",
            "mty.framework.deliveries.outgest.custom_names_client_remapping",
            "mty.framework.deliveries.outgest.custom_version_name_template",
            "mty.framework.deliveries.outgest_plugin.supported_task_names",
            "mty.framework.deliveries.outgest.client_version_extra_fields",
            "mty.framework.deliveries.outgest.desired_client_task_status",
            "mty.framework.deliveries.outgest.update_client_task_status",
            "mty.framework.deliveries.outgest.create_playlist_on_client_SG",
            "mty.framework.deliveries.outgest.playlist_name_dict",
            "mty.framework.deliveries.outgest.playlist_name_template",
            "mty.framework.deliveries.outgest.custom_publish_file_name_templates",
        ]
        mappings = self.update_mappings_from_overrides_list(mappings, overrides_list)

        self.parent.engine.logger.debug(
            "mappings type: {}, value:\n{}".format(type(mappings), pformat(mappings))
        )

        return mappings

    def merge_dicts(self, base_dict, updated_dict):
        """
        Merge dictionaries recursively to update the base dict with the updated_dict
        data. Useful to update data from override while keeping the overrides values
        small.

        Args:
            base_dict (dict): the base mappings dict
            updated_dict (dict): A dictionary containing data that should be added to
                                 the base dict. Usually, a small dictionary coming from
                                 a value override
        Returns:
            dict: the updated base dictionary
        """
        for key, value in updated_dict.items():
            if (
                key in base_dict
                and isinstance(base_dict[key], dict)
                and isinstance(value, dict)
            ):
                # Recursively merge dictionaries
                self.merge_dicts(base_dict[key], value)
            else:
                # Add the new key or replace with a non-dict value
                base_dict[key] = value

        return base_dict

    def update_mappings_from_overrides(self, mappings, default_value):
        """
        Update the mappings dict with the value from the override
        """

        # ensure the valueoverrides framework is available
        if not hasattr(self, "valueoverrides"):
            self.valueoverrides = self.load_framework("mty-framework-valueoverrides")

        value_from_override = self.valueoverrides.get_value(default_value)
        if value_from_override:
            try:
                value_from_override = json.loads(value_from_override)
                self.parent.engine.logger.info(
                    "value_from_override{}".format(value_from_override)
                )

                mappings = self.merge_dicts(mappings, value_from_override)
            except Exception:
                self.parent.engine.logger.error(
                    f"Error loading {default_value}:\n{traceback.format_exc()}"
                )
        else:
            self.parent.engine.logger.warning(
                "get_value returned None, check if the value override was properly set!"
            )

        return mappings

    def update_mappings_from_overrides_list(self, mappings, overrides_list):
        """
        Iterate over the list of overrides and update the mappings dict
        """
        for override in overrides_list:
            mappings = self.update_mappings_from_overrides(mappings, override)

        return mappings
