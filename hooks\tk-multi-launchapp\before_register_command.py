# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

import os
import sgtk

HookBaseClass = sgtk.get_hook_baseclass()


class BeforeRegisterCommand(HookBaseClass):
    """
    Before Register Command Hook

    This hook is run prior to launchapp registering launcher commands with
    the parent engine. Note: this hook is only run for Software entity
    launchers.
    """

    def determine_engine_instance_name(self, software_version, engine_instance_name):
        """
        Hook method to intercept SoftwareLauncher and engine instance name data prior to
        launcher command registration and alter the engine instance name should that
        be required.

        :param software_version: The software version instance constructed when
            the scan software routine was run.
        :type: :class:`sgtk.platform.SoftwareVersion`
        :param str engine_instance_name: The name of the engine instance that will
            be used when SGTK is bootstrapped during launch.

        :returns: The desired engine instance name.
        :rtype: str
        """
        # We're going to end up getting a SoftwareVersion for Nuke Studio that
        # wants to route us to the tk-nuke engine instance. We don't want that, so
        # we'll redirect to tk-nukestudio.
        if software_version.product == "NukeStudio":
            engine_instance_name = "tk-nukestudio"

        if engine_instance_name == 'tk-harmony':
            # Custom xstage startup and newfile templates
            # depending on harmony version

            version = software_version.version
            self.parent.logger.info(
                "harmony version: {}, type: {}".format(version, type(version))
            )
            self.parent.logger.info("harmony float version: {}".format(float(version)))

            hooks_location = os.path.dirname(
                os.path.dirname(self.disk_location)
            )

            templates_location = os.path.join(
                hooks_location, 'hooks', 'tk-harmony', 'templates'
            )

            self.parent.logger.info(
                'templates_location: {}'.format(templates_location)
            )

            # the following code was used to set custom templates for startup and
            # newfile depending on harmony version. We are no using one single template
            # for all Harmony versions, so this code is no longer needed, was left
            # here for reference in case we want to go back to the previous approach
            # --------------------------------------------------------------------------
            # if float(version) < 21:
            #     startup = os.path.join(
            #         templates_location, 'startup', 'default', 'template.xstage'
            #     )
            #     newfile = os.path.join(
            #         templates_location, 'newfile', 'default', 'template.xstage'
            #     )
            # else:
            #     startup = os.path.join(
            #         templates_location, 'startup', str(version), 'template.xstage'
            #     )
            #     newfile = os.path.join(
            #         templates_location, 'newfile', str(version), 'template.xstage'
            #     )
            # --------------------------------------------------------------------------

            # use the same custom templates for all Harmony versions
            startup = os.path.join(
                templates_location, 'startup', 'default', 'template.xstage'
            )
            newfile = os.path.join(
                templates_location, 'newfile', 'default', 'template.xstage'
            )

            self.parent.logger.info(
                'Harmony startup template: {}'.format(startup)
            )

            self.parent.logger.info(
                'Harmony newfile template: {}'.format(newfile)
            )

            os.environ['SGTK_HARMONY_CUSTOM_STARTUP_TEMPLATE'] = str(startup)
            os.environ['SGTK_HARMONY_CUSTOM_NEWFILE_TEMPLATE'] = str(newfile)

        return engine_instance_name
