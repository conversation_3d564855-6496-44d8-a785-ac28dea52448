# -*- coding: utf-8 -*-
# Standard library:
import pprint
import os
# ___   ___   ___   ___   ___   ___  ___
# Third party:
import sgtk
from tank.platform.qt import QtCore, QtGui

# ___   ___   ___   ___   ___   ___  ___
# Project:
HookBaseClass = sgtk.get_hook_baseclass()
# ====================================================================
pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)


class Transfers(HookBaseClass):
    def __init__(self, parent):
        super(Transfers, self).__init__(parent)

        if not hasattr(self, 'metasync'):
            self.metasync = self.load_framework("mty-framework-metasync")

        self._transfers_manager = self.metasync.transfersManager

    @property
    def TM(self):
        return self._transfers_manager

    def download_publish_and_dependencies(self, list_of_items):
        for item in list_of_items:
            if not os.path.exists(item['path']):
                self.TM.ensure_file_is_local(item['path'], item['publish'])

            self.TM.ensure_local_dependencies(item['publish'])

