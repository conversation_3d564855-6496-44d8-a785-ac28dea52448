import sgtk
import pprint
import os
import sys
import shutil
from tank.platform.qt import QtCore, QtGui


pp = pprint.pprint
pf = pprint.pformat

# toolkit will automatically resolve the base class for you
# this means that you will derive from the default hook that comes with the app
HookBaseClass = sgtk.get_hook_baseclass()


class MyActions(HookBaseClass):
    def generate_actions(self, sg_data, actions, ui_area):
        """
        Returns a list of action instances for a particular object.
        The data returned from this hook will be used to populate the
        actions menu.

        :param sg_data: ShotGrid data dictionary with all the standard publish fields.
        :param actions: List of action strings which have been defined in the app configuration.
        :param ui_area: String denoting the UI Area (see above).
        :returns List of dictionaries, each with keys name, params, caption, group and description
        """

        # get the actions from the base class firs
        try:
            action_instances = super(MyActions, self).generate_actions(
                sg_data, actions, ui_area
            )
        except:
            action_instances = []

        if "Download_Attachement_Note" in actions:
            action_instances.append(
                {
                    "name": "Download_Attachement_Note",
                    "params": None,
                    "group": "Pipeline Utils",
                    "caption": "Download note attachments",
                    "description": "Download note attachments.",
                }
            )

        if "Download_Attachement_Version" in actions:
            action_instances.append(
                {
                    "name": "Download_Attachement_Version",
                    "params": None,
                    "group": "Pipeline Utils",
                    "caption": "Download version attachments",
                    "description": "download version attachments.",
                }
            )
        return action_instances

    def execute_action(self, name, params, sg_data):
        """
        Execute a given action. The data sent to this be method will
        represent one of the actions enumerated by the generate_actions method.

        :param name: Action name string representing one of the items returned by generate_actions.
        :param params: Params data, as specified by generate_actions.
        :param sg_data: ShotGrid data dictionary with all the standard publish fields.
        :returns: No return value expected.
        """
        self.shotgun = self.parent.engine.shotgun
        self.loggin = self.parent.engine.logger

        if name == "Download_Attachement_Note":
            self.loggin.info("Note shotgun data: {}".format(pf(sg_data)))
            note_id = sg_data["id"]

            list_dict_attach, external_attachments = self.get_attachments_for_notes(
                note_id
            )

            # create a window for the user to select the folder to save the files
            if list_dict_attach:
                path_folder = self.show_directory_dialog()

            else:
                if external_attachments:
                    self.external_attachments(external_attachments)
                    message = (
                        "This note only has attachments that can be outside of Shotgun"
                    )
                    self.error_popup(message)
                    return

                message = "The note doesn't have attachments"
                self.error_popup(message)
                return

            # check if the user select a folder to download the files
            if not path_folder:
                message = (
                    "Could not select any folder, please try again but select a folder"
                )
                self.error_popup(message)
                return

            path_folder = self.fix_path(path_folder)

            self.parent.engine.logger.info(
                "note attachment download path_folder: {}".format(path_folder)
            )

            # download the files from SG:
            if list_dict_attach:
                for attachment in list_dict_attach:
                    path_file = os.path.join(path_folder, attachment["filename"])
                    path_file = self.fix_path(path_file)
                    self.shotgun.download_attachment(attachment["this_file"], path_file)

            # if some attachments is uploaded in other services show the name and link to download
            if external_attachments:
                self.external_attachments(external_attachments)

            message = (
                "Attachments were downloaded to the following path:"
                "\n\n{}\n"
            ).format(path_folder)
            # self.info_popup(message)
            self.info_popup_with_location(message, path_folder)

        if name == "Download_Attachement_Version":
            # log shotgun information
            self.loggin.info("Version shotgun data: {}".format(pf(sg_data)))

            # get the ids of the notes associated with the version as "notes_ids"
            notes_ids = []
            version_notes = self.shotgun.find_one(
                "Version", [["id", "is", sg_data["id"]]], ["open_notes"]
            )
            notes = version_notes["open_notes"]

            if notes:
                for note in notes:
                    id_note = note["id"]
                    notes_ids.append(id_note)

            if notes_ids:
                list_dict_attach, external_attachments = self.get_attachments_for_notes(
                    notes_ids
                )
            else:
                message = "This version doesn't have notes"
                self.error_popup(message)
                return

            # create a window for the user to select the folder to save the files only if it has attachments to download
            if list_dict_attach:
                path_folder = self.show_directory_dialog()
                folder_name = sg_data["code"]
                folder_name = folder_name.replace(" ", "_")
                folder_name = folder_name.replace(":", "_")
                path_folder = os.path.join(path_folder, folder_name)
                path_folder = self.fix_path(path_folder)
                if os.path.exists(path_folder):
                    shutil.rmtree(path_folder)
                os.makedirs(path_folder)
            else:
                if external_attachments:
                    self.external_attachments(external_attachments)
                    message = "This version only has attachments that can be outside of Shotgun"
                    self.error_popup(message)

                message = "The version doesn't have attachments"
                self.error_popup(message)
                return

            if not path_folder:
                message = (
                    "Could not select any folder, please try again but select a folder"
                )
                self.error_popup(message)
                return

            self.parent.engine.logger.info(
                "version attachment download path_folder: {}".format(path_folder)
            )
            # download attachments from SG

            for attachment in list_dict_attach:

                path_file = os.path.join(path_folder, attachment["filename"])
                path_file = self.fix_path(path_file)
                self.shotgun.download_attachment(attachment["this_file"], path_file)

            # if some attachments is uploaded in other services show the name and link to download
            if external_attachments:
                self.external_attachments(external_attachments)

            message = (
                "Attachments were downloaded to the following path:"
                "\n\n{}\n"
            ).format(path_folder)
            # self.info_popup(message)
            self.info_popup_with_location(message, path_folder)

        else:
            # call base class implementation
            super(MyActions, self).execute_action(name, params, sg_data)

    def fix_path(self, path):
        path = path.replace("\\\\", "/")
        path = path.replace("\\", "/")
        return path

    def show_directory_dialog(self):
        qt_parent = self.parent.engine._get_dialog_parent()
        options = QtGui.QFileDialog.Options()
        options |= QtGui.QFileDialog.ShowDirsOnly
        directory = QtGui.QFileDialog.getExistingDirectory(
            qt_parent, "Select folder", QtCore.QDir.homePath(), options=options
        )
        return directory

    def get_attachments_for_notes(self, notes_ids):
        """This method takes the ids of the notes and looks for all the attachments associated with them.
        notes_ids[list]or[int] = list or unique value of id of the notes
        return[list of dict] = in case of a successful search,
        it returns a list of dictionaries with the information of the attachments
        """

        list_id_attachments = []
        web_attachments = []

        notes = self.shotgun.find("Note", [["id", "in", notes_ids]], ["attachments"])
        for note in notes:
            note_attachments = note["attachments"]
            for file in note_attachments:
                if file:
                    list_id_attachments.append(file["id"])

        if list_id_attachments:
            list_dict_attach = self.shotgun.find(
                "Attachment",
                [["id", "in", list_id_attachments]],
                ["filename", "this_file"],
            )
        else:
            return None, None
        if list_dict_attach:
            for dict_attachemnt in list_dict_attach:
                if dict_attachemnt["this_file"]["link_type"] == "web":
                    list_dict_attach.remove(dict_attachemnt)
                    web_attachments.append(dict_attachemnt)

            if list_dict_attach:
                return list_dict_attach, web_attachments
            else:
                return None, web_attachments
        else:
            return None, None

    def external_attachments(self, external_attachments):
        result = []
        for web_attachmen in external_attachments:
            info_web_dict = {
                "File name": web_attachmen["this_file"]["name"],
                "Url": web_attachmen["this_file"]["url"],
            }
            result.append(info_web_dict)

        external_attachments_messes = (
                "Some attachments cannot be downloaded because they are in other "
                "storage services\n{}"
            ).format(pf(result))
        self.info_popup(external_attachments_messes)

    def error_popup(self, message):
        """This method shows a warning window with some message
        message[str] = This will be the message that the window will show. It must be type str
        """
        self.loggin.info(message)
        msg_error = QtGui.QMessageBox()
        msg_error.setWindowTitle("Warning")
        msg_error.setText(message)
        msg_error.setWindowFlag(QtCore.Qt.WindowStaysOnTopHint)
        msg_error.setIcon(QtGui.QMessageBox.Warning)
        msg_error.raise_()
        msg_error.exec_()

    def info_popup(self, message):
        """This method displays an information window with some message.
        message[str] = This will be the message that the window will display.
        Must be type str"""
        self.loggin.info(message)
        msg_info = QtGui.QMessageBox()
        msg_info.setWindowTitle("Info window")
        msg_info.setText(message)
        msg_info.setWindowFlag(QtCore.Qt.WindowStaysOnTopHint)
        msg_info.setIcon(QtGui.QMessageBox.Information)
        msg_info.raise_()
        msg_info.exec_()

    def open_location(self, location):
        """This method opens the location in the system's default application."""

        system = sys.platform
        if system == "linux":
            cmd = 'xdg-open "%s"' % location
        elif system == "darwin":
            cmd = 'open "%s"' % location
        elif system == "win32":
            # QProcess expects backwards slashes for windows paths, so we need to
            # convert them
            location = location.replace("/", "\\")
            cmd = 'cmd.exe /C start "explorer" "%s"' % location
        else:
            raise Exception("Platform '%s' is not supported." % system)

        self.parent.engine.logger.info("Open Location full command: {}".format(cmd))

        exit_code = QtCore.QProcess.execute(cmd)

    def create_info_dialog(self, message, location):
        """This method creates a dialog with some message and an option to open the
        location in the system's default application."""

        dialog = QtGui.QDialog()
        dialog.setWindowTitle("Info window")

        layout = QtGui.QVBoxLayout(dialog)

        label = QtGui.QLabel(message)
        layout.addWidget(label)

        open_location_button = QtGui.QPushButton("Open location")
        open_location_button.clicked.connect(lambda: self.open_location(location))
        layout.addWidget(open_location_button)

        ok_button = QtGui.QPushButton("OK")
        ok_button.clicked.connect(dialog.accept)
        layout.addWidget(ok_button)

        dialog.setWindowFlag(QtCore.Qt.WindowStaysOnTopHint)
        return dialog

    def info_popup_with_location(self, message, download_location):
        """This method displays an information window with some message and an
        "Open location" button."""
        dialog = self.create_info_dialog(message, download_location)
        dialog.exec_()
