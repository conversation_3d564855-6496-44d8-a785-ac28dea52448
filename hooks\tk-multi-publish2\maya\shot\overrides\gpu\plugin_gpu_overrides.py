# -*- coding: utf-8 -*-
# Standard library:
import os
import re
import json
import traceback
import pprint
# ___   ___   ___   ___   ___   ___  ___
# Third party:
import sgtk
import maya.cmds as cmds
import maya.mel as mel
import pymel.core as pm

pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)
# ___   ___   ___   ___   ___   ___  ___
# Project:
# ====================================================================
HookBaseClass = sgtk.get_hook_baseclass()
pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)
pf = pprint.pformat


class MayaEnvOverridesPublishPlugin(HookBaseClass):
    @property
    def icon(self):
        return os.path.join(self.disk_location, 'icon_gpu_overrides.png')

    @property
    def name(self):
        return "Publish Environment Overrides"

    @property
    def description(self):
        return """
        <p>This plugin publish a definition of transform overrides for enviornments, 
        storing it as a json file describing such transforms.</p>
        """

    @property
    def settings(self):
        maya_publish_settings = {
            "Work Template": {
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "Primary Publish Template": {
                "type": "template",
                "default": None,
                "description": (
                    "Template path for the current scene for publish path. "
                    "Should correspond to a template defined in "
                    "templates.yml."
                ),
            },

            "Publish Template": {
                "type": "template",
                "default": None,
                "description": "Template path for published Scene FBX file. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "Publish Type": {
                "type": "string",
                "default": "",
                "description": "The published file type to register.",
            },
        }

        # inherit the settings from the base publish plugin
        plugin_settings = super(MayaEnvOverridesPublishPlugin,
                                self).settings or {}

        # update the base settings
        plugin_settings.update(maya_publish_settings)

        return plugin_settings

    @property
    def item_filters(self):
        return ["maya.session.gpu.environments"]

    def accept(self, settings, item):
        print("\n" + (">" * 120))
        print('ACCEPTING GPU OVERRIDES')
        print("\n" + (">" * 120))
        return {"accepted": True, 'checked': True}

    def validate(self, settings, item):
        print("\n" + (">" * 120))
        print('VALIDATING GPU OVERRIDES')
        print("\n" + (">" * 120))

        list_of_errors = []

        gpu_node = item.properties['gpu_node']

        transform = gpu_node.getParent()
        if pm.nodeType(transform) != 'transform':
            message = "The parent node of the '%s' gp cache shape, is not a transform"
            list_of_errors.append(message % gpu_node)

        if list_of_errors:
            self.logger.error(list_of_errors)
            raise Exception(list_of_errors)

        return True

    def export_overrides(self, item, publish_entity, export_path):

        gpu_node = item.properties['gpu_node']
        transform = gpu_node.getParent()

        # this is just a patch, hoping that at this poin name matches
        # but we should properly get the breakdown name
        # When we shitch to proper overrides, whcih are maya references
        # then we will be able to just use the namespace
        breakdown_name = transform.name()

        asset_name = publish_entity['entity.Asset.code']
        asset_type = publish_entity['entity.Asset.sg_asset_type']

        matrix = pm.xform(
            transform, query=True, matrix=True, worldSpace=False
        )

        rotatePivot = pm.xform(
            transform, query=True, rotatePivot=True, worldSpace=False
        )

        scalePivot = pm.xform(
            transform, query=True, scalePivot=True, worldSpace=False
        )

        schema = {
            "parent": "root",
            "name": "transform",
            "parameters": {
                "xform": {
                    "matrix": matrix,
                    "rotatePivot": rotatePivot,
                    "scalePivot": scalePivot, }
            },
            "type": "Group",
            "children": [],
            "metadata": {'mighty': {'breakdown_name': breakdown_name,
                                    'asset_name': asset_name,
                                    'asset_type': asset_type}}
        }

        with open(export_path, 'w') as export_file:
            json.dump(schema, export_file, indent=4)

    def publish(self, settings, item):

        gpu_node = item.properties['gpu_node']
        transform = gpu_node.getParent()

        matrix = pm.xform(
            transform, query=True, matrix=True, worldSpace=False
        )
        rotatePivot = pm.xform(
            transform, query=True, rotatePivot=True, worldSpace=False
        )
        scalePivot = pm.xform(
            transform, query=True, scalePivot=True, worldSpace=False
        )

        diffScalePivot = (scalePivot != [0.0, 0.0, 0.0])
        diffRotatePivot = (rotatePivot != [0.0, 0.0, 0.0])
        diffMatrix = (
                matrix != [1.0, 0.0, 0.0, 0.0,
                           0.0, 1.0, 0.0, 0.0,
                           0.0, 0.0, 1.0, 0.0,
                           0.0, 0.0, 0.0, 1.0]
        )

        publish_entity = item.properties['publish_entity']
        asset_name = publish_entity['entity.Asset.code']

        if not any([diffMatrix, diffRotatePivot, diffScalePivot]):
            message = ("Ignoring publish of environment overrides for %s.\n"
                       "No differences found in the current environment")
            self.logger.debug(message % asset_name)
            return

        # Get the current format to be exported from settings
        published_file_type = settings["Publish Type"].value

        scene_path = os.path.abspath(pm.sceneName())
        work_template_name = settings["Work Template"].value
        work_template = self.parent.engine.get_template_by_name(
            work_template_name)
        fields = work_template.get_fields(scene_path)
        publish_version = fields["version"]

        # create the publish path by applying the fields
        # with the publish template:
        publish_template_name = settings["Publish Template"].value
        publish_template = self.parent.engine.get_template_by_name(
            publish_template_name)
        fields['Asset'] = asset_name

        publish_path = publish_template.apply_fields(fields)

        # ensure the publish folder exists:
        publish_folder = os.path.dirname(publish_path)
        self.parent.ensure_folder_exists(publish_folder)

        scene_pub_template_name = settings["Primary Publish Template"].value
        scene_pub_template = self.parent.engine.get_template_by_name(
            scene_pub_template_name)
        primary_publish_path = scene_pub_template.apply_fields(fields)

        # Export the environment overrides into a json file
        self.export_overrides(item, publish_entity, publish_path)

        publish_name = self._regex_replace(
            regex=r'_v\d{3}',
            source_string=os.path.basename(publish_path),
            replace_string=''
        )
        publish_name = self._regex_replace(
            regex=r'e\d{3}_\w{3}\d{3}_\d{4}_\w{3}_',
            source_string=publish_name,
            replace_string=''
        )

        args = {
            "tk": self.parent.engine.sgtk,
            "context": item.context,
            "comment": item.description,
            "path": publish_path,
            "name": publish_name,
            "version_number": publish_version,
            "thumbnail_path": item.get_thumbnail_as_path(),
            "task": self.parent.engine.context.task,
            "dependency_paths": [primary_publish_path],
            "published_file_type": published_file_type,
            "sg_fields": {"sg_status_list": 'rev'},
        }

        publish = sgtk.util.register_publish(**args)

        item.properties['sg_publish_data'] = publish
        item.properties['sg_publish_path'] = publish["path"]["local_path"]

        return True

    #  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
    def _regex_replace(self, regex, source_string, replace_string):
        result = ''
        pattern = re.compile(regex)
        result = pattern.sub(repl=replace_string, string=source_string)
        return result

    # ---------------------------------------------------------------------------

    def finalize(self, settings, item):
        pass
