################################################################################
#
# Copyright (c) 2024 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
################################################################################

import os
import json
import sgtk

from pprint import pformat

HookBaseClass = sgtk.get_hook_baseclass()


class ToolBoxScriptsHook(HookBaseClass):
    def get_list_of_scripts(self):
        """
        Find all scripts for the current project.

        :return: A list of all the scripts paths.
        """
        self.engine = self.parent.engine

        project_data = self.get_project_data_from_SG()
        self.engine.logger.info("About to find scripts locations")
        script_locations_list = self.find_scripts_locations(project_data)
        self.engine.logger.info("About to find scripts files")
        result = self.find_script_files(script_locations_list)

        return result

    def get_project_data_from_SG(self):
        """
        Returns a dictionary of project data from SG relevant to a
        shot of the following form:

            {
                'code': (str),
                 'id': (int),
             }
        """

        project_data = {}

        filters = [
            ["id", "is", self.engine.context.project["id"]],
        ]
        fields = [
            "code",
            "id",
        ]

        project_data = self.engine.shotgun.find_one(
            entity_type="Project", filters=filters, fields=fields
        )

        return project_data

    def resolve_scripts_location(
        self, hooks_location, project_code, engine_name, entity_type=None
    ):
        """
        Resolve the different scripts locations for the current project, engine and
        entity.

        The locations are found in the following order of preference:

        1. dcc scripts location: hooks/{dcc_name}/scripts
        2. project scripts location: hooks/{dcc_name}/{project_code}
        3. entity scripts location: hooks/{dcc_name}/{entity_type}

        All the found locations are returned in a dictionary.

        :param hooks_location: path to the hooks folder
        :type hooks_location: str
        :param project_code: code of the current project
        :type project_code: str
        :param engine_name: name of the dcc engine
        :type engine_name: str
        :param entity_type: type of the context entity
        :type entity_type: str
        :return: a dictionary of paths to the available scripts locations
        :rtype: dict
        """
        all_locations = {}

        # find dcc scripts locations ---------------------------------------------------
        scripts_dcc_location = os.path.join(
            hooks_location,
            "mty-multi-scriptstoolbox",
            engine_name,
        )

        # find project scripts locations -----------------------------------------------
        scripts_dcc_project_location = os.path.join(
            scripts_dcc_location, project_code,
        )

        # find entity scripts locations ------------------------------------------------
        scripts_dcc_entity_location = scripts_dcc_location
        if entity_type:
            scripts_dcc_entity_location = os.path.join(
                scripts_dcc_location, entity_type
            )

        # dcc location -----------------------------------------------------------------
        if scripts_dcc_location and os.path.exists(scripts_dcc_location):
            self.engine.logger.info("scripts_dcc_location exists.")
            all_locations["dcc"] = scripts_dcc_location
        else:
            self.engine.logger.info("scripts_dcc_location doesn't exist.")

        # project location -------------------------------------------------------------
        if scripts_dcc_project_location and os.path.exists(
            scripts_dcc_project_location
        ):
            self.engine.logger.info("scripts_dcc_project_location exists.")
            all_locations["project"] = scripts_dcc_project_location
        else:
            self.engine.logger.info("scripts_dcc_project_location doesn't exist.")

        # entity location --------------------------------------------------------------
        if scripts_dcc_entity_location and os.path.exists(scripts_dcc_entity_location):
            self.engine.logger.info("scripts_dcc_entity_location exists.")
            all_locations["entity"] = scripts_dcc_entity_location
        else:
            self.engine.logger.info("scripts_dcc_entity_location doesn't exist.")

        self.engine.logger.info("scripts locations:\n{}".format(pformat(all_locations)))

        return all_locations

    def find_scripts_locations(self, project_data):
        """
        Finds all available scripts locations for the current project, engine and entity.

        The locations are found in the following order of preference:

        1. dcc scripts location: hooks/{dcc_name}/scripts
        2. project scripts location: hooks/{dcc_name}/{project_code}
        3. entity scripts location: hooks/{dcc_name}/{entity_type}

        All the found locations are returned in a list.

        :param project_data: SG project data
        :type project_data: dict
        :return: A list of paths to the available scripts locations
        :rtype: list
        """
        self.engine.logger.info("Finding scripts locations")

        project_code = project_data.get("code")
        self.engine.logger.info("project_code: {}".format(project_code))
        engine_name = self.engine.name
        engine_name = engine_name.replace("tk-", "")

        hooks_location = os.path.dirname(self.disk_location)
        self.engine.logger.info("hooks_location: {}".format(hooks_location))

        scripts_locations = []

        # find all available locations
        entity_type = self.engine.context.entity.get("type")
        all_locations = self.resolve_scripts_location(
            hooks_location, project_code, engine_name, entity_type
        )

        if not all_locations:
            return []

        for location_type, location in all_locations.items():
            scripts_locations.append(location)

        return scripts_locations

    def find_script_files(self, script_locations_list):
        """Find Script files in a directory"""
        if not script_locations_list:
            return None

        found_script_files = []

        # load overrides framework -----------------------------------------------------
        overrides_framework = self.load_framework("mty-framework-valueoverrides")

        # load valid steps for exporting fbx meshes from overrides or from settings
        default_value_supported_scripts_extensions = "mty.multi.scriptsToolbox.suported_extensions_per_engine"

        context = self.engine.context
        task = context.task
        all_supported_extensions = overrides_framework.get_value(
            default_value_supported_scripts_extensions, link=task
        )
        if all_supported_extensions:
            all_supported_extensions = json.loads(all_supported_extensions)
            self.engine.logger.info(
                "Got all supported_extensions from overrides:\n{}".format(
                    pformat(all_supported_extensions)
                )
            )
        else:
            all_supported_extensions = {
                "tk-aftereffects": [".js", ".jsx", ".jsxbin"],
                "tk-harmony": [".js", ".jsx"],
                "tk-photoshopcc": [".js", ".jsx", ".jsxbin"],
                "tk-maya": [".py", ".mel"],
                "tk-houdini": [".py"],
                "tk-krita": [".py"],
            }
            self.engine.logger.info(
                "Got all supported_extensions from hook:\n{}".format(
                    pformat(all_supported_extensions)
                )
            )

        engine_supported_extensions = all_supported_extensions.get(
            self.engine.name, []
        )
        self.engine.logger.info(
            "Engine supported_extensions: {}".format(engine_supported_extensions)
        )

        for location in script_locations_list:
            if not os.path.exists(location):
                continue
            for file_ in os.listdir(location):
                _, extension = os.path.splitext(file_)
                if extension in engine_supported_extensions:
                    found_script_files.append(os.path.join(location, file_))

        return found_script_files
