# Copyright (c) 2021 Autodesk, Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Autodesk, Inc.

import os
import re
import sgtk
import fileseq
import traceback
import pprint
# from pprint import pformat as pf

pp = pprint.pprint
pf = pprint.pformat

HookBaseClass = sgtk.get_hook_baseclass()


class BreakdownSceneOperations(HookBaseClass):
    """
    Breakdown operations for After Effects.

    This implementation handles detection of composition items, footage items, etc.
    """

    def scan_scene(self):
        """
        Scan the After Effects project and return a list of items to be operated on.

        The return data structure is a list of dictionaries. Each scene reference
        that is returned should be represented by a dictionary with keys:

        - "node_name": The name of the 'node' that is to be operated on.
        - "node_type": The object type that this is. This is later passed to the
          update method so that it knows how to handle the object.
        - "path": Path on disk to the referenced object.
        - "extra_data": Optional key to pass some extra data to the update method.

        <PERSON><PERSON><PERSON> will scan the list of items, see if any of the objects matches
        a published file, and try to determine if there is a more recent version
        available. Any such versions are then displayed in the UI as out of date.
        """

        self.parent.engine.logger.info("The scan function started. ".ljust(88, "-"))

        adobe = self.parent.engine.adobe

        project_items = adobe.app.project.items
        self.parent.engine.logger.info(f"Project items scan: {project_items}")

        # Debug: Print out type of project_items and total count
        total_items = list(self.parent.engine.iter_collection(project_items))
        self.parent.engine.logger.info(f"Total project items count: {len(total_items)}")

        pattern = re.compile(
            r"(?P<head>.)*"
            r"(?P<version>v\d{3})[\._]"
            r"(?P<seq_token>\d{4,5})?"
            r"(?P<tail>.*)"
        )

        items = []

        for i, item in enumerate(self.parent.engine.iter_collection(project_items)):
            # Extensive logging for each item
            self.parent.engine.logger.info(f"Processing item {i}:")
            self.parent.engine.logger.info(f"Item Name: {item.name}")
            self.parent.engine.logger.info(f"Item Type Name: {item.typeName}")

            if item.typeName == "Footage":
                self.parent.engine.logger.info("-" * 80)
                self.parent.engine.logger.info(
                    f"Working with footage item: {item.name}"
                )

                # Detailed file path logging
                try:
                    file_path = item.file.fsName
                    self.parent.engine.logger.info(f"File Path: {file_path}")
                    self.parent.engine.logger.info(f"File Path Exists: {os.path.exists(file_path) if file_path else 'No path'}")
                except Exception as e:
                    self.parent.engine.logger.warning(f"Error accessing file path: {e}")
                    file_path = ""

                # Detailed comment logging
                try:
                    layer_comment = item.comment
                    self.parent.engine.logger.info(f"Layer Comment: {layer_comment}")

                    # Try source comment if layer comment is empty
                    if not layer_comment:
                        try:
                            layer_comment = item.source.comment
                            self.parent.engine.logger.info(f"Source Comment: {layer_comment}")
                        except Exception as e:
                            self.parent.engine.logger.warning(f"Could not get source comment: {e}")
                except Exception as e:
                    self.parent.engine.logger.warning(f"Error accessing comment: {e}")
                    layer_comment = ""

                # Determine item path
                item_path = None
                match = None

                # Try file path first
                if file_path:
                    self.parent.engine.logger.info(f"Attempting to match file path: {file_path}")
                    match = re.match(pattern, file_path)
                    if match:
                        item_path = file_path
                        self.parent.engine.logger.info(f"Matched file path: {item_path}")
                    else:
                        self.parent.engine.logger.warning(f"File path did not match pattern: {file_path}")

                # If no match from file path, try comment
                if not item_path and layer_comment:
                    self.parent.engine.logger.info(f"Attempting to match comment: {layer_comment}")
                    match = re.match(pattern, layer_comment)
                    if match:
                        item_path = layer_comment
                        self.parent.engine.logger.info(f"Matched comment path: {item_path}")
                    else:
                        self.parent.engine.logger.warning(f"Comment did not match pattern: {layer_comment}")

                # If we still don't have a path, log and skip
                if not item_path:
                    self.parent.engine.logger.warning(f"Could not determine path for item: {item.name}")
                    continue

                # Create item dictionary
                item_dict = {
                    "node_name": item.name,
                    "path": item_path,
                    "extra_data": {"footage_item": item},
                }

                # Determine node type
                try:
                    if match and match.groupdict().get("seq_token"):
                        item_dict["node_type"] = item.typeName + "_fileSequence"
                    elif layer_comment and layer_comment.endswith(".jsonx"):
                        if "_camera_" in layer_comment:
                            item_dict["node_type"] = item.typeName + "_cameraTransform"
                        elif "_peg_" in layer_comment:
                            item_dict["node_type"] = item.typeName + "_tracker"
                    else:
                        item_dict["node_type"] = item.typeName + "_singleFile"
                except Exception as e:
                    self.parent.engine.logger.warning(f"Error determining node type: {e}")
                    item_dict["node_type"] = item.typeName + "_unknown"

                # Log final item dictionary
                self.parent.engine.logger.info(f"Prepared item dictionary: {pf(item_dict)}")

                # Add to items list
                if item_dict not in items:
                    items.append(item_dict)
                    self.parent.engine.logger.info(f"Added item {item.name} to breakdown list")
                else:
                    self.parent.engine.logger.info(f"Item {item.name} already in breakdown list")

        # Final logging
        self.parent.engine.logger.info(f"Found {len(items)} items")
        self.parent.engine.logger.info(f"Items:\n{pf(items)}")

        self.parent.engine.logger.info("End of scan function.".ljust(88, "-"))

        return items

    def update(self, item):
        """
        Perform replacements given a number of scene items passed from the app.

        Once a selection has been performed in the main UI and the user clicks
        the update button, this method is called.

        :param item: Dictionary on the same form as was generated by the scan_scene hook above.
                     The path key now holds the path that the node should be updated *to*
                     rather than the current path.
        """

        tk = self.parent.engine.sgtk
        engine = self.parent.engine

        node_name = item.get("node_name")
        node_type = item.get("node_type")
        path = item.get("path")
        extra_data = item.get("extra_data")
        sg_publish_data = item.get("sg_data")
        old_path = extra_data.get("old_path")

        path = self.fix_path(path)
        old_path = self.fix_path(old_path)

        footage_obj = extra_data.get("footage_item")

        self.parent.engine.logger.info(
            f"The update function started for item {node_name}.".ljust(120, "-")
        )
        # self.parent.engine.logger.info(
        #     "Item {} path: {}".format(node_name, path)
        # )
        self.parent.engine.logger.debug("Item:\n{}".format(pf(item)))

        # ensure metasync framework is available, but only load it once
        if "%" in path or "_fileSequence" in node_type:
            try:
                seq_first_frame = self.get_sequence_first_frame(path)
                if not os.path.exists(seq_first_frame):
                    local_file_exists = False
                else:
                    local_file_exists = True
            except:
                local_file_exists = False
        else:
            if not os.path.exists(path):
                local_file_exists = False
            else:
                local_file_exists = True

        # self.parent.engine.logger.info(
        #     "local_file_exists: {}, path: {}".format(local_file_exists, path)
        # )

        if not local_file_exists:
            if not hasattr(self, 'metasync'):
                self.metasync = self.load_framework("mty-framework-metasync")
            transfersManager = self.metasync.transfersManager

            retries = 0
            while not local_file_exists and retries < 3:
                publishes = sgtk.util.find_publish(tk, [path])
                for key in publishes.keys():
                    # self.parent.engine.logger.info(
                    #     "publishes[key]: {}".format(publishes[key])
                    # )
                    transfersManager.ensure_file_is_local(path, publishes[key])
                    transfersManager.ensure_local_dependencies(publishes[key])

                retries += 1
                # resolve first frame in the case of a sequence
                if "%" in path or "_fileSequence" in node_type:
                    seq_first_frame = self.get_sequence_first_frame(path)
                    if os.path.exists(seq_first_frame):
                        local_file_exists = True
                else:
                    if os.path.exists(path):
                        local_file_exists = True

        pattern = re.compile(
            r"(?P<head>.*)[\._]"
            r"(?P<version>v\d{3})[\._]"
            r"(?P<seq_token>(?:\d{4,5}|%(?P<padding>\d{2})d))?"
            r"(?P<tail>.*)"
        )
        match_old_path = re.match(pattern, old_path)
        match_new_path = re.match(pattern, path)

        if not match_old_path:
            self.parent.engine.logger.warning("Couldn't get a regex match for old path.")
        if not match_new_path:
            self.parent.engine.logger.warning("Couldn't get a regex match for new path.")

        if "_fileSequence" in node_type:  # --------------------------------------------
            self.parent.engine.logger.info("Found sequence item.")

            try:
                fileseq_obj = fileseq.findSequenceOnDisk(path)
            except:
                self.parent.engine.logger.error(traceback.format_exc())

            if not fileseq_obj:
                self.parent.engine.logger.info("Couldn't get fileseq_obj")
                raise RuntimeError("Failed to find file sequence on disk.")

            # Getting start and end frames from fileseq_obj
            first_frame = fileseq_obj.start()
            last_frame = fileseq_obj.end()

            self.parent.engine.logger.info(
                "Sequence range: {}-{}".format(first_frame, last_frame)
            )

            if match_old_path and match_new_path:
                seq_token = match_new_path.groupdict().get("seq_token")
                re_padding = match_new_path.groupdict().get("padding")
                if re_padding:
                    padding = int(re_padding)
                else:
                    padding = len(seq_token)
                ae_path = path.replace(seq_token, str(first_frame).zfill(padding))
                new_name = node_name.replace(
                    match_old_path.groupdict().get("version"),
                    match_new_path.groupdict().get("version")
                )

                self.parent.engine.logger.debug(
                    "ae_path (resolved new path): {}".format(ae_path)
                )

                file_obj = engine.adobe.File(ae_path)
                footage_obj.replaceWithSequence(file_obj, True)
                footage_obj.name = new_name

        elif "_singleFile" in node_type:  # --------------------------------------------
            self.parent.engine.logger.info("Found single file item.")

            file_obj = engine.adobe.File(path)
            footage_obj.replace(file_obj)
            self.parent.engine.logger.debug(
                "ae_path (new path for replacement): {}".format(path)
            )

            if match_old_path and match_new_path:
                new_name = node_name.replace(
                    match_old_path.groupdict().get("version"),
                    match_new_path.groupdict().get("version")
                )
                footage_obj.name = new_name

        elif "_cameraTransform" in node_type or "_tracker" in node_type:  # ------------
            self.parent.engine.logger.info(f"Found {node_type} transform file item.")
            children_layers = self.get_children_layers(footage_obj)

            # Ensure we delete all the old layers in all old compositions
            for comp in children_layers.keys():
                for layer in children_layers[comp]["children"]:
                    try:
                        layer.remove()
                    except:
                        pass

            # we also delete the transforms solid (originally found footage_obj)
            try:
                footage_obj.remove()
            except:
                pass

            # impport the updated camera using the existing hook method from the
            # tk-multi-loader app
            version = match_new_path.groupdict().get("version")

            if "_cameraTransform" in node_type:
                self.parent.engine.logger.info("About to create import_cam_command")
                # create the command
                import_cam_cmd = self.parent.execute_hook_expression(
                    "{config}/tk-multi-loader2/tk-aftereffects_actions.py",
                    "create_import_camera_command",
                    file_path=path,
                    version=f"_{version}",
                )
                self.parent.engine.logger.debug(
                    f"import_cam_cmd:\n\n{import_cam_cmd}\n\n"
                )

                # execute the command
                self.parent.engine.logger.info("About to execute import_cam_command")
                self.parent.execute_hook_expression(
                    "{config}/tk-multi-loader2/tk-aftereffects_actions.py",
                    "execute_import_cam_command",
                    import_cam_cmd=import_cam_cmd
                )

            if "_tracker" in node_type:
                self.parent.engine.logger.info("About to create import_peg_command")
                # create the command
                import_peg_cmd = self.parent.execute_hook_expression(
                    "{config}/tk-multi-loader2/tk-aftereffects_actions.py",
                    "create_import_peg_command",
                    file_path=path,
                    version=f"_{version}",
                )
                self.parent.engine.logger.debug(
                    f"import_peg_cmd:\n\n{import_peg_cmd}\n\n"
                )

                # execute the command
                self.parent.engine.logger.info("About to execute import_peg_command")
                self.parent.execute_hook_expression(
                    "{config}/tk-multi-loader2/tk-aftereffects_actions.py",
                    "execute_import_peg_command",
                    import_peg_cmd=import_peg_cmd
                )

        self.parent.engine.logger.info(
            f"The update function has ended for item {node_name}."
        )

    def get_sequence_first_frame(self, path):
        """
        Given a sequence path, get the path to the first frame of the sequence
        """

        # resolve first frame in the case of a sequence
        fileseq_obj = fileseq.findSequenceOnDisk(path)
        first_frame = list(fileseq_obj)[0]
        self.parent.engine.logger.info(
            f"First frame of sequence: {first_frame}"
        )

        return first_frame

    def collect_all_compositions_in_project(self):
        compositions = []

        adobe = self.parent.engine.adobe

        project_items = adobe.app.project.items

        for i, item in enumerate(self.parent.engine.iter_collection(project_items)):
            if item.typeName == "Composition":
                compositions.append(item)

        return compositions

    def get_children_layers(self, parent_layer):
        self.parent.engine.logger.info(
            "entering get_children_layers method".ljust(120, "-")
        )
        adobe = self.parent.engine.adobe
        self.parent.engine.logger.info("adobe: {}".format(adobe))

        compositions = self.collect_all_compositions_in_project()
        self.parent.engine.logger.info("compositions: {}".format(compositions))

        children_layers = {}

        for comp in compositions:
            comp_layers = comp.layers
            self.parent.engine.logger.info(
                f"Working with comp: {comp.name}".ljust(120, "=")
            )
            self.parent.engine.logger.debug(f"comp_layers: {comp_layers}")
            for item in self.parent.engine.iter_collection(comp_layers):
                self.parent.engine.logger.debug(f"item: {item}".ljust(120, "-"))
                try:
                    item_parent = item.parent
                    item_parent_name = item.parent.name or "No parent"
                except:
                    item_parent = "No parent"
                    item_parent_name = "No parent"

                self.parent.engine.logger.debug(
                    "\nitem_parent_name: {}\nparent_layer.name: {}".format(
                        item_parent_name, parent_layer.name
                    )
                )

                # in case we found a child layer
                if item_parent_name == parent_layer.name:
                    self.parent.engine.logger.info("Found child layer")
                    comp_name = comp.name
                    self.parent.engine.logger.debug(f"comp_name: {comp_name}")

                    # init dictionary
                    if not children_layers.get(comp_name):
                        children_layers[comp_name] = {}
                    # self.parent.engine.logger.info(
                    #     "children_layers[comp_name]: {}".format(
                    #         children_layers[comp_name]
                    #     )
                    # )

                    # init children list
                    if not children_layers[comp_name].get("children"):
                        children_layers[comp_name]["children"] = []
                    # self.parent.engine.logger.info(
                    #     "children_layers[comp_name]['children']: {}".format(
                    #         children_layers[comp_name]['children']
                    #     )
                    # )
                    children_layers[comp_name]["children"].append(item)

                    # store comp object
                    if not children_layers[comp_name].get("composition"):
                        children_layers[comp_name]["composition"] = comp
                    # self.parent.engine.logger.info(
                    #     "children_layers[comp_name]['composition']: {}".format(
                    #         children_layers[comp_name]['composition']
                    #     )
                    # )

                    # self.parent.engine.logger.info(
                    #     "item: {} parent: {}".format(item.name, item_parent)
                    # )

                else:
                    self.parent.engine.logger.info(f"Skipped layer: {item.name}")

        self.parent.engine.logger.info(f"children_layers:\n{pf(children_layers)}")
        self.parent.engine.logger.info(
            "finishing get_children_layers method".ljust(120, "-")
        )

        return children_layers

    def fix_path(self, path):
        """Replace all backward slashes with forward slashes."""

        path = path.replace("\\\\", "/")
        path = path.replace("\\", "/")

        return path
