import os
import sys
import pprint
from hashlib import md5

import tank
from tank import Hook


class BeforeRegisterPublish(Hook):
    def execute(self, shotgun_data, context, **kwargs):
        if tank.platform.current_engine():
            # load mty-framework-metasync
            # This is only posible because of a hack in engine_init
            engine = tank.platform.current_engine()
            metasync = engine.custom_frameworks.get(
                "mty-framework-metasync"
                ) or self.load_framework("mty-framework-metasync")

            engine.logger.debug(
                "Data recieved by before_register_publish:\n{}".format(
                    pprint.pformat(shotgun_data)
                )
            )

            if metasync:
                shotgun_data["sg_source_location"] = metasync.currentLocation

                # this will recalculate the file size and checksum
                # from the already copied local file
                syncLogsManager = metasync.syncLogsManager
                try:
                    syncLogsManager.get_publish_size_and_checksum(
                        shotgun_data, engine.logger
                    )
                except:
                    syncLogsManager.get_publish_size_and_checksum(shotgun_data)

            # define source engine for current publish file
            shotgun_data["sg_source_engine"] = engine.name

        return shotgun_data
