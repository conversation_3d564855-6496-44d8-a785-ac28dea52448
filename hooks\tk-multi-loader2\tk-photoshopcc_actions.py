# Copyright (c) 2015 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

"""
Hook that loads defines all the available actions, broken down by publish type.
"""
import os
import pprint

import sgtk
from tank_vendor import six

HookBaseClass = sgtk.get_hook_baseclass()

pp = pprint.pprint
pf = pprint.pformat

# Name of available actions. Corresponds to both the environment config values and the action instance names.
_ADD_AS_A_LAYER = "add_as_a_layer"
_OPEN_FILE = "open_file"

class PhotoshopCCActions(HookBaseClass):

    ##############################################################################################################
    # public interface - to be overridden by deriving classes

    def generate_actions(self, sg_publish_data, actions, ui_area):
        """
        Returns a list of action instances for a particular publish.
        This method is called each time a user clicks a publish somewhere in the UI.
        The data returned from this hook will be used to populate the actions menu for a publish.

        The mapping between Publish types and actions are kept in a different place
        (in the configuration) so at the point when this hook is called, the loader app
        has already established *which* actions are appropriate for this object.

        The hook should return at least one action for each item passed in via the
        actions parameter.

        This method needs to return detailed data for those actions, in the form of a list
        of dictionaries, each with name, params, caption and description keys.

        Because you are operating on a particular publish, you may tailor the output
        (caption, tooltip etc) to contain custom information suitable for this publish.

        The ui_area parameter is a string and indicates where the publish is to be shown.
        - If it will be shown in the main browsing area, "main" is passed.
        - If it will be shown in the details area, "details" is passed.
        - If it will be shown in the history area, "history" is passed.

        Please note that it is perfectly possible to create more than one action "instance" for
        an action! You can for example do scene introspection - if the action passed in
        is "character_attachment" you may for example scan the scene, figure out all the nodes
        where this object can be attached and return a list of action instances:
        "attach to left hand", "attach to right hand" etc. In this case, when more than
        one object is returned for an action, use the params key to pass additional
        data into the run_action hook.

        :param sg_publish_data: Shotgun data dictionary with all the standard publish fields.
        :param actions: List of action strings which have been defined in the app configuration.
        :param ui_area: String denoting the UI Area (see above).
        :returns List of dictionaries, each with keys name, params, caption and description
        """


        app = self.parent

        app.logger.info("Generate actions called for UI element: '{}'.".format(ui_area))

        action_instances = []

        if _ADD_AS_A_LAYER in actions:
            action_instances.append(
                {
                    "name": _ADD_AS_A_LAYER,
                    "params": None,
                    "caption": "Add as a Layer",
                    "description": "Adds a layer referencing the image to the current document.",
                }
            )

        if _OPEN_FILE in actions:
            action_instances.append(
                {
                    "name": _OPEN_FILE,
                    "params": None,
                    "caption": "Open File",
                    "description": "This will open the file.",
                }
            )

        if "download_publish" in actions:
            action_instances.append(
                {
                    "name": "download_publish",
                    "params": None,
                    "caption": "Download published file",
                    "description": (
                        "This will verify if the file exists locally on disk, "
                        "if not, it will be downloaded."
                    )
                }
            )

        return action_instances

    def execute_multiple_actions(self, actions):
        """
        Executes the specified action on a list of items.

        The default implementation dispatches each item from ``actions`` to
        the ``execute_action`` method.

        The ``actions`` is a list of dictionaries holding all the actions to execute.
        Each entry will have the following values:

            name: Name of the action to execute
            sg_publish_data: Publish information coming from Shotgun
            params: Parameters passed down from the generate_actions hook.

        .. note::
            This is the default entry point for the hook. It reuses the ``execute_action``
            method for backward compatibility with hooks written for the previous
            version of the loader.

        .. note::
            The hook will stop applying the actions on the selection if an error
            is raised midway through.

        :param list actions: Action dictionaries.
        """
        for single_action in actions:
            name = single_action["name"]
            sg_publish_data = single_action["sg_publish_data"]
            params = single_action["params"]

            try:
                self.execute_action(name, params, sg_publish_data)
                # play sound from config hook 'play_sounds'
                self.parent.logger.info("Playing sound, reached end of execute_action")
                self.parent.engine.execute_hook_expression(
                    "{config}/notifications.py",
                    "success_sound",
                )
            except:
                # play sound from config hook 'play_sounds'
                self.parent.logger.info("Playing sound, error raised in execute_action")
                self.parent.engine.execute_hook_expression(
                    "{config}/notifications.py",
                    "error_sound",
                )


    def execute_action(self, name, params, sg_publish_data):
        """
        Execute a given action. The data sent to this be method will
        represent one of the actions enumerated by the generate_actions method.

        :param name: Action name string representing one of the items returned by
        generate_actions.
        :param params: Params data, as specified by generate_actions.
        :param sg_publish_data: Shotgun data dictionary with all the standard publish fields.
        :returns: No return value expected.
        """

        app = self.parent

        app.logger.info("Execute action called for action: '{}'.".format(name))

        # resolve path
        path = self.get_publish_path(sg_publish_data)

        # first ensure file is local, always
        self.ensure_file_is_local(path, sg_publish_data)

        # toolkit uses utf-8 encoded strings internally and the After Effects API
        # expects unicode so convert the path to ensure filenames containing complex
        # characters are supported
        path = six.ensure_text(path)

        # if not os.path.exists(path):
        #     raise IOError("File not found on disk - '%s'" % path)

        # Default actions _open_file() and _place_file() are being gathered from the
        # original tk-multiloader hook: {self}/tk-photoshopcc_actions.py
        # ------------------------------------------------------------------------------
        if name == _OPEN_FILE:
            self._open_file(path, sg_publish_data)
        if name == _ADD_AS_A_LAYER:
            self._place_file(path, sg_publish_data)
        # ------------------------------------------------------------------------------

        if name == "download_publish":
            self.ensure_file_is_local(path, sg_publish_data)

    ####################################################################################
    # helper methods which can be subclassed in custom hooks to fine tune the behaviour
    # of things
'''
    # moved to /tk-multi-loader2/tk-multi-loader2_multi_transfer_actions.py

    def ensure_file_is_local(self, path, sg_publish_data):

        app = self.parent

        if not hasattr(self, 'metasync'):
            self.metasync = self.load_framework("mty-framework-metasync")

        transfersManager = self.metasync.transfersManager
        if "%" in path:
            dirname = os.path.dirname(path)
            if os.path.exists(dirname) and self._collect_sequenced_files(path):
                transfersManager.ensure_local_dependencies(sg_publish_data)
                return path
        else:
            if os.path.exists(path):
                transfersManager.ensure_local_dependencies(sg_publish_data)
                return path

        transfersManager.ensure_file_is_local(path, sg_publish_data)
        transfersManager.ensure_local_dependencies(sg_publish_data)

        return path
'''
