# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

################################################################################

includes:
- ../app_locations.yml
- ../engine_locations.yml
- ./tk-flame-export.yml
- ./tk-multi-loader2.yml
- ./tk-multi-shotgunpanel.yml

################################################################################

# project
settings.tk-flame.project:
  apps:
    tk-flame-review:
      location: "@apps.tk-flame-review.location"
    tk-flame-export: "@settings.tk-flame-export"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel"
    tk-multi-loader2: "@settings.tk-multi-loader2.flame"
    mty-executeaction-ensure-folders:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Ensure Tasks Folders"
      action_hook: "{config}/tk-shotgun-executeaction/create_folders_for_assigned_tasks.py"
      sg_extended_fields: {}
      allowed_entities: ['Shot', 'Sequence', 'Asset', 'Project']
  location: "@engines.tk-flame.location"
  context_menu: [
    { name : "Load...", app_instance: "tk-multi-loader2", display_name: "Shotgun Loader..."},
    { name : "Shotgun Panel...", app_instance: "tk-multi-shotgunpanel", display_name: "Shotgun Panel..."}
  ]

# shot
settings.tk-flame.shot:
  apps:
    tk-flame-review:
      location: "@apps.tk-flame-review.location"
    tk-flame-export: "@settings.tk-flame-export"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel"
    tk-multi-loader2: "@settings.tk-multi-loader2.flame"
  location: "@engines.tk-flame.location"
  context_menu: [
    { name : "Load...", app_instance: "tk-multi-loader2", display_name: "Shotgun Loader..."},
    { name : "Shotgun Panel...", app_instance: "tk-multi-shotgunpanel", display_name: "Shotgun Panel..."}
  ]
