########################################################################################
#
# Copyright (c) 2024 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio.
########################################################################################

import os
import shutil
import pprint

import sgtk
from sgtk.util.filesystem import ensure_folder_exists

HookBaseClass = sgtk.get_hook_baseclass()

class HarmonyColorPalettesPublishPlugin(HookBaseClass):
    """
    Plugin for publishing an open Harmony session.
    This hook relies on functionality found in the base file publisher hook in
    the publish2 app and should inherit from it in the configuration. The hook
    setting for this plugin should look something like this::
        hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py"
    """

    # NOTE: The plugin icon and name are defined by the base file plugin.
    @property
    def icon(self):
        """
        Path to an png icon on disk
        """
        # look for icon one level up from this hook's folder in "icons" folder
        return os.path.join(
            self.disk_location,
            os.pardir,
            "icons",
            "color_palette.png"
        )

    @property
    def description(self):
        """
        Verbose, multi-line description of what the plugin does. This can
        contain simple html for formatting.
        """

        loader_url = "https://support.shotgunsoftware.com/hc/en-us/articles/219033078"

        return """
        Publishes the file to Shotgun. A <b>Publish</b> entry will be
        created in Shotgun which will include a reference to the file's current
        path on disk. If a publish template is configured, a copy of the
        current session will be copied to the publish template path which
        will be the file that is published. Other users will be able to access
        the published file via the <b><a href='%s'>Loader</a></b> so long as
        they have access to the file's location on disk.
        If the session has not been saved, validation will fail and a button
        will be provided in the logging output to save the file.
        <h3>File versioning</h3>
        If the filename contains a version number, the process will bump the
        file to the next version after publishing.
        The <code>version</code> field of the resulting <b>Publish</b> in
        Shotgun will also reflect the version number identified in the filename
        The basic worklfow recognizes the following version formats by default:
        <ul>
        <li><code>filename.v###.ext</code></li>
        <li><code>filename_v###.ext</code></li>
        <li><code>filename-v###.ext</code></li>
        </ul>
        After publishing, if a version number is detected in the work file, the
        work file will automatically be saved to the next incremental version
        number. For example, <code>filename.v001.ext</code> will be published
        and copied to <code>filename.v002.ext</code>
        If the next incremental version of the file already exists on disk, the
        validation step will produce a warning, and a button will be provided
        in the logging output which will allow saving the session to the next
        available version number prior to publishing.
        <br><br><i>NOTE: any amount of version number padding is supported. for
        non-template based workflows.</i>
        <h3>Overwriting an existing publish</h3>
        In non-template workflows, a file can be published multiple times,
        however only the most recent publish will be available to other users.
        Warnings will be provided during validation if there are previous
        publishes.
        """ % (
            loader_url,
        )

    @property
    def item_filters(self):
        """
        List of item types that this plugin is interested in.
        Only items matching entries in this list will be presented to the
        accept() method. Strings can contain glob patters such as *, for
        example ["harmony.*", "file.harmony"]
        """
        return ["harmony.color_palette"]

    @property
    def settings(self):
        """
        Dictionary defining the settings that this plugin expects to receive
        through the settings parameter in the accept, validate, publish and
        finalize methods.
        A dictionary on the following form::
            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }
        The type string should be one of the data types that toolkit accepts as
        part of its environment configuration.
        """

        # inherit the settings from the base publish plugin
        base_settings = super(HarmonyColorPalettesPublishPlugin, self).settings or {}

        # settings specific to this class
        color_palettes_publish_settings = {
            "Publish Color Palette Template": {
                "type": "template",
                "default": None,
                "description": "Template path where the files will be published",
            },
        }

        # update the base settings
        base_settings.update(color_palettes_publish_settings)

        return base_settings

    def accept(self, settings, item):
        result = {"accepted": True, "checked": True}

        return result

    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish. Returns a
        boolean to indicate validity.
        :param settings: Dictionary of Settings. The keys are strings, matching
                         the keys returned in the settings property. The values
                         are `Setting` instances.
        :param item: Item to process
        :returns: True if item is valid, False otherwise.
        """

        context = self.parent.context
        publish_palette_setting = settings.get("Publish Color Palette Template").value
        publish_color_palette_template = self.parent.engine.get_template_by_name(publish_palette_setting)
        publish_path = item.properties["publish_path"]

        valid_path = publish_color_palette_template.validate(publish_path)

        return True if valid_path else False

    def publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.
        :param settings: Dictionary of Settings. The keys are strings, matching
                         the keys returned in the settings property.
                         The values are `Setting` instances.
        :param item: Item to process
        """

        # return
        self.logger.info("Publishing Color Palette")
        self.parent.logger.info("Publishing Color Palette.")

        # get the path in a normalized state. no trailing separator, separators
        # are appropriate for current os, no double separators, etc.
        engine = self.parent.engine

        # Get item properties
        work_fields = item.properties["work_fields"]
        publish_session_template = item.properties["template_session_publish"]
        primary_publish_path = publish_session_template.apply_fields(work_fields)
        publish_path = item.properties["publish_path"]
        publish_folder = os.path.dirname(publish_path)

        # Ensure folder exists
        if not os.path.exists(publish_folder):
            engine.ensure_folder_exists(publish_folder)

        # Export the file
        file_correctly_copied = self._copy_color_palette_to_publish_path(
            settings, item
        )
        self.parent.logger.info(
            "{} file_correctly_copied: {}".format(
                os.path.basename(publish_path), file_correctly_copied
            )
        )

        if file_correctly_copied:
            publish_name = self.parent.util.get_publish_name(publish_path)

            self.parent.logger.info(
                "Color palette publish name: {}".format(publish_name)
            )

            publish_data = {
                "tk":                   self.parent.sgtk,
                "context":              item.context,
                "comment":              item.description,
                "path":                 publish_path,
                "name":                 publish_name,
                "version_number":       work_fields['version'],
                "thumbnail_path":       item.get_thumbnail_as_path(),
                "published_file_type":  "Harmony Color Palette",
                "dependency_paths":     [primary_publish_path],
                "dependency_ids":       [],
                "sg_fields":            {"sg_status_list": "rev"},
            }

            # Register the publish of the original PNG, so that we can have the pointer
            sg_publishes = sgtk.util.register_publish(**publish_data)

            item.properties.sg_publish_data = sg_publishes
            item.properties.sg_publish_path = sg_publishes["path"]["local_path"]

            # finally just store the publish data for later retrieval
            # and upload to the host storage location
            root_item = self.get_root_item(item)
            root_item.properties.setdefault('sg_publish_extra_data', [])

            self.parent.logger.debug(
                'Storing extra publish data on root item: {}'.format(root_item)
            )
            publish_extra_data = root_item.properties['sg_publish_extra_data']
            root_item.properties['sg_publish_extra_data'].append(sg_publishes)
            self.parent.logger.debug(
                "Already {} elements in extra publish data".format(
                    len(publish_extra_data)
                )
            )

            # Update task status for revision
            sg_task = self.parent.context.task
            self.parent.logger.info(
                "End of Publish and updating task with id: {} to status: 'rev'".format(
                    str(sg_task['id'])
                )
            )

            try:
                self.parent.engine.shotgun.update(
                    "Task", sg_task["id"], {"sg_status_list": 'rev'}
                )
            except:
                pass

    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed, and can for example be used to version up files.
        :param settings: Dictionary of Settings. The keys are strings, matching
                         the keys returned in the settings property.
                         The values are `Setting` instances.
        :param item: Item to process
        """

        # do the base class finalization
        #super(HarmonySessionPublishPlugin, self).finalize(settings, item)
        self.logger.debug('Item color palette successfully published')
        # bump the session file to the next version
        #self._save_to_next_version(item.properties["path"], item, _save_session)

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item
        return root

    def _copy_color_palette_to_publish_path(self, settings, item):
        """
        Copy the color palette to the Publish Area with the color palette template
        applied (publish_path).
        """
        engine = self.parent.engine

        color_palette_path = item.properties["color_palette_path"]
        publish_path = item.properties["publish_path"]

        shutil.copy(color_palette_path, publish_path)

        # Check if the file was created (if it exists on disk)
        file_exists = os.path.exists(publish_path)

        return file_exists
