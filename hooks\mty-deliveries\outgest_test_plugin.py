################################################################################
#
# Copyright (c) 2024 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
################################################################################

import re
import os
import json
import pprint
import shutil

import sgtk

from pprint import pformat

HookBaseClass = sgtk.get_hook_baseclass()


class OutgestPlugin(HookBaseClass):
    """
    Base class plugin for outgesting a file an its dependencies to Shotgun.
    This plugin is typically configured to act upon published files on studio
    Shotgun and those need to be packed, uploaded and registered with Client
    It needs to be extended for each particualr case but it contains standard
    operations for validating and registering versions with Shotgun.
    """

    ############################################################################
    # requiered custpm ingest plugin properties

    @property
    def settings_schema(self):
        """
        Dictionary defining the settings that this plugin expects to recieve
        through the settings parameter in the accept, validate, publish and
        finalize methods.
        A dictionary on the following form:
            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }
        The type string should be one of the data types that toolkit accepts
        as part of its environment configuration.
        """
        settings = {
            "supported_entity_identifiers": {
                "type": "list",
                "default": [],
                "description": "random",
            },
            'supported_entity_types': {
                'type': 'dict',
                'default': {},
                'description': '',
            },
            'supported_task_names': {
                'type': 'dict',
                'default': {},
                'description': '',
            },
        }

        return settings

    ############################################################################
    # requiered custom ingest plugin methods

    def outgest(self, location, entity, files):
        """ This is a plugin that expects a single remote file

            Args:
                location (dict): The entity representing the remote location
                entity (dict): PublishedFile entity
                files (dict): A map of local files to already uploaded remote files
        """

        direct_submissions = []

        self.logger.debug("Uploaded local files:\n{0}".format(pformat(files)))

        query_entities_on_client_site = self.query_entities_on_client_site()

        if query_entities_on_client_site:
            # plugin can use settings from the mappings hoook
            ingest_mappings = self.parent.mappings['ingest']
            outgest_mappings = self.parent.mappings['outgest']

            # Ensure entity and task exists
            client_entity = self.resolve_client_entity(entity, location)
            client_task = self.resolve_client_task(entity, client_entity)

            # resolve the most apropiate version name for client
            version_name = self.resolve_version_name(
                entity, client_entity, client_task,
            )
            self.parent.engine.logger.info(
                "resolved version_name: {}".format(version_name)
            )

            # set some extra arguments
            description = 'Automated Delivery from Vendor'

            # upload from "delivered" location
            media_file_path = files[entity['path']['local_path']]

            # check if we need to add extra fields
            extra_fields_dict = outgest_mappings.get("version_extra_fields")
            self.parent.engine.logger.info(
                "extra_fields_dict: {}".format(extra_fields_dict)
            )

            # now lets register it, internally will check if already was registered
            version = self.ensure_version(
                client_entity,
                client_task,
                version_name,
                media_file_path,
                description,
                extra_fields_dict
            )

            # take the id from client and register it back on studio
            self.register_external_id(
                entity, version["id"],
                target_entity_type="Version"
            )

            direct_submissions.append(version)

            #  check if we need to update the client task and update it if needed
            update_client_task_status = outgest_mappings.get("update_client_task_status")
            desired_client_task_status = outgest_mappings.get("desired_client_task_status")

            self.parent.engine.logger.info(
                f"update_client_task_status: {update_client_task_status}"
            )
            self.parent.engine.logger.info(
                f"desired_client_task_status: {desired_client_task_status}"
            )

            if update_client_task_status and desired_client_task_status:
                updated_client_task = self.update_client_task_status(
                    client_task, desired_client_task_status
                )
                if updated_client_task:
                    self.logger.info(
                        "Updated client task status to {}".format(
                            desired_client_task_status
                        )
                    )
                else:
                    self.logger.warning(
                        "Failed to update client task: {}".format(client_task)
                    )

            # check if we need to create a playlist on client's site
            create_playlist_on_client_SG = outgest_mappings.get("create_playlist_on_client_SG")
            if create_playlist_on_client_SG:
                playlist_name_template =outgest_mappings.get("playlist_name_template")
                playlist_name_dict = outgest_mappings.get("playlist_name_dict")
                playlist_name = self.resolve_playlist_name(playlist_name_template, playlist_name_dict)
                playlist_description = 'Automated Playlist from Vendor'

                # create the playlist
                playlist = self.create_playlist(
                    playlist_name, playlist_description, direct_submissions
                )

                if playlist:
                    self.logger.info(
                        (
                            f"-----> Created playlist with id: {playlist.get('id')}, "
                            f"name: {playlist.get('code')}"
                        )
                    )
                    self.parent.engine.logger.info(
                        "Full playlist info:\n{}".format(pformat(playlist))
                    )
                else:
                    self.logger.warning("Failed to create playlist")

        # return the standarzed list of versions from this ingest
        return direct_submissions
