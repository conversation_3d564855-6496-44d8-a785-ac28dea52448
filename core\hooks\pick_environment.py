# Copyright (c) 2018 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

"""
Hook which chooses an environment file to use based on the current context.
"""

from tank import Hook


class PickEnvironment(Hook):
    def execute(self, context, **kwargs):
        """
        The default implementation assumes there are three environments, called shot, asset
        and project, and switches to these based on entity type.
        """
        if context.source_entity:
            if context.source_entity["type"] == "Version":
                return "version"
            elif context.source_entity["type"] == "PublishedFile":
                return "publishedfile"

        if context.project is None:
            # Our context is completely empty. We're going into the site context.
            return "site"

        if context.entity is None:
            # We have a project but not an entity.
            return "project"

        if context.entity and context.step is None:
            # We have an entity but no step.
            if context.entity["type"] == "Shot":
                return "shot"
            if context.entity["type"] == "Asset":
                return "asset"
            if context.entity["type"] == "Sequence":
                return "sequence"
            if context.entity["type"] == "Episode":
                return "episode"

        if context.entity and context.step:
            # We have a step and an entity.
            if context.entity["type"] == "Episode":
                return "episode_step"
            if context.entity["type"] == "Sequence":
                return "sequence_step"
            if context.entity["type"] == "Shot":
                return "shot_step"

            if context.entity["type"] == "Asset":

                filters = [['id', 'is', context.entity['id']]]
                fields = ['sg_asset_type', 'sg_environment_type']
                asset = self.parent.shotgun.find_one('Asset', filters, fields)

                if asset["sg_asset_type"] in ['Environment']:

                    environment_name = 'environment_step'

                else:
                    environment_name = "asset_step"

                # for particular tasks we have an exception
                # they use especial environment without task tokens
                # this cause that there will be common files
                # between two or more tasks, being versioned together
                no_task_list = ["modeling", "uvs", "mdlmodeling", "mdluvs"]
                if context.task["name"].lower() in no_task_list:
                    environment_name += '_notask'

                return environment_name

        return None
