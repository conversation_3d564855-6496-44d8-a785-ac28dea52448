#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

import os
import json
from pprint import pformat
from tank.platform.qt import QtCore, QtGui
from tank import Hook


class ScanSceneHook(Hook):

    def scan_scene(self):

        self.parent.engine.logger.info(
            "Scanning scene for 3D rigs and geos...".ljust(120, "-")
        )
        self.parent.engine.logger.info("Scanning scene to get the rigs needed for the scene.")


        items_result = []
        warnings = []
        errors = []

        # Load value overrides framework -----------------------------------------------
        valueoverrides = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        ) or self.load_framework("mty-framework-valueoverrides")

        if not valueoverrides:
            self.parent.engine.logger.error("Couldn't load value overrides framework")

        # first load the all settings from the config settings as fallback in case of
        # missing value overrides
        tasks_for_avoiding_scanning_for_rigs_and_envs = self.parent.get_setting(
            "tasks_for_avoiding_scanning_for_rigs_and_envs"
        )
        self.parent.engine.logger.info(
            "tasks_for_avoiding_scanning_for_rigs_and_envs from hook settings: {}".format(
                tasks_for_avoiding_scanning_for_rigs_and_envs
            )
        )

        # then try to load the settings from value overrides
        if valueoverrides:
            # Get task priority list
            override_link = {
                "type": "Task",
                "id": self.parent.engine.context.task["id"],
            }

            default_value_code = (
                "mty.multi.batchloader.scan.tasks_for_avoiding_scanning_for_rigs_and_envs"
            )
            supported_tasks_for_loading_anim_caches_value = valueoverrides.get_value(
                default_value_code, link=override_link
            )
            if supported_tasks_for_loading_anim_caches_value:
                tasks_for_avoiding_scanning_for_rigs_and_envs = json.loads(
                    supported_tasks_for_loading_anim_caches_value
                )
                self.parent.engine.logger.info(
                    "tasks_for_avoiding_scanning_for_rigs_and_envs from override: {}".format(
                        tasks_for_avoiding_scanning_for_rigs_and_envs
                    )
                )

        # ------------------------------------------------------------------------------

        context_task = self.parent.engine.context.task.get("name")

        if context_task in tasks_for_avoiding_scanning_for_rigs_and_envs:
            self.parent.engine.logger.info(
                "Skipping rig scan for task: {}".format(context_task)
            )
            return items_result, warnings, errors

        try:
            rigs, envs, warnings = self._scan_for_rigs_and_envs(
                self.parent.engine.shotgun, self._get_current_shot()
            )
            overrides = self._scan_for_env_overrides(self.parent.engine.shotgun, self.parent.context)

            items_scanned = rigs + envs + overrides

            self.parent.log_debug(str(rigs) + str(warnings))

            if self._get_current_task()['step']['name'] == "Animation":
                if warnings:
                    message = str(warnings)
                    self.parent.engine.execute_in_main_thread(
                        QtGui.QMessageBox.information,
                        None,
                        "No assets found...",
                        message
                    )

            # Ensure metasync framework is available, but only load it once.
            if not hasattr(self, 'metasync'):
                self.metasync = self.load_framework("mty-framework-metasync")
                self.parent.log_debug("metasync: %s" % self.metasync)

            transfersManager = self.metasync.transfersManager

            for item in items_scanned:
                if not os.path.exists(item['path']):
                    transfersManager.ensure_file_is_local(item['path'], item['publish'])

                transfersManager.ensure_local_dependencies(item['publish'])

            for rig in rigs:
                items_result.append({'node': rig['name'],
                                     'type': 'Rig File',
                                     'path': rig['path'],
                                     'process_hook': 'batchload_reference_rigs'})

            for env in envs:
                items_result.append({'node': env['name'],
                                     'type': 'Env Proxy',
                                     'path': env['path'],
                                     'process_hook': 'batchload_reference_rigs'})

            for override in overrides:
                items_result.append({'node': override['name'],
                                     'type': 'Env Overrides',
                                     'path': override['path'],
                                     'process_hook': 'batchload_reference_rigs'})

        except:
            import traceback

            error_str = traceback.format_exc()
            errors.append(error_str)
            self.parent.engine.logger.errer(error_str)

        return items_result, warnings, errors

    def _scan_for_rigs_and_envs(self, shotgun, shot):

        _rigs = []
        _envs = []
        _warnings = []

        # Load value overrides framework
        valueoverrides = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        ) or self.load_framework("mty-framework-valueoverrides")

        if not valueoverrides:
            self.parent.engine.logger.error("Couldn't load value overrides framework")

            self.rigging_task_names = self.parent.get_setting("rigging_task_names")
            self.parent.engine.logger.info("Got rigging_task_names from hook settings")

            # TODO: move all modeling scan and imports to their own hook
            self.modeling_task_names = self.parent.get_setting("modeling_task_names")
            self.parent.engine.logger.info("Got modeling_task_names from hook settings")
        else:
            override_link = {"type": "Task", "id": self.parent.engine.context.task["id"]}

            default_value_code = "mty.multi.batchloader.scan.rigging_task_names"
            rigging_task_names = valueoverrides.get_value(
                default_value_code, link=override_link
            )
            if rigging_task_names:
                self.rigging_task_names = json.loads(rigging_task_names)
                self.parent.engine.logger.info(
                    "Got rigging_task_names from override: {}".format(
                        rigging_task_names
                    )
                )

            # TODO: move all modeling scan and imports to their own hook
            default_value_code = "mty.multi.batchloader.scan.modeling_task_names"
            modeling_task_names = valueoverrides.get_value(
                default_value_code, link=override_link
            )
            if modeling_task_names:
                self.modeling_task_names = json.loads(modeling_task_names)
                self.parent.engine.logger.info(
                    "Got modeling_task_names from override: {}".format(
                        modeling_task_names
                    )
                )


        self.parent.log_debug(self.parent.context.entity)
        self.parent.log_debug(shot)

        if len(shot['sg_breakdowns']) < 1:
            _warnings.append(
                "This shot doesn't have any breakdowns, "
                "or the assets in those breakdowns don't have any "
                "approved published files."
                # "This shot doesn't have any linked assets, "
                # "or their publishes are not approved, "
            )

        else:
            found_rigs = []
            found_envs = []

            breakdown_items = self._get_breakdown_items_from_shot(shot)
            breakdown_publishes = self._get_published_files_from_breakdown_items(
                breakdown_items
            )
            breakdown_item_names = [
                breakdown_item['sg_asset']['name']
                for breakdown_item in breakdown_items
            ]

            for breakdown_name, published_files_list in iter(breakdown_publishes.items()):
                self.parent.engine.logger.info("-" * 40)
                self.parent.engine.logger.info(
                    "Working with breakdown: {}".format(breakdown_name)
                )

                for published_file in published_files_list:
                    self.parent.log_debug('Checking:%s\n' % pformat(published_file))

                    valid_rig_context = self._is_context_valid_rig(published_file)
                    self.parent.engine.logger.info(
                        "valid_context: {}".format(valid_rig_context)
                    )

                    if valid_rig_context:
                        is_valid_rig_type, rig_type = self._get_rig_type(published_file)

                        file_key = '{0}_{1}'.format(
                            published_file['entity']['name'], rig_type
                        )

                        self.parent.engine.logger.info("is_valid_rig_type: {}".format(is_valid_rig_type))
                        self.parent.engine.logger.info("rig_type: {}".format(rig_type))
                        self.parent.engine.logger.info("file_key: {}".format(file_key))

                        if not file_key in found_rigs and is_valid_rig_type:
                            self.parent.engine.logger.info(
                                "Path to check: %s" % published_file['path']['local_path']
                            )

                            for rig in breakdown_item_names:
                                if rig == published_file['entity']['name']:
                                    _rigs.append({'name': breakdown_name,
                                                'path': published_file['path']['local_path'],
                                                'publish': published_file})
                                    found_rigs.append(file_key)

                    if self._is_environment_type(published_file):

                        if not published_file['entity']['name'] in found_envs:
                            for env in breakdown_item_names:

                                if env == published_file['entity']['name']:
                                    _envs.append({'name': breakdown_name,
                                                'path': published_file['path']['local_path'],
                                                'publish': published_file})
                                    found_envs.append(published_file['entity']['name'])

        return _rigs, _envs, _warnings

    def _scan_for_env_overrides(self, shotgun, context):

        _overrides = []

        published_files = self._get_published_files_overrides_from_shot(shotgun, context)
        latest_published_files_by_name = self._get_published_files_overrides_lastest_version(published_files)

        for published_file_name in latest_published_files_by_name:
            published_file = latest_published_files_by_name[published_file_name]
            _overrides.append({'name': published_file_name,
                               'path': published_file['path']['local_path'],
                               'publish': published_file})

        return _overrides

    def _get_current_shot(self):

        filters = [['id', 'is', self.parent.context.entity['id']]]
        fields = ['assets', 'sg_breakdowns']
        entity = self.parent.engine.shotgun.find_one("Shot", filters, fields)

        return entity

    def _get_current_task(self):

        filters = [['id', 'is', self.parent.context.task['id']]]
        fields = ['code', 'step', 'content']
        entity = self.parent.engine.shotgun.find_one('Task', filters, fields)

        return entity

    def _get_rig_type(self, published_file):

        local_path_lower = published_file['path']['local_path'].lower()

        if 'full' in local_path_lower:
            self.parent.engine.logger.info("found 'full' in local_path_lower")
            return True, 'full'

        elif 'body' in local_path_lower:
            self.parent.engine.logger.info("found 'body' in local_path_lower")
            return True, 'body'

        elif "rig3d" in local_path_lower:
            self.parent.engine.logger.info("found 'rig3d' in local_path_lower")
            return True, 'full'

        elif published_file['entity.Asset.sg_asset_type'] == 'Camera':
            self.parent.engine.logger.info("found 'Camera' as asset type")
            return True, 'full'

        else:
            self.parent.engine.logger.warning(
                "Couldn't find any match for rig type in path: {}".format(
                    local_path_lower
                )
            )
            return False, None

    def _get_breakdown_items_from_shot(self, shot):

        breakdown_items = []

        for breakdown_item in shot['sg_breakdowns']:
            breakdown_items.append(
                self.parent.engine.shotgun.find_one(
                    "CustomEntity30",
                    [['id', 'is', breakdown_item['id']]],
                    ['sg_locked_version.PublishedFile.id',
                    'sg_asset', 'code']
                )
            )

        return breakdown_items

    def _get_published_files_from_breakdown_items(self, breakdown_items):

        unlocked_breakdowns = []
        locked_published_file_ids = []

        for breakdown_item in breakdown_items:
            if breakdown_item.get('sg_locked_version.PublishedFile.id'):
                locked_published_file_ids.append(breakdown_item.get('sg_locked_version.PublishedFile.id'))
            else:
                unlocked_breakdowns.append(breakdown_item)

        self.parent.logger.info(
            "unlocked_breakdowns:\n{}".format(pformat(unlocked_breakdowns))
        )

        fields = ['id', 'code', 'path_cache', 'path', 'entity',
                  'entity.Asset.sg_sub_type', 'entity.Asset.sg_asset_type',
                  'entity.Asset.code', "published_file_type", "task"]

        order = [{'field_name': 'id', 'direction': 'desc'}]

        locked_publishes = []
        if locked_published_file_ids:
            filters = [['id', 'in', locked_published_file_ids]]
            locked_publishes = self.parent.engine.shotgun.find("PublishedFile", filters, fields, order)

        unlocked_publishes = []
        if unlocked_breakdowns:
            filters = [['entity.Asset.sg_breakdowns', 'in', unlocked_breakdowns],
                       ['published_file_type.PublishedFileType.code', 'in', ["Alembic Cache", 'GPU Cache', 'Maya Scene']],
                       ['sg_status_list', 'is', 'apr']]

            unlocked_publishes = self.parent.engine.shotgun.find("PublishedFile", filters, fields, order)
            # unlocked_publishes = self._get_top_in_hierarchy(unlocked_publishes)

        self.parent.logger.info(
            'Unlocked_publishes:\n{}'.format(pformat(unlocked_publishes))
        )
        self.parent.logger.info(
            'Locked_publishes:\n{}'.format(pformat(locked_publishes))
        )

        breakdown_publishes = {}

        for breakdown_item in breakdown_items:

            publishes = []

            if breakdown_item.get('sg_locked_version.PublishedFile.id'):
                for p in locked_publishes:
                    if p['entity']['id'] == breakdown_item['sg_asset']['id']:
                        publishes.append(p)
            else:
                for p in unlocked_publishes:
                    if p['entity']['id'] == breakdown_item['sg_asset']['id']:
                        publishes.append(p)

            if publishes:
                breakdown_publishes[breakdown_item['code']] = publishes

        return breakdown_publishes

    def _get_published_files_overrides_from_shot(self, shotgun, context):

        publish_type_name_field = 'published_file_type.PublishedFileType.code'

        override_type = 'Shot Environment Overrides'

        filters = [['entity', 'is', context.entity],
                   [publish_type_name_field, 'is', override_type]]

        fields = ['path', 'name', 'version_number', 'created_at']

        published_files = shotgun.find("PublishedFile", filters, fields)

        return published_files

    def _get_published_files_overrides_lastest_version(self, published_files):

        latest_published_files_by_name = {}

        for published_file in published_files:
            name = published_file['name']

            if name not in latest_published_files_by_name:
                latest_published_files_by_name['name'] = published_file

            else:
                current = latest_published_files_by_name[name]['created_at']
                newone = published_file['created_at']

                if newone > current:
                    latest_published_files_by_name[name] = published_file

        return latest_published_files_by_name

    def _is_context_valid_rig(self, published_file):

        if published_file['entity.Asset.sg_asset_type'] == 'Character':
            return True

        elif published_file['entity.Asset.sg_asset_type'] == 'Prop':
            return True

        elif published_file['entity.Asset.sg_asset_type'] == 'EnvProp':
            return True

        elif published_file['entity.Asset.sg_asset_type'] == 'Camera':
            if self.parent.context.step['name'] in ['Layout', 'Animation']:
                return True
            else:
                return False
        else:
            return False

    def _is_environment_type(self, published_file):

        file_type_name = published_file.get("published_file_type", {}).get("name")
        task_name = published_file.get("task", {}).get("name")

        if published_file['entity.Asset.sg_asset_type'] == 'EnvLocation':
            return True

        elif published_file['entity.Asset.sg_asset_type'] == 'EnvModule':
            return True

        elif task_name in self.modeling_task_names and file_type_name == "Alembic Cache":
            self.parent.engine.logger.info("found alembic geo")
            return True

        elif published_file['entity.Asset.sg_asset_type'] == 'Camera':
            self.parent.engine.logger.info("found 'Camera' as asset type")
            return True, 'full'

        else:
            return False

    def _get_top_in_hierarchy(self, all_publishes):

        top_rigs = {}

        for p in all_publishes:
            try:
                pub_task_name = p['task']['name']
                pub_entity_name = p['entity']['name']
                pub_version_number = p['version_number']
                if pub_entity_name not in top_rigs:
                    top_rigs[pub_entity_name] = {
                        'task': pub_task_name,
                        'version': pub_version_number,
                        'publishedfile': p,
                    }
                else:

                    update_records = False
                    task_name = top_rigs[pub_entity_name]['task'].lower()
                    new_version_validation = top_rigs[pub_entity_name]['version'] < pub_version_number

                    # if task_name == 'fullrig' and pub_task_name.lower() == 'fullrig' and new_version_validation:
                    #     update_records = True
                    # elif task_name == 'bodyrig' and pub_task_name.lower() == 'bodyrig' and new_version_validation:
                    #     update_records = True
                    # elif task_name == 'proxyrig' and pub_task_name.lower() == 'proxyrig' and new_version_validation:
                    #     update_records = True
                    if task_name in self.rigging_task_names:
                        update_records = True
                    # elif task_name in ['mdl', 'modeling'] and new_version_validation:
                    #     update_records = True
                    elif task_name in self.modeling_task_names:
                        update_records = True

                    if update_records:
                        top_rigs[pub_entity_name]['version'] = pub_version_number
                        top_rigs[pub_entity_name]['publishedfile'] = p
            except:
                pass

        most_updated_rigs = [pub['publishedfile'] for pub in top_rigs.values()]

        return most_updated_rigs
