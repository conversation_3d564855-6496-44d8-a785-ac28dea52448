# -*- coding: utf-8 -*-
# Standard library:
import pprint
#   .   .   .   .   .   .   .   .   .   .   .
# Third party:
import sgtk
import pymel.core as pm
#   .   .   .   .   .   .   .   .   .   .   .
# Project:
# ================================================================
pp = pprint.PrettyPrinter(indent=4, width=70).pprint
HookBaseClass = sgtk.get_hook_baseclass()


class MeshValidationPlugin(HookBaseClass):

    def __init__(self, parent):
        super(
            MeshValidationPlugin,
            self
        ).__init__(parent)

    @property
    def description(self):
        return """
            <p>
            This plugin help to validate data
            in key geometry nodes.
            </p>
        """

    # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

    @property
    def item_filters(self):
        return["maya.session.attrs"]

    # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

    def accept(self, settings, item):
        return{
            "accepted": True,
            "enabled": True,
            "visible": True,
            "checked": True
        }

    def validate(self, settings, item):

        data = self.fb_checks_structure()

        state = {
            "errors": 0,
            "messages": {
                "debugs": [],
                "errors": []
            }
        }

        self.validate_controller_attributes(data, state)
        self.evaluate(state)

        self.validate_connections(data, state)
        self.evaluate(state)


        if self.parent.engine.context.task["name"] != 'ProxyRig':
            self.validate_shape_attributes(data, state)
            self.evaluate(state)

        return True

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def publish(self, settings, item):
        return True

    def finalize(self, settings, item):
        pass

    # ================================================================

    def evaluate(self, state):
        if state["errors"] == 0:
            for message in state["messages"]["debugs"]:
                self.logger.debug(message)

            return True

        else:
            self.logger.error(
                "{0} errors found.".format(state["errors"])
            )

            for message in state["messages"]["errors"]:
                self.logger.error(message)

            raise Exception("Session attributes checks fail.")

    # ================================================================

    def fb_checks_structure(self):
        list_of_controllers = pm.ls("*_CTL", type="transform")

        result = {}

        for controller in list_of_controllers:
            key = controller.name()
            if controller.hasAttr("ANI"):
                result[key] = {}
                result[key] = {
                    "controller": controller
                }

                result[key]["ANI-connections"] = []

                result[key]["ANI-connections"].extend(
                    controller.ANI.listConnections(
                        type="mesh",
                        exactType=True,
                        shapes=True
                    )
                )

            if controller.hasAttr("Out_ANI"):
                result[key]["Out_ANI-connections"] = []

                result[key]["Out_ANI-connections"].extend(
                    controller.Out_ANI.listConnections(
                        type="mesh",
                        exactType=True,
                        shapes=True
                    )
                )

        return result

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def validate_controller_attributes(self, data, state):

        for key in data.keys():
            controller = data[key]["controller"]

            if controller.hasAttr("ANI") \
                    and controller.hasAttr("Out_ANI"):

                state["messages"]["debugs"].append(
                    controller.name() + " " +
                    "has ANI and Out_ANI attrs.")

            else:
                state["errors"] += 1
                state["messages"]["errors"].append(
                    controller.name() + " " +
                    "is missing \"ANI\" or the \"Out_ANI\" attribute."
                )

    # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

    def validate_connections(self, data, state):
        for key in data.keys():

            if len(data[key]["ANI-connections"]) != 0:
                state["errors"] += 1

                state["messages"]["errors"].append(
                    data[key]["controller"].name() + ".ANI"
                    + " " +
                    "is directly connected to shape nodes, " +
                    " the \"Out_ANI\" attribute should be the one " +
                    "connected to the shape node."
                )

            if len(data[key]["Out_ANI-connections"]) > 0:
                state["messages"]["debugs"].append(
                    data[key]["controller"].name() + " " +
                    "is connected to the following shapes " +
                    str(data[key]["Out_ANI-connections"])
                )

    # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

    def validate_shape_attributes(self, data, state):
        shapes = {}

        for key in data.keys():

            if len(data[key]["Out_ANI-connections"]) == 0:
                state["errors"] += 1
                state["messages"]["errors"].append(
                    key + ".Out_ANI"
                    " attribute has no mesh connected to it."
                )

            else:
                for shape in data[key]["Out_ANI-connections"]:
                    shapes[shape.name()] = {
                        "node": shape,
                        "attrs": []
                    }
                    for attr in shape.listAttr():
                        shapes[shape.name()]["attrs"] \
                            .append(attr.longName())

        list_of_attributes_wildcard = ["fbCache_Dyn"]

        pp(shapes)

        for key in shapes.keys():

            list_of_attrs = shapes[key]["attrs"]
            has_attributes = False
            for attrs in list_of_attrs:
                for wildcard in list_of_attributes_wildcard:
                    if wildcard in attrs:
                        has_attributes = True

            if has_attributes:
                state["messages"]["debugs"].append(
                    key + " " + "contains: " +
                    str(list_of_attributes_wildcard) +
                    " attributes."
                )
            else:
                state["errors"] += 1
                state["messages"]["errors"].append(
                    key + " " + "do not contains: " +
                    str(list_of_attributes_wildcard) +
                    " attributes."
                )

    # ================================================================
