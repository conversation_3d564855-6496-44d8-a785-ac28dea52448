#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

"""
An app that fixes amn image (read) node's scale and proportions, by setting the right
parameters in the node properties windows. Basically replicates the import options
when using "Project Resolution" in the alignment rules when importing image as bitmap.
Also adds the proper scaling in the transformation tab

"""

from tank import Hook
from tank import TankError
from tank.platform import Application
from tank.platform.qt import QtCore, QtGui
import tank

import os
import re
import json
import pprint


class ProcessItemsHook(Hook):
    def execute(self, **kwargs):

        self.parent.engine.logger.info(
            "execute action fix_image_scaling start. {}".format("-" * 80)
        )

        # Ensure the scene is saved
        self.parent.engine.app.save_project()

        result = self._fix_image_scaling()

        if not result:
            result = {"succes": [1], "messages": [], "errors": []}

        # Ensure the scene is saved
        self.parent.engine.app.save_project()

        self.parent.engine.logger.info(
            "execute action fix_image_scaling end. {}".format("-" * 80)
        )

        return result

    def _get_image_path(self, input_node):
        """
        Get's the image path of the input read node. If the input node is not a read
        node, returns None.
        """

        get_image_path_cmd = """
function get_image_path()
{
    var input_node = "%s";
    MessageLog.trace("node type: " + node.type(input_node));

    if (node.type(input_node) != "READ")
    {
return null;
    };

    var startFrame = scene.getStartFrame();
    var path_attr = node.getAttr(input_node, startFrame, "DRAWING");
    var image_path = path_attr.textValue();

    MessageLog.trace("imgage_path: " + image_path);

    return image_path;
};

get_image_path();
""" % input_node

        self.parent.engine.logger.debug(get_image_path_cmd)
        image_path = self.parent.engine.app.execute(get_image_path_cmd)

        return image_path or None

    def _get_image_width(self, input_node):
        """
        Get's the image original resolution of the selected read node. If nothing is
        selected, or if the selected node is not a read node, it won't do anything.
        """

        image_path = self._get_image_path(input_node)

        self.parent.engine.logger.info("image_path: {}".format(image_path))

        result = None



        if image_path and os.path.exists(image_path):
            self.parent.engine.logger.info("loading mty-framework-ffmpeg")
            ffmpeg = self.load_framework("mty-framework-ffmpeg")
            FFmpegCoreTools = ffmpeg.ffmpegCore
            FFmpegCoreTools.set_binary(binary='media')
            ffprobe_path = os.path.dirname(ffmpeg.ffmpegCore.get_bin_path())

            # if not ffprobe_path.endswith("ffprobe"):
            #     ffprobe_path = os.path.join(ffprobe_path, "ffprobe")
            # ffprobe_path = ffprobe_path.replace("\\", "/")

            self.parent.engine.logger.info("ffprobe path: {}".format(ffprobe_path))

            ffprobe_cmd = (
                # "{} "
                "-hide_banner "
                "-loglevel panic "
                "-of json "
                "-show_entries stream=width "
                "{}"
            ).format(image_path)

            self.parent.engine.logger.info("ffprobe_cmd: {}".format(ffprobe_cmd))

            _err, _info = FFmpegCoreTools.execute_command(ffprobe_cmd)

            if  not _err:
                data = dict(json.loads(_info))
                streams = data.get("streams")
                if streams:
                    result = streams[0].get("width")
                self.parent.engine.logger.info("width: {}".format(result))

        return result


    def _fix_image_scaling(self):

        result = {"succes": [], "messages": [], "errors": []}

        # First check if selected node is a read node
        check_node_type_cmd = """

function check_node_type()
{
    var read_node = selection.selectedNode(0);

    if (node.type(read_node) != "READ")
    {
        var box_text = "Node '" + read_node + "' is not a Read node!";
        MessageBox.critical(box_text, 0, 1, 0, "Fix image scale");
        return false
    };

    return true
};

check_node_type()

"""

        self.parent.engine.logger.debug(check_node_type_cmd)
        node_type_ok = self.parent.engine.app.execute(check_node_type_cmd)
        self.parent.engine.logger.info("node_type_ok: {}".format(node_type_ok))

        if not node_type_ok:
            return result

        image_scale_multiplier = 1

        # get selected node
        selected_node = self.parent.engine.app.execute("selection.selectedNode(0)")

        image_width = self._get_image_width(selected_node)
        self.parent.engine.logger.info("image_width: {}".format(image_width))

        if not image_width:
            msg = "Couldn't get image width"
            return {"succes": [], "messages": [msg], "errors": [1]}


        # get resolution from SG
        hook_expression = "{config}/get_entity_resolution.py"
        resolution = self.parent.engine.execute_hook_expression(
            hook_expression,
            "get_resolution",
            engine=self.parent.engine,
        )
        self.parent.engine.logger.info("resolution: {}".format(resolution))

        if resolution:
            pattern = re.compile(r"(?P<width>\d+)x(?P<height>\d+)")
            match = re.match(pattern, str(resolution))
            if match:
                project_width = int(match.groupdict()["width"])
                project_height = int(match.groupdict()["height"])

                image_scale_multiplier = float(image_width) / float(project_width)

                self.parent.engine.logger.info(
                    "project_width: {}".format(project_width)
                )
                self.parent.engine.logger.info(
                    "project_height: {}".format(project_height)
                )
                self.parent.engine.logger.info(
                    "image_scale_multiplier: {}".format(image_scale_multiplier)
                )
        else:
            msg = "Couldn't get project resolution from SG"
            self.parent.engine.logger.error(msg)
            return {"succes": [], "messages": [msg], "errors": [1]}

        set_attr_values_cmd = """
function set_attrs(read_node, attrs_array)
{
    for (var key in attrs_array)
    {
        if (attrs_array.hasOwnProperty(key))
        {
            var attr = node.getAttr(read_node, 1, key);
            attr.setValue(attrs_array[key]);
        };
    };
};

var attrs_array = {};
attrs_array["scale.x"] = %s;
attrs_array["scale.y"] = %s;
attrs_array["alignmentRule"] = "As Is";
attrs_array["applyMatteToColor"] = "Premultiplied with Black";

var read_node = selection.selectedNode(0);

if (node.type(read_node) == "READ")
{
    set_attrs(read_node, attrs_array);
    MessageBox.information("Fixed image scaling for node " + read_node);
    return true;
} else {
    MessageBox.critical("Node '" + read_node + "' is not a Read node!");
    return false;
};

set_attrs(read_node, attrs_array)

""" % (
    image_scale_multiplier,
    image_scale_multiplier
)

        self.parent.engine.logger.debug(set_attr_values_cmd)
        successfully_fixed_scale = self.parent.engine.app.execute(set_attr_values_cmd)
        if successfully_fixed_scale:
            msg = "Successfully fixed image scaling"
            return {"succes": [1], "messages": [msg], "errors": []}
        else:
            msg = "Couldn't fix image scaling"
            return {"succes": [], "messages": [msg], "errors": [1]}
