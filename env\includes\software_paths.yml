# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

# Software path configuration. Typically consumed by the launchapp
# configurations for software that is not auto-discovered

################################################################################

# Mari
path.linux.mari: "Mari4.2"
path.mac.mari: "/Applications/Mari3.4v2/Mari3.4v2.app"
path.windows.mari: C:\Program Files\Mari4.2v1\Bundle\bin\Mari4.2v1.exe

# Motionbuilder
path.windows.motionbuilder: C:\Program Files\Autodesk\MotionBuilder 2018\bin\x64\motionbuilder.exe

# Hiero
path.linux.hiero: "Nuke11.2"
path.mac.hiero: "/Applications/Nuke11.2v4/Hiero11.2v4.app"
path.windows.hiero: C:\Program Files\Nuke11.2v4\Nuke11.2.exe

# RV
path.linux.rv: "/opt/RV/bin/rv"
path.mac.rv: "/Applications/RV.app"
path.windows.rv: C:\Program Files\ShotGrid\RV-2024.0.0\bin\rv.exe
