# -*- coding: utf-8 -*-
# Standard library:
import os
import re
import sys

from collections import namedtuple
from sgtk.util.filesystem import copy_file, ensure_folder_exists

# ___   ___   ___   ___   ___   ___  ___
# Third party:
import sgtk
import maya.cmds as cmds
import pymel.core as pm

HookBakeClass = sgtk.get_hook_baseclass()

PublishMetadata = namedtuple(
    "PublishMetadata",
    ["fields", "reference", "publish", "path_to_publish", "root_node"],
)


class USDShotPublishPlugin(HookBakeClass):
    @property
    def icon(self):
        return os.path.join(
            self.disk_location,
            os.pardir,
            os.pardir,
            os.pardir,
            os.pardir,
            os.pardir,
            "icons",
            "usd_publish.png",
        )

    @property
    def name(self):
        return "Publish Shot USD"

    @property
    def description(self):
        return """
        <p>This plugin publish a USD per each referenced rig of the scene.</p>

        """

    @property
    def item_filters(self):
        return ["maya.session.shot.geometryCache"]

    @property
    def settings(self):
        # inherit the settings from the base publish plugin
        plugin_settings = super(USDShotPublishPlugin, self).settings or {}

        _settings = {
            "Work Template": {
                "type": "template",
                "default": None,
                "desciprtion": "Template path for the current scene file.",
            },
            "USD Publish Template": {
                "type": "template",
                "default": None,
                "description": "",
            },
            "Publish Session Template": {
                "type": "template",
                "default": None,
                "description": "Maya publish session",
            },
            "USD publish_type": {"type": "string", "default": None, "description": ""},
            "start_field": {"type": "string", "default": None, "description": ""},
            "end_field": {"type": "string", "default": None, "description": ""},
            "map_of_tasks": {"type": "dict", "default": None, "description": ""},
            "map_of_pipeline_steps": {
                "type": "dict",
                "default": "dict",
                "description": "",
            },
        }

        # update the base settings
        plugin_settings.update(_settings)
        return plugin_settings

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root

    ############################################################################
    # standard publish plugin methods

    def accept(self, settings, item):
        engine = self.parent.engine
        current_task = engine.context.task["name"]
        pipeline_step = engine.context.step["name"]

        item.properties["accepted"] = False

        tasks = settings.get("map_of_tasks").value
        pipeline_steps = settings.get("map_of_pipeline_steps").value

        if (
            pipeline_step.lower() in pipeline_steps["supported"]
            and current_task.lower() in tasks["supported"]
        ):
            return {"accepted": True, "checked": True}
        else:
            return {"accepted": False}

    def validate(self, settings, item):
        """
        Validates the given item to check if it is ok to publish.

        Returns a boolean to indicate validty.
        """

        plugins = cmds.pluginInfo(query=True, listPlugins=True)
        if "mayaUsdPlugin" not in plugins:
            try:
                cmds.loadPlugin("mayaUsdPlugin")
            except:
                message = (
                    "You don't have the USD plugin!\n"
                    "You can get the latest version from here:\n%s"
                )
                usd_url = "https://github.com/Autodesk/maya-usd/releases"
                raise Exception(message % usd_url)

        return True

    def publish(self, settings, item):
        self.development = 0
        publisher = self.parent
        engine = self.parent.engine
        sg = engine.shotgun

        # Settings
        maya_work_template_name = settings.get("Work Template").value
        maya_publish_template_name = settings.get("Publish Session Template").value
        usd_publish_template_name = settings.get("USD Publish Template").value

        start_frame_field = settings.get("start_field").value
        end_frame_field = settings.get("end_field").value
        USDc_publish_type = settings.get("USD publish_type").value

        # Templates
        work_template = self.parent.engine.get_template_by_name(maya_work_template_name)
        publish_template = self.parent.engine.get_template_by_name(
            maya_publish_template_name
        )

        current_work_path = os.path.abspath(cmds.file(query=True, sn=True))
        fields = work_template.get_fields(current_work_path)

        publish_session_path = publish_template.apply_fields(fields)

        shot = sg.find_one(
            entity_type="Shot",
            filters=[["id", "is", self.parent.context.entity["id"]]],
            fields=[start_frame_field, end_frame_field],
        )

        start_frame = shot[start_frame_field]
        end_frame = shot[end_frame_field]

        frame_range = {"start": "{}".format(start_frame), "end": "{}".format(end_frame)}

        list_of_publish_metadata = self._list_of_publish_metadata(settings, item)

        for meta in list_of_publish_metadata:
            pubish_name = self._publish_name(meta.path_to_publish)
            publish_version = meta.fields["version"]
            USDc_publish_type = USDc_publish_type.format(
                step_code=meta.fields["step_code"]
            )
            Usd_path = meta.path_to_publish

            model_high_root_node = self.get_model_high_root_node(meta.root_node)

            self.parent.engine.ensure_folder_exists(os.path.dirname(Usd_path))

            if self.development == 0:
                print("-" * 32)
                print(
                    "USD: Node: {0}, path {1}, frame range: {2}".format(
                        str(model_high_root_node), Usd_path, str(frame_range)
                    )
                )
                print("-" * 32)
                self.exportUSDc(model_high_root_node, Usd_path, frame_range)

            sg_fields_to_update = {
                "sg_status_list": "rev",
                "task.Task.sg_status_list": "rev",
            }

            publish_data = {
                "tk": self.parent.sgtk,
                "context": item.context,
                "comment": item.description,
                "path": Usd_path,
                "name": pubish_name,
                "version_number": publish_version,
                "thumbnail_path": item.get_thumbnail_as_path(),
                "task": self.parent.engine.context.task,
                "sg_fields": sg_fields_to_update,
                "published_file_type": USDc_publish_type,
                "dependency_paths": [publish_session_path],
            }

            sg_publishes = sgtk.util.register_publish(**publish_data)

            item.properties.sg_publish_data = sg_publishes

            # finally just store the publish data for later retrieval
            # and upload to the host storage location
            root_item = self.get_root_item(item)
            root_item.properties.setdefault("sg_publish_extra_data", [])

            publish_extra_data = root_item.properties["sg_publish_extra_data"]
            publish_extra_data.append(sg_publishes)

    def finalize(self, settings, item):
        pass

    def get_model_high_root_node(self, transform_node):
        result = filter(
            lambda x: "model_high_root" in x.name(),
            transform_node.listRelatives(type="transform"),
        )
        return result[0]

    def _publish_name(self, publish_path):
        publish_name = self._regex_replace(
            regex=r"_v\d{3}",
            source_string=os.path.basename(publish_path),
            replace_string="",
        )
        return publish_name

    def _regex_replace(self, regex, source_string, replace_string):
        result = ""
        pattern = re.compile(regex)
        result = pattern.sub(repl=replace_string, string=source_string)
        return result

    def _list_of_publish_metadata(self, settings, item):
        result = []
        _map = item.properties["map_of_publishes_by_path"]

        for ref in item.properties["list_of_references"]:
            _node = ref.nodes()[0]
            _fields = self._reference_fields(
                settings=settings, reference=ref, map_of_publishes_by_path=_map
            )
            result.append(
                PublishMetadata(
                    fields=_fields,
                    reference=ref,
                    publish=_map[str(ref.path)],
                    path_to_publish=self._publish_path(settings, _fields),
                    root_node=_node,
                )
            )

        return result

    def _reference_fields(self, settings, reference, map_of_publishes_by_path):
        result = self._get_file_fields(settings)
        result["Asset"] = reference.namespace
        return result

    def _get_file_fields(self, settings):
        template = self.parent.engine.get_template_by_name(
            settings.get("Work Template").value
        )
        scene_path = os.path.abspath(pm.sceneName())
        return template.get_fields(scene_path)

    def _publish_path(self, settings, fields):
        template = self.parent.engine.get_template_by_name(
            settings.get("USD Publish Template").value
        )
        publish_path = template.apply_fields(fields)
        publish_folder = os.path.dirname(publish_path)
        self.parent.ensure_folder_exists(publish_folder)
        return publish_path

    def exportUSDc(self, node, usd_path, frame_range):
        usd_settings = (
            "exportUVs=1;"
            "exportSkels=none;"
            "exportSkin=none;"
            "exportBlendShapes=0;"
            "exportColorSets=0;"
            "defaultMeshScheme=catmullClark;"
            "defaultUSDFormat=usdc;"
            "animation=1;"
            "eulerFilter=0;"
            "staticSingleSample=0;"
            "startTime={start};"
            "endTime={end};"
            "frameStride=1;"
            "frameSample=0.0;"
            "parentScope=;"
            "exportDisplayColor=0;"
            "shadingMode=none;"
            "exportInstances=1;"
            "exportVisibility=1;"
            "mergeTransformAndShape=1;"
            "stripNamespaces=1".format(**frame_range)
        )

        pm.select(node, replace=True)

        exp = cmds.file(
            usd_path,
            force=True,
            exportSelected=True,
            type="USD Export",
            options=usd_settings,
        )

        pm.select(clear=True)
