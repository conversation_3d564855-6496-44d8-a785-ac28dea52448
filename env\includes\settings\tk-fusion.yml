# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

################################################################################

includes:
- ../app_locations.yml
- ../engine_locations.yml
- ./tk-multi-loader2.yml
- ./tk-multi-publish2.yml
- ./tk-multi-screeningroom.yml
- ./tk-multi-shotgunpanel.yml
- ./tk-multi-snapshot.yml
- ./tk-multi-workfiles2.yml
- ./tk-multi-breakdown.yml

################################################################################

# asset
settings.tk-fusion.asset:
  apps:
    tk-multi-about:
      location: '@apps.tk-multi-about.location'
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel'
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2.fusion.project'
    tk-multi-pythonconsole:
      location: '@apps.tk-multi-pythonconsole.location'
  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  #debug_logging: false
  location: '@engines.tk-fusion.location'

# asset_step
settings.tk-fusion.asset_step:
  apps:
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2.fusion.asset_step'
    tk-multi-snapshot: '@settings.tk-multi-snapshot.fusion.asset_step'
    tk-multi-loader2: '@settings.tk-multi-loader2.fusion'
    tk-multi-breakdown: '@settings.tk-multi-breakdown.fusion'
    tk-multi-publish2: '@settings.tk-multi-publish2.fusion.asset_step'
    mty-multi-scenesetup:
      location: "@apps.mty-multi-scenesetup.location"
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel'
    tk-multi-pythonconsole:
      location: '@apps.tk-multi-pythonconsole.location'
    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset" ]

    tk-multi-about:
      location: '@apps.tk-multi-about.location'

  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-snapshot, name: Snapshot...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  - {app_instance: tk-multi-publish2, name: Publish...}
  template_project: asset_work_area_fusion
  location: '@engines.tk-fusion.location'

# project
settings.tk-fusion.project:
  apps:
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2.fusion.project'
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel'
    tk-multi-pythonconsole:
      location: '@apps.tk-multi-pythonconsole.location'
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
    mty-executeaction-ensure-folders:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Ensure Tasks Folders"
      action_hook: "{config}/tk-shotgun-executeaction/create_folders_for_assigned_tasks.py"
      sg_extended_fields: {}
      allowed_entities: ['Shot', 'Sequence', 'Asset', 'Project']
    tk-multi-about:
      location: '@apps.tk-multi-about.location'
  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  #debug_logging: false
  location: '@engines.tk-fusion.location'

# sequence
settings.tk-fusion.sequence:
  apps:
    tk-multi-about:
      location: '@apps.tk-multi-about.location'
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel'
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2.fusion.project'
    tk-multi-pythonconsole:
      location: '@apps.tk-multi-pythonconsole.location'
  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  location: '@engines.tk-fusion.location'

# shot
settings.tk-fusion.shot:
  apps:
    tk-multi-about:
      location: '@apps.tk-multi-about.location'
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel'
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2.fusion.project'
    tk-multi-pythonconsole:
      location: '@apps.tk-multi-pythonconsole.location'
  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  location: '@engines.tk-fusion.location'

# shot_step
settings.tk-fusion.shot_step:
  # saver_nodes:
  #   - name: 'Saver Exr Master'
  #     format_id: 'OpenEXRFormat'
  #     format_settings: {CreateDir: 1, OpenEXRFormat.Compression: 3, OpenEXRFormat.Depth: 1}
  #     render_template: fusion_shot_render_aov
  #     work_template: fusion_shot_work
  #     aov_name: 'default'
  #   - name: 'Saver Exr AOV'
  #     format_id: 'OpenEXRFormat'
  #     format_settings: {CreateDir: 1, OpenEXRFormat.Compression: 3, OpenEXRFormat.Depth: 1}
  #     render_template: fusion_shot_render_aov
  #     work_template: fusion_shot_work
  apps:
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2.fusion.shot_step'
    tk-multi-snapshot: '@settings.tk-multi-snapshot.fusion.shot_step'
    tk-multi-loader2: '@settings.tk-multi-loader2.fusion'
    tk-multi-breakdown: '@settings.tk-multi-breakdown.fusion'
    tk-multi-publish2: '@settings.tk-multi-publish2.fusion.shot_step'
    mty-multi-scenesetup:
      location: "@apps.mty-multi-scenesetup.location"
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel.fusion'
    tk-multi-pythonconsole:
      location: '@apps.tk-multi-pythonconsole.location'
    # tk-multi-setframerange:
    #   location: "@apps.tk-multi-setframerange.location"
    #   hook_frame_operation: '{engine}/tk-multi-setframerange/frame_operations_tk-fusion.py'
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
    mty-fusion-autoloaders:
      location: '@apps.mty-fusion-autoloaders.location'
    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Sequence" ]

    tk-multi-about:
      location: '@apps.tk-multi-about.location'

  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-snapshot, name: Snapshot...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  - {app_instance: tk-multi-publish2, name: Publish...}
  template_project: shot_work_area_fusion
  location: '@engines.tk-fusion.location'
