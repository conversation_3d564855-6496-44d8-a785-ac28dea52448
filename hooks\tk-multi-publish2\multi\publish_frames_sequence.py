########################################################################################
#
# Copyright (c) 2024 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
########################################################################################

import os
import json
import shutil
import subprocess
from os.path import join
from pprint import pformat

import sgtk
from sgtk.platform.qt import QtGui, QtCore

HookBaseClass = sgtk.get_hook_baseclass()


class PublishFramesSeqPlugin(HookBaseClass):
    """
    Plugin for publish rendered sequence

    """

    @property
    def name(self):
        """
        One line display name describing the plugin
        """
        return "Publish sequence files"

    @property
    def description(self):
        """
        Copy/upload the render sequence to publish area
        """

        loader_url = "https://support.shotgunsoftware.com/hc/en-us/articles/219033078"

        publisher = self.parent
        engine = publisher.engine

        description = """
            This plugin publish copy render files to publish area and
            register in shotgun.

            Optionally create EXR into publish area with the multiple layers appended.
            """

        return "<p>{}</p>".format(description)

    @property
    def settings(self):
        """
        Dictionary defining the settings that this plugin expects to recieve
        through the settings parameter in the accept, validate, publish and
        finalize methods.

        A dictionary on the following form::

            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }

        The type string should be one of the data types that toolkit accepts
        as part of its environment configuration.
        """

        # settings specific to this class
        publish_settings = {
            "Work Template": {
                "type": "template",
                "default": "harmony_shot_work",
                "description": "Harmony work session",
            },
            "Primary Publish Template": {
                "type": "template",
                "default": "harmony_shot_publish",
                "description": "Harmony publish session",
            },
            "Publish Render Template": {
                "type": "template",
                "default": "shot_flat_render_publish_exr",
                "description": "Template to publish and copy render sequence files",
            },
            "Image Sequence Workflow": {
                "type": "str",
                "default": "grouped",
                "description": "Define the workflow to collect image sequences,"
                " which can be either 'individual' or 'grouped'",
            },
            "Publish Render Type": {
                "type": "str",
                "default": "Rendered Image",
                "description": "Published File Type name to use for the published frames",
            },
            "Invalid Image Extensions": {
                "type": "list",
                "default": [],
                "description": "Invalid image extensions for current environment",
            },
            # "Resolution Validation Ignore Tasks": {
            #     "type": "list",
            #     "default": [],
            #     "description": "Tasks to ignore resolution validation"
            # },
        }

        # inherit the settings from the base publish plugin
        plugin_settings = super(PublishFramesSeqPlugin, self).settings or {}

        # update the base settings
        plugin_settings.update(publish_settings)

        return plugin_settings

    @property
    def item_filters(self):
        """
        List of item types that this plugin is interested in.

        Only items matching entries in this list will be presented to the
        accept() method. Strings can contain glob patters such as *, for example
        ["harmony.*", "file.harmony"]
        """
        return ["harmony.frames_sequence", "maya.frames_sequence"]

    def accept(self, settings, item):
        """
        Method called by the publisher to determine if an item is of any
        interest to this plugin. Only items matching the filters defined via the
        item_filters property will be presented to this method.

        A submit for review task will be generated for each item accepted here. Returns a
        dictionary with the following booleans:

            - accepted: Indicates if the plugin is interested in this value at
                all. Required.
            - enabled: If True, the plugin will be enabled in the UI, otherwise
                it will be disabled. Optional, True by default.
            - visible: If True, the plugin will be visible in the UI, otherwise
                it will be hidden. Optional, True by default.
            - checked: If True, the plugin will be checked in the UI, otherwise
                it will be unchecked. Optional, True by default.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process

        :returns: dictionary with boolean keys accepted, required and enabled.
            This plugin makes use of the tk-multi-reviewsubmission app; if this
            app is not available then the item will not be accepted, this method
            will return a dictionary with the accepted field that has a value of
            False.

            Several properties on the item must also be present for it to be
            accepted. The properties are 'path', 'publish_name', 'color_space',
            'first_frame' and 'last_frame'
        """

        publisher = self.parent
        engine = publisher.engine
        context = engine.context

        # Verify entity type SHOT
        entity_type = context.entity.get("type", None) or str(context).split(" ")[1]
        if entity_type != "Shot":
            return {"accepted": False}

        return {"accepted": True, "checked": True}

    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish. Returns a
        boolean to indicate validity.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        :returns: True if item is valid and not in proxy mode, False otherwise.
        """

        sg_shot = self.get_sg_shot_info(["sg_cut_in", "sg_cut_out"])
        shot_duartion = int(sg_shot["sg_cut_out"]) - int(sg_shot["sg_cut_in"])

        # sequence property MUST contain a fileseq object
        sequence = item.properties["sequence"]
        layers_sequences = item.properties.get("layers_sequences", [])

        render_publish_template_setting = settings.get("Publish Render Template")
        render_publish_template = self.parent.engine.get_template_by_name(
            render_publish_template_setting.value
        )

        # optional image format/extension validations
        # check if the item has an invalid_extensions property
        invalid_exts = settings.get("Invalid image extensions", [])
        self.parent.engine.logger.info("Invalid image extensions: {0}".format(invalid_exts))
        current_extension = sequence.format("{extension}").replace(".", "").lower()
        self.parent.engine.logger.info(
            "Current Image Extension: {0}".format(current_extension)
        )
        if current_extension in invalid_exts:
            valid_choices = render_publish_template.keys["extension"].choices
            self.parent.engine.logger.debug("Valid extension choices: {0}".format(valid_choices))
            valid_exts = [e for e in valid_choices if e not in invalid_exts]
            error_msg = (
                "Your rendered image sequence is using an invalid image "
                "extension ({0}), at the moment, only the following ones "
                "are supported: {1}"
            ).format(current_extension, valid_exts)
            self.logger.error(error_msg)
            return False

        # Load value overrides framework -----------------------------------------------
        valueoverrides = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        ) or self.load_framework("mty-framework-valueoverrides")

        if not valueoverrides:
            self.parent.engine.logger.error("Couldn't load value overrides framework")

        # init sequence length validation value, by default is set to True
        validate_sequence_length = True

        if valueoverrides:
            # Get task priority list
            override_link = {
                "type": "Task",
                "id": self.parent.engine.context.task["id"],
            }

            default_value_code = (
                "mty.multi.publish2.frames_sequence.validate"
            )
            validate_sequence_length = valueoverrides.get_value(
                default_value_code, link=override_link
            )
            self.parent.engine.logger.info(
                "Got validate_sequence_length from override: {}".format(
                    validate_sequence_length
                )
            )

        self.parent.engine.logger.info(
            "Will validate_sequence_length?: {}".format(validate_sequence_length)
        )

        if validate_sequence_length:
            # Harmony fames can only start at 1
            # we can only validate if the number of frames matches shot
            sequence_frames = len(sequence)
            if sg_shot["sg_cut_out"] is None or sg_shot["sg_cut_in"] is None:
                error_msg = (
                    f"Missing shot info: sg_cut_in: {sg_shot['sg_cut_out']}, "
                    "sg_cut_out: {sg_shot['sg_cut_in']}"
                )
                self.logger.error(error_msg)
                return False

            shot_frames = sg_shot["sg_cut_out"] - sg_shot["sg_cut_in"] + 1
            if sequence_frames != shot_frames:
                error_msg = (
                    "Shot length ({0} frames) and rendered frames ({1} "
                    "rendered images) don't match."
                )
                # self.__frame_mismatch_more_info()
                self.logger.error(
                    error_msg.format(shot_frames, sequence_frames),
                    extra={
                        "action_button": {
                            "label": "How to fix it",
                            "tooltip": "More info about how to fix this error.",
                            # will launch wf2 if configured
                            "callback": lambda: self.__frame_mismatch_more_info(),
                        }
                    },
                )
                return False

        # Validate image resolution ----------------------------------------------------
        self.parent.engine.logger.info("Validate image resolution start {}".format("-" * 80))
        # get allowed steps and tasks from settings. Only tasks of the selected steps
        # be ignored
        sg_project_info = self.get_sg_project_info(
            proj_fields=["sg_fps"]
        )

        context = self.parent.engine.context
        context_task = context.task

        # ignore_tasks = settings.get("Resolution Validation Ignore Tasks", []).value
        ignore_tasks = []

        # load overrides framework
        overrides_framework = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        )
        if not overrides_framework:
            overrides_framework = self.load_framework("mty-framework-valueoverrides")

        default_value = "mty.engine.multi.ignore_render_resolution_tasks"
        link = {"type": "Task", "id": context.task["id"]}
        ignore_tasks_default_value = overrides_framework.get_value(
            default_value, link=link
        )
        self.parent.logger.debug(
            "ignore_tasks_default_value: {}, type: {}".format(
                ignore_tasks_default_value, type(ignore_tasks_default_value)
            )
        )

        if ignore_tasks_default_value:
            ignore_tasks = json.loads(ignore_tasks_default_value)

        self.parent.logger.debug(
            "ignore_render_resolution_tasks: {}, type: {}".format(
                ignore_tasks, type(ignore_tasks)
            )
        )

        # load ffmpeg framework
        ffmpeg_framework = self.parent.engine.custom_frameworks.get(
            "mty-framework-ffmpeg"
        ) or self.load_framework("mty-framework-ffmpeg")

        # load imagemagick framework
        imagemagick_framework = self.parent.engine.custom_frameworks.get(
            "mty-framework-imagemagick"
        ) or self.load_framework("mty-framework-imagemagick")

        validate_image_res_dict = {
            "ignore_tasks": ignore_tasks,
            "context_task": context_task,
            "sg_project_info": sg_project_info,
            "ffmpeg_framework": ffmpeg_framework,
            "imagemagick_framework": imagemagick_framework,
        }

        is_valid, msg = self.validate_image_resolution(
            sequence, validate_image_res_dict
        )
        if not is_valid:
            self.logger.error(
                msg,
                extra={
                    "action_button": {
                        "label": "How to fix it",
                        "tooltip": "More info about how to fix this error.",
                        # will launch wf2 if configured
                        "callback": lambda: self.__resolution_mismatch_more_info(),
                    }
                }
            )
            return False

        # Other known issue is if the render naming convention uses "-"
        # that will cause conflict with the image sequence recognition
        # and will make the numbers look like negative, which will cause problems

        beauty_start = sequence.start()
        beauty_end = sequence.end()
        beauty_padding = sequence.padding()
        item.properties["valid_layers_sequences"] = []

        sequence_name_format = "{basename}{padding}{extension} ({start}-{end})"
        display_name = sequence.format(sequence_name_format)

        # Validating
        for layer_seq in layers_sequences:
            valid = True
            error_msg = ""
            layer_n = layer_seq._base[:-2]

            # Validation 1:  Name
            if not layer_seq._base.endswith("."):
                valid = False
                error_msg = "Invalid name"
            elif "_" in layer_n or "." in layer_n or " " in layer_n:
                valid = False
                error_msg = "Invalid name, the sequence name can't contain: _ . or space"

            # Validation 2: Frames
            if layer_seq.start() != beauty_start or layer_seq.end() != beauty_end:
                valid = False
                if error_msg != "":
                    error_msg += ", frame range"
                else:
                    error_msg = "Invalid frame range"

            # Validation 3: Padding
            if layer_seq.padding() != beauty_padding:
                valid = False
                if error_msg != "":
                    error_msg += ", invalid padding"
                else:
                    error_msg = "Invalid padding"

            # Validation 4: Resolution
            is_valid, msg = self.validate_image_resolution(
                layer_seq, validate_image_res_dict
            )
            if not is_valid:
                valid = False
                if error_msg != "":
                    error_msg += ", resolution"
                else:
                    error_msg = "Invalid resolution"

            sequence_name_format = "{basename}{padding}{extension} ({start}-{end})"
            display_name = layer_seq.format(sequence_name_format)
            display_name = display_name.replace("@@@", "%03d")
            display_name = display_name.replace("#", "%04d")

            if not valid:
                self.logger.warning(
                    "Layer ommited. {}: {}".format(error_msg, display_name)
                )
                continue

            self.logger.info("Valid layer: {}".format(display_name))
            item.properties["valid_layers_sequences"].append(layer_seq)
            continue

            # Checking first frame number
            if layer_seq.start() != 1:
                self.logger.warning(
                    "Layer ommited. Invalid range: {}".format(display_name)
                )
                continue

            # Checking sequence duration
            seq_duration = layer_seq.start() - layer_seq.end()
            if seq_duration < shot_duartion:
                self.logger.warning(
                    "Layer ommited. Invalid duration: {}".format(display_name)
                )
                continue

            # If layer sequence pass all validations
            self.logger.info("Valid layer: {}".format(display_name))
            item.properties["valid_layers_sequences"].append(layer_seq)

        # Validating maya name
        if "custom_validations" in item.properties:
            if item.properties["custom_validations"]["validate_callback"](item):
                self.logger.warning(
                    "Found invalid non-alphanumeric characters in name: {}".format(
                        sequence.basename()
                    ),
                    extra={
                        "action_button": {
                            "label": "Change Names",
                            "tooltip": "Change current saved sequence names",
                            "callback": lambda: item.properties["custom_validations"][
                                "autofix_callback"
                            ](item),
                        }
                    },
                )
                return False
        return True

    def publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """

        message = "Publish plugin settings:\n%s" % pformat(settings)
        self.parent.engine.logger.debug(message)

        engine = self.parent.engine
        path = item.properties["scene_path"]
        sequence = item.properties["sequence"]
        for source_frame in list(sequence):
            # self.logger.info(source_frame)
            if not os.path.exists(source_frame):
                raise Exception("{} does not exist".format(source_frame))
        sequence_text_id = item.properties["sequence_text_id"]
        sequence_text2_id = item.properties.get("sequence_text2_id")

        # Getting settings
        primary_publish_template_setting = settings.get("Primary Publish Template")
        render_publish_template_setting = settings.get("Publish Render Template")
        render_publish_type_setting = settings.get("Publish Render Type")
        work_template_setting = settings.get("Work Template")

        image_sequence_workflow = settings.get("Image Sequence Workflow").value

        # We support two different workflows here:
        #
        # 1- The collector created a single main layer item and it
        #    contains all layers grouped in its properties
        #    under the "layers_sequences" key
        #
        # 2- The collector created all layers as individual items
        #
        # The main difference is related to how we can publish
        # the image sequences, where in the first case we can
        # do it ideally as a single published file, for example
        # as a single multi layer exr that we can combine in the
        # publish hook, and in a second case, where we are not
        # able to do a single publish, but we are forced to do
        # one publish per image sequence
        #
        # The collection workflow will be defined by a setting
        # which is set in the environment settings:
        # - Image Sequence Workflow
        #   "individual" or "grouped"
        #
        # TODO: Ideally we should control using an SG override
        #       we need to add the mty-framework-valueoverrides
        #       and define a setting for this case and update
        #       the code acoordingly
        # Get templates from settings

        primary_publish_template = engine.get_template_by_name(
            primary_publish_template_setting.value
        )
        render_publish_template = engine.get_template_by_name(
            render_publish_template_setting.value
        )
        work_template = engine.get_template_by_name(work_template_setting.value)

        # Fields from work path
        fields = work_template.get_fields(path)

        # Dynamic paths and elements from templates and settings
        primary_publish_path = primary_publish_template.apply_fields(fields)
        publish_version = fields["version"]

        # template can be dynamic for file extension, in that case we
        # need to set the proper image file extention
        ext = sequence._ext.lower().replace(".", "")
        fields["extension"] = ext

        message = "Item Properties:\n{}".format(pformat(item.properties))
        self.parent.engine.logger.debug(message)

        # optionally, if we need to add the layer name
        grouped_workflow = False
        if image_sequence_workflow == "individual":
            fields["layer"] = sequence_text_id
            fields["render.layer_name"] = sequence_text_id
            if sequence_text2_id:
                fields["buffer"] = sequence_text2_id
                fields["render.buffer"] = sequence_text2_id
        else:
            grouped_workflow = True

        self.parent.engine.logger.info(
            "sequence fields before applying them to render_publish_template:\n{}".format(
                pformat(fields)
            )
        )
        render_publish_path = render_publish_template.apply_fields(fields)
        render_publish_type_name = render_publish_type_setting.value

        # Ensure folder exists
        self.parent.engine.ensure_folder_exists(os.path.dirname(render_publish_path))

        # Proceed with the corresponding image sequence workflow
        description_updated = item.description
        if grouped_workflow:
            description_updated = self.sequence_grouped_workflow(
                item, sequence, render_publish_path
            )
        else:
            # Get the shot frame range to enforse it
            # on the publish image sequence
            sg_shot = self.get_sg_shot_info(["sg_cut_in", "sg_cut_out"])
            self.copy_image_sequence(
                sequence, render_publish_path, start_frame=sg_shot["sg_cut_in"]
            )

        # get media resolution
        media_resolution = self.parent.execute_hook_expression(
            "{config}/tk-multi-publish2/multi/resolution_from_path.py",
            "media_resolution_value",
            path=render_publish_path
        )
        self.parent.engine.logger.info(
            "media resolution from hook: {}".format(media_resolution)
        )

        # finally register the sequence publish
        message = "Publish sequence in shotgun"
        self.logger.info(message)

        message = "File name: {}".format(os.path.basename(render_publish_path))
        self.logger.info(message)

        publish_name = self.get_publish_name(render_publish_path)

        args = {
            "tk": self.parent.engine.sgtk,
            "context": item.context,
            "comment": description_updated,
            "path": render_publish_path,
            "name": publish_name,
            "version_number": publish_version,
            "thumbnail_path": item.get_thumbnail_as_path(),
            "task": self.parent.engine.context.task,
            "dependency_paths": [primary_publish_path],
            "sg_fields": {
                "sg_status_list": "rev",
                "task.Task.sg_status_list": "rev",
                "sg_media_resolution": media_resolution,
                },
            "published_file_type": render_publish_type_name,
        }

        sg_publishes = sgtk.util.register_publish(**args)

        root_item = self.get_root_item(item)
        self.parent.logger.debug(
            "Storing extra publish data on root item: {}".format(root_item)
        )
        root_item.properties.setdefault("sg_publish_extra_data", [])
        publish_extra_data = root_item.properties["sg_publish_extra_data"]
        publish_extra_data.append(sg_publishes)

        item.properties.sg_publish_data = sg_publishes
        item.properties.sg_publish_path = sg_publishes["path"]["local_path"]

        self.parent.logger.debug(
            "Adding existing publish "
            + "(id:{}) ".format(sg_publishes["id"])
            + "as extra data for item: {}".format(item)
        )

        self.logger.info("Publish sequence in shotgun. Complete")

        # Update task status for revision
        sg_task = self.parent.context.task
        self.parent.logger.info(
            "End of Publish and updating task with id: {} to status: 'rev'".format(
                str(sg_task["id"])
            )
        )

        try:
            self.parent.engine.shotgun.update(
                "Task", sg_task["id"], {"sg_status_list": "rev"}
            )
        except:
            pass

    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        pass

    def get_publish_name(self, path):
        import re
        import os

        regex = r"(?P<name>.*)(?P<ver>[._-]v\d+)(?P<padd>\.?[^.]+)?(?P<ext>\.[^.]+)"

        name = os.path.basename(path)
        match_data = re.match(regex, name).groupdict()

        for key in ["ver", "padd"]:
            value = match_data.get(key)
            if value:
                name = name.replace(value, "")

        return name

    def sequence_grouped_workflow(self, item, beauty_sequence, render_publish_path):
        """ """

        # Image sequence data
        beauty_base = beauty_sequence._base
        beauty_folder = beauty_sequence._dir
        beauty_ext = beauty_sequence._ext
        padding = len(beauty_sequence._frame_pad)
        frame_number = beauty_sequence.start()

        # Shot frame range
        sg_shot = self.get_sg_shot_info(["sg_cut_in", "sg_cut_out"])
        offset = sg_shot["sg_cut_in"] - frame_number

        created_multi_exr = False
        layer_names = []
        valid_layers_sequences = item.get_property(
            "valid_layers_sequences", default_value=[]
        )
        valid_layers_count = len(valid_layers_sequences)

        if valid_layers_count and beauty_ext.lower() == ".exr":
            # Multi process creation
            message = "Creating multilayer exr: {} Layers".format(valid_layers_count)
            self.parent.logger.debug(message)
            self.logger.info(message)

            format_str = "{}:{}::{}"
            openExr_framework = self.load_framework("mty-framework-openexr")
            openExr = openExr_framework.exrCoreTools
            openExr.set_binary("multipart")

            # Register names of all layer to add to the comments.
            layer_names.append(beauty_base[:-1])
            for sub_layer in valid_layers_sequences:
                layer_names.append(sub_layer._base[:-1])

            # Create folder if does not exists
            container_folder = os.path.dirname(render_publish_path)
            self.parent.engine.ensure_folder_exists(container_folder)

            for source_frame in beauty_sequence:
                # we will iterate over all frames, from first to last
                # and we will rename them using shot range
                final_frame = frame_number + offset
                destination_frame = render_publish_path % final_frame
                cmd = ["-combine", "-i"]
                padding = len(source_frame.split(".")[1])
                frame_str = str(frame_number).zfill(padding)

                beauty_file = "{}{}{}".format(beauty_base, frame_str, beauty_ext)
                beauty_path = join(beauty_folder, beauty_file)

                # First layer
                cmd.append(
                    format_str.format(source_frame, 0, item.properties["beauty_output"])
                )

                for layer_sequence in valid_layers_sequences:
                    base_name = layer_sequence._base
                    aov_folder = layer_sequence._dir
                    aov_ext = layer_sequence._ext
                    aov_file = "{}{}{}".format(base_name, frame_str, aov_ext)
                    aov_path = join(aov_folder, aov_file)
                    # Layer N
                    cmd.append(format_str.format(aov_path, 0, base_name[:-1]))

                cmd.append("-o")
                cmd.append(destination_frame)
                cmd.append("-override")

                _err, _info = openExr.execute_command_list(cmd)
                if _err:
                    created_multi_exr = False
                    message = "Failed to create multi exr: {}\ncommand: {}"
                    message = message.format(_info, cmd)
                    self.logger.error(message)
                    raise Exception(message)
                else:
                    created_multi_exr = True

                frame_number += 1

            message = "Creating multilayer exr: Complete"
            self.parent.logger.debug(message)
            self.logger.info(message)

        # If the multi layer fails, then we will copy the beauty
        if not created_multi_exr:
            self.copy_image_sequence(
                beauty_sequence, render_publish_path, start_frame=sg_shot["sg_cut_in"]
            )

        description_updated = item.description
        if len(layer_names):
            layers_str = ", ".join(layer_names)
            if description_updated is None:
                description_updated = ""
            description_updated += "\nLayers included: " + layers_str

        return description_updated

    def copy_image_sequence(self, sequence_object, sequence_path, start_frame=None):
        """
        Execute the copy of an image sequence frames using a custom hook

        :param sequence_object: fileseq.Sequence. The object representing the
                                image sequence
        :param sequence_path: str. Target path for the destination copy.
                              it must be defined in fstring format (%0{0-9}d)
        :param start_frame: int. Optional number to define the initial
                            frame number for the target path, which allows
                            for renumbering the image sequence frames
                            If not defined, the original sequence start frame
                            will be used
        """
        message = "Copy sequence to publish area. {}"
        message = message.format(os.path.basename(sequence_path))
        self.logger.info(message)
        self.parent.logger.info(message)

        if start_frame:
            frame_number = start_frame
        else:
            frame_number = sequence_object.start()

        for source_frame in sequence_object:
            # we will iterate over all frames, from first to last
            # and we will rename them using shot range

            source_frame = os.path.normpath(source_frame)

            destination_frame = sequence_path % frame_number
            message = "Copying image sequence frame:\nfrom: {0}\nTo: {1}"
            self.parent.logger.debug(message.format(source_frame, destination_frame))

            # Custom hook to copy files
            self.parent.execute_hook_expression(
                "{config}/tk-multi-publish2/harmony/copy_files.py",
                "execute",
                source=source_frame,
                destination=destination_frame,
            )

            frame_number += 1

        message = "Copy sequence to publish area. Complete"
        self.parent.logger.info(message)
        self.logger.info(message)

    def get_sg_shot_info(self, shot_fields):
        engine = self.parent.engine
        sg = engine.shotgun
        sg_proj = engine.context.project

        context_tokens = str(engine.context).split(" ")
        entity_name = context_tokens[2]
        shot_filter = [["project", "is", sg_proj], ["code", "is", entity_name]]
        # shot_fields  = ['sg_cut_in', 'sg_cut_out']
        sg_shot = sg.find_one("Shot", shot_filter, shot_fields)
        return sg_shot

    def get_sg_project_info(self, proj_fields):
        engine = self.parent.engine
        sg = engine.shotgun
        sg_proj = engine.context.project
        proj_filter = [["id", "is", sg_proj["id"]]]

        sg_proj = sg.find_one("Project", proj_filter, proj_fields)

        # get project resolution
        hook_expression = "{config}/get_entity_resolution.py"
        project_resolution = self.parent.engine.execute_hook_expression(
            hook_expression,
            "get_resolution",
            engine=self.parent.engine,
        )

        # ensure we add the resolution to the project_dict
        if sg_proj and project_resolution:
            sg_proj["sg_resolution"] = project_resolution

        return sg_proj

    def get_image_resolution(self, ffprobe_bin_path, imagemagick_bin_path, image_path):
        src = image_path
        if not src:
            msg = "Couldn't get a valid source image path."
            return False, msg

        # if "ffmpeg.exe" in ffmpeg_bin_path:
        #     ffprobe_bin_path = ffmpeg_bin_path.replace("ffmpeg.exe", "ffprobe.exe")
        # else:
        #     ffprobe_bin_path = ffmpeg_bin_path.replace("/ffmpeg", "/ffprobe")

        ffprobe_cmd = [
            ffprobe_bin_path,
            "-hide_banner",
            "-v",
            "error",
            "-of",
            "json",
            "-show_entries",
            "stream=width:stream=height",
            src,
        ]

        imagemagick_cmd = [
            imagemagick_bin_path,
            "-format",
            "%wx%h",
            src,
        ]
        imagemagick_cmd_str = " ".join(imagemagick_cmd)

        try:
            # try to get image resolution with ffprobe
            extract = subprocess.Popen(
                ffprobe_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                shell=True,
                encoding="utf-8",
            )

            stdout, stderr = extract.communicate()

            # Remove any curly bracket and white spaces that could be found in the stderr
            stderr = stderr.replace("{", "")
            stderr = stderr.replace("}", "")
            stderr = stderr.strip()
            if stderr:
                source_video_width = None
                source_video_height = None
                msg = "Can't retrieve source image width or height from {}.\nError: {}\ncmd: {}".format(
                    src, stderr, " ".join(ffprobe_cmd)
                )
                msg = "{}{}".format(
                    msg, "\nSource image resolution set to None"
                )
                self.parent.engine.logger.error(msg)
                raise Exception(msg)

            ffprobe_output = json.loads(stdout)

            # streams[0] MUST be the image stream, and usually is but be careful
            source_video_width = int(ffprobe_output["streams"][0]["width"])
            source_video_height = int(ffprobe_output["streams"][0]["height"])
        except:
            # try to get image resolution with ImageMagick
            extract = subprocess.Popen(
                imagemagick_cmd_str,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                shell=True,
                encoding="utf-8",
            )

            stdout, stderr = extract.communicate()

            if stderr:
                source_video_width = None
                source_video_height = None
                msg = "Couldn't retrieve source image width or height from {}.\nError: {}\ncmd: {}".format(
                    src, stderr, " ".join(imagemagick_cmd)
                )
                msg = "{}{}".format(
                    msg, "\nSource image resolution set to None"
                )
                self.parent.engine.logger.error(msg)

                return False, msg

            res_split = stdout.split("x")
            source_video_width = int(res_split[0])
            source_video_height = int(res_split[1])

        return True, [source_video_width, source_video_height]

    def validate_image_resolution(self, sequence, data_dict):
        ignore_tasks = data_dict.get("ignore_tasks", [])
        context_task = data_dict.get("context_task", {})
        sg_project_info = data_dict.get("sg_project_info", {})
        ffmpeg_framework = data_dict.get("ffmpeg_framework", None)
        imagemagick_framework = data_dict.get("imagemagick_framework", None)

        self.parent.logger.info("ignore_tasks:\n{}".format(ignore_tasks))
        self.parent.logger.info("context_task:\n{}".format(pformat(context_task)))
        self.parent.logger.info("sg_project_info:\n{}".format(pformat(sg_project_info)))
        self.parent.logger.info("ffmpeg_framework: {}".format(ffmpeg_framework))
        self.parent.logger.info("imagemagick_framework: {}".format(imagemagick_framework))

        context = self.parent.engine.context
        context_task = context.task
        context_task_name = context_task.get("name")

        if context_task_name in ignore_tasks:
            msg = "Skipping image resolution validation for task: {} due to override".format(
                context_task_name
            )
            self.parent.logger.info(msg)
            return True, msg

        ffmpeg_framework.ffmpegCore.set_binary("media")
        ffprobe_bin_path = ffmpeg_framework.ffmpegCore.get_bin_path()
        imagemagick_framework.imageMagickCore.set_binary("identify")
        imagemagick_bin_path = imagemagick_framework.imageMagickCore.get_bin_path()

        if not ffprobe_bin_path:
            error_msg = (
                "Couldn't get ffprobe path from mty-framework-ffmpeg "
                "while trying to validate image resolution."
            )
            self.parent.logger.error(error_msg)
            return False, error_msg

        if not imagemagick_bin_path:
            error_msg = (
                "Couldn't get imagemagick identify path from mty-framework-"
                "imagemagick while trying to validate image resolution."
            )
            self.parent.logger.error(error_msg)
            return False, error_msg

        self.parent.logger.info("ffprobe_bin_path: {}".format(ffprobe_bin_path))
        self.parent.logger.info("imagemagick_bin_path: {}".format(imagemagick_bin_path))
        self.parent.logger.info("sequence first file: {}".format(sequence))
        self.parent.logger.info("sequence start frame: {}".format(sequence.start()))

        valid_seq, image_resolution = self.get_image_resolution(
            ffprobe_bin_path, imagemagick_bin_path, list(sequence)[0]
        )
        if not valid_seq:
            error_msg = "Couldn't get resolution from image {}".format(
                list(sequence)[0]
            )
            self.parent.logger.error(error_msg)
            self.parent.engine.logger.error(error_msg)
            return False, error_msg

        image_res = "{}x{}".format(image_resolution[0], image_resolution[1])
        self.parent.logger.info("current image resolution: {}".format(image_res))

        if context_task["name"] not in ignore_tasks:
            msg = "Task {} not in ignore list. Validation will happen.".format(
                context_task["name"]
            )
            self.parent.logger.info(msg)
            self.parent.engine.logger.info(msg)

            if image_res != sg_project_info.get("sg_resolution", None):
                error_msg = (
                    "Image resolution {} doesn't math expected resolution {}".format(
                        image_res, sg_project_info.get("sg_resolution", None)
                    )
                )
                self.parent.logger.error(error_msg)
                self.parent.engine.logger.error(error_msg)
                return False, error_msg
            else:
                msg = (
                    "Image resolution {} matches expected resolution {}".format(
                        image_res, sg_project_info.get("sg_resolution", None)
                    )
                )
                self.parent.logger.info(msg)
                self.parent.engine.logger.info(msg)
                return True, msg
        else:
            msg = "Task {} in ignore list. Skipping validation.".format(
                context_task["name"]
            )
            self.parent.engine.logger.info(msg)

            # validate that resolution is divisible by 2
            if image_resolution[0] % 2 == 0 and image_resolution[1] % 2 == 0:
                return True, "Resolution validation skipped by default value."
            else:
                msg = (
                    "Resolution is not divisible by 2. Plase change your render "
                    "resolution or Scale-Output nodes to be divisible by, "
                    "save your scene (very important) and finaly try to publish "
                    "again. "
                    "Offending resolution: {}"
                ).format(image_res)
                self.parent.engine.logger.error(msg)
                return False, msg


    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root

    def __frame_mismatch_more_info(self):
        msg = (
            "Shot length and rendered frames don't match. This usually means your rendered "
            "frames sequence is shorter or longer than it should (than the shot length).\n"
            "\nTo fix this:"
            "\n    * delete the extra frames from your 'frames' folder so the sequence "
            "length match the shot length"
            "\n    * OR render the missing frames so the sequence length match the shot length"
            "\n    * OR remove everything from your 'frames' folder and render the shot "
            "again using the 'Render write nodes' execute action (SG menu > SG Execute "
            "Actions > General > Render write nodes)\n\n"
            "Remember: you need to close and re-open the publisher so the changes are "
            "reflected in the validation. If you don't do this, the publisher will keep "
            "reading the previous sequence length, regardless of the current length."
        )
        # QtGui.QMessageBox.error(None, "Shot length and rendered frames mismatch", msg)
        title = "Shot length and rendered frames mismatch"
        msg_box = self.show_message_box_message(title, msg)
        return msg_box

    def __resolution_mismatch_more_info(self):
        msg = (
            "The image resolution doesn't match the project, episode, sequence or shot "
            "resolution. This usually means you rendered at a different resolution "
            "than what the current context (project, episode, sequence or shot) "
            "expects.\n\n"
            "Please check what's the expected resolution or contact the pipeline "
            "support team form more information."
        )
        # QtGui.QMessageBox.error(None, "Shot length and rendered frames mismatch", msg)
        title = "Rendered frames resolution mismatch"
        msg_box = self.show_message_box_message(title, msg)
        return msg_box

    def show_message_box_message(self, title, msg):
        """
        Shows a message box
        """

        app = QtGui.QApplication.instance()
        msg_error = QtGui.QMessageBox()
        msg_error.setWindowTitle(title)
        msg_error.setText(msg)
        msg_error.setIcon(QtGui.QMessageBox.Critical)
        msg_error.exec_()
        # msg_error.show()

        return msg_error

    def fix_path(self, path):
        """
        Fixes the path to be compatible with the Harmony API, which only accepts forward
        slashes
        """
        # fix image_path
        path = path.replace("\\\\", "/")
        path = path.replace("\\", "/")

        return path
