"use strict";

include("harmony_utility_functions.js");

var META_SHOTGUN_PATH = "meta.shotgun.path";

function custom_import_3dObjet(
    filename, parent
)
{
    if (parent === undefined)
        parent = node.root();

    // var transparency = null;
    // var alignmentRule = null;
    var read_node = custom_dropObjInNewElement(
        parent, filename
    );
    MessageLog.trace("Created node: " + read_node);
    return read_node;
};

function custom_dropObjInNewElement(
    root, filename
)
{
    include("harmony_utility_functions.js");

    var good_position = get_good_scene_position(0, 100);
    var xPos = good_position.x;
    var yPos = good_position.y;

    var pos = filename.lastIndexOf( "." );
    if( pos < 0 )
        return null;

    extension = filename.substr(pos+1).toLowerCase();

    if( extension == "obj" )
    {
        vectorFormat = "OBJ"
        extension ="OBJ"; // element.add() will use this.
    }
    if( extension == "fbx" )
    {
        vectorFormat = "FBX"
        extension ="FBX"; // element.add() will use this.
    }

    var name = custom_basename(filename);
    var elemId = element.add(name, "COLOR", scene.numberOfUnitsZ(), extension.toUpperCase(), vectorFormat);
    if ( elemId == -1 )
    {
        // hum, unknown file type most likely -- let's skip it.
        return null; // no read to add.
    }

    var uniqueColumnName = get_unique_column_name(name);
    column.add(uniqueColumnName , "DRAWING");
    column.setElementIdOfDrawing( uniqueColumnName, elemId );

    var read = node.add(root, name, "READ", xPos, yPos, 0);

    // Add SG metadata (path)
    set_node_metadata(read, META_SHOTGUN_PATH, filename)

    // 3d enable for objets
    var read_enable_3d = node.getAttr(read, frame.current(), "ENABLE_3D");
    read_enable_3d.setValue(true);

    node.linkAttr(read, "DRAWING.ELEMENT", uniqueColumnName);

    var timing = "1"; // we're creating drawing name '1'

    Drawing.create(elemId, timing, true); // create a drawing drawing, 'true' indicate that the file exists.
    var drawingFilePath = Drawing.filename(elemId, timing);   // get the actual path, in tmp folder.
    copyFile( filename, drawingFilePath );

    // creat peg and subnodes
    var pegname = name + "-Peg"
    var newPeg = node.add(root, pegname, "PEG", xPos, yPos - 50, 0);
    var peg_enable_3d = node.getAttr(newPeg, frame.current(), "ENABLE_3D");
    peg_enable_3d.setValue(true);

    var subnodeanm_name = name +"-SubNodeAnim"
    var subnodeanm = node.add(root, subnodeanm_name, "SubNodeAnimation", xPos, yPos + 50, 0);
    // connect nodes

    node.link(newPeg, 0 , read, 0);
    node.link(read, 0 , subnodeanm, 0);


    //set exposure of all frames.
    var nframes = frame.numberOf();
    for( var i =1; i <= nframes; ++i)
    {
        column.setEntry(uniqueColumnName, 1, i, timing );
    }

    return read; // name of the new drawing layer.
}
