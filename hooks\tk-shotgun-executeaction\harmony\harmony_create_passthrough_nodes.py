#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

"""
An app that creates a "COMPOSITE" node for each selected node. This new node will
be used for creating the write nodes for output renderings. The name of each
passthrough node will be used for the name of each output sequence:

     ____________________
    |                    |
    |  OutputRenderName  |  Composite node named after the desired output sequence name
    | (passhtrough node) |
    |____________________|
              |
              |
     _________|__________
    |                    |
    |  OutputRenderName  |  Write node (created by a different hook) that will take the
    | (passhtrough node) |  name of the parent passthrough node for the sequence name
    |____________________|  and path:
                            root_scene_folder/frame/{OutputRenderName}/{OutputRenderName}

It creates a boolean attribute in each passthrough node called 'mtyPassthrough' set to
true. This allows us to collect specific write nodes for the publisher. This attribute
is not exposed in the interface so it can be modified only via scripting. This
functionality is used by another execute-action hook: "create write nodes"

"""

from tank import Hook
from tank import TankError
from tank.platform import Application
from tank.platform.qt import QtCore, QtGui
import tank

import os
import re
import pprint


class ProcessItemsHook(Hook):
    def execute(self, **kwargs):

        self.parent.engine.logger.info(
            "execute action create_output_passthrough_nodes start. {}".format("-" * 80)
        )

        result = self.create_output_passthrough_nodes()

        if not result:
            result = {"succes": [1], "messages": [], "errors": []}

        self.parent.engine.logger.info(
            "execute action create_output_passthrough_nodes end. {}".format("-" * 80)
        )

        return result

    def create_output_passthrough_nodes(self):

        result = {"succes": [1], "messages": [], "errors": []}

        # get passthrough_node_color from settings stored in tk-harmony.yml
        passthrough_node_color = self.parent.get_setting("passthrough_node_color")
        passthrough_node_color_str = str(passthrough_node_color)[1:-1]
        passthrough_attr_name = self.parent.get_setting("passthrough_attr_name")
        passthrough_attr_type = self.parent.get_setting("passthrough_attr_type")

        create_passthrough_nodes_cmd = """
include("harmony_utility_functions.js");

function create_passthrough_nodes() {
    var selected_nodes = selection.numberOfNodesSelected();

    var passthrough_nodes_array = [];
    if(selected_nodes > 0) {
        var x_offset = 140;
        var y_offset = 70;

        for(var n = 0; n < selected_nodes; n++) {
            // var parent_node = selection.selectedNode(n);
            var current_node = selection.selectedNode(n);
            log("Current node: " + current_node);
            var previous_node = "";
            try {
                previous_node = node.srcNode(current_node, 0);
            } catch (error) {
                log("Couldn't get input node: " + error);
            }
            log("Previous node: " + previous_node);

            var passthrough_node_color = new ColorRGBA(%s);
            var node_name = "RenderName";
            log("node_name: " + node_name);

            // if (passthrough_nodes_array.length > 0) {
            //     x_offset += 200;
            // };

            var passthrough_node = create_node(
                "COMPOSITE",
                previous_node,
                passthrough_node_color,
                node_name,
                x_offset,
                y_offset,
                current_node,
                false  // share parent?
            );
            log("new passthrough node: " + passthrough_node);

            create_attr(passthrough_node, "%s", "%s")

            passthrough_nodes_array.push(passthrough_node);
        };

        selection.clearSelection();
        selection.addNodesToSelection(passthrough_nodes_array);

    } else {
        MessageBox.information("You must select at least 1 node.");
    };
    return passthrough_nodes_array;
};

var passthrough_nodes_array = create_passthrough_nodes();

MessageBox.information(passthrough_nodes_array.length + " passthrough nodes were created");

""" % (
    passthrough_node_color_str,
    passthrough_attr_name,
    passthrough_attr_type
)

        self.parent.engine.logger.debug(create_passthrough_nodes_cmd)
        self.parent.engine.app.execute(create_passthrough_nodes_cmd)

        return result
