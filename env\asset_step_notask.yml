# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

description: Apps and Engines related to Asset based work.

################################################################################

includes:
- ./includes/frameworks.yml
- ./includes/settings/tk-desktop2.yml
- ./includes/settings/tk-3dsmax.yml
- ./includes/settings/tk-houdini.yml
- ./includes/settings/tk-mari.yml
- ./includes/settings/tk-maya.yml
- ./includes/settings/tk-motionbuilder.yml
- ./includes/settings/tk-nuke.yml
- ./includes/settings/tk-photoshopcc.yml
- ./includes/settings/tk-aftereffects.yml
- ./includes/settings/tk-alias.yml
- ./includes/settings/tk-vred.yml
- ./includes/settings/tk-shell.yml
- ./includes/settings/tk-shotgun.yml
- ./includes/settings/tk-harmony.yml
- ./includes/settings/tk-fusion.yml
- ./includes/settings/tk-krita.yml
- ./includes/settings/tk-substancepainter.yml
- ./includes/settings/tk-blender.yml

################################################################################
# configuration for all engines to load in an asset step context

engines:
  #tk-desktop2: "@settings.tk-desktop2.all"
  #tk-3dsmax: "@settings.tk-3dsmax.asset_step_notask"
  #tk-houdini: "@settings.tk-houdini.asset_step_notask"
  #tk-mari: "@settings.tk-mari.asset_step_notask"
  tk-maya: "@settings.tk-maya.asset_step_notask"
  #tk-motionbuilder: "@settings.tk-motionbuilder.asset_step_notask"
  #tk-nuke: "@settings.tk-nuke.asset_step_notask"
  #tk-nukestudio: "@settings.tk-nuke.nukestudio.asset_step_notask"
  tk-photoshopcc: "@settings.tk-photoshopcc.asset_step_notask"
  #tk-aftereffects: "@settings.tk-aftereffects.asset_step_notask"
  #tk-alias: "@settings.tk-alias.asset_step_notask"
  #tk-vred: "@settings.tk-vred.asset_step_notask"
  tk-shell: "@settings.tk-shell.asset_step_notask"
  tk-shotgun: "@settings.tk-shotgun.asset_step_notask"
  #tk-harmony: "@settings.tk-harmony.asset_step_notask"
  #tk-fusion: "@settings.tk-fusion.asset_step_notask"
  #tk-krita: "@settings.tk-krita.asset_step_notask"
  #tk-substancepainter: "@settings.tk-substancepainter.asset_step_notask"
  tk-blender: "@settings.tk-blender.asset_step_notask"

################################################################################
# reference all of the common frameworks

frameworks: "@frameworks"
