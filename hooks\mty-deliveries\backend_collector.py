################################################################################
#
# Copyright (c) 2021 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
################################################################################

import sgtk

from pprint import pformat

HookBaseClass = sgtk.get_hook_baseclass()


class BackendFramework(HookBaseClass):

    def collect(self):
        """
        A hook to define the required delivery backend logic for a production
        Depending on the different studio colaborations more than one might be
        requiered both at a studio and also on the same project

        A backend is just a generic class with common classes and methods that 
        perform different depending on the data schemas and transfer logic

        The backend can be defined directly in the hook or can also be loaded
        from other frameworks, which can works as follows:

        framework = self.load_framework("mty-framework-sampledeliveriesbackend")

        In this case, the framework is a class instance that have
        the following methods and classes inside it:

        - ingestManager (class instance as a property of the framework)
            A class that have all the logic to process the ingest

        - outgestManager (class instance as a property of the framework)
            A class that have all the logic to process the outgest

        ...

        """

        # default hook doesn't define any backend framework
        framework = self.load_framework("mty-framework-deliveries")

        return framework
