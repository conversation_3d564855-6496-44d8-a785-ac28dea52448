# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

description: Apps and Engines related to Sequence based work.

################################################################################

includes:
- ./includes/frameworks.yml
- ./includes/settings/tk-desktop2.yml
- ./includes/settings/tk-shell.yml
- ./includes/settings/tk-shotgun.yml
- ./includes/settings/tk-photoshopcc.yml
- ./includes/settings/tk-krita.yml
- ./includes/settings/tk-premiere.yml
- ./includes/settings/tk-photoshopcc.yml
- ./includes/settings/tk-blender.yml

################################################################################
# configuration for all engines to load in a sequence step context

engines:
  # referencing the DCC-specific episode_step environments included above
  tk-desktop2: "@settings.tk-desktop2.all"
  tk-shell: "@settings.tk-shell.episode_step"
  tk-shotgun: "@settings.tk-shotgun.episode_step"
  tk-photoshopcc: "@settings.tk-photoshopcc.episode_step"
  tk-premiere: "@settings.tk-premiere.episode_step"
  tk-krita: "@settings.tk-krita.episode_step"
  tk-blender: "@settings.tk-blender.episode_step"

################################################################################
# reference all of the common frameworks

frameworks: "@frameworks"
