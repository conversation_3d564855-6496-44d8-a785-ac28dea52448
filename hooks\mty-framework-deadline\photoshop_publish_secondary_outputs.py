################################################################################
#
# Copyright (c) 2024 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
################################################################################

import os
import re
import sys
import json
import sgtk
import fileseq
import pprint
import tempfile

from pprint import pformat

HookBaseClass = sgtk.get_hook_baseclass()


class PhotoshoPublishSecondaryOutputs(HookBaseClass):

    def publish_secondary_outputs(self, *args, **kwargs):
        """
esta funcino no funciona
"""
        self.logger.info("Exporting Secondary Outputs PSD")

        self.logger.info(pformat(kwargs))
        self.logger.info(pformat(args))

        self.engine = self.parent.engine
        self.shotgun = self.parent.engine.shotgun
        self.context = self.parent.engine.context

        self.secondary_items = kwargs.get('secondary_outputs_to_farm',{})
        primary_item_data = kwargs.get('primary_item_data',{})
        primary_item_data = self.get_all_data_of_primary_item(primary_item_data)

        self.logger.info("secondary_items {}".format(self.secondary_items))
        self.logger.info("primary_item_data {}".format(primary_item_data))


        primary_path = primary_item_data.get('path')
        self.description = primary_item_data.get("description")
        self.version_number = primary_item_data.get("version_number")

        if sys.platform == 'darwin':
            self.primary_path = primary_path.get("local_path_mac")
        if sys.platform == 'win32':
            self.primary_path = primary_path.get("local_path_windows")
        if sys.platform == 'linux':
            self.primary_path = primary_path.get("local_path_linux")

        psdutil_framework = self.parent.engine.custom_frameworks.get(
                "mty-framework-psdutils"
                ) or self.load_framework("mty-framework-psdutils")
        #load psd tools package
        psdutil_framework.import_psd_tools()
        self.psd_tools= psdutil_framework.corepsdtools

        self.metasync = self.parent.engine.custom_frameworks.get(
            'mty-framework-metasync'
        ) or self.load_framework('mty-framework-metasync')

        self.valueoverrides = self.parent.engine.custom_frameworks.get(
            'mty-framework-valueoverrides'
        ) or self.load_framework('mty-framework-valueoverrides')

        self.imagemagick = self.parent.engine.custom_frameworks.get(
            "mty-framework-imagemagick"
            ) or self.load_framework("mty-framework-imagemagick")
        self.ImageMagickCoreTools = self.imagemagick.imageMagickCore


        self.ffmpeg = (
                self.parent.engine.custom_frameworks.get("mty-framework-ffmpeg", None)
                or self.load_framework("mty-framework-ffmpeg")
            )

        sg_publishes = []
        layer_group_items = {}

        # publish secondary outputs layer
        if "photoshop.layergroups" in  self.secondary_items.keys():
            data_layergroups =  self.secondary_items.get('photoshop.layergroups')
            layer_group_items, sg_publishes_layers = self.photoshop_layergroups_publish(
                data_layergroups
            )
            sg_publishes.extend(sg_publishes_layers)
        
        if "photoshop.layergroupsproxies" in self.secondary_items.keys():
            data_layergroupsproxies =  self.secondary_items.get('photoshop.layergroupsproxies')
            layer_group_items, sg_publishes_layersproxies = self.photoshop_layergroupsproxies_publish(
                data_layergroupsproxies
            )
            sg_publishes.extend(sg_publishes_layersproxies)

        if layer_group_items:
            #export version and sequences files
            data_layergroups_review =  self.secondary_items.get('photoshop.layergroups') or self.secondary_items.get('photoshop.layergroupsproxies')
            templates_and_file_types = data_layergroups_review.get("templates_and_file_types")
            layer_review_paths, sg_published_sequence = self.publish_layer_groups_review_images(layer_group_items, templates_and_file_types)

            sg_publishes.append(sg_published_sequence)
            dependency_list = list(layer_group_items.keys())

            sg_pullishes_mov = self.create_and_register_video(sg_published_sequence, templates_and_file_types, dependency_list)
            sg_publishes.append(sg_pullishes_mov)

        if sg_publishes:
            process_synklogs = self.metasync.syncLogsManager.process_synklogs_for_publishes
            synclogs = process_synklogs(sg_publishes, self.metasync.hostLocation)

        self.logger.info("exporting secondary outputs psd on farm hook is done")
        return True
    
    def get_all_data_of_primary_item(self, primary_item_data):
        filters = [["id", "is", primary_item_data.get("id")]]
        fields = [
        "path",
        "description",
        "version_number",
        "code",
        "sg_is_primary_output",
        "sg_metadata",
        "task",
        "path"
        ]

        primary_item_data = self.shotgun.find_one(
            "PublishedFile",
            filters,
            fields
        )

        return primary_item_data

    def photoshop_layergroups_publish(self, dict_layergroup):

        layer_group_items= {}
        sg_publishes = []

        tempaltes_and_types = dict_layergroup.get('templates_and_file_types')
        layer_group_data = dict_layergroup.get('layer_group_data')

        self.logger.info("tempaltes_and_types {}".format(tempaltes_and_types))
        self.logger.info("layer_group_data {}".format(layer_group_data))

        #get fields from tempaltes_and_types
        primary_publish_template = self.parent.sgtk.template_from_path(self.primary_path)

        layergroup_publish_template_name = tempaltes_and_types.get("image_publish_template_name")
        layergroup_publish_template = self.engine.get_template_by_name(layergroup_publish_template_name)
        layer_group_type = tempaltes_and_types.get("image_file_type")


        self.psd = self.psd_tools.load_psd(self.primary_path)
        for name, dict_info_layer in layer_group_data.items():
            primary_fields = primary_publish_template.get_fields(self.primary_path)
            primary_fields["photoshop.layer_name"] = dict_info_layer.get("reformat_name")
            primary_fields["extension"] = "png"
            layer_index = dict_info_layer.get("index")

            image_publish_path = layergroup_publish_template.apply_fields(primary_fields)
            layer_name = dict_info_layer.get("original_name")

            self.logger.info("image_publish_path {}".format(image_publish_path))
            self.logger.info("layer_name {}".format(layer_name))

            self.psd_tools.export_single_layer_as_png(self.psd, image_publish_path, layer_name)
            # Get publish name
            publish_name = self.parent.util.get_publish_name(
                image_publish_path, sequence=False
            )
            self.parent.logger.info(
                "publish_name from util (not used): {}".format(publish_name)
            )

            publish_name = self.parent.execute_hook_expression(
                "{config}/tk-multi-publish2/multi/name_from_path_info.py",
                "get_publish_name",
                path=image_publish_path
            )
            self.parent.logger.info(
                "publish_name from hook: {}".format(publish_name)
            )


            # Create ublish data dict for registering the publish
            publish_data = {
                "comment": self.description,
                "path": image_publish_path,
                "name": publish_name,
                "version_number": self.version_number,
                "thumbnail": image_publish_path,
                "published_file_type": layer_group_type,
                "dependencies": [self.primary_path],
            }

            sg_publish = self.register_publish(**publish_data)
            sg_publishes.append(sg_publish)
            layer_group_items.update({image_publish_path: layer_index})


        return layer_group_items, sg_publishes


    def create_and_register_video(
        self,
        sg_published_sequence,
        teplates_and_types,
        extra_dependencies = [],
    ):
        sequence_path = sg_published_sequence["path"]["local_path"]
        video_type = teplates_and_types.get("video_file_type")
        video_template = teplates_and_types.get("video_publish_template_name")
        proxy_psd_template_name = teplates_and_types.get("publish_photoshop_proxy_template_name")
        template_video = self.engine.get_template_by_name(video_template)

        #get fields from tempaltes_and_types
        proxy_psd_template_template = self.engine.get_template_by_name(proxy_psd_template_name)
        primary_publish_template = self.parent.sgtk.template_from_path(self.primary_path)
        primary_fields = primary_publish_template.get_fields(self.primary_path)
        video_path = template_video.apply_fields(primary_fields)

        # Craete video from layer groups images
        self.build_video_from_images(sequence_path, video_path)
        publish_name = self.parent.execute_hook_expression(
                "{config}/tk-multi-publish2/multi/name_from_path_info.py",
                "get_publish_name",
                path=video_path
            )

        dependencies = [self.primary_path, sequence_path]
        path_proxy_psd = proxy_psd_template_template.apply_fields(primary_fields)
        if os.path.exists(path_proxy_psd):
            dependencies.append(path_proxy_psd)

        if extra_dependencies:
            dependencies.extend(extra_dependencies)

        sg_publish_data = {
            "comment": self.description,
            "path": video_path,
            "name": publish_name,
            "version_number": self.version_number,
            "thumbnail": None,
            "published_file_type": video_type,
            "dependencies": dependencies,
        }
        self.parent.logger.info(
            "video sg_publish_data:\n{}".format(pprint.pprint(sg_publish_data))
        )
        sg_video_publish = self.register_publish(**sg_publish_data)

        first, last = self.get_shot_range(sequence_path)

        # Generate version
        sg_version = self.submit_version(
            sequence_path,
            video_path,
            [sg_video_publish],
            self.parent.engine.context.task,
            self.description,
            True,
            first,
            last,
            "rev",
        )

        self.parent.shotgun.upload(
                    "Version",
                    sg_version["id"],
                    video_path,
                    "sg_uploaded_movie",
                )


        return sg_video_publish

    def photoshop_layergroupsproxies_publish(self, dict_layergroup_proxies):

        layer_group_items= {}
        sg_publishes = []
        self.sg_project_resolution = None
        self.layer_group_image_width = None

        tempaltes_and_types = dict_layergroup_proxies.get('templates_and_file_types')
        layer_group_proxy_data = dict_layergroup_proxies.get('layer_group_data')

        self.logger.info("tempaltes_and_types {}".format(tempaltes_and_types))
        self.logger.info("layer_group_data {}".format(layer_group_proxy_data))

        #get fields from tempaltes_and_types
        primary_publish_template = self.parent.sgtk.template_from_path(self.primary_path)

        layergroup_proxies_publish_template_name = tempaltes_and_types.get(
            "proxy_image_publish_template_name"
            )
        layergroup_proxies_publish_template = self.engine.get_template_by_name(
            layergroup_proxies_publish_template_name
            )
        layer_group_proxy_type = tempaltes_and_types.get("proxy_image_file_type")

        self.psd = self.psd_tools.load_psd(self.primary_path)
        for name, dict_info_layer in layer_group_proxy_data.items():
            primary_fields = primary_publish_template.get_fields(self.primary_path)
            primary_fields["photoshop.layer_name"] = dict_info_layer.get("reformat_name")
            primary_fields["extension"] = "png"
            layer_index = dict_info_layer.get("index")

            image_publish_path = layergroup_proxies_publish_template.apply_fields(primary_fields)
            layer_name = dict_info_layer.get("original_name")

            self.logger.info("image_publish_path {}".format(image_publish_path))
            self.logger.info("layer_name {}".format(layer_name))

            basename_layer = os.path.basename(image_publish_path)
            temp_folfer = tempfile.gettempdir()

            temp_path_layer = os.path.join(temp_folfer, basename_layer)
            self.delete_if_exists(temp_path_layer)

            # export layer
            self.psd_tools.export_single_layer_as_png(self.psd, temp_path_layer, layer_name)

            # convert to proxy
            self.create_proxy_image(temp_path_layer, image_publish_path)
            self.delete_if_exists(temp_path_layer)

            # Get publish name
            publish_name = self.parent.util.get_publish_name(
                image_publish_path, sequence=False
            )
            self.parent.logger.info(
                "publish_name from util (not used): {}".format(publish_name)
            )

            publish_name = self.parent.execute_hook_expression(
                "{config}/tk-multi-publish2/multi/name_from_path_info.py",
                "get_publish_name",
                path=image_publish_path
            )
            self.parent.logger.info(
                "publish_name from hook: {}".format(publish_name)
            )

            # Create ublish data dict for registering the publish
            publish_data = {
                "comment": self.description,
                "path": image_publish_path,
                "name": publish_name,
                "version_number": self.version_number,
                "thumbnail": image_publish_path,
                "published_file_type": layer_group_proxy_type,
                "dependencies": [self.primary_path],
            }
            sg_publish = self.register_publish(**publish_data)
            sg_publishes.append(sg_publish)
            layer_group_items.update({image_publish_path: layer_index})


        return layer_group_items, sg_publishes


    def publish_layer_groups_review_images(self,layer_group_paths, templates_and_types):

        layer_group_items = []

        self.resize_value = self.get_resize_value()

        image_publish_template_name = templates_and_types["image_publish_template_name"]
        image_publish_template = self.engine.get_template_by_name(image_publish_template_name)
        image_seq_publish_template_name = templates_and_types["image_seq_publish_template_name"]
        image_seq_publish_template = self.engine.get_template_by_name(image_seq_publish_template_name)

        for layer_group_path in layer_group_paths.keys():
            self.parent.logger.info("layer_group_path: {}".format(layer_group_path))

            layer_index = layer_group_paths[layer_group_path]

            fields = image_publish_template.get_fields(layer_group_path)
            fields["SEQ5"] = layer_index

            image_seq_publish_path = image_seq_publish_template.apply_fields(fields)

            # copy published layer group png image to sequence review path usimg the new
            # sequence review name as file name
            self.logger.info(
                "Converting sequence image '{}', please wait".format(
                    os.path.basename(image_seq_publish_path)
                )
            )
            self.parent.logger.info(
                "Converting sequence image '{}'".format(
                    os.path.basename(image_seq_publish_path)
                )
            )
            self.export_copy_image_mediareview(layer_group_path, image_seq_publish_path)

            layer_group_items.append(image_seq_publish_path)
            self.parent.logger.info(
                "image_seq_publish_path: {}".format(image_seq_publish_path)
            )

        # register publish for sequence images
        publish_name = self.parent.util.get_publish_name(
            image_seq_publish_path, sequence=True
        )

        # keys must match the values expected by the register_publish method dictionary
        # values and with the create_and_register_video dictionary keys
        publish_data = {
            "comment": self.description,
            "path": image_seq_publish_path,
            "name": publish_name,
            "version_number": self.version_number,
            "thumbnail": None,
            "published_file_type": templates_and_types["image_seq_file_type"],
            "dependencies": [self.primary_path],
        }

        self.parent.logger.info(
            "layer_group publish_data:\n{}".format(pprint.pformat(publish_data))
        )

        sg_publish = self.register_publish(**publish_data)

        return layer_group_items, sg_publish
    def register_publish(
            self,
            comment,
            path,
            name,
            version_number,
            thumbnail,
            published_file_type,
            dependencies=[],
        ):
            # get media resolution
            media_resolution = self.parent.execute_hook_expression(
                "{config}/tk-multi-publish2/multi/resolution_from_path.py",
                "media_resolution_value",
                path=path
            )
            self.parent.logger.info(
                "media resolution from hook: {}".format(media_resolution)
            )

            # Register the publish:
            publish_data = {
                "tk": self.parent.engine.sgtk,
                "context": self.context,
                "comment": comment,
                "path": path,
                "name": name,
                "version_number": version_number,
                "thumbnail_path": thumbnail,
                "task": self.context.task,
                "published_file_type": published_file_type,
                "dependency_paths": dependencies,
                "sg_fields": {
                    "sg_central_data_status": "avlb",
                    "sg_media_resolution": media_resolution,
                },
            }
            sg_publish = sgtk.util.register_publish(**publish_data)

            self.parent.tank.shotgun.update(
                "PublishedFile", sg_publish["id"], {"sg_status_list": "rev"}
            )

            return sg_publish

        # ----------------------------------------------------------------------------------
    def create_synclog(self, publish_path, sg_publish):
        """
        Creates a new synclog entity for the recently published file

        Returns None if successful or an error message if not
        """

        try:
            synclog_entity = "CustomNonProjectEntity08"
            source_location_entity = {
                "id": 1,
                "name": "Mighty",
                "type": "CustomNonProjectEntity12",
            }
            # name of synclog
            name_synclog = os.path.basename(publish_path)
            
            name_synclog = name_synclog.replace("_", "")
            name_synclog = ("{}_{}_upload").format(name_synclog, "mty-farm-api")

            data_synclog = {
                "code": name_synclog,
                "sg_publishedfile": sg_publish,
                "sg_status_list": "cmpt",
                "description": "Secondary Outputs from Deadline Synclog",
                "sg_owned_by": self.context.task,
                "sg_source": source_location_entity,
                "sg_destination": source_location_entity,
            }
            self.shotgun.create(synclog_entity, data_synclog)

            return None

        except:
            error_msg = "Synclog creation has failed for {}".format(publish_path)
            return error_msg

    def delete_if_exists(self, path):

        if os.path.exists(path):
            os.remove(path)

    def create_proxy_image(self, source_path, dest_path):
        # --------------------------------------------------------------------------</span>
                # Create proxy png image using imagemagick
                                # Get project resolution width
                if not self.sg_project_resolution:
                    self.sg_project_resolution = self._get_project_resolution_width()

                # Get image width using ffprobe
                if not self.layer_group_image_width:
                    self.layer_group_image_width = self._get_image_width(source_path)

                override_code_steps = 'mty.publisher.photoshopcc.layer_proxy_resize_percentage'
                scale_percentage = self.valueoverrides.get_value(override_code_steps)

                # check if scaling at scale_percentage is enough for the project resolution
                # if not, then we calculate a new percentage.
                proxy_image_width = self.layer_group_image_width / scale_percentage
                if proxy_image_width < self.sg_project_resolution:
                    scale_percentage = int(
                        ((self.sg_project_resolution * 1.2) * 100) / self.layer_group_image_width
                    )

                self.parent.logger.info(
                    "proxy image scale_percentage: {}".format(scale_percentage)
                )

                #cmd = ('{1} -resize {0}% {2}').format(
                #    scale_percentage, layer_group_path, image_review_publish_path
                #)
                cmd = [
                    source_path, "-resize",
                    "{}%".format(scale_percentage),
                    dest_path,
                ]
                self.export_copy_image(source_path, dest_path, cmd)

    def export_copy_image(self, source_path, dest_path, cmd=[]):

        if not os.path.exists(os.path.dirname(dest_path)):
            os.makedirs(os.path.dirname(dest_path))

        if not cmd:
            # default copy source to dest path command
            cmd = [source_path, "-resize", "20%", dest_path]

        self.ImageMagickCoreTools.set_binary("convert")
        imgagemagick_bin_path = self.ImageMagickCoreTools.get_bin_path()
        self.parent.logger.info(
            "imgagemagick_bin_path: {}".format(imgagemagick_bin_path)
        )

        self.parent.logger.info("EXPORT COPY: magick convert {}".format(cmd))
        _err, _out, executed_cmd = self.ImageMagickCoreTools.execute_command(cmd)
        if _err:
            raise Exception(
                (
                    "Failed to export image: {0} to {1}\n"
                    "Error: {2}\n"
                    "Executed command: {3}"
                ).format(
                    source_path, dest_path, _out, executed_cmd
                )
            )

    # ----------------------------------------------------------------------------------
    def export_copy_image_mediareview(self, source_path, dest_path, cmd=[]):

        if not os.path.exists(os.path.dirname(dest_path)):
            os.makedirs(os.path.dirname(dest_path))

        if not cmd:
            # default copy source to dest path command
            cmd = [source_path, "-resize", self.resize_value, dest_path]

        self.ImageMagickCoreTools.set_binary("convert")
        imgagemagick_bin_path = self.ImageMagickCoreTools.get_bin_path()
        self.parent.logger.info(
            "imgagemagick_bin_path: {}".format(imgagemagick_bin_path)
        )

        self.parent.logger.info("EXPORT COPY: magick convert {}".format(cmd))
        _err, _out, executed_cmd = self.ImageMagickCoreTools.execute_command(cmd)
        if _err:
            raise Exception(
                (
                    "Failed to export image: {0} to {1}\n"
                    "Error: {2}\n"
                    "Executed command: {3}"
                ).format(
                    source_path, dest_path, _out, executed_cmd
                )
            )

    # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
    def _get_project_resolution_width(self):
        result = None

        # Get project resolution from SG
        hook_expression = "{config}/get_entity_resolution.py"
        project_resolution = self.parent.execute_hook_expression(
            hook_expression,
            "get_resolution",
            engine=self.parent.engine,
        )

        if project_resolution:
            pattern = re.compile(r"(?P<width>\d+)x(?P<height>\d+)")
            match = re.match(pattern, str(project_resolution))
            if match:
                result = int(match.groupdict()["width"])

        return result

    def _get_image_width(self, image_path):
        """
        Get's the image original width resolution using ffprobe.
        """

        result = None

        if image_path and os.path.exists(image_path):
            
            FFmpegCoreTools = self.ffmpeg.ffmpegCore
            # "media" binary means ffprobe executable will be used
            FFmpegCoreTools.set_binary(binary='media')
            ffprobe_path = os.path.dirname(self.ffmpeg.ffmpegCore.get_bin_path())

            self.parent.engine.logger.info("ffprobe path: {}".format(ffprobe_path))

            ffprobe_cmd = (
                # "{} "
                "-hide_banner "
                "-loglevel panic "
                "-of json "
                "-show_entries stream=width "
                "{}"
            ).format(image_path)

            self.parent.engine.logger.info("ffprobe_cmd: {}".format(ffprobe_cmd))

            _err, _info = FFmpegCoreTools.execute_command(ffprobe_cmd)

            if  not _err:
                data = dict(json.loads(_info))
                streams = data.get("streams")
                if streams:
                    result = int(streams[0].get("width"))
                self.parent.engine.logger.info("original image width: {}".format(result))

        return result
    def build_video_from_images(self, sequence_path, video_path):
        seq_obj = fileseq.findSequenceOnDisk(sequence_path)
        seq_start_frame = seq_obj.start()

        convert_cmd = (
            '-r '
            '1/5 '
            '-start_number '
            '{0} '
            '-i '
            '{1} '
            '-vf '
            '"scale=1920:-2, '
            'premultiply=inplace=1" '
            '-vcodec '
            'libx264 '
            '-shortest '
            '{2} '
            '-y'
        ).format(str(seq_start_frame ), sequence_path, video_path)

        # ensure the right binary is set
        self.ffmpeg.ffmpegCore.set_binary("convert")
        # get the full patht o the binary and use it to log the fill command
        path_to_binary  = self.ffmpeg.ffmpegCore.get_bin_path()
        self.parent.logger.info(
            "convert cmd: {} {}".format(os.path.basename(path_to_binary), convert_cmd)
        )
        _err, _info = self.ffmpeg.ffmpegCore.execute_command(convert_cmd)

        if _err:
            raise Exception("Failed to convert images to video: {}".format(_info))

    # ----------------------------------------------------------------------------------
    def get_shot_range(self, sequence_path):
        """ """

        entity = self.parent.context.entity
        sg_entity_type = entity["type"]

        if not sg_entity_type == "Shot":
            file_sequence = fileseq.findSequenceOnDisk(sequence_path)
            self.parent.logger.info("file_sequence: {}".format(file_sequence))
            self.parent.logger.info("file_sequence start: {}".format(file_sequence.start()))
            self.parent.logger.info("file_sequence end: {}".format(file_sequence.end()))
            return file_sequence.start(), file_sequence.end()

        sg_filters = [["id", "is", entity["id"]]]
        sg_in_field = "sg_cut_in"
        sg_out_field = "sg_cut_out"
        fields = [sg_in_field, sg_out_field]

        data = self.parent.shotgun.find_one(
            sg_entity_type, filters=sg_filters, fields=fields
        )

        self.logger.debug(data)

        if not data:
            message = "Cant find a valid shotgun entity for: {}".format(entity)
            self.logger.error(message)
            raise Exception(message)

        first_frame = data.get(sg_in_field)
        last_frame = data.get(sg_out_field)

        if not first_frame or not last_frame:
            message = "Can't find a valid frame range from: {}".format(entity)
            self.logger.error(message)
            raise Exception(message)

        return first_frame, last_frame

    def submit_version(
        self,
        path_to_frames,
        path_to_movie,
        sg_publishes,
        sg_task,
        comment,
        store_on_disk,
        first_frame,
        last_frame,
        work_status,
        override_entity=False,
        has_slate=False,
    ):
        """
        Create a version in Shotgun for
        this path and linked to this publish.

        """

        user = self.parent.engine.context.user

        # create a name for the version based on the file name
        # grab the file name, strip off extension
        name = os.path.splitext(os.path.basename(path_to_movie))[0]
        name = name.replace("_", " ")
        # name = name.capitalize()

        if override_entity != False:
            entity = override_entity
        else:
            entity = self.parent.engine.context.entity

        # Create the version in Shotgun
        data = {
            "code": name,
            "sg_status_list": work_status,
            "entity": entity,
            "sg_task": sg_task,
            "sg_version_type": "Production",
            "sg_first_frame": first_frame,
            "sg_last_frame": last_frame,
            "frame_count": (last_frame - first_frame + 1),
            "frame_range": "{}-{}".format(first_frame, last_frame),
            "sg_frames_have_slate": False,
            "published_files": sg_publishes,
            "created_by": user,
            "description": comment,
            "sg_path_to_frames": path_to_frames,
            "sg_movie_has_slate": True,
            "project": self.parent.engine.context.project,
            "user": user,
            "sg_movie_has_slate": has_slate,
        }

        if store_on_disk:
            data["sg_path_to_movie"] = path_to_movie

        sg_version = self.parent.engine.shotgun.create("Version", data)

        self.parent.logger.debug("Created version in shotgun: {}".format(str(data)))

        return sg_version
    
    def get_resize_value(self):
        # get horizontal project resolution
        hook_expression = "{config}/get_entity_resolution.py"
        project_resolution = self.parent.execute_hook_expression(
            hook_expression,
            "get_resolution",
            engine=self.parent.engine,
        )

        if not project_resolution:
            error_msg = ("Couldn't get project resolution from SG.")
            self.parent.engine.logger.error(error_msg)
            return None

        pattern = re.compile(r"(?P<width>\d+)x(?P<height>\d+)")
        match = re.match(pattern, str(project_resolution))
        if not match:
            error_msg = ("Couldn't get image resolution.")
            self.parent.engine.logger.error(error_msg)
            return None

        project_width = int(match.groupdict()["width"])
        resize_value = "{0}x{0}".format(project_width)

        return resize_value
