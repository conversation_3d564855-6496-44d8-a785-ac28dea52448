# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

################################################################################

includes:
- ../app_locations.yml
- ../engine_locations.yml
- ./tk-multi-loader2.yml
- ./tk-multi-publish2.yml
- ./tk-multi-screeningroom.yml
- ./tk-multi-shotgunpanel.yml
- ./tk-multi-snapshot.yml
- ./tk-multi-workfiles2.yml
- ./tk-multi-breakdown2.yml

################################################################################

# asset
settings.tk-aftereffects.asset:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel.aftereffects"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.aftereffects"
  shelf_favorites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  location: '@engines.tk-aftereffects.location'

# asset_step
settings.tk-aftereffects.asset_step:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    tk-multi-loader2: "@settings.tk-multi-loader2.aftereffects"
    tk-multi-publish2: "@settings.tk-multi-publish2.aftereffects.asset_step"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel.aftereffects"
    tk-multi-snapshot: "@settings.tk-multi-snapshot.aftereffects.asset_step"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.aftereffects.asset_step"

    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'

    mty-executeaction-load-project-settings:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "mty | 1 Load project settings..."
      action_hook: "{config}/tk-shotgun-executeaction/afx/afx_asset_load_project_settings.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset" ]

    mty-executeaction-apply-settings-to-all-output-comps:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "mty | 2 Apply project settings to all output compositions..."
      action_hook: "{config}/tk-shotgun-executeaction/afx/afx_apply_settings_to_output_comps_in_project.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset" ]

    mty-executeaction-create-render-queue-items:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "mty | 3 Create render queue items..."
      action_hook: "{config}/tk-shotgun-executeaction/afx/afx_create_render_queue_items.py"
      sg_extended_fields: { }
      output_module: "PNG Sequence with Alpha"
      padding: 5
      sequence_extension: ".png"
      allowed_entities: [ "Asset" ]

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset" ]

  shelf_favorites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-snapshot, name: Snapshot...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  - {app_instance: tk-multi-publish2, name: Publish...}
  - {app_instance: mty-multi-scriptstoolbox, name: Toolbox}
  location: '@engines.tk-aftereffects.location'

# project
settings.tk-aftereffects.project:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel.aftereffects"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.aftereffects"
    mty-executeaction-ensure-folders:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Ensure Tasks Folders"
      action_hook: "{config}/tk-shotgun-executeaction/create_folders_for_assigned_tasks.py"
      sg_extended_fields: {}
      allowed_entities: ['Shot', 'Sequence', 'Asset', 'Project']
  shelf_favorites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  location: '@engines.tk-aftereffects.location'

# shot
settings.tk-aftereffects.shot:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel.aftereffects"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.aftereffects"
  shelf_favorites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  location: '@engines.tk-aftereffects.location'

# shot_step
settings.tk-aftereffects.shot_step:
  apps:
    tk-multi-about:
      location: "@apps.tk-multi-about.location"
    tk-multi-pythonconsole:
      location: "@apps.tk-multi-pythonconsole.location"
    tk-multi-loader2: "@settings.tk-multi-loader2.aftereffects"
    tk-multi-publish2: "@settings.tk-multi-publish2.aftereffects.shot_step"
    tk-multi-screeningroom: "@settings.tk-multi-screeningroom.rv"
    tk-multi-shotgunpanel: "@settings.tk-multi-shotgunpanel.aftereffects"
    tk-multi-snapshot: "@settings.tk-multi-snapshot.aftereffects.shot_step"
    tk-multi-workfiles2: "@settings.tk-multi-workfiles2.aftereffects.shot_step"
    tk-multi-breakdown2: "@settings.tk-multi-breakdown2.aftereffects.shot_step"

    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'

    mty-executeaction-load-template-and-settings:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "mty | 1 Load template and settings..."
      action_hook: "{config}/tk-shotgun-executeaction/afx/afx_shot_load_template_and_set_project_settings.py"
      sg_extended_fields: { }
      color_depth: 16
      user_template: "aftereffects_shot_template_user"
      allowed_entities: [ "Shot" ]

    mty-executeaction-apply-settings-to-all-output-comps:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "mty | 2 Apply shot settings to all output compositions..."
      action_hook: "{config}/tk-shotgun-executeaction/afx/afx_apply_settings_to_output_comps_in_project.py"
      sg_extended_fields: { }
      allowed_entities: [ "Shot" ]

    mty-executeaction-create-render-queue-items:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "mty | 3 Create render queue items..."
      action_hook: "{config}/tk-shotgun-executeaction/afx/afx_create_render_queue_items.py"
      sg_extended_fields: { }
      output_module: "PNG Sequence with Alpha"
      padding: 4
      sequence_extension: ".png"
      allowed_entities: [ "Shot" ]

    mty-executeaction-apply-preset-to-layer:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "mty | Apply preset to selected layer / fx..."
      action_hook: "{config}/tk-shotgun-executeaction/afx/afx_apply_preset_to_layer.py"
      sg_extended_fields: { }
      allowed_entities: [ "Shot" ]

    mty-executeaction-get-job-info:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "mty | Get output comps info..."
      action_hook: "{config}/tk-shotgun-executeaction/afx/afx_get_job_info.py"
      sg_extended_fields: { }
      allowed_entities: [ "Shot" ]

    mty-executeaction-show-rendered-outputs:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "mty | Show Rendered Outputs"
      action_hook: "{config}/tk-shotgun-executeaction/show_rendered_outputs.py"
      sg_extended_fields: { }
      allowed_entities: [ "Shot" ]

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Shot" ]

  shelf_favorites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-snapshot, name: Snapshot...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  - {app_instance: tk-multi-publish2, name: Publish...}
  - {app_instance: mty-multi-scriptstoolbox, name: Toolbox}
  location: '@engines.tk-aftereffects.location'
