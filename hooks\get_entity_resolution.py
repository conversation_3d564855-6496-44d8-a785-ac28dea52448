########################################################################################
#
# Copyright (c) 2024 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty Animation Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
########################################################################################

import sgtk
import json

HookBaseClass = sgtk.get_hook_baseclass()


class GetResolution(HookBaseClass):
    """Class to get the proper resolution for a given entity. It will search through
    the hierarchy of entities ( Project > Episode > Sequence > Shot ) and return the
    first not inherited value found."""

    def get_resolution(self, engine):
        """
        Depending on the entity type, it will search through the hierarchy of entities
        ( Project > Episode > Sequence > Shot ) and return the first not inherited value
        of the sg_resolution fields.
        """

        shotgun = engine.shotgun
        context = engine.context
        entity = context.entity
        currrent_task = context.task

        engine.logger.info("Getting resolution...")
        engine.logger.info(
            (
                "Curent values:\n"
                "context: {}\n"
                "entity: {}\n"
                "currrent_task: {}\n"
            ).format(context, entity, currrent_task)
        )

        valueoverrides = engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        ) or self.load_framework("mty-framework-valueoverrides")

        resolution_dict = None

        # Get resolutions dictionary from value override in case it exists
        if valueoverrides:
            default_value_code = "mty.toolkit.resolutions_dictionary"
            if currrent_task:
                override_link = {"type": "Task", "id": currrent_task["id"]}
            else:
                override_link = None
            resolution_dict = valueoverrides.get_value(
                default_value_code, link=override_link
            )
            if resolution_dict:
                resolution_dict = json.loads(resolution_dict)
                engine.logger.info("Got resolution dict from value override")

        if not resolution_dict:
            resolution_dict = {
                "Inherited": "Inherited",
                "HD - H (1280 x 720)": "1280x720",
                "HD - V (720 x 1280)": "720x1280",
                "Full HD - H (1920 x 1080)": "1920x1080",
                "Full HD - V (1080 x 1920)": "1080x1920",
                "2K DCI - H (2048 x 1080)": "2048x1080",
                "2K DCI - V (1080 x 2048)": "1080x2048",
                "4K UHD - H (3840x 2160)": "3840x2160",
                "4K UHD - V (2160 x 3840)": "2160x3840",
            }

        if entity:
            entity_id = entity["id"]
            entity_type = entity["type"]

            filters = [
                ["project", "is", context.project],
                ["id", "is", entity_id],
            ]
        else:
            # use the project data if the context doesn't have an entity
            entity_id = context.project.get("id")
            entity_type = "Project"

            filters = [
                ["id", "is", entity_id],
            ]

        if entity_type != "Project":
            fields = [
                # for all entities:
                "project.Project.sg_resolution",
                "sg_resolution",
                # for shot:
                "sg_sequence.Sequence.episode.Episode.sg_resolution",
                "sg_sequence.Sequence.sg_resolution",
                # for sequence:
                "episode.Episode.sg_resolution",
            ]
        else:
            fields = ["sg_resolution"]

        entity_data = shotgun.find_one(entity_type, filters, fields)

        project_resolution = entity_data.get("project.Project.sg_resolution") or entity_data.get("sg_resolution")
        episode_resolution = None
        sequence_resolution = None
        shot_resolution = None

        if entity_type == "Shot":
            episode_resolution = entity_data.get("sg_sequence.Sequence.episode.Episode.sg_resolution")
            sequence_resolution = entity_data.get("sg_sequence.Sequence.sg_resolution")
            shot_resolution = entity_data.get("sg_resolution")

        elif entity_type == "Sequence":
            episode_resolution = entity_data.get("episode.Episode.sg_resolution")
            sequence_resolution = entity_data.get("sg_resolution")

        elif entity_type == "Episode":
            episode_resolution = entity_data.get("sg_resolution")

        inherited_resolutions = ["Inherited", None]

        # use shot resolution if it's not inherited
        if (
            shot_resolution not in inherited_resolutions
            and shot_resolution in resolution_dict.keys()
        ):
            engine.logger.info(
                "Got resolution from shot: {} - {}".format(
                    shot_resolution, resolution_dict[shot_resolution]
                )
            )
            return resolution_dict[shot_resolution]

        # use sequence resolution if it's not inherited
        elif (
            sequence_resolution not in inherited_resolutions
            and sequence_resolution in resolution_dict.keys()
        ):
            engine.logger.info(
                "Got resolution from sequence: {} - {}".format(
                    sequence_resolution, resolution_dict[sequence_resolution]
                )
            )
            return resolution_dict[sequence_resolution]

        # use episode resolution if it's not inherited
        elif (
            episode_resolution not in inherited_resolutions
            and episode_resolution in resolution_dict.keys()
        ):
            engine.logger.info(
                "Got resolution from episode: {} - {}".format(
                    episode_resolution, resolution_dict[episode_resolution]
                )
            )
            return resolution_dict[episode_resolution]

        # use project resolution if none of the above were set
        elif project_resolution in resolution_dict.keys():
            engine.logger.info(
                "Got resolution from project: {} - {}".format(
                    project_resolution, resolution_dict[project_resolution]
                )
            )
            return resolution_dict[project_resolution]
        else:
            engine.logger.info(
                "Got resolution from project: {}".format(project_resolution)
            )
            return project_resolution

        # fallback return
        return project_resolution
