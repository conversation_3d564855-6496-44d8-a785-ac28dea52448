# Copyright (c) 2018 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

"""
Hook that gets executed every time an engine has been fully initialized.
"""

import os
import re
import sys
import json
import pprint
import socket
import shutil
import getpass
import datetime
import platform
import subprocess

from tank import Hook
from tank.platform.qt import QtCore, QtGui


pp = pprint.pprint
pf = pprint.pformat

class EngineInit(Hook):
    def execute(self, engine, **kwargs):
        """
        Executed when a Toolkit engine has been fully initialized.

        At this point, all apps and frameworks have been loaded,
        and the engine is fully operational.

        The default implementation does nothing.

        :param engine: Engine that has been initialized.
        :type engine: :class:`~sgtk.platform.Engine`
        """

        # import external modules so they are available in all engines
        config_path = os.path.dirname(os.path.dirname(self.disk_location))

        python_modules_path = os.path.join(
            config_path, "hooks", "external_python_modules"
        )
        python_internal_modules_path = os.path.join(
            config_path, "hooks", "internal_python_modules"
        )
        python_future_path = os.path.join(python_modules_path, "python-future")
        if not os.getenv("SG_USER_PROFILE"):
            if platform.system() == "Windows":
                user_profile_path = os.environ["USERPROFILE"]
            else:
                user_profile_path = os.environ["HOME"]
            os.environ["SG_USER_PROFILE"] = user_profile_path

        self.logger.info("engine_init config_path: {}".format(config_path))
        self.logger.info(
            "engine_init python_modules_path: {}".format(python_modules_path)
        )
        self.logger.info(
            "engine_init python_internal_modules_path: {}".format(
                python_internal_modules_path
            )
        )
        self.logger.info(
            "engine_init python_future_path: {}".format(python_future_path)
        )
        self.logger.info(
            "SG_USER_PROFILE env var: {}".format(os.getenv("SG_USER_PROFILE"))
        )

        sys.path.append(python_modules_path)
        sys.path.append(python_internal_modules_path)
        sys.path.append(python_future_path)

        # Add env variable for the ca_certs
        certs_path = os.path.join(config_path, "core", "cacert.pem")
        os.environ["SHOTGUN_API_CACERTS"] = certs_path

        # Sentry section ---------------------------------------------------------------
        # TODO: fix sentry dependencies to be compatible con maya
        # # setup sentry
        # import sentry_sdk
        # from sentry_sdk.integrations.excepthook import ExcepthookIntegration
        # sentry_sdk.init(
        #     dsn="https://<EMAIL>/4504173613481984",
        #     # Set traces_sample_rate to 1.0 to capture 100%
        #     # of transactions for performance monitoring.
        #     # We recommend adjusting this value in production.
        #     traces_sample_rate=1.0,
        #     integrations=[ExcepthookIntegration(always_run=True)]
        # )
        # sentry_sdk.set_tag("engine", engine.name)
        # sentry_sdk.set_tag("sguser", engine.context.user['name'])
        # sentry_sdk.set_tag("project", engine.context.project['name'])

        # basic imports
        import fileseq
        import timecode
        import six

        # RV section -------------------------------------------------------------------
        rv_path, rv_executable = self.get_rv_path()

        if rv_path:
            self.logger.info("rv_path: {}".format(rv_path))
            current_path = os.environ["PATH"]
            if current_path:
                updated_path = "{};{}".format(current_path, rv_path)
                # set the updated path env variable
                os.environ["PATH"] = updated_path

            rv_executable_path = os.path.join(rv_path, rv_executable)
            rv_executable_path = self.fix_path(rv_executable_path)
            self.logger.info("rv_executable_path: {}".format(rv_executable_path))
            # Set rv executable as environment variable
            os.environ["RV_PATH"] = rv_executable_path

        # collect custom paths and add them to pythonpath
        pythonpath_fallbacks = os.environ.get("PYTHONPATH_FALLBACKS")
        if pythonpath_fallbacks:
            pythonpath_fallbacks = pythonpath_fallbacks.split(os.pathsep)
            self.logger.info("pythonpath_fallbacks: {}".format(pythonpath_fallbacks))
            sys.path.extend(pythonpath_fallbacks)

        # hiyack the most default toolkit app to use it
        # as a generic framework loader, with the help of
        # a custom hook where we actually load them
        self.logger.info("Apps:\n{0}".format(pprint.pformat(engine.apps)))
        handler_app = (
            engine.apps.get("tk-multi-devutils")
            or engine.apps.get("tk-multi-about")
            or engine.apps.get("tk-multi-publish2")
            or engine.apps.get("mty-multi-queue")
            or engine.apps.get("tk-multi-screeningroom")
        )
        self.logger.info("handler_app: {}".format(handler_app))

        avoid_frameworks = {
            "tk-blender": ["mty-framework-deliveries"]
        }
        custom_frameworks = handler_app.execute_hook_expression(
            "{config}/frameworks.py", "collect",
            avoid_frameworks=avoid_frameworks.get(engine.name, [])
        )

        # attach the custom frameworks to the engine as a property
        setattr(engine, "custom_frameworks", custom_frameworks)

        # # ensure the Metasync framework python modules are in pythonpath
        # metasync = custom_frameworks.get("mty-framework-metasync")
        # self.logger.info("MetaSync Framework: {}".format(metasync))
        # metasync_path = os.path.join(metasync.current_path(), "python")
        # self.logger.debug("metasync path: %s" % metasync_path)
        # sys.path.append(metasync_path)

        # ensure the ValueOverrides framework python modules are in pythonpath
        valueoverrides = custom_frameworks.get("mty-framework-valueoverrides")
        self.logger.info("ValueOverrides Framework: {}".format(valueoverrides))

        if valueoverrides:
            self.logger.info(
                "valueoverrides.disk_location: {}".format(valueoverrides.disk_location)
            )
            self.logger.info(
                "valueoverrides_path: {}".format(
                    os.path.join(valueoverrides.disk_location, "python")
                )
            )
            valueoverrides_path = os.path.join(valueoverrides.disk_location, "python")
            self.logger.debug("valueoverrides path: %s" % valueoverrides_path)
            sys.path.append(valueoverrides_path)

        # set attrs for notifications
        notifications = Notifications(engine)
        setattr(engine, "success_sound", notifications.success_sound)
        setattr(engine, "error_sound", notifications.error_sound)
        setattr(engine, "finalized_sound", notifications.finalized_sound)
        setattr(engine, "confirm_sound", notifications.confirm_sound)
        setattr(engine, "dual_confirm_sound", notifications.dual_confirm_sound)

        if engine.name == "tk-desktop":
            self.logger.info(
                "Engine desktop available apps:\n{}".format(
                    pprint.pformat(engine.apps)
                )
            )
            # TODO: once the desktop engine is loaded, ask for the user credentials
            # (depending on the current backend) to have them available throughout
            # the pipeline

            # Check SG Desktop version -------------------------------------------------
            sg_desktop_path, sg_desktop_executable = self.get_sg_desktop_path()

            if sg_desktop_path:
                self.logger.info("sg_desktop_path: {}".format(sg_desktop_path))
                bootstrap_version_path = os.path.join(
                    sg_desktop_path,
                    "Resources",
                    "Desktop",
                    "Python",
                    "helpers",
                    "bootstrap_version.py",
                )
                self.logger.info("temp_path: {}".format(bootstrap_version_path))

                # find the desktop version by reading the bootstrap_version.py file
                if os.path.exists(bootstrap_version_path):
                    self.logger.info("bootstrap_version_path exists")
                    version_string = None
                    allowed_sg_desktop_version = None
                    version_pattern = re.compile(
                        (
                            r'(?P<head>.+\")'
                            r'(?P<version>v\d{1,2}\.\d{1,2}\.\d{1,2})'
                        )
                    )
                    with open(bootstrap_version_path, 'r') as file:
                        lines = file.readlines()
                    for line in lines:
                        match = version_pattern.search(line)
                        if match:
                            version_string = match.groupdict().get("version")
                            break
                    self.logger.info("SG Desktop version: {}".format(version_string))

                    valueoverrides = engine.custom_frameworks.get(
                        "mty-framework-valueoverrides"
                    )
                    if valueoverrides:
                        value_code = "mty.engine.desktop.version_check.allowed_version"
                        allowed_sg_desktop_version = valueoverrides.get_value(value_code)
                        self.logger.info(
                            "allowed_sg_desktop_version from overrides: {}".format(
                                allowed_sg_desktop_version
                            )
                        )

                    # if version_string:
                    #     pass
                    #     if allowed_sg_desktop_version:
                    #         if version_string != allowed_sg_desktop_version:
                    #             msg = (
                    #                 "Wrong SG Desktop version detected: {}. Please "
                    #                 "install version {} or contact pipeline support"
                    #             ).format(version_string, allowed_sg_desktop_version)
                    #             self.logger.error(msg)
                    #             engine.error_sound()
                    #             self.show_message(
                    #                 "SG Desktop", msg, "error"
                    #             )
                    #             sys.exit(1)
                    #         else:
                    #             self.logger.info(
                    #                 "SG Desktop version is correct, continuing..."
                    #             )
                    # else:
                    #     msg = (
                    #         "SG Desktop version could not be detected. Please contact "
                    #         "pipeline support.\n\nThe required SG Desktop version is "
                    #         "{}"
                    #     ).format(allowed_sg_desktop_version)
                    #     self.logger.error(msg)
                    #     engine.error_sound()
                    #     self.show_message("SG Desktop", msg, "error")
                    #     sys.exit(1)
            else:
                self.logger.warning("sg_desktop_path not found")

            # final message after engine has been initialized
            title = "Initizlization finished"
            separator = "=" * 32
            msg = (
                "\n{0}"
                "\n{1} initialization finished.\nYou can continue working now."
                "\n{0}"
            ).format(separator, engine.name)
            self.logger.info(msg)

            # play desktop init sound
            engine.execute_hook_expression(
                "{config}/notifications.py",
                "desktop_init_sound",
            )
            self.show_message(title, msg, "info")

        if engine.name == "tk-houdini":
            project_path = engine.sgtk.project_path

            message = "Seting project path as $JOB: {0}"
            engine.log_info(message.format(project_path))
            os.environ["JOB"] = project_path.replace("\\", "/")

        if engine.name == "tk-harmony":
            hooks_location = os.path.dirname(os.path.dirname(self.disk_location))

            harmony_scripts = os.path.join(
                hooks_location, "hooks", "tk-harmony", "scripts"
            )

            os.environ["TOONBOOM_GLOBAL_SCRIPT_LOCATION"] = harmony_scripts

            DAYS_DIFFERENCE_THRESHOLD = 35

            def close_harmony(temp_root, option):
                option_1 = (
                    "We were not able to validate your Harmony License.\n"
                    "Please restart Harmony."
                )
                option_2 = (
                    "Your Harmony license seems non-legit!\n"
                    "Please uninstall it, install the production version "
                    "and properly license it!\n\n"
                    "Remember: you must use a legit license, "
                    "either provided by Mighty, or your own."
                    "\n\nWe will close this session since you "
                    "should not use this license for working."
                )
                if option == 1:
                    engine.app.show_message(option_1)
                elif option == 2:
                    engine.app.show_message(option_2)

                engine.log_info(
                    "Deleting temporary startup file: {0}".format(temp_root)
                )
                try:
                    shutil.rmtree(temp_root)
                except:
                    pass

                result = engine.app.execute(
"""
    Scene.closeSceneAndExit()
"""
                )

                return

            def validate_or_not(user, host_name, allowed_users_dict):
                if user in allowed_users_dict.keys():
                    user_hosts_list = allowed_users_dict.get(user)
                    if host_name in user_hosts_list:
                        return True

                return False

            def find_toon_boom_directory_mac_os():
                search_paths = [
                    # r'/Applications/Toon Boom Harmony\s*\d*',      # Take care: one test (Pepi's Mac) got "Found Harmony installation dir: /Applications/Toon Boom Harmony 17 Premium"
                    r"/Applications/Toon Boom Harmony\s*2\d*"  # /Applications/Toon Boom Harmony 2*
                    # Add more regular expressions if necessary
                ]

                for pattern in search_paths:
                    regex = re.compile(pattern)
                    for root, dirs, files in os.walk("/Applications"):
                        for dir in dirs:
                            if regex.match(os.path.join(root, dir)):
                                return os.path.join(root, dir)

                return None

            def find_toon_boom_license_directory_mac_os(toom_boom_dir):
                for root, dirs, files in os.walk(toom_boom_dir):
                    for dir in dirs:
                        _ = os.path.join(
                            root,
                            dir,
                            "Contents",
                            "Applications",
                            "LicenseWizard.app",
                            "Contents",
                            "MacOS",
                            "ClientActivation.app",
                            "Contents",
                            "MacOS",
                        )
                        if os.path.exists(_):
                            return _

                # TODO: Why in macOS, even for the same architecture (x86_64/Intel) Harmony path installation
                # could be not the same?
                # Next one was included because it seems that, for other Harmony installation
                # (i.e. for Paola "Pepi" Mac) path changes. Please note that could be related with
                # Orlando's comment:
                # "the mac installation has options... in windows there is standalone and database or something like this"
                for root, dirs, files in os.walk(toom_boom_dir):
                    for dir in dirs:
                        _ = os.path.join(
                            root,
                            dir,
                            "Contents",
                            "Applications",
                            "LicenseWizard.app",
                            "Contents",
                            "MacOS",
                        )
                        if os.path.exists(_):
                            return _

                return None

            def find_main_container_dir_harmony():
                toon_boom_directory = find_toon_boom_directory_mac_os()

                if not toon_boom_directory:
                    error = "Unable to find Harmony installation dir for {}".format(
                        PLATFORM
                    )
                    self.logger.error(error)
                    engine.app.show_message(error)
                    raise Exception(error)

                self.logger.info(
                    "Found Harmony installation dir: {}".format(toon_boom_directory)
                )

                return toon_boom_directory

            def get_flexlm_dir():
                if sys.platform == "win32":
                    return "C:/flexlm"
                # TODO: fix mac and linux paths
                elif sys.platform == "darwin":
                    return "/usr/local/flexlm"
                else:
                    return "/usr/lib/flexlm"

            def get_flexlm_license_file():
                flexlm_dir = get_flexlm_dir()
                if os.path.exists(flexlm_dir):
                    license_file_path = "{}/license.dat".format(flexlm_dir)
                    if os.path.exists(license_file_path):
                        engine.logger.info("Found license file: {}".format(license_file_path))
                        return license_file_path

                return None

            def validate_connection_with_vpn():
                result = True
                # this ip is hardcoded to where the license server is installed
                command = ["ping", "**********"]
                ping = subprocess.run(command, capture_output=True, text=True)

                # only find the "100%" because the language may vary depending on the pc
                # example: "(100% loss)" 0r "(100% perdidos)"
                if "100%" in ping.stdout:
                    result = False

                return result

            def validate_server_information_from_license_file():
                """
                Get server information from the license file.
                Reads the license file content and checks for server information.
                Returns True if server information matches, False otherwise.
                """
                license_file_path = get_flexlm_license_file()
                if not license_file_path:
                    engine.logger.error("Floating license file not found!")
                    return None

                with open(license_file_path, "r") as f:
                    license_file_content = f.readlines()
                    engine.logger.info("Floating license file read, logging lines:")

                for line in license_file_content:
                    engine.logger.info(line)
                    if "**********" in line and "20001" in line:
                        engine.logger.info("License server information validated!")
                        # verify ping with server
                        if validate_connection_with_vpn():
                            return True
                return False

            def validate_license(path_to_client_activation, shell):
                client_activation = path_to_client_activation

                self.logger.debug("About to run: {}".format(client_activation))

                command = [client_activation, "-batch", "-list"]

                # check license using command line
                lic_check = subprocess.Popen(
                    command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=shell
                )
                stdout, stderr = lic_check.communicate()
                stdout = six.ensure_str(stdout)
                stderr = six.ensure_str(stderr)

                # if Popen returned error, show the error and then close Harmony
                if stderr:
                    engine.logger.error("Errors occurred while trying to validate license.")
                    engine.app.show_message(stderr)
                    close_harmony("", 1)
                    return

                # if no stdout was returned, close Harmony
                if not stdout:
                    engine.logger.error("Errors occurred while trying to validate license.")
                    close_harmony("", 1)
                    return

                engine.logger.warning(
                        "Trying to get license server info..."
                )

                # Check if the flexlm license file contains our server information
                valid_license_server = validate_server_information_from_license_file()
                if valid_license_server:
                    legit_message = (
                        "Your Harmony license seems legit. "
                        "You can continue working."
                    )
                    engine.app.show_message(legit_message)
                    engine.logger.info("Legit license server information found!")
                if not valid_license_server:
                    engine.logger.warning(
                        ("Your aren't using a harmony license server. "
                         "now try to get stand alone license")
                )
                    expiration_dates = []
                    # iterate through the lines of stdout and collect expiration dates
                    for line in stdout.splitlines():
                        if line.startswith("Expiration date:"):
                            expiration_dates.append(line.split(" ")[-1])

                    # if expirations dates are not present in stdout, close Harmony
                    if not expiration_dates:
                        engine.logger.error(
                            "Errors occurred while trying to validate license."
                        )
                        close_harmony("", 1)
                        return

                    # format expiration dates to obtain days difference
                    latest_exipiration_date = expiration_dates[-1]
                    now_date = datetime.datetime.now()

                    if "permanent" in latest_exipiration_date:
                        exp_date_formatted = datetime.datetime.strftime(
                            now_date, "%Y-%m-%d"
                        )
                    else:
                        exp_date_formatted = datetime.datetime.strptime(
                            latest_exipiration_date, "%d-%b-%Y"
                        ).strftime("%Y-%m-%d")
                    now_date_formatted = datetime.datetime.strftime(
                        now_date, "%Y-%m-%d"
                    )

                    exp_date_num_list = [int(n) for n in exp_date_formatted.split("-")]
                    now_date_num_list = [int(n) for n in now_date_formatted.split("-")]

                    exp_date_obj = datetime.date(
                        exp_date_num_list[0], exp_date_num_list[1], exp_date_num_list[2]
                    )

                    now_date_obj = datetime.date(
                        now_date_num_list[0], now_date_num_list[1], now_date_num_list[2]
                    )

                    days_difference = (now_date_obj - exp_date_obj).days

                    if days_difference > DAYS_DIFFERENCE_THRESHOLD:
                        engine.logger.warning("license has expired.")
                        engine.app.show_message(
                            (
                                "Seems like your license has expired.\n\n",
                                "or you are not connecting whit the license server.\n\n"
                                "Please contact the production team ",
                                "to get a new license.",
                            )
                        )
                        if stderr:
                            engine.app.show_message(stderr)
                    else:
                        engine.logger.info("license has not expired, it is still valid.")

                        legit_message = (
                            "Your Harmony license seems legit. "
                            "You can continue working."
                        )
                        engine.app.show_message(legit_message)
                        engine.log_info("Legit license found!")

            # # Custom template code section start ---------------------------------------
            # config_location = os.path.dirname(os.path.dirname(self.disk_location))

            # # ----------------------------------------------------------
            # # start from a custom template file (from config)
            # template_key = "SGTK_HARMONY_CUSTOM_STARTUP_TEMPLATE"
            # harmony_template = os.environ.get(template_key)

            # self.logger.info(
            #     "Starting from template file: {0}".format(harmony_template)
            # )

            # # but make a copy to a temporal path
            # temp_root = tempfile.mkdtemp()
            # shutil.copytree(
            #     os.path.dirname(harmony_template),
            #     # the destination directory, must not already exist
            #     os.path.join(temp_root, "startup"),
            # )

            # # ----------------------------------------------------------
            # time.sleep(2)

            # open_path = os.path.join(temp_root, "startup", "template.xstage")
            # open_path = open_path.replace("\\", "/")
            # ideal_path = ""
            # iteration = 0

            # while ideal_path != open_path and iteration < 3:
            #     engine.app.open_project(open_path)

            #     # Since harmony engine relies on socket communication
            #     # after opening a project we need to wait a bit,
            #     # otherwise the scene will not be ready and the next
            #     # command will not have access to the scene data
            #     time.sleep(3)
            #     ideal_path = engine.app.get_current_project_path()
            #     # ideal_path = ideal_path.replace('/', '\\')

            #     self.logger.info("ideal_path: {}".format(ideal_path))
            #     self.logger.info("open_path: {}".format(open_path))
            #     self.logger.info("Open project try: {}".format(iteration + 1))
            #     iteration += 1

            # time.sleep(1)
            # if ideal_path != open_path:
            #     close_harmony(temp_root, 1)
            #     return

            # self.logger.info("Opened project path: {0}".format(open_path))
            # # Custom template code section end -----------------------------------------

            # list of allowed users and their hosts
            valueoverrides = engine.custom_frameworks.get(
                "mty-framework-valueoverrides"
            )
            if valueoverrides:
                value_code = "mty.engine.harmony.license_check.allowed_users_dict"
                data = valueoverrides.get_values(value_code)
                allowed_users_dict = json.loads(data.get(value_code, "{}"))
                self.logger.debug(
                    "allowed_users_dict:\n{}".format(pprint.pformat(allowed_users_dict))
                )
            else:
                allowed_users_dict = {
                    # "Luisa": ["MLSV"],
                    "render": ["MightyWS53"],
                    # "micro": ["DESKTOP-A4DUFDO"],
                    # "kurok": ["LAPTOP-HT09R44B"],
                    "orlando": ["DESKTOP-LS2J6OB"],
                    "Orlando": ["microxx-lap"],
                    "Chavador": ["DESKTOP-2KVF9HG"],
                    "Hiram": ["DESKTOP-0CCTK5C"],
                }

            # get curent pc user
            user = getpass.getuser()
            # get curent pc host
            host_name = socket.gethostname()

            # TODO: instead of hardcoding the user names and hosts, ideallly we
            # should be able to use the overrides implementation at user level.
            proceed = validate_or_not(user, host_name, allowed_users_dict)

            # License validation override
            license_validation = False
            if valueoverrides:
                lic_validation_value_code = "mty.engine.harmony.license_validation"
                data = valueoverrides.get_values(lic_validation_value_code)
                license_validation = data.get(lic_validation_value_code)

            engine.logger.debug("proceed: {}".format(proceed))
            engine.logger.debug("license_validation: {}".format(license_validation))

            if proceed:
                # engine.log_info("you have skipped the validation")
                proceed_message = "You can work now!"
                engine.show_message(proceed_message)

            else:
                if license_validation:
                    PLATFORM = sys.platform.upper()
                    software_path = os.environ.get("CURRENT_SOFTWARE_PATH")

                    msg = "Software path: {}".format(software_path)
                    self.logger.debug(msg)

                    if PLATFORM == "WIN32":
                        client_activation = os.path.join(
                            os.path.dirname(software_path), "ClientActivation.exe"
                        )
                        validate_license(client_activation, True)
                    elif PLATFORM == "LINUX":
                        error = (
                            "License client activation binary for "
                            "{} not determined".format(PLATFORM)
                        )
                        self.logger.error(error)
                        engine.app.show_message(error)
                        raise NotImplementedError(error)  # TODO: find binary for Linux
                    elif PLATFORM == "DARWIN":
                        if software_path:
                            # 1. Go up n_times dirs
                            n_times = 5
                            counter = 0
                            parent_dir = software_path

                            while counter < n_times:
                                parent_dir = os.path.dirname(parent_dir)
                                counter += 1

                            toon_boom_directory = parent_dir
                        else:
                            # 1. Search for main container dir for Harmony
                            toon_boom_directory = find_main_container_dir_harmony()

                        # 2. Now search for the ClientActivation dir
                        license_dir = find_toon_boom_license_directory_mac_os(
                            toon_boom_directory
                        )

                        if not license_dir:
                            toon_boom_directory = find_main_container_dir_harmony()
                            license_dir = find_toon_boom_license_directory_mac_os(
                                toon_boom_directory
                            )

                            if not license_dir:
                                error = (
                                    "Unable to find License Harmony dir for {}".format(
                                        PLATFORM
                                    )
                                )
                                self.logger.error(error)
                                engine.app.show_message(error)
                                raise Exception(error)

                        self.logger.info(
                            "Found License Harmony dir: {}".format(license_dir)
                        )

                        client_activation = os.path.join(
                            license_dir, "ClientActivation"
                        )

                        self.logger.debug(
                            "ClientActivation: {}".format(client_activation)
                        )

                        if not os.path.exists(client_activation):
                            client_activation = os.path.join(
                                license_dir, "LicenseWizard"
                            )

                            self.logger.debug(
                                "LicenseWizard: {}".format(client_activation)
                            )

                            if not os.path.exists(client_activation):
                                error = (
                                    "Unable to find License Harmony bin for {}".format(
                                        PLATFORM
                                    )
                                )
                                self.logger.error(error)
                                engine.app.show_message(error)
                                raise Exception(error)

                        self.logger.info(
                            "Found License Harmony bin: {}".format(client_activation)
                        )

                        validate_license(client_activation, False)

        if engine.name == "tk-maya":
            import maya.cmds as cmds

            # ------------------------------------------------------------------
            # Disable Exocortex loading
            # plugin_path = os.environ.get('MAYA_EXOCORTEX_PLUGIN', None)
            # current_plugins = cmds.pluginInfo(query=True, listPlugins=True)

            # if 'MayaExocortexCreate' not in current_plugins and plugin_path:
            #     # paths in MAYA_PLUG_IN_PATH are stored with fordwars slashes
            #     plugin_dir = os.path.dirname(plugin_path).replace(os.path.sep, '/')
            #     MAYA_PLUG_IN_PATH = os.environ.get('MAYA_PLUG_IN_PATH', '')
            #     if plugin_dir in MAYA_PLUG_IN_PATH:
            #         cmds.loadPlugin(plugin_path)
            # ------------------------------------------------------------------

            def convert_fps_to_maya_format(fps):
                fps_formats = {
                    "10fps": 10,
                    "12fps": 12,
                    "game": 15,
                    "23.976fps": 23.976,
                    "film": 24,
                    "pal": 25,
                    "29.97fps": 29.97,
                    "ntsc": 30,
                    "palf": 50,
                    "59.94fps": 59.94,
                    "ntscf": 60,
                    10: "10fps",
                    12: "12fps",
                    15: "game",
                    23.976: "23.976fps",
                    24: "film",
                    25: "pal",
                    29.97: "29.97fps",
                    30: "ntsc",
                    50: "palf",
                    59.94: "59.94fps",
                    60: "ntscf",
                }

                return fps_formats.get(fps)

            # first save the default frame range for later use
            start_frame = cmds.playbackOptions(query=True, min=True)
            end_frame = cmds.playbackOptions(query=True, max=True)

            # Get fps from SG
            project_data = self.get_project_data_from_SG(engine)
            project_fps = project_data.get("sg_fps", 24)

            # Convert SG pfs to Maya fps format
            converted_project_fps = convert_fps_to_maya_format(project_fps)

            engine.logger.info(
                "Attempting to set playback fps to '{}'".format(converted_project_fps)
            )
            new_time_unit = cmds.currentUnit(time=converted_project_fps)
            engine.logger.info("new_time_unit: {}".format(new_time_unit))
            # engine.log_info("Attempt done, but still not sure if it worked")

            # after setting the project frame rate, we need to set the frame range
            # again to ensure it is not fractional
            cmds.playbackOptions(edit=True, minTime=start_frame, maxTime=end_frame)
            cmds.playbackOptions(
                edit=True, animationStartTime=start_frame, animationEndTime=end_frame
            )
            cmds.currentTime(start_frame)

            engine.logger.info("Attempting to set units to 'cm'")
            new_linear_unit = cmds.currentUnit(linear="cm")
            engine.logger.info("new_linear_unit: {}".format(new_linear_unit))
            # engine.log_info("Attempt done, but still not sure if it worked")

            # Enable color mamagement
            cmds.colorManagementPrefs(
                edit=True,
                cmEnabled=True,
                colorManagePots=False,
                configFilePath="",
                ocioRulesEnabled=False,
                outputTransformEnabled=True,
                policyFileName="",
            )

            # set the correct color spaces for working and view solor transforms
            project_working_color_space = project_data.get(
                "sg_working_color_space", "sRGB"
            )
            project_output_color_space = project_data.get(
                "sg_output_color_space", "sRGB"
            )
            if project_working_color_space == "aces_cg":
                project_working_color_space = "ACES - ACEScg"
            if project_output_color_space == "rec709":
                project_output_color_space = "Rec.709"
            try:
                # set the display colot transform
                cmds.setAttr(
                    "defaultColorMgtGlobals.workingSpaceName",
                    project_working_color_space,
                    type="string"
                )
                # set the view colot transform
                cmds.setAttr(
                    "defaultColorMgtGlobals.outputTransformName",
                    project_output_color_space,
                    type="string"
                )
                # set the view colot transform
                cmds.setAttr(
                    "defaultColorMgtGlobals.viewTransformName",
                    project_output_color_space,
                    type="string"
                )
            except Exception as e:
                msg = (
                    "Unable to set working and output color space: {}. "
                    "Full traceback:\n{}"
                ).format(e, traceback.format_exc())
                engine.logger.error(msg)

            # Set default color management options
            cmds.optionVar(intValue=("colorManagementEnabledByDefault", 1))

            # Set the default image file prefix in render settings
            file_prefix = "<scene>/<renderLayer>/<aov>/<renderLayer>"
            cmds.setAttr(
                "defaultRenderGlobals.imageFilePrefix", file_prefix, type="string"
            )

            # set the default image file format
            # common image formats:
            # 32: PNG
            # 8: JPEG
            # 4: Maya IFF
            # 7: TIF
            # 51: EXR
            # Set the default image format to EXR (value = 51)
            cmds.setAttr("defaultRenderGlobals.imageFormat", 51)

    def fix_path(self, path):
        """Replace all backward slashes with forward slashes."""

        path = path.replace("\\\\", "/")
        path = path.replace("\\", "/")

        return path

    def get_project_data_from_SG(self, engine):
        # engine = sgtk.platform.current_engine()
        project_data = engine.shotgun.find_one(
            entity_type="Project",
            filters=[["id", "is", engine.context.project.get("id")]],
            fields=["sg_fps", "sg_working_color_space", "sg_output_color_space"],
        )
        # project_fps = data.get("sg_fps")

        return project_data

    def get_rv_path(self):
        """check if rv is installed and if the path can be found. Important: IT MUST BE
        INSTALLED IN THE DEFAULT LOCATION.
        returns the path to the folder where the rv executable is located or None.
        """

        # find rv path per platform

        # Windows ----------------------------------------------------------------------
        if sys.platform == "win32":
            pattern = re.compile(
                (
                    r"(?P<head>.+?(?:Autodesk|[Ss]hot[Gg](?:rid|un)))[\\/]"
                    r"(?P<tail>(?P<rv>[Rr][Vv])-(?P<version>\d{4}\.\d+\.\d+)?"
                    r"[\\/]bin)"
                )
            )

            # Specify the root directory to start the search
            root_directories = [
                r"C:\Program Files\Autodesk",
                r"C:\Program Files\Shotgrid",
                r"C:\Program Files\Shotgun",
            ]

            for root_directory in root_directories:
                if os.path.exists(root_directory):
                    # Search for the matching path
                    rv_path = self.search_matching_path(pattern, root_directory)

                    if rv_path:
                        self.logger.info("Matching RV path found: {}".format(rv_path))
                        return rv_path, "rv.exe"
                    else:
                        self.logger.info(
                            "No matching path found in root folder: {}".format(
                                root_directory
                            )
                        )
            return None, None

        # Linux ------------------------------------------------------------------------
        elif sys.platform == "linux":
            pattern = re.compile(
                (
                    r"(?P<head>.+?(?:Autodesk|[Ss]hot[Gg](?:rid|un)))[\\/]"
                    r"(?P<tail>(?P<rv>[Rr][Vv])-(?P<version>\d{4}\.\d+\.\d+)?"
                    r"[\\/]bin)"
                )
            )

            # Specify the root directory to start the search
            root_directories = [
                r"/opt/Autodesk",
                r"/opt/ShotGrid",
                r"/opt/Shotgun",
            ]

            for root_directory in root_directories:
                if os.path.exists(root_directory):
                    # Search for the matching path
                    rv_path = self.search_matching_path(pattern, root_directory)

                    if rv_path:
                        self.logger.info("Matching RV path found: {}".format(rv_path))
                        return rv_path, "rv"
                    else:
                        self.logger.info(
                            "No matching path found in root folder: {}".format(
                                root_directory
                            )
                        )
            return None, None

        # Mac --------------------------------------------------------------------------
        elif sys.platform == "darwin":
            """
            pattern = re.compile(
                (
                    r"(?P<path>.+[aA]pplications/[rvRV]+\.app/[cC]ontents/[mM]ac[osOS]+)"
                )
            )

            # Specify the root directory to start the search
            root_directories = [
                r"/Applications/RV.app/Contents/MacOS",
            ]

            for root_directory in root_directories:
                self.logger.info(">>> root_directory: {}".format(root_directory))
                if os.path.exists(root_directory):
                    self.logger.info(">>> ENTRO")

                    # Search for the matching path
                    rv_path = self.search_matching_path(pattern, root_directory)
                    self.logger.info(">>> rv_path: {}".format(rv_path))

                    if rv_path:
                        self.logger.info("Matching RV path found: {}".format(rv_path))
                        return rv_path, "RV"
                    else:
                        self.logger.info(
                            "No matching path found in root folder: {}".format(
                                root_directory
                            )
                        )
            return None, None
            """

            RV_bin_filename = "RV"

            # Define the regular expression pattern to match the RV binary file
            pattern = re.compile("{}$".format(RV_bin_filename))

            # Specify the root directory to start the search
            root_directory = "/Applications/RV.app/Contents/MacOS"

            for dirpath, dirnames, filenames in os.walk(root_directory):
                for filename in filenames:
                    if pattern.search(filename):
                        rv_directory = dirpath
                        return rv_directory, RV_bin_filename

            return None, None

        # Any other OS -----------------------------------------------------------------
        else:
            return None, None

    def get_sg_desktop_path(self):
        """Gets the SG Desktop app path. Important: IT MUST BE INSTALLED IN THE DEFAULT
        LOCATION.
        return:
            sg_desktop_path (str): the path to the folder where the sg desktop executable
                                   is located or None.
            sg_desktop_executable (str): the name of the sg desktop executable or None.
        """

        # find sg desktop path per platform

        sg_desktop_path = None
        sg_desktop_executable = None

        # Windows ----------------------------------------------------------------------
        if sys.platform == "win32":
            pattern = re.compile(
                (
                    r"(?P<head>.+?(?:[Ss]hot[Gg](?:rid|un)))[\\/]"
                    r"(?P<executable>[Ss]hotgun(?:\.exe)?)"
                )
            )

            # Specify the root directory to start the search
            root_directories = [
                r"C:\Program Files\Shotgun",
                r"C:\Program Files\Shotgrid",
            ]

            for root_directory in root_directories:
                if os.path.exists(root_directory):
                    # Search for the matching path
                    for file_ in os.listdir(root_directory):
                        full_path = os.path.join(root_directory, file_)
                        if os.path.isfile(full_path):
                            match = re.match(pattern, full_path)
                            if match:
                                sg_desktop_path = match.groupdict().get("head")
                                sg_desktop_executable = match.groupdict().get("executable")
                                break  # Exit loop once a match is found

                if sg_desktop_path:
                    self.logger.info(
                        "Matching SG Desktop path found: {}".format(sg_desktop_path)
                    )
                    return sg_desktop_path, sg_desktop_executable
                else:
                    self.logger.info(
                        "No matching path found in root folder: {}".format(
                            root_directory
                        )
                    )
            return None, None

        # Linux ------------------------------------------------------------------------
        elif sys.platform == "linux":
            pattern = re.compile(
                (
                    r"(?P<head>.+?(?:Autodesk|[Ss]hot[Gg](?:rid|un)))[\\/]"
                    r"(?P<tail>(?P<rv>[Rr][Vv])-(?P<version>\d{4}\.\d+\.\d+)?"
                    r"[\\/]bin)"
                )
            )

            # Specify the root directory to start the search
            root_directories = [
                r"/opt/Autodesk",
                r"/opt/ShotGrid",
                r"/opt/Shotgun",
            ]

            for root_directory in root_directories:
                if os.path.exists(root_directory):
                    # Search for the matching path
                    rv_path = self.search_matching_path(pattern, root_directory)

                    if rv_path:
                        self.logger.info("Matching RV path found: {}".format(rv_path))
                        return rv_path, "rv"
                    else:
                        self.logger.info(
                            "No matching path found in root folder: {}".format(
                                root_directory
                            )
                        )
            return None, None

        # Mac --------------------------------------------------------------------------
        elif sys.platform == "darwin":
            """
            pattern = re.compile(
                (
                    r"(?P<path>.+[aA]pplications/[rvRV]+\.app/[cC]ontents/[mM]ac[osOS]+)"
                )
            )

            # Specify the root directory to start the search
            root_directories = [
                r"/Applications/RV.app/Contents/MacOS",
            ]

            for root_directory in root_directories:
                self.logger.info(">>> root_directory: {}".format(root_directory))
                if os.path.exists(root_directory):
                    self.logger.info(">>> ENTRO")

                    # Search for the matching path
                    rv_path = self.search_matching_path(pattern, root_directory)
                    self.logger.info(">>> rv_path: {}".format(rv_path))

                    if rv_path:
                        self.logger.info("Matching RV path found: {}".format(rv_path))
                        return rv_path, "RV"
                    else:
                        self.logger.info(
                            "No matching path found in root folder: {}".format(
                                root_directory
                            )
                        )
            return None, None
            """

            RV_bin_filename = "RV"

            # Define the regular expression pattern to match the RV binary file
            pattern = re.compile("{}$".format(RV_bin_filename))

            # Specify the root directory to start the search
            root_directory = "/Applications/RV.app/Contents/MacOS"

            for dirpath, dirnames, filenames in os.walk(root_directory):
                for filename in filenames:
                    if pattern.search(filename):
                        rv_directory = dirpath
                        return rv_directory, RV_bin_filename

            return None, None

        # Any other OS -----------------------------------------------------------------
        else:
            return None, None

    def search_matching_path(self, pattern, directory):
        for name in os.listdir(directory):
            item_path = os.path.join(directory, name)
            # Check if the item is a directory
            if os.path.isdir(item_path):
                # Check if the item matches the pattern
                match = pattern.match(item_path)
                if match:
                    matching_path = item_path
                    self.logger.info("matching_path: {}".format(matching_path))
                    self.logger.info(
                        "path exists: {}".format(os.path.exists(matching_path))
                    )

                    # Check if the matching path exists on disk, it should but just
                    # to confirm
                    if os.path.exists(matching_path):
                        return matching_path

                # Recursively search for matching paths in the subdirectory if we haven't
                # found a match yet
                matching_path = self.search_matching_path(pattern, item_path)
                # Check if the matching path exists on disk, it should but just to confirm
                if matching_path:
                    if os.path.exists(matching_path):
                        return matching_path

        return None

    def show_message(self, title, msg, type="info"):
        """
        Shows a message box to the user
        """

        # from sgtk.platform.qt import QtCore, QtGui

        # app = QtGui.QApplication.instance()
        # msg_error = QtGui.QMessageBox()
        # msg_error.setWindowTitle("Wrong {} version".format(software))
        # msg_error.setText(msg)
        # msg_error.setIcon(QtGui.QMessageBox.Critical)
        # # msg_error.exec_()
        # msg_error.show()

        # return msg_error

        # temporary workaround using tkinter
        import tkinter as tk
        from tkinter import messagebox

        # Create the root window and hide it immediately
        root = tk.Tk()
        root.withdraw()  # Hide the main window

        # Bring the root window to the front
        root.lift()

        if type == "info":
            # Show the message box
            messagebox.showinfo(
                title=title,
                message=msg
            )

        elif type == "warning":
            # Show the message box
            messagebox.showwarning(
                title=title,
                message=msg
            )

        elif type == "error":
            # Show the message box
            messagebox.showerror(
                title=title,
                message=msg
            )

        # Ensure the hidden main window is destroyed after the message box is closed
        root.destroy()


class Notifications:
    """Class to notify the user. Initially it supports sound notifications only but
    additional notifications can be added to the {config}/notifications.py hook,
    either more sounds or maybe system notifications."""

    def __init__(self, engine):
        self.engine = engine

    def success_sound(self):
        """Play success sound"""

        self.engine.execute_hook_expression(
            "{config}/notifications.py",
            "success_sound",
        )

    def error_sound(self):
        """Play error sound"""

        self.engine.execute_hook_expression(
            "{config}/notifications.py",
            "error_sound",
        )

    def finalized_sound(self):
        """Play download/finalized sound"""

        self.engine.execute_hook_expression(
            "{config}/notifications.py",
            "download_sound",
        )

    def confirm_sound(self):
        """Play confirm sound"""

        self.engine.execute_hook_expression(
            "{config}/notifications.py",
            "confirm_sound",
        )

    def dual_confirm_sound(self):
        """Play dual_confirm sound"""

        self.engine.execute_hook_expression(
            "{config}/notifications.py",
            "dual_confirm_sound",
        )
