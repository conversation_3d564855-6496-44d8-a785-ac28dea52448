"""
An app that finds image sequences in the frames folder that don't correspond to
any enabled write node in the scene, and allows the user to select and delete
unwanted sequences.
"""

from tank import Hook
from tank.platform.qt import QtCore, QtGui
import os
import sys
import fileseq

class ProcessItemsHook(Hook):
    def execute(self, **kwargs):
        self.parent.engine.logger.info(
            "execute action cleanup_frames_folder start. {}".format("-" * 80)
        )

        # Get all enabled write nodes output paths
        scene_write_nodes_paths = self.get_write_nodes_output_paths()

        if not scene_write_nodes_paths:
            msg = "No write nodes found in the scene."
            QtGui.QMessageBox.warning(None, "Warning", msg)
            return {"succes": [], "messages": [msg], "errors": [1]}

        # Get scene root path
        scene_root = self.parent.engine.app.custom_script("scene.currentProjectPath();")
        frames_location = os.path.join(scene_root, 'frames')

        if not os.path.exists(frames_location):
            msg = f"Frames folder not found: {frames_location}"
            QtGui.QMessageBox.warning(None, "Warning", msg)
            return {"succes": [], "messages": [msg], "errors": [1]}

        # Find all sequences in frames folder
        all_sequences = self.find_sequences_in_frames_folder(frames_location)
        self.parent.engine.logger.info(f"Found {len(all_sequences)} sequences in frames folder")
        for seq in all_sequences:
            self.parent.engine.logger.info(f"Sequence: {seq}, dirname: {seq.dirname()}, basename: {seq.basename()}")

        # Log write node paths
        self.parent.engine.logger.info(f"Write node paths: {scene_write_nodes_paths}")

        # Find sequences that don't correspond to write nodes and classify all sequences
        sequences_info = self.classify_sequences(all_sequences, scene_write_nodes_paths, scene_root)
        orphan_sequences = sequences_info['orphan_sequences']

        self.parent.engine.logger.info(f"Found {len(orphan_sequences)} orphan sequences")
        for seq in orphan_sequences:
            self.parent.engine.logger.info(f"Orphan sequence: {seq}, dirname: {seq.dirname()}, basename: {seq.basename()}")

        # Log a message if no orphan sequences are detected
        if not orphan_sequences:
            self.parent.engine.logger.warning("No orphan sequences detected, but dialog will be shown")

        # Log a message if orphan sequences are detected
        else:
            self.parent.engine.logger.info(f"Detected {len(orphan_sequences)} orphan sequences:")
            for seq in orphan_sequences:
                seq_basename = os.path.basename(seq.basename())
                self.parent.engine.logger.info(f"  - {seq_basename} ({seq})")

        # Show selection dialog with all sequences
        selected_sequences = self.show_sequence_selection_dialog(all_sequences, orphan_sequences, scene_write_nodes_paths)

        if selected_sequences:
            self.delete_selected_sequences(selected_sequences)
            msg = f"Successfully deleted {len(selected_sequences)} sequence(s)"
            return {"succes": [1], "messages": [msg], "errors": []}

        return {"succes": [1], "messages": [], "errors": []}

    def get_write_nodes_output_paths(self):
        """Get all output paths of the enabled write nodes"""
        output_paths = self.parent.engine.app.custom_script(
            """
            include("harmony_utility_functions.js");
            var enabled_write_nodes = get_enabled_nodes_by_type("WRITE");
            get_write_nodes_output_paths(enabled_write_nodes);
            """
        )
        self.parent.engine.logger.info(f"Write node output paths: {output_paths}")

        # Convert paths to a consistent format
        normalized_paths = []
        for path in output_paths:
            # Normalize path and convert to forward slashes
            norm_path = os.path.normpath(path).replace('\\', '/')
            normalized_paths.append(norm_path)
            self.parent.engine.logger.info(f"Normalized write node path: {norm_path}")

        return normalized_paths

    def find_sequences_in_frames_folder(self, frames_path):
        """Find all sequences in the frames folder and subdirectories"""
        sequences = []
        try:
            # Find sequences directly in the frames folder
            root_sequences = fileseq.findSequencesOnDisk(frames_path)
            if root_sequences:
                sequences.extend(root_sequences)

            # Also look for sequences in subdirectories
            for item in os.listdir(frames_path):
                subdir_path = os.path.join(frames_path, item)
                if os.path.isdir(subdir_path):
                    try:
                        subdir_sequences = fileseq.findSequencesOnDisk(subdir_path)
                        if subdir_sequences:
                            sequences.extend(subdir_sequences)
                    except Exception as e:
                        self.parent.engine.logger.error(f"Error finding sequences in subdirectory {subdir_path}: {str(e)}")
        except Exception as e:
            self.parent.engine.logger.error(f"Error finding sequences: {str(e)}")

        return sequences

    def classify_sequences(self, all_sequences, write_node_paths, scene_root):
        """Classify sequences as orphan (no write node) or non-orphan (has write node)"""
        orphan_sequences = []
        non_orphan_sequences = []

        # Get the root frames folder path
        frames_dir = os.path.join(scene_root, 'frames')

        # Convert write node paths to sequence names
        write_node_sequence_names = []
        for write_path in write_node_paths:
            parts = write_path.split('/')
            if parts:
                base_name = parts[-1].split('.')[0]
                base_name = base_name.split('#')[0].strip('.')
                write_node_sequence_names.append(base_name)

        self.parent.engine.logger.debug(f"Write node sequence names: {write_node_sequence_names}")

        # Classify each sequence
        for seq in all_sequences:
            seq_basename = os.path.basename(seq.basename())
            seq_basename_clean = seq_basename.split('.')[0]
            seq_basename_clean = seq_basename_clean.split('#')[0].strip('.')

            # Special handling ONLY for sequences named exactly "final" (lowercase)
            if seq_basename_clean == "final":  # Exact match, case sensitive
                # If it's not in the root frames folder, it's automatically an orphan
                if os.path.normpath(seq.dirname()) != os.path.normpath(frames_dir):
                    self.parent.engine.logger.debug(
                        f"Found 'final' sequence in non-root directory: {seq.dirname()}. "
                        "Marking as orphan since 'final' should only exist in root."
                    )
                    orphan_sequences.append(seq)
                    continue

            # Normal processing for all other sequences (including "Final", "FINAL", etc)
            has_write_node = False
            for write_node_name in write_node_sequence_names:
                if seq_basename_clean == write_node_name:
                    self.parent.engine.logger.debug(f"Match found! {seq_basename_clean} = {write_node_name}")
                    has_write_node = True
                    break

            if has_write_node:
                non_orphan_sequences.append(seq)
                self.parent.engine.logger.debug(f"Sequence {seq_basename_clean} classified as non-orphan")
            else:
                orphan_sequences.append(seq)
                self.parent.engine.logger.debug(f"Sequence {seq_basename_clean} classified as orphan")

        return {
            'orphan_sequences': orphan_sequences,
            'non_orphan_sequences': non_orphan_sequences
        }

    def show_sequence_selection_dialog(self, all_sequences, orphan_sequences, write_node_paths):
        """Show dialog for user to select sequences to delete"""
        dialog = QtGui.QDialog()
        dialog.setWindowTitle("Select Sequences to Delete")
        dialog.setMinimumWidth(500)
        layout = QtGui.QVBoxLayout(dialog)

        if not all_sequences:
            # Show message when no sequences are found
            label = QtGui.QLabel(
                "No sequences were found in the frames folder.\n"
                "The frames folder will be opened for you to verify its contents."
            )
            label.setStyleSheet("color: #666666; padding: 20px;")
            label.setAlignment(QtCore.Qt.AlignCenter)
            layout.addWidget(label)
        else:
            # Add explanation label
            label = QtGui.QLabel(
                "The following sequences were found in the frames folder.\n"
                "Sequences without a corresponding write node are pre-selected.\n"
                "Sequences with write nodes are not pre-selected.\n"
                "Copied sequences are marked with 🔄 and highlighted in pink.\n"
                "All sequences can be selected or deselected manually.\n"
                "Select the ones you want to delete:"
            )
            layout.addWidget(label)

            # Create a list widget to show all sequences
            list_widget = QtGui.QListWidget()
            list_widget.setMinimumHeight(300)

            # Get frames directory path
            frames_dir = os.path.join(self.parent.engine.app.custom_script("scene.currentProjectPath();"), 'frames')

            # Add all sequences to the list
            for seq in all_sequences:
                # Extract the base name of the sequence
                display_name = os.path.basename(seq.basename())

                # Get the frame range
                try:
                    frame_set = seq.frameSet()
                    if frame_set is not None:
                        frames = sorted([abs(f) for f in frame_set])
                        if frames:
                            frame_range = f"({frames[0]}-{frames[-1]})"
                        else:
                            frame_range = "(no frames)"
                    else:
                        frame_range = "(invalid sequence)"
                except Exception as e:
                    self.parent.engine.logger.error(f"Error getting frame range for sequence {seq}: {str(e)}")
                    frame_range = "(error)"

                # Check if this is a copied sequence
                copy_patterns = [' - Copy', '(1)', '(2)', ' copy', ' Copy']
                is_copy = any(pattern in display_name for pattern in copy_patterns)

                # Create display text (without redundant tags)
                if is_copy:
                    item = QtGui.QListWidgetItem(f"🔄 {display_name} {frame_range}")
                    # Set background color to light red for copy sequences
                    item.setBackground(QtGui.QColor(255, 220, 220))
                else:
                    item = QtGui.QListWidgetItem(f"{display_name} {frame_range}")

                item.setFlags(item.flags() | QtCore.Qt.ItemIsUserCheckable)

                # Check if this sequence is in the orphan_sequences list
                is_orphan = seq in orphan_sequences

                # Pre-select if it's either a copy OR an orphan sequence
                should_check = is_copy or is_orphan

                # Debug logging
                self.parent.engine.logger.debug(f"""
                    Sequence: {display_name}
                    Is Copy: {is_copy}
                    Is Orphan: {is_orphan}
                    Has Write Node: {not is_orphan}
                    Should Check: {should_check}
                """)

                item.setCheckState(QtCore.Qt.Checked if should_check else QtCore.Qt.Unchecked)

                # Save the sequence as data for the item
                item.setData(QtCore.Qt.UserRole, seq)
                list_widget.addItem(item)

            layout.addWidget(list_widget)

        # Add buttons
        button_layout = QtGui.QHBoxLayout()

        if all_sequences:
            # Only show Select/Deselect buttons if there are sequences
            select_all_button = QtGui.QPushButton("Select All")
            deselect_all_button = QtGui.QPushButton("Deselect All")
            select_all_button.clicked.connect(lambda: self.toggle_all_selections(list_widget, True, write_node_paths))
            deselect_all_button.clicked.connect(lambda: self.toggle_all_selections(list_widget, False, write_node_paths))
            button_layout.addWidget(select_all_button)
            button_layout.addWidget(deselect_all_button)

        # Add Open Frames Folder button
        frames_dir = os.path.join(self.parent.engine.app.custom_script("scene.currentProjectPath();"), 'frames')
        open_frames_button = QtGui.QPushButton("Open Frames Folder")
        open_frames_button.clicked.connect(lambda: self.open_location(frames_dir))
        button_layout.addWidget(open_frames_button)

        # Push the buttons to the left
        spacer = QtGui.QSpacerItem(40, 20, QtGui.QSizePolicy.Expanding, QtGui.QSizePolicy.Minimum)
        button_layout.addItem(spacer)

        if all_sequences:
            # Only show Delete/Cancel buttons if there are sequences
            button_box = QtGui.QDialogButtonBox(QtGui.QDialogButtonBox.Cancel)
            delete_button = QtGui.QPushButton("Delete Selected")
            delete_button.setStyleSheet("font-weight: bold;")
            delete_button.clicked.connect(dialog.accept)
            button_box.addButton(delete_button, QtGui.QDialogButtonBox.AcceptRole)
            button_box.rejected.connect(dialog.reject)
            button_layout.addWidget(button_box)
        else:
            # Just show Close button if no sequences
            close_button = QtGui.QPushButton("Close")
            close_button.clicked.connect(dialog.reject)
            button_layout.addWidget(close_button)

        layout.addLayout(button_layout)

        if dialog.exec_() == QtGui.QDialog.Accepted and all_sequences:
            selected = []
            for i in range(list_widget.count()):
                item = list_widget.item(i)
                if item.checkState() == QtCore.Qt.Checked:
                    selected.append(item.data(QtCore.Qt.UserRole))
            return selected
        return []

    def delete_selected_sequences(self, sequences):
        """Delete the selected sequences and empty parent folders"""
        for seq in sequences:
            try:
                self.parent.engine.logger.info(f"Deleting sequence: {seq}")
                deleted_files = 0

                # Get the sequence path and parent directory
                seq_str = str(seq)
                parent_dir = os.path.dirname(seq_str)

                self.parent.engine.logger.info(f"Processing sequence: {seq_str}")
                self.parent.engine.logger.info(f"Parent directory: {parent_dir}")

                # Use fileseq to get all files in the sequence
                try:
                    # Iterate through the sequence and delete each file
                    for frame_path in seq:
                        try:
                            if os.path.exists(frame_path):
                                self.parent.engine.logger.info(f"Removing file: {frame_path}")
                                os.remove(frame_path)
                                deleted_files += 1
                        except Exception as e:
                            self.parent.engine.logger.error(f"Error deleting file {frame_path}: {str(e)}")

                    # If we deleted files, try to remove the parent directory if it's empty
                    if deleted_files > 0 and parent_dir and os.path.exists(parent_dir):
                        self.remove_empty_directory(parent_dir)

                    self.parent.engine.logger.info(f"Deleted {deleted_files} files from sequence {seq}")

                except Exception as e:
                    self.parent.engine.logger.error(f"Error deleting sequence {seq}: {str(e)}")

            except Exception as e:
                self.parent.engine.logger.error(f"Error processing sequence {seq}: {str(e)}")

    def remove_empty_directory(self, directory):
        """Remove a directory if it's empty"""
        try:
            # Check if the directory is empty
            if os.path.exists(directory) and os.path.isdir(directory):
                if not os.listdir(directory):
                    # Don't delete the main frames folder
                    frames_dir = os.path.join(self.parent.engine.app.custom_script("scene.currentProjectPath();"), 'frames')
                    if directory != frames_dir:
                        self.parent.engine.logger.info(f"Removing empty directory: {directory}")
                        os.rmdir(directory)

                        # Also try to remove parent directories if they become empty
                        parent_dir = os.path.dirname(directory)
                        if parent_dir and os.path.exists(parent_dir) and parent_dir != frames_dir:
                            self.remove_empty_directory(parent_dir)
        except Exception as e:
            self.parent.engine.logger.error(f"Error removing directory {directory}: {str(e)}")

    def toggle_all_selections(self, list_widget, select_all, write_node_paths):
        """Toggle all checkboxes in the list to either selected or deselected"""
        # Get frames directory path
        frames_dir = os.path.join(self.parent.engine.app.custom_script("scene.currentProjectPath();"), 'frames')

        if not select_all:
            # If deselecting all, simply uncheck everything
            for i in range(list_widget.count()):
                list_widget.item(i).setCheckState(QtCore.Qt.Unchecked)
            return

        # If selecting all, apply our selection rules
        for i in range(list_widget.count()):
            item = list_widget.item(i)
            seq = item.data(QtCore.Qt.UserRole)
            seq_basename = os.path.basename(seq.basename())

            # Check if this is a copied sequence
            copy_patterns = [' - Copy', '(1)', '(2)', ' copy', ' Copy']
            is_copy = any(pattern in seq_basename for pattern in copy_patterns)

            # If it's a copy, select it immediately
            if is_copy:
                item.setCheckState(QtCore.Qt.Checked)
                continue

            # Get clean name for write node comparison
            seq_basename_clean = seq_basename.split('.')[0]
            seq_basename_clean = seq_basename_clean.split('#')[0].strip('.')

            # Special handling for exact "final" sequences
            if seq_basename_clean == "final":
                # Select only if not in root frames folder
                should_select = os.path.normpath(seq.dirname()) != os.path.normpath(frames_dir)
                item.setCheckState(QtCore.Qt.Checked if should_select else QtCore.Qt.Unchecked)
                continue

            # For all other sequences, check for write node
            has_write_node = False
            for write_path in write_node_paths:
                write_node_name = write_path.split('/')[-1].split('.')[0].split('#')[0].strip('.')
                if seq_basename_clean == write_node_name:
                    has_write_node = True
                    break

            # Select if no write node found
            item.setCheckState(QtCore.Qt.Checked if not has_write_node else QtCore.Qt.Unchecked)

    def open_location(self, location):
        """This method opens the location in the system's default application.

        Args:
            location (str): The file or directory path to be opened.
        """
        system = sys.platform
        if system == "linux":
            cmd = 'xdg-open "%s"' % location
        elif system == "darwin":
            cmd = 'open "%s"' % location
        elif system == "win32":
            # Backwards slashes for windows paths
            location = location.replace("/", "\\")
            cmd = 'cmd.exe /C start "explorer" "%s"' % location
        else:
            raise Exception("Platform '%s' is not supported." % system)

        self.parent.engine.logger.info("Open Location full command: {}".format(cmd))

        QtCore.QProcess.execute(cmd)
