########################################################################################
#
# Copyright (c) 2024 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio.
########################################################################################

"""
An app that creates a "mtyMayaBatchRender" scripted node (from a tpl) for each
enabled SubNodeAnimation found in the selected nodes. It only works for nodes created
by the pipeline tools because it uses the meta.shotgun.path attribute to find the
relevant shading publishes.

All created nodes are already initialized and ready to render (contrary to what happens
if we create the node from scratch instead of using a tpl, which requires the scene
to be saved and reopened to populate the scripted node attributes).

"""

import os
import json
import time
import pprint

import sgtk
from tank import Hook
from tank.platform.qt import QtCore, QtGui

QMessageBox = QtGui.QMessageBox


pp = pprint.pprint
pf = pprint.pformat


class ProcessItemsHook(Hook):
    def execute(self, **kwargs):

        self.parent.engine.logger.info(
            "execute action create_maya_batch_render_nodes start.".ljust(120, "-")
        )

        selected_subnode_anim_nodes = self.get_selected_subnode_anim_nodes()
        if not selected_subnode_anim_nodes:
            msg = (
                "Couldn't find any SubNodeAnimation node. Your selection must contain "
                "at least one SubNodeAnimation node."
            )
            self.parent.engine.logger.error(
                "execute action create_maya_batch_render_nodes end with errors.".ljust(120, "-")
            )
            return {"succes": [], "messages": [msg], "errors": [1]}

        object_3d_paths = self.get_3d_objects_paths(selected_subnode_anim_nodes)
        if not object_3d_paths:
            msg = (
                "Couldn't find any SubNodeAnimation node. Your selection must contain "
                "at least one SubNodeAnimation node."
            )
            self.parent.engine.logger.error(
                "execute action create_maya_batch_render_nodes end with errors.".ljust(120, "-")
            )
            return {"succes": [], "messages": [msg], "errors": [1]}

        self.parent.engine.logger.info(
            "Found object_3d_paths:\n{}".format(pf(object_3d_paths))
        )

        entities_dict = self.get_shading_publishes(object_3d_paths)
        if not entities_dict:
            msg = "Couldn't find any shading publishes."
            self.parent.engine.logger.error(
                "execute action create_maya_batch_render_nodes end with errors.".ljust(120, "-")
            )
            return {"succes": [], "messages": [msg], "errors": [1]}

        self.parent.engine.logger.info(
            "Found shading entities_dict:\n{}".format(pf(entities_dict))
        )

        # create mdl - shading publishes relations dict
        mdl_shd_publishes_dict = {}
        for mdl_pub in object_3d_paths:
            for entity in entities_dict:
                for task in entities_dict[entity]:
                    for name in entities_dict[entity][task]:
                        mdl_path = entities_dict[entity][task][name]["mdl_path"]
                        if mdl_path == mdl_pub:
                            shd_path = entities_dict[entity][task][name]["path"]
                            asset_name = entities_dict[entity][task][name].get("entity", {}).get("name", "")
                            self.parent.engine.logger.info(f"{mdl_pub = }")
                            self.parent.engine.logger.info(f"{mdl_path = }")
                            self.parent.engine.logger.info(f"{shd_path = }")
                            if not mdl_pub in mdl_shd_publishes_dict:
                                mdl_shd_publishes_dict[mdl_pub] = {}
                            if not name in mdl_shd_publishes_dict[mdl_pub]:
                                mdl_shd_publishes_dict[mdl_pub][name] = {}
                            mdl_shd_publishes_dict[mdl_pub][name]["shd_path"] = shd_path
                            mdl_shd_publishes_dict[mdl_pub][name]["asset_name"] = asset_name
                            # break
        self.parent.engine.logger.info(
            f"mdl_shd_publishes_dict:\n{pf(mdl_shd_publishes_dict)}"
        )

        created_maya_batch_render_nodes = self.create_maya_batch_render_nodes(
            selected_subnode_anim_nodes,
            mdl_shd_publishes_dict
        )

        if not created_maya_batch_render_nodes:
            msg = "Couldn't create any maya batch render nodes."
            return {"succes": [], "messages": [msg], "errors": [1]}

        msg = (
            f"Successfully created {len(created_maya_batch_render_nodes)} maya batch "
            f"render nodes:\n{pf(created_maya_batch_render_nodes)}"
        )
        self.show_message("Created nodes", msg, icon="Information")
        self.parent.engine.logger.info(
            "execute action create_maya_batch_render_nodes end.".ljust(120, "-")
        )
        return {"succes": [1], "messages": [msg], "errors": []}

    def get_selected_subnode_anim_nodes(self):
        """
        Get enabled SubNodeAnimation nodes from the selection.

        :return: list of SubNodeAnimation nodes
        """

        get_selected_subnode_anim_nodes_cmd = """
include("harmony_utility_functions.js");

var log = MessageLog.trace;

function get_selected_subnode_anim_nodes() {
    var nodes_array = selection.selectedNodes();
    log("all selected nodes:\\n" + JSON.stringify(nodes_array, null, 4));
    if (nodes_array.length == 0) {
        var msg = "Couldn't find any selected node. Your selection must contain at least one SubNodeAnimation node.";
        MessageBox.warning(msg, 1, 0, 0, "Invalid selection");
        return null;
    }
    var selected_subnode_anim_nodes = filter_nodes_by_type(nodes_array, "SubNodeAnimation");
    selected_subnode_anim_nodes = filter_enabled_nodes(selected_subnode_anim_nodes);
    if (selected_subnode_anim_nodes.length == 0) {
        var msg = "Couldn't find any SubNodeAnimation node. Your selection must contain at least one SubNodeAnimation node.";
        MessageBox.warning(msg, 1, 0, 0, "Invalid selection");
        return null;
    }
    return selected_subnode_anim_nodes;
}

get_selected_subnode_anim_nodes();

"""
        self.parent.engine.logger.debug(
            f"get_selected_subnode_anim_nodes_cmd:\n{get_selected_subnode_anim_nodes_cmd}"
        )
        selected_subnode_anim_nodes = self.parent.engine.app.execute(
            get_selected_subnode_anim_nodes_cmd
        )
        time.sleep(2)
        if selected_subnode_anim_nodes:
            self.parent.engine.logger.info(
                f"Found {len(selected_subnode_anim_nodes)}, "
                f"selected_subnode_anim_nodes:\n{pf(selected_subnode_anim_nodes)}"
            )
            return selected_subnode_anim_nodes
        else:
            return []

    def get_3d_objects_paths(self, selected_subnode_anim_nodes):
        """
        Get the 3d objects paths from the pipeline created nodes of type
        "READ" that contain the meta.shotgun.path attribute.

        It's based on the selection of enabled SubNodeAnimation nodes.

        :return: list of 3d object paths
        """

        get_3d_objects_paths_cmd = """
include("harmony_utility_functions.js");

var log = MessageLog.trace;

function get_3d_objects_paths(selected_subnode_anim_nodes) {
    var object_3d_paths = [];
    for (var i = 0; i < selected_subnode_anim_nodes.length; i++) {
        var current_node = selected_subnode_anim_nodes[i];
        log("-----------------------------------");
        log("current_node: " + current_node);

        // get source and destination nodes
        var source_node = node.srcNode(current_node, 0);

        // get 3d object path from src node attributes
        path_attr = node.getAttr(source_node, frame.current(), "meta.shotgun.path");
        var published_file_path = path_attr.textValue();
        if (published_file_path == "") {
            log("Published file path not found");
            continue;
        } else {
            object_3d_paths.push(published_file_path);
            log("published_file_path: " + published_file_path);
        }
    }
    return object_3d_paths;
}

var selected_subnode_anim_nodes = %s;
get_3d_objects_paths(selected_subnode_anim_nodes);

""" % selected_subnode_anim_nodes

        self.parent.engine.logger.debug(
            f"get_3d_objects_paths_cmd:\n{get_3d_objects_paths_cmd}"
        )
        objects_3d_paths = self.parent.engine.app.execute(
            get_3d_objects_paths_cmd
        )
        time.sleep(2)
        if objects_3d_paths:
            self.parent.engine.logger.info(
                f"Found {len(objects_3d_paths)} 3d object paths:\n{pf(objects_3d_paths)}"
            )
            return objects_3d_paths
        else:
            return []

    def get_shading_publishes(self, object_3d_paths):
        """
        Get the latest shading publishes relevant to each 3d object path (usually these
        paths are from the modeling task, but we require the shading publishes)

        return: entities dictionary with their latest publishes data ordered per task:

                {
                    entity_name: {
                        task_name: {
                            "path": path,
                            "version_number": version_number,
                            "entity": entity,
                        }
                    }
                }
        """


        context = self.parent.engine.context

        # get settings either from overrides or from the config
        # Load overrides framework -----------------------------------------------------
        valueoverrides = self.parent.engine.custom_frameworks.get(
                'mty-framework-valueoverrides'
            ) or self.load_framework('mty-framework-valueoverrides')

        if valueoverrides:
            # get shading tasks
            value_code = 'mty.execute_action.harmony.create_maya_batch_nodes.shading_tasks'
            data = valueoverrides.get_value(
                value_code, link={"type": "Task", "id": context.task["id"]}
            )

            if data:
                shading_tasks = json.loads(data)
                self.parent.engine.logger.info(
                    f"Got shading_tasks from overrides: {shading_tasks}"
                )
            else:
                shading_tasks = self.parent.get_setting("shading_tasks")
                self.parent.engine.logger.info(
                    f"Got shading_tasks from settings: {shading_tasks}"
                )

        shotgun = self.parent.engine.shotgun
        tk = sgtk.sgtk_from_path(object_3d_paths[0])

        # find the 3d objcts published files from their paths
        publishes_dict = sgtk.util.find_publish(tk, object_3d_paths)
        self.parent.engine.logger.info(
            "Found publishes in scene:\n{}".format(pf(publishes_dict))
        )

        all_published_files_ids = [publishes_dict[pub]["id"] for pub in publishes_dict]
        self.parent.engine.logger.info(
            "all_modelingpublished_files_ids:\n{}".format(pf(all_published_files_ids))
        )

        entities_dict = {}

        # get 3d objects published files. We are interested on to what entity they belong
        filters = [["id", "in", all_published_files_ids]]
        fields = ["entity", "path"]
        published_files = shotgun.find("PublishedFile", filters, fields)
        self.parent.engine.logger.info(
            f"3d object published files:\n{pf(published_files)}"
        )

        # find latest publish for the shading task for each entity found
        all_mdl_entities = [published_file["entity"] for published_file in published_files]
        self.parent.engine.logger.info(
            f"all modeling entities:\n{pf(all_mdl_entities)}"
        )
        filters = [
            ["entity", "in", all_mdl_entities],
            ["task.Task.content", "in", shading_tasks],
            ["sg_status_list", "is", "apr"],
        ]
        fields = [
            "path",
            "version_number",
            "created_at",
            "entity",
            "task.Task.content",
            "name",
        ]
        order = [
            {"field_name": "created_at", "direction": "desc"},
            {"field_name": "id", "direction": "desc"},
        ]
        task_publishes = shotgun.find("PublishedFile", filters, fields, order=order)

        self.parent.engine.logger.info(
            "Found {} shading publishes:\n{}".format(
                len(task_publishes), pf(task_publishes)
            )
        )

        # dump the queried data to a dictionary
        for published_file in task_publishes:
            self.parent.engine.logger.info("-" * 120)
            entity = published_file["entity"]
            entity_name = published_file["entity"]["name"]
            # latest_publish = task_publishes[0]
            path = published_file["path"]["local_path"]
            path = path.replace(os.sep, "/")
            version_number = published_file["version_number"]
            task_name = published_file["task.Task.content"]
            published_name = published_file["name"]
            self.parent.engine.logger.info(f"Entity: {entity_name}")
            self.parent.engine.logger.info(f"Task: {task_name}")
            self.parent.engine.logger.info(f"Version: {version_number}")
            self.parent.engine.logger.info(f"Published name: {published_name}")
            # get the original mdl path
            for pub in published_files:
                mdl_local_path = pub["path"]["local_path"]
                mdl_local_path = mdl_local_path.replace(os.sep, "/")
                mdl_entity = pub["entity"]
                if mdl_entity == entity:
                    break
            if entity_name not in entities_dict:
                entities_dict[entity_name] = {}
            if not task_name in entities_dict[entity_name]:
                entities_dict[entity_name][task_name] = {}
            if version_number > entities_dict[entity_name].get(task_name, {}).get(published_name, {}).get("version_number", 0):
                entities_dict[entity_name][task_name][published_name] = {
                    "path": path,
                    "version_number": version_number,
                    "entity": entity,
                    "mdl_path": mdl_local_path,
                    "name": published_name,
                }

        return entities_dict

    def create_maya_batch_render_nodes(
        self,
        selected_subnode_anim_nodes,
        mdl_shd_publishes_dict
    ):

        result = {"succes": [1], "messages": [], "errors": []}

        context = self.parent.engine.context

        # get settings either from overrides or from the config
        # Load overrides framework -----------------------------------------------------
        valueoverrides = self.parent.engine.custom_frameworks.get(
                'mty-framework-valueoverrides'
            ) or self.load_framework('mty-framework-valueoverrides')

        if valueoverrides:
            # get renderer
            value_code = 'mty.execute_action.harmony.create_maya_batch_nodes.renderer'
            renderer = valueoverrides.get_value(
                value_code, link={"type": "Task", "id": context.task["id"]}
            )
            if renderer:
                self.parent.engine.logger.info(
                    f"Got renderer from overrides: {renderer}"
                )
            else:
                renderer = self.parent.get_setting("renderer")
                self.parent.engine.logger.info(
                    f"Got renderer from settings: {renderer}"
                )

        # get the tpl path, which contains the initialized mtyMayaBatchRender scripted
        # node. It's located in the config, so we must resolve its path
        disk_location = self.disk_location
        self.parent.engine.logger.debug("disk_location: {}".format(disk_location))

        tpl_path = os.path.join(disk_location, "tpls", "mty_MayaBatchRender.tpl")
        tpl_path = tpl_path.replace(os.sep, "/")
        self.parent.engine.logger.info("tpl_path: {}".format(tpl_path))

        # ensure we have the relevant variables before continuing
        if not selected_subnode_anim_nodes or not mdl_shd_publishes_dict or not renderer or  not tpl_path:
            msg = (
                f"One or more required variables are missing:\n"
                f"{selected_subnode_anim_nodes = }\n{renderer = }\n{tpl_path = }\n"
                f"{mdl_shd_publishes_dict = }"
            )
            return {"succes": [], "messages": [msg], "errors": [1]}

        create_maya_batch_render_nodes_cmd = """
include("harmony_utility_functions.js");


function get_appropriate_name(str) {
    // Split the string by underscore
    var parts = str.split('_');

    // Get the first and fourth elements
    var firstElement = parts[0];
    var fourthElement = parts[3];

    // Combine the selected elements
    var combinedString = firstElement + "_" + fourthElement;

    // Remove the file extension
    var result = combinedString.split('.')[0];

    return result;
}

function create_maya_batch_render_nodes(selected_subnode_anim_nodes, mdl_shd_publishes_dict) {
    var created_nodes = [];
    for (var i = 0; i < selected_subnode_anim_nodes.length; i++) {
        var current_node = selected_subnode_anim_nodes[i];
        log("-----------------------------------");
        log("current_node: " + current_node);

        // get source and destination nodes
        var source_node = node.srcNode(current_node, 0);
        var dest_node = node.dstNode(current_node, 0, 0);

        // get 3d object path from src node attributes
        path_attr = node.getAttr(source_node, frame.current(), "meta.shotgun.path");
        var published_mdl_file_path = path_attr.textValue();
        log("published_mdl_file_path: " + published_mdl_file_path);
        if (published_mdl_file_path == "") {
            log("Mdl published file path not found");
            continue;
        }

        // get the shading scene path from the mdl_shd_publishes_dict
        if (mdl_shd_publishes_dict.hasOwnProperty(published_mdl_file_path) == false) {
            log("Shading scene not found in mdl_shd_publishes_dict for file: " + published_mdl_file_path);
            continue;
        }

        for (var name in mdl_shd_publishes_dict[published_mdl_file_path]) {

            // first we split the name to keep just the template name field instead of
            // the full published file name field
            var fixed_name = get_appropriate_name(name);
            log("fixed_name: " + fixed_name);

            var published_shd_file_path = mdl_shd_publishes_dict[published_mdl_file_path][name]["shd_path"];
            log("published_shd_file_path: " + published_shd_file_path);
            var asset_name = mdl_shd_publishes_dict[published_mdl_file_path][name]["asset_name"];
            log("asset_name: " + asset_name);

            // import the tpl file containing the mtyMayaBatchRender node already initialized,
            // this node is a scripted node and the only way to created already initialized with
            // the required attribute, is either by importing it through a tpl or by reopening
            // the scene after the node creation. We are using the first option.
            // var mty_maya_batch_render_node = import_tpl("Q:/mty-config-default/assets/harmony_tpls/mty_MayaBatchRender.tpl");
            var mty_maya_batch_render_node = import_tpl("%s");
            if (typeof mty_maya_batch_render_node == "object") {
                mty_maya_batch_render_node = mty_maya_batch_render_node[0];
            }

            // Create the new node name and rename the node
            // var new_node_name = asset_name + "_mtyMayaBatchRender";
            var new_node_name = fixed_name + "_mtyMayaBatchRender";
            var successfully_renamed = node.rename(mty_maya_batch_render_node, new_node_name);
            if (successfully_renamed == false) {
                number_token = 1;
                while (successfully_renamed == false) {
                    new_node_name = fixed_name + "_mtyMayaBatchRender_" + number_token;
                    successfully_renamed = node.rename(mty_maya_batch_render_node, new_node_name);
                    number_token += 1;
                }
            }

            // store current node node view position
            var current_node_x_pos = node.coordX(current_node);
            var current_node_y_pos = node.coordY(current_node);

            // rename the node to include the asset name
            // Split the path by '/'
            var path_components = mty_maya_batch_render_node.split('/');
            // Remove the last component
            path_components.pop();
            // Add the asset name to the path components
            path_components.push(new_node_name);

            // Join the components back together to get the new node path
            var mty_maya_batch_render_node = path_components.join('/');
            created_nodes.push(mty_maya_batch_render_node);

            // insert the mtyMayaBatchRender node into the current node's connections
            var current_node_output_nodes_num = node.numberOfOutputPorts(current_node);
            var current_node_output_links_num = node.numberOfOutputLinks(current_node, 0);

            log("========================================");
            log("current_node: " + current_node);
            log("current_node_output_nodes_num: " + current_node_output_nodes_num);
            log("current_node_output_links_num: " + current_node_output_links_num);

            for (var i = 0; i < current_node_output_nodes_num; i++) {
                for (var j = 0; j < current_node_output_links_num; j++) {
                    var dst_node = node.dstNode(current_node, i, j);
                    var dst_node_input_nodes_num = node.numberOfInputPorts(dst_node);
                    var dst_node_info = node.dstNodeInfo(current_node, i, j);

                    log("----------------------------------------");
                    log("dst_node: " + dst_node);
                    log("dst_node_input_nodes_num: " + dst_node_input_nodes_num);

                    for (var k = 0; k < dst_node_input_nodes_num; k++) {
                        var src_node_info = node.srcNodeInfo(dst_node, k, j);

                        if (src_node_info.node == current_node) {
                            log("src_node_info.node: " + src_node_info.node);

                            log("----------------");
                            log("dst_node_info: " + JSON.stringify(dst_node_info, null, 2));
                            log("----------------");
                            log("src_node_info: " + JSON.stringify(src_node_info, null, 2));

                            // diconnect the specific in port on destination node
                            node.unlink(dst_node, k);
                            // connect the newly created node to both, the current node
                            // and the destination node
                            node.link(current_node, 0, mty_maya_batch_render_node, 0);
                            node.link(mty_maya_batch_render_node, 0, dst_node, k);


                            // set node position, we set the node position up to here
                            // because we need both, the current node and the destination
                            // nodes to get the middle position

                            // store dst node node view position
                            if (node.getName(dst_node) != "") {
                                var dest_node_x_pos = node.coordX(dst_node);
                                var dest_node_y_pos = node.coordY(dst_node);
                            } else {
                                var dest_node_x_pos = current_node_x_pos;
                                var dest_node_y_pos = current_node_y_pos + 70;
                            }

                            // get the mid point between the current node and the
                            // destination node, to get the new node position
                            var nodes_x_difference = (current_node_x_pos - dest_node_x_pos) / 2;
                            var nodes_y_difference = (current_node_y_pos - dest_node_y_pos) / 2;

                            var new_node_position_x = current_node_x_pos + nodes_x_difference;
                            var new_node_position_y = current_node_y_pos + nodes_y_difference;

                            log("current_node_x_pos: " + current_node_x_pos);
                            log("dest_node_x_pos: " + dest_node_x_pos);
                            log("current_node_y_pos: " + current_node_y_pos);
                            log("dest_node_y_pos: " + dest_node_y_pos);
                            log("nodes_x_difference: " + nodes_x_difference);
                            log("nodes_y_difference: " + nodes_y_difference);

                            var new_node_position_x = current_node_x_pos + Math.round((dest_node_x_pos - current_node_x_pos) / 2);
                            var new_node_position_y = current_node_y_pos + Math.round((dest_node_y_pos - current_node_y_pos) / 2);
                            node.setCoord(mty_maya_batch_render_node, new_node_position_x, new_node_position_y);
                        }
                    }
                }
            }

            // set the attributes of the mtyMayaBatchRender node
            node.setTextAttr(mty_maya_batch_render_node, "renderer", frame.current(), "%s");
            // node.setTextAttr(mty_maya_batch_render_node, "renderer", frame.current(), "arnold");
            node.setTextAttr(mty_maya_batch_render_node, "OriginalMayaFileName", frame.current(), published_shd_file_path);

            // set node color
            var new_node_olor = new ColorRGBA(65, 230, 220, 255);
            node.setColor(mty_maya_batch_render_node, new_node_olor);
        }
    }
    return created_nodes;
}

var selected_subnode_anim_nodes = %s;
var mdl_shd_publishes_dict = %s;
log("----------------------------------");
log(JSON.stringify(selected_subnode_anim_nodes, null, 4));
log("----------------------------------");
log(JSON.stringify(mdl_shd_publishes_dict, null, 4));
create_maya_batch_render_nodes(selected_subnode_anim_nodes, mdl_shd_publishes_dict);

""" % (
    tpl_path,
    renderer,
    selected_subnode_anim_nodes,
    mdl_shd_publishes_dict,
)

        self.parent.engine.logger.debug(
            "create_maya_batch_render_nodes_cmd:\n{}".format(
                create_maya_batch_render_nodes_cmd
            )
        )
        created_maya_batch_render_nodes =self.parent.engine.app.execute(
            create_maya_batch_render_nodes_cmd
        )
        time.sleep(2)
        self.parent.engine.logger.info(f"{created_maya_batch_render_nodes = }")

        return created_maya_batch_render_nodes

    def show_message(self, title, msg, icon=None):
        """
        Shows a message box with the input message
        """

        icons_dict = {
            "NoIcon": QMessageBox.NoIcon,
            "Question": QMessageBox.Question,
            "Information": QMessageBox.Information,
            "Warning": QMessageBox.Warning,
            "Critical": QMessageBox.Critical,
        }

        if not icon or icon not in icons_dict.keys():
            icon_selection = icons_dict["NoIcon"]
        else:
            icon_selection = icons_dict[icon]

        msg_error = QMessageBox()
        msg_error.setWindowTitle(title)
        msg_error.setText(msg)
        msg_error.setIcon(icon_selection)
        msg_error.raise_()
        msg_error.activateWindow()
        msg_error.exec_()
