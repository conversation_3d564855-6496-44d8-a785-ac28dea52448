# -*- coding: utf-8 -*-
# Standard library:
import os
import json
from collections import namedtuple
import pprint
import re
# ___   ___   ___   ___   ___   ___  ___
# Third party:
import sgtk
import pymel.core as pm
import maya.mel as mel
import maya.cmds as cmds

# ___   ___   ___   ___   ___   ___  ___
# Project:
# ====================================================================
HookBaseClass = sgtk.get_hook_baseclass()
pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)
pf = pprint.pformat

PublishMetadata = namedtuple(
    'PublishMetadata',
    ['fields', 'reference', 'publish', 'path_to_publish', 'root_node']
)


class Alembics(HookBaseClass):
    def __init__(self, parent):
        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__(parent)
        self.time = self._get_shot_time()
        if not pm.pluginInfo("AbcExport", loaded=True, query=True):
            pm.loadPlugin("AbcExport")

        self.development = False

    # ---------------------------------------------------------------------------

    def publish(self, settings, item):
        list_of_publish_metadata = (
            self._list_of_publish_metadata(settings, item)
        )

        # We have to create the alembics first and then the publish
        # so that the checksum can be created based on an actual file.
        list_of_commands = self._get_abc_list_of_commands(
            list_of_publish_metadata
        )
        item.properties['list_of_commands'] = list_of_commands

        full_cmd_str = 'AbcExport' + list_of_commands
        self.parent.engine.logger.info("full_abc_cmd_str: {}".format(full_cmd_str))

        #  -    -    -    -    -    -    -    -    -    -
        # Execute command:
        if not self.development:
            mel.eval(full_cmd_str)
        #  -    -    -    -    -    -    -    -    -    -

        result = []
        for meta in list_of_publish_metadata:
            self.parent.engine.logger.info("-" * 40)
            self.parent.engine.logger.info("meta.root_node: {}".format(meta.root_node))

            self.parent.engine.logger.info('Publish metadata:\n{}'.format(pf(meta)))
            publish_name = self._publish_name(meta)
            self.parent.engine.logger.info('publish_name:\n{}'.format(publish_name))
            path_to_primary_publish = self._get_primary_publish_path(
                settings=settings, fields=meta.fields
            )


            tmp = settings['publish_type'].value
            publish_file_type = tmp.format(step_code=meta.fields['step_code'])
            self.parent.engine.logger.info(
                'publish_file_type:\n{}'.format(publish_file_type)
            )

            sg_fields_to_update = {
                "sg_status_list": "rev",
                "task.Task.sg_status_list": "rev",
            }

            result.append(
                sgtk.util.register_publish(
                    tk=self.parent.sgtk,
                    context=self.parent.context,
                    comment=item.description,
                    path=meta.path_to_publish,
                    name=publish_name,
                    version_number=meta.fields['version'],
                    thumbnail_path=item.get_thumbnail_as_path(),
                    dependency_paths=[path_to_primary_publish],
                    task=self.parent.context.task,
                    published_file_type=publish_file_type,
                    sg_fields=sg_fields_to_update,
                    dry_run=self.development
                )
            )

        return result

    #  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

    def get_model_high_root_node(self, transform_node):
        namespace = self.get_namespace(transform_node)
        result = filter(
            lambda x: '{}:model_high_root'.format(namespace) == x.name(),
            transform_node.listRelatives(allDescendents=True, type='transform')
        )

        if result:
            result = list(result)
        else:
            result = []

        if result and len(result) == 1:
            return result[0]
        elif len(result) > 1:
            ref = pm.referenceQuery(transform_node.name(), referenceNode=True)
            self.parent.logger.warning(
                "Found more than one 'model_high_root' for reference {}".format(
                    ref
                )
            )
            return None
        else:
            ref = pm.referenceQuery(transform_node.name(), referenceNode=True)
            self.parent.logger.warning(
                "Couldn't find 'model_high_root' for reference {}".format(
                    ref
                )
            )
            return None

    def get_namespace(self, node):
        result = None
        result = str(node.namespace())
        if result.endswith(":"):
            result = result[:-1]

        return result

    def _get_abc_list_of_commands(self, list_of_publish_metadata):

        list_of_commands = []

        for meta in list_of_publish_metadata:
            model_high_root_node = (
                self.get_model_high_root_node(meta.root_node)
            )
            if model_high_root_node:
                self.parent.logger.info(
                    "Found model_high_root_node: {}".format(model_high_root_node)
                )
                fixed_path = self.fix_path(meta.path_to_publish)
                command = self._abc_command(
                    prefix='mtyCache',
                    path_to_publish=fixed_path,
                    node=model_high_root_node
                )
                list_of_commands.append(command)
            else:
                self.parent.logger.warning(
                    "Couldn't find model_high_root_node for node: {}".format(
                        meta.root_node
                    )
                )

        return "".join(list_of_commands)


    def _publish_name(self, meta):
        # publish_name = self._regex_replace(
        #     regex=r'_v\d{3}',
        #     source_string=os.path.basename(meta.path_to_publish),
        #     replace_string=''
        # )
        # return publish_name

        # determine the publish name:
        publish_name = None

        version_pattern = re.compile(
            r"(?P<head>.+)"
            r"(?P<version_token>_v\d{3})"
            r"(?P<tail>.+)"
        )
        version_match = re.match(
            version_pattern, os.path.basename(meta.path_to_publish)
        )
        if version_match:
            publish_name = "{}{}".format(
                version_match.groupdict()["head"],
                version_match.groupdict()["tail"]
            )

        # fallback to use file name as publish name
        if not publish_name:
            publish_name = os.path.basename(meta.path_to_publish)

        return publish_name

    def _regex_replace(self, regex, source_string, replace_string):
        result = ''
        pattern = re.compile(regex)
        result = pattern.sub(repl=replace_string, string=source_string)
        return result

    #  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

    def _get_primary_publish_path(self, settings, fields):
        scene_pub_template_name = settings["primary_publish_template"].value
        scene_pub_template = self.parent.engine.get_template_by_name(
            scene_pub_template_name
        )
        return scene_pub_template.apply_fields(fields)

    def _list_of_publish_metadata(self, settings, item):
        result = []
        _map = item.properties['map_of_publishes_by_path']

        for ref in item.properties['list_of_references']:
            _node = ref.nodes()[0]
            _fields = self._reference_fields(
                settings=settings,
                reference=ref,
                map_of_publishes_by_path=_map
            )
            result.append(
                PublishMetadata(
                    fields=_fields,
                    reference=ref,
                    publish=_map[str(ref.path)],
                    path_to_publish=self._publish_path(settings, _fields),
                    root_node=_node
                )
            )

        return result

    def _reference_fields(self, settings, reference, map_of_publishes_by_path):
        result = self._get_file_fields(settings)
        result['Asset'] = (reference.namespace)
        return result

    #  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
    def _get_file_fields(self, settings):
        work_template = self.parent.engine.get_template_by_name(
            settings.get('workarea_template').value
        )

        publish_template = self.parent.engine.get_template_by_name(
            settings.get('primary_publish_template').value
        )

        scene_path = os.path.abspath(pm.sceneName())

        if work_template.validate(scene_path):
            fields = work_template.get_fields(scene_path)
        elif publish_template.validate(scene_path):
            fields = publish_template.get_fields(scene_path)
        else:
            message = (
                "Your current scene path ({0}) doesn't correspond "
                "to a valid work or publish template"
            )
            raise Exception(message.format(scene_path))

        return fields

    def _publish_path(self, settings, fields):
        template = self.parent.engine.get_template_by_name(
            settings.get("publish_template").value
        )
        publish_path = template.apply_fields(fields)
        publish_folder = os.path.dirname(publish_path)
        self.parent.ensure_folder_exists(publish_folder)
        return publish_path

    # ---------------------------------------------------------------------------

    def abc_export_alembic(self):
        command_arguments = (
            self._abc_command(
                start=self.time['start'],
                end=self.time['end'],
                prefix='mtyCache'
            )
        )

    def _get_shot_time(self):
        return {
            'start': pm.playbackOptions(animationStartTime=True, query=True),
            'end': pm.playbackOptions(animationEndTime=True, query=True)
        }

    #  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

    def _abc_command(self, prefix, node, path_to_publish):
        _start = self.time['start']
        _end = self.time['end']
        list_of_arguments = [
            '-frameRange {0} {1}'.format(_start, _end),
            '-attrPrefix {0}'.format(prefix),
            '-stripNamespaces',
            '-uvWrite',
            # '-writeColorSets', # Usually not needed
            # '-writeFaceSets', # Usually not needed
            '-worldSpace',
            '-writeVisibility',
            # '-writeUVSets', # Usually not needed
            # '-dataFormat ogawa', # not needed as the default is Ogawa
            '-eulerFilter',
            '-root {0}'.format(node.longName()),
            '-file {0}'.format(path_to_publish)
        ]
        # result = ' '.join(list_of_arguments)
        result = (" -j \"%s\"" % ' '.join(list_of_arguments))

        return result

    def fix_path(self, path):
        path = path.replace("\\\\", "/")
        path = path.replace("\\", "/")

        return path

