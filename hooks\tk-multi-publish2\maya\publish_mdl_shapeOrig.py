#####################################################################################
#
# Copyright (c) 2021 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################
import os
import shutil
import sys
import tempfile
import traceback

import sgtk
from sgtk.util.filesystem import copy_file, ensure_folder_exists

import maya.cmds as cmds
import maya.mel as mel
import pymel.core as pm

HookBaseClass = sgtk.get_hook_baseclass()


class ModelShapeOrigPublishPlugin(HookBaseClass):
    """
    Plugin for publishing an open Harmony session.
    This hook relies on functionality found in the base file publisher hook in
    the publish2 app and should inherit from it in the configuration. The hook
    setting for this plugin should look something like this::
        hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py"

    """

    # NOTE: The plugin icon and name are defined by the base file plugin.
    @property
    def icon(self):
        """
        Path to an png icon on disk
        """
        # look for icon one level up from this hook's folder in "icons" folder
        return os.path.join(self.disk_location, os.pardir, "icons", "shape_publish.png")

    @property
    def name(self):
        return "Publish ShapeOrig  to Shotgun"

    @property
    def description(self):
        """
        Verbose, multi-line description of what the plugin does. This can
        contain simple html for formatting.
        """

        loader_url = "https://support.shotgunsoftware.com/hc/en-us/articles/219033078"

        return """
        Export Geometry with shapeOrig nodes
        """

    @property
    def settings(self):
        # settings specific to this class
        maya_publish_settings = {
            "Work Template": {
                "type": "template",
                "defaut": None,
                "description": "Template path for the current scene file. Should correspond to a template defined in templates.yml.",
            },
            "Primary Publish Template": {
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                "correspond to a template defined in "
                "templates.yml.",
            },
            "Model Group name": {
                "type": "str",
                "default": "model_high_root",
                "description": "The Model group name, is the name of the transform node"
                "that will be scaned, by this plugin ",
            },
        }

        # inherit the settings from the base publish plugin
        plugin_settings = super(ModelShapeOrigPublishPlugin, self).settings or {}

        # update the base settings
        plugin_settings.update(maya_publish_settings)

        return plugin_settings

    @property
    def item_filters(self):
        """
        List of item types that this plugin is interested in.
        Only items matching entries in this list will be presented to the
        accept() method. Strings can contain glob patters such as *, for
        example ["harmony.*", "file.harmony"]
        """
        return ["maya.session.geometry"]

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root

    def accept(self, settings, item):
        item.properties["accepted"] = True
        scene_name = cmds.file(query=True, sn=True)

        if not scene_name:
            raise Exception("Please Save your file before Publishing")

        if self.parent.engine.context.step["name"].lower() != "model":
            return {"accepted": False}

        valueoverrides = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        )

        acceptShapeOrig = valueoverrides.get_value("mty.publisher.accept.shapeOrig")

        if acceptShapeOrig:
            return {"accepted": True, "checked": True}
        else:
            return {"accepted": False}

    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish. Returns a
        boolean to indicate validity.
        :param settings: Dictionary of Settings. The keys are strings, matching
                         the keys returned in the settings property. The values
                         are `Setting` instances.
        :param item: Item to process
        :returns: True if item is valid, False otherwise.
        """

        publisher = self.parent
        path = item.properties.get("path")

        model_grp = settings["Model Group name"].value

        model_grp_exist = cmds.objExists(model_grp)

        if not model_grp_exist:
            message = "The Scene doesn't have {0}".format(model_grp_exist)
            raise Exception(message)

        return True

    def publish(self, settings, item):
        # Get Settings
        work_template_name = settings["Work Template"].value
        publish_template_name = settings["Primary Publish Template"].value
        main_modeling_grp = settings["Model Group name"].value

        work_template = self.parent.engine.get_template_by_name(work_template_name)
        publish_template = self.parent.engine.get_template_by_name(
            publish_template_name
        )

        current_work_path = os.path.abspath(cmds.file(query=True, sn=True))
        fields = work_template.get_fields(current_work_path)

        publish_version = fields["version"]
        original_name = fields["name"]

        publish_session_path = publish_template.apply_fields(fields)
        dependencies = [publish_session_path]

        fields["name"] = original_name + "MDL"
        publish_mdl_path = publish_template.apply_fields(fields)

        publish_name = fields.get("name")

        # Save new Scene
        self.create_shapeOrig_nodes(main_modeling_grp)

        cmds.file(rename=publish_mdl_path)
        cmds.file(save=True, type="mayaAscii")

        cmds.file(current_work_path, o=True)

        publish_data = {
            "tk": self.parent.engine.sgtk,
            "context": item.context,
            "comment": item.description,
            "path": publish_mdl_path,
            "name": publish_name,
            "version_number": publish_version,
            "thumbnail_path": item.get_thumbnail_as_path(),
            "task": self.parent.engine.context.task,
            "published_file_type": "Maya Scene",
            "dependency_paths": dependencies,
        }
        sg_publishes = sgtk.util.register_publish(**publish_data)

        item.properties.sg_publish_data = sg_publishes

        # finally just store the publish data for later retrieval
        # and upload to the host storage location

        root_item = self.get_root_item(item)
        root_item.properties.setdefault("sg_publish_extra_data", [])

        publish_extra_data = root_item.properties["sg_publish_extra_data"]
        publish_extra_data.append(sg_publishes)

    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed, and can for example be used to version up files.
        :param settings: Dictionary of Settings. The keys are strings, matching
                         the keys returned in the settings property.
                         The values are `Setting` instances.
        :param item: Item to process
        """

        # do the base class finalization
        # super(HarmonySessionPublishPlugin, self).finalize(settings, item)
        self.logger.debug("Item sequence successfully published")
        # bump the session file to the next version
        # self._save_to_next_version(item.properties["path"], item, _save_session)

    def create_shapeOrig_nodes(self, main_modeling_grp):
        # This Cluster remove vertex information
        initial_cluster = cmds.cluster(main_modeling_grp)
        cmds.delete(initial_cluster)

        # Delete Construction history
        cmds.delete(main_modeling_grp, ch=True)

        if not cmds.objExists(main_modeling_grp + "_Orig"):
            shapeOrig_grp = main_modeling_grp + "_Orig"
            cmds.duplicate(main_modeling_grp, name=shapeOrig_grp)

            names_ls = cmds.listRelatives(
                main_modeling_grp, allDescendents=True, type="transform"
            )
            nodes_ls = cmds.listRelatives(
                shapeOrig_grp, allDescendents=True, type="transform", f=True
            )

            for i in range(len(names_ls)):
                orig_mesh = cmds.rename(nodes_ls[i], names_ls[i] + "_Orig")

                list_target_mesh = cmds.listRelatives(names_ls[i], shapes=True)
                list_source_mesh = cmds.listRelatives(orig_mesh, shapes=True)

                if not list_source_mesh is None:
                    for i in range(len(list_source_mesh)):
                        cmds.connectAttr(
                            list_source_mesh[i] + ".worldMesh[0]",
                            list_target_mesh[i] + ".inMesh",
                        )
                        cmds.setAttr(list_source_mesh[i] + ".visibility", 0)

            cmds.setAttr("{0}.hiddenInOutliner".format(shapeOrig_grp), 1)
