#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

import os
import sys
import json
import traceback
from tank import Hook
from html import entities
from pprint import pformat
from tank.platform.qt import QtCore, QtGui

class ScanSceneHook(Hook):

    def scan_scene(self):

        self.parent.engine.logger.info("")
        self.parent.engine.logger.info(
            "Scanning scene for 2D Backgrounds...".ljust(120, "-")
        )

        items_result = []
        warnings = []
        errors = []

        shot_id = self.parent.context.entity['id']

        filters = [['id', 'is', shot_id]]
        fields = ['assets']
        data_shot = self.parent.shotgun.find_one('Shot', filters, fields)

        assets_id = [asset["id"] for asset in data_shot["assets"]]

        backgrounds_asset_type = self.parent.get_setting("backgrounds_asset_type")


        # ------------------------------------------------------------------------------
        # Load value overrides framework
        valueoverrides = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        ) or self.load_framework("mty-framework-valueoverrides")

        if not valueoverrides:
            self.parent.engine.logger.error("Couldn't load value overrides framework")
            backgrounds_tasks_list = self.parent.get_setting(
                "backgrounds_tasks_list"
            )
            self.parent.engine.logger.info(
                "backgrounds_tasks_list from hook settings: {}".format(
                    backgrounds_tasks_list
                )
            )

        # Get task priority list
        if valueoverrides:
            default_value_code = "mty.multi.batchloader.scan.backgrounds_priority_list"
            override_link = {"type": "Task", "id": self.parent.engine.context.task["id"]}
            backgrounds_tasks_list = valueoverrides.get_value(
                default_value_code, link=override_link
            )
            if backgrounds_tasks_list:
                backgrounds_tasks_list = json.loads(backgrounds_tasks_list)
                self.parent.engine.logger.info(
                    "backgrounds_tasks_list from override: {}".format(
                        backgrounds_tasks_list
                    )
                )

        published_file_type_names = ['Photoshop Layer Group Proxy Image']

        if not assets_id:
            items_result.append({})
            return items_result, warnings, errors

        filters = [
            ["entity.Asset.sg_asset_type", "in", backgrounds_asset_type],
            ['entity.Asset.id', 'in', assets_id],
            ['task.Task.content', 'in', backgrounds_tasks_list],
            ['sg_status_list', 'is', 'apr'],
            ['published_file_type.PublishedFileType.code', 'in', published_file_type_names]
        ]

        fields = [
            "code", "name", "published_file_type", "version_number", "task",
            "path", 'id', "entity.Asset.code"
        ]

        task_priority = backgrounds_tasks_list

        try:

            self.parent.logger.info("Scanning scene to get the backgrounds needed for the scene.")

            published_files_list = self.parent.shotgun.find(
                "PublishedFile", filters, fields
            )

            if not published_files_list:
                items_result.append({})
                return items_result, warnings, errors

            # Filter by task priority
            # ====================

            publishes_dict = {}
            for publish in published_files_list:
                asset_name = publish.get("entity.Asset.code", None)
                task_name = publish.get("task", {}).get("name", None)
                version_number = publish.get("version_number", {})

                if not asset_name:
                    self.parent.logger.error(
                        "Couldn't get asset name for publish:\n{}".format(
                            pformat(publish)
                        )
                    )
                    continue

                if asset_name not in publishes_dict.keys():
                    publishes_dict[asset_name] = {
                        task_name: {
                            version_number: [publish]
                        }
                    }
                else:
                    if task_name in publishes_dict[asset_name].keys():
                        if version_number in publishes_dict[asset_name][task_name].keys():
                            publishes_dict[asset_name][task_name][version_number].append(publish)
                        else:
                            publishes_dict[asset_name][task_name][version_number] = [publish]
                    else:
                        publishes_dict[asset_name][task_name] = {
                            version_number: [publish]
                        }

            self.parent.logger.info("publishes_dict:\n{}".format(pformat(publishes_dict)))

            # Order by version number
            # ====================

            for bg in publishes_dict.keys():
                for task in task_priority:
                    if publishes_dict[bg].get(task, None):
                        versions = list(publishes_dict[bg][task].keys())
                        latest_version = max(versions)
                        version_publishes_list = publishes_dict[bg][task][latest_version]

                        for publish in version_publishes_list:
                            node = publish["name"]
                            path = publish["path"]["local_path"]
                            sg_data = publish

                            items_result.append(
                                {
                                    'node': node,
                                    'path': path,
                                    'process_hook': 'batchload_process_bgs',
                                    'type': 'Bg file',
                                    'sg_data': sg_data,
                                }
                            )
                        break


            # Ensure metasync framework is available, but only load it once.
            if not hasattr(self, 'metasync'):
                self.metasync = self.parent.engine.custom_frameworks.get(
                "mty-framework-metasync"
            )
            if not self.metasync:
                self.metasync = self.load_framework("mty-framework-metasync")

            transfersManager = self.metasync.transfersManager

            for bg in items_result:
                # if "path" in bg and "sg_data" in bg:
                bg_local_path = bg.get("path", "")
                if not os.path.exists(bg_local_path):
                    self.parent.logger.info(
                        "Bg not found on disk: %s" % bg_local_path
                    )
                    self.parent.logger.info(
                        "sg_data:\n{}".format(pformat(bg['sg_data']))
                    )
                    transfersManager.ensure_file_is_local(
                        # bg_local_path, bg['sg_data']["sg_data"]
                        bg_local_path, bg.get('sg_data', {}), sound_notifications=False
                    )
                # transfersManager.ensure_local_dependencies(bg.get('sg_data', {}).get('sg_data', None))
                transfersManager.ensure_local_dependencies(bg.get('sg_data', {}))

        except:
            error_str = traceback.format_exc()
            errors.append(error_str)
            self.parent.log_debug(error_str)

        self.parent.logger.info("items_result:\n{}".format(items_result))

        return items_result, warnings, errors
