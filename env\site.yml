# Copyright (c) 2018 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

description: Apps and Engines when launching with a site level context.

################################################################################

includes:
- ./includes/frameworks.yml
- ./includes/settings/tk-desktop.yml
- ./includes/settings/tk-desktop2.yml

################################################################################
# configuration for all engines to load in a project context
engines:
  tk-desktop: '@settings.tk-desktop.site'
  tk-desktop2: '@settings.tk-desktop2.site'

################################################################################
# reference all of the common frameworks

frameworks: "@frameworks"
