# ----------------------------------------------------------------------------
# Copyright (c) 2020, <PERSON>.
#
# Your use of this software as distributed in this GitHub repository, is
# governed by the Apache License 2.0
#
# Your use of the Shotgun Pipeline Toolkit is governed by the applicable
# license agreement between you and Autodesk / Shotgun.
#
# The full license is in the file LICENSE, distributed with this software.
# ----------------------------------------------------------------------------


"""
Hook that loads defines all the available actions, broken down by publish type.
"""

import os
import re
import bpy
import glob
import sgtk
import pprint
import fileseq
import traceback
from mathutils import Matrix
from sgtk.errors import TankError
from contextlib import contextmanager
from tank.platform.qt import QtCore, QtGui
from bpy_extras.image_utils import load_image

QLabel = QtGui.QLabel
QDialog = QtGui.QDialog
QPushButton = QtGui.QPushButton
QVBoxLayout = QtGui.QVBoxLayout
QApplication = QtGui.QApplication


__author__ = "Diego Garcia Huerta"
__contact__ = "https://www.linkedin.com/in/diegogh/"


HookBaseClass = sgtk.get_hook_baseclass()


pp = pprint.pprint
pf = pprint.pformat

# Get Blender version
blender_version = bpy.app.version


class BlenderActions(HookBaseClass):

    # --- COLOR SPACE MAPPING ---
    # Key: Name from ShotGrid
    # Value: Exact name Blender expects for img.colorspace_settings.name
    BLENDER_COLOR_SPACE_MAP = {
        "sRGB": "Utility - sRGB - Texture", # Common for most color textures
        "Linear": "Utility - Linear - sRGB", # Common for data like normal maps, displacement, or linear exrs
        "Raw": "Utility - Raw",             # For non-color data or specific cases
        "ACEScg": "ACES - ACEScg",          # For ACES workflow.
    }

    def __init__(self, *args, **kwargs):
        """
        Initialize the BlenderActions class.
        This will retrieve ShotGrid project color space info and set up the Blender-specific mapping.
        """
        super(BlenderActions, self).__init__(*args, **kwargs)
        self.parent.engine.logger.debug("BlenderActions __init__ called.")

        self.is_OCIO_loaded = False # Keep this as per your original code

        try:
            self.sg_project_colorspaces = self.get_sg_project_info(
                ["sg_working_color_space", "sg_output_color_space"]
            )
            self.parent.engine.logger.info("Successfully retrieved ShotGrid project color spaces.")
        except Exception as e:
            self.parent.engine.logger.error(f"Failed to retrieve ShotGrid project color spaces: {e}. Using default values.")
            # Fallback to default values if ShotGrid info retrieval fails
            self.sg_project_colorspaces = {
                "sg_working_color_space": "Utility - sRGB - Texture",
                "sg_output_color_space": "Utility - sRGB - Texture"
            }

        sg_color_space_name_from_sg = self.sg_project_colorspaces.get("sg_working_color_space")

        # --- Assign desired_blender_color_space as a self attribute ---
        self.desired_blender_color_space = self.BLENDER_COLOR_SPACE_MAP.get(sg_color_space_name_from_sg, None)

        if self.desired_blender_color_space:
            self.parent.engine.logger.info(f"Mapped ShotGrid working color space '{sg_color_space_name_from_sg}' to Blender '{self.desired_blender_color_space}'")
        else:
            self.parent.engine.logger.warning(f"No explicit Blender color space mapping found for ShotGrid value: '{sg_color_space_name_from_sg}'. Using Blender's default for texture color space.")

    def get_view3d_operator_context(self):
        """
        Get a context override for 3D view operators.
        Logs debug messages to the ShotGrid Toolkit logger.
        """
        try:
            self.parent.engine.logger.debug("get_view3d_operator_context() called.")
            for window in bpy.context.window_manager.windows:
                self.parent.engine.logger.debug("Found a window object.")

                for area in window.screen.areas:
                    self.parent.engine.logger.debug(f"Found area: Type={area.type}, Width={area.width}, Height={area.height}")
                    if area.type == "VIEW_3D":
                        self.parent.engine.logger.debug("Found VIEW_3D area.")
                        for region in area.regions:
                            self.parent.engine.logger.debug(f"Found region: Type={region.type}")
                            if region.type == "WINDOW":
                                self.parent.engine.logger.debug("Found WINDOW region.")
                                space = area.spaces.active
                                self.parent.engine.logger.debug(f"Active space type: {space.type}")
                                if space.type == 'VIEW_3D':
                                    self.parent.engine.logger.debug("Active space is VIEW_3D. Constructing context_override.")

                                    context_override = {
                                        "window": window,
                                        "screen": window.screen,
                                        "area": area,
                                        "region": region,
                                        "scene": bpy.context.scene,
                                        "space_data": space,
                                        "view_layer": bpy.context.view_layer,
                                    }

                                    if hasattr(bpy.context, 'active_object') and bpy.context.active_object is not None:
                                        context_override['active_object'] = bpy.context.active_object
                                        self.parent.engine.logger.debug("Added active_object to context_override.")

                                    if hasattr(bpy.context, 'selected_objects') and bpy.context.selected_objects is not None:
                                        context_override['selected_objects'] = bpy.context.selected_objects
                                        self.parent.engine.logger.debug("Added selected_objects to context_override.")

                                    self.parent.engine.logger.debug("get_view3d_operator_context() returning context_override.")
                                    return context_override
            self.parent.engine.logger.debug("get_view3d_operator_context() finished loops, no valid context found. Returning None.")
            return None
        except Exception as e:
            self.parent.engine.logger.error(f"ERROR in get_view3d_operator_context: {e}")
            return None


    ###########################################################################
    # public interface - to be overridden by deriving classes

    def generate_actions(self, sg_publish_data, actions, ui_area):
        """
        Returns a list of action instances for a particular publish. This
        method is called each time a user clicks a publish somewhere in the UI.
        The data returned from this hook will be used to populate the actions
        menu for a publish.

        The mapping between Publish types and actions are kept in a different
        place (in the configuration) so at the point when this hook is called,
        the loader app has already established *which* actions are appropriate
        for this object.

        The hook should return at least one action for each item passed in via
        the actions parameter.

        This method needs to return detailed data for those actions, in the
        form of a list of dictionaries, each with name, params, caption and
        description keys.

        Because you are operating on a particular publish, you may tailor the
        output  (caption, tooltip etc) to contain custom information suitable
        for this publish.

        The ui_area parameter is a string and indicates where the publish is to
        be shown.
        - If it will be shown in the main browsing area, "main" is passed.
        - If it will be shown in the details area, "details" is passed.
        - If it will be shown in the history area, "history" is passed.

        Please note that it is perfectly possible to create more than one
        action "instance" for an action!
        You can for example do scene introspectionvif the action passed in
        is "character_attachment" you may for examplevscan the scene, figure
        out all the nodes where this object can bevattached and return a list
        of action instances: "attach to left hand",v"attach to right hand" etc.
        In this case, when more than  one object isvreturned for an action, use
        the params key to pass additional data into the run_action hook.

        :param sg_publish_data: Shotgun data dictionary with all the standard
                                publish fields.
        :param actions: List of action strings which have been
                        defined in the app configuration.
        :param ui_area: String denoting the UI Area (see above).
        :returns List of dictionaries, each with keys name, params, caption
         and description
        """

        app = self.parent
        engine = app.engine
        engine.logger.info(
            "Generate actions called for UI element %s. "
            "Actions: %s. Publish Data: %s" % (ui_area, actions, sg_publish_data)
        )

        published_file_type = sg_publish_data["published_file_type"]["name"]
        app.log_debug("published_file_type: {}".format(published_file_type))

        # Get project colorspaces info
        self.sg_project_colorspaces = self.get_sg_project_info(
            ["sg_working_color_space", "sg_output_color_space"]
        )

        env_vars_dict = dict(os.environ)
        if "OCIO" in env_vars_dict:
            self.is_OCIO_loaded = True
        else:
            self.is_OCIO_loaded = False

        # Get actions from the base class
        action_instances = super(BlenderActions, self).generate_actions(
            sg_publish_data, actions, ui_area
        )

        engine.logger.info("Original action_instances:\n{}".format(pf(action_instances)))

        # Modify the captions and descriptions of the actions to remove channel information
        for action in action_instances:
            if action["name"] == "asSequencerMovie":
                action["caption"] = "As Sequencer Movie"
                action["description"] = "This will create a new movie clip in the sequencer"
            elif action["name"] == "asSequencerSound":
                action["caption"] = "As Sequencer Sound"
                action["description"] = "This will create a new sound clip in the sequencer"
            elif action["name"] == "asSequencerImage":
                action["caption"] = "As Sequencer Image"
                action["description"] = "This will create a new image clip in the sequencer"

        engine.logger.info("Modified action_instances:\n{}".format(pf(action_instances)))

        action_instances = []

        if "link" in actions:
            action_instances.append(
                {
                    "name": "link",
                    "params": None,
                    "caption": "Link Library file",
                    "description": (
                        "This will link the contents of the chosen item"
                        " to the current collection."
                    ),
                }
            )

        if "import" in actions:
            action_instances.append(
                {
                    "name": "import",
                    "params": None,
                    "caption": "Import into Collection",
                    "description": (
                        "This will import the item into the current collection."
                    ),
                }
            )

        if "append" in actions:
            action_instances.append(
                {
                    "name": "append",
                    "params": None,
                    "caption": "Append Library File",
                    "description": (
                        "This will add the contents of the chosen item"
                        " to the current collection."
                    ),
                }
            )

        if "asCompositorNodeMovieClip" in actions:
            action_instances.append(
                {
                    "name": "asCompositorNodeMovieClip",
                    "params": None,
                    "caption": "As Compositor Movie Clip",
                    "description": (
                        "This will create a new compositor node and load the movie into it"
                    ),
                }
            )

        if "asCompositorNodeImage" in actions:
            action_instances.append(
                {
                    "name": "asCompositorNodeImage",
                    "params": None,
                    "caption": "As Compositor Image Node",
                    "description": (
                        "This will create a new compositor node and load the image into it"
                    ),
                }
            )

        if "asSequencerImage" in actions:
            action_instances.append(
                {
                    "name": "asSequencerImage",
                    "params": None,
                    "caption": "As Sequencer Image",
                    "description": (
                        "This will create a new image clip in the sequencer"
                    ),
                }
            )

        if "asSequencerMovie" in actions:
            action_instances.append(
                {
                    "name": "asSequencerMovie",
                    "params": None,
                    "caption": "As Sequencer Movie",
                    "description": (
                        "This will create a new movie clip in the sequencer"
                    ),
                }
            )

        if "asSequencerSound" in actions:
            action_instances.append(
                {
                    "name": "asSequencerSound",
                    "params": None,
                    "caption": "As Sequencer Sound",
                    "description": (
                        "This will create a new sound clip in the sequencer"
                    ),
                }
            )

        if "copy_path" in actions:
            action_instances.append(
                {
                    "name":
                        "copy_path",
                    "params":
                        None,
                    "caption":
                        "Copy publish path",
                    "description":
                        "Copies the publish path to the clipboard",
                }
            )

        if "download_publish" in actions:
            action_instances.append(
                {
                    "name":
                        "download_publish",
                    "params":
                        None,
                    "caption":
                        "Download publish",
                    "description":
                        "Ensure the file exists locally, downloading it if necesary...",
                }
            )

        if "add_audio" in actions:
            action_instances.append(
                {
                    "name": "add_audio",
                    "params": None,
                    "caption": "Add Audio",
                    "description": "Add this audio to the animation timeline",
                }
            )

        if "add_texture" in actions:
            action_instances.append(
                {
                    "name": "add_texture",
                    "params": None,
                    "caption": "Add Texture",
                    "description": "Add this texture to the animation timeline",
                }
            )

        if "add_camera" in actions:
            action_instances.append(
                {
                    "name": "add_camera",
                    "params": None,
                    "caption": "Add Camera",
                    "description": "Add this camera to the animation timeline",
                }
            )

        if "add_background_image" in actions:
            action_instances.append(
                {
                    "name": "add_background_image",
                    "params": None,
                    "caption": "Add Background Image",
                    "description": "Add this image as a background to the camera",
                }
            )

        # Modify the description of some of the engine actions
        # for action in action_instances:

        engine.logger.info("all action_instances:\n{}".format(pf(action_instances)))

        return action_instances


    def execute_action(self, name, params, sg_publish_data):
        """
        Execute a given action. The data sent to this be method will
        represent one of the actions enumerated by the generate_actions method.

        :param name: Action name string representing one of the items returned
                     by generate_actions.
        :param params: Params data, as specified by generate_actions.
        :param sg_publish_data: Shotgun data dictionary with all the standard
                                publish fields.
        :returns: No return value expected.
        """
        engine = sgtk.platform.current_engine()
        app = self.parent
        app.log_debug(
            "Execute action called for action %s. "
            "Parameters: %s. Publish Data: %s" % (name, params, sg_publish_data)
        )

        # resolve path
        # toolkit uses utf-8 encoded strings internally and Blender API
        # expects unicode so convert the path to ensure filenames containing
        # complex characters are supported
        path = self.get_publish_path(sg_publish_data)
        path = self.fix_path(path)
        engine.logger.info("fixed path: {}".format(path))
        engine.logger.info("publish_path: {}".format(path))

        # ensure file is local, always
        self.ensure_file_is_local(path, sg_publish_data)

        if name == "link":
            self._create_link(path, sg_publish_data)
            return

        if name == "append":
            self._create_append(path, sg_publish_data)
            return

        if name == "import":
            self._do_import(path, sg_publish_data)
            return

        if name == "asCompositorNodeMovieClip":
            self._create_compositor_node_movie_clip(path, sg_publish_data)
            return

        if name == "asSequencerMovie":
            self._create_sequencer_movie(path, sg_publish_data)
            return

        if name == "asSequencerSound":
            self._create_sequencer_sound(path, sg_publish_data)
            return

        if name == "copy_path":
            self._copy_publish_path(path)

        if name == "download_publish":
            self.ensure_file_is_local(path, sg_publish_data)

        if name == "asSequencerImage":
            engine.logger.info("asSequencerImage, path: {}".format(path))
            self._create_sequencer_image(path, sg_publish_data)
            return

        if name == "add_audio":
            self.add_audio(path, sg_publish_data)
            return

        if name == "add_camera":
            self.add_camera(path, sg_publish_data)
            return

        if name == "add_background_image":
            self.add_background_image(path, sg_publish_data)
            return

        if name == "add_texture":
            self.add_texture(path, sg_publish_data)
            return

        # override engine actions with the proper resolved path
        if "%" in path:
            sequence_files, fileseq_obj = self._collect_sequenced_files(path)
            if sequence_files:
                path = sequence_files[0]

        if name == "asCompositorNodeImage":
            self._create_compositor_node_image(path, sg_publish_data)
            return

        super(BlenderActions, self).execute_action(name, params, sg_publish_data)


    def execute_multiple_actions(self, actions):
        """
        Executes the specified action on a list of items.

        The default implementation dispatches each item from ``actions`` to
        the ``execute_action`` method.

        The ``actions`` is a list of dictionaries holding all the actions to
        execute.
        Each entry will have the following values:

            name: Name of the action to execute
            sg_publish_data: Publish information coming from Shotgun
            params: Parameters passed down from the generate_actions hook.

        .. note::
            This is the default entry point for the hook. It reuses the
            ``execute_action`` method for backward compatibility with hooks
             written for the previous version of the loader.

        .. note::
            The hook will stop applying the actions on the selection if an
            error is raised midway through.

        :param list actions: Action dictionaries.
        """
        app = self.parent
        for single_action in actions:
            app.log_debug("Single Action: %s" % single_action)
            name = single_action["name"]
            sg_publish_data = single_action["sg_publish_data"]
            params = single_action["params"]

            # self.execute_action(name, params, sg_publish_data)
            try:
                self.execute_action(name, params, sg_publish_data)
                # play sound from config hook 'play_sounds'
                self.parent.logger.info("Playing sound, reached end of execute_action")
                self.parent.engine.execute_hook_expression(
                    "{config}/notifications.py",
                    "success_sound",
                )
            except Exception as e:
                self.parent.logger.error(
                    "Error raised in execute_action: {}, Full traceback:\n{}".format(
                        e, traceback.format_exc()
                    )
                )
                # play sound from config hook 'play_sounds'
                self.parent.logger.info("Playing sound, error raised in execute_action")
                self.parent.engine.execute_hook_expression(
                    "{config}/notifications.py",
                    "error_sound",
                )

    ###########################################################################
    # helper methods which can be subclassed in custom hooks to fine tune the
    # behaviour of things

    def fix_path(self, path):
        """
        Fixes the path to be compatible with the Harmony API, which only accepts forward
        slashes
        """
        # fix image_path
        path = path.replace("\\\\", "/")
        path = path.replace("\\", "/")

        path = path.replace("/", os.path.sep)

        return path

    def _copy_publish_path(self, path):
        import sys

        python_modules_path = os.path.join(
            os.path.dirname(self.disk_location), "external_python_modules"
        )
        sys.path.append(python_modules_path)
        import pyperclip

        pyperclip.copy(path)

        self.parent.engine.logger.info("Path copied to clipboard: {}".format(path))

    # def ensure_file_is_local(self, path, publish):
    #     if not hasattr(self, 'metasync'):
    #         self.metasync = self.parent.engine.custom_frameworks.get(
    #             "mty-framework-metasync"
    #         ) or self.load_framework("mty-framework-metasync")

    #     transfersManager = self.metasync.transfersManager
    #     if "%" in path:
    #         dirname = os.path.dirname(path)
    #         if os.path.exists(dirname) and self._collect_sequenced_files(path):
    #             transfersManager.ensure_local_dependencies(publish)
    #             return path
    #     else:
    #         if os.path.exists(path):
    #             transfersManager.ensure_local_dependencies(publish)
    #             return path

    #     transfersManager.ensure_file_is_local(path, publish)
    #     transfersManager.ensure_local_dependencies(publish)

    #     return path

    # def _collect_sequenced_files(self, sequence_path):
    #     """Returns a list containing all of the sequence files"""

    #     fileseq_obj = fileseq.findSequenceOnDisk(sequence_path)
    #     if fileseq_obj:
    #         self.parent.log_info("Found sequence on disk: {}".format(fileseq_obj))
    #         sequence_files = list(fileseq_obj)
    #     else:
    #         self.parent.log_warning(
    #             "Could not find sequence on disk: {}".format(sequence_path)
    #         )
    #         sequence_files = []

    #     return sequence_files, fileseq_obj

    def _create_link(self, path, sg_publish_data):
        """
        Create a reference with the same settings Blender would use
        if you used the create settings dialog.

        :param path: Path to file.
        :param sg_publish_data: Shotgun data dictionary with all the standard
                                publish fields.
        """
        if not os.path.exists(path):
            raise TankError("File not found on disk - '%s'" % path)

        with bpy.data.libraries.load(path, link=True) as (data_from, data_to):
            data_to.collections = data_from.collections

        for collection in data_to.collections:
            new_collection = bpy.data.objects.new(collection.name, None)
            new_collection.instance_type = "COLLECTION"
            new_collection.instance_collection = collection
            bpy.context.scene.collection.objects.link(new_collection)

            # Initialize custom attributes for the collection instance
            self._add_custom_attributes([new_collection])

            # Set specific values for this linked collection
            new_collection['mty_path'] = path
            new_collection['mty_type'] = 'collection'
            new_collection['mty_export'] = True

        self.parent.engine.logger.info("Linked library file successfully")

    def _create_append(self, path, sg_publish_data):
        """
        Create a reference with the same settings Blender would use
        if you used the create settings dialog.

        :param path: Path to file.
        :param sg_publish_data: Shotgun data dictionary with all the standard
                                publish fields.
        """
        if not os.path.exists(path):
            raise TankError("File not found on disk - '%s'" % path)

        with bpy.data.libraries.load(path, link=False) as (data_from, data_to):
            data_to.collections = data_from.collections

        for collection in data_to.collections:
            new_collection = bpy.data.objects.new(collection.name, None)
            new_collection.instance_type = "COLLECTION"
            new_collection.instance_collection = collection
            bpy.context.scene.collection.objects.link(new_collection)

            # Initialize custom attributes for the collection instance
            self._add_custom_attributes([new_collection])

            # Set specific values for this appended collection
            new_collection['mty_path'] = path
            new_collection['mty_type'] = 'collection'
            new_collection['mty_export'] = True

        self.parent.engine.logger.info("Appended library file successfully")

    def _do_import(self, path, sg_publish_data):
        """
        Import a file into Blender using the appropriate importer based on the file extension.
        This method supports various file formats including Alembic, Collada, FBX, OBJ, etc.

        :param path: Path to file.
        :param sg_publish_data: Shotgun data dictionary with all the standard
                                publish fields.
        """
        if not os.path.exists(path):
            raise TankError("File not found on disk - '%s'" % path)

        _, extension = os.path.splitext(path)
        extension_name = extension.lower()[1:]

        self.parent.engine.logger.info(f"Importing file with extension: {extension_name}")

        # Get the 3D view context for operators that require it
        context = self.get_view3d_operator_context()

        try:
            if extension_name in ("abc",):

                # Import Alembic file based on Blender version
                if blender_version[0] == 3:
                    self.parent.engine.logger.info("Importing Alembic file")
                    bpy.ops.wm.alembic_import(context, filepath=path, as_background_job=False)
                    return
                elif blender_version[0] == 4:
                    self.parent.engine.logger.info("Importing Alembic file (using temp_override)")
                    with bpy.context.temp_override(**context):
                        bpy.ops.wm.alembic_import(filepath=path, as_background_job=False)
                    return

            # After importing, get the selected objects and add custom attributes
            imported_objects = bpy.context.selected_objects
            if imported_objects:
                # Initialize custom attributes for all imported objects
                self._add_custom_attributes(imported_objects)

                # Set specific values for imported objects
                for obj in imported_objects:
                    obj['mty_path'] = path
                    obj['mty_type'] = 'imported_asset'
                    obj['mty_export'] = True

                self.parent.engine.logger.info(f"Added custom attributes to {len(imported_objects)} imported objects")

            return


        except Exception as e:
            self.parent.engine.logger.error(f"Error importing file: {e}")
            self.parent.engine.logger.error(f"Full traceback: {traceback.format_exc()}")
            raise


    def _create_compositor_node_movie_clip(self, path, sg_publish_data):
        """
        Create a new clip compositor node and load the selected publish into it.

        :param path: Path to file.
        :param sg_publish_data: Shotgun data dictionary with all the standard
                                publish fields.
        """
        if not bpy.context.scene.node_tree:
            bpy.context.scene.use_nodes = True

        node = bpy.context.scene.node_tree.nodes.new("CompositorNodeMovieClip")

        # store the ids of the current clips
        # I use id from python because I could not find another way to
        # uniquely identify the data
        current_movie_clip_ids = list(map(id, bpy.data.movieclips))

        filename_path, filename_file = os.path.split(path)
        bpy.ops.clip.open(
            directory=filename_path,
            files=[{"name": filename_file, "name": filename_file}],
            relative_path=True,
        )

        # find the newly import clip
        for clip in bpy.data.movieclips:
            if id(clip) not in current_movie_clip_ids:
                node.clip = clip
                break

        self.parent.engine.logger.info("Compositor node movie clip created successfully")

    def _create_compositor_node_image(self, path, sg_publish_data):
        """
        Create a new image compositor node and load the selected publish into it.

        :param path: Path to file.
        :param sg_publish_data: Shotgun data dictionary with all the standard
                                publish fields.
        """
        try:
            if not bpy.context.scene.node_tree:
                bpy.context.scene.use_nodes = True

            # Get project colorspaces with fallback to a safe default
            working_colorspace = self.sg_project_colorspaces.get("sg_working_color_space", "Utility - sRGB - Texture")

            # Create the image node
            node_tree = bpy.context.scene.node_tree
            image_node = node_tree.nodes.new("CompositorNodeImage")

            # Load the image
            image = bpy.data.images.load(path, check_existing=True)

            # Set colorspace to a known valid value
            try:
                image.colorspace_settings.name = working_colorspace
            except:
                # Fallback to a known working colorspace
                image.colorspace_settings.name = "Utility - sRGB - Texture"

            # Assign the image to the node
            image_node.image = image

            # Position the node nicely in the compositor
            image_node.location = 0, 0

            # Initialize custom attributes
            self._add_custom_attributes([image_node])

            # Set custom properties
            image_node['mty_path'] = path
            image_node['mty_type'] = 'image'
            image_node['mty_export'] = True

            return image_node

        except Exception as e:
            self.parent.logger.error(f"Error creating compositor image node: {str(e)}")
            import traceback
            self.parent.logger.error(traceback.format_exc())
            return None

    def _create_sequencer_sound(self, path, sg_publish_data):
        """
        Create a new sound for the sequence editor and load the selected publish into it.
        Note we use the next available channel

        :param path: Path to file.
        :param sg_publish_data: Shotgun data dictionary with all the standard
                                publish fields.
        """

        filename = os.path.basename(path)

         # Find the next available channel
        available_channel = self.find_next_available_channel()
        if available_channel is None:
            error_msg = "No available channels found in the sequencer"
            self.parent.engine.logger.error(error_msg)
            raise RuntimeError(error_msg)

        self.parent.engine.logger.info(f"Next available channel for audio: {available_channel}")
        self.parent.engine.logger.info(f"filename: {filename}")
        self.parent.engine.logger.info(f"path: {path}")

        # Ensure the sequence editor exists
        scene = bpy.context.scene
        if scene.sequence_editor is None:
            scene.sequence_editor_create()

        # Get the first frame of the shot
        # First, try to get it from the ShotGrid data if available
        first_frame = scene.frame_start
        try:
            if sg_publish_data and "entity" in sg_publish_data:
                # Try to get the cut in value from the shot entity
                shot_entity = sg_publish_data["entity"]
                if shot_entity and shot_entity["type"] == "Shot":
                    # Get the shot from ShotGrid
                    shot = self.parent.shotgun.find_one(
                        "Shot",
                        [["id", "is", shot_entity["id"]]],
                        ["sg_cut_in"]
                    )
                    if shot and "sg_cut_in" in shot and shot["sg_cut_in"] is not None:
                        first_frame = shot["sg_cut_in"]
                        self.parent.engine.logger.info(f"Using shot's cut_in frame: {first_frame}")
        except Exception as e:
            self.parent.engine.logger.warning(f"Error getting shot's cut_in frame: {e}")
            self.parent.engine.logger.warning("Using scene's frame_start instead")

        self.parent.engine.logger.info(f"Importing audio at frame: {first_frame}")

        # Add the audio to the sequence editor
        audio_sequence = scene.sequence_editor.sequences.new_sound(
            name=filename,
            filepath=path,
            channel=available_channel,
            frame_start=first_frame
        )

        # Initialize custom attributes
        self._add_custom_attributes([audio_sequence])

        # Set specific values for this audio sequence
        audio_sequence['mty_path'] = path
        audio_sequence['mty_type'] = 'audio'
        audio_sequence['mty_export'] = True

        # Configure audio settings
        audio_sequence.show_waveform = True  # Show audio waveform in the sequencer
        audio_sequence.volume = 1.0         # Set default volume

        # Update scene frame range to match audio length if needed
        if audio_sequence.frame_final_duration > scene.frame_end:
            scene.frame_end = audio_sequence.frame_final_duration

        self.parent.engine.logger.info("Audio loaded successfully")

    def _create_sequencer_movie(self, path, sg_publish_data):
        """
        Create a new movie for the sequence editor and load the selected publish into it.
        Note we use the next available channel

        :param path: Path to file.
        :param sg_publish_data: Shotgun data dictionary with all the standard
                                publish fields.
        """

        filename = os.path.basename(path)

        # Find the next available channel
        available_channel = self.find_next_available_channel()
        if available_channel is None:
            self.parent.engine.logger.warning(
                "Couldn't find any available channel, adding the movie to channel 1"
            )
            available_channel = 1

        self.parent.engine.logger.info(f"Next available channel for movie: {available_channel}")
        self.parent.engine.logger.info(f"filename: {filename}")
        self.parent.engine.logger.info(f"path: {path}")

        # Ensure the sequence editor exists
        scene = bpy.context.scene
        if scene.sequence_editor is None:
            scene.sequence_editor_create()

        # Get the first frame of the shot
        # First, try to get it from the ShotGrid data if available
        first_frame = scene.frame_start
        try:
            if sg_publish_data and "entity" in sg_publish_data:
                # Try to get the cut in value from the shot entity
                shot_entity = sg_publish_data["entity"]
                if shot_entity and shot_entity["type"] == "Shot":
                    # Get the shot from ShotGrid
                    shot = self.parent.shotgun.find_one(
                        "Shot",
                        [["id", "is", shot_entity["id"]]],
                        ["sg_cut_in"]
                    )
                    if shot and "sg_cut_in" in shot and shot["sg_cut_in"] is not None:
                        first_frame = shot["sg_cut_in"]
                        self.parent.engine.logger.info(f"Using shot's cut_in frame: {first_frame}")
        except Exception as e:
            self.parent.engine.logger.warning(f"Error getting shot's cut_in frame: {e}")
            self.parent.engine.logger.warning("Using scene's frame_start instead")

        self.parent.engine.logger.info(f"Importing movie at frame: {first_frame}")

        # Add the movie to the sequence editor
        movie_sequence = scene.sequence_editor.sequences.new_movie(
            name=filename,
            filepath=path,
            channel=available_channel,
            frame_start=first_frame
        )

        # Initialize custom attributes
        self._add_custom_attributes([movie_sequence])

        # Set specific values for this movie sequence
        movie_sequence['mty_path'] = path
        movie_sequence['mty_type'] = 'movie'
        movie_sequence['mty_export'] = True

        # Update scene frame range to match movie length if needed
        if movie_sequence.frame_final_duration > scene.frame_end:
            scene.frame_end = movie_sequence.frame_final_duration

        self.parent.engine.logger.info("Movie loaded successfully")

    def _create_sequencer_image(self, path, sg_publish_data):
        """
        Create a new image for the sequence editor and load the selected publish into it.

        :param path: Path to file.
        :param sg_publish_data: Shotgun data dictionary with all the standard
                                publish fields.
        """
        try:
            # Get the next available channel
            next_channel = self.find_next_available_channel() or 3

            # Get filename and directory
            filename = os.path.basename(path)
            directory = os.path.dirname(path)

            # Ensure the sequence editor exists
            scene = bpy.context.scene
            if not scene.sequence_editor:
                scene.sequence_editor_create()

            # Get project colorspaces with fallback
            working_colorspace = self.sg_project_colorspaces.get("sg_working_color_space", "sRGB")

            # Create the image sequence strip using the sequence_editor API directly
            seq_editor = scene.sequence_editor
            if "%" in filename:
                # Handle image sequence
                try:
                    # Get frame range from the sequence
                    frame_start = 1  # Default start frame
                    frame_end = 100  # Default end frame

                    # Create the strip
                    strip = seq_editor.sequences.new_image(
                        name=filename,
                        filepath=path,
                        channel=next_channel,
                        frame_start=scene.frame_current,
                    )

                    # Set properties
                    strip.frame_final_duration = frame_end - frame_start + 1
                    strip.frame_offset_start = 0
                    strip.frame_offset_end = 0

                except Exception as e:
                    self.parent.logger.error(f"Error creating image sequence: {str(e)}")
                    import traceback
                    self.parent.logger.error(traceback.format_exc())
                    return None
            else:
                # Handle single image
                try:
                    strip = seq_editor.sequences.new_image(
                        name=filename,
                        filepath=path,
                        channel=next_channel,
                        frame_start=scene.frame_current,
                    )
                    # Set duration to 100 frames by default
                    strip.frame_final_duration = 100
                except Exception as e:
                    self.parent.logger.error(f"Error creating image strip: {str(e)}")
                    import traceback
                    self.parent.logger.error(traceback.format_exc())
                    return None

            # Set colorspace
            if hasattr(strip, 'color_space'):
                strip.color_space = working_colorspace

            # Initialize custom attributes
            self._add_custom_attributes([strip])

            # Set custom properties
            strip['mty_path'] = path
            strip['mty_type'] = 'image_sequence' if "%" in filename else 'image'
            strip['mty_export'] = True

            return strip

        except Exception as e:
            self.parent.logger.error(f"Error in _create_sequencer_image: {str(e)}")
            import traceback
            self.parent.logger.error(traceback.format_exc())
            return None

    def add_audio(self, path, sg_publish_data=None):
        """
        Add an audio file to the animation timeline using production settings.
        Uses ShotGrid data for frame timing and automatically finds next available channel.

        Args:
            path (str): Path to the audio file.
            sg_publish_data (dict, optional): Shotgun data dictionary with publish info.

        Returns:
            bpy.types.SoundSequence: The created sound sequence or None if failed.
        """
        if not os.path.exists(path):
            self.parent.engine.logger.error(f"Audio file not found: {path}")
            return None

        # Ensure we have a sequence editor
        scene = bpy.context.scene
        if not scene.sequence_editor:
            scene.sequence_editor_create()

        # Find the next available channel
        channel = self.find_next_available_channel(scene)
        if channel is None:
            self.parent.engine.logger.warning(
                "Couldn't find any available channel, adding the audio to channel 2"
            )
            channel = 2

        # Get frame start from ShotGrid data or use scene start
        frame_start = scene.frame_start  # Default to scene start
        if sg_publish_data and "entity" in sg_publish_data:
            try:
                shot_entity = sg_publish_data["entity"]
                if shot_entity and shot_entity["type"] == "Shot":
                    shot = self.parent.shotgun.find_one(
                        "Shot",
                        [["id", "is", shot_entity["id"]]],
                        ["sg_cut_in"]
                    )
                    if shot and "sg_cut_in" in shot and shot["sg_cut_in"] is not None:
                        frame_start = shot["sg_cut_in"]
            except Exception as e:
                self.parent.engine.logger.warning(
                    f"Couldn't get frame start from ShotGrid: {e}. Using scene start."
                )

        # Get the filename without extension for the strip name
        filename = os.path.splitext(os.path.basename(path))[0]

        try:
            # Add the sound strip
            sound_strip = scene.sequence_editor.sequences.new_sound(
                name=filename,
                filepath=path,
                channel=channel,
                frame_start=frame_start
            )

            # Initialize custom attributes
            self._add_custom_attributes([sound_strip])

            # Set specific values for this audio sequence
            sound_strip['mty_path'] = path
            sound_strip['mty_type'] = 'audio'
            sound_strip['mty_export'] = True

            # Configure audio settings for production
            sound_strip.show_waveform = True  # Show audio waveform
            sound_strip.volume = 1.0         # Fixed volume at 1.0
            sound_strip.mute = False          # Ensure audio is not muted
            sound_strip.pan = 0.0             # Center pan

            self.parent.engine.logger.info(
                f"Added audio '{filename}' to channel {channel} starting at frame {frame_start}"
            )
            return sound_strip

        except Exception as e:
            self.parent.engine.error(f"Failed to add audio: {e}")
            self.parent.engine.error(f"Full traceback: {traceback.format_exc()}")
            return None

    def add_texture(self, path, sg_publish_data=None):
        """
        Creates a new material with a texture and assigns it to the selected object.
        Supports both single images and image sequences (detects %04d pattern)
        or UDIMs (if detected by Blender's loader based on file naming).
        Configures the texture node for correct playback (sequence or UDIM) and color space.
        """
        self.parent.logger.info("=== START add_texture ===")
        self.parent.logger.info(f"Path: {path}")

        input_is_frame_sequence_pattern = '%04d' in path or '#' in path
        self.parent.logger.info(f"Input path suggests frame sequence: {'Yes' if input_is_frame_sequence_pattern else 'No'}")

        path_for_initial_load = path
        first_frame_number = None
        total_frame_count = 1
        sequence_files = []

        is_udim_detected = False

        # --- Handle frame sequences ---
        if input_is_frame_sequence_pattern:
            sequence_files, fileseq_obj = self._collect_sequenced_files(path)
            if sequence_files and len(sequence_files) > 0:
                path_for_initial_load = sequence_files[0]
                total_frame_count = len(sequence_files)
                
                # Extract frame number from first file
                try:
                    first_frame_number = fileseq_obj.frame_set.start
                    self.parent.logger.info(f"First frame number: {first_frame_number}, Total frames: {total_frame_count}")
                except:
                    self.parent.logger.info("Could not determine first frame number from sequence.")

        # --- Check if selected objects ---
        try:
            # Corregir el acceso a los objetos seleccionados
            if hasattr(bpy.context, 'selected_objects'):
                selected_objects = bpy.context.selected_objects
            else:
                # Alternativa para versiones más recientes de Blender
                selected_objects = [obj for obj in bpy.context.view_layer.objects if obj.select_get()]
                
            if not selected_objects:
                self.parent.logger.warning("No objects selected. Please select an object to apply the texture to.")
                return
        except Exception as e:
            self.parent.logger.error(f"Error getting selected objects: {str(e)}")
            self.parent.logger.error(traceback.format_exc())
            return None

        # --- Create material ---
        try:
            # Generate a material name based on the file name
            material_name = os.path.splitext(os.path.basename(path))[0]
            
            # Create new material
            mat = bpy.data.materials.new(name=material_name)
            mat.use_nodes = True
            
            # Clear all nodes to start clean
            nodes = mat.node_tree.nodes
            nodes.clear()
            
            # Create output node
            output = nodes.new(type='ShaderNodeOutputMaterial')
            output.location = (300, 0)
            
            # Create principled BSDF node
            bsdf = nodes.new(type='ShaderNodeBsdfPrincipled')
            bsdf.location = (0, 0)
            
            # Link principled BSDF to output
            links = mat.node_tree.links
            links.new(bsdf.outputs[0], output.inputs[0])
            
            # Create texture node
            tex = nodes.new(type='ShaderNodeTexImage')
            tex.location = (-300, 0)
            
            # Load image
            try:
                self.parent.logger.info(f"Loading image: {path_for_initial_load}")
                img = bpy.data.images.load(path_for_initial_load, check_existing=True)
                tex.image = img
                
                # Configure image for sequence playback if needed
                if input_is_frame_sequence_pattern:
                    img.source = 'SEQUENCE'
                    
                    # Set frame duration
                    if hasattr(tex, 'image_user'):
                        tex.image_user.use_auto_refresh = True
                        tex.image_user.use_cyclic = True
                        tex.image_user.frame_duration = total_frame_count
                        
                        if first_frame_number is not None:
                            tex.image_user.frame_offset = first_frame_number - bpy.context.scene.frame_start
                    
                    self.parent.logger.info(f"Configured image as sequence: Source={img.source}, Filepath={img.filepath}")
                    
                    # Forzar una actualización para que Blender reconozca la secuencia
                    bpy.context.scene.frame_current = bpy.context.scene.frame_current
                    
                elif img.source == 'TILED':
                    is_udim_detected = True
                    self.parent.logger.info(f"Blender detected UDIM: Source={img.source}, Filepath={img.filepath}, Name={img.name}")
                else: # SINGLE_IMAGE
                    self.parent.logger.info(f"Blender detected Single Image: Name={img.name}")
                
                # --- Set Color Space based on ShotGrid project info ---
                try:
                    # Obtener el mapeo desde los settings con la ruta correcta
                    color_space_map = self.parent.get_setting("settings.color-spaces.blender", {})
                    
                    if color_space_map:
                        # Obtener el color space del proyecto o usar 'sRGB' como predeterminado
                        project_color_space = getattr(self, 'sg_project_colorspaces', {}).get("sg_working_color_space", "sRGB")
                        # Obtener el color space de Blender del mapeo
                        blender_color_space = color_space_map.get(project_color_space)
                        
                        self.parent.logger.info(f"Project color space: {project_color_space}")
                        self.parent.logger.info(f"Mapped Blender color space: {blender_color_space}")
                        
                        if blender_color_space:
                            try:
                                img.colorspace_settings.name = blender_color_space
                                self.parent.logger.info(f"Set image color space to: {img.colorspace_settings.name} (Mapped from {project_color_space})")
                            except Exception as e:
                                self.parent.logger.warning(f"Could not set color space to {blender_color_space}: {str(e)}")
                        else:
                            self.parent.logger.warning(f"No Blender color space mapping found for: {project_color_space}")
                    else:
                        self.parent.logger.warning("No color space mapping found in settings. Using Blender's default.")
                except Exception as e:
                    self.parent.logger.warning(f"Error setting color space: {str(e)}")
                    self.parent.logger.warning(traceback.format_exc())
                
                # Link texture to principled BSDF
                links.new(tex.outputs[0], bsdf.inputs[0])  # Base Color
                
                # --- Texture Node Configuration (for Sequence OR UDIM) ---
                if is_udim_detected:
                    tex.interpolation = 'Cubic'
                    tex.extension = 'REPEAT'
                    self.parent.logger.info("Texture node configured for UDIM playback.")
                elif input_is_frame_sequence_pattern:
                    # Configuración específica para secuencias de imágenes en Blender 4.2
                    tex.image_user.frame_duration = total_frame_count
                    
                    if first_frame_number is not None:
                        # Calcular el offset para que la secuencia comience en el frame correcto
                        tex.image_user.frame_offset = first_frame_number - bpy.context.scene.frame_start
                        self.parent.logger.info(f"Calculated frame offset for image_user: {tex.image_user.frame_offset}")
                    else:
                        tex.image_user.frame_offset = 0
                    
                    tex.interpolation = 'Linear'
                    tex.extension = 'REPEAT'
                    self.parent.logger.info("Texture node configured for sequence playback.")
                
                # --- Apply material to selected objects ---
                for obj in selected_objects:
                    if obj.type == 'MESH':
                        # Assign material to object
                        if len(obj.material_slots) == 0:
                            obj.data.materials.append(mat)
                        else:
                            obj.material_slots[0].material = mat
                        
                        # Add custom attributes
                        self._add_custom_attributes([obj])
                        obj['mty_path'] = path
                        obj['mty_type'] = 'textured_object'
                        obj['mty_export'] = True
                        
                        self.parent.logger.info(f"Applied texture material to {obj.name}")
                
                self.parent.logger.info("=== END add_texture ===")
                return mat
                
            except Exception as e:
                self.parent.logger.error(f"Error loading image: {str(e)}")
                self.parent.logger.error(traceback.format_exc())
                return None
                
        except Exception as e:
            self.parent.logger.error(f"Error creating material: {str(e)}")
            self.parent.logger.error(traceback.format_exc())
            return None


    def add_camera(self, path, sg_publish_data=None):
        """
        Import a camera from an Alembic or FBX file and lock its properties.
        Uses _do_import for reliable importing.
        """
        self.parent.logger.info("=== START add_camera ===")
        self.parent.logger.info(f"Importing camera from: {path}")

        # Check if file exists
        if not os.path.exists(path):
            self.parent.logger.error(f"Camera file not found: {path}")
            return None

        # Store current selection state
        try:
            view_layer = bpy.context.view_layer
            active_obj = view_layer.objects.active
            selected_objects = [obj for obj in view_layer.objects if obj.select_get()] if hasattr(bpy.types.Object, 'select_get') else []
        except Exception as e:
            self.parent.logger.warning(f"Could not store selection state: {str(e)}")
            selected_objects = []

        try:
            # Use _do_import to handle the import
            self._do_import(path, sg_publish_data)

            # Find newly imported objects by comparing with previous selection
            imported_objects = [obj for obj in bpy.context.scene.objects
                              if obj not in selected_objects and obj.select_get()]

            # Find the camera in the imported objects
            imported_camera = None
            for obj in imported_objects:
                if obj.type == 'CAMERA':
                    imported_camera = obj
                    break
                # Check if it's an empty with camera children
                elif obj.type == 'EMPTY':
                    for child in obj.children:
                        if child.type == 'CAMERA':
                            imported_camera = child
                            break
                    if imported_camera:
                        break

            if not imported_camera:
                self.parent.logger.error("No camera found in the imported file")
                return None

            # Lock all transform properties
            imported_camera.lock_location = (True, True, True)
            imported_camera.lock_rotation = (True, True, True)
            imported_camera.lock_scale = (True, True, True)

            # Lock camera properties if they exist
            if hasattr(imported_camera, 'data') and imported_camera.data:
                cam_data = imported_camera.data
                if hasattr(cam_data, 'lock_zoom'): cam_data.lock_zoom = True
                if hasattr(cam_data, 'lock_dof'): cam_data.lock_dof = True
                if hasattr(cam_data, 'lock_shift'): cam_data.lock_shift = True
                if hasattr(cam_data, 'lock_sensor'): cam_data.lock_sensor = True
                if hasattr(cam_data, 'dof') and hasattr(cam_data.dof, 'use_dof'):
                    cam_data.dof.use_dof = False

            # Set as active camera
            bpy.context.scene.camera = imported_camera

            # Add custom properties
            imported_camera['mty_path'] = path
            imported_camera['mty_type'] = 'camera'
            imported_camera['mty_export'] = True

            self.parent.logger.info(f"Successfully imported and locked camera: {imported_camera.name}")
            self.parent.logger.info("=== END add_camera ===")  # Movido aquí
            return imported_camera

        except Exception as e:
            self.parent.logger.error(f"Failed to import camera: {str(e)}")
            self.parent.logger.error(traceback.format_exc())
            self.parent.logger.info("=== END add_camera (with errors) ===")  # También agregado para el caso de error
            return None

        finally:
            # Clean up selection
            try:
                bpy.ops.object.select_all(action='DESELECT')
                if active_obj and hasattr(active_obj, 'select_set'):
                    active_obj.select_set(True)
                    view_layer.objects.active = active_obj
            except:
                pass


    def add_background_image(self, path, sg_publish_data=None):
        self.parent.engine.logger.info("=== START add_background_image (v2 - RESTORED LOGIC) ===")
        self.parent.engine.logger.info(f"Path: {path}")

        is_sequence = '%04d' in path
        self.parent.engine.logger.info(f"Is sequence? {'Yes' if is_sequence else 'No'}")

        path_for_initial_load = path
        total_frame_count = 1
        first_frame_number = None

        if is_sequence:
            base_path, ext = os.path.splitext(path.replace('%04d', ''))
            glob_pattern = f"{base_path}*{ext}"
            self.parent.engine.logger.info(f"Glob pattern for sequence check: {glob_pattern}")

            matching_files = sorted(glob.glob(glob_pattern))
            if not matching_files:
                self.parent.engine.logger.error(f"No files found matching sequence pattern: {glob_pattern}")
                return None

            path_for_initial_load = matching_files[0]
            self.parent.engine.logger.info(f"First frame found for initial load: {path_for_initial_load}")
            total_frame_count = len(matching_files)
            self.parent.engine.logger.info(f"Calculated total frames in sequence: {total_frame_count}")

            try:
                filename_only = os.path.basename(path_for_initial_load)
                match = re.search(r'(\d+)(?=\.\w+$)', filename_only)
                if match:
                    first_frame_number = int(match.group(1))
                    self.parent.engine.logger.info(f"Extracted first frame number: {first_frame_number}")
                else:
                    self.parent.engine.logger.warning(f"Could not extract a numeric frame number from {filename_only}.")
            except Exception as e:
                self.parent.engine.logger.warning(f"Error extracting first frame number: {e}")

        elif not os.path.exists(path_for_initial_load):
            self.parent.engine.logger.error(f"File not found: {path_for_initial_load}")
            return None

        try:
            # --- LÓGICA DE CÁMARA RESTAURADA ---
            # Se restaura la lógica original para encontrar la cámara específica.
            camera = None
            # Primero intenta con la cámara activa en la vista 3D
            if bpy.context.view_layer.objects.active and bpy.context.view_layer.objects.active.type == 'CAMERA':
                if bpy.context.view_layer.objects.active.get('mty_type') == 'camera':
                    camera = bpy.context.view_layer.objects.active

            # Si no se encuentra una cámara activa válida, se busca en toda la escena.
            if not camera:
                self.parent.engine.logger.info("No active valid camera found, searching scene...")
                # La lógica original que busca el atributo personalizado 'mty_type'
                cameras = [obj for obj in bpy.context.scene.objects if obj.type == 'CAMERA' and obj.get('mty_type') == 'camera']
                if cameras:
                    camera = cameras[0]
                    # Es buena práctica asegurarse de que la cámara encontrada sea la activa de la escena
                    bpy.context.scene.camera = camera
                    self.parent.engine.logger.info(f"Found and set scene camera: {camera.name} with 'mty_type' attribute.")
                else:
                    self.parent.engine.logger.error("No camera with custom attribute 'mty_type' == 'camera' found in the scene.")
                    return None
            else:
                 self.parent.engine.logger.info(f"Using active camera: {camera.name}")


            if not camera.data.background_images:
                bg = camera.data.background_images.new()
            else:
                bg = camera.data.background_images[0]

            img = None
            try:
                self.parent.engine.logger.info(f"Attempting to load: {path_for_initial_load}")
                img = bpy.data.images.load(path_for_initial_load, check_existing=True)

                bg.image = img

                if is_sequence:
                    bg.image.source = 'SEQUENCE'
                    self.parent.engine.logger.info(f"Image source set to SEQUENCE.")

                if hasattr(self, 'desired_blender_color_space') and self.desired_blender_color_space:
                    bg.image.colorspace_settings.name = self.desired_blender_color_space
                    self.parent.engine.logger.info(f"Set image color space to: {bg.image.colorspace_settings.name}")

                if is_sequence:
                    bg.image_user.frame_duration = total_frame_count
                    if first_frame_number is not None:
                        bg.image_user.frame_offset = first_frame_number - bpy.context.scene.frame_start
                    else:
                        bg.image_user.frame_offset = 0
                    self.parent.engine.logger.info(f"Sequence configured: Duration={bg.image_user.frame_duration}, Offset={bg.image_user.frame_offset}")

                bg.frame_method = 'FIT'
                bg.display_depth = 'BACK'
                bg.alpha = 1.0

                camera.data.show_background_images = True
                self.parent.engine.logger.info(f"Background image properties set for {camera.name}")

                # --- CAMBIO CLAVE: Forzar actualización de la escena ---
                if is_sequence and first_frame_number is not None:
                    self.parent.engine.logger.info("Forcing scene update to display sequence...")
                    # Guardamos el frame actual
                    current_frame = bpy.context.scene.frame_current
                    # Saltamos al primer fotograma de la secuencia para forzar la carga de esa imagen
                    bpy.context.scene.frame_set(first_frame_number)
                    # Opcionalmente, podemos volver al fotograma original
                    # bpy.context.scene.frame_set(current_frame)
                    self.parent.engine.logger.info(f"Scene frame set to {first_frame_number} to force refresh.")

                self.parent.engine.logger.info("=== END add_background_image ===")
                return camera

            except Exception as e:
                self.parent.engine.logger.error(f"Error loading image or setting properties: {str(e)}")
                self.parent.engine.logger.error(traceback.format_exc())
                return None

        except Exception as e:
            self.parent.engine.logger.error(f"Error in add_background_image: {str(e)}")
            self.parent.engine.logger.error(traceback.format_exc())
            return None


    def find_next_available_channel(self, scene=None, max_channels=128):
        """
        Finds the next available channel in the sequence editor.

        Args:
            scene: The scene to check (default: bpy.context.scene).
            max_channels: The maximum number of channels to check (default: 128).

        Returns:
            The next available channel index, or None if all channels are full.
        """
        if not scene:
            scene = bpy.context.scene

        if not scene.sequence_editor:
            # if no sequence editor exists, channel 1 should be available.
            return 1

        sequence_editor = scene.sequence_editor

        used_channels = set()
        for sequence in sequence_editor.sequences:
            used_channels.add(sequence.channel)
        self.parent.engine.logger.info(f"Used channels: {used_channels}")

        if not used_channels:
            return 1

        for channel_index in range(1, max_channels + 1):
            if channel_index not in used_channels:
                return channel_index

        # finally return None if all channels are being used
        return None

    def get_sg_project_info(self, proj_fields):
        engine         = self.parent.engine
        sg             = engine.shotgun
        sg_proj        = engine.context.project

        proj_filter    = [['id', 'is', sg_proj['id']]]

        sg_proj = sg.find_one('Project', proj_filter, proj_fields)
        return sg_proj

    def _add_custom_attributes(self, objects):
        """
        Initialize custom attributes in Blender objects.

        Args:
            objects (list): List of Blender objects to add attributes to
        """
        # Define the custom attributes with empty values
        attributes = {
            'mty_path': '',
            'mty_type': '',
            'mty_export': False
        }

        # Add each attribute to each object
        for obj in objects:
            for attr_name, value in attributes.items():
                obj[attr_name] = value