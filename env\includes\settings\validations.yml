validations:
  validations:
    # Maya session validations
    maya.script_nodes_jobs_vaccine: {}
    maya.delete_scripts_vaccine: {}
    maya.not_loaded_reference_nodes: {}
    maya.project_fps_setting: {}
    maya.remove_unknown_nodes: {}
    maya.remove_unknown_plugins: {}
    maya.remove_viewport_error_methods: {}
    maya.check_orphan_references: {}
    maya.check_animated_gpu_caches: {}
    maya.check_if_published_referenced_check: {}
    maya.references_without_nodes_check: {}
    maya.no_unpublished_texture_files: {}

    # Harmony session validations
    harmony.wrongly_named_nodes: {}
    harmony.long_names_files:
      setting:
        id_elements_mapping file name: "id_elements_mapping_tmp"
    harmony.sync_settings:
      setting:
        Verification elements:
          - sg_working_color_space
          - sg_resolution
          - sg_fps
          - frame_range
        No Resolution Validation tasks:
          - rndCutOut
          - rndPaperless
          - rnd2dFX
          - rnd2dLighting
          - rnd3dLighting
        Color Spaces:
          aces_cg: ACES - ACEScg
          sRGB: sRGB
          rec709: Rec.709
          rec709 2.4: Rec.709 2.4
    harmony.verify_illegal_files: {}
    harmony.unable_to_read_files: {}
    harmony.time_chart:
      setting:
        Timechart validation: false
    harmony.task_status: {}
    harmony.main_display_node_active: {}
    harmony.color_palette_location: {}

    # After Effects session validations
    aftereffects.check_published_and_unpublished_files: {}

  # By Context validations
  context_entity:
    episode: {}
    sequence:
      validation:
        # Maya sequence context validations
        maya.no_audio_source_check: {}
        maya.no_shots_without_assets_attribute: {}
        maya.breakdown_assets_in_session_check: {}
        maya.no_locked_version_without_description: {}
        maya.shot_length_error_check: {}
        maya.shot_scale_error_check: {}
        maya.not_visible_huds_check: {}

        # Harmony sequence context validations
    shot:
      validation:
        # Maya shot context validations
        maya.no_audio_source_check: {}
        maya.animation_publish_references_check: {}
        maya.breakdown_assets_in_session_check: {}
        maya.use_latest_published_references_check: {}
        # maya.check_shot_assets_exist: {}
        maya.latest_published_audio_check: {}
        maya.duplicated_model_high_root_check: {}
        maya.duplicated_namespaces_in_shot_breakdown: {}
        maya.breakdown_entities_without_linked_assets_check: {}
        maya.referenced_namespaces_not_shot_breakdown_check: {}

        # Harmony shot context validations
        harmony.audio_on_scene:
          setting:
            Audio validations: [ ]
      setting: {}
      context_step:
        reference:
          validation: {}
          context_task:
            refreference: {}
        editorial:
          validation: {}
          context_task:
            edlanimatic: {}
        comp:
          validation: {}
          context_task:
            cmppre: {}
            cmpcomp: {}
        setup:
          validation: {}
          context_task:
            stpassembly: {}
            stplayout: {}
        animation:
          validation:
            # Maya animation context validations
            maya.not_enabled_huds_check: {}

          context_task:
            anmposing: {}
            anminbetween: {}
            anmrough: {}
            anmtiedown: {}
            anmclean: {}
            anmcolor: {}
            anm3dblocking: {}
            anm3dpolish: {}
        fx:
          validations: {}
          context_task:
            fx2drough: {}
            fx2dcleanup: {}
            fx3dfx: {}
        lighting:
          validation: {}
          context_task:
            lgt2dLighting: {}
            lgt3dLighting: {}
        rendering:
          validation: {}
          context_task:
            rndcutout: {}
            rnd2dfx: {}
            rnd3dlighting: {}
        retakes:
          validation: {}
          context_task:
            retakecutout: {}
            retakerough: {}
        cutout:
          validation: {}
          context_task:
            cutposing: {}
            cutinbetween: {}
    asset:
      validation:
        # Maya asset context validations
        # maya.only_one_root_node_allowed: {}
        maya.check_anim_nodes: {}
        maya.no_duplicated_node_names_check: {}
      # setting: {}

      context_step:
        reference:
          validation: {}
          context_task:
            refreference: {}
        art:
          validation: {}
          context_task:
            artconcept: {}
            artdesign: {}
        design:
          validation: {}
          context_task:
            dsnsheeta: {}
            dsnposea: {}
            dsncharta: {}
            dsncolora: {}
        model:
          validation:
            # Maya model context validations
            maya.model_high_root_check: {}
            maya.no_shape_deformed_shapes_check: {}
            maya.no_smooth_geometry_display_check: {}
          context_task:
            mdlmodeling: {}
            mdluvs: {}
        surfacing:
          validation:
            # Maya surfacing context validations
            maya.model_high_root_check: {}
          context_task:
            sfctexturing: {}
            sfcshading: {}
        rig:
          validation:
            # Maya rig context validations
            maya.rig_root_check: {}
            maya.mesh_reference_override: {}
          context_task:
            rig2drigging: {}
            rig3drigging: {}
        cycles:
          validation: {}
          context_task:
            cysidie: {}
            cyswalk: {}
            cysrun: {}
            cysextra: {}
