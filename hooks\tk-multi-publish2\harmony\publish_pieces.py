#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

import os
import sys
import shutil

import os
import glob
import shutil
import fnmatch
import traceback

import sgtk
from sgtk.util.filesystem import ensure_folder_exists


HookBaseClass = sgtk.get_hook_baseclass()


class HarmonySessionPublishPlugin(HookBaseClass):
    """
    Plugin for publishing an open Harmony session.
    This hook relies on functionality found in the base file publisher hook in
    the publish2 app and should inherit from it in the configuration. The hook
    setting for this plugin should look something like this::
        hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py"
    """

    # NOTE: The plugin icon and name are defined by the base file plugin.

    @property
    def description(self):
        """
        Verbose, multi-line description of what the plugin does. This can
        contain simple html for formatting.
        """

        loader_url = "https://support.shotgunsoftware.com/hc/en-us/articles/219033078"

        return """
        Publishes the file to Shotgun. A <b>Publish</b> entry will be
        created in Shotgun which will include a reference to the file's current
        path on disk. If a publish template is configured, a copy of the
        current session will be copied to the publish template path which
        will be the file that is published. Other users will be able to access
        the published file via the <b><a href='%s'>Loader</a></b> so long as
        they have access to the file's location on disk.
        If the session has not been saved, validation will fail and a button
        will be provided in the logging output to save the file.
        <h3>File versioning</h3>
        If the filename contains a version number, the process will bump the
        file to the next version after publishing.
        The <code>version</code> field of the resulting <b>Publish</b> in
        Shotgun will also reflect the version number identified in the filename
        The basic worklfow recognizes the following version formats by default:
        <ul>
        <li><code>filename.v###.ext</code></li>
        <li><code>filename_v###.ext</code></li>
        <li><code>filename-v###.ext</code></li>
        </ul>
        After publishing, if a version number is detected in the work file, the
        work file will automatically be saved to the next incremental version
        number. For example, <code>filename.v001.ext</code> will be published
        and copied to <code>filename.v002.ext</code>
        If the next incremental version of the file already exists on disk, the
        validation step will produce a warning, and a button will be provided
        in the logging output which will allow saving the session to the next
        available version number prior to publishing.
        <br><br><i>NOTE: any amount of version number padding is supported. for
        non-template based workflows.</i>
        <h3>Overwriting an existing publish</h3>
        In non-template workflows, a file can be published multiple times,
        however only the most recent publish will be available to other users.
        Warnings will be provided during validation if there are previous
        publishes.
        """ % (
            loader_url,
        )

    @property
    def settings(self):
        """
        Dictionary defining the settings that this plugin expects to receive
        through the settings parameter in the accept, validate, publish and
        finalize methods.
        A dictionary on the following form::
            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }
        The type string should be one of the data types that toolkit accepts as
        part of its environment configuration.
        """

        # inherit the settings from the base publish plugin
        base_settings = super(HarmonySessionPublishPlugin, self).settings or {}

        #base_settings["File Types"]["default"].append(["Harmony Project File", "xstage"])

        # settings specific to this class
        harmony_publish_settings = {
            "Work Template": {
                "type": "template",
                "default": None,
                "description": "Template path for published work files. Should"
                "correspond to a template defined in "
                "templates.yml.",
            },
            "Proxy Work Template": {
                "type": "template",
                "default": None,
                "description": "Template path for published work files. Should"
                "correspond to a template defined in "
                "templates.yml.",
            },
            "Proxy Publish Library Template": {
                "type": "template",
                "default": None,
                "description": "Template path for published work files. Should"
                "correspond to a template defined in "
                "templates.yml.",
            },
            "Proxy Publish Version Template": {
                "type": "template",
                "default": None,
                "description": "Template path for published work files. Should"
                "correspond to a template defined in "
                "templates.yml.",
            },
            "Render Work Template": {
                "type": "template",
                "default": None,
                "description": "Template path for published work files. Should"
                "correspond to a template defined in "
                "templates.yml.",
            },
            "Render Publish Library Template": {
                "type": "template",
                "default": None,
                "description": "Template path for published work files. Should"
                "correspond to a template defined in "
                "templates.yml.",
            },
            "Render Publish Version Template": {
                "type": "template",
                "default": None,
                "description": "Template path for published work files. Should"
                "correspond to a template defined in "
                "templates.yml.",
            }
        }

        # update the base settings
        base_settings.update(harmony_publish_settings)

        return base_settings

    @property
    def item_filters(self):
        """
        List of item types that this plugin is interested in.
        Only items matching entries in this list will be presented to the
        accept() method. Strings can contain glob patters such as *, for
        example ["harmony.*", "file.harmony"]
        """
        return ["harmony.pieces"]

    def accept(self, settings, item):
        """
        Method called by the publisher to determine if an item is of any
        interest to this plugin. Only items matching the filters defined via
        the item_filters property will be presented to this method.
        A publish task will be generated for each item accepted here. Returns a
        dictionary with the following booleans:
            - accepted: Indicates if the plugin is interested in this value at
                all. Required.
            - enabled: If True, the plugin will be enabled in the UI, otherwise
                it will be disabled. Optional, True by default.
            - visible: If True, the plugin will be visible in the UI, otherwise
                it will be hidden. Optional, True by default.
            - checked: If True, the plugin will be checked in the UI, otherwise
                it will be unchecked. Optional, True by default.
        :param settings: Dictionary of Settings. The keys are strings, matching
                         the keys returned in the settings property. The values
                         are `Setting` instances.
        :param item: Item to process
        :returns: dictionary with boolean keys accepted, required and enabled
        """

        # if a publish template is configured, disable context change. This
        # is a temporary measure until the publisher handles context switching
        # natively.
        engine = sgtk.platform.current_engine()
        path = engine.app.get_current_project_path()
        publisher = self.parent

        template = publisher.engine.get_template_by_name('harmony_asset_work')
        fields = template.get_fields(path)

        work_template = engine.get_template_by_name('asset_work_proxy_custom_shape_image')
        render_work_template = engine.get_template_by_name('asset_work_render_custom_shape_image')
        render_publish_template = engine.get_template_by_name('asset_publish_render_custom_shape_image')
        publish_template = engine.get_template_by_name('asset_publish_proxy_custom_shape_image')

        try:
            item_piece_sequence = item.properties['piece_sequence']
            self.logger.debug("Found a piece item and is %s" % (item_piece_sequence))
            return {"accepted": True, "checked": True}
        except:
            self.logger.debug("Not a piece item")
            return {"accepted": False, "checked": True}


        if settings.get("Proxy Publish Template").value:
            item.context_change_allowed = False

        #if not session_path:
            # the session has not been saved before (no path determined).
            # provide a save button. the session will need to be saved before
            # validation will succeed.
            #self.logger.warn(
            #    "The Harmony session has not been saved.", extra=_get_save_as_action()
            #)
        self.logger.info("Harmony 'ERROR LINE' plugin accepted the current session.")
        #self.logger.info("Harmony '%s' plugin accepted the current session." % (self.name))
        return {"accepted": True, "checked": True}

    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish. Returns a
        boolean to indicate validity.
        :param settings: Dictionary of Settings. The keys are strings, matching
                         the keys returned in the settings property. The values
                         are `Setting` instances.
        :param item: Item to process
        :returns: True if item is valid, False otherwise.
        """
        ctx = self.parent.context
        publisher = self.parent
        path = _session_path()

        # ---- ensure the session has been saved

        if not path:
            # the session still requires saving. provide a save button.
            # validation fails.
            error_msg = "The Harmony session has not been saved."
            self.logger.error(error_msg, extra=_get_save_as_action())
            raise Exception(error_msg)

        # ---- check the session against any attached work template

        # get the path in a normalized state. no trailing separator,
        # separators are appropriate for current os, no double separators,
        # etc.
        asset_id = ctx.entity['id']
        asset_name = ctx.entity['name']

        path = sgtk.util.ShotgunPath.normalize(path)
        # if the session item has a known work template, see if the path
        # matches. if not, warn the user and provide a way to save the file to
        # a different path
        work_template = item.properties.get("work_template")
        if work_template:
            if not work_template.validate(path):
                self.logger.warning(
                    "The current session does not match the configured work " "file template.",
                    extra={
                        "action_button": {
                            "label": "Save File",
                            "tooltip": "Save the current session to a " "different file name",
                            # will launch wf2 if configured
                            "callback": _get_save_as_action(),
                        }
                    },
                )
            else:
                self.logger.debug("Work template configured and matches session file.")
        else:
            self.logger.debug("No work template configured.")

        # ---- see if the version can be bumped post-publish
        # ---- Check if sequence pieces exist in the asset customshapes
        import pyseq
        item_piece_sequence = item.properties["piece_sequence"]
        piece = item.properties['piece_piece']

        session_work_template_settings = settings.get("Work Template")
        session_work_template = publisher.engine.get_template_by_name(session_work_template_settings.value)
        fields = session_work_template.get_fields(path)

        work_template = item.properties['piece_work_template']
        padding = int(work_template.keys['shape.frame'].format_spec)
        asset_entity = publisher.shotgun.find('Asset',[['id','is',asset_id]])

        frames_problems = []
        for frame in item_piece_sequence.frames():
            self.logger.info('Validating frame {0} from {1}'.format(frame,item_piece_sequence))
            fields['shape.frame'] = frame
            fields['shape.piece'] = piece
            frame_work_path = work_template.apply_fields(fields)

            frame_item = pyseq.Item(frame_work_path)
            frame_digits = frame_item.digits[0]
            item_padding = len(frame_digits)

            fileExt = (os.path.basename(frame_work_path)).split(".")[1]
            self.logger.debug("Filename: {0}".format(str(item_piece_sequence.head())))
            #if not item_piece_sequence.includes(expected_format):
            if item_piece_sequence.head() != ("{0}_".format(piece)):
                error_msg = 'The sequence {0} does not have the name as the required. \nPlease check it again cuz the script is case sensitive.\nIt should be like {1}_'.format(str(item_piece_sequence.head()),piece)
                self.logger.error (error_msg)
                raise Exception(error_msg)

            if not item_padding == padding:
                error_msg = 'Padding on the {0} piece is not correct, the expected padding was {1}, received {2}'
                self.logger.error(error_msg.format(piece,padding,item_padding))
                raise Exception(error_msg.format(piece,padding,item_padding))


        if frames_problems:
            error_msg = ("The following frames do not exist in {0}'s shotgun shape dictionary and will not be published,\n"
                         "if there is an error on this comunicate it to your supervisor: \n{1}")
            message = error_msg.format(piece, ", ".join(frames_problems))
            self.logger.error(message)
            raise Exception(message)


        # ---- populate the necessary properties and call base class validation

        # populate the publish template on the item if found
        publish_template_setting = settings.get("Proxy Publish Version Template")
        self.logger.debug(publish_template_setting.value)
        publish_template = publisher.engine.get_template_by_name(
            publish_template_setting.value
        )
        if publish_template:
            item.properties["proxy_version_publish_template"] = publish_template

        # populate the publish template on the item if found
        publish_template_setting = settings.get("Proxy Publish Library Template")
        self.logger.debug(publish_template_setting.value)
        publish_template = publisher.engine.get_template_by_name(
            publish_template_setting.value
        )
        if publish_template:
            item.properties["proxy_library_publish_template"] = publish_template


        # populate the publish template on the item if found
        publish_template_setting = settings.get("Render Publish Version Template")
        self.logger.debug(publish_template_setting.value)
        publish_template = publisher.engine.get_template_by_name(
            publish_template_setting.value
        )
        if publish_template:
            item.properties["render_version_publish_template"] = publish_template

        # populate the publish template on the item if found
        publish_template_setting = settings.get("Render Publish Library Template")
        self.logger.debug(publish_template_setting.value)
        publish_template = publisher.engine.get_template_by_name(
            publish_template_setting.value
        )
        if publish_template:
            item.properties["render_library_publish_template"] = publish_template

        # set the session path on the item for use by the base plugin
        # validation
        # step. NOTE: this path could change prior to the publish phase.
        item.properties["path"] = path

        # run the base class validation
        ###
        # AGREGAR EL CHECAR QUE AUN EXISTA EL PATH DEL ACHIVO Y SI NO EXISTE MANDAR LOG ERROR O RISE EXCEPTION
        return True

    def publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.
        :param settings: Dictionary of Settings. The keys are strings, matching
                         the keys returned in the settings property.
                         The values are `Setting` instances.
        :param item: Item to process
        """
        self.logger.debug("Pre Load opencolorio framework")
        ocioCoreTools = self.load_framework("mty-framework-opencolorio")
        self.ocioCore = ocioCoreTools.ocioCore
        self.ocioCore.set_ocio_env()
        self.logger.debug("Pre Load openimageio framework")
        openimageIO = self.load_framework("mty-framework-openimageio")
        self.openImageIOCoreTools = openimageIO.openImageIOCore
        self.openImageIOCoreTools.set_binary('oiiotool')

        self.logger.debug(self.ocioCore)

        # get pyseq in a partially generic way, untill we have mty-framework-externalpython
        # we know this hook files lives in {config}/tk-multi-publish2/harmony, so we will place pyseq there
        config_path = os.path.dirname(os.path.dirname(self.disk_location))
        python_modules_path = os.path.join(config_path, "external_python_modules")
        sys.path.append(python_modules_path)
        import pyseq

        # we will also need to deal with some synclogs so we need metasync
        metasync = self.load_framework("mty-framework-metasync")
        self.logger.debug("Loaded metasync framework")

        # get the path in a normalized state. no trailing separator, separators
        # are appropriate for current os, no double separators, etc.
        path = sgtk.util.ShotgunPath.normalize(_session_path())
        engine = sgtk.platform.current_engine()
        ctx = self.parent.context
        publisher = self.parent

        # get fields from current work session file
        session_work_template_settings = settings.get("Work Template")
        session_work_template = publisher.engine.get_template_by_name(session_work_template_settings.value)
        fields = session_work_template.get_fields(path)

        # get item properties
        item_piece_sequence = item.properties["piece_sequence"]
        item_piece_path = item.properties['piece_sequence_path']
        piece = item.properties['piece_piece']
        piece_type = item.properties['piece_type']
        fields['shape.piece'] = piece

        work_template = item.properties['piece_work_template']
        publish_aces_template = item.properties['piece_publish_aces_template']
        publish_version_template = item.properties['piece_publish_version_template']
        publish_library_template = item.properties['piece_publish_library_template']

        self.logger.info("Publishing Versioned Sequence: %s" % (item_piece_sequence.format('%h%p%t')))
        self.parent.log_info("Publishing Versioned Sequence: %s" % (item_piece_sequence.format('%h%p%t')))

        # current image sequence generic path
        sequence_work_path = os.path.join(item_piece_sequence.dirname,
                                          item_piece_sequence.format('%h%p%t'))

        # set some placeholders
        asset_id = ctx.entity['id']
        asset_name = ctx.entity['name']
        padding = int(work_template.keys['shape.frame'].format_spec)

        # get new paths from templates and fields
        work_path = work_template.apply_fields(fields)

        # start publishing versioned pieces
        publish_version_path = publish_version_template.apply_fields(fields)
        publish_aces_path = publish_aces_template.apply_fields(fields)

        # copy files to publish location

        sequence_frames = item_piece_sequence.frames()
        if not sequence_frames and len(item_piece_sequence) != 0:
            # there is a known issue with pyseq and sequences of only one frame
            # so, we need to define the frame number manually as a workaround
            frame_item = [i for i in item_piece_sequence][0]
            # we are hardcoding an asumption of digits tokens
            frame_number = int(frame_item.digits[0])
            sequence_frames = [frame_number]

        # iterate over known frames to copy workfiles to publish
        for frame in sequence_frames:
            self.logger.debug("Copying frame %s" % (frame))
            self.parent.log_debug("Copying frame %s" % (frame))
            # update the item with the saved session path
            fields['shape.frame'] = frame

            # rebuild frame specific publish path
            frame_publish_path = publish_version_template.apply_fields(fields)
            frame_work_path = work_template.apply_fields(fields)
            frame_aces_path = publish_aces_template.apply_fields(fields)
            frame_version_path = publish_version_template.apply_fields(fields)

            if not os.path.exists(frame_work_path):
                raise Exception("Missing work frame path: {0}".format(frame_work_path))

            # get directory of file then ensures the path exists
            engine.ensure_folder_exists(os.path.dirname(frame_version_path))
            engine.ensure_folder_exists(os.path.dirname(frame_aces_path))
            sgtk.util.filesystem.copy_file(frame_work_path, frame_version_path, 0o444)
            self._transform_aces(frame_work_path,frame_aces_path)

            self.logger.debug("Created aces frame: %s" % (frame))
            self.parent.log_debug("Created aces frame: %s" % (frame))
            self.logger.debug( 'Success copy of {0}'.format(frame_publish_path))
            self.parent.log_debug( 'Success copy of {0}'.format(frame_publish_path))
            os.remove(frame_work_path)


        # also make sure that there is a frame 000 for the published items!
        if piece_type == 'proxy':
            dummy_ext = 'png'
        else:
            dummy_ext = 'exr'

        dummy_path = os.path.join(self.disk_location, "empty_piece_{0}.{1}".format(piece_type,dummy_ext))

        fields['shape.frame'] = 0
        fields['shape.frame.number'] = '000'
        dummy_publish_version_path = publish_version_template.apply_fields(fields)
        dummy_publish_render_path = dummy_publish_version_path.replace('proxies','render').replace('.png','.exr')
        engine.ensure_folder_exists(os.path.dirname(dummy_publish_version_path))
        engine.ensure_folder_exists(os.path.dirname(dummy_publish_render_path))
        if not os.path.exists(dummy_publish_version_path) or not os.path.exists(dummy_publish_render_path):
            # copy the current png
            sgtk.util.filesystem.copy_file(dummy_path, dummy_publish_version_path,0o777)
            # but also the exr
            sgtk.util.filesystem.copy_file(dummy_path.replace('proxy','render').replace('.png','.exr'), dummy_publish_render_path,0o777)


        # Now publish the versioned sequence
        # we just need to override some properties
        item.properties["path"] = sequence_work_path
        item.properties['work_template'] = work_template
        item.properties['publish_template'] = publish_version_template

        publish_data = {
            "tk": publisher.sgtk,
            "context": item.context,
            "comment": item.description,
            "path": publish_version_path,
            "name": self.get_publish_name(settings, item),
            "created_by": self.get_publish_user(settings, item),
            "version_number": fields['version'],
            "thumbnail_path": item.get_thumbnail_as_path(),
            "published_file_type": self.get_publish_type(settings, item),
            "dependency_paths": [],
            "dependency_ids": [],
            "sg_fields": {"sg_status_list": "rev"},
        }

        # Register the publish of the original PNG, so that we can have the pointer
        png_data_register = item.properties.sg_publish_data = sgtk.util.register_publish(**publish_data)
        item.properties.sg_publish_path = item.properties.sg_publish_data["path"]["local_path"]


        publish_data_aces = {
            "tk": publisher.sgtk,
            "context": item.context,
            "comment": item.description,
            "path": publish_aces_path,
            # temporary hardcode the proper extention in the name of the exr publish
            "name": self.get_publish_name(settings, item).replace('.png','.exr'),
            "created_by": self.get_publish_user(settings, item),
            "version_number": fields['version'],
            "thumbnail_path": item.get_thumbnail_as_path(),
            "published_file_type": "Harmony Pieces Render Sequence",
            "dependency_paths": [],
            # Include the path of the png as dependency
            # important to later know where it came from
            "dependency_ids": [png_data_register['id']],
            "sg_fields": {},
        }

        # and register the publish for the exr sequence
        aces_data_register = sgtk.util.register_publish(**publish_data_aces)

        # finally just store the publish data for later retrieval
        # and upload to the host storage location
        root_item = self.get_root_item(item)
        root_item.properties.setdefault('sg_publish_extra_data', [])

        self.parent.log_debug('Storing extra publish data on root item: %s' % root_item)

        publish_extra_data = root_item.properties['sg_publish_extra_data']
        publish_extra_data.append(aces_data_register)

        self.parent.log_debug("Already %s elements in extra publish data" % len(publish_extra_data))

        # Update task status for revision
        sg_task = self.parent.context.task
        self.parent.logger.info(
            "End of Publish and updating task with id: {} to status: 'rev'".format(
                str(sg_task['id'])
            )
        )

        try:
            self.parent.engine.shotgun.update(
                "Task", sg_task["id"], {"sg_status_list": 'rev'}
            )
        except:
            pass


    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed, and can for example be used to version up files.
        :param settings: Dictionary of Settings. The keys are strings, matching
                         the keys returned in the settings property.
                         The values are `Setting` instances.
        :param item: Item to process
        """

        # do the base class finalization
        #super(HarmonySessionPublishPlugin, self).finalize(settings, item)
        self.logger.debug('Item sequence successfully published')
        # bump the session file to the next version
        #self._save_to_next_version(item.properties["path"], item, _save_session)

    def get_root_item(self, item):

        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root

    def _transform_aces(self,input_image,output_image):
        import pprint
        self.parent.log_debug('Excecuting ACES Transform')
        ocio_config = os.path.join(self.ocioCore.get_config_path(), 'config.ocio')
        #input_image = '{0}\\{1}\\library\\{2}'.format(input_folder,piece,in_image)
        #out_image = '{0}\\{1}\\{2}.exr'.format(out_folder_,piece,in_image.split('.')[0])

        # since we are creating a file, we need to make sure that the folder exists
        if not os.path.exists(os.path.dirname(output_image)):
            os.makedirs(os.path.dirname(output_image))

        # the execute the conversion of the image
        commands = [input_image,
                    '--colorconfig', ocio_config,
                    '--colorconvert', "Utility - sRGB - Texture", "ACES - ACEScg",
                    '-o', output_image]

        self.parent.log_debug('ACES Command: %s' % commands)

        messageOut = pprint.pformat(self.openImageIOCoreTools.execute_command_list(commands))
        self.parent.log_debug(messageOut)
        #self.openImageIOCoreTools.execute_command_list(commands)
        self.parent.log_debug("ACES excecuted, saved in {0}".format(output_image))


    def _copy_work_to_publish(self, settings, item):
        """
        This method handles copying work file path(s) to a designated publish
        location.
        This method requires a "work_template" and a "publish_template" be set
        on the supplied item.
        The method will handle copying the "path" property to the corresponding
        publish location assuming the path corresponds to the "work_template"
        and the fields extracted from the "work_template" are sufficient to
        satisfy the "publish_template".
        The method will not attempt to copy files if any of the above
        requirements are not met. If the requirements are met, the file will
        ensure the publish path folder exists and then copy the file to that
        location.
        If the item has "sequence_paths" set, it will attempt to copy all paths
        assuming they meet the required criteria with respect to the templates.
        """
        publisher = self.parent
        dcc_app = publisher.engine.app

        # ---- ensure templates are available
        work_template = item.properties.get("work_template")
        if not work_template:
            self.logger.debug(
                "No work template set on the item. " "Skipping copy file to publish location."
            )
            return

        publish_template = self.get_publish_template(settings, item)
        if not publish_template:
            self.logger.debug(
                "No publish template set on the item. "
                "Skipping copying file to publish location."
            )
            return

        # ---- get a list of files to be copied

        # by default, the path that was collected for publishing
        work_file = item.properties.path

        # ---- copy the work files to the publish location
        if not work_template.validate(work_file):
            self.logger.warning(
                "Work file '%s' did not match work template '%s'. "
                "Publishing in place." % (work_file, work_template)
            )
            return

        work_fields = work_template.get_fields(work_file)

        missing_keys = publish_template.missing_keys(work_fields)

        if missing_keys:
            self.logger.warning(
                "Work file '%s' missing keys required for the publish "
                "template: %s" % (work_file, missing_keys)
            )
            return

        publish_file = publish_template.apply_fields(work_fields)

        return dcc_app.save_project_as(
            source_file=work_file, target_file=publish_file, open_project=False
        )


def _harmony_find_additional_session_dependencies():
    """
    Find additional dependencies from the session
    """

    return []


def _session_path():
    """
    Return the path to the current session
    :return:
    """
    engine = sgtk.platform.current_engine()

    # get the path to the current file
    path = engine.app.get_current_project_path()

    if isinstance(path, unicode):
        path = path.encode("utf-8")

    return path


def _save_session(path=None):
    """
    Save the current session to the supplied path.
    """

    engine = sgtk.platform.current_engine()
    if path is None:
        engine.app.save_project()
    else:
        # Ensure that the folder is created when saving
        folder = os.path.dirname(path)
        ensure_folder_exists(folder)

        # we are saving a new version, so we only need the name of the file
        _, filename = os.path.split(path)
        filename_file, _ = os.path.splitext(filename)
        engine.app.save_new_version(filename_file)


# TODO: method duplicated in all the Harmony hooks
def _get_save_as_action():
    """
    Simple helper for returning a log action dict for saving the session
    """

    engine = sgtk.platform.current_engine()

    callback = _save_as

    # if workfiles2 is configured, use that for file save
    if "tk-multi-workfiles2" in engine.apps:
        app = engine.apps["tk-multi-workfiles2"]
        if hasattr(app, "show_file_save_dlg"):
            callback = app.show_file_save_dlg

    return {
        "action_button": {
            "label": "Save As...",
            "tooltip": "Save the current session",
            "callback": callback,
        }
    }

import os
import sgtk
import shutil
def ensure_dummy_entity(asset_name, piece_name, project,publisher):
    exists = 1
    filters =[['sg_piece', 'is', piece_name],
              ['sg_source', 'is', '000'],
              ['assets.Asset.code', 'is', asset_name],
              ['project', 'is', project]]
    custom_shape = publisher.shotgun.find_one('Blendshape', filters)
    if custom_shape:
        return custom_shape,exists
    filters = [['code', 'is', asset_name],
               ['project', 'is', project]]
    asset = publisher.shotgun.find_one('Asset', filters)
    data = {'sg_piece': piece_name,
            'sg_source': '000',
            'code': '%s_%s_MTY' % (asset_name, piece_name),
            'assets': [asset],
            'description': 'Dummy empty image',
            'project': project}
    custom_shape = publisher.shotgun.create('Blendshape', data)
    exists = 0
    return custom_shape,exists
def presto_upload(path):

    remote = os.path.dirname(path.replace('\\', '/').replace('P:/', 'Projects/'))
    remote = "presto://user:<EMAIL>:%s" % remote
    command = ('presto-cli '
               '--overwrite=skip '
               '--encryption '
               '--compression '
               '--verbose '
               '"%s" '
               '"%s"')
    command = command % (path, remote)
    # self.parent.logger.info(command)
    os.system(command)

def register_publish(custom_shape,piece,path,publisher):
    published_file_type = "CustomShape Proxy Piece Version Frame"
    piece_ctx = publisher.sgtk.context_from_entity(custom_shape['type'], custom_shape['id'])
    publish_data = {
        "tk": publisher.sgtk,
        "context": piece_ctx,
        "path": path,
        "name": "%s_000.png" % (piece),
        "version_number": 1,
        "comment": 'automatic generation of empty piece',
        "published_file_type": published_file_type,
        "created_by": {'type': 'HumanUser', 'id': 385},
        "thumbnail_path": path,
    }
    sgtk.util.register_publish(**publish_data)


def _save_as():
    engine = sgtk.platform.current_engine()
    engine.app.save_new_version_action()

