<?xml version="1.0" encoding="UTF-8"?>
<project source="Harmony Premium (HarmonyPremium.exe) version 22.0.3 build 21960 2023-09-13 23:55:53" version="2203" build="21960" creator="harmony">
 <elements/>
 <options>
  <metrics unitAspectRatioX="4" unitAspectRatioY="3" numberOfUnitsX="24" numberOfUnitsY="24" numberOfUnitsZ="12"/>
  <resolution name="HDTV_1080p24" size="1920,1080" fovFit="VerticalFitFov" fov="41.112090439166927" projection="PerspectiveProjection"/>
  <framerate val="24"/>
  <zdragging val="true"/>
  <zOrderCompatibilityWith7_3 val="false"/>
  <pixelPerModelUnitForVectorLayers val="0.28800000000000009"/>
  <pixelPerModelUnitForBitmapLayers val="0.28800000000000009"/>
  <canvasForBitmapLayers size="3840,2160"/>
  <imageProcessingFormat val="rgbaFloat"/>
  <cameraInSymbols val="true"/>
  <scaleFactor val="1"/>
  <colorSpace val="sRGB"/>
  <convertPalettesColorSpace val="true"/>
 </options>
 <timelineMarkers/>
 <scenes>
  <scene name="Top" id="0c24b971571207a1" nbframes="54" startFrame="1" stopFrame="1">
   <columns/>
   <options>
    <defaultDisplay val="Unconnected_Display"/>
   </options>
   <rootgroup name="Top">
    <options>
     <collapsed val="false"/>
    </options>
    <nodeslist>
     <module type="SCRIPT_MODULE" name="mty_MayaBatchRender" pos="0,26,1" publishUnderTab="mty_MayaBatchRender">
      <options>
       <collapsed val="false"/>
       <version val="1"/>
      </options>
      <attrs>
       <specsEditor>
        <val>
         <![CDATA[
<specs>
  <ports>
    <in type="IMAGE"/>
    <out type="IMAGE"/>
  </ports>
  <attributes>
    <attr type="string" name="renderer" value="" tooltip="If this attribute is not set, then the MayaBatchRender node will use the default renderer specified in the Maya file. If this attribute is set, then it forces the use of a specific renderer other than the default. The following renderers are currently supported: 'renderMan' (renderMan version 22.0 or higher), 'renderManRIS' or 'RIS' (renderMan version 21.x or earlier), 'renderManReyes' or 'reyes' (renderMan version 20.x or earlier), 'arnold', 'mentalRay', 'mayaSoftware' or 'maya'. Note that those values are case insensitive."/>
    <attr type="string" name="OriginalMayaFileName" value="" tooltip="Write your project path here"/>
  </attributes>
</specs>
]]>
        </val>
       </specsEditor>
       <scriptEditor>
        <filename/>
        <editor val="renderMayaBatch();"/>
       </scriptEditor>
       <initScript>
        <filename/>
        <editor val="// Following code will be called during creation of
// Script Module.

// ..."/>
       </initScript>
       <cleanupScript>
        <filename/>
        <editor val="// Following code will be called during destruction of
// Script Module.

// ..."/>
       </cleanupScript>
       <uiScript>
        <filename/>
        <editor/>
       </uiScript>
       <uiData/>
       <files/>
       <dynamicAttr desc="string" kw="renderer">
        <renderer/>
       </dynamicAttr>
       <dynamicAttr desc="string" kw="OriginalMayaFileName">
        <originalmayafilename/>
       </dynamicAttr>
      </attrs>
     </module>
    </nodeslist>
    <linkedlist/>
   </rootgroup>
   <unconnectedComposite>
    <module type="COMPOSITE" name="Unconnected_Composite" pos="0,0,0" publishUnderTab="Unconnected_Composite">
     <options>
      <collapsed val="false"/>
      <version val="1"/>
     </options>
     <attrs>
      <compositeMode val="compositeBitmap"/>
      <flattenOutput val="true"/>
      <flattenVector val="false"/>
      <composite2d val="false"/>
      <composite3d val="false"/>
      <outputZ val="LEFTMOST"/>
      <outputZInputPort val="1"/>
      <applyFocus val="true"/>
      <multiplier val="1" defaultValue="1"/>
      <tvgPalette val="compositedPalette"/>
      <mergeVector val="false"/>
     </attrs>
    </module>
   </unconnectedComposite>
   <timelineOrderer/>
  </scene>
 </scenes>
 <symbols>
  <folder name="Symbols">
   <scene id="0c24b971571207a1"/>
  </folder>
 </symbols>
 <actionTemplate validationMarker="1">
  <node val="Top/mty_MayaBatchRender"/>
 </actionTemplate>
 <timeline>
  <scene id="0c24b971571207a1"/>
 </timeline>
</project>
