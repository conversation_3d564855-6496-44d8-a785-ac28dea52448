################################################################################
#
# Copyright (c) 2021 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
################################################################################

import os
import sys
import shutil
import traceback
import sgtk

from pprint import pformat

HookBaseClass = sgtk.get_hook_baseclass()


class TransferBackends(HookBaseClass):

    def collect(self):
        """
        A hook to define the required transfer plugins available
        Depending on the Location configuration the apropiate one must
        provide all the logic to upload and download files to the location

        This backends are just a generic classes with commond methods that
        perform different depending on the protocol and credentials to use

        The can be defined directly in the hook or can also be loaded from
        other frameworks, which can works as follows:

        sample = self.load_framework("mty-framework-sampletransferbackend")

        backends = {
            'sample': sample.sampleCore,
        }

        In this case, the sample.sampleCore is a class instance that have
        the following methods:

        - set_remote_host(host)
            a method to receive and cache the passed host to connect to

        - set_remote_root(root)
            a method to receive and cache the passed root path in the host
            where files will be downloaded from and uplaoded to

        - set_credentials(user, password)
            a method to receive and cache the passed credentials to authenticate
            to the host

        - test_credentials(user, password)
            a method to perform a test connection to validate the credentials,
            returning True or False depending if the connection was successfull

        - execute_upload(local_path, remote_path)
            a method to perform a file/folder upload to the host

        - execute_download(remote_path, local_path)
            a method to perform a file/folder download from the host

        """

        # define the generic aws framework
        aws_framework = self.load_framework("mty-framework-aws")

        # And a generic wrapper for local transfers (copy to a folder)
        class LocalTransfersWrapper():
            host = None
            root = None

            def fix_path_transfer(self, path):
                # we need to account that this local path can be a folder
                # being a folder means that it can have multiple nested folders and
                # files inside it, which can lead some file paths to be really long
                # and in the case of windows and python 2 there is a limitation
                # in the path length to be 256 characters, the limit is only
                # unlocked properly on python 3 and windows 10 and only if a registry
                # key exist, more information can be found here:
                # https://docs.microsoft.com/en-us/windows/win32/fileio/maximum-file-path-limitation
                # https://docs.microsoft.com/en-us/windows/win32/fileio/naming-a-file#win32-file-namespaces
                # https://stackoverflow.com/a/********/2133436
                # https://stackoverflow.com/a/********/2133436
                PLATFORM = sys.platform.upper()

                if PLATFORM.startswith("DARWIN"):
                    # return the original path
                    return path
                elif PLATFORM.startswith("LINUX"):
                    # return the original path
                    return path
                elif PLATFORM.startswith("WIN"):
                    # first we need to replace all forward slashes with back slashes so the
                    # namespace works properly
                    path = path.replace("/", os.sep)
                    # we took the safest path and use file namespace on such cases
                    if not "\\\\?\\" in path:
                        # commented block because the platform identification has been done
                        # already and because it doesn't matter if the major python version is
                        # 2 or 3, it applies equally
                        # if sys.platform == "win32" and sys.version_info.major == 2:
                        #     # make sure that we use the windows file namespace
                        #     # needs to be unicode and properly scaping backslashes
                        #     new_path = u'\\\\?\\%s' % path

                        new_path = u'\\\\?\\%s' % path

                        return new_path
                # any other platform return the original path
                else:
                    return path

            def copytree(self, source_path, dest_path):
                if not os.path.exists(os.path.dirname(dest_path)):
                    os.makedirs(os.path.dirname(dest_path))
                shutil.copytree(source_path, dest_path, dirs_exist_ok=True)

            def set_remote_host(self, host):
                self.host = host

            def set_remote_root(self, root):
                self.root = root

            def set_credentials(self, user, password):
                pass

            def test_credentials(self, user, password):
                return True

            def clean_up_harmony_folder(self, basename, target_dir):
                """
                Clean up Harmony folder: delete all other xstage and aux folders that
                are not the published one
                """
                # we are only interested in delete files with these extensions, all
                # others will be kept regardless of theirs names
                filter_extensions = [
                    ".xstage",
                    ".aux",
                    r".xstage~",
                    r".aux~",
                    ".xstage.thumbnails",
                    ".thumbnails",
                ]

                # basename = os.path.basename(source_path)
                basename, ext = os.path.splitext(basename)

                # get possible names to keep list
                keep_files_names = [
                    "{}{}".format(basename, ext) for ext in filter_extensions
                ]

                # iterate through files in target_dir
                for file_name in os.listdir(target_dir):
                    file_path = os.path.join(target_dir, file_name)

                    # check if the file has the specified extension and not in
                    # keep_files_names
                    if (
                        any(file_name.endswith(ext) for ext in filter_extensions)
                        and file_name not in keep_files_names
                        and os.path.isfile(file_path)
                    ):
                        try:
                            # attempt to delete the file
                            os.remove(file_path)
                            # self.parent.logger.info(f"Deleted file: {file_name}")
                            print(f"Deleted file: {file_name}")
                        except Exception as e:
                            # self.parent.logger.error(
                            #     f"Error deleting file {file_name}: {str(e)}"
                            # )
                            print(
                                f"Error deleting file {file_name}: {str(e)}"
                            )
                    # else:
                    #     self.parent.logger.info(
                    #         "File didnt meet the conditionals: {}".format(file_name)
                    #     )

            @staticmethod
            def rename_xstage_files(
                studio_entity_name,
                client_entity_name,
                studio_task_name,
                client_task_name,
                target_dir,
                logger
            ):
                if not client_entity_name or not client_task_name:
                    logger.info(f"Skipping renaming files for {target_dir}")
                    return

                # remove placeholder extension from remote path
                target_dir_split = target_dir.split("\\")
                if target_dir_split[-1] == ".xstage":
                    _ = target_dir_split.pop(-1)
                    target_dir = os.path.join(*target_dir_split)

                # get the already resolved name from the target directory. This name
                # should already consider any custom file name template applied in the
                # deliveries framework:
                #   outgest: {
                #       custom_published_file_name_templates: {
                #           entity_type: "{custom}_{name}_{template}"
                #       }
                #   }
                resolved_name = os.path.basename(target_dir)

                logger.info("Renaming xstage file starting...")

                # iterate through files in target_dir
                for file_name in os.listdir(target_dir):
                    if not studio_entity_name in file_name:
                        continue
                    logger.info("-" * 88)

                    existing_file_name = file_name
                    # first we try to replace the existing name with the resolved name
                    file_name_no_ext = os.path.splitext(file_name)[0]
                    target_file_name = file_name.replace(
                        file_name_no_ext, resolved_name
                    )
                    logger.info(
                        f"target_file_name after resolved name replace: {target_file_name}"
                    )

                    # then, as a fallback, we try to replace the studio entity name and
                    # studio task name with the client names
                    if client_entity_name not in target_file_name:
                        target_file_name = file_name.replace(
                            studio_entity_name, client_entity_name
                        )
                        logger.info(
                            f"target_file_name after entity name replace: {target_file_name}"
                        )

                    if client_task_name not in target_file_name:
                        target_file_name = target_file_name.replace(
                            studio_task_name, client_task_name
                        )
                        logger.info(f"target_file_name after task name replace: {target_file_name}")

                    existing_file_path = os.path.join(target_dir, existing_file_name)
                    target_file_path = os.path.join(target_dir, target_file_name)
                    logger.info(f"existing_file_path: {existing_file_path}")
                    logger.info(f"target_file_path: {target_file_path}")

                    # try to rename the file
                    try:
                        os.rename(existing_file_path, target_file_path)
                    except Exception as e:
                        logger.error(f"Error renaming file: {existing_file_path}")
                        logger.error(f"Exception:\n{e}")
                        logger.error(f"Full traceback:\n{traceback.format_exc()}")

            def execute_upload(self, local_path, remote_path):
                """a method to perform a file/folder upload to the host"""

                # add namespaces if the current platform is windows
                local_path = self.fix_path_transfer(local_path)
                remote_path = self.fix_path_transfer(remote_path)

                if os.path.isdir(local_path):
                    self.copytree(local_path, remote_path)

                elif "%" in local_path or local_path.endswith(".xstage"):
                    # save xstage basename for later use when cleaning up the folder
                    if local_path.endswith(".xstage"):
                        xstage_basename = os.path.basename(local_path)
                    else:
                        xstage_basename = None
                    local_path = os.path.dirname(local_path)
                    remote_path = os.path.dirname(remote_path)
                    self.copytree(local_path, remote_path)

                    # cleanup Harmony folder
                    if xstage_basename:
                        self.clean_up_harmony_folder(xstage_basename, remote_path)

                else:
                    if not os.path.exists(os.path.dirname(remote_path)):
                        os.makedirs(os.path.dirname(remote_path))
                    shutil.copy2(local_path, remote_path)

            def execute_download(self, remote_path, local_path):
                """a method to perform a file/folder download from the host"""

                # add namespaces if the current platform is windows
                remote_path = self.fix_path_transfer(remote_path)
                local_path = self.fix_path_transfer(local_path)

                if os.path.isdir(local_path):
                    self.copytree(remote_path, local_path)
                elif "%" in local_path or local_path.endswith(".xstage"):
                    local_path = os.path.dirname(local_path)
                    remote_path = os.path.dirname(remote_path)
                    self.copytree(remote_path, local_path)
                else:
                    if not os.path.exists(os.path.dirname(local_path)):
                        os.makedirs(os.path.dirname(local_path))
                    shutil.copy2(remote_path, local_path)

        credentials =  self.load_framework("mty-framework-credentials")

        deadline =  self.load_framework("mty-framework-deadline")

        backends = {
            'aws': aws_framework.awsCore,
            'local': LocalTransfersWrapper(),
            'credentials': credentials.credentialsManager,
            'farm': deadline.deadlineSubmitJobUtils
        }

        return backends
