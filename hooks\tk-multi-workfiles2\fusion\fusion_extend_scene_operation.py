########################################################################################
#
# Copyright (c) 2025 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
########################################################################################

import os
import re
import sys
import imp
import shutil
import pprint
import logging
import traceback

import sgtk
from sgtk.platform.qt import QtGui

import BlackmagicFusion as bmd


pp = pprint.pprint
pf = pprint.pformat

fusion = bmd.scriptapp("Fusion")

HookClass = sgtk.get_hook_baseclass()


class SceneOperation(HookClass):
    """
    Hook called to perform an operation with the
    current scene
    """

    def save_as(self, file_path, context):
        engine = self.parent.engine
        if not context:
            context = engine.context
        logger = engine.logger

        logger.info("About to run save_as from super")
        super(SceneOperation, self).save_as(file_path)

        logger.info("After save_as from super, about to run scenesetup")
        self.set_env_settings(engine, context)

    def reset(self, context):
        print("*" * 120)
        print("SceneOperation.__mro__: {}".format(SceneOperation.__mro__))
        print("*" * 120)
        engine = self.parent.engine
        if not context:
            context = engine.context
        logger = engine.logger

        logger.info("About to run reset from super")
        result = super(SceneOperation, self).reset(context)
        if result:
            logger.info("After reset from super, about to run scenesetup")
            self.set_env_settings(engine, context)

        return result

    def set_env_settings(self, engine, context):
        app_scenesetup = engine.apps.get("mty-multi-scenesetup")
        module_scenesetup = app_scenesetup.import_module("scene_setup")
        manager = module_scenesetup.get_manager(app_scenesetup)

        if context.entity["type"] == "Shot":
            manager.setup_shot_settings(context=context)

        elif context.entity["type"] == "Asset":
            manager.setup_asset_settings(context=context)
