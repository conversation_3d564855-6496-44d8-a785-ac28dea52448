#####################################################################################
#
# Copyright (c) 2021 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

import os
# import sys
# import pprint
# import shutil

import sgtk

HookBaseClass = sgtk.get_hook_baseclass()


class HarmonyAssetSeqCollector(HookBaseClass):
    """
    Collector that operates on the Toon Boom Harmony session. Should inherit
    from the basic collector hook.
    """

    @property
    def settings(self):
        """
        Dictionary defining the settings that this collector expects to receive
        through the settings parameter in the process_current_session and
        process_file methods.
        A dictionary on the following form::
            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }
        The type string should be one of the data types that toolkit accepts as
        part of its environment configuration.
        """

        # grab any base class settings
        collector_settings = super(HarmonyAssetSeqCollector, self).settings or {}

        # settings specific to this collector
        harmony_session_settings = {
            "Publish Template": {
                "type": "template",
                "default": None,
                "description": "Publish path template"
            },
            "Work asset Seq Template": {
                "type": "template",
                "default": None,
                "description": "Template path where the files are located"
            },
            "Publish asset Seq Template": {
                "type": "template",
                "default": None,
                "description": "Template path where the files will be published"
            },
            "Publish asset MOV Template": {
                "type": "template",
                "default": None,
                "description": "Template path where the video will be published"
            }}

        # update the base settings with these settings
        collector_settings.update(harmony_session_settings)

        return collector_settings

    def process_current_session(self, settings, parent_item):
        """
        Analyzes the current session open in Toon Boom Harmony and parents a
        subtree of items under the parent_item passed in.
        :param dict settings: Configured settings for this collector
        :param parent_item: Root item instance
        """

        super(HarmonyAssetSeqCollector, self).process_current_session(settings, parent_item)

        item = next((i for i in parent_item.descendants if i.type_spec.endswith('.session')), None)
        if item is None:
            item = self.collect_current_harmony_session(settings, parent_item)

        assetReference = self._collect_asset_sequence(settings, item)
        #return item


    def _collect_asset_sequence(self, settings, parent_item):

        self.logger.debug("Collecting asset Squence references...")

        # get fileseq in a partially generic way, untill we have mty-framework-externalpython
        # we know this hook files lives in {config}/tk-multi-publish2/harmony, so we will place fileseq there
        config_path = os.path.dirname(os.path.dirname(self.disk_location))

        engine           = sgtk.platform.current_engine()
        #Harmony path
        path             = engine.app.get_current_project_path()
        publisher        = self.parent
        template_by_name = engine.get_template_by_name

        # get templates from settings
        work_template_setting        = settings.get("Work Template").value
        publish_session_setting      = settings.get("Publish Template").value

        work_seq_template_setting    = settings.get("Work asset Seq Template").value
        publish_seq_setting          = settings.get("Publish asset Seq Template").value
        publish_mov_setting          = settings.get("Publish asset MOV Template").value


        # Templates
        work_template            = template_by_name(work_template_setting)
        publish_session_template = template_by_name(publish_session_setting )
        work_seq_template        = template_by_name(work_seq_template_setting)
        publish_seq_template     = template_by_name(publish_seq_setting )
        publish_mov_template     = template_by_name(publish_mov_setting )


        #Adding extra element in work_fields
        work_fields               = work_template.get_fields(path)
        work_fields['frame']      = 1
        work_fields['tokenName']  = work_fields['name']
        work_seq_path             = work_seq_template.apply_fields(work_fields)

        container_folder = os.path.dirname(work_seq_path)
        base_name        = os.path.basename(work_seq_path)
        base_parts       = base_name.split('.')
        ext              = base_parts[-1]
        name             = base_parts[0]
        #task             = name.split("_")[-2]
        version          = name.split("_")[-1]
        nums             = []

        self.logger.debug("Contenedor {0}".format(container_folder))

        if not os.path.exists(container_folder):
            return

        if not os.listdir(container_folder):
            return

        work_seq_path_ext = os.path.splitext(work_seq_path)[-1].lower()
        self.parent.logger.info("work_seq_path_ext: {}".format(work_seq_path_ext))
        for f in os.listdir(container_folder):
            if len(f.split(".")) == 3:
                filename_ext = os.path.splitext(f)[-1].lower()
                self.parent.logger.debug("filename_ext: {}".format(filename_ext))
                if filename_ext != work_seq_path_ext:
                    self.parent.logger.error(
                        (
                            "Rendered file sequence uses extension {} and it doesn't "
                            "match the expected extension by the publish template. "
                        ).format(filename_ext, work_seq_path_ext)
                    )
                    return
                filename_split = f.split(".")
                newName= filename_split[0]
                num = filename_split[-2]
                if num.isdigit():
                   nums.append(int(num))

        if not len(nums):
            return

        #display_name= work_seq_path
        display_name = 'Asset Sequence {start}-{end} .png'.format(
            start=str(min(nums)).zfill(4), end=str(max(nums)).zfill(4))

        session_item = parent_item.create_item(
                                    "harmony.asset_sequence",
                                    "Asset Sequence", display_name)

        publish_icons = os.path.join(config_path, 'tk-multi-publish2', 'icons')
        icon_path     = os.path.join(publish_icons, "video.png")
        session_item.set_icon_from_path(icon_path)

        session_item.properties["asset_seq_frames"]         = nums
        session_item.properties["tokenName"]                = newName
        session_item.properties["work_fields"]              = work_fields
        session_item.properties["template_seq_work"]        = work_seq_template
        session_item.properties["template_session_publish"] = publish_session_template

        # Sub template for every plugin item
        session_item.properties["template_seq_publish"]     = publish_seq_template
        session_item.properties["template_asset_video_publish"]   = publish_mov_template


        return [session_item]


