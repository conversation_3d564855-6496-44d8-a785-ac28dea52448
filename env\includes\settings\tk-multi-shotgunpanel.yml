# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

################################################################################

includes:
- ../app_locations.yml

################################################################################

# generic
settings.tk-multi-shotgunpanel:
  action_mappings:
    PublishedFile:
    - actions: [publish_clipboard]
      filters: {}
    Task:
    - actions: [assign_task, task_to_ip]
      filters: {}
    Version:
    - actions: [quicktime_clipboard, sequence_clipboard, add_to_playlist, Download_Attachement_Version]
      filters: {}
    Note:
    - actions: [Download_Attachement_Note]
      filters: {}
  actions_hook: '{config}/tk-multi-shotgunpanel/shotgun_panel_actions.py'
  location: "@apps.tk-multi-shotgunpanel.location"

# 3dsmax
settings.tk-multi-shotgunpanel.3dsmax:
  actions_hook: '{self}/general_actions.py:{engine}/tk-multi-shotgunpanel/basic/scene_actions.py:{config}/tk-multi-shotgunpanel/shotgun_panel_actions.py'
  action_mappings:
    PublishedFile:
    - actions: [import]
      filters: {published_file_type: Alembic Cache}
    - actions: [texture_node]
      filters: {published_file_type: Image}
    - actions: [reference, import]
      filters: {published_file_type: 3dsmax Scene}
    - actions: [texture_node]
      filters: {published_file_type: Rendered Image}
    - actions: [texture_node]
      filters: {published_file_type: Texture}
    - actions: [publish_clipboard]
      filters: {}
    Task:
    - actions: [assign_task, task_to_ip]
      filters: {}
    Version:
    - actions: [quicktime_clipboard, sequence_clipboard, add_to_playlist, Download_Attachement_Version]
      filters: {}
    Note:
    - actions: [Download_Attachement_Note]
      filters: {}
  location: "@apps.tk-multi-shotgunpanel.location"

# houdini
settings.tk-multi-shotgunpanel.houdini:
  action_mappings:
    PublishedFile:
    - actions: [import]
      filters: {published_file_type: Alembic Cache}
    - actions: [merge]
      filters: {published_file_type: Houdini Scene}
    - actions: [file_cop]
      filters: {published_file_type: Image}
    - actions: [file_cop]
      filters: {published_file_type: Photoshop Image}
    - actions: [file_cop]
      filters: {published_file_type: Rendered Image}
    - actions: [file_cop]
      filters: {published_file_type: Texture}
    - actions: [publish_clipboard]
      filters: {}
    Task:
    - actions: [assign_task, task_to_ip]
      filters: {}
    Version:
    - actions: [quicktime_clipboard, sequence_clipboard, add_to_playlist, Download_Attachement_Version]
      filters: {}
    Note:
    - actions: [Download_Attachement_Note]
      filters: {}
  actions_hook: '{config}/tk-multi-shotgunpanel/shotgun_panel_actions.py'
  location: "@apps.tk-multi-shotgunpanel.location"

# maya
settings.tk-multi-shotgunpanel.maya:
  action_mappings:
    PublishedFile:
    - actions: [reference, import]
      filters: {published_file_type: Alembic Cache}
    - actions: [texture_node, image_plane]
      filters: {published_file_type: Image}
    - actions: [reference, import]
      filters: {published_file_type: Maya Scene}
    - actions: [texture_node, image_plane]
      filters: {published_file_type: Photoshop Image}
    - actions: [texture_node, image_plane]
      filters: {published_file_type: Rendered Image}
    - actions: [texture_node, image_plane]
      filters: {published_file_type: Texture}
    - actions: [udim_texture_node]
      filters: {published_file_type: UDIM Image}
    - actions: [publish_clipboard]
      filters: {}
    Task:
    - actions: [assign_task, task_to_ip]
      filters: {}
    Version:
    - actions: [quicktime_clipboard, sequence_clipboard, add_to_playlist, Download_Attachement_Version]
      filters: {}
    Note:
    - actions: [Download_Attachement_Note]
      filters: {}
  actions_hook: '{config}/tk-multi-shotgunpanel/shotgun_panel_actions.py'
  location: "@apps.tk-multi-shotgunpanel.location"

# nuke
settings.tk-multi-shotgunpanel.nuke:
  action_mappings:
    PublishedFile:
    - actions: [read_node]
      filters: {published_file_type: Alembic Cache}
    - actions: [read_node]
      filters: {published_file_type: Flame Render}
    - actions: [read_node]
      filters: {published_file_type: Flame Quicktime}
    - actions: [read_node]
      filters: {published_file_type: Image}
    - actions: [read_node]
      filters: {published_file_type: Movie}
    - actions: [script_import]
      filters: {published_file_type: Nuke Script}
    - actions: [open_project]
      filters: {published_file_type: NukeStudio Project}
    - actions: [read_node]
      filters: {published_file_type: Photoshop Image}
    - actions: [read_node]
      filters: {published_file_type: Rendered Image}
    - actions: [read_node]
      filters: {published_file_type: Texture}
    - actions: [publish_clipboard]
      filters: {}
    Task:
    - actions: [assign_task, task_to_ip]
      filters: {}
    Version:
    - actions: [quicktime_clipboard, sequence_clipboard, add_to_playlist, Download_Attachement_Version]
      filters: {}
    Note:
    - actions: [Download_Attachement_Note]
      filters: {}
  actions_hook: '{config}/tk-multi-shotgunpanel/shotgun_panel_actions.py'
  location: "@apps.tk-multi-shotgunpanel.location"

# nuke studio

settings.tk-multi-shotgunpanel.nukestudio:
  action_mappings:
    PublishedFile:
    - actions: [read_node]
      filters: {published_file_type: Alembic Cache}
    - actions: [read_node, clip_import]
      filters: {published_file_type: Flame Render}
    - actions: [read_node, clip_import]
      filters: {published_file_type: Flame Quicktime}
    - actions: [read_node, clip_import]
      filters: {published_file_type: Image}
    - actions: [read_node, clip_import]
      filters: {published_file_type: Movie}
    - actions: [script_import]
      filters: {published_file_type: Nuke Script}
    - actions: [open_project]
      filters: {published_file_type: NukeStudio Project}
    - actions: [read_node]
      filters: {published_file_type: Photoshop Image}
    - actions: [read_node, clip_import]
      filters: {published_file_type: Rendered Image}
    - actions: [read_node]
      filters: {published_file_type: Texture}
    - actions: [publish_clipboard]
      filters: {}
    Task:
    - actions: [assign_task, task_to_ip]
      filters: {}
    Version:
    - actions: [quicktime_clipboard, sequence_clipboard, add_to_playlist, Download_Attachement_Version]
      filters: {}
    Note:
    - actions: [Download_Attachement_Note]
      filters: {}
  actions_hook: '{config}/tk-multi-shotgunpanel/shotgun_panel_actions.py'
  location: "@apps.tk-multi-shotgunpanel.location"

# hiero

settings.tk-multi-shotgunpanel.hiero:
  action_mappings:
    PublishedFile:
    - actions: [clip_import]
      filters: {published_file_type: Flame Render}
    - actions: [clip_import]
      filters: {published_file_type: Flame Quicktime}
    - actions: [clip_import]
      filters: {published_file_type: Image}
    - actions: [clip_import]
      filters: {published_file_type: Movie}
    - actions: [clip_import]
      filters: {published_file_type: Rendered Image}
    - actions: [publish_clipboard]
      filters: {}
    Task:
    - actions: [assign_task, task_to_ip]
      filters: {}
    Version:
    - actions: [quicktime_clipboard, sequence_clipboard, add_to_playlist, Download_Attachement_Version]
      filters: {}
    Note:
    - actions: [Download_Attachement_Note]
      filters: {}
  actions_hook: '{config}/tk-multi-shotgunpanel/shotgun_panel_actions.py'
  location: "@apps.tk-multi-shotgunpanel.location"

# photoshop
settings.tk-multi-shotgunpanel.photoshop:
  actions_hook: "{self}/general_actions.py:{config}/tk-multi-shotgunpanel/shotgun_panel_actions.py"
  action_mappings:
    PublishedFile:
    - actions: [add_as_a_layer, open_file]
      filters: {published_file_type: Photoshop Image}
    - actions: [add_as_a_layer, open_file]
      filters: {published_file_type: Rendered Image}
    - actions: [add_as_a_layer, open_file]
      filters: {published_file_type: Image}
    - actions: [add_as_a_layer, open_file]
      filters: {published_file_type: Texture}
    - actions: [publish_clipboard]
      filters: {}
    Task:
    - actions: [assign_task, task_to_ip]
      filters: {}
    Version:
    - actions: [quicktime_clipboard, sequence_clipboard, add_to_playlist, Download_Attachement_Version]
      filters: {}
    Note:
    - actions: [Download_Attachement_Note]
      filters: {}
  location: "@apps.tk-multi-shotgunpanel.location"

# after effects
settings.tk-multi-shotgunpanel.aftereffects:
  action_mappings:
    PublishedFile:
    - actions: [add_to_project, add_to_comp]
      filters: {published_file_type: After Effects Project}
    - actions: [add_to_project, add_to_comp]
      filters: {published_file_type: Maya Scene}
    - actions: [add_to_project, add_to_comp]
      filters: {published_file_type: Photoshop Image}
    - actions: [add_to_project, add_to_comp]
      filters: {published_file_type: Rendered Image}
    - actions: [add_to_project, add_to_comp]
      filters: {published_file_type: Image}
    - actions: [add_to_project, add_to_comp]
      filters: {published_file_type: Texture}
    Task:
    - actions: [assign_task, task_to_ip]
      filters: {}
    Version:
    - actions: [quicktime_clipboard, sequence_clipboard, add_to_playlist, Download_Attachement_Version]
      filters: {}
    Note:
    - actions: [Download_Attachement_Note]
      filters: {}
  actions_hook: "{engine}/tk-multi-shotgunpanel/basic/scene_actions.py:{config}/tk-multi-shotgunpanel/shotgun_panel_actions.py"
  location: "@apps.tk-multi-shotgunpanel.location"

# alias
settings.tk-multi-shotgunpanel.alias:
  action_mappings:
    PublishedFile:
    - actions: [import]
      filters: {published_file_type: Alias File}
    - actions: [reference]
      filters: {published_file_type: Wref File}
    - actions: [import]
      filters: {published_file_type: Igs File}
    - actions: [import]
      filters: {published_file_type: Stp File}
    - actions: [import]
      filters: {published_file_type: Stl File}
    - actions: [import]
      filters: {published_file_type: Jt File}
    - actions: [import]
      filters: {published_file_type: Catpart File}
    - actions: [import]
      filters: {published_file_type: Fbx File}
    - actions: [texture_node]
      filters: {published_file_type: Image}
    - actions: [texture_node]
      filters: {published_file_type: Photoshop Image}
    - actions: [publish_clipboard]
      filters: {}
    Task:
    - actions: [assign_task, task_to_ip]
      filters: {}
    Version:
    - actions: [quicktime_clipboard, sequence_clipboard, add_to_playlist, Download_Attachement_Version]
      filters: {}
    Note:
    - actions: [Download_Attachement_Note]
      filters: {}
  actions_hook: "{engine}/tk-multi-shotgunpanel/basic/scene_actions.py:{config}/tk-multi-shotgunpanel/shotgun_panel_actions.py"
  location: "@apps.tk-multi-shotgunpanel.location"

# VRED
settings.tk-multi-shotgunpanel.vred:
  action_mappings:
    PublishedFile:
    - actions: [import]
      filters: {published_file_type: Alias File}
    - actions: [import]
      filters: {published_file_type: Igs File}
    - actions: [import]
      filters: {published_file_type: Stp File}
    - actions: [import]
      filters: {published_file_type: Stl File}
    - actions: [import]
      filters: {published_file_type: Jt File}
    - actions: [import]
      filters: {published_file_type: Catpart File}
    - actions: [import]
      filters: {published_file_type: Fbx File}
    - actions: [import]
      filters: {published_file_type: VRED Scene}
    - actions: [import]
      filters: {published_file_type: Osb File}
    - actions: [publish_clipboard]
      filters: {}
    Task:
    - actions: [assign_task, task_to_ip]
      filters: {}
    Version:
    - actions: [quicktime_clipboard, sequence_clipboard, add_to_playlist, Download_Attachement_Version]
      filters: {}
    Note:
    - actions: [Download_Attachement_Note]
      filters: {}
  actions_hook: "{engine}/tk-multi-shotgunpanel/basic/scene_actions.py:{config}/tk-multi-shotgunpanel/shotgun_panel_actions.py"
  location: "@apps.tk-multi-shotgunpanel.location"

# harmony
settings.tk-multi-shotgunpanel.harmony:
  action_mappings:
    PublishedFile:
    - actions: [publish_clipboard]
      filters: {}
    Task:
    - actions: [assign_task, task_to_ip]
      filters: {}
    Version:
    - actions: [quicktime_clipboard, sequence_clipboard, add_to_playlist, Download_Attachement_Version]
      filters: {}
    Note:
    - actions: [Download_Attachement_Note]
      filters: {}
  actions_hook: '{config}/tk-multi-shotgunpanel/shotgun_panel_actions.py'
  location: "@apps.tk-multi-shotgunpanel.location"

# blender
settings.tk-multi-shotgunpanel.blender:
  action_mappings:
    PublishedFile:
    - actions: [publish_clipboard]
      filters: {}
    Task:
    - actions: [assign_task, task_to_ip]
      filters: {}
    Version:
    - actions: [quicktime_clipboard, sequence_clipboard, add_to_playlist]
      filters: {}
  location: "@apps.tk-multi-shotgunpanel.location"

# fusion
settings.tk-multi-shotgunpanel.fusion:
  action_mappings:
    PublishedFile:
    - actions: [read_node]
      filters: {published_file_type: Alembic Cache}
    - actions: [read_node]
      filters: {published_file_type: Flame Render}
    - actions: [read_node]
      filters: {published_file_type: Flame Quicktime}
    - actions: [read_node]
      filters: {published_file_type: Image}
    - actions: [read_node]
      filters: {published_file_type: Movie}
    - actions: [read_node]
      filters: {published_file_type: Photoshop Image}
    - actions: [read_node]
      filters: {published_file_type: Rendered Image}
    - actions: [read_node]
      filters: {published_file_type: Texture}
    - actions: [publish_clipboard]
      filters: {}
    Task:
    - actions: [assign_task, task_to_ip]
      filters: {}
    Version:
    - actions: [quicktime_clipboard, sequence_clipboard, add_to_playlist, Download_Attachement_Version]
      filters: {}
    Note:
    - actions: [Download_Attachement_Note]
      filters: {}
  actions_hook: '{config}/tk-multi-shotgunpanel/shotgun_panel_actions.py'
  location: "@apps.tk-multi-shotgunpanel.location"


# krita
settings.tk-multi-shotgunpanel.krita:
  action_mappings:
    PublishedFile:
    - actions: [publish_clipboard]
      filters: {}
    Task:
    - actions: [assign_task, task_to_ip]
      filters: {}
    Version:
    - actions: [quicktime_clipboard, sequence_clipboard, add_to_playlist, Download_Attachement_Version]
      filters: {}
    Note:
    - actions: [Download_Attachement_Note]
      filters: {}
  actions_hook: '{config}/tk-multi-shotgunpanel/shotgun_panel_actions.py'
  location: "@apps.tk-multi-shotgunpanel.location"


# substancepainter
settings.tk-multi-shotgunpanel.substancepainter:
  action_mappings:
    PublishedFile:
    - actions: [publish_clipboard]
      filters: {}
    Task:
    - actions: [assign_task, task_to_ip]
      filters: {}
    Version:
    - actions: [quicktime_clipboard, sequence_clipboard, add_to_playlist, Download_Attachement_Version]
      filters: {}
    Note:
    - actions: [Download_Attachement_Note]
      filters: {}
  actions_hook: '{config}/tk-multi-shotgunpanel/shotgun_panel_actions.py'
  location: "@apps.tk-multi-shotgunpanel.location"


# premiere
settings.tk-multi-shotgunpanel.premiere:
  action_mappings:
    PublishedFile:
    - actions: [add_to_project, add_to_comp]
      filters: {published_file_type: Premiere Project}
    - actions: [add_to_project, add_to_comp]
      filters: {published_file_type: After Effects Project}
    - actions: [add_to_project, add_to_comp]
      filters: {published_file_type: Maya Scene}
    - actions: [add_to_project, add_to_comp]
      filters: {published_file_type: Photoshop Image}
    - actions: [add_to_project, add_to_comp]
      filters: {published_file_type: Rendered Image}
    - actions: [add_to_project, add_to_comp]
      filters: {published_file_type: Image}
    - actions: [add_to_project, add_to_comp]
      filters: {published_file_type: Texture}
    Task:
    - actions: [assign_task, task_to_ip]
      filters: {}
    Version:
    - actions: [quicktime_clipboard, sequence_clipboard, add_to_playlist, Download_Attachement_Version]
      filters: {}
    Note:
    - actions: [Download_Attachement_Note]
      filters: {}
  actions_hook: '{config}/tk-multi-shotgunpanel/shotgun_panel_actions.py'
  location: "@apps.tk-multi-shotgunpanel.location"
