This location is for core hook override files.

The Shotgun Pipeline Toolkit comes with a series of Core Hooks, essentially snippets of python
that contain key operations such as copying a file, creating directories etc.

These hooks are part of the platform distribution and are therefore bundled with the code itself.

If you want to provide your own hook implementations, for example control file system permissions
as files are being copied, you can override the core hooks by putting a file with the same
name as the core hook in this location. Sgtk will basically look in this folder first as it is
requesting a core hook - if it doesn't find the core hook here, it will look in its internal
platform location.
