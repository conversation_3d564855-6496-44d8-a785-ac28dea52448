########################################################################################
#
# Copyright (c) 2024 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
########################################################################################

"""
An app that creates a "PEG" node for the selected node found.
It takes the name of the selected node but removes all invalid characters for the
pipeline template, using lowerCamelCase to reformat the new node name, adding the
'-tracker' suffix.

It creates a boolean attribute in the new peg node called 'mtyShotTracker' set to true.
This allows us to collect specific peg nodes for the publisher. This attribute is
not exposed in the interface so it can be modified only via scripting.

"""

from tank import Hook
from tank.platform.qt import QtCore, QtGui

QMessageBox = QtGui.QMessageBox


class ProcessItemsHook(Hook):
    def execute(self, **kwargs):

        self.parent.engine.logger.info(
            "execute action create_tracker_peg_node start. {}".format("-" * 80)
        )

        result = self.create_tracker_peg_node()

        if not result:
            result = {"succes": [], "messages": [], "errors": []}

        self.parent.engine.logger.info(
            "execute action create_tracker_peg_node end. {}".format("-" * 80)
        )

        return result

    def create_tracker_peg_node(self):

        result = {"succes": [], "messages": [], "errors": []}

        # get settings previously set in tk-harmony.yml
        peg_node_color = self.parent.get_setting("peg_node_color")
        peg_attr_name = self.parent.get_setting("peg_attr_name")
        peg_attr_type = self.parent.get_setting("peg_attr_type")

        # convert values to strings in the right format
        peg_node_color_str = str(peg_node_color)[1:-1]

        create_write_nodes_cmd = """
include("harmony_utility_functions.js");

var log = MessageLog.trace;
var msg = {"error": null, "success": null};

function main() {
    var selected_nodes_num = selection.numberOfNodesSelected();
    log("number of selected nodes: " + selected_nodes_num);
    if (selected_nodes_num == "0" || selected_nodes_num == 0) {
        msg["error"] = "At least one peg node must be selected. Select at least one peg node and try again."
        // MessageBox.critical(msg["error"]);
        return msg;
    }
    var selected_nodes = selection.selectedNodes();

    tracker_peg_nodes = [];
    for (var i = 0; i < selected_nodes.length; i++) {
        var current_node = selected_nodes[i];
        if (node.type(current_node) != "PEG") {
            continue;
        }
        var tracker_peg_name = convert_to_lowerCamelCase(node.getName(current_node));
        tracker_peg_name = tracker_peg_name + "-tracker";
        log("Tracker peg node name: " + tracker_peg_name);

        var new_peg_node_color = new ColorRGBA(%s);
        new_peg_node = create_node("PEG", current_node, new_peg_node_color, tracker_peg_name, 100, 50, current_node);
        create_attr(new_peg_node, "%s", "%s");
        // copy also the pivot
        copy_node_pivot(current_node, new_peg_node);
        tracker_peg_nodes.push(new_peg_node);
        var enable_3d_attr = node.getAttr(new_peg_node, 1, "ENABLE_3D");
        enable_3d_attr.setValue(true);
    }

    selection.clearSelection();
    selection.addNodesToSelection(tracker_peg_nodes);

    if (tracker_peg_nodes.length == 0) {
        msg["error"] = "Couldn't find any peg in the selected nodes. Please select at least one peg node and try again."
        // MessageBox.warning(msg["error"]);
    } else {
        msg["success"] = tracker_peg_nodes.length + " tracker_peg nodes were created"
        // MessageBox.information(msg["success"]);
    }
    return msg;
}

main();

""" % (
    peg_node_color_str,
    peg_attr_name,
    peg_attr_type
)

        self.parent.engine.logger.debug(create_write_nodes_cmd)
        cmd_result = self.parent.engine.app.execute(create_write_nodes_cmd)

        self.parent.engine.logger.info(cmd_result)

        if cmd_result.get("success"):
            result["succes"] = [1]
            result["messages"].append(cmd_result.get("success"))
            self.show_message("Info", cmd_result.get("success"), icon="Information")
            return result
        elif cmd_result.get("error"):
            result["error"] = [1]
            result["messages"].append(cmd_result.get("error"))
            self.show_message("Error", cmd_result.get("error"), icon="Critical")
            return result

    def show_message(self, title, msg, icon=None):
        """
        Shows a message box with the input message
        """

        icons_dict = {
            "NoIcon": QMessageBox.NoIcon,
            "Question": QMessageBox.Question,
            "Information": QMessageBox.Information,
            "Warning": QMessageBox.Warning,
            "Critical": QMessageBox.Critical,
        }

        if not icon or icon not in icons_dict.keys():
            icon_selection = icons_dict["NoIcon"]
        else:
            icon_selection = icons_dict[icon]

        msg_error = QMessageBox()
        msg_error.setWindowTitle(title)
        msg_error.setText(msg)
        msg_error.setIcon(icon_selection)
        msg_error.raise_()
        msg_error.activateWindow()
        msg_error.exec_()
