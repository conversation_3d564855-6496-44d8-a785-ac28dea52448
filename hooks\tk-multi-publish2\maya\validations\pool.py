# -*- coding: utf-8 -*-
# Standard library:
import pprint
from functools import partial
#   .   .   .   .   .   .   .   .   .   .   .
# Third party:
import sgtk
import pymel.core as pm

#   .   .   .   .   .   .   .   .   .   .   .
# Project:
# ================================================================
pp = pprint.PrettyPrinter(indent=4).pprint
HookBaseClass = sgtk.get_hook_baseclass()


class Pool(HookBaseClass):

    def all_controllers_in_animControls_set_check(
            self, state, **kwargs
    ):
        dict_of_elements = \
            self.missing_controllers_in_animControls_set()

        list_of_errors = dict_of_elements["errors"]
        action_function = dict_of_elements["callback"]
        label = dict_of_elements["label"]

        self.composer.add(
            name="missing animControls in set",
            list_of_errors=dict_of_elements["errors"],
            state=state,
            action={
                "enabled": True,
                "type": "info",
                "label":
                    label,
                "message":
                    "This button will add the missing " +
                    "controllers to the animControllers set:",
                "tooltip":
                    "Click to add missing controllers to the set.",
                "callback":
                    lambda: dict_of_elements["callback"]
            }
        )

    #   ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___

    def no_audio_source_check(self, state, **kwargs):
        list_of_errors = self.audio_in_timeline()

        self.composer.add(
            name="No audio source found",
            list_of_errors=list_of_errors,
            state=state,
            action={
                "enabled": False
            }
        )

    def attributes_in_controllers_check(self, state, **kwargs):
        action_function = \
            self.select_controllers_without_attributes

        self.composer.add(
            name="Check fbControl-fbCurve attributes",
            list_of_errors=self.missing_controller_attributes(),
            state=state,
            action={
                "enabled": True,
                "type": "info",
                "label":
                    "Select problem CTL",
                "message":
                    "This button will select CTL " +
                    "controllers with missing fbControl " +
                    "or fbCurve attribute:",
                "tooltip":
                    "Click to select faulty CTLs.",
                "callback":
                    lambda: action_function
            }
        )

    #   ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___

    def mesh_cache_color_attribute_check(self, state, **kwargs):
        list_of_errors = self.missing_cache_color_in_meshes()
        action_function = self.select_missing_cache_color_nodes

        self.composer.add(
            name="Check cache color attributes",
            list_of_errors=list_of_errors,
            state=state,
            action={
                "enabled": False,
                "type": "info",
                "label":
                    "Select problem meshes",
                "message":
                    "This button will select meshes " +
                    "with missing cache color attribute:",
                "tooltip":
                    "Click to select faulty meshes.",
                "callback":
                    lambda: action_function
            }
        )

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def model_high_root_check(self, state, **kwargs):
        list_of_errors = self.check_model_high_root()

        self.composer.add(
            name="Check model_high_root",
            list_of_errors=list_of_errors,
            state=state,
            action={"enabled": False}
        )

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def rig_root_check(self, state, **kwargs):

        task_name = None
        if 'task' in kwargs:
            task_name = kwargs['task']

        if task_name not in ['BodyRig']:
            rig_root_name = 'rig_high_root'
        else:
            # Only for body rig
            rig_root_name = 'rig_low_root'

        check_name = "Check {}".format(rig_root_name)
        list_of_errors = self.check_rig_root_name(rig_root_name)

        self.composer.add(
            name=check_name,
            list_of_errors=list_of_errors,
            state=state,
            action={"enabled": False}
        )

    def non_blendshapes_textures(self, state, **kwargs):
        list_of_errors = self.check_blendshapes_textures()

        self.composer.add(
            name="Check blendShape textures",
            list_of_errors=list_of_errors,
            state=state,
            action={"enabled": False}
        )

    # ================================================================

    def no_shape_deformed_shapes_check(self, state, **kwargs):
        list_of_errors = self.list_of_shape_deformed_nodes()
        action_function = self.select_shape_deformed_nodes

        self.composer.add(
            name="Check shape deformed shapes",
            list_of_errors=list_of_errors,
            state=state,
            action={
                "enabled": True,
                "type": "info",
                "message":
                    "This button will select all " +
                    "ShapeDeformed nodes in the scene.",
                "tooltip": "",
                "label": "Select ShapeDeformed",
                "callback":
                    lambda: action_function
            }
        )

    # ================================================================

    def animation_publish_references_check(self, state, **kwargs):
        list_of_errors = \
            self.animation_publish_references(**kwargs)
        action_function = \
            partial(self.open_editor, "ReferenceEditor")

        self.composer.add(
            name="Invalid reference in the scene",
            list_of_errors=list_of_errors,
            state=state,
            action={
                "enabled": True,
                "type": "info",
                "message":
                    "This button will open the ReferenceEditor:",
                "tooltip": "",
                "label": "ReferenceEditor",
                "callback":
                    lambda: action_function
            }
        )

    # ================================================================

    def breakdown_assets_in_session_check(self, state, **kwargs):
        action_function = \
            partial(self.open_editor, "ReferenceEditor")

        self.composer.add(
            name="Missing references in the scene",
            list_of_errors=self.list_of_not_loaded_assets(**kwargs),
            state=state,
            action={
                "enabled": True,
                "type": "info",
                "message":
                    "This button will open the ReferenceEditor:",
                "tooltip":
                    "",
                "label":
                    "ReferenceEditor",
                "callback":
                    lambda: action_function
            }
        )

    def reference_edits_for_texture_check(self, state, **kwargs):
        self.composer.add(
            name="No reference edits for texture files",
            list_of_errors=self.no_reference_edits_for_texture_files(**kwargs),
            state=state,
            action={
                "enabled": True,
                "type": "info",
                "message":
                    "This button will open the ReferenceEditor:",
                "tooltip":
                    "",
                "label":
                    "Clear Edits",
                "callback":
                    lambda: self.clear_edits_for_textures
            }
        )

    #   ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___

    def no_camera_rig_for_animation(self, state, **kwargs):
        self.composer.add(
            name="No camera rig for Animation, use alembic.",
            list_of_errors=self.no_camera_rig_for_shot_animation(**kwargs),
            state=state,
            action={
                "enabled": True,
                "type": "info",
                "message":
                    "This button will open the ReferenceEditor:",
                "tooltip":
                    "",
                "label":
                    "ReferenceEditor",
                "callback":
                    lambda: partial(self.open_editor, "ReferenceEditor")
            }
        )

    #   ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___

    def no_locked_version_without_description(self, state, **kwargs):
        list_of_errors = \
            self.list_of_locked_versions_without_description()

        self.composer.add(
            name="No locked version without description",
            list_of_errors=list_of_errors,
            state=state,
            action={
                "enabled": False,
                "type": "warning"
            }
        )

    #   ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___ ___

    def shot_length_error_check(self, state, **kwargs):
        list_of_errors = \
            self.list_of_shots_duration_errors(**kwargs)
        action_function = \
            partial(self.open_editor, "SequenceEditor")

        self.composer.add(
            name="Shots matching CutItems frame range.",
            list_of_errors=list_of_errors,
            state=state,
            action={
                "enabled": True,
                "type": "info",
                "message":
                    "This button will open the camera sequencer:",
                "tooltip":
                    "",
                "label":
                    "CameraSequencer",
                "callback":
                    lambda: action_function
            }
        )

    # ================================================================

    def not_enabled_huds_check(self, state, **kwargs):
        action_function = kwargs["callback"]

        self.composer.add(
            name="HUDs not enabled",
            list_of_errors=self.list_of_not_enabled_huds(**kwargs),
            state=state,
            action={
                "enabled": True,
                "type": "info",
                "message":
                    kwargs["message"],
                "tooltip":
                    "",
                "label": "Layout Toolkit",
                "callback":
                    lambda: action_function
            }
        )

    # ================================================================

    def not_visible_huds_check(self, state, hud):
        list_of_errors = \
            self.not_visible_hud(hud=hud)
        action_function = \
            partial(self.set_hud_visible, hud)

        self.composer.add(
            name="{0} is not visible.".format(hud),
            list_of_errors=list_of_errors,
            state=state,
            action={
                "enabled": True,
                "type": "info",
                "message":
                    "Set {0} visible:".format(hud),
                "tooltip":
                    "",
                "label":
                    "{0}".format(hud),
                "callback":
                    lambda: action_function
            }
        )

    # ================================================================

    def not_required_huds_check(self, state, list_of_required_huds):
        list_of_errors = \
            self.list_of_not_required_huds(list_of_required_huds)

        def turn_off_HUDs(list_of_huds):
            for hud in list_of_huds:
                pm.headsUpDisplay(hud, visible=False, edit=True)

        action_function = \
            partial(turn_off_HUDs, list_of_errors)

        self.composer.add(
            name="Not required HUDs in scene.",
            list_of_errors=list_of_errors,
            state=state,
            action={
                "enabled": True,
                "type": "error",
                "message":
                    "This button will turn off not required HUDs:",
                "tooltip": "",
                "label":
                    "HUDs off",
                "callback":
                    lambda: action_function
            }
        )

    # ================================================================

    def use_latest_published_references(self, state, **kwargs):
        app = kwargs["app"]
        list_of_errors = \
            self.list_of_not_updated_assets(app)
        action_function = kwargs["callback"]

        self.composer.add(
            name="Use references latest published files",
            list_of_errors=list_of_errors,
            state=state,
            action={
                "enabled": True,
                "type": "warning",
                "message":
                    "This button will open the Breakdown app:",
                "tooltip": "",
                "label": "Breakdown",
                "callback":
                    lambda: action_function
            }
        )

    # ================================================================

    def limit_transform_distance_from_origin(
            self, state, **kwargs
    ):
        list_of_transforms = \
            self.list_of_objects_too_far_from_origin()
        list_of_nodes = \
            [x.get("node") for x in list_of_transforms]
        list_of_errors = \
            [x.get("message") for x in list_of_transforms]

        action_function = \
            lambda: pm.select(list_of_nodes)

        self.composer.add(
            name="Out of limit transforms",
            list_of_errors=list_of_errors,
            state=state,
            action={
                "enabled": True,
                "type": "error",
                "message":
                    "This button will select objects out of limit.",
                "tooltip":
                    "",
                "label":
                    "Select",
                "callback":
                    lambda: action_function
            }
        )

    # ================================================================

    def mesh_reference_override(self, state):
        list_of_elements = \
            self.list_of_non_reference_mesh_display_overrides()

        list_of_errors = [x["name"] for x in list_of_elements]

        list_of_nodes = [y["node"] for y in list_of_elements]

        def enable_overrides():
            for node in list_of_nodes:
                node.overrideEnabled.set(True)
                node.overrideDisplayType.set(2)

        action_function = enable_overrides

        self.composer.add(
            name="Enable reference override in meshes.",
            list_of_errors=list_of_errors,
            state=state,
            action={
                "enabled": True,
                "type": "error",
                "message":
                    "This button will enable the override.",
                "tooltip":
                    "",
                "label":
                    "Enable Overrides",
                "callback":
                    lambda: action_function
            }
        )

    def fps_time_unit(self, state, **kwargs):

        list_of_errors = self.check_fps_time_unit(**kwargs)

        self.composer.add(
            name="Change framerate to proper project fps and adjust animation if it´s needed.",
            list_of_errors=list_of_errors,
            state=state,
            action={
                "enabled": False,
                "type": "error"
            }
        )

    def constraints_targeted_to_camera(self, state):

        list_of_errors = self.list_constraints_targeted_to_camera()

        self.composer.add(
            name="There are constraints targeted to camera. You can bake the animation.",
            list_of_errors=list_of_errors,
            state=state,
            action={
                "enabled": False,
                "type": "error"
            }
        )

    def geometry_non_referenced(self, state):

        list_of_errors = self.list_geometry_non_referenced()

        self.composer.add(
            name="There are geometries non referenced into animation scene.",
            list_of_errors=list_of_errors,
            state=state,
            action={
                "enabled": False,
                "type": "error"
            }
        )

    def script_nodes_jobs_vaccine(self, state):

        list_of_errors = self.list_scripts_nodes_jobs_vaccine()

        self.composer.add(
            name="There are scripts node & job type 'vaccine' for delete.",
            list_of_errors=list_of_errors,
            state=state,
            action={
                "enabled": False,
                "type": "warning"
            }
        )

    # ---------------------------------------------------------------------------

    def unsupported_reference_nodes(self, state):
        list_of_supported_names = [
            'model_high_root_fb', 'rig_high_root', 'Camera_GRP'
        ]
        list_of_supported_types = ['EnvLocation', 'EnvModule']
        list_of_supported_special_cases = ['model_high_root']

        def filter_function(node):
            # False will not add node to error list, True will add it.
            _node_name = node.stripNamespace()
            _result = True
            if _node_name in list_of_supported_names:
                _result = False

            if _node_name in list_of_supported_special_cases:
                _has_special_attr = node.hasAttr('assetType')
                _special_attr_value = (
                    node.assetType.get() if _has_special_attr else ''
                )
                _is_special_case = (
                    True if
                    _special_attr_value in list_of_supported_types
                    else False
                )

                _result = False if _is_special_case else True
                'You are not using a Asset reference contained inside a '
                ' \"rig_high_root\" named group node.\n'

            return _result

        list_of_errors = \
            self.list_unsupported_references_nodes(filter_function)

        self.composer.add(
            name="Unsupported reference nodes",
            list_of_errors=list_of_errors,
            state=state,
            action={
                "enabled": False,
                "type": "error",
            }
        )

    # ---------------------------------------------------------------------------

    def not_loaded_reference_nodes(self, state):
        list_of_errors = self.list_of_not_loaded_nodes()

        self.composer.add(
            name="There are reference nodes not loaded in your scene",
            list_of_errors=list_of_errors,
            state=state,
            action={
                "enabled": False,
                "type": "error",
            }
        )

    def unknown_reference_nodes(self, state):
        list_of_errors = self.list_of_unknown_reference_nodes()
        self.composer.add(
            name="There are unknown reference nodes in your scene",
            list_of_errors=list_of_errors,
            state=state,
            action={
                "enabled": False,
                "type": "error",
            }
        )

    # ---------------------------------------------------------------------------

    def _temporal_validation(self, state, **kwargs):
        list_of_errors = self._temporal_request_environment_for_episodes(
            app=kwargs['app'],
            list_of_episodes=kwargs['list_of_episodes'],
            list_of_environments=kwargs['list_of_environments'],
            list_of_steps=kwargs['list_of_steps']
        )

        self.composer.add(
            name='Temporal validation for EnvLocation',
            list_of_errors=list_of_errors,
            state=state,
            action={
                'enabled': False,
                'type': 'error'
            }
        )
