# -*- coding: utf-8 -*-
# Standard library:
import os
import sys
import json

#   .   .   .   .   .   .   .   .   .   .   .
# Third party:
import sgtk


HookBaseClass = sgtk.get_hook_baseclass()


class fbxCollector(HookBaseClass):
    def __init__(self, parent):
        super(fbxCollector, self).__init__(parent)

    @property
    def settings(self):
        """
        Dictionary defining the settings that this collector expects to receive
        through the settings parameter in the process_current_session and
        process_file methods.

        A dictionary on the following form::

            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }

        The type string should be one of the data types that toolkit accepts as
        part of its environment configuration.
        """

        # grab any base class settings
        collector_settings = super(fbxCollector, self).settings or {}

        # settings specific to this collector
        maya_fbx_settings = {
            "Work Template": {
                "type": "template",
                "default": None,
                "description": "Template path for artist work files. Should "
                "correspond to a template defined in "
                "templates.yml. If configured, is made available"
                "to publish plugins via the collected item's "
                "properties. ",
            },
            "Transform Node": {
                "type": "string",
                "default": "model_high_root",
                "description": "",
            },
            "Valid Steps": {
                "type": "list",
                "default": None,
                "description": "Valid steps for exporting fbx meshes",
            },
        }

        # update the base settings with these settings
        collector_settings.update(maya_fbx_settings)

        return collector_settings

    # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

    def process_current_session(self, settings, parent_item):
        self.parent.logger.info("fbx collect start".ljust(88, "-"))
        super(fbxCollector, self).process_current_session(settings, parent_item)
        if not parent_item.type_spec.endswith(".session"):
            item = next(
                (i for i in parent_item.descendants if i.type_spec.endswith(".session")),
                parent_item,
            )
        else:
            item = parent_item

        self.collect_meshes_for_fbx_export(settings, item)

    def collect_meshes_for_fbx_export(self, settings, parent_item):
        icon_path = os.path.join(
            self.disk_location, os.pardir, os.pardir, "icons", "mesh.png"
        )
        # self.parent.logger.info("icon_path: {}".format(icon_path))
        # self.parent.logger.info("self.disk_location: {}".format(self.disk_location))
        # self.parent.logger.info("os.pardir: {}".format(os.pardir))

        ctx = self.parent.context
        step_name = ctx.step["name"].lower
        task = ctx.task

        if self.ASSET["sg_asset_type"] != "Camera":
            fbx_item = parent_item.create_item(
                "maya.session.asset.fbx",
                "FBX Asset  in session",
                "FBX Node",
            )

            self.parent.engine.execute_hook_expression(
                "{config}/tk-multi-publish2/maya/hook_pre_publish_farm_output.py",
                "use_farm_or_local_processing",
                item = fbx_item,
            )

            fbx_item.set_icon_from_path(icon_path)

            # load overrides framework -------------------------------------------------
            overrides_framework = self.parent.engine.custom_frameworks.get(
                "mty-framework-valueoverrides"
            ) or self.load_framework("mty-framework-valueoverrides")

            # load valid steps for exporting fbx meshes from overrides or from settings
            default_value_fbx_export_steps = "mty.publisher.maya.fbx_export_steps"

            valid_steps = overrides_framework.get_value(
                default_value_fbx_export_steps, link=task
            )
            self.parent.logger.info(f"fbx_export_steps: {valid_steps}")
            if valid_steps:
                valid_steps = json.loads(valid_steps)
            else:
                valid_steps = settings.get("Valid Steps").value or ["model"]

            # load valid steps for exporting fbx meshes from overrides or from settings
            default_value_transform_node_name = (
                "mty.publisher.maya.3d_mesh_transform_node_name"
            )

            transform_node = overrides_framework.get_value(
                default_value_transform_node_name, link=task
            )
            if transform_node:
                self.parent.logger.info(
                    f"transform_node_name from overrides: {default_value_transform_node_name}"
                )
            else:
                transform_node = settings.get("Transform Node").value or "model_high_root"
                self.parent.logger.info(
                    "Transform Node Name set to fall back 'model_high_root'"
                )

            if step_name in valid_steps:
                fbx_item.properties["model"] = transform_node
                self.logger.debug(
                    "Collected high_roots: {0}".format(
                        str(fbx_item.properties["model"])
                    )
                )
