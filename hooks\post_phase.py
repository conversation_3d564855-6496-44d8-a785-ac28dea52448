########################################################################################
#
# Copyright (c) 2023 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
########################################################################################


import os
import sys
import json
import pprint

import sgtk


HookBaseClass = sgtk.get_hook_baseclass()
pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)
pf = pprint.pformat


class PostPhaseHook(HookBaseClass):
    @property
    def name(self):
        return "PostPhaseHook"

    def post_finalize(self, publish_tree):
        self.parent.logger.info("post_finalize_start".ljust(120, "-"))
        self.parent.logger.info("{0}.post_finalize".format(self.name))

        if not hasattr(self, "metasync"):
            self.parent.logger.info("Loading metasync")
            self.metasync = self.load_framework("mty-framework-metasync")
            self.parent.logger.info("Finished loading metasync")

        # we also identify the primary item
        primary_item = None
        primary_publish_data = None

        # loop over all items and try and extract out the sg_publish_data if we can.
        # TODO: check that publishes with the same path have same id
        published_files = {}

        self.parent.logger.info(
            "About to iterate through the publish tree... ".ljust(88, "-")
        )
        for item in publish_tree:
            publish_data = self.get_published_file_data(item)
            self.parent.logger.info(
                "- item ({0}), publish_data: {1}".format(item, publish_data)
            )
            if publish_data:
                published_files[publish_data["id"]] = publish_data

            if not primary_item:
                primary_item = item
                primary_publish_data = publish_data

            for child_item in item.descendants:
                child_publish_data = self.get_published_file_data(child_item)
                self.parent.logger.info(
                    "   - child item ({0}), publish_data: {1}".format(
                        child_item, child_publish_data
                    )
                )
                if child_publish_data:
                    published_files[child_publish_data["id"]] = child_publish_data
        self.parent.logger.info(
            "Ended iterating through the publish tree.".ljust(88, "-")
        )

        # Now update the primary publish to be maked as primary
        self.parent.logger.info("primary_item: {0}".format(primary_item))
        self.parent.logger.info(
            "primary_publish_data: {0}".format(primary_publish_data)
        )

        self.parent.logger.info("About to update primary publish.")
        if primary_publish_data:
            self.parent.shotgun.update(
                "PublishedFile",
                primary_publish_data["id"],
                {"sg_is_primary_output": True},
            )
        self.parent.logger.info("Finished updating primary publish.")

        root_item = publish_tree.root_item
        extra_publishes = root_item.properties.get("sg_publish_extra_data", [])
        self.parent.logger.info("Found {} extra publishes".format(len(extra_publishes)))

        for publish in extra_publishes:
            published_files[publish["id"]] = publish

        # get publishes codes for debugging only
        publishes_codes = [publish["code"] for publish in published_files.values()]

        self.parent.logger.info(
            "Uploading {} published files".format(len(published_files))
        )
        message = "Uploading the following published files:\n{}"
        self.parent.logger.info(message.format(pf(publishes_codes)))

        self.parent.logger.info("About to create SyncLogs...")
        # Important logging for ui
        self.logger.info("About to create SyncLogs...")

        publishes = published_files.values()

        self.parent.logger.info("About to process SyncLogs...")
        process_synklogs = self.metasync.syncLogsManager.process_synklogs_for_publishes
        synclogs = process_synklogs(publishes, self.metasync.hostLocation)

        self.parent.logger.info("Finished creating SyncLogs...")
        # Important logging for ui
        self.logger.info("Finished creating SyncLogs...")

        if synclogs:
            # Queue Manager
            self.parent.logger.info("Injecting queue manager in pythonpath")
            if "mty-multi-queue" in self.parent.engine.apps:
                app = self.parent.engine.apps["mty-multi-queue"]
                queueclient_path = os.path.join(app.disk_location, "python")
                self.parent.logger.info("queueclient_path: %s" % queueclient_path)
                sys.path.append(queueclient_path)

            self.parent.logger.info("Importing Client from queue manager")
            from tk_multi_queueclient.queue_client import Client

            # self.parent.logger.info("Initializing Client")
            # client = Client()

            # load overrides framework -------------------------------------------------
            overrides_framework = (
                self.parent.engine.custom_frameworks.get("mty-framework-valueoverrides")
                or self.load_framework("mty-framework-valueoverrides")
                or None
            )

            # QM upload colors setup ---------------------------------------------------
            qm_upload_colors = {}

            if overrides_framework:
                default_value = "mty.multiqueue.job_colors"
                link = self.parent.context.task or self.parent.context.project or {}
                override_job_colors = overrides_framework.get_value(
                    default_value, link=link
                )
                if override_job_colors:
                    qm_upload_colors = json.loads(override_job_colors)
                    self.parent.logger.info("QM upload colors obtained from overrides")

            if not qm_upload_colors:
                qm_upload_colors = {
                    # Photoshop
                    "Photoshop Image": [64, 106, 136],
                    "Photoshop Proxy Image": [64, 106, 136],
                    "Photoshop Layer Group Image": [106, 139, 163],
                    "Photoshop Layer Group Proxy Image": [106, 139, 163],
                    # Harmony
                    "Toon Boom Harmony Project File": [136, 64, 116],
                    "Harmony TPL": [163, 106, 147],
                    "Harmony Shot Camera": [163, 106, 147],
                    # After Effects
                    "After Effects Project": [93, 0, 154],
                    # Maya
                    "Maya Scene": [0, 126, 82],
                    "Maya Shot anm Alembic": [0, 154, 114],
                    "Maya Shot lyt Alembic": [0, 154, 114],
                    "Maya Shot Geo Alembic": [0, 154, 114],
                    "Maya Shot Global Locators Alembic": [0, 154, 114],
                    "Alembic Cache": [0, 154, 114],
                    "Shot Camera Alembic": [50, 20, 140],
                    "GPU Cache": [84, 182, 149],
                    "Shot Environment Overrides": [219, 168, 105],
                    "Structure Abstraction": [219, 168, 105],
                    "Maya Shot RigsOnTheFly": [219, 168, 105],
                    "Maya Review": [169, 241, 254],
                    "Maya TopView Review": [169, 241, 254],
                    # Krita
                    "Krita Document": [134, 56, 178],
                    "Krita Layer": [179, 105, 219],
                    # General
                    "Rendered Image": [105, 212, 226],
                    "Media Review": [169, 241, 254],
                    "Image Sequence Review": [105, 212, 226],
                    "Sequence Review": [105, 212, 226],
                }
                self.parent.logger.info(
                    "QM upload colors obtained from hardcoded values in the config"
                )

            self.parent.logger.info("Creating commands for queue manager")
            # Now this functionallity will be done by the queue manager
            commands_ls = ["execute_hook_expression("]
            commands_ls.append('    "{config}/upload_publish.py",')
            commands_ls.append('    "execute", logger=logger,')
            commands_ls.append("    set_progress=set_progress,")
            commands_ls.append("    synclog=%s")
            commands_ls.append(")")
            commands = "\n".join(commands_ls)
            self.parent.logger.info("commands:\n{}".format(commands))

            self.parent.logger.info("About to iterate through synclogs list")
            for synclog in synclogs:
                txt_str = "Sync log:\n{}".format(str(synclog))
                self.parent.logger.info(txt_str)
                published_file_type = (
                    synclog.get("sg_publishedfile", {})
                    .get("published_file_type", {})
                    .get("name", "")
                )
                if published_file_type in qm_upload_colors.keys():
                    job_color = qm_upload_colors[published_file_type]
                else:
                    job_color = [254, 189, 169]

                # define project name for queue manager jobs. It must be
                # in the form: {project_id}_{project_name}
                project_name = "{}_{}".format(
                    self.parent.context.project.get("id", "000"),
                    self.parent.context.project.get("name", "generic") or "generic"
                )
                self.parent.logger.info(
                    "queue manager jobs project_name: {}".format(project_name)
                )

                job_data = {
                    "name": "Upload_%s" % synclog["sg_publishedfile"]["code"],
                    "commands": commands % synclog,
                    "priority": 50,
                    "type": "Upload",
                    "color": job_color,
                    "project": project_name,
                }
                self.parent.logger.debug("job_data:\n{}".format(pf(job_data)))
                self.parent.logger.info("Initializing Client")
                client = Client()
                published_file_code = synclog.get(
                    "sg_publishedfile", {}).get("code", ""
                )
                msg = "Sending {} to QM".format(published_file_code)
                self.logger.info(msg)
                self.parent.logger.info(msg)
                self.parent.logger.info("About to submit queue manager job")
                response = client.submit_job(job_data)
                self.parent.logger.debug(
                    "submit_job response:\n{}".format(pf(response))
                )
                self.parent.logger.info("Queue manager job submitted")
                # self.metasync.transfersManager.process_upload(synclog, self.logger)

            msg = (
                "Remember to open and start the queue manager to complete "
                "the upload process."
            )
            msg_2 = (
                "\n\nPlease make sure you have ONLY ONE queue "
                "manager opened at a time, otherwise the publish might not complete "
                "as expected."
            )
            self.logger.warning(msg)
            try:
                self.show_message(msg + msg_2, "Warning")
            except:
                pass

        self.parent.logger.info("{0}.post_finalize".format(self.name))
        self.parent.logger.info("post_finalize_end".ljust(120, "-"))

    def get_published_file_data(self, item):
        if hasattr(item.properties, "sg_publish_data"):
            return item.properties.sg_publish_data
        return None

    def show_message(self, msg, icon=None):
        """
        Displays a message box with a specified message and icon.

        This method creates a QMessageBox to display a message to the user.
        The appearance of the message box can be customized using the 'icon' parameter.

        Args:
            msg (str): The message to be displayed in the message box.
            icon (str, optional): The icon to be displayed in the message box.
                Valid options are "NoIcon", "Question", "Information", "Warning", and "Critical".
                Defaults to "NoIcon" if not provided or invalid.
        """

        # first we need to check if the current engine has ui and qt, otherwise
        # we can't show the message box

        if not self.parent.engine.has_ui:
            msg = (
                (
                    "The current engine '{}' does not have a UI. "
                    "Skipping message box."
                ).format(self.parent.engine.name)
            )
            self.parent.engine.logger.warning(msg)
            return

        from sgtk.platform.qt import QtCore, QtGui

        icons_dict = {
            "NoIcon": QtGui.QMessageBox.NoIcon,
            "Question": QtGui.QMessageBox.Question,
            "Information": QtGui.QMessageBox.Information,
            "Warning": QtGui.QMessageBox.Warning,
            "Critical": QtGui.QMessageBox.Critical,
        }

        if not icon or icon not in icons_dict.keys():
            icon_selection = icons_dict["NoIcon"]
        else:
            icon_selection = icons_dict[icon]

        msg_error = QtGui.QMessageBox()
        msg_error.setWindowTitle("Finished execution.")
        msg_error.setText(msg)
        msg_error.setIcon(icon_selection)
        msg_error.raise_()
        msg_error.activateWindow()
        msg_error.exec_()
