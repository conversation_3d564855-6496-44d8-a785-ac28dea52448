################################################################################
#
# Copyright (c) 2021 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
################################################################################

import re
import os
import shutil

import sgtk

from pprint import pformat

HookBaseClass = sgtk.get_hook_baseclass()


class IngestPlugin(HookBaseClass):
    """
    Base class plugin for ingesting a file an its dependencies to Shotgun.
    This plugin is typically configured to act upon files that are defined
    from a client manifest and those need to be downloaded, unpacked and publish
    It needs to be extended for each particualr case but it contains standard
    operations for validating and registering publishes with Shotgun.
    """

    ############################################################################
    # requiered custpm ingest plugin properties

    @property
    def settings_schema(self):
        """
        Dictionary defining the settings that this plugin expects to recieve
        through the settings parameter in the accept, validate, publish and
        finalize methods.
        A dictionary on the following form:
            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }
        The type string should be one of the data types that toolkit accepts
        as part of its environment configuration.
        """
        settings = {
            "supported_entity_identifiers": {
                "type": "list",
                "default": [],
                "description": "random",
            },
            'supported_entity_types': {
                'type': 'dict',
                'default': {},
                'description': '',
            },
            'supported_task_names': {
                'type': 'dict',
                'default': {},
                'description': '',
            },
            'required_metadata_keys': {
                'type': 'list',
                'default': [],
                'description': '',
            },
            'published_file_templates': {
                'type': 'dict',
                'default': {},
                'description': '',
            },
            'published_file_types': {
                'type': 'dict',
                'default': {},
                'description': '',
            }
        }

        return settings

    ############################################################################
    # requiered custom ingest plugin methods

    def ingest(self, location, entity, manifest, files):
        """ This is a plugin that expects a single remote file
        """

        direct_publishes = []
        dependent_publishes = []

        self.logger.debug("Downloaded remote files:\n{0}".format(pformat(files)))

        ingest_mappings = self.parent.mappings['ingest']

        # Ensure entity and task exists
        studio_entity = self.ensure_studio_entity(manifest, location)
        studio_task = self.resolve_studio_task(manifest, studio_entity)

        # build context
        context = self.resolve_context_from_task(studio_task)

        # map publish template
        main_file_template = self.resolve_publish_file_template('Main File')

        # identify the main file
        main_file_path_key = ingest_mappings['main_remote_file_path_key']
        main_file_path = manifest[main_file_path_key]
        local_file_path = files[main_file_path]

        # resolve template fields and publish path
        template_fields = context.as_template_fields(
            main_file_template
        )
        name_field = "master"
        template_fields['name'] = name_field

        # resolve some basic arguments
        external_id = manifest['ids'].get(location['code'].lower())
        external_file_name = os.path.basename(main_file_path)
        publish_name = self.resolve_publish_name(
            name_field, context, main_file_template, template_fields
        )

        # map published file type
        published_file_type = self.resolve_publish_file_type('Main File')

        # first check if we didn't ingest this file already
        existing = self.publish_exists(
            external_id, studio_task, 
            publish_name, published_file_type
        )

        # And only process it if its still pending
        if not existing:

            # resolve the most apropiate version in the studio
            publish_version = self.resolve_template_next_version(
                main_file_template, template_fields,
                reference_path=local_file_path
            )
            template_fields['version'] = publish_version

            # copy file to publish area
            publish_file_path = main_file_template.apply_fields(template_fields)
            self.copy_filesystem_path(local_file_path, publish_file_path)
            
            # set some extra arguments
            dependencies = []
            description = 'Automated Ingestion from Client'

            # now lets publish it
            publish = self.ensure_publish_file(
                context, studio_task, publish_name, 
                publish_file_path, publish_version, 
                published_file_type, description,
                external_id, external_file_name,
                dependencies
            )
        else:
            publish = existing

        direct_publishes.append(publish)

        # return teh standarzed list of publishes from this ingest
        return direct_publishes, dependent_publishes