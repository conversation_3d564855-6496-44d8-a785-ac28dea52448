# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.
#

# Author: <PERSON>
# Contact: https://www.linkedin.com/in/diegogh/

################################################################################

includes:
- ../app_locations.yml
- ../engine_locations.yml
- ./tk-multi-breakdown.yml
- ./tk-multi-loader2.yml
- ./tk-multi-publish2.yml
- ./tk-multi-screeningroom.yml
- ./tk-multi-shotgunpanel.yml
- ./tk-multi-snapshot.yml
- ./tk-multi-workfiles2.yml
- ./color-spaces.yml
- ./mty-multi-batchloader.yml
- ./tk-multi-breakdown2.yml

################################################################################

# asset --------------------------------------------------------------------------------
settings.tk-harmony.asset:
  apps:
    tk-multi-about:
      location: '@apps.tk-multi-about.location'
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel.harmony'
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2.harmony.project'
    tk-multi-pythonconsole:
      location: '@apps.tk-multi-pythonconsole.location'
    mty-harmony-library:
      location: '@apps.mty-harmony-library.location'
    mty-multi-scenesetup:
      location: '@apps.mty-multi-scenesetup.location'
      colorspace_mappings: "@settings.color-spaces.harmony"
      No Resolution Validation Tasks:
        - Render

  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  location: '@engines.tk-harmony.location'

# asset_step ---------------------------------------------------------------------------
settings.tk-harmony.asset_step:
  apps:
    tk-multi-about:
      location: '@apps.tk-multi-about.location'
    tk-multi-loader2: '@settings.tk-multi-loader2.harmony'
    # tk-multi-breakdown: '@settings.tk-multi-breakdown.harmony'
    tk-multi-publish2: '@settings.tk-multi-publish2.harmony.asset_step'
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel.harmony'
    tk-multi-snapshot: '@settings.tk-multi-snapshot.harmony.asset_step'
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2.harmony.asset_step'
    tk-multi-pythonconsole:
      location: '@apps.tk-multi-pythonconsole.location'
    mty-multi-scenesetup:
      location: '@apps.mty-multi-scenesetup.location'
      colorspace_mappings: "@settings.color-spaces.harmony"
      No Resolution Validation Tasks:
        - Render
    # mty-harmony-assettemplate:
    #   location: '@apps.mty-harmony-assettemplate.location'
    mty-harmony-library:
      location: '@apps.mty-harmony-library.location'
    mty-harmony-video-importer:
      location: '@apps.mty-harmony-video-importer.location'
      ffmpeg_backend_hook: '{config}/tk-harmony/custom_apps_hooks/import_video_ffmpeg_backend.py'

    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'

    mty-executeaction-create-output-passthrough-nodes:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Render Setup/1 Create output passthrough nodes..."
      action_hook: "{config}/tk-shotgun-executeaction/harmony/harmony_create_passthrough_nodes.py"
      sg_extended_fields: { }
      passthrough_node_color: [5, 250, 5, 255]
      passthrough_attr_name: "mtyPassthrough"
      passthrough_attr_type: "BOOL"
      allowed_entities: ["Asset", "Shot"]

    mty-executeaction-create-write-nodes:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Render Setup/2 Create write nodes..."
      action_hook: "{config}/tk-shotgun-executeaction/harmony/harmony_create_write_nodes.py"
      sg_extended_fields: { }
      passthrough_node_color: [5, 250, 5, 255]
      write_node_color: [250, 80, 5, 255]
      color_depth: 16
      passthrough_attr_name: "mtyPassthrough"
      passthrough_attr_type: "BOOL"
      write_attr_name: "mtyWriteExport"
      write_attr_type: "BOOL"
      allowed_entities: ["Asset", "Shot"]

    mty-executeaction-render_write_nodes:
      location: "@apps.tk-shotgun-executeaction.location"
      # when more than one execute action exists in this context, we need to remove the
      # starting 'SG Execute Actions' part in the display name, but if only one action
      # exists in this context, we need to add it to force that part to show in the menu
      # display_name: "General/Render write nodes..."
      # display_name: "SG Execute Actions/General/Render write nodes..."
      display_name: "General/Render write nodes..."
      action_hook: "{config}/tk-shotgun-executeaction/harmony/harmony_render_write_nodes.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset", "Shot" ]

    mty-executeaction-list_asset_versions:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "General/List asset versions..."
      action_hook: "{config}/tk-shotgun-executeaction/harmony/harmony_list_asset_versions.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset", "Shot" ]

    mty-executeaction-open_rendered_seq_in_rv:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "General/Open rendered sequence in RV..."
      action_hook: "{config}/tk-shotgun-executeaction/harmony/harmony_open_default_render_in_RV.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset", "Shot" ]

    mty-executeaction-set-line-thickness:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Rig/Set line Thickness on selected nodes"
      action_hook: "{config}/tk-shotgun-executeaction/harmony/harmony_line_thickness.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset", "Shot" ]

    mty-executeaction-connect-line-thickness-to-slider:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Rig/Connect line thickness to slider..."
      action_hook: "{config}/tk-shotgun-executeaction/harmony/harmony_control_line_thickness_with_slider.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset", "Shot" ]

    mty-executeaction-create-maya-batch_render-nodes:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Render Setup/Create maya batch render nodes..."
      action_hook: "{config}/tk-shotgun-executeaction/harmony/harmony_create_maya_batch_render_nodes.py"
      sg_extended_fields: { }
      renderer: "arnold"
      shading_tasks: ["sfcShading"]
      allowed_entities: ["Asset", "Shot"]

    mty-executeaction-reorder_nodes_in_z:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "General/Reorder nodes in Z..."
      action_hook: "{config}/tk-shotgun-executeaction/harmony/harmony_reorder_peg_nodes_in_Z.py"
      sg_extended_fields: { }
      allowed_entities: ["Asset", "Shot"]

    mty-executeaction-cleanup-frames:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "General/Cleanup frames folder..."
      action_hook: "{config}/tk-shotgun-executeaction/harmony/harmony_cleanup_frames_folder.py"
      sg_extended_fields: { }
      allowed_entities: ["Asset", "Shot"]

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "General/Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset" ]

  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-snapshot, name: Snapshot...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  - {app_instance: tk-multi-publish2, name: Publish...}
  template_project: asset_work_area_harmony
  location: '@engines.tk-harmony.location'

# project ------------------------------------------------------------------------------
settings.tk-harmony.project:
  compatibility_dialog_min_version: 21
  apps:
    tk-multi-about:
      location: '@apps.tk-multi-about.location'
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel.harmony'
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2.harmony.project'
    tk-multi-pythonconsole:
      location: '@apps.tk-multi-pythonconsole.location'
    mty-executeaction-ensure-folders:
      location: '@apps.tk-shotgun-executeaction.location'
      display_name: Ensure Tasks Folders
      action_hook: "{config}/tk-shotgun-executeaction/create_folders_for_assigned_tasks.py"
      sg_extended_fields: {}
      allowed_entities: [Shot, Sequence, Asset, Project]
    mty-harmony-library:
      location: '@apps.mty-harmony-library.location'
    mty-multi-scenesetup:
      location: '@apps.mty-multi-scenesetup.location'
      colorspace_mappings: "@settings.color-spaces.harmony"
      No Resolution Validation Tasks:
        - Render
    # mty-harmony-assettemplate:
    #   location: '@apps.mty-harmony-assettemplate.location'
  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  location: '@engines.tk-harmony.location'

# sequence -----------------------------------------------------------------------------
settings.tk-harmony.sequence:
  apps:
    tk-multi-about:
      location: '@apps.tk-multi-about.location'
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel'
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2.harmony.project'
    tk-multi-pythonconsole:
      location: '@apps.tk-multi-pythonconsole.location'
    mty-harmony-library:
      location: '@apps.mty-harmony-library.location'
    mty-multi-scenesetup:
      location: '@apps.mty-multi-scenesetup.location'
      colorspace_mappings: "@settings.color-spaces.harmony"
      No Resolution Validation Tasks:
        - Render

  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  location: '@engines.tk-harmony.location'

# sequence_step ------------------------------------------------------------------------
settings.tk-harmony.sequence_step:
  apps:
    tk-multi-about:
      location: '@apps.tk-multi-about.location'
    # tk-multi-breakdown: '@settings.tk-multi-breakdown.harmony'
    tk-multi-loader2: '@settings.tk-multi-loader2.harmony'
    tk-multi-publish2: '@settings.tk-multi-publish2.harmony.sequence_step'
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel.harmony'
    tk-multi-snapshot: '@settings.tk-multi-snapshot.harmony.sequence_step'
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2.harmony.sequence_step'
    tk-multi-pythonconsole:
      location: '@apps.tk-multi-pythonconsole.location'
    mty-multi-write-node:
      location: '@apps.mty-multi-write-node.location'
    mty-harmony-library:
      location: '@apps.mty-harmony-library.location'
    mty-multi-scenesetup:
      location: '@apps.mty-multi-scenesetup.location'
      colorspace_mappings: "@settings.color-spaces.harmony"
      No Resolution Validation Tasks:
        - Render

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "General/Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Sequence" ]

  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-snapshot, name: Snapshot...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  - {app_instance: tk-multi-publish2, name: Publish...}
  template_project: sequence_work_area_harmony
  location: '@engines.tk-harmony.location'

# shot ---------------------------------------------------------------------------------
settings.tk-harmony.shot:
  apps:
    tk-multi-about:
      location: '@apps.tk-multi-about.location'
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel'
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2.harmony.project'
    tk-multi-pythonconsole:
      location: '@apps.tk-multi-pythonconsole.location'
    mty-harmony-library:
      location: '@apps.mty-harmony-library.location'
    mty-multi-scenesetup:
      location: '@apps.mty-multi-scenesetup.location'
      colorspace_mappings: "@settings.color-spaces.harmony"
      No Resolution Validation Tasks:
        - Render

  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  location: '@engines.tk-harmony.location'

# shot_step ----------------------------------------------------------------------------
settings.tk-harmony.shot_step:
  apps:
    tk-multi-about:
      location: '@apps.tk-multi-about.location'
    # tk-multi-breakdown: '@settings.tk-multi-breakdown.harmony'
    # tk-multi-setframerange:
    #   location:
    #     version: v0.5.1
    #     type: app_store
    #     name: tk-multi-setframerange
      # hook_frame_operation: '{engine}/tk-multi-setframerange/frame_operations_tk-harmony.py'
    tk-multi-loader2: '@settings.tk-multi-loader2.harmony'
    tk-multi-publish2: '@settings.tk-multi-publish2.harmony.shot_step'
    tk-multi-screeningroom: '@settings.tk-multi-screeningroom.rv'
    tk-multi-shotgunpanel: '@settings.tk-multi-shotgunpanel.harmony'
    tk-multi-snapshot: '@settings.tk-multi-snapshot.harmony.shot_step'
    tk-multi-workfiles2: '@settings.tk-multi-workfiles2.harmony.shot_step'
    tk-multi-batchloader: "@settings.mty-multi-batchloader.harmony.shot_step"
    tk-multi-breakdown2: "@settings.tk-multi-breakdown2.harmony.shot_step"
    tk-multi-pythonconsole:
      location: '@apps.tk-multi-pythonconsole.location'
    mty-multi-scenesetup:
      location: '@apps.mty-multi-scenesetup.location'
      colorspace_mappings: "@settings.color-spaces.harmony"
      No Resolution Validation Tasks:
        - Render

    # mty-multi-write-node:
    #   location: '@apps.mty-multi-write-node.location'
    # mty-harmony-assettemplate:
    #   location: '@apps.mty-harmony-assettemplate.location'
    mty-harmony-library:
      location: '@apps.mty-harmony-library.location'
    mty-harmony-video-importer:
      location: '@apps.mty-harmony-video-importer.location'
      ffmpeg_backend_hook: '{config}/tk-harmony/custom_apps_hooks/import_video_ffmpeg_backend.py'

    mty-multi-scriptstoolbox:
      location: '@apps.mty-multi-scriptstoolbox.location'
      path_scripts_hook: '{config}/mty-multi-scriptstoolbox/get_path_for_scripts.py'

    mty-executeaction-create-output-passthrough-nodes:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Render Setup/1 Create output passthrough nodes..."
      action_hook: "{config}/tk-shotgun-executeaction/harmony/harmony_create_passthrough_nodes.py"
      sg_extended_fields: { }
      passthrough_node_color: [5, 250, 5, 255]
      passthrough_attr_name: "mtyPassthrough"
      passthrough_attr_type: "BOOL"
      allowed_entities: [ "Shot" ]

    mty-executeaction-create-write-nodes:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Render Setup/2 Create write nodes..."
      action_hook: "{config}/tk-shotgun-executeaction/harmony/harmony_create_write_nodes.py"
      sg_extended_fields: { }
      passthrough_node_color: [5, 250, 5, 255]
      write_node_color: [250, 80, 5, 255]
      color_depth: 16
      passthrough_attr_name: "mtyPassthrough"
      passthrough_attr_type: "BOOL"
      write_attr_name: "mtyWriteExport"
      write_attr_type: "BOOL"
      allowed_entities: [ "Shot" ]

    mty-executeaction-create-shot_camera-node:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Shot Assembly/1 Create shot camera node..."
      action_hook: "{config}/tk-shotgun-executeaction/harmony/harmony_create_shot_pipeline_camera_node.py"
      sg_extended_fields: { }
      camera_node_color: [5, 5, 255, 255]
      camera_attr_name: "mtyShotCamera"
      camera_attr_type: "BOOL"
      allowed_entities: [ "Shot" ]

    mty-executeaction-create-peg-nodes:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Animation/Create tracker peg node..."
      action_hook: "{config}/tk-shotgun-executeaction/harmony/harmony_create_tracker_peg_nodes.py"
      sg_extended_fields: { }
      peg_node_color: [96, 240, 255, 255]
      peg_attr_name: "mtyShotTracker"
      peg_attr_type: "BOOL"
      allowed_entities: [ "Shot" ]

    mty-executeaction-fix_image_scale:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "General/Fix image scale..."
      action_hook: "{config}/tk-shotgun-executeaction/harmony/harmony_fix_image_scale.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset", "Shot" ]

    mty-executeaction-render_write_nodes:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "General/Render write nodes..."
      action_hook: "{config}/tk-shotgun-executeaction/harmony/harmony_render_write_nodes.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset", "Shot" ]

    mty-executeaction-list_asset_versions:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "General/List asset versions..."
      action_hook: "{config}/tk-shotgun-executeaction/harmony/harmony_list_asset_versions.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset", "Shot" ]

    mty-executeaction-open_rendered_seq_in_rv:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "General/Open rendered sequence in RV..."
      action_hook: "{config}/tk-shotgun-executeaction/harmony/harmony_open_default_render_in_RV.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset", "Shot" ]

    mty-executeaction-connect_tones_tpl_nodes:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Tones/Connect tones tpl nodes..."
      action_hook: "{config}/tk-shotgun-executeaction/harmony/harmony_connect_tones_setup_nodes.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset", "Shot" ]

    mty-executeaction-reconnect_existing_tones_setup:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Tones/Reconnect existing tones setup..."
      action_hook: "{config}/tk-shotgun-executeaction/harmony/harmony_reconnect_existing_tones_setup.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset", "Shot" ]

    mty-executeaction-set-line-thickness:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Rig/Set line Thickness on selected nodes"
      action_hook: "{config}/tk-shotgun-executeaction/harmony/harmony_line_thickness.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset", "Shot" ]

    mty-executeaction-connect-line-thickness-to-slider:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Rig/Connect line thickness to slider..."
      action_hook: "{config}/tk-shotgun-executeaction/harmony/harmony_control_line_thickness_with_slider.py"
      sg_extended_fields: { }
      allowed_entities: [ "Asset", "Shot" ]

    # mty-executeaction-color-override:
    #   location: "@apps.tk-shotgun-executeaction.location"
    #   display_name: "BNA2/Add Color Override"
    #   action_hook: "{config}/tk-shotgun-executeaction/harmony/harmony_add_tpl_color_override.py"
    #   sg_extended_fields: { }
    #   allowed_entities: [ "Asset", "Shot" ]

    mty-executeaction-show-rendered-outputs:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Render Setup/Show Rendered Outputs"
      action_hook: "{config}/tk-shotgun-executeaction/show_rendered_outputs.py"
      sg_extended_fields: { }
      allowed_entities: ["Shot"]

    mty-executeaction-create-maya-batch_render-nodes:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Render Setup/Create maya batch render nodes..."
      action_hook: "{config}/tk-shotgun-executeaction/harmony/harmony_create_maya_batch_render_nodes.py"
      sg_extended_fields: { }
      renderer: "arnold"
      shading_tasks: ["sfcShading"]
      allowed_entities: ["Asset", "Shot"]

    mty-executeaction-reorder_nodes_in_z:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "General/Reorder nodes in Z..."
      action_hook: "{config}/tk-shotgun-executeaction/harmony/harmony_reorder_peg_nodes_in_Z.py"
      sg_extended_fields: { }
      allowed_entities: ["Asset", "Shot"]

    mty-executeaction-replace_proxies_with_hires:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "Render Setup/Replace proxies with hires..."
      action_hook: "{config}/tk-shotgun-executeaction/harmony/harmony_replace_proxies_with_hires.py"
      sg_extended_fields: { }
      allowed_entities: ["Asset", "Shot"]

    mty-executeaction-cleanup-frames:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "General/Cleanup frames folder..."
      action_hook: "{config}/tk-shotgun-executeaction/harmony/harmony_cleanup_frames_folder.py"
      sg_extended_fields: { }
      allowed_entities: ["Asset", "Shot"]

    mty-executeaction-clear_overrides_cache:
      location: "@apps.tk-shotgun-executeaction.location"
      display_name: "General/Clear Overrides Cache..."
      action_hook: "{config}/tk-shotgun-executeaction/clear_overrides_cache.py"
      sg_extended_fields: { }
      allowed_entities: [ "Shot" ]

  menu_favourites:
  - {app_instance: tk-multi-workfiles2, name: File Open...}
  - {app_instance: tk-multi-snapshot, name: Snapshot...}
  - {app_instance: tk-multi-workfiles2, name: File Save...}
  - {app_instance: tk-multi-publish2, name: Publish...}
  template_project: shot_work_area_harmony
  location: '@engines.tk-harmony.location'
