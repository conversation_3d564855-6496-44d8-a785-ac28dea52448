# -*- coding: utf-8 -*-
# Standard library:
import os
import re
import time
import atexit
import pprint
import shutil
import tempfile
import traceback
#   .   .   .   .   .   .   .   .   .   .   .
# Third party:
import sgtk
import maya.cmds as cmds
import maya.mel as mel
import pymel.core as pm
from sgtk.platform.qt import QtCore

#   .   .   .   .   .   .   .   .   .   .   .
# Project:
# ================================================================
pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)
pf = pprint.pformat

HookBaseClass = sgtk.get_hook_baseclass()


class MayaMediaTopViewReviewPlugin(HookBaseClass):
    def __init__(self, parent):
        super(MayaMediaTopViewReviewPlugin, self).__init__(parent)

        engine = self.parent.engine

        # frameworks   . . . . . . . . . . . . . . . . . . . . . .

        self.coremayatools = self.load_framework("mty-framework-coremayatools")

        self.mayacapture = self.load_framework("mty-framework-mayacapture")


    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    @property
    def tags(self):
        return {
            'pose': 2750,
            'no-audio': 2751
        }

    @property
    def icon(self):

        return os.path.join(
            self.disk_location,
            os.pardir,
            os.pardir,
            "icons",
            "review.png"
        )

    @property
    def name(self):

        return "Publish TopView Media Review"

    @property
    def description(self):

        return """
        <p>This plugin publish a topview playblast from the current sequence.</p>

        """

    @property
    def settings(self):

        # inherit the settings from the base publish plugin
        plugin_settings = super(
            MayaMediaTopViewReviewPlugin, self).settings or {}

        maya_publish_settings = {
            "work_template": {
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "primary_publish_template": {
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },

            "media_review_publish_template": {
                "type": "template",
                "default": None,
                "description": "Template path for published Scene playblast. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "publish_type": {
                "type": "string",
                "default": "Maya Layout Playblast",
                "description": "The published file type to register.",
            },
            "shot_prefix": {
                "type": "string",
                "default": "Shot_",
                "description": "The string or dynamic prefix to use to validate shot naming conventions."
                               "It can be a plain string or a tokenized one, which use a system of keys,"
                               "Those keys match items from the context, supported tokens are as follow:"
                               "{context.project} which matchs: context.project['name']"
                               "{context.entity} which matchs: context.entity['name']"
                               "{context.step} which matchs: context.step['name']"
                               "{context.task} which matchs: context.task['name']",
            },
            "shot_digits": {
                "type": "int",
                "default": 3,
                "description": "The valid amount of digits in the shot name after the shot prefix.",
            },
            'framerate': {
                'type': 'int',
                'default': 24.0,
                'description': 'Framerate to based operations on'
            },
            'sequence_start_frame': {
                'type': 'int',
                'default': 1001,
                'description': (
                    'Sequences often start at frame 1000 or 1001 in order to '
                    'have room previosuly for simulation or just to keep the '
                    'numbering with the same digit count and not have to pad '
                    'with 0s at the beginning'
                )
            },
            'clip_publish_template': {
                'type': 'template',
                'default': 'maya_sequence_clip_publish',
                'description': (
                    'Template for saving individual shot clips at '
                    'sequence level'
                )
            },
            'plugin_hooks': {
                'type': 'dict',
                'default': None,
                'description': 'Utility hooks for extra work'
            }

        }

        plugin_settings.update(maya_publish_settings)

        return plugin_settings


    @property
    def item_filters(self):
        return ["maya.session.sceneTopviewPlayblast"]

    def accept(self, settings, item):
        result = {"accepted": False, "checked": False}

        # change result depending on the task
        invalid_tasks = ["Polish"]
        task_name = self.parent.context.task.get("name")

        if task_name in invalid_tasks:
            result["checked"] = False
        else:
            result["checked"] = True
            # if taks name is not invalid, also force accepted status to True
            result["accepted"] = True

        # change result depending on the existance of a TopCam in the scene
        topCam_shape = self.get_sequence_top_camera()

        if not topCam_shape:
            if task_name in invalid_tasks:
                result["accepted"] = False
            else:
                result["accepted"] = True

        elif topCam_shape:
            result["accepted"] = True
            if task_name in invalid_tasks:
                result["checked"] = False
            else:
                result["checked"] = True

        return result

    # --------------------------------------------------------------------------

    def validate(self, settings, item):

        custom_render_settings, seq_data  = self.sequence_info(settings)

        if not self.has_tag(self.tags.get("no-audio"), seq_data.get("tags", [])):
            scene_audio_dict = self.get_scene_audio(seq_data)
            audio_node = scene_audio_dict.get("name")
            if not audio_node:
                msg = (
                    "There is no audio in the scene: "
                    "To fix this create a new audio node using the SG Loader app."
                )
                raise Exception(msg)

            item.properties["audio_node"] = scene_audio_dict.get("name")
            item.properties["audio_path"] = scene_audio_dict.get("path")
            item.properties["audio_offset"] = scene_audio_dict.get("offset")
            item.properties["audio_offset_in_secs"] = scene_audio_dict.get("offset_in_secs")

        topCam_shape = self.get_sequence_top_camera()
        if not topCam_shape:
            msg = (
                "There is no TopCam in the scene: "
                "To fix this create a new one from the SG menu."
            )
            raise Exception(msg)

        item.properties["camera"] = topCam_shape

        seq_start_frame = seq_data.get("seq_start_frame")
        seq_end_frame = seq_data.get("seq_end_frame")

        if not seq_start_frame or not seq_end_frame:
            msg = (
                "Could not get sequence start or end frame from the "
                "shots data: {}".format(seq_data)
            )
            raise Exception(msg)

        item.properties["start_frame"] = seq_start_frame
        item.properties["end_frame"] = seq_end_frame
        item.properties["custom_render_settings"] = custom_render_settings

        item.properties["seq_name"] = self.parent.context.entity['name']

        # Validate shot frame range
        frame_resut, sg_in, sg_out = self.validate_frame_range()
        if not frame_resut:
            msg = ("Scene frame range does not match sequence needed range: "
                   "{0} - {1}, to fix this, please set the right frame range "
                   "in Maya's main timeline (set start frame to {0} and set "
                   "end frame to {1}.")
            raise Exception(msg.format(sg_in, sg_out))

        return True

    # --------------------------------------------------------------------------

    def publish(self, settings, item):

        self.parent.log_debug(
            "----------------------Publishing TopView Media Review............")

        # create a pointer to the parent root item to store
        # any extra publishes apart form the primary one
        root_item = self.get_root_item(item)
        root_item.properties.setdefault('sg_publish_extra_data', [])

        self.parent.log_debug(
            'Storing extra publish data on root item: {}'.format(root_item))
        publish_extra_data = root_item.properties['sg_publish_extra_data']

        # Get the publish path from settings template
        work_template_name = settings["work_template"].value
        work_template = self.parent.engine.get_template_by_name(
            work_template_name)

        scene_path = os.path.abspath(cmds.file(query=True, sn=True))
        fields = work_template.get_fields(scene_path)
        fields["secondary_name"] = "TopCam"

        publish_version = fields["version"]
        publish_template_name = settings["media_review_publish_template"].value
        publish_template = self.parent.engine.get_template_by_name(
            publish_template_name)
        publish_path = publish_template.apply_fields(fields)

        scene_pub_template_name = settings["primary_publish_template"].value

        scene_pub_template = self.parent.engine.get_template_by_name(
            scene_pub_template_name)

        primary_publish_path = scene_pub_template.apply_fields(fields)

        # determine the publish name
        publish_name = self.parent.util.get_publish_name(publish_path)

        # create the image sequence playblast
        images_path = self.create_playblast(
            settings, item, publish_path, fields
        )

        dependencies = [primary_publish_path]

        # reference the path of the specific audio used fo the playblast
        audio_node = item.properties.get('audio_node', False)
        self.parent.log_info("item properties audio_node: {}".format(audio_node))
        if audio_node:
            audio_path = item.properties.get('audio_path', False)
            audio_path = self.fix_path(audio_path)
            dependencies.append(audio_path)

        sg_fields_to_update = {
            "sg_status_list": "rev",
            "task.Task.sg_status_list": "rev",
        }

        # register the publish:
        publish_data = {
            "tk": self.parent.engine.sgtk,
            "context": item.context,
            "comment": item.description,
            "path": publish_path,
            "name": publish_name,
            "version_number": publish_version,
            "thumbnail_path": item.get_thumbnail_as_path(),
            "task": self.parent.engine.context.task,
            "sg_fields": sg_fields_to_update,
            "published_file_type": settings["publish_type"].value,
            "dependency_paths": dependencies,
        }
        sg_publishes = sgtk.util.register_publish(**publish_data)

        self.parent.log_info(
            "Adding existing Camera publish " +
            "(id:{0}) ".format(sg_publishes['id']) +
            "as extra data for item: {0}".format(item)
        )

        publish_extra_data.append(sg_publishes)

        work_status = 'rev'

        self.parent.shotgun.update(
            "PublishedFile", sg_publishes["id"],
            {
                "sg_status_list": work_status
            }
        )

        sg_version = self.submit_version(
            images_path,
            publish_path,
            [sg_publishes],
            self.parent.engine.context.task,
            item.description,
            True,
            item.properties['start_frame'],
            item.properties['end_frame'],
            work_status
        )

        # delete the playblast folders
        self.remove_tmp_files(settings)

        # Upload in a new thread and make our own event loop
        # to wait for the thread to finish.

        event_loop = QtCore.QEventLoop()
        thread = UploaderThread(
            self.parent, sg_version,
            publish_path,
            item.get_thumbnail_as_path(),
            True
        )

        thread.finished.connect(event_loop.quit)
        thread.start()
        event_loop.exec_()

        # Update task status for revision
        sg_task = self.parent.context.task
        self.parent.log_info(
            "End of Publish and updating task with id: " + str(
                sg_task['id']) + " to status: " + str('rev')
        )

        try:
            self.parent.engine.shotgun.update(
                "Task", sg_task["id"], {"sg_status_list": 'rev'}
            )
        except:
            pass

        # log any errors generated in the thread
        for e in thread.get_errors():
            self.parent.log_error(e)


    # ==========================================================================

    def fix_path(self, path):
        path = path.replace('\\', '/')
        path = path.replace('\\\\', '/')

        return path

    def get_project_fps_from_SG(self):

        data = self.parent.engine.shotgun.find_one(
            entity_type="Project",
            filters=[["id", "is", self.parent.engine.context.project.get("id")]],
            fields=["sg_fps"]
        )
        project_fps = data.get("sg_fps")

        return project_fps

    def get_project_resolution_from_SG(self):
        hook_expression = "{config}/get_entity_resolution.py"
        project_resolution = self.parent.engine.execute_hook_expression(
            hook_expression,
            "get_resolution",
            engine=self.parent.engine,
        )
        if project_resolution:
            resolution_width = int(project_resolution.split("x")[0])
            resolution_height = int(project_resolution.split("x")[1])
        else:
            # Fallback to HD resolution
            resolution_width = 1920
            resolution_height = 1080

        return resolution_width, resolution_height


    def sequence_info(self, settings):
        """
        """

        engine = sgtk.platform.current_engine()
        entity = self.parent.context.entity

        sg_entity_type = entity.get("type")
        sg_filters = [
            ["id", "is", entity.get("id")],
            # ["id", "is", engine.context.project.get("id")]
        ]

        fields = [
            "code",
            "name",
            "tags",
            "sg_animation_exposition",
            "shots",
            "project.Project.sg_fps",
        ]

        data = self.parent.shotgun.find_one(sg_entity_type, sg_filters, fields)

        # print("entity data: {}".format(data))
        self.logger.debug(data)

        if not data:
            message = "Cant find a valid shotgun entity for: {}".format(entity)
            self.logger.error(message)
            raise Exception(message)

        shots_dict = self.get_shots_info()
        seq_start_frame, seq_end_frame = self.get_frame_range_from_shot_nodes(shots_dict)

        if not seq_start_frame or not seq_end_frame:
            message = "Cant find a valid frame range for: {}".format(entity)
            self.logger.error(message)
            raise Exception(message)

        data["seq_start_frame"] = seq_start_frame
        data["seq_end_frame"] = seq_end_frame

        custom_render_settings = self.collect_custom_render_settings(
            settings,
            data.get("tags")
        )

        return custom_render_settings, data


    def get_shots_info(self):
        shot_nodes = cmds.ls(type="shot", long=True)

        shots_dict = {}
        for node in shot_nodes:
            attr_exists = cmds.attributeQuery("shotName", node=node, exists=True)
            if attr_exists:
                shot_start_frame = cmds.getAttr("{}.startFrame".format(node))
                shot_end_frame = cmds.getAttr("{}.endFrame".format(node))

                shot_name = cmds.getAttr("{}.shotName".format(node))
                # check ifn shot is insert with regex. The first groups searches
                # for a pattern "e###_aaa###_####", the second group searches
                # for whatever is after the first patterm which means this shot
                # is an insert
                # name_match = re.match(r"(^e\d{3,}.\w*\d{3}.\d{4})(.*$)", shot_name)
                pattern = re.compile(
                    r"(?P<episode>[a-zA-Z]+([0-9]+)?)_"
                    r"(?P<seq>\d{3,})_"
                    r"(?P<shot>\d{4})"
                    r"(?P<insert>[a-zA-Z]+[0-9]+)?"
                )
                name_match = re.match(pattern, shot_name)
                # print(name_match.groups())
                if name_match:
                    insert_token = name_match.groupdict().get("insert", None)
                    if insert_token:
                        is_insert = True
                    else:
                        is_insert = False

                    shot_dict = {
                        "shot_name": shot_name,
                        "is_insert": is_insert,
                        "shot_node": node,
                        "shot_start_frame": shot_start_frame,
                        "shot_end_frame": shot_end_frame,
                    }

                    shots_dict[shot_name] = shot_dict

        return shots_dict


    def get_frame_range_from_shot_nodes(self, shots_dict):
        # ordered_shots = shots_dict.keys()
        # ordered_shots.sort()

        seq_start_frame = 1001
        seq_end_frame = 1001

        for shot in shots_dict.keys():
            shot_start_frame = shots_dict.get(shot, {}).get("shot_start_frame", None)
            if shot_start_frame <= seq_start_frame:
                seq_start_frame = shot_start_frame

            shot_end_frame = shots_dict.get(shot, {}).get("shot_end_frame", None)
            if shot_end_frame > seq_end_frame:
                seq_end_frame = shot_end_frame

        return seq_start_frame, seq_end_frame


    def validate_frame_range(self):
        # Set frame range for shot
        shots_dict = self.get_shots_info()
        seq_start_frame, seq_end_frame = self.get_frame_range_from_shot_nodes(shots_dict)

        current_in = cmds.playbackOptions(query=True, animationStartTime=True)
        current_out = cmds.playbackOptions(query=True, animationEndTime=True)

        if seq_start_frame != current_in or seq_end_frame != current_out:
            return False, seq_start_frame, seq_end_frame

        return True, seq_start_frame, seq_end_frame

    # --------------------------------------------------------------------------

    def has_tag(self, id_tag, entity_tags):
        entity_tag_ids = [tag['id'] for tag in entity_tags]
        if id_tag in entity_tag_ids:
            return True
        else:
            return False

    # --------------------------------------------------------------------------

    def get_sequence_top_camera(self):
        topCam_shape = None

        camera_shapes = cmds.ls(type="camera")
        for cam in camera_shapes:
            attr_exists = cmds.attributeQuery("assetType", node=cam, exists=True)
            if attr_exists and cmds.getAttr("{0}.assetType".format(cam)) == "TopCam":
                topCam_shape = cam
                break

        self.parent.log_info("topCam_shape: {}".format(topCam_shape))

        return topCam_shape

    # --------------------------------------------------------------------------

    def get_scene_audio(self, seq_dict):

        scene_audio = {}

        try:
            # get sound via ui controls
            aPlayBackSliderPython = mel.eval('$tmpVar=$gPlayBackSlider')
            audio_node = cmds.timeControl(aPlayBackSliderPython, query=True, sound=True)
            if not audio_node:
                raise Exception("Audio not found in timeline")
        except:
            # get sound via cmds
            all_audio_nodes = cmds.ls(type="audio", long=True)
            if len(all_audio_nodes) == 1:
                audio_node = all_audio_nodes[0]
            else:
                if not all_audio_nodes:
                    msg = (
                        "No audio nodes found in the scene, "
                        "please add an audio node to the scene using the loader app"
                    )
                    self.parent.engine.logger.error(msg)
                    raise RuntimeError(msg)
                else:
                    self.parent.engine.logger.warning(
                        "Found multiple audio nodes in the scene, using the first one"
                    )
                    audio_node = all_audio_nodes[0]

        self.parent.engine.logger.info('audio_node: {}'.format(audio_node))
        if audio_node and audio_node not in ['', None]:

            # Get fps from SG
            project_fps = seq_dict.get("project.Project.sg_fps")
            if not project_fps:
                self.parent.engine.logger.warning("Couldn't fps from sequence data")
                project_fps = self.get_project_fps_from_SG()

            seq_start_frame = seq_dict.get("seq_start_frame")
            audio_offset = cmds.getAttr("{}.offset".format(audio_node))
            self.parent.engine.logger.debug("audio_offset: {}".format(audio_offset))
            offset_value_frames = seq_start_frame - audio_offset
            offset_value_seconds = offset_value_frames / project_fps

            scene_audio["name"] = audio_node
            scene_audio["path"] = cmds.getAttr("{}.filename".format(audio_node))
            scene_audio["offset"] = offset_value_frames
            scene_audio["offset_in_secs"] = offset_value_seconds

        return scene_audio

    # --------------------------------------------------------------------------

    def build_render_properties(self, stt_map, tag_names):

        render_settings = {}
        for tag in stt_map:
            for tag_token in stt_map[tag]:
                if tag in tag_names:
                    render_settings[tag_token] = stt_map[tag][tag_token]

        return render_settings

    # --------------------------------------------------------------------------

    def collect_custom_render_settings(self, settings, sg_tag_list):
        """
        Collect the custom render setting required for the media review
        :param item_data: A shotgrid dictionary containing the list of tags
                          linked to an entity
        :return:

        A dictionary containing the names for render setting properties and values.
        """

        render_settings = {
            "v_opts": {},
            "v2_opts": {},
            "others": {}
        }

        if not sg_tag_list:
            return render_settings

        cfg_render_settings = settings.get("custom_render_settings").value

        list_of_tag_ids = [item["id"] for item in sg_tag_list]
        filters = [["id", "in", list_of_tag_ids]]
        fields = ["id", "name"]

        list_of_tag_entities = self.parent.shotgun.find("Tag", filters, fields)

        tag_names = [tag["name"] for tag in list_of_tag_entities]

        for setting_key in render_settings.keys():
            if setting_key in cfg_render_settings:
                render_settings[setting_key] = self.build_render_properties(
                    cfg_render_settings[setting_key], tag_names)

        self.parent.engine.logger.info(
            "collected Render Settings: {0}".format(render_settings)
        )
        return render_settings

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def execute_converter(self, ffmpegCore, convert_args, msg):
        _err, _info = ffmpegCore.execute_command(convert_args)
        if _err:
            raise Exception("{0}: \n{1}".format(msg, _err))

        return _info

    def convert_to_video(
            self,
            fps,
            source_file,
            dest_file,
            first_frame,
            last_frame,
            sound_offset=None,
            sound_path=None,
            ffmpeg_framework=None
    ):

        self.parent.log_info("Creting video file ....")

        image_sequence = "{}.%04d.jpg".format(source_file)

        ffmpeg_framework.ffmpegCore.set_binary("convert")

        # build all the command parts individually to later decide if a particular part
        # is needed or not
        # input section
        cmd_pt1 = "-framerate {3} -start_number {2} -i {0} "
        # video section
        cmd_pt2 = "-c:v libx264 -pix_fmt yuv420p -preset fast -crf 20 "
        # audio section
        cmd_pt3 = "-c:a aac "
        # output section
        cmd_pt4 = "-r {3} -y {4}"

        if sound_path:
            # add the audio input section to the input cmd part
            cmd_pt1 = cmd_pt1 + "-itsoffset {5} -i {1} "
            # ensure the shortest stream duration is used as the video duration
            cmd_pt4 = "-r {3} -shortest -y {4}"
            command = cmd_pt1 + cmd_pt2 + cmd_pt3 + cmd_pt4
        else:
            command = cmd_pt1 + cmd_pt2 + cmd_pt4

        # init audio offset regardless of whether we use sound or not in the command
        audio_offset = (first_frame - 1) / fps
        command = command.format(
            image_sequence,
            sound_path,
            first_frame,
            fps,
            dest_file,
            audio_offset
        )

        self.execute_converter(
            ffmpeg_framework.ffmpegCore,
            command,
            "Failed to compile publish video"
        )

        # Restore ffprobe to ffmpeg
        ffmpeg_framework.ffmpegCore.set_binary("convert")

    # --------------------------------------------------------------------------

    def create_playblast(self, settings, item, publish_path, sg_fields):
        # Loading FFMPEG framework
        ffmpeg = self.load_framework("mty-framework-ffmpeg")

        # General playblast data
        engine = sgtk.platform.current_engine()
        custom_render_settings, seq_data = self.sequence_info(settings)

        seq_start_frame = seq_data.get("seq_start_frame")
        seq_end_frame = seq_data.get("seq_end_frame")

        # Create frames list
        list_of_frames = []
        for i in range(int(seq_start_frame), int(seq_end_frame) + 1):
            current_frame = i
            list_of_frames.append(current_frame)

        animation_exposition = seq_data.get("sg_animation_exposition")
        if animation_exposition == None:
            animation_exposition = 1

        # Get fps from SG
        project_fps = seq_data.get("project.Project.sg_fps")
        if not project_fps:
            self.parent.engine.logger.warning("Couldn't fps from sequence data")
            project_fps = self.get_project_fps_from_SG()

        # The following lines shouldn't be necessary as the color management should be
        # enabled by default in maya (when setting up the OCIO env variable)
        # # Enable Color Management
        # color_management_enabled = cmds.colorManagementPrefs(q=True, cmEnabled=True)
        # if color_management_enabled is False:
        #     cmds.colorManagementPrefs(e=True, cmEnabled=True)

        format_ = "image"
        compression_ = "jpg"

        # Sound data
        sound_data = self.get_scene_audio(seq_data)
        sound_name = sound_data.get("name")
        sound_path = sound_data.get("path")
        sound_offset = sound_data.get("offset")

        try:
            cmds.setAttr(item.properties['camera'] + '.overscan', 1)
        except:
            pass

        try:
            cmds.setAttr(item.properties['camera'] + '.depthOfField', 0)
        except:
            pass

        playblast_folder = os.path.dirname(publish_path)

        if not os.path.exists(playblast_folder):
            os.makedirs(playblast_folder)

        playblast_filename, _ = os.path.splitext(publish_path)

        # get the user tmp directory
        tmp_root = tempfile.gettempdir()

        seq_name = engine.context.entity["name"]
        version = 'v{0}'.format(str(sg_fields.get('version')).zfill(3))

        playblast_file_name = os.path.basename(playblast_filename)
        tmp_dir = os.path.join(tmp_root, "playblast_images", seq_name, version)
        tmp_dir = self.fix_path(tmp_dir)

        tmp_playblast_root_dir = os.path.join(tmp_root, "playblast_images")
        tmp_playblast_root_dir = self.fix_path(tmp_playblast_root_dir)
        playblast_tmp_path = os.path.join(tmp_dir, playblast_file_name)
        playblast_tmp_path = self.fix_path(playblast_tmp_path)

        self.parent.engine.logger.info(
            "Playblast tmp root path: {}".format(tmp_playblast_root_dir)
        )
        self.parent.engine.logger.info(
            "Playblast tmp path: {}".format(playblast_tmp_path)
        )

        if not os.path.exists(tmp_dir):
            os.makedirs(tmp_dir)

        item_tmp_paths = {
            "tmp_dir": tmp_dir,
            "tmp_playblast_root_dir": tmp_playblast_root_dir
        }
        settings.update(item_tmp_paths)

        # set capture custom viewport settings
        view_opts = self.mayacapture.ViewportOptions.copy()
        view_opts['grid'] = False
        view_opts['polymeshes'] = True
        view_opts['displayAppearance'] = "smoothShaded"
        view_opts['nurbsCurves'] = False
        view_opts['locators'] = False
        view_opts['joints'] = False
        view_opts['pluginShapes'] = True
        view_opts['pluginObjects'] = ("gpuCacheDisplayFilter", True)
        view_opts['ikHandles'] = False
        view_opts['textures'] = True
        # Enable viewport2 AmbientOclusion
        view2_opts = self.mayacapture.Viewport2Options.copy()
        view2_opts['ssaoEnable'] = True
        view2_opts['maxHardwareLights'] = 8
        view2_opts['textureMaxResolution'] = 2048
        view2_opts['enableTextureMaxRes'] = False
        view2_opts["singleSidedLighting"] = False
        view2_opts["multiSampleEnable"] = False
        view2_opts["transparentShadow"] = True

        view_opts.update(item.properties["custom_render_settings"]["v_opts"])
        # Update values with custom shot render settings
        view2_opts.update(item.properties["custom_render_settings"]["v2_opts"])

        enable_two_sides = True
        if "two_side_lightning" in item.properties["custom_render_settings"]["others"]:
            enable_two_sides = item.properties["custom_render_settings"]["others"]["two_side_lightning"]

        # camera options
        cam_opts = self.mayacapture.CameraOptions.copy()
        cam_opts['displayResolution'] = True

        resolution_width, resolution_height = self.get_project_resolution_from_SG()

        # build the playblast config dictionary, which will be used to create the
        # playblast
        playblast_config = {
            'camera': item.properties["camera"],
            'width': resolution_width,
            'height': resolution_height,
            'filename': playblast_tmp_path,
            'frames': list_of_frames,
            'format': format_,
            'compression': compression_,
            'viewer': False,
            'overwrite': True,
            'viewport_options': view_opts,
            'viewport2_options': view2_opts,
            'camera_options': cam_opts,
            'two_sides': enable_two_sides,
            "off_screen": True,
        }

        # set the audio properly
        if not self.has_tag(self.tags.get("no-audio"), seq_data.get("tags", [])):
            audio = item.properties.get("audio_path")
            if audio:
                sound_name = audio
            else:
                sound_name = None
            playblast_config['sound'] = sound_name

        self.mayacapture.capture(**playblast_config)

        first_frame = item.properties['start_frame'] - item.properties['start_frame']
        last_frame = item.properties['end_frame'] - item.properties['start_frame']

        # print item properties for debugging
        self.parent.engine.logger.debug(
            "item properties: {}".format(pf(dict(item.properties)))
        )

        self.parent.engine.logger.info(
            "Sequence tags:\n{}".format(pf(seq_data.get("tags", [])))
        )

        has_no_audio_tag = self.has_tag(
            self.tags.get("no-audio"), seq_data.get("tags", [])
        )

        if (
            has_no_audio_tag
            or not sound_path
            or not os.path.exists(sound_path)
        ):
            # Export movie without audio
            self.parent.engine.logger.info(
                (
                    "Export movie without audio:\n"
                    "has_no_audio_tag: {}\n"
                    "sound_path: {}\n"
                    "sound_path exists locally: {}"
                ).format(has_no_audio_tag, sound_path, os.path.exists(sound_path))
            )
            self.convert_to_video(
                fps=project_fps,
                source_file=playblast_tmp_path,
                dest_file=publish_path,
                first_frame=first_frame,
                last_frame=last_frame,
                sound_offset=None,
                sound_path=None,
                ffmpeg_framework=ffmpeg
            )
        else:
            # Export movie with audio
            self.parent.engine.logger.info(
                (
                    "Export movie with audio:\n"
                    "has_no_audio_tag: {}\n"
                    "sound_path: {}\n"
                    "sound_path exists locally: {}"
                ).format(has_no_audio_tag, sound_path, os.path.exists(sound_path))
            )
            self.convert_to_video(
                fps=project_fps,
                source_file=playblast_tmp_path,
                dest_file=publish_path,
                first_frame=first_frame,
                last_frame=last_frame,
                sound_offset=sound_offset,
                sound_path=sound_path,
                ffmpeg_framework=ffmpeg
            )

        return publish_path

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def submit_version(self, path_to_frames, path_to_movie, sg_publishes,
                       sg_task, comment, store_on_disk, first_frame, last_frame,
                       work_status, override_entity=False):
        """
        Create a version in Shotgun for this path and linked to this publish.
        """

        # get current shotgun user
        current_user = self.parent.engine.context.user

        # create a name for the version based on the file name
        # grab the file name, strip off extension
        name = os.path.splitext(os.path.basename(path_to_movie))[0]
        # do some replacements
        name = name.replace("_", " ")
        # and capitalize
        name = name.capitalize()

        if override_entity != False:
            entity = override_entity
        else:
            entity = self.parent.engine.context.entity

        # Create the version in Shotgun
        data = {
            "code": name,
            "sg_status_list": work_status,
            "entity": entity,
            "sg_task": sg_task,
            "sg_first_frame": int(first_frame),
            "sg_last_frame": int(last_frame),
            "frame_count": int((last_frame - first_frame + 1)),
            "frame_range": "{0}-{1}".format(int(first_frame), int(last_frame)),
            "sg_frames_have_slate": False,
            "published_files": sg_publishes,
            "created_by": current_user,
            "description": comment,
            "sg_path_to_frames": path_to_frames,
            "sg_movie_has_slate": False,
            "project": self.parent.engine.context.project,
            "user": current_user,
            "sg_version_type": "Production",
        }

        if store_on_disk:
            data["sg_path_to_movie"] = path_to_movie

        sg_version = self.parent.engine.shotgun.create("Version", data)
        self.parent.log_debug("Created version in shotgun: {}".format(str(data)))

        return sg_version


    def remove_tmp_files(self, settings):
        self.parent.engine.logger.info("About to remove temp files...")

        temp_image_seq_dir = settings.get("tmp_dir")
        tmp_playblast_root_dir = settings.get("tmp_playblast_root_dir")

        self.parent.engine.logger.info(
            "temp_image_seq_dir: {}".format(temp_image_seq_dir)
        )
        self.parent.engine.logger.info(
            "tmp_playblast_root_dir: {}".format(tmp_playblast_root_dir)
        )

        # try to remove the specific scene image sequence dir
        if os.path.exists(temp_image_seq_dir):
            try:
                self.parent.engine.logger.info(
                    "Deleting scene playblast dir: {}".format(temp_image_seq_dir)
                )
                shutil.rmtree(temp_image_seq_dir)
                self.parent.engine.logger.info(
                    "Successfully deleted scene playblast dir"
                )
            except Exception as e:
                msg = "Couldn't delete path {}: {}, full traceback:\n{}".format(
                    temp_image_seq_dir, e, traceback.format_exc()
                )
                self.parent.engine.logger.error(msg)

        # try to delete the root playblasts dir
        if os.path.exists(tmp_playblast_root_dir):
            try:
                self.parent.engine.logger.info(
                    "Deleting playblasts root dir: {}".format(tmp_playblast_root_dir)
                )
                shutil.rmtree(tmp_playblast_root_dir)
                self.parent.engine.logger.info(
                    "Successfully deleted playblasts root dir"
                )
            except Exception as e:
                msg = "Couldn't delete path {}: {}, full traceback:\n{}".format(
                    tmp_playblast_root_dir, e, traceback.format_exc()
                )
                self.parent.engine.logger.error(msg)

    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        pass


class UploaderThread(QtCore.QThread):

    def __init__(self, app, version, path_to_movie, thumbnail_path,
                 upload_to_shotgun):
        QtCore.QThread.__init__(self)
        self._app = app
        self._version = version
        self._path_to_movie = path_to_movie
        self._thumbnail_path = thumbnail_path
        self._upload_to_shotgun = upload_to_shotgun
        self._errors = []

    def get_errors(self):
        """
        can be called after execution to retrieve a list of errors
        """
        return self._errors

    def run(self):
        """
        Thread loop
        """
        upload_error = False

        if self._upload_to_shotgun:
            try:
                self._app.tank.shotgun.upload(
                    "Version", self._version["id"], self._path_to_movie,
                    "sg_uploaded_movie")
            except Exception as e:
                self._errors.append("Movie upload to Shotgun failed: %s" % e)
                upload_error = True

        if not self._upload_to_shotgun or upload_error:
            try:
                self._app.tank.shotgun.upload_thumbnail(
                    "Version", self._version["id"], self._thumbnail_path)
            except Exception as e:
                self._errors.append(
                    "Thumbnail upload to Shotgun failed: %s" % e)
