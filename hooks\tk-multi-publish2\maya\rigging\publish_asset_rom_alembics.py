# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

import os
import sys
import sgtk
import maya.cmds as cmds
import maya.mel as mel
import math
import pymel.core as pm

import re
import traceback
import subprocess
from sgtk.platform.qt import QtGui, QtCore
from pprint import pprint as pprint
from pprint import pformat as pformat

HookBaseClass = sgtk.get_hook_baseclass()


class MayaAssetAlembicDynamicAttributesPublishPlugin(HookBaseClass):
    """
    Plugin for publish Scene playblast Publish

    """

    @property
    def tags(self):
        return {
            'New': 3216,
        }

    @property
    def icon(self):
        """
        Path to an png icon on disk
        """

        # look for icon one level up from this hook's folder in "icons" folder
        return os.path.join(
            self.disk_location,
            os.pardir,
            "icons",
            "publish_alembic.png"
        )

    @property
    def name(self):
        """
        One line display name describing the plugin
        """
        return "Publish Dyn-Atts alembic"

    @property
    def description(self):
        """
        Verbose, multi-line description of what the plugin does. This can
        contain simple html for formatting.
        """

        loader_url = "https://support.shotgunsoftware.com/hc/en-us/articles/219033078"

        return """
        <p>This plugin publish an alembic with dynamic attributes baked on every frame.</p>

        """

    @property
    def settings(self):
        """
        Dictionary defining the settings that this plugin expects to recieve
        through the settings parameter in the accept, validate, publish and
        finalize methods.

        A dictionary on the following form::

            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }

        The type string should be one of the data types that toolkit accepts
        as part of its environment configuration.
        """
        # inherit the settings from the base publish plugin
        plugin_settings = super(
            MayaAssetAlembicDynamicAttributesPublishPlugin, self).settings or {}

        # settings specific to this class
        maya_publish_settings = {
            "Work Template": {
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "Primary Publish Template": {
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "Publish Template": {
                "type": "template",
                "default": None,
                "description": "Template path for published Scene playblast. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "Publish type": {
                "type": "string",
                "default": "Maya Shot Geo Alembic",
                "description": "The published file type to register.",
            },
            "Rom Publish type": {
                "type": "string",
                "default":  "Animation Data",
                "description": "The published file type for the animation.",
            },
            "Allowed groups": {
                "type": "list",
                "default": ["model_high_root"],
                "description": "The valid secondary groups.",
            },
            "Allowed asset types": {
                "type": "list",
                "default": ["character"],
                "description": "Allowed asset type to export dyn attrs alembic.",
            },
            "Export mode": {
                "type": "string",
                "default": 'alembic',
                "description": "The alembic export to use.",
            },
            "Allowed steps": {
                "type": "list",
                "default": ["rig"],
                "description": "Allowed asset type to export dyn attrs alembic.",
            },
            "Allowed tasks": {
                "type": "list",
                "default": ["fullrig"],
                "description": "Allowed asset type to export dyn attrs alembic.",
            },
            "Rom Task": {
                "type": "list",
                "default": ["fullrig"],
                "description": "Allowed asset type to export dyn attrs alembic.",
            },
            "Allowed tags": {
                "type": "list",
                "default": ["New"],
                "description": "Allowed asset tags to export dyn attrs alembic.",
            },
        }

        # update the base settings
        plugin_settings.update(maya_publish_settings)

        return plugin_settings

    @property
    def item_filters(self):
        """
        List of item types that this plugin is interested in.

        Only items matching entries in this list will be presented to the
        accept() method. Strings can contain glob patters such as *, for example
        ["maya.*", "file.maya"]
        """

        return ["maya.anim.roms"]

    def is_required_node(self, ref_node, referenced_list):
        tk = self.parent.engine.context.sgtk
        ref_file = cmds.referenceQuery(ref_node, filename=True)
        fields = ['entity', 'task.Task.step.Step.code', 'published_file_type']
        publish = sgtk.util.find_publish(tk, [ref_file], fields=fields)
        if publish:
            publish = publish[ref_file]
            isAsset = publish['entity']['type'] == 'Asset'
            isCurrentAsset = publish['entity']['name'] == self.parent.engine.context.entity.get("name")
            isMayaScene = publish['published_file_type']['name'] == 'Maya Scene'
            isModel = publish['task.Task.step.Step.code'] == 'Model'
            file_is_top = ref_file in referenced_list

            if isAsset and isCurrentAsset and isMayaScene and isModel and file_is_top:
                return True

        return False


    def _get_tags(self):
        data = self.parent.shotgun.find_one(
            entity_type=self.parent.context.entity['type'],
            filters=[
                ['code', 'is', self.parent.context.entity['name']],
            ],
            fields=['tags']
        )
        return data['tags']


    def has_tag(self, id_tag):
        tag = filter(lambda x: x['id'] == id_tag, self._get_tags())
        if tag:
            return True
        else:
            return False


    def get_top_level_referenced_nodes(self):
        # Get the current TOP references objects in the scene
        all_refs = cmds.ls(objectsOnly=True, references=True)
        top_ref_files = cmds.file(query=True, reference=True)

        top_referenced_nodes = [ref_node for ref_node in all_refs if self.is_required_node(ref_node, top_ref_files)]

        return top_referenced_nodes

    def get_root_node(self, ref_obj=None):
        """
        From a reference object collects the top root node name.
        returns: A string corresponding to the top root node name.
        """

        if ref_obj is None:
            lastParent = "|rig_high_root"
            return lastParent


        ref_nodes = cmds.referenceQuery(ref_obj, nodes=True, dagPath=True)

        transform_list = cmds.ls(ref_nodes, type="transform", long=True)

        # get main parent of current reference's transform
        if len(transform_list) == 0:
            return None

        lastParent = transform_list[0]
        while True:
            newParent = cmds.listRelatives(
                lastParent, parent=True, fullPath=True)
            if newParent == None:
                break
            lastParent = newParent[0]

        return lastParent

    def get_rig_controls(self):

        nurbs_curves = cmds.ls(type='nurbsCurve', ni=1, o=1, r=1)
        curve_transforms = [cmds.listRelatives(i, p=1, type='transform', fullPath=True)[0] for i in nurbs_curves]

        return curve_transforms

    def set_key_framecontrols(self, list_of_elements):
        conte = True
        iteration = 0
        # set current time to first frame
        cmds.currentTime(1, edit=True)

        while len(list_of_elements) > 0:
            updated_list = []
            for prop in list_of_elements:
                try:
                    ctrl_min = (int((cmds.attributeQuery('ANI', node=prop.split('.')[0], min=True))[0]))
                    ctrl_max = (int((cmds.attributeQuery('ANI', node=prop.split('.')[0], max=True))[0]))
                    # get the current control value to set
                    new_value = ctrl_min + iteration
                    # set value to control
                    cmds.setAttr(prop.split('.')[0] + '.ANI', new_value)
                    # set kyeframe
                    cmds.setKeyframe(prop.split('.')[0], attribute='ANI', time=iteration + 1)
                    if new_value < ctrl_max:
                        updated_list.append(prop)
                except:
                    print('\t\t> {}  !!! No Tiene contrtoles min max: '.format(prop.split('.')[0]))
                    import traceback
                    print(traceback.format_exc())

            list_of_elements = updated_list
            iteration += 1

        return iteration

    def set_keys_for_dyn_attrs(self, dynamic_attr_list):

        top_value = 0

        for dyn_shape in dynamic_attr_list:
            max_value = self.set_key_framecontrols(dynamic_attr_list[dyn_shape])
            if max_value > top_value:
                top_value = max_value

        return top_value

    def accept(self, settings, item):

        cmds.loadPlugin("atomImportExport.py")

        step_name = self.parent.context.step['name'].lower()
        task_name = self.parent.context.task['name'].lower()

        allowed_steps = settings['Allowed steps'].value
        allowed_tasks = settings['Allowed tasks'].value
        allowed_tags = settings['Allowed tags'].value

        if not self.has_tag(self.tags["New"]):
            return {"accepted": False, "checked": False}

        if step_name in allowed_steps and task_name in allowed_tasks:

            rig_controls = self.get_rig_controls()
            allowed_types = settings['Allowed asset types'].value
            current_asset = self.parent.engine.shotgun.find_one(
                "Asset",
                [["id", "is", self.parent.context.entity["id"]]],
                ["sg_asset_type"]
            )
            current_asset_type = current_asset["sg_asset_type"].lower()
            valid_asset_type = current_asset_type in allowed_types

            if rig_controls and valid_asset_type:
                item.properties["rig_controls"] = rig_controls

                # Get the rome file
                rom_file = self.get_rom(settings)
                item.properties['rom'] = rom_file
                if not os.path.exists(rom_file):
                    return {"accepted": True, "checked": False}

                return {"accepted": True, "checked": True}


        return {"accepted": False, "checked": True}

    def read_hierarchy_gen(self, obj_name):
        """
        Reads the hierarchy for a node and yields the name of the group,
        is a recursive generator for each children node contained.
        """
        secondary_groups = cmds.listRelatives(obj_name, children=True, fullPath=True)
        if secondary_groups:
            for sec_grp in secondary_groups:
                yield sec_grp
                if cmds.listRelatives(sec_grp, children=True, fullPath=True):
                    for val in self.read_hierarchy_gen(sec_grp):
                        yield val

    def validate_groups(self, node, allowed):

        for val in self.read_hierarchy_gen(node):
            # Get the last name group and remove namespace
            secondary_node_name = val.split('|')[-1]
            # print secondary_node_name, '-->', val
            if str(secondary_node_name) in allowed:
                return val

    def ensure_file_is_local(self, publish):

        print("publish: ", publish)

        path = publish["path"]["local_path"]

        if not hasattr(self, 'metasync'):
            self.metasync = self.load_framework("mty-framework-metasync")

        transfersManager = self.metasync.transfersManager
        if "%" in path:
            dirname = os.path.dirname(path)
            if os.path.exists(dirname) and self._collect_sequenced_files(path):
                transfersManager.ensure_local_dependencies(publish)
                return path
        else:
            if os.path.exists(path):
                transfersManager.ensure_local_dependencies(publish)
                return path

        transfersManager.ensure_file_is_local(path, publish)
        transfersManager.ensure_local_dependencies(publish)

        return path

    def get_rom(self, settings):

        publish_type = settings["Rom Publish type"].value
        rom_task_name = settings["Rom Task"].value

        # First get the rom from last published file
        filters = [
            ["project", "is", self.parent.engine.context.project],
            ["entity", "is", self.parent.engine.context.entity],
            ["published_file_type.PublishedFileType.code", 'is', publish_type],
            ["task.Task.content", 'is', rom_task_name],
        ]
        fields = ["path"]
        order = [{'field_name': 'version_number', 'direction': 'desc'}]

        rom_publish = self.parent.engine.shotgun.find_one(
            "PublishedFile", filters, fields, order
        )
        if rom_publish:
            self.parent.log_debug("Rom publish exist, loading file...")
            rom_path = self.ensure_file_is_local(rom_publish)
        else:
            self.parent.log_debug("Published atom file not found, fallback to workarea atom file...")
            # If no published file fallback to workarea file rom
            current_file = cmds.file(q=True, sn=True)
            dir_path = os.path.dirname(current_file)
            filename = os.path.basename(current_file)
            raw_name, extension = os.path.splitext(filename)
            rom_name = raw_name + ".atom"
            rom_path = os.path.sep.join((dir_path, rom_name))

        return rom_path

    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish. Returns a
        boolean to indicate validity.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        :returns: True if item is valid and not in proxy mode, False otherwise.
        """

        rom_file = item.properties.get('rom', False)
        print("rom_file: {}".format(rom_file))
        if not rom_file or not os.path.exists(rom_file):
            raise Exception("Rom animation file does not exists.")


        # []  #
        allowed_grps = settings["Allowed groups"].value
        root_object = self.get_root_node()
        item.properties['root_object'] = root_object
        # Validate secondary groups to have at least any of the valid groups within
        #allowed_groups_found = self.validate_groups(allowed_grps, root_object)
        allowed_groups_found = self.validate_groups(root_object, allowed_grps)
        item.properties['export_group'] = allowed_groups_found
        if not allowed_groups_found:
            raise Exception("Root allowed group name is missing, Valid secondary groups are: {}.".format(allowed_grps))

        return True

    def read_atom_gen(self, atom_file):
        with open(atom_file, "r") as f:
            for line in f.readlines():
                yield line

    def get_rom_frameset(self, rom_file):
        start_frame = None
        last_frame = None
        for line in self.read_atom_gen(rom_file):
            if "startTime" in str(line):
                start_frame = float(line.replace("startTime", "").replace(";", "").replace(" ", "").strip())
            if "endTime" in str(line):
                last_frame = float(line.replace("endTime", "").replace(";", "").replace(" ", "").strip())
            if start_frame and last_frame:
                print("found frameset: ", start_frame, last_frame)
                break
        start_frame = math.floor(start_frame)
        last_frame = math.ceil(last_frame)

        return start_frame, last_frame

    def select_controls(self):
        nurbs_curves = cmds.ls(type='nurbsCurve', ni=1, o=1, r=1)
        cv_transforms = [cmds.listRelatives(i, p=1, type='transform', fullPath=True)[0] for i in nurbs_curves]

        cmds.select(cv_transforms)

        return cv_transforms

    def import_rom_animation(self, item):

        rom_file = item.properties["rom"]
        start_value, top_value = self.get_rom_frameset(rom_file)

        # Select rig controls
        self.select_controls()

        # Import atom file to curves
        cmds.file(
            rom_file,
            i=True,  # import
            type="atomImport",
            options='"targetTime=1","option=scaleInsert","match=hierarchy","selected=selectedOnly"'
        )

        return start_value, top_value

    def publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """

        # Save current state
        current_filepath = cmds.file(q=True, sn=True)
        filename = os.path.basename(current_filepath)
        dirname = os.path.dirname(current_filepath)
        raw_name, extension = os.path.splitext(filename)
        rom_filename = raw_name + "_ROM" + extension

        bk_file = "/".join((dirname, rom_filename))
        #item.properties['bk_file'] = bk_file
        #item.properties['curent_filename'] = current_filepath

        # Rename and save as rom file
        cmds.file(rename=bk_file)
        cmds.file(save=True, type="mayaAscii")

        # Restore to actual file
        cmds.file(rename=current_filepath)
        cmds.file(save=True, type="mayaAscii")

        start_value, top_value = self.import_rom_animation(item)

        root_object = item.properties['root_object']
        work_template_name = settings["Work Template"].value
        work_template = self.parent.engine.get_template_by_name(work_template_name)
        scene_path = cmds.file(q=True, sn=True)
        fields = work_template.get_fields(scene_path)

        abc_pub_name = settings["Publish Template"].value
        abc_pub_template = self.parent.engine.get_template_by_name(abc_pub_name)
        abc_published_path = abc_pub_template.apply_fields(fields)

        #abc_published_path = item.properties["rom"].replace(".atom", ".abc")
        export_group = item.properties['export_group']

        exported_alembics = self.abc_export_alembic(export_group, abc_published_path, start_value, top_value)

        publish_type = settings["Publish type"].value

        #referenced_file = item.properties['asset_ref_file']

        scene_pub_template_name = settings["Primary Publish Template"].value
        scene_pub_template = self.parent.engine.get_template_by_name(scene_pub_template_name)
        primary_publish_path = scene_pub_template.apply_fields(fields)

        self.register_abc_publishes(root_object, item, fields, publish_type, abc_published_path, primary_publish_path)

        # restore current rom animated file to the original file name
        #bk_file = item.properties['bk_file']
        #current_filepath = item.properties['curent_filename']

        # Rename and save as rom file
        cmds.file(bk_file, open=True, force=True)

        # Restore to actual file
        cmds.file(rename=current_filepath)
        cmds.file(save=True, type="mayaAscii", force=True)

        return []

    def get_root_item(self, item):
        """ A handy recursive function to get an item's root parent
            This is mostly useful because the root is the only one
            where we can store extra data
        """

        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root


    def register_abc_publishes(self, asset_name, p_item, p_fields, p_type, publish_path, prim_pub_path):

        root_item = self.get_root_item(p_item)
        root_item.properties.setdefault('sg_publish_extra_data', [])
        publish_extra_data = root_item.properties['sg_publish_extra_data']

        publishes = []
        alembic_dependencies = []
        alembic_dependencies.append(prim_pub_path)
        print('alembic_dependencies: ', alembic_dependencies)

        self.parent.log_info("Publish alembic for:  {}".format(asset_name))

        # and a publish name
        publish_name = self.parent.util.get_publish_name(publish_path)

        sg_fields_to_update = {
            "sg_status_list": "rev",
            "task.Task.sg_status_list": "rev",
        }

        # Register the publish of the Alembic File:
        args = {
            "tk": self.parent.engine.sgtk,
            "context": p_item.context,
            "comment": p_item.description,
            "path": publish_path,
            "name": publish_name,
            "version_number": p_fields["version"],
            "thumbnail_path": p_item.get_thumbnail_as_path(),
            "task": self.parent.engine.context.task,
            "dependency_paths": alembic_dependencies,
            "sg_fields": sg_fields_to_update,
            "published_file_type": p_type.replace("{step_code}", p_fields['step_code'])
        }
        sg_publish = sgtk.util.register_publish(**args)

        publish_extra_data.append(sg_publish)

        #self.parent.tank.shotgun.update("PublishedFile", sg_publish["id"],{"sg_status_list": 'rev'})
        self.parent.log_info("Published file registered.")

        return sg_publish

    def abc_export_alembic(self, export_items, abc_publish_path, export_in_frame, export_out_frame, samples=False):
        startFrame = export_in_frame
        endFrame = export_out_frame

        alembic_frame_relative_samples = (-0.2500, 0, 0.2500)
        abcCachePrefix = 'fbCache'
        alembic_args = [
            '-frameRange %d %d' % (startFrame, endFrame),
            '-attrPrefix %s' % abcCachePrefix,
            '-attr fbCache_Dyn_Text_L_EyeBrow_CTL_Id',
            '-stripNamespaces',
            '-uvWrite',
            '-writeColorSets',
            '-writeFaceSets',
            '-worldSpace',
            '-writeVisibility',
            '-writeUVSets',
            '-dataFormat ogawa',
            '-eulerFilter',
            # '-attr \"cacheColor\"',
            # '-attr \"xgen_Pref\"',
            # '-attr \"SubDivisionMesh\"',
        ]

        if not cmds.pluginInfo("AbcExport", loaded=True, query=True):
            cmds.loadPlugin("AbcExport")

        abc_export_cmd = "AbcExport"

        # start with fresh args for each asset
        the_args = list(alembic_args)

        export_filename = abc_publish_path
        self.parent.engine.ensure_folder_exists(os.path.dirname(export_filename))

        # Verify if the cachNodes is a list type, then add every node to the root list
        if isinstance(export_items, list):
            for node in export_items:
                the_args.append('-root %s' % node)
        else:
            the_args.append('-root %s' % export_items)

        if samples and startFrame != endFrame:
            for sample in alembic_frame_relative_samples:
                the_args.append('-frs %.4f' % sample)
                the_args.append('-step %d' % 1.0)

        the_args.append('-file ' + export_filename.replace('\\', '/'))

        abc_export_cmd += (" -j \"%s\"" % " ".join(the_args))

        print("*" * 60)
        print(abc_export_cmd)
        print("*" * 60)
        print("Exporting alembic with command:\n{}".format(abc_export_cmd))

        mel.eval(abc_export_cmd)


    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        pass
