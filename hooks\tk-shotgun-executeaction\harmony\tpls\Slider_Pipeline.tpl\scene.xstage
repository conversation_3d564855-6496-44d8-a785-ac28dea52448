<?xml version="1.0" encoding="UTF-8"?>
<project source="Harmony Premium (HarmonyPremium.exe) version 21.1.1 build 18815 2022-09-18 10:48:34" version="2111" build="18815" creator="harmony">
 <elements>
  <element id="1" elementName="Slider" elementFolder="Slider" pixmapFormat="2" scanType="2" fieldChart="12" vectorType="2" rootFolder="elements">
   <drawings>
    <dwg name="1"/>
   </drawings>
  </element>
  <element id="2" elementName="Slider_CTR" elementFolder="Slider_CTR" pixmapFormat="2" scanType="2" fieldChart="12" vectorType="2" rootFolder="elements">
   <drawings>
    <dwg name="1"/>
   </drawings>
  </element>
 </elements>
 <options>
  <metrics unitAspectRatioX="4" unitAspectRatioY="3" numberOfUnitsX="24" numberOfUnitsY="24" numberOfUnitsZ="12"/>
  <resolution name="HDTV_1080p24" size="1920,1080" fovFit="VerticalFitFov" fov="41.112090439166927" projection="PerspectiveProjection"/>
  <framerate val="24"/>
  <zdragging val="true"/>
  <zOrderCompatibilityWith7_3 val="false"/>
  <pixelPerModelUnitForVectorLayers val="0.28800000000000009"/>
  <pixelPerModelUnitForBitmapLayers val="0.28800000000000009"/>
  <canvasForBitmapLayers size="3840,2160"/>
  <cameraInSymbols val="true"/>
  <scaleFactor val="1"/>
  <colorSpace val="sRGB"/>
  <convertPalettesColorSpace val="true"/>
 </options>
 <timelineMarkers/>
 <scenes>
  <scene name="Top" id="0be3d552b320192e" nbframes="60" startFrame="1" stopFrame="1">
   <columns>
    <column type="3" name="ATV-0BE3D552B3201955" color="#C7C6BBFF" displayOrder="0" width="100" anonymous="true">
     <step val="1" start="1" stop="1"/>
    </column>
    <column type="3" name="ATV-0BE3D552B3201957" color="#C7C6BBFF" displayOrder="0" width="100" anonymous="true">
     <step val="1" start="1" stop="1"/>
    </column>
    <column type="3" name="ATV-0BE3D552B3201959" color="#C7C6BBFF" displayOrder="0" width="100" anonymous="true">
     <step val="1" start="1" stop="1"/>
    </column>
    <column type="3" name="ATV-0BE3D552B320195B" color="#C7C6BBFF" displayOrder="0" width="100" anonymous="true">
     <step val="1" start="1" stop="1"/>
    </column>
    <column type="3" name="ATV-0BE3D552B320195D" color="#C7C6BBFF" displayOrder="0" width="100" anonymous="true">
     <step val="1" start="1" stop="1"/>
    </column>
    <column type="3" name="ATV-0BE3D552B320195F" color="#C7C6BBFF" displayOrder="0" width="100" anonymous="true">
     <step val="1" start="1" stop="1"/>
    </column>
    <column type="3" name="ATV-0BE3D552B3201961" color="#C7C6BBFF" displayOrder="0" width="100" anonymous="true">
     <step val="1" start="1" stop="1"/>
    </column>
    <column type="0" name="ATV-0BE3D552B3201963" color="#FFFFFFFF" displayOrder="0" width="100" anonymous="true" id="1">
     <elementSeq exposures="1-60" val="1" id="1"/>
    </column>
    <column type="3" name="Slider_CTR-P_Pos_x" color="#C7C6BBFF" displayOrder="0" width="100" anonymous="false">
     <step val="1" start="1" stop="1"/>
     <points version="2">
      <pt constSeg="false" x="1" yLocal="1" y="1"/>
     </points>
    </column>
    <column type="5" name="Expr_Empty" color="#C4D1DFFF" displayOrder="0" width="100" anonymous="false">
     <JS>
      <![CDATA[0]]>
     </JS>
    </column>
    <column type="5" name="Expr_One" color="#C4D1DFFF" displayOrder="0" width="100" anonymous="false">
     <JS>
      <![CDATA[1]]>
     </JS>
    </column>
    <column type="3" name="ATV-0BE3D552B3201965" color="#C7C6BBFF" displayOrder="0" width="100" anonymous="true">
     <step val="1" start="1" stop="1"/>
     <points version="2">
      <pt constSeg="false" x="1" yLocal="0" y="0"/>
     </points>
    </column>
    <column type="0" name="ATV-0BE3D552B3201967" color="#FFFFFFFF" displayOrder="0" width="100" anonymous="true" id="2">
     <elementSeq exposures="1-60" val="1" id="2"/>
    </column>
   </columns>
   <options>
    <defaultDisplay val="Unconnected_Display"/>
   </options>
   <rootgroup name="Top">
    <options>
     <collapsed val="false"/>
    </options>
    <nodeslist>
     <module type="PEG" name="Slider_CTR-P" pos="16,177,1" publishUnderTab="Slider_CTR-P">
      <options>
       <collapsed val="false"/>
       <version val="1"/>
      </options>
      <attrs>
       <enable3d val="false"/>
       <faceCamera val="false"/>
       <cameraAlignment val="NO_CAMERA_ALIGNMENT"/>
       <position>
        <separate val="true"/>
        <x val="0" defaultValue="0" col="Slider_CTR-P_Pos_x"/>
        <y val="0" defaultValue="0" col="Expr_Empty"/>
        <z val="0" defaultValue="0" col="Expr_Empty"/>
       </position>
       <scale>
        <separate val="true"/>
        <inFields val="false"/>
        <xy val="1" defaultValue="1"/>
        <x val="1" defaultValue="1" col="Expr_One"/>
        <y val="1" defaultValue="1" col="Expr_One"/>
        <z val="1" defaultValue="1"/>
       </scale>
       <rotation>
        <separate val="false"/>
        <anglex val="0" defaultValue="0"/>
        <angley val="0" defaultValue="0"/>
        <anglez val="0" defaultValue="0" col="Expr_Empty"/>
       </rotation>
       <angle val="0" defaultValue="0" col="Expr_Empty"/>
       <skew val="0" defaultValue="0" col="Expr_Empty"/>
       <pivot>
        <x val="-4.1313508643891117" defaultValue="0"/>
        <y val="-1.8038670948649864" defaultValue="0"/>
        <z val="0" defaultValue="0"/>
       </pivot>
       <splineOffset>
        <x val="0" defaultValue="0"/>
        <y val="0" defaultValue="0"/>
        <z val="0" defaultValue="0"/>
       </splineOffset>
       <ignoreParentPegScaling val="false"/>
       <disableFieldRendering val="false"/>
       <depth val="0"/>
       <enableMinMaxAngle val="false"/>
       <minAngle val="-360" defaultValue="-360"/>
       <maxAngle val="360" defaultValue="360"/>
       <nailForChildren val="false"/>
       <ikHoldOrientation val="false"/>
       <ikHoldX val="false"/>
       <ikHoldY val="false"/>
       <ikExcluded val="false"/>
       <ikCanRotate val="true"/>
       <ikCanTranslateX val="false"/>
       <ikCanTranslateY val="false"/>
       <ikBoneX val="0.20000000000000001" defaultValue="0.20000000000000001"/>
       <ikBoneY val="0" defaultValue="0"/>
       <ikStiffness val="1" defaultValue="1"/>
       <groupAtNetworkBuilding val="false"/>
       <addCompositeToGroup val="true"/>
      </attrs>
     </module>
     <module type="READ" name="Slider_CTR" pos="0,250,2" publishUnderTab="Slider_CTR">
      <options>
       <collapsed val="false"/>
       <version val="1"/>
      </options>
      <attrs>
       <enable3d val="false"/>
       <faceCamera val="false"/>
       <cameraAlignment val="NO_CAMERA_ALIGNMENT"/>
       <offset>
        <separate val="true"/>
        <x val="0" defaultValue="0" col="ATV-0BE3D552B3201965"/>
        <y val="0" defaultValue="0"/>
        <z val="0" defaultValue="0"/>
       </offset>
       <scale>
        <separate val="true"/>
        <inFields val="false"/>
        <xy val="1" defaultValue="1"/>
        <x val="1" defaultValue="1"/>
        <y val="1" defaultValue="1"/>
        <z val="1" defaultValue="1"/>
       </scale>
       <rotation>
        <separate val="false"/>
        <anglex val="0" defaultValue="0"/>
        <angley val="0" defaultValue="0"/>
        <anglez val="0" defaultValue="0"/>
       </rotation>
       <angle val="0" defaultValue="0"/>
       <skew val="0" defaultValue="0"/>
       <pivot>
        <x val="0" defaultValue="0"/>
        <y val="0" defaultValue="0"/>
        <z val="0" defaultValue="0"/>
       </pivot>
       <splineOffset>
        <x val="0" defaultValue="0"/>
        <y val="0" defaultValue="0"/>
        <z val="0" defaultValue="0"/>
       </splineOffset>
       <ignoreParentPegScaling val="false"/>
       <disableFieldRendering val="false"/>
       <depth val="0"/>
       <enableMinMaxAngle val="false"/>
       <minAngle val="-360" defaultValue="-360"/>
       <maxAngle val="360" defaultValue="360"/>
       <nailForChildren val="false"/>
       <ikHoldOrientation val="false"/>
       <ikHoldX val="false"/>
       <ikHoldY val="false"/>
       <ikExcluded val="false"/>
       <ikCanRotate val="true"/>
       <ikCanTranslateX val="false"/>
       <ikCanTranslateY val="false"/>
       <ikBoneX val="0.20000000000000001" defaultValue="0.20000000000000001"/>
       <ikBoneY val="0" defaultValue="0"/>
       <ikStiffness val="1" defaultValue="1"/>
       <drawing>
        <elementMode val="true"/>
        <element col="ATV-0BE3D552B3201967">
         <layer/>
        </element>
        <customName>
         <name/>
         <extension val="tga"/>
         <fieldChart val="12" defaultValue="12"/>
        </customName>
       </drawing>
       <readOverlay val="true"/>
       <readLineArt val="true"/>
       <readColorArt val="true"/>
       <readUnderlay val="true"/>
       <overlayArtDrawingMode val="VectorDrawingMode"/>
       <lineArtDrawingMode val="VectorDrawingMode"/>
       <colorArtDrawingMode val="VectorDrawingMode"/>
       <underlayArtDrawingMode val="VectorDrawingMode"/>
       <pencilLineDeformationPreserveThickness val="false"/>
       <pencilLineDeformationQuality val="Low"/>
       <pencilLineDeformationSmooth val="1"/>
       <pencilLineDeformationFitError val="3" defaultValue="3"/>
       <readColor val="true"/>
       <readTransparency val="true"/>
       <colorTransformation val="Linear"/>
       <colorSpace val="sRGB"/>
       <applyMatteToColor val="N"/>
       <enableLineTexture val="true"/>
       <antialiasingQuality val="HIGH"/>
       <antialiasingExponent val="1" defaultValue="1"/>
       <opacity val="80" defaultValue="100"/>
       <textureFilter val="NEAREST_FILTERED"/>
       <adjustPencilThickness val="false"/>
       <normalLineArtThickness val="true"/>
       <zoomIndependentLineArtThickness val="zoomIndependent"/>
       <multLineArtThickness val="1" defaultValue="1"/>
       <addLineArtThickness val="0" defaultValue="0"/>
       <minLineArtThickness val="0" defaultValue="0"/>
       <maxLineArtThickness val="0" defaultValue="0"/>
       <useDrawingPivot val="APPLY_ON_READ_TRANSFORM"/>
       <flipHor val="false"/>
       <flipVert val="false"/>
       <turnBeforeAlignment val="false"/>
       <noClipping val="false"/>
       <xClipFactor val="0"/>
       <yClipFactor val="0"/>
       <alignmentRule val="CENTER_FIRST_PAGE"/>
       <morphingVelo val="0" defaultValue="0"/>
       <canAnimate val="false"/>
       <tileHorizontal val="false"/>
       <tileVertical val="false"/>
      </attrs>
     </module>
     <module type="READ" name="Slider" pos="178,177,3" publishUnderTab="Slider">
      <options>
       <collapsed val="false"/>
       <version val="1"/>
      </options>
      <attrs>
       <enable3d val="false"/>
       <faceCamera val="false"/>
       <cameraAlignment val="NO_CAMERA_ALIGNMENT"/>
       <offset>
        <separate val="true"/>
        <x val="0" defaultValue="0" col="ATV-0BE3D552B3201955"/>
        <y val="0" defaultValue="0" col="ATV-0BE3D552B3201957"/>
        <z val="0" defaultValue="0" col="ATV-0BE3D552B3201959"/>
       </offset>
       <scale>
        <separate val="true"/>
        <inFields val="false"/>
        <xy val="1" defaultValue="1"/>
        <x val="1" defaultValue="1" col="ATV-0BE3D552B320195B"/>
        <y val="1" defaultValue="1" col="ATV-0BE3D552B320195D"/>
        <z val="1" defaultValue="1"/>
       </scale>
       <rotation>
        <separate val="false"/>
        <anglex val="0" defaultValue="0"/>
        <angley val="0" defaultValue="0"/>
        <anglez val="0" defaultValue="0" col="ATV-0BE3D552B320195F"/>
       </rotation>
       <angle val="0" defaultValue="0" col="ATV-0BE3D552B320195F"/>
       <skew val="0" defaultValue="0" col="ATV-0BE3D552B3201961"/>
       <pivot>
        <x val="0" defaultValue="0"/>
        <y val="0" defaultValue="0"/>
        <z val="0" defaultValue="0"/>
       </pivot>
       <splineOffset>
        <x val="0" defaultValue="0"/>
        <y val="0" defaultValue="0"/>
        <z val="0" defaultValue="0"/>
       </splineOffset>
       <ignoreParentPegScaling val="false"/>
       <disableFieldRendering val="false"/>
       <depth val="0"/>
       <enableMinMaxAngle val="false"/>
       <minAngle val="-360" defaultValue="-360"/>
       <maxAngle val="360" defaultValue="360"/>
       <nailForChildren val="false"/>
       <ikHoldOrientation val="false"/>
       <ikHoldX val="false"/>
       <ikHoldY val="false"/>
       <ikExcluded val="false"/>
       <ikCanRotate val="true"/>
       <ikCanTranslateX val="false"/>
       <ikCanTranslateY val="false"/>
       <ikBoneX val="0.20000000000000001" defaultValue="0.20000000000000001"/>
       <ikBoneY val="0" defaultValue="0"/>
       <ikStiffness val="1" defaultValue="1"/>
       <drawing>
        <elementMode val="true"/>
        <element col="ATV-0BE3D552B3201963">
         <layer/>
        </element>
        <customName>
         <name/>
         <extension val="tga"/>
         <fieldChart val="12" defaultValue="12"/>
        </customName>
       </drawing>
       <readOverlay val="true"/>
       <readLineArt val="true"/>
       <readColorArt val="true"/>
       <readUnderlay val="true"/>
       <overlayArtDrawingMode val="VectorDrawingMode"/>
       <lineArtDrawingMode val="VectorDrawingMode"/>
       <colorArtDrawingMode val="VectorDrawingMode"/>
       <underlayArtDrawingMode val="VectorDrawingMode"/>
       <pencilLineDeformationPreserveThickness val="false"/>
       <pencilLineDeformationQuality val="Low"/>
       <pencilLineDeformationSmooth val="1"/>
       <pencilLineDeformationFitError val="3" defaultValue="3"/>
       <readColor val="true"/>
       <readTransparency val="true"/>
       <colorTransformation val="Linear"/>
       <colorSpace val="sRGB"/>
       <applyMatteToColor val="N"/>
       <enableLineTexture val="true"/>
       <antialiasingQuality val="HIGH"/>
       <antialiasingExponent val="1" defaultValue="1"/>
       <opacity val="65" defaultValue="100"/>
       <textureFilter val="NEAREST_FILTERED"/>
       <adjustPencilThickness val="false"/>
       <normalLineArtThickness val="true"/>
       <zoomIndependentLineArtThickness val="zoomIndependent"/>
       <multLineArtThickness val="1" defaultValue="1"/>
       <addLineArtThickness val="0" defaultValue="0"/>
       <minLineArtThickness val="0" defaultValue="0"/>
       <maxLineArtThickness val="0" defaultValue="0"/>
       <useDrawingPivot val="APPLY_ON_READ_TRANSFORM"/>
       <flipHor val="false"/>
       <flipVert val="false"/>
       <turnBeforeAlignment val="false"/>
       <noClipping val="false"/>
       <xClipFactor val="0"/>
       <yClipFactor val="0"/>
       <alignmentRule val="CENTER_FIRST_PAGE"/>
       <morphingVelo val="0" defaultValue="0"/>
       <canAnimate val="false"/>
       <tileHorizontal val="false"/>
       <tileVertical val="false"/>
      </attrs>
     </module>
     <module type="VISIBILITY" name="Visibility" pos="168,376,5" publishUnderTab="Visibility">
      <options>
       <collapsed val="false"/>
       <version val="1"/>
      </options>
      <attrs>
       <oglrender val="true"/>
       <softrender val="false"/>
      </attrs>
     </module>
     <module type="StaticConstraint" name="ST-Slider" pos="191,26,6" publishUnderTab="ST-Slider">
      <options>
       <collapsed val="false"/>
       <version val="1"/>
      </options>
      <attrs>
       <bakeattr/>
       <bakeattrall/>
       <active val="false"/>
       <translate>
        <x val="0" defaultValue="0"/>
        <y val="0" defaultValue="0"/>
        <z val="0" defaultValue="0"/>
       </translate>
       <scale>
        <separate val="true"/>
        <inFields val="false"/>
        <xy val="1" defaultValue="1"/>
        <x val="1" defaultValue="1"/>
        <y val="1" defaultValue="1"/>
        <z val="1" defaultValue="1"/>
       </scale>
       <rotate>
        <anglex val="0" defaultValue="0"/>
        <angley val="0" defaultValue="0"/>
        <anglez val="0" defaultValue="0"/>
       </rotate>
       <skewx val="0" defaultValue="0"/>
       <skewy val="0" defaultValue="0"/>
       <skewz val="0" defaultValue="0"/>
       <inverted val="false"/>
      </attrs>
     </module>
     <module type="PEG" name="Slider-P" pos="194,79,7" publishUnderTab="Slider-P">
      <options>
       <collapsed val="false"/>
       <version val="1"/>
      </options>
      <attrs>
       <enable3d val="false"/>
       <faceCamera val="false"/>
       <cameraAlignment val="NO_CAMERA_ALIGNMENT"/>
       <position>
        <separate val="true"/>
        <x val="0" defaultValue="0"/>
        <y val="0" defaultValue="0"/>
        <z val="0" defaultValue="0"/>
       </position>
       <scale>
        <separate val="true"/>
        <inFields val="false"/>
        <xy val="1" defaultValue="1"/>
        <x val="1" defaultValue="1"/>
        <y val="1" defaultValue="1"/>
        <z val="1" defaultValue="1"/>
       </scale>
       <rotation>
        <separate val="false"/>
        <anglex val="0" defaultValue="0"/>
        <angley val="0" defaultValue="0"/>
        <anglez val="0" defaultValue="0"/>
       </rotation>
       <angle val="0" defaultValue="0"/>
       <skew val="0" defaultValue="0"/>
       <pivot>
        <x val="-3.1333469980981459" defaultValue="0"/>
        <y val="-1.7157604712923524" defaultValue="0"/>
        <z val="0" defaultValue="0"/>
       </pivot>
       <splineOffset>
        <x val="0" defaultValue="0"/>
        <y val="0" defaultValue="0"/>
        <z val="0" defaultValue="0"/>
       </splineOffset>
       <ignoreParentPegScaling val="false"/>
       <disableFieldRendering val="false"/>
       <depth val="0"/>
       <enableMinMaxAngle val="false"/>
       <minAngle val="-360" defaultValue="-360"/>
       <maxAngle val="360" defaultValue="360"/>
       <nailForChildren val="false"/>
       <ikHoldOrientation val="false"/>
       <ikHoldX val="false"/>
       <ikHoldY val="false"/>
       <ikExcluded val="false"/>
       <ikCanRotate val="true"/>
       <ikCanTranslateX val="false"/>
       <ikCanTranslateY val="false"/>
       <ikBoneX val="0.20000000000000001" defaultValue="0.20000000000000001"/>
       <ikBoneY val="0" defaultValue="0"/>
       <ikStiffness val="1" defaultValue="1"/>
       <groupAtNetworkBuilding val="false"/>
       <addCompositeToGroup val="true"/>
      </attrs>
     </module>
     <module type="COMPOSITE" name="Slider_Comp" pos="157,319,8" publishUnderTab="Slider_Comp">
      <options>
       <collapsed val="false"/>
       <version val="1"/>
      </options>
      <attrs>
       <compositeMode val="compositePassthrough"/>
       <flattenOutput val="false"/>
       <flattenVector val="false"/>
       <composite2d val="false"/>
       <composite3d val="false"/>
       <outputZ val="LEFTMOST"/>
       <outputZInputPort val="1"/>
       <applyFocus val="true"/>
       <multiplier val="1" defaultValue="1"/>
       <tvgPalette val="compositedPalette"/>
       <mergeVector val="false"/>
      </attrs>
     </module>
    </nodeslist>
    <linkedlist>
     <link out="Slider_CTR-P" in="Slider_CTR" inport="0"/>
     <link out="Slider_CTR" in="Slider_Comp" inport="1"/>
     <link out="Slider" in="Slider_Comp" inport="0"/>
     <link out="ST-Slider" in="Slider-P"/>
     <link out="Slider-P" in="Slider" inport="0"/>
     <link out="Slider-P" in="Slider_CTR-P"/>
     <link out="Slider_Comp" in="Visibility"/>
    </linkedlist>
    <backdrops>
     <backdrop topLeft="-45,-26" bottomRight="390,402">
      <background color="#323232FF"/>
      <title text="Slider" color="#000000FF" size="12" font="Arial"/>
      <description text="" color="#000000FF" size="12" font="Arial"/>
     </backdrop>
    </backdrops>
    <waypoints>
     <waypoint nameId="waypoint" location="228,114"/>
    </waypoints>
    <waypointLinks>
     <waypointLink nameId="waypoint" out="Slider-P" outport="0" in="Slider" inport="0"/>
    </waypointLinks>
   </rootgroup>
   <unconnectedComposite>
    <module type="COMPOSITE" name="Unconnected_Composite" pos="0,0,0" publishUnderTab="Unconnected_Composite">
     <options>
      <collapsed val="false"/>
      <version val="1"/>
     </options>
     <attrs>
      <compositeMode val="compositeBitmap"/>
      <flattenOutput val="true"/>
      <flattenVector val="false"/>
      <composite2d val="false"/>
      <composite3d val="false"/>
      <outputZ val="LEFTMOST"/>
      <outputZInputPort val="1"/>
      <applyFocus val="true"/>
      <multiplier val="1" defaultValue="1"/>
      <tvgPalette val="compositedPalette"/>
      <mergeVector val="false"/>
     </attrs>
    </module>
   </unconnectedComposite>
   <timelineOrderer/>
  </scene>
 </scenes>
 <symbols>
  <folder name="Symbols">
   <scene id="0be3d552b320192e"/>
  </folder>
 </symbols>
 <actionTemplate validationMarker="7">
  <node val="Top/Slider_CTR-P"/>
  <node val="Top/Slider_CTR"/>
  <node val="Top/Slider"/>
  <node val="Top/Visibility"/>
  <node val="Top/ST-Slider"/>
  <node val="Top/Slider-P"/>
  <node val="Top/Slider_Comp"/>
 </actionTemplate>
 <timeline>
  <scene id="0be3d552b320192e"/>
 </timeline>
</project>
