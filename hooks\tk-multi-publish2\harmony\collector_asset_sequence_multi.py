#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

import os
import re
import sys
import pprint
# import shutil

import sgtk

HookBaseClass = sgtk.get_hook_baseclass()

class HarmonyFramesCollector(HookBaseClass):
    """
    Collector that operates on the Toon Boom Harmony session. Should inherit
    from the basic collector hook.
    """

    @property
    def settings(self):

        # grab any base class settings
        collector_settings = super(HarmonyFramesCollector, self).settings or {}

        # settings specific to this collector
        harmony_session_settings = {
            "Work Template": {
                "type": "template",
                "default": None,
                "description": "Template path for artist work files. Should "
                               "correspond to a template defined in "
                               "templates.yml. If configured, is made available"
                               "to publish plugins via the collected item's "
                               "properties. ",
            },
            "Work Frames Folder Template": {
                "type": "template",
                "default": None,
                "description": "Template path for artist work frames. Should "
                               "correspond to a template defined in "
                               "templates.yml. If configured, is made available"
                               "to publish plugins via the collected item's "
                               "properties. ",
            },
            "Publish Frames Template": {
                "type": "template",
                "default": None,
                "description": "Template path for artist published frames. Should "
                               "correspond to a template defined in "
                               "templates.yml. If configured, is made available"
                               "to publish plugins via the collected item's "
                               "properties. ",
            },
            "Image Sequence Workflow": {
                "type": "str",
                "default": "grouped",
                "description": "Define the workflow to collect image sequences,"
                               " which can be either 'individual' or 'grouped'"
            },
            "Beauty rendered name": {
                "type": "str",
                "default": None,
                "description": "Name of the base layer to create multi exr"
            },

            "Beauty register layer": {
                "type": "str",
                "default": None,
                "description": "Name of the base layer user as layer 0 in multiexr"
            }
        }

        # update the base settings with these settings
        collector_settings.update(harmony_session_settings)

        return collector_settings

    def process_current_session(self, settings, parent_item):
        """
        Analyzes the current session open in Toon Boom Harmony and parents a
        subtree of items under the parent_item passed in.
        :param dict settings: Configured settings for this collector
        :param parent_item: Root item instance
        """

        # create an item representing the current Toon Boom Harmony session
        item = self.collect_current_harmony_session(settings, parent_item)
        frames_sequences = self._collect_frames(settings, item)

    def _collect_frames(self, settings, parent_item):

        self.logger.debug("Collecting rendered frames...")

        # get fileseq in a partially generic way, untill we have mty-framework-externalpython
        # we know this hook files lives in {config}/tk-multi-publish2/harmony, so we will place fileseq there
        config_path = os.path.dirname(os.path.dirname(self.disk_location))
        python_modules_path = os.path.join(config_path, "external_python_modules")
        python_future_path  = os.path.join(python_modules_path, "python-future")

        sys.path.append(python_modules_path)
        sys.path.append(python_future_path)

        # import fileseq

        engine        = sgtk.platform.current_engine()
        path          = engine.app.get_current_project_path()
        publisher     = self.parent
        publish_icons = os.path.join(config_path, 'tk-multi-publish2', 'icons')
        icon_path     = os.path.join(publish_icons, "video.png")

        # get templates from settings
        self.parent.log_debug(settings)

        # Collector settings
        work_template_setting           = settings.get("Work Template").value
        work_frames_template_setting    = settings.get("Work Frames Folder Template").value
        publish_frames_template_setting = settings.get("Publish Frames Template").value
        beauty_aov_name                 = settings.get("Beauty rendered name").value
        beauty_output_name              = settings.get("Beauty register layer").value

        image_sequence_workflow = settings.get("Image Sequence Workflow").value


        # Creating templates from settings
        work_template           = publisher.engine.get_template_by_name(work_template_setting)
        work_frames_template    = publisher.engine.get_template_by_name(work_frames_template_setting)
        publish_frames_template = publisher.engine.get_template_by_name(publish_frames_template_setting)

        # Dynamic paths from templates
        work_fields         = work_template.get_fields(path)
        publish_frames_path = publish_frames_template.apply_fields(work_fields)
        work_frames_folder  = work_frames_template.apply_fields(work_fields)

        self.logger.debug("Looking in folder: %s" % work_frames_folder)
        self.parent.engine.logger.info("Looking in folder: %s" % work_frames_folder)

        # Get rendered paths from write nodes
        output_paths = self.parent.engine.app.custom_script("""
            include("harmony_utility_functions.js");
            var enabled_write_nodes = get_enabled_nodes_by_type("%s");
            get_write_nodes_output_paths(enabled_write_nodes);
            """ % "WRITE"
        )
        self.parent.engine.logger.info('Output paths: ' + str(output_paths))

        # TODO: validate paths are in work folder directory dirname()?

        # Get valid extension choices from template
        valid_extensions  = publish_frames_template.keys['extension'].choices
        # Create dictionary with sequences by a unique name
        sequences_dict = self.find_sequences_in_write_node_paths(output_paths, valid_extensions)

        self.parent.engine.logger.info(
            "Found {} sequences (sequences_dict):\n{}".format(
                len(sequences_dict.keys()), pprint.pformat(sequences_dict)
            )
        )

        # Info of the publish path render to compare in each iteration
        # valid_extentions  = publish_frames_template.keys['extension'].choices
        session_items    = []
        layers_sequences = []

        self.parent.engine.logger.info('seqeunce dict: %s', sequences_dict )

        # Find disabled write nodes and show warning they wont be published
        self.validate_disabled_write_nodes()

        for sequence_text_id, sequence in sequences_dict.items():

            sequence_name = str(sequence)

            display_name = sequence.format(
                '{basename}{padding}{extension} ({start}-{end})'
            ).replace(sequence._base, sequence_text_id)
            display_name = display_name.replace('@@@', '%03d')
            display_name = display_name.replace('#', '%04d')
            self.parent.engine.logger.info('sequence_name: ' + sequence_name)

            if "Thumbs.db" in sequence_name:
                continue

            # We support two different workflows here:
            #
            # 1- We collect a single main layer item and it
            #    contains all layers grouped in its properties
            #    under the "layers_sequences" key
            #
            # 2- We collect all layers as individual items
            #
            # The main difference is related to how we can publish
            # the image sequences, where in the first case we can
            # do it ideally as a single published file, for example
            # as a single multi layer exr that we can combine in the
            # publish hook, and in a second case, where we are not
            # able to do a single publish, but we are forced to do
            # one publish per image sequence
            #
            # The collection workflow will be defined by a setting
            # which is set in the environment settings:
            # - Image Sequence Workflow
            #   "individual" or "grouped"
            #
            # TODO: Ideally we should control using an SG override
            #       we need to add the mty-framework-valueoverrides
            #       and define a setting for this case and update
            #       the code acoordingly
            #
            # TODO: All this collection logic should also be a separate
            #       hook, something that can be reused by other per dcc
            #       hooks in a hierarchical way so that code is not duplicated

            if image_sequence_workflow == "grouping":
                if '{}.'.format(beauty_aov_name) == sequence._base:
                    self.logger.info(
                        "Found master sequence: {}".format(display_name)
                    )
                    session_item = parent_item.create_item(
                        "harmony.frames_sequence",
                        "Render Frames",
                        display_name
                    )
                    session_item.properties['sequence']  = sequence
                    session_item.properties['sequence_text_id'] = sequence_text_id
                    session_item.properties['beauty_output'] = beauty_output_name
                    session_item.properties['scene_path'] = engine.app.get_current_project_path()
                    session_item.set_icon_from_path(icon_path)
                    session_items.append(session_item)

                else:
                    self.logger.info(
                        'Found layer sequence: {0}'.format(display_name)
                    )
                    layers_sequences.append(sequence)
                    continue
            else:
                self.logger.info(
                    "Found individual sequence: {}".format(display_name)
                )
                session_item = parent_item.create_item(
                    "harmony.frames_sequence",
                    "Render Frames",
                    display_name
                )
                session_item.properties['sequence'] = sequence
                session_item.properties['sequence_text_id'] = sequence_text_id
                session_item.properties['scene_path'] = engine.app.get_current_project_path()
                session_item.set_icon_from_path(icon_path)
                session_items.append(session_item)

        # For the "grouping" workflow, add layers to the item if it exists
        if image_sequence_workflow == "grouping":
            if len(session_items):
                session_items[0].properties['layers_sequences'] = layers_sequences
            else:
                beauty_msg = 'Processing sequence item:  No "{}." sequence found'
                self.logger.error(beauty_msg.format(beauty_aov_name))

        return session_items

    def validate_disabled_write_nodes(self):
        '''
        Find disabled write nodes in harmony and show warning it wont be published.
            Raises:
                Warning in logger if it founds disabled nodes.
        '''
        # Get disabled nodes and show warning
        output_disabled_paths = self.parent.engine.app.custom_script("""
            include("harmony_utility_functions.js");
            var disabled_write_nodes = get_disabled_nodes_by_type("%s");
            get_write_nodes_output_paths(disabled_write_nodes);
            """ % "WRITE"
        )
        disabled_nodes = self.extract_names_from_image_path(output_disabled_paths)
        if len(disabled_nodes) > 0:
            message = "Disabled write nodes found for the sequences with the following filenames: \"%s\", for publishing please enable and open publisher again."
            self.logger.warning(message % str(disabled_nodes))

        self.parent.engine.logger.info('Disabled nodes: %s',disabled_nodes)

    def clean_basename_from_sequence(self, text_id):
        '''
        Find basename separator and remove it
            Args:
                (str) basename from sequence
            Returns:
                (str) clean name
        '''
        separator_pattern = re.compile(r"(?P<base>[\d\w]+)(?P<separator>[-_.]?)")
        separator_match = re.match(separator_pattern, text_id)
        if separator_match:
            if "separator" in separator_match.groupdict().keys():
                text_id = text_id.replace(
                    separator_match.groupdict()["separator"], ""
                )

        return text_id

    def compare_sequence_with_node(self, sequences, path):
        '''
        Compare found sequences name with write node names
            Args:
                (list) file seq list of found sequences
                (str) path that contains name from write node
            Returns:
                (list) file seq list without non matching names sequences
        '''
        self.parent.engine.logger.info('Comparing sequences and node names...')
        node_basename = os.path.basename(path)
        node_name, file_extension = os.path.splitext(node_basename)
        self.parent.engine.logger.info('node name: ' + node_name)

        remove_list = []
        for sequence in sequences:
            sequence_name = sequence.basename()
            self.parent.engine.logger.info('sequence name: ' + sequence_name)

            if sequence_name not in node_name and sequence_name != "Thumbs":
                message = "Ignoring sequence \"%s\", it has different filename from write node. "
                self.logger.warning(message % sequence.basename())

                remove_list.append(sequence)
            else:
                self.parent.engine.logger.info('valid')
        self.parent.engine.logger.info('sequences list: %s', sequences)
        self.parent.engine.logger.info('remove list: %s', remove_list)
        for r in remove_list:
            sequences.remove(r)

        return sequences

    def extract_names_from_image_path(self, file_paths):
        '''
        Extract names from list of paths formed by name.number.extension
            Args:
                (list) paths
            Returns:
                (list) extracted names
        '''
        extracted_names = []

        for file_path in file_paths:
            file_name = os.path.basename(file_path)
            base_name, extension = os.path.splitext(file_name)

            # Remove numbers, '.' and '_' from the base name using a simple regex
            # base_name_without_numbers = ''.join([char for char in base_name if not char.isdigit()])
            base_name_without_numbers = ''.join(
                [char for char in base_name if not (char.isdigit() or char == '.' or char == '_')]
            )
            self.parent.engine.logger.info('individual name: %s', base_name_without_numbers)

            extracted_names.append(base_name_without_numbers)

        return extracted_names

    def find_sequences_in_write_node_paths(self, output_paths, valid_extensions):
        '''
        Finds all valid sequences in the output paths(they should come from
        scene settings), filters with "compare_sequence_with_node()" so they match,
        and validates they have a valid extension.
            Args:
                (list) output_paths: string list
            Returns:
                (list) valid_extensions
        '''
        import fileseq

        sequences_dict = {}

        # Find all sequences in render node paths
        for path in output_paths:
            sequences = fileseq.findSequencesOnDisk(os.path.dirname(path))
            self.parent.engine.logger.info('sequences: %s',sequences)

            # Filter list of sequences that match write nodes
            sequences = self.compare_sequence_with_node(sequences, path)

            # Validate before adding to dictionary
            # - If there is only one sequence rendered and has wrong extension, raise error
            # - If there is no rendered sequence for write node, raise error
            if len(sequences) == 1:
                sequence = sequences[0]
                sequence_ext = sequence.extension()
                sequence_ext = sequence_ext.replace('.', '')

                if sequence_ext.lower() not in valid_extensions and sequence_ext.lower() != "db":
                    message = "Incorrect sequence extension for sequence: %s%s"
                    self.logger.error(message % (sequence.basename(),sequence.extension()))
                    continue
            elif len(sequences) == 0:
                message = "No rendered sequence found for write node with filename: %s, disable node or render sequence to publish."
                self.logger.error(message % os.path.basename(path))
                continue

            for sequence in sequences:
                # If sequence is rendered multiple times with
                # valid and invalid extensions, show warning and ignore invalid
                # Asume there is only one valid
                sequence_ext = sequence.extension()
                sequence_ext = sequence_ext.replace('.', '')

                if sequence_ext.lower() not in valid_extensions and sequence_ext.lower() != "db":
                    message = "Ignoring incorrect sequence extension: %s%s"
                    self.logger.warning(message % (sequence.basename(),sequence.extension()))
                    continue

                # Add sequence with name to dictionary
                sequence_text_id = sequence.basename()
                sequence_text_id = self.clean_basename_from_sequence(sequence_text_id)
                sequences_dict[sequence_text_id] = sequence

        return sequences_dict
