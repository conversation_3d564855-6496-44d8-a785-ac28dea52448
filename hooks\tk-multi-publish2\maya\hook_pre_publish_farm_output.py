import os
import json
import pprint
import time
import sgtk


HookBaseClass = sgtk.get_hook_baseclass()


class PrePublishFarmOutputHook(HookBaseClass):
    """
    Global hook to determine if an item should be published based on farm output property.

    This hook provides a mechanism to skip publishing for items that have been
    explicitly marked with a specific property.
    """

    def __init__(self, parent):
        super(PrePublishFarmOutputHook, self).__init__(parent)

    @property
    def settings(self):
        # inherit the settings from the base publish plugin
        plugin_settings = super(PrePublishFarmOutputHook, self).settings or {}
        return plugin_settings

    def publish(self, settings, item):
        output_to_farm = item.get_property("output_to_farm")

        # If the property doesn't exist, proceed with normal publication
        if not output_to_farm:
            self.parent.logger.info("Running publish for %s" % item)
            super(PrePublishFarmOutputHook, self).publish(settings, item)

        else:
            self.parent.logger.info("Skipping publish for %s" % item)
            return

    def finalize(self, settings, item):
        output_to_farm = item.get_property("output_to_farm")

        # If the property doesn't exist, proceed with normal publication
        if not output_to_farm:
            self.parent.logger.info("Running finalize for %s" % item)
            super(PrePublishFarmOutputHook, self).finalize(settings, item)

        else:
            self.parent.logger.info("Skipping publish for %s" % item)
            return

    def use_farm_or_local_processing(self, item):
        valueoverrides = self.parent.custom_frameworks.get(
            "mty-framework-valueoverrides"
        ) or self.load_framework("mty-framework-valueoverrides")

        self.parent.logger.info("parent name: {}".format(self.parent.name))

        if os.environ.get("SG_PROCESSING_SECONDARY_OUTPUTS", False):
            secondary_outputs_on_local = True
        else:
            secondary_outputs_on_local = False
            item_type = item.type_spec
            self.parent.logger.info("item_name: {}".format(item_type))
            self.parent.logger.info("item to dict: {}".format(item.to_dict()))
            engine_name = self.parent.name.lower()
            engine_name = engine_name.replace("tk-", "")
            value_code = (
                "mty.engine.{}.multi-publish" ".process_secondary_outputs_on_local"
            ).format(engine_name)

            if valueoverrides:
                value_secondary_outputs_on_local = valueoverrides.get_value(value_code)
                self.parent.logger.info(
                    "{}: {}".format(value_code, value_secondary_outputs_on_local)
                )

                dict_secondary_outputs_on_local = json.loads(
                    value_secondary_outputs_on_local
                )
                secondary_outputs_on_local = dict_secondary_outputs_on_local.get(
                    item_type, {}
                ).get("value", True)

        if not secondary_outputs_on_local:
            # update secondary_outputs_to_farm root item
            root_item = self.get_root_item(item)
            second_outputs_to_farm = root_item.properties.get(
                "secondary_outputs_to_farm", {}
            )
            dict_to_primary = {item_type: True}

            second_outputs_to_farm.update(dict_to_primary)
            root_item.properties["secondary_outputs_to_farm"] = second_outputs_to_farm

            # add properties to item
            item.properties["output_to_farm"] = True
            self.parent.logger.info(
                "{} item export on farm: {}".format(
                    item.name, str(item.properties["output_to_farm"])
                )
            )

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item
        return root
