#####################################################################################
#
# Copyright (c) 2021 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################
import os
import re
import sys
import shutil

import os
import glob
import shutil
import fnmatch
import traceback

import sgtk
from sgtk.util.filesystem import ensure_folder_exists

HookBaseClass = sgtk.get_hook_baseclass()

class HarmonyAssetSeqPublishPlugin(HookBaseClass):
    """
    Plugin for publishing an open Harmony session.
    This hook relies on functionality found in the base file publisher hook in
    the publish2 app and should inherit from it in the configuration. The hook
    setting for this plugin should look something like this::
        hook: "{self}/publish_file.py:{engine}/tk-multi-publish2/basic/publish_session.py"
    """

    # NOTE: The plugin icon and name are defined by the base file plugin.
    @property
    def icon(self):
        """
        Path to an png icon on disk
        """
        # look for icon one level up from this hook's folder in "icons" folder
        return os.path.join(
            self.disk_location,
            os.pardir,
            "icons",
            "sequence.png"
        )

    @property
    def description(self):
        """
        Verbose, multi-line description of what the plugin does. This can
        contain simple html for formatting.
        """

        loader_url = "https://support.shotgunsoftware.com/hc/en-us/articles/219033078"

        return """
        Publishes the file to Shotgun. A <b>Publish</b> entry will be
        created in Shotgun which will include a reference to the file's current
        path on disk. If a publish template is configured, a copy of the
        current session will be copied to the publish template path which
        will be the file that is published. Other users will be able to access
        the published file via the <b><a href='%s'>Loader</a></b> so long as
        they have access to the file's location on disk.
        If the session has not been saved, validation will fail and a button
        will be provided in the logging output to save the file.
        <h3>File versioning</h3>
        If the filename contains a version number, the process will bump the
        file to the next version after publishing.
        The <code>version</code> field of the resulting <b>Publish</b> in
        Shotgun will also reflect the version number identified in the filename
        The basic worklfow recognizes the following version formats by default:
        <ul>
        <li><code>filename.v###.ext</code></li>
        <li><code>filename_v###.ext</code></li>
        <li><code>filename-v###.ext</code></li>
        </ul>
        After publishing, if a version number is detected in the work file, the
        work file will automatically be saved to the next incremental version
        number. For example, <code>filename.v001.ext</code> will be published
        and copied to <code>filename.v002.ext</code>
        If the next incremental version of the file already exists on disk, the
        validation step will produce a warning, and a button will be provided
        in the logging output which will allow saving the session to the next
        available version number prior to publishing.
        <br><br><i>NOTE: any amount of version number padding is supported. for
        non-template based workflows.</i>
        <h3>Overwriting an existing publish</h3>
        In non-template workflows, a file can be published multiple times,
        however only the most recent publish will be available to other users.
        Warnings will be provided during validation if there are previous
        publishes.
        """ % (
            loader_url,
        )

    @property
    def item_filters(self):
        """
        List of item types that this plugin is interested in.
        Only items matching entries in this list will be presented to the
        accept() method. Strings can contain glob patters such as *, for
        example ["harmony.*", "file.harmony"]
        """
        return ["harmony.asset_sequence"]


    def accept(self, settings, item):
        return {"accepted": True, "checked": True}


    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish. Returns a
        boolean to indicate validity.
        :param settings: Dictionary of Settings. The keys are strings, matching
                         the keys returned in the settings property. The values
                         are `Setting` instances.
        :param item: Item to process
        :returns: True if item is valid, False otherwise.
        """

        ctx= self.parent.context
        publisher= self.parent
        path= _session_path()

        # --- ensure the session has been saved
        if not path:
            error_msg = "The Harmony session has not been saved"
            raise Exception(error_msg)

        #validate frames
        if 'asset_seq_frames' not in item.properties.keys():
            raise Exception('Missing asset sequence. Check collector')

        #validating template
        for t in ["template_seq_work", "template_seq_publish"]:
            if t not in item.properties.keys():
                raise Exception('Missing Harmony asset sequence template, publish and work')

        # Checking work area info
        if 'work_fields' not in item.properties.keys():
            raise Exception('Invalid work area info. Check collector.')

        elif not len(item.properties['work_fields']):
            raise Exception('Invalid work area info.')

        return True

    def publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.
        :param settings: Dictionary of Settings. The keys are strings, matching
                         the keys returned in the settings property.
                         The values are `Setting` instances.
        :param item: Item to process
        """
        self.logger.info("Publishing Asset image Sequence.")
        self.parent.logger.info("Publishing Asset image Sequence.")

        # get the path in a normalized state. no trailing separator, separators
        # are appropriate for current os, no double separators, etc.
        path      = sgtk.util.ShotgunPath.normalize(_session_path())
        engine    = sgtk.platform.current_engine()
        ctx       = self.parent.context
        publisher = self.parent

        #Get item properties-----------
        frames = item.properties["asset_seq_frames"]
        newName = item.properties ["tokenName"]

        publish_asset_seq_template = item.properties["template_seq_publish"]
        work_asset_seq_template = item.properties["template_seq_work"]
        work_fields = item.properties["work_fields"]

        # movin' sequence frames to publish area
        for frame in frames:
            work_fields["tokenName"] = newName
            work_fields['frame'] = frame
            work_fields['SEQ'] = frame
            work_frame_path = work_asset_seq_template.apply_fields(work_fields)
            publish_frame_path = publish_asset_seq_template.apply_fields(work_fields)
            publish_folder = os.path.dirname(publish_frame_path)

            # Ensure folder exists
            if not os.path.exists(publish_folder):
                self.parent.engine.ensure_folder_exists(publish_folder)
            shutil.move(work_frame_path  , publish_frame_path)

        # set some placeholders
        asset_id = ctx.entity['id']
        asset_name = ctx.entity['name']
        padding = int(work_asset_seq_template.keys['frame'].format_spec)

        # get new paths from templates and fields
        work_fields['frame'] = frames[0]
        str_num = str(work_fields['frame']).zfill(padding)

        publish_seq_path = publish_asset_seq_template.apply_fields(work_fields)

        #replace first frame with the padding
        publish_seq_path = publish_seq_path .replace(
            '_{}.'.format(str_num), '_%0{}d.'.format(padding))

        # get publish path
        session_publish_template = item.properties['template_session_publish']
        publish_path = session_publish_template.apply_fields(work_fields)

        # Now publish the versioned sequence
        # we just need to override some properties
        item.properties["path"]             = publish_seq_path
        item.properties['work_template']    = work_asset_seq_template
        item.properties['publish_template'] = publish_asset_seq_template

        # item_name = self.get_publish_name(settings, item).replace(
        #     '_%0{}d.'.format(padding),
        #     '_[{}-{}].'.format(frames[0], frames[-1]))
        item_name = self.get_publish_name(settings, item)

        # Remove version token and sequence token
        pattern1 = re.compile(
            r"(?P<basename>.+)(?P<remove>_v\d{3}\.%\d{2}d)(?P<extension>\.\w{3})"
        )
        pattern2 = re.compile(
            r"(?P<basename>.+)(?P<remove>_v\d{3}\.\d{4})(?P<extension>\.\w{3})"
        )

        match = re.match(pattern1, item_name) or re.match(pattern2, item_name)

        if match:
            item_name = "{}{}".format(
                match.groupdict().get("basename"),
                match.groupdict().get("extension"),
            )

        # ver_str   = 'v{}'.format(str(work_fields['version']).zfill(3))
        # item_name = '_'.join([asset_name, ver_str, item_name])

        self.parent.logger.info("Asset sequence item name: {}".format(item_name))

        publish_data = {
            "tk":                   self.parent.engine.sgtk,
            "context":              item.context,
            "comment":              item.description,
            "path":                 publish_seq_path,
            "name":                 item_name,
            # "created_by":           self.get_publish_user(settings, item),
            "version_number":       work_fields['version'],
            "thumbnail_path":       item.get_thumbnail_as_path(),
            "task":                 self.parent.engine.context.task,
            "dependency_paths":     [publish_path],
            "sg_fields":            {"sg_status_list": "rev"},
            "published_file_type":  "Sequence Review",
            # "dependency_ids":       [],
        }

        # Register the publish of the original PNG, so that we can have the pointer
        sg_publishes = sgtk.util.register_publish(**publish_data)

        item.properties.sg_publish_data = sg_publishes
        item.properties.sg_publish_path = sg_publishes["path"]["local_path"]

        # finally just store the publish data for later retrieval
        # and upload to the host storage location

        root_item = self.get_root_item(item)
        self.parent.logger.debug(
            'Storing extra publish data on root item: {}'.format(root_item)
        )
        root_item.properties.setdefault('sg_publish_extra_data', [])
        publish_extra_data = root_item.properties['sg_publish_extra_data']
        publish_extra_data.append(sg_publishes)

        # Update task status for revision
        sg_task = self.parent.context.task
        self.parent.logger.info(
            "End of Publish and updating task with id: {} to status: 'rev'".format(
                str(sg_task['id'])
            )
        )

        try:
            self.parent.engine.shotgun.update(
                "Task", sg_task["id"], {"sg_status_list": 'rev'}
            )
        except:
            pass


    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed, and can for example be used to version up files.
        :param settings: Dictionary of Settings. The keys are strings, matching
                         the keys returned in the settings property.
                         The values are `Setting` instances.
        :param item: Item to process
        """

        # do the base class finalization
        #super(HarmonySessionPublishPlugin, self).finalize(settings, item)
        self.logger.debug('Item sequence successfully published')
        # bump the session file to the next version
        #self._save_to_next_version(item.properties["path"], item, _save_session)

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item
        return root


def _session_path():
    """
    Return the path to the current session
    :return:
    """
    engine = sgtk.platform.current_engine()

    # get the path to the current file
    path = engine.app.get_current_project_path()

    if isinstance(path, unicode):
        path = path.encode("utf-8")

    return path

