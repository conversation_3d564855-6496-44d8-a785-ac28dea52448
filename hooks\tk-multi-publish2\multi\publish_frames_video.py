#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

import os
# import re
import json
import pprint
import traceback
import subprocess

import sgtk
from sgtk.platform.qt import QtGui, QtCore

# try:
#     import hou
# except:
#     pass

HookBaseClass = sgtk.get_hook_baseclass()


pp = pprint.pprint
pf = pprint.pformat


class PublishReviewPlugin(HookBaseClass):
    """
    Plugin for publish rendered sequence

    """

    @property
    def icon(self):
        """
        Path to an png icon on disk
        """

        # look for icon one level up from this hook's folder in "icons" folder
        return os.path.join(self.disk_location, os.pardir, "icons", "review.png")

    @property
    def name(self):
        """
        One line display name describing the plugin
        """
        return "Publish sequence files"

    @property
    def description(self):
        """
        Copy/upload the render sequence to publish area
        """

        loader_url = "https://support.shotgunsoftware.com/hc/en-us/articles/219033078"

        return """
        <p>This plugin upload the video as a version in shotgun.</p>
        """

    @property
    def settings(self):
        """
        Dictionary defining the settings that this plugin expects to recieve
        through the settings parameter in the accept, validate, publish and
        finalize methods.

        A dictionary on the following form::

            {
                "Settings Name": {
                    "type": "settings_type",
                    "default": "default_value",
                    "description": "One line description of the setting"
            }

        The type string should be one of the data types that toolkit accepts
        as part of its environment configuration.
        """

        # settings specific to this class
        publish_settings = {
            "Work Template": {
                "type": "template",
                "default": "harmony_shot_work",
                "description": "Harmony work session",
            },
            "Primary Publish Template": {
                "type": "template",
                "default": "harmony_shot_publish",
                "description": "Harmony publish session",
            },
            "Publish Render Template": {
                "type": "template",
                "default": "shot_flat_render_publish_exr",
                "description": "Template where render sequence files got published",
            },
            "Publish Review Template": {
                "type": "template",
                "default": "shot_flat_render_publish_mov",
                "description": "Template to create a mov file",
            },
            "Publish Review Type": {
                "type": "str",
                "default": "Media Review",
                "description": "Published File Type name to use for the published movie",
            },
            "Image Sequence Workflow": {
                "type": "str",
                "default": "grouped",
                "description": "Define the workflow to collect image sequences,"
                " which can be either 'individual' or 'grouped'",
            },
            "Editorial Video Opacity": {
                "type": "float",
                "default": None,
                "description": "Value of opacity for editorial overlay",
            },
        }

        # inherit the settings from the base publish plugin
        plugin_settings = super(PublishReviewPlugin, self).settings or {}

        # update the base settings
        plugin_settings.update(publish_settings)
        return plugin_settings

    @property
    def item_filters(self):
        """
        List of item types that this plugin is interested in.

        Only items matching entries in this list will be presented to the
        accept() method. Strings can contain glob patters such as *, for example
        ["fusion.*", "file.fusion"]
        """
        return ["harmony.frames_sequence", "maya.frames_sequence"]

    def accept(self, settings, item):
        """
        Method called by the publisher to determine if an item is of any
        interest to this plugin. Only items matching the filters defined via the
        item_filters property will be presented to this method.

        A submit for review task will be generated for each item accepted here. Returns a
        dictionary with the following booleans:

            - accepted: Indicates if the plugin is interested in this value at
                all. Required.
            - enabled: If True, the plugin will be enabled in the UI, otherwise
                it will be disabled. Optional, True by default.
            - visible: If True, the plugin will be visible in the UI, otherwise
                it will be hidden. Optional, True by default.
            - checked: If True, the plugin will be checked in the UI, otherwise
                it will be unchecked. Optional, True by default.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process

        :returns: dictionary with boolean keys accepted, required and enabled.
            This plugin makes use of the tk-multi-reviewsubmission app; if this
            app is not available then the item will not be accepted, this method
            will return a dictionary with the accepted field that has a value of
            False.

            Several properties on the item must also be present for it to be
            accepted. The properties are 'path', 'publish_name', 'color_space',
            'first_frame' and 'last_frame'
        """

        publisher = self.parent
        engine = publisher.engine
        context = engine.context

        # Verify entity type SHOT
        entity_type = context.entity.get("type", None) or str(context).split(" ")[1]
        if entity_type != "Shot":
            return {"accepted": False}

        layer = item.properties.get('sequence_text_id')
        aov = item.properties.get('sequence_text2_id')

        avoid_aovs = ["Cryptomatte"]
        if layer in avoid_aovs or aov in avoid_aovs:
            return {"accepted": False}

        return {"accepted": True, "checked": True}

    def validate(self, settings, item):
        """
        Validates the given item to check that it is ok to publish. Returns a
        boolean to indicate validity.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        :returns: True if item is valid and not in proxy mode, False otherwise.
        """

        # get latest approved audio. If there's no approved audio, it returns None
        sg_audio_data = self.get_sg_editorial_info()
        if not sg_audio_data:
            # Editorial audio is missing, or there's no approved audio publish
            raise Exception(
                "Couldn't find an approved Editorial Audio published file for the Shot."
            )

        shot_cutin = sg_audio_data.get("entity.Shot.sg_cut_in", False)
        shot_cutout = sg_audio_data.get("entity.Shot.sg_cut_out", False)
        last_published_audio = sg_audio_data.get("path", False)

        # Audio validation
        if last_published_audio:
            last_published_audio_path = last_published_audio.get("local_path", None)
            if last_published_audio_path:
                last_published_audio_path = last_published_audio_path.replace("\\", "/")
        else:
            # Editorial audio is missing, or there's no approved audio publish
            raise Exception(
                "Couldn't find an approved Editorial Audio published file for the Shot."
            )

        # ensure file exists locally
        if not os.path.exists(last_published_audio_path):
            self.ensure_file_is_local(last_published_audio_path, sg_audio_data)

        item.properties["audio_path"] = last_published_audio_path
        item.properties["cutin"] = shot_cutin
        item.properties["cutout"] = shot_cutout

        # Load value overrides framework -----------------------------------------------
        valueoverrides = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        ) or self.load_framework("mty-framework-valueoverrides")

        if not valueoverrides:
            self.parent.engine.logger.error("Couldn't load value overrides framework")

        # init sequence length validation value, by default is set to True
        validate_sequence_length = True

        if valueoverrides:
            # Get task priority list
            override_link = {
                "type": "Task",
                "id": self.parent.engine.context.task["id"],
            }

            self.parent.engine.logger.info(
                "current link for validations: {}".format(override_link)
            )

            default_value_code = (
                "mty.multi.publish2.frames_sequence.validate"
            )
            validate_sequence_length = valueoverrides.get_value(
                default_value_code, link=override_link
            )
            self.parent.engine.logger.info(
                "Got validate_sequence_length from override: {}".format(
                    validate_sequence_length
                )
            )

        self.parent.engine.logger.info(
            "Will validate_sequence_length when creating video?: {}".format(
                validate_sequence_length
            )
        )

        if validate_sequence_length:
            sequence = item.properties["sequence"]

            # Harmony fames can only start at 1
            # we can only validate if the number of frames matches shot
            sequence_frames = len(sequence)

            shot_frames = shot_cutout - shot_cutin + 1

            if sequence_frames != shot_frames:
                error_msg = "Shot lenght and render frames don't match: {0} - {1}"
                raise Exception(error_msg.format(shot_frames, sequence_frames))

            # Other known issue is if the render naming convention uses "-"
            # that will cause conflict with the image sequence recognition
            # and will make the numbers look like negative, which will cause problems
            if sequence.start() < 0 or sequence.end() < 0:
                error_msg = (
                    "It looks like you have negative numbers!\n"
                    "This is mostly because of your frames naming "
                    "convention, just make sure that you are not using '-'"
                )
                raise Exception(error_msg)

        if self.parent.context.entity.get("type") == "Shot":
            result = self.collect_editorial_preview(item)
            if not result:
                error_msg = "Can't obtain a local copy of latest editorial preview"
                self.logger.error(error_msg)

                # return False
                raise Exception(error_msg)

        return True

    def publish(self, settings, item):
        """
        Executes the publish logic for the given item and settings.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """

        self.parent.engine.logger.info("Starting video publish...".ljust(80, "-"))

        self.parent.engine.logger.debug("Item name: {}".format(item.name))
        self.parent.engine.logger.debug("Item dict:\n{}".format(pf(item.to_dict())))
        self.parent.engine.logger.debug("Item properties:")
        for prop in item.properties:
            self.parent.engine.logger.debug("{}: {}".format(prop, item.properties[prop]))

        #   Video process
        engine = self.parent.engine
        path = item.properties["scene_path"]
        sequence = item.properties["sequence"]
        layer_name = item.properties["sequence_text_id"]
        buffer_name = item.properties.get("sequence_text2_id", None)
        tpl_by_name = engine.get_template_by_name

        # Gettiings settings
        work_template_setting = settings.get("Work Template")
        primary_publish_template_setting = settings.get("Primary Publish Template")
        render_publish_template_setting = settings.get("Publish Render Template")
        review_publish_template_setting = settings.get("Publish Review Template")
        review_publish_type_setting = settings.get("Publish Review Type")
        self.opacity_editorial_vid = settings.get("Editorial Video Opacity").value

        image_sequence_workflow = settings.get("Image Sequence Workflow").value

        # Getting templates
        work_template = tpl_by_name(work_template_setting.value)
        primary_publish_template = tpl_by_name(primary_publish_template_setting.value)
        render_publish_template = tpl_by_name(render_publish_template_setting.value)
        review_publish_template = tpl_by_name(review_publish_template_setting.value)

        # Work area fields
        fields = work_template.get_fields(path)

        # optionally, if we need to add the layer name and buffer name
        self.parent.logger.info(
            "image_sequence_workflow: {}".format(image_sequence_workflow)
        )
        if image_sequence_workflow == "individual":
            fields["layer"] = layer_name
            # buffer name is used when the renders are aovs of a main render layer
            # usually used in maya, houdini and other 3d DCCs
            if buffer_name:
                fields["buffer"] = buffer_name

        self.parent.logger.info(
            "fields before applying them to review publish template:\n{}".format(
                pprint.pformat(fields)
            )
        )

        # Dynamic paths or elements from templates
        primary_publish_path = primary_publish_template.apply_fields(fields)
        review_publish_path = review_publish_template.apply_fields(fields)

        self.parent.logger.info(
            "review_publish_path after applying fields: {}".format(review_publish_path)
        )
        self.parent.logger.info(
            "sequence: {}, type: {}".format(sequence, type(sequence))
        )
        # specify the extention as used from the image sequence
        fields["extension"] = sequence.extension().lower().replace(".", "")
        render_publish_path = render_publish_template.apply_fields(fields)

        review_publish_type_name = review_publish_type_setting.value

        publish_name = self.get_publish_name(review_publish_path)
        publish_version = fields["version"]
        # padding = len(sequence[0].split('.')[1])
        sg_proj = self.get_sg_project_info(
            ["sg_working_color_space", "sg_output_color_space", "sg_fps"]
        )

        # source sequence to convert
        source_seq = os.path.normpath(
            "".join(
                [
                    sequence.dirname(),
                    sequence.basename(),
                    "%0{}d".format(sequence._zfill),
                    sequence.extension(),
                ]
            )
        )

        shot_cutin = item.properties["cutin"]
        shot_cutout = item.properties["cutout"]

        # Converting exr to png files
        # Conversion to other colorspace
        seq_first_frame = sequence.start()
        seq_last_frame = sequence.end()
        srgb_seq = self.convert_seq_to_srgb(
            settings, item, source_seq, sg_proj, seq_first_frame, sequence
        )
        if not srgb_seq:
            # Second try
            srgb_seq = self.convert_seq_to_srgb(
                settings, item, source_seq, sg_proj, seq_first_frame, sequence
            )
        if not srgb_seq:
            message = "Failed to png sequence, check sequence: %s"
            raise Exception(message % (source_seq))

        # Audio section
        audio_path = item.properties["audio_path"]
        # audio_cmd_1 = ""
        # audio_cmd_2 = ""
        # if os.path.exists(audio_path):
        #     audio_cmd_1 = ' -i "{}"'.format(audio_path)

        #     # not used:
        #     # audio_cmd_2  = ' -c:a aac'
        #     # audio_cmd_2  = ' -acodec pcm_s16le'
        #     # audio_cmd_2 += ' -aframes 0'
        #     # audio_cmd_2 += ' -map 1:a'

        #     audio_cmd_2 = " -acodec pcm_s16le"

        # Check if video folder exists
        self.parent.engine.ensure_folder_exists(os.path.dirname(review_publish_path))

        # message = "Creating video from png files: {}"
        # self.logger.info(message.format(review_publish_path))

        # # its important that the video filter goes
        # # last to preserve the color matrix
        # ffmpeg_str = "-start_number {FRAME}"
        # ffmpeg_str += " -r {FRAME_RATE}"
        # ffmpeg_str += " -i {SEQ}"
        # ffmpeg_str += audio_cmd_1
        # # ffmpeg_str += " -s {RESOLUTION}"
        # ffmpeg_str += " -vcodec libx264"
        # ffmpeg_str += " -pix_fmt yuv420p"
        # ffmpeg_str += audio_cmd_2
        # # ffmpeg_str  += " -map 0:v"
        # ffmpeg_str += " -crf 5"
        # ffmpeg_str += " -r {FRAME_RATE}"
        # # ffmpeg_str += ' -vf "colorspace=bt709:iall=bt601-6-625:fast=1; scale={RESOLUTION_X}:{RESOLUTION_Y}"'
        # ffmpeg_str += " -y"
        # ffmpeg_str += " {VIDEO}"

        # ffmpeg_cmd = ffmpeg_str.format(
        #     FRAME=str(sequence.start()),
        #     SEQ=srgb_seq,
        #     FRAME_RATE=sg_proj["sg_fps"],
        #     VIDEO=review_publish_path,
        #     RESOLUTION_X=proj_res_x,
        #     RESOLUTION_Y=proj_res_y,
        # )

        # ffmpeg_framework = self.load_framework("mty-framework-ffmpeg")
        # ffmpeg_framework.ffmpegCore.set_binary("convert")

        # _err, _info = ffmpeg_framework.ffmpegCore.execute_command(ffmpeg_cmd)
        # if _err:
        #     message = "Failed to create video: %s\ncommand: %s"
        #     self.parent.logger.info(message % (_info, ffmpeg_cmd))
        #     raise Exception(message % (_info, ffmpeg_cmd))


        mediatools = self.load_framework("mty-framework-mediatools")

        #get editorial path
        editorial_preview_path = item.properties.get("editorial_preview")
        # if not editorial_preview_path:
        #     editorial_preview_path = ""

        self.transcode_and_format_clip(
            review_publish_path,
            srgb_seq,
            audio_path,
            editorial_preview_path,
            sequence.start(),
            shot_cutin,
            shot_cutout,
            fields["Shot"],
            publish_version,
            mediatools,
            sg_proj['sg_fps'],
        )

        # get media resolution
        media_resolution = self.parent.execute_hook_expression(
            "{config}/tk-multi-publish2/multi/resolution_from_path.py",
            "media_resolution_value",
            path=review_publish_path
        )
        self.parent.logger.info(
            "media resolution from hook: {}".format(media_resolution)
        )

        # Cleaning pngs
        offset = shot_cutin - sequence.start()
        last_f = shot_cutout - offset
        self.logger.info("Cleaning temporal files")
        for i in range(sequence.start(), last_f + 1):
            cur_frame = srgb_seq % i
            if os.path.exists(cur_frame):
                self.parent.log_debug("Removing: %s" % cur_frame)
                os.remove(cur_frame)

        #   Publish video process
        args = {
            "tk": self.parent.engine.sgtk,
            "context": item.context,
            "comment": item.description,
            "path": review_publish_path,
            "name": publish_name,
            "version_number": publish_version,
            "thumbnail_path": item.get_thumbnail_as_path(),
            "task": self.parent.engine.context.task,
            "dependency_paths": [primary_publish_path, render_publish_path],
            "sg_fields": {"sg_status_list": "rev",
                          "sg_media_resolution": media_resolution},
            "published_file_type": review_publish_type_name,
        }

        sg_publishes = sgtk.util.register_publish(**args)

        item.properties.sg_publish_data = sg_publishes
        item.properties.sg_publish_path = sg_publishes["path"]["local_path"]

        self.parent.log_debug(
            "Adding existing publish "
            + "(id:{}) ".format(sg_publishes["id"])
            + "as extra data for item: {}".format(item)
        )

        #   Version process Ref publish_shot_media_review.py from maya/layout
        sg_version = self.submit_version(
            render_publish_path,
            review_publish_path,
            [sg_publishes],
            self.parent.engine.context.task,
            item.description,
            int(shot_cutin),
            int(shot_cutout),
        )

        # Upload in a new thread and make our own event loop
        # to wait for the thread to finish.
        event_loop = QtCore.QEventLoop()
        thread = UploaderThread(
            self.parent,
            sg_version,
            review_publish_path,
            item.get_thumbnail_as_path(),
            True,
        )

        thread.finished.connect(event_loop.quit)
        thread.start()
        event_loop.exec_()

        # log any errors generated in the thread
        for e in thread.get_errors():
            self.parent.log_error(e)

        # Update task status for revision
        sg_task = self.parent.context.task
        self.parent.logger.info(
            "End of Publish and updating task with id: {} to status: 'rev'".format(
                str(sg_task["id"])
            )
        )

        try:
            self.parent.engine.shotgun.update(
                "Task", sg_task["id"], {"sg_status_list": "rev"}
            )
        except:
            pass

    def finalize(self, settings, item):
        """
        Execute the finalization pass. This pass executes once all the publish
        tasks have completed.

        :param settings: Dictionary of Settings. The keys are strings, matching
            the keys returned in the settings property. The values are `Setting`
            instances.
        :param item: Item to process
        """
        self.logger.debug("Item video successfully published")

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root

    def submit_version(
        self,
        path_to_frames,
        path_to_movie,
        sg_publishes,
        sg_task,
        comment,
        first_frame,
        last_frame,
    ):
        """
        Create a version in Shotgun for this path and linked to this publish.
        """

        # get current shotgun user
        current_user = self.parent.engine.context.user

        # create a name for the version based on the file name
        # grab the file name, strip off extension
        name = os.path.splitext(os.path.basename(path_to_movie))[0]
        # do some replacements and capitalize
        # name = name.replace("_", " ").capitalize()
        # avoid calling capitalize() to keep the name consistent with the corresponding
        # published files.
        name = name.replace("_", " ")

        LinkFolder = {
            "local_path": os.path.dirname(path_to_frames) + os.sep,
            "content_type": None,
            "link_type": "local",
            "name": name,
        }

        LinkFile = {
            "local_path": path_to_movie,
            "content_type": None,
            "link_type": "local",
            "name": name,
        }

        entity = self.parent.engine.context.entity
        proj = self.parent.engine.context.project
        # Create the version in Shotgun
        data = {
            "code": name,
            "sg_status_list": "rev",
            "entity": entity,
            "sg_task": sg_task,
            "sg_version_type": "Production",
            "sg_first_frame": first_frame,
            "sg_last_frame": last_frame,
            "frame_count": (last_frame - first_frame + 1),
            "frame_range": "%s-%s" % (first_frame, last_frame),
            "sg_frames_have_slate": False,
            "published_files": sg_publishes,
            "created_by": current_user,
            "description": comment,
            "sg_path_to_frames": path_to_frames,
            "sg_path_to_movie": path_to_movie,
            "sg_movie_has_slate": False,
            "project": proj,
            "user": current_user,
        }

        sg_version = self.parent.engine.shotgun.create("Version", data)
        self.parent.log_debug("Created version in shotgun: %s" % str(data))

        return sg_version

    def get_sg_editorial_info(self):
        sg_proj = self.parent.engine.context.project
        shotgun = self.parent.engine.shotgun
        current_entity = self.parent.engine.context.entity

        order = [{"field_name": "version_number", "direction": "desc"}]
        sg_qry = shotgun.find_one(
            "PublishedFile",
            filters=[
                ["project", "is", sg_proj],
                ["published_file_type.PublishedFileType.code", "is", "Editorial Audio"],
                ["entity.Shot.id", "is", current_entity["id"]],
                ["sg_status_list", "is", "apr"],
            ],
            fields=[
                "path",
                "entity.Shot.sg_cut_in",
                "entity.Shot.sg_cut_out",
                "entity",
                "version_number",
            ],
            order=order,
        )

        return sg_qry

    def ensure_file_is_local(self, path, publish):
        if not hasattr(self, "metasync"):
            self.metasync = self.load_framework("mty-framework-metasync")

        transfersManager = self.metasync.transfersManager
        if "%" in path:
            dirname = os.path.dirname(path)
            if os.path.exists(dirname) and self._collect_sequenced_files(path):
                transfersManager.ensure_local_dependencies(publish)
                return path
        else:
            if os.path.exists(path):
                transfersManager.ensure_local_dependencies(publish)
                return path

        transfersManager.ensure_file_is_local(path, publish)
        transfersManager.ensure_local_dependencies(publish)

        return path

    def transcode_and_format_clip(
            self,
            mov_file_path,
            sequence_file_path,
            audio_path,
            editorial_preview_path,
            first_fileseq_frame,
            shot_start_frame,
            shot_end_frame,
            context_name,
            version,
            mediatools,
            project_fps
    ):

        self.parent.logger.debug(
            (
                "transcode_and_format_clip:\n"
                "      mov_file_path: {0}\n"
                "      sequence_file_path: {1}\n"
                "      audio_path: {10}\n"
                "      editorial_preview_path: {2}\n"
                "      first_fileseq_frame: {3}\n"
                "      shot_start_frame: {4}\n"
                "      shot_end_frame: {5}\n"
                "      context_name: {6}\n"
                "      version: {7}\n"
                "      mediatools: {8}\n"
                "      project_fp: {9}\n"
            ).format(
                mov_file_path,
                sequence_file_path,
                editorial_preview_path,
                first_fileseq_frame,
                shot_start_frame,
                shot_end_frame,
                context_name,
                version,
                mediatools,
                project_fps,
                audio_path
            )
        )

        step_name = self.parent.context.step['name']
        task_name = self.parent.context.task['name']
        engine_name = self.parent.engine.name

        # load overrides framework -----------------------------------------------------
        overrides_framework = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        )
        if not overrides_framework:
            overrides_framework = self.load_framework("mty-framework-valueoverrides")
        # # ------------------------------------------------------------------------------

        default_value_overlay = "mty.publisher.tasks_without_video_overlay"

        tasks_without_overlay_hidden = overrides_framework.get_value(
            default_value_overlay
        )
        self.parent.logger.info(
            "tasks without editorial overlay: {}, type: {}".format(
                tasks_without_overlay_hidden, type(tasks_without_overlay_hidden)
            )
        )
        if tasks_without_overlay_hidden:
            tasks_without_overlay_hidden = json.loads(tasks_without_overlay_hidden)
        else:
            tasks_without_overlay_hidden = []

        # create a custom input stream to control certain parameters
        media_stream = mediatools.Modifier.create_input_stream(
            sequence_file_path, start_number=first_fileseq_frame, r=project_fps,
            probesize=u'10M'
        )

        # create a custom input stream for the audio
        audio_stream = mediatools.Modifier.create_input_stream(audio_path)

        if task_name not in tasks_without_overlay_hidden:
        # to extract the audio from
            if editorial_preview_path:

                # create a temp input stream to get the audio
                media_stream_temp = mediatools.Modifier.create_input_stream(
                    editorial_preview_path, r=project_fps, probesize=u'10M'
                )

                # split the video and audio streams to use them independently
                # we will only modify the video stram. Audio won't be used as
                # we will use the editorial audio published file
                video_stream = media_stream.video
                # audio_stream = media_stream_temp.audio

                overlay_data = {
                    'supD': {
                        'file_path': editorial_preview_path,
                    }
                }

                # set opacity for editorial video overlay
                opacity_override = "mty.publisher.editorial_overlay_opacity"

                opacity_override_value = overrides_framework.get_value(
                    opacity_override
                )
                self.parent.logger.info(
                    "Editorial overlay editorial: {}".format(
                        opacity_override_value
                    )
                )
                if opacity_override_value:
                    overlay_filter= {"opacity": float(opacity_override_value)}

                else:
                    overlay_filter= {"opacity": self.opacity_editorial_vid}



                media_stream = mediatools.Modifier.create_quadrants_media_overlay(
                    video_stream, overlay_data, media_filters=overlay_filter
                )
            # define the text overlay options using quadrants_text_overlay
            # which will automatically position the text in proper sections
            # and it will also include handy text formatting defaults

            # set start frame for overlay counter
            startframe_for_sequence = "mty.publisher.overlay_startframe_from_sequence_dcc"

            startframe_for_sequence_dcc = overrides_framework.get_value(
                startframe_for_sequence
            )
            self.parent.logger.info(
                "list of DCCs that will use start frame from sequence: {}".format(
                    startframe_for_sequence_dcc
                )
            )
            if startframe_for_sequence_dcc:
                dccs_start_with_sequence_framenumber= json.loads(
                    startframe_for_sequence_dcc
                )
                if engine_name in dccs_start_with_sequence_framenumber:
                    shot_start_frame = first_fileseq_frame

            text_data = {
                'supA': str(context_name),
                'supB': '{0}-{1}'.format(step_name, task_name),
                'supC': "v{:0>3}".format(version),
                'infD':'{frame_number}:%s' % shot_start_frame,
            }

            media_stream = mediatools.Modifier.create_quadrants_text_overlay(
                text_data, media_stream, font_size=32
            )

        # finally concatenate the modified video and the original audio
        # if editorial_preview_path:
        media_stream = mediatools.Modifier.ffmpeg.concat(
            media_stream, audio_stream, v=1, a=1
        )

        self.parent.engine.logger.debug("media_stream: {}".format(media_stream))
        self.parent.engine.logger.debug("mov_file_path: {}".format(mov_file_path))
        self.parent.engine.logger.debug("project_fps: {}".format(project_fps))
        # and transcode it with a preset and some extra custom parameters
        mediatools.Transcoder.transcode(
            media_stream, mov_file_path, 'h264_high_yuv420p',
            r=project_fps, max_muxing_queue_size=2048
        )

    def convert_seq_to_srgb(
        self,
        settings,
        item,
        source_seq,
        sg_proj,
        seq_first_f,
        fileseq_obj=None):
        # setup oiio framework to first, check if the sequences needs to be color
        # converted based on its bit depth, and seccond, if it needs to be converted,
        # to execute that conversion.
        oiio = self.load_framework("mty-framework-openimageio")
        oiio_tool = oiio.openImageIOCore
        oiio_tool.set_binary("oiiotool")
        oiio_exe = oiio_tool.get_bin_path()

        # check if image is floating point, thus needs conversion, or return the
        # original image
        try:
            output = subprocess.check_output(
                [oiio_exe, "--info", "--frames", str(seq_first_f), source_seq],
                stderr=subprocess.STDOUT,
                encoding="utf-8",
            )

            image_file = os.path.basename(source_seq)
            self.parent.engine.logger.info("Processing {}".format(image_file))
            # print("output:\n{}".format(output))

            # Check if the output indicates the image is floating point
            if not " half " in output and not " float " in output:
                msg = "{} is not floating point. Skipping conversion".format(image_file)
                self.logger.info(msg)
                self.parent.engine.logger.info(msg)
                return source_seq
            else:
                msg = "{} is floating point. Requires conversion.".format(image_file)
                self.logger.info(msg)
                self.parent.engine.logger.info(msg)
        except subprocess.CalledProcessError as e:
            msg = "Error processing {}: {}\nFull traceback:\n{}".format(
                    image_file, e.output, traceback.format_exc()
                )
            self.logger.error(msg)
            self.parent.engine.logger.error(msg)

        # If we got to this point, either the sequence needs conversion or oiiotool
        # failed to process it, so as a fallback, it will be converted to sRGB

        # Element used to convert exr to png for colorconverting
        ocio = self.load_framework("mty-framework-opencolorio")
        ocio_path = ocio.disk_location + "/version/aces_1_2/config.ocio"
        frames_limit = 15

        all_colorspaces = {
            "sRGB": "Output - sRGB",
            "rec709": "Output - Rec.709",
            "aces_cg": "ACES - ACEScg",
        }

        if fileseq_obj:
            fr_start = int(fileseq_obj.start())
            fr_end = int(fileseq_obj.end())
        else:
            fr_start = int(item.properties["cutin"])
            fr_end = int(item.properties["cutout"])
        total_frames = fr_end - fr_start
        oiio_jobs = int(total_frames / frames_limit) + 1

        self.parent.engine.logger.info(
            "start frame: {}, end frame: {}".format(fr_start, fr_end)
        )
        self.parent.engine.logger.info("total frames: {}".format(total_frames))
        self.parent.engine.logger.info("total oiio_jobs: {}".format(oiio_jobs))

        # Offset frames to sequence files range
        offset = fr_start - seq_first_f
        fr_start = seq_first_f
        fr_end = fr_end - offset

        # png with the color already converted
        source_seq_split = source_seq.split(".")
        head = ".".join(source_seq_split[:-1])
        temp_seq = ".".join(
            ["{}.temp".format(head), "png"]
        )
        self.parent.engine.logger.info("temp_seq: {}".format(temp_seq))

        # Cleaning previous files
        folder_container = os.path.dirname(temp_seq)

        suffixes = [".temp.png", "_temp.png"]

        all_elements = os.listdir(folder_container)
        for sub_element in all_elements:
            if len(sub_element.split(".")) <= 3:
                continue
            if sub_element.endswith(tuple(suffixes)):
                full_delete_path = os.path.join(folder_container, sub_element)
                self.parent.engine.logger.info("Removing: {}".format(full_delete_path))
                os.remove(full_delete_path)

        # Using valid color space name
        proj_working_colorspace = sg_proj["sg_working_color_space"]
        if proj_working_colorspace in all_colorspaces.keys():
            proj_working_colorspace = all_colorspaces[proj_working_colorspace]

        proj_output_colorspace = sg_proj["sg_output_color_space"]
        if proj_output_colorspace in all_colorspaces.keys():
            proj_output_colorspace = all_colorspaces[proj_output_colorspace]

        self.parent.engine.logger.info(
            "proj_working_colorspace translated: {}".format(proj_working_colorspace)
        )
        self.parent.engine.logger.info(
            "proj_output_colorspace translated: {}".format(proj_output_colorspace)
        )

        # Split the process in parts because it fails after frame 32
        for i in range(0, oiio_jobs):
            current_first_f = str(fr_start + (frames_limit * i))
            if i == oiio_jobs - 1:
                current_last_f = str(fr_end)
            else:
                current_last_f = str(fr_start + (frames_limit * (i + 1)) - 1)

            # oiiotool command
            if current_first_f != current_last_f:
                oiio_cmds = "--frames {}-{}".format(current_first_f, current_last_f)
            else:
                oiio_cmds = "--frames {}".format(current_first_f)

            # fix paths
            source_seq = self.fix_path(source_seq)
            temp_seq = self.fix_path(temp_seq)
            ocio_path = self.fix_path(ocio_path)

            oiio_cmds += ' -i "{}"'.format(source_seq)
            # ignore the alpha channel and avoid rough edges in the png sequence files
            oiio_cmds += " -ch R,G,B"
            oiio_cmds += " --colorconfig"
            oiio_cmds += ' "{}"'.format(ocio_path)
            oiio_cmds += " --colorconvert"
            oiio_cmds += ' "{}"'.format(proj_working_colorspace)  # Input (working) colorspace
            oiio_cmds += ' "{}"'.format(proj_output_colorspace)  # Output colorspace
            oiio_cmds += " --croptofull"
            oiio_cmds += ' -o "{}"'.format(temp_seq)

            info_msg = "Temp png  Job: {}/{}  Frames: {}-{}"
            info_msg = info_msg.format(
                str(i + 1), str(oiio_jobs + 1), current_first_f, current_last_f
            )
            self.logger.info(info_msg)
            self.parent.engine.logger.info(info_msg)

            self.parent.engine.logger.info("oiio_cmds: {}".format(oiio_cmds))

            if not " " in oiio_exe:
                _err, _info, _cmd = oiio_tool.execute_command_str(oiio_cmds)

            else:
                # Problems with user with spaces
                # Using subprocess instead of the framework
                new_oiio_cmds = '"{}" {}'.format(oiio_exe, oiio_cmds)
                process2 = subprocess.Popen(
                    new_oiio_cmds,
                    shell=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    encoding="utf-8",
                )
                _info, _err = process2.communicate()

            if _err:
                war_msg = "{}/{} {}-{}: Error creating temp files"
                war_msg = war_msg.format(
                    str(i + 1), str(oiio_jobs + 1), current_first_f, current_last_f
                )
                self.logger.error(war_msg)
                self.parent.engine.logger.error(war_msg)
                raise Exception(
                    "Executing: {} \nFailed to create png aux sequence:\n {},\nError {} ".format(
                        oiio_cmds, _info, _err
                    )
                )

        # Validating all the frames
        for i in range(fr_start, fr_end + 1):
            # the following line should correctly format any printf padding style,
            # ex: %03d, %04d, %05d
            cur_file = temp_seq % i
            if not os.path.exists(cur_file):
                return False

        self.logger.info("EXR files converted to PNG.")
        self.parent.engine.logger.info("EXR files converted to PNG.")
        return temp_seq

    def get_sg_project_info(self, proj_fields):
        engine = self.parent.engine
        sg = engine.shotgun
        sg_proj = engine.context.project

        proj_filter = [["id", "is", sg_proj["id"]]]

        sg_proj = sg.find_one("Project", proj_filter, proj_fields)
        return sg_proj

    def get_publish_name(self, path):
        import re
        import os

        regex = r"(?P<name>.*)(?P<ver>[._-]v\d+)(?P<padd>\.?[^.]+)?(?P<ext>\.[^.]+)"

        name = os.path.basename(path)
        match_data = re.match(regex, name).groupdict()

        for key in ["ver", "padd"]:
            value = match_data.get(key)
            if value:
                name = name.replace(value, "")

        return name

    def collect_editorial_preview(self, item):

        file_type_code_field = 'published_file_type.PublishedFileType.code'

        editorial_preview = self.parent.shotgun.find_one(
            'PublishedFile',
            [
                [file_type_code_field, 'is', 'Media Review'],
                ['task.Task.step.Step.code', 'is', 'Editorial'],
                ['entity', 'is', self.parent.context.entity]
            ],
            ['path'],
            order=[
                {'field_name':'version_number', 'direction':'desc'},
                {'field_name':'id', 'direction':'desc'}
            ]
        )

        if editorial_preview:

            editorial_preview_file = editorial_preview['path']['local_path']

            if not os.path.exists(editorial_preview_file):
                metasync = self.load_framework("mty-framework-metasync")
                transfersManager = metasync.transfersManager
                transfersManager.ensure_file_is_local(
                    editorial_preview_file, editorial_preview)

            if os.path.exists(editorial_preview_file):
                item.properties["editorial_preview"] = editorial_preview_file
            else:
                return False

        return True

    def fix_path(self, path):
        """
        Fixes the path to be compatible with the Harmony API, which only accepts forward
        slashes
        """
        # fix image_path
        path = path.replace("\\\\", "/")
        path = path.replace("\\", "/")

        return path


class UploaderThread(QtCore.QThread):
    """
    Simple worker thread that encapsulates uploading to shotgun.
    Broken out of the main loop so that the UI can remain responsive
    even though an upload is happening
    """

    def __init__(self, app, version, path_to_movie, thumbnail_path, upload_to_shotgun):
        QtCore.QThread.__init__(self)
        self._app = app
        self._version = version
        self._path_to_movie = path_to_movie
        self._thumbnail_path = thumbnail_path
        self._upload_to_shotgun = upload_to_shotgun
        self._errors = []

    def get_errors(self):
        """
        can be called after execution to retrieve a list of errors
        """
        return self._errors

    def run(self):
        """
        Thread loop
        """
        upload_error = False

        if self._upload_to_shotgun:
            try:
                self._app.tank.shotgun.upload(
                    "Version",
                    self._version["id"],
                    self._path_to_movie,
                    "sg_uploaded_movie",
                )
            except Exception as e:
                self._errors.append("Movie upload to Shotgun failed: %s" % e)
                upload_error = True

        if not self._upload_to_shotgun or upload_error:
            try:
                self._app.tank.shotgun.upload_thumbnail(
                    "Version", self._version["id"], self._thumbnail_path
                )
            except Exception as e:
                self._errors.append("Thumbnail upload to Shotgun failed: %s" % e)
