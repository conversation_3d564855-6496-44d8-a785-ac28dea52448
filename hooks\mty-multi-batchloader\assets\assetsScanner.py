# -*- coding: utf-8 -*-
# Standard library:
# ___   ___   ___   ___   ___   ___  ___
# Third party:
import sgtk

# ___   ___   ___   ___   ___   ___  ___
# Project:
HookBaseClass = sgtk.get_hook_baseclass()


# ====================================================================


class AssetsScanner(HookBaseClass):

    def __init__(self, parent):
        super(AssetsScanner, self).__init__(parent)
        self.map_of_utilities_hooks = self.parent.get_setting(
            'map_of_utilities_hooks'
        )

        self.query = self.parent.create_hook_instance(
            self.parent.get_setting('queries_hook')
        )

        self.assets_filter = self.parent.create_hook_instance(
            self.map_of_utilities_hooks['assets']['filters']
        )

        self._shot_verbose_data = (
            self.query.shotgun_for_shot_verbose_data(
                self.parent.context.entity
            )
        )

    # ---------------------------------------------------------------------------

    @property
    def shot_verbose_data(self):
        return self._shot_verbose_data

    @property
    def list_of_breakdowns(self):
        return (
            self.query.shotgun_for_shot_breakdown_list(
                entity_shot=self.shot_verbose_data
            )
        )

    @property
    def list_of_breakdown_published_files(self):
        return (
            self.query.shotgun_published_files_from_breakdowns(
                list_of_breakdowns=self.list_of_breakdowns
            )
        )

    @property
    def list_of_rigged_asset_types(self):
        return self.parent.get_setting('list_of_rigged_asset_types')

    @property
    def list_of_valid_env_types(self):
        return self.parent.get_setting('list_of_environment_types')

    @property
    def list_of_valid_rig_types(self):
        return self.parent.get_setting('list_of_rigged_asset_types')

    # ---------------------------------------------------------------------------
    def find(self):
        result = {
            'list_of_rigs': [],
            'list_of_environments': [],
            'list_of_warnings': [],
            'list_of_errors': []
        }

        shot_data = self.shot_verbose_data

        if len(shot_data['sg_breakdowns']) < 1:
            result['list_of_errors'].append(
                "This shot doesn't have any linked assets, "
                "or their publishes are not approved."
            )
            return result

        list_of_publishes = self.list_of_breakdown_published_files


        list_of_asset_names = list(set(
            map(lambda x: x['entity']['name'], list_of_publishes)
        ))

        result['list_of_environments'] = (
            self.assets_filter.published_files_by_names_and_types(
                list_of_publishes=list_of_publishes,
                list_of_asset_names=list_of_asset_names,
                list_of_types=self.list_of_valid_env_types
            )
        )

        result['list_of_rigs'] = (
            self.assets_filter.published_files_by_names_and_types(
                list_of_publishes=list_of_publishes,
                list_of_asset_names=list_of_asset_names,
                list_of_types=self.list_of_valid_rig_types
            )
        )

        return result
