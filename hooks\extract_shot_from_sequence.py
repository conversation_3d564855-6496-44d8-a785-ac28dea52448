import os
import re
import sys
import copy
import shutil
import pprint
import platform
import traceback

import sgtk
import maya.mel as mel
import pymel.core as pm
import maya.cmds as cmds

# Project:
pp = lambda x, depth=6: pprint.PrettyPrinter(depth=depth).pprint(x)
pf = pprint.pformat

VALID_RIG_TASKS = ["FullRig", "rig3dRigging", "rigRig"]
FULL_RIG_TASK = "rig3dRigging"

class KeyframesManager(object):
    def __init__(self, engine, tk):
        self.engine = engine
        self.tk = tk
        self.context = self.engine.context
        self.latest_cutItems = self.context_latest_cutItems(self.context)

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    @property
    def list_of_animCurves(self):
        return pm.ls(
            type=[
                'animCurveTT',
                'animCurveTL',
                'animCurveTA',
                'animCurveTU'
            ]
        )

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def context_latest_cut(self):
        result = None

        entity = "Cut"
        filters = [["entity", "is", self.context.entity]]
        fields = ["revision_number"]
        order = [
            {
                "field_name": "revision_number",
                "direction": "desc"
            }
        ]

        result = self.tk.shotgun.find_one(
            entity,
            filters,
            fields,
            order
        )
        return result

    #   . . . . . . . . . . . . . . . . . . . . . .

    def context_latest_cutItems(self, context):
        result = {}

        latest_cut = self.context_latest_cut()

        entity = "CutItem"
        filter = [["cut", "is", latest_cut]]

        fields = [
            "cut_item_in",
            "cut_item_out",
            "code",
            "cut_item_duration"
        ]

        list_of_cutItems = self.tk.shotgun.find(
            entity,
            filter,
            fields
        )

        for element in list_of_cutItems:
            self.engine.logger.info('element: {}'.format(element))
            result.update({
                element["code"]: {
                    "start": element["cut_item_in"],
                    "end": element["cut_item_out"],
                    "duration": element["cut_item_duration"]
                }
            })

        return result

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def limit_animation_curves(self, start, end):
        for animCurve in self.list_of_animCurves:
            self.insert_range_limit_keyframes(animCurve, start, end)
            self.remove_not_required_animation(animCurve, start, end)

    #   . . . . . . . . . . . . . . . . . . . . . .

    def insert_range_limit_keyframes(self, curve, start, end):

        pm.setKeyframe(
            curve, animated=True,
            insert=True, time=start
        )

        pm.setKeyframe(
            curve, animated=True,
            insert=True, time=end
        )

    #   . . . . . . . . . . . . . . . . . . . . . .

    def remove_not_required_animation(self, curve, start, end):
        global_infinity_range = 999999

        pm.cutKey(
            curve, animation="objects",
            time=(-global_infinity_range, start - 1)
        )

        pm.cutKey(
            curve, animation="objects",
            time=(end + 1, global_infinity_range)
        )

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def move_animation_with_cutItem_data(self, start, shot):

        shot_start = shot.getStartTime()
        shot_name = shot.getShotName()
        cutItem_start = self.latest_cutItems[shot_name]["start"]

        amount = None

        if shot_start > cutItem_start:
            amount = cutItem_start - shot_start
        else:
            amount = shot_start - cutItem_start

        shot.sequenceStartFrame.set(
            shot.sequenceStartFrame.get() + amount)
        shot.sequenceEndFrame.set(
            shot.sequenceEndFrame.get() + amount)
        shot.startFrame.set(
            shot.startFrame.get() + amount)
        shot.endFrame.set(
            shot.endFrame.get() + amount)

        if amount == 0:
            message = "Shot node match cutItem data"
            self.engine.logger.info(message)
        else:
            message = \
                "Difference between shot data " + \
                "and cutItem data: {0}\n".format(amount) + \
                "animation curves will proceed to be moved."
            self.engine.logger.info(message)

            for animCurve in self.list_of_animCurves:
                pm.keyframe(
                    animCurve,
                    edit=True,
                    includeUpperBound=True,
                    relative=True,
                    option="insert",
                    timeChange=amount
                )


# ================================================================


class breakdownValidation():

    def __init__(self, entity_name, sequence, shotgun_instance, engine):

        self.shotgun = shotgun_instance
        self.sequence = sequence
        self.engine = engine
        self.entity = self.get_sg_entity(entity_name)
        self.publishedfile = None
        # TODO: use valueoverrides ofr these tasks
        self.valid_rig_tasks = VALID_RIG_TASKS

    def get_sg_entity(self, name):

        project_id = self.engine.context.project['id']
        filters = [
            ['code', 'is', name],
            ['project.Project.id', 'is', project_id],
            ['sg_sequence', 'is', self.sequence]
        ]

        entity = self.shotgun.find_one('Shot', filters)

        return entity

    def get_publishedfile(self):
        """
        Gets the last version published for a Maya Scene published in Layout for a shot entity.
        :return:
        A dict containing a published file for a Maya Scene.
        """

        layout_pipeline_step_entity_code = 'Layout'

        filters = [
            ['entity', 'is', self.entity],
            ['task.Task.step.Step.code', 'is', layout_pipeline_step_entity_code],
            ['published_file_type.PublishedFileType.code', 'is', 'Maya Scene'],
            ['project.Project.id', 'is', self.engine.context.project['id']]]

        fields = [
            "id",
            "code",
            "entity",
            "task.Task.content",
            "task.Task.step.Step.code"
            "task.Task.step",
            "entity.Shot.sg_breakdowns",
            "upstream_published_files",
            "downstream_published_files",
            "tags"
        ]

        order = [{'field_name': 'created_at', 'direction': 'desc'}]

        result_publish = self.shotgun.find_one("PublishedFile", filters, fields, order)

        # Set dependencie validate status
        if result_publish:
            result_publish['validate'] = False

        return result_publish

    def get_asset_list(self, asset_breakdown):

        # create an id list for each asset entity in breakdown
        bkd_ids = [asset["id"] for asset in asset_breakdown]
        if not bkd_ids:
            return [], []

        # List the actual entitys for breakdown with extra data
        breakdown_tags = self.shotgun.find('CustomEntity30', [['id', 'in', bkd_ids]], ['code', 'sg_asset.Asset.code'])
        # Collect asset names from breakdown and omit baseCamera rig from asset list
        #asset_names = list(set([asset['sg_asset.Asset.code'] for asset in breakdown_tags if asset['sg_asset.Asset.code'] != 'baseCamera']))
        #if not asset_names:
        #    return [], []

        asset_map_namespace = {}
        for asset in breakdown_tags:
            if asset['sg_asset.Asset.code'] != 'baseCamera':
                if asset['sg_asset.Asset.code'] not in asset_map_namespace:
                    asset_map_namespace[asset['sg_asset.Asset.code']] = [asset['code']]
                else:
                    asset_map_namespace[asset['sg_asset.Asset.code']].append(asset['code'])

        asset_names = list(asset_map_namespace.keys())

        if asset_names:
            # Get the actual list for the asset names found in the breakdown
            project_id = self.engine.context.project['id']
            result = self.shotgun.find('Asset',
                                       [['code', 'in', asset_names], ['project.Project.id', 'is', project_id]],
                                       ['sg_asset_type', 'code', 'sg_status_list', 'sg_asset_type', 'project']
                                       )
        else:
            result = []


        # return the list of asset entitites for the breakedown, and a list of assets names found
        return result, asset_names, asset_map_namespace

    def get_publishes(self, publishedFile_list, upstream=True):

        if not publishedFile_list:
            return [], []

        publishes_ids = [pub['id'] for pub in publishedFile_list]
        filters = [
            ['id', 'in', publishes_ids],
            ['published_file_type.PublishedFileType.code', 'is_not', 'Shot Environment Overrides']
        ]
        fields = ['name', 'code', 'entity', 'published_file_type', 'tags',
                  'entity.Shot.code', 'entity.Asset.code', 'task.Task.step.Step.code',
                  "task.Task.content"]
        publishedFile_list = self.shotgun.find("PublishedFile", filters, fields)

        published_assets = []
        for p in publishedFile_list:
            if upstream:
                if p['entity.Asset.code']:
                    if 'baseCamera' not in p['entity.Asset.code']:
                        published_assets.append(p['entity.Asset.code'])
            else:
                file_types = [
                    'Shot lyt Alembic',
                    'Maya Shot lyt Alembic'
                    'Shot Alembic',
                    'Maya Shot Alembic'
                ]
                if p['published_file_type']['name'] in file_types:

                    #self.engine.logger.info(p)
                    #self.engine.logger.info("asset new {}, old: {}".format(p['entity.Asset.code'], p['code'].split('_')[5]))
                    #published_assets.append(p['entity.Asset.code'])
                    published_assets.append(p['code'].split('_')[5])
                    # print p['boom']
            # set default validation value
            p["validate"] = False

        return publishedFile_list, list(set(published_assets))

    def validate_rigs(self, asset_name, published_list):


        # Only accept rig_body or rig_full
        for publish in published_list:

            if publish.get("entity.Asset.code") == asset_name and publish.get('published_file_type')['name'] == 'Maya Scene':
                # Task name must be Rig
                if publish["task.Task.content"] in self.valid_rig_tasks:
                    publish['validate'] = True
                    return True

        return False

    def validate_cache(self, asset_name, published_list):

        for publish in published_list:
            if publish.get("entity.Asset.code") == asset_name and publish.get('published_file_type')['name'] == 'GPU Cache':
                publish['validate'] = True
                return True

        return False

    def validate_model(self, asset_name, published_list):

        for publish in published_list:
            asset_name_match = publish.get("entity.Asset.code") == asset_name
            publish_type = publish.get('published_file_type')['name'] == 'Maya Scene'
            if asset_name_match and publish_type:
                if '{}_mdl'.format(asset_name) in publish.get('code'):
                    publish['validate'] = True

    def validate_audio(self, entity, published_list):

        supported_audio_tasks = ["Editorial", "Animatic"]
        for publish in published_list:
            if publish.get('published_file_type')['name'] == 'Editorial Audio':
                # Link must be shot # Task must be Editorial
                if publish['entity']['id'] == entity['id'] and publish["task.Task.step.Step.code"] in supported_audio_tasks:
                    publish['validate'] = True

                    return True

        return False

    def validate_camera(self, published_list, dep):

        for publish in published_list:

            if dep == 'upstream':
                # print publish.get('published_file_type')['name'], '--', publish.get('code')
                if publish.get('published_file_type')['name'] == 'Maya Scene' and 'baseCamera' in publish.get('code'):
                    publish['validate'] = True
                    return True
            else:
                type_is_cache = publish.get('published_file_type')['name'] == 'Shot Camera Cache'
                if type_is_cache and 'camera' in publish.get('code'):
                    publish['validate'] = True
                    return True

        return False

    def validate_media_review(self, shot, published_list):

        for publish in published_list:
            # print 1, publish.get('published_file_type')['name']
            if publish.get('published_file_type')['name'] == 'Media Review':
                # print shot['id'], '---', publish['entity']['id']
                if shot['id'] == publish['entity']['id']:
                    publish['validate'] = True
                    return True

        return False

    def validate_upstreams(self, shot, assets, publishes):

        validation_errors = []

        # Audio validation
        self.engine.logger.info('Validating audio ...')
        if not self.validate_audio(shot, publishes):
            msg = 'Audio file for {} is not valid or missing.'.format(shot['name'])
            self.engine.logger.error(msg)
            validation_errors.append(msg)

        self.engine.logger.info('Validating camera ...')
        # Camera validation
        if not self.validate_camera(publishes, "upstream"):
            msg = 'Camera rig for {} is not valid or missing.'.format(shot['name'])
            self.engine.logger.error(msg)
            validation_errors.append(msg)

        self.engine.logger.info('Validating assets...')
        for asset in assets:
            # Validates modeling publish for asset name
            self.validate_model(asset['code'], publishes)

            # Filter some valid entity types
            if asset['sg_asset_type'] in ['Character', 'Prop', 'EnvProp', 'Vehicle']:
                self.engine.logger.info('Assets {} is {}, validating rig...'.format(asset['code'], asset['sg_asset_type']))
                # Validating rigs...
                if not self.validate_rigs(asset['code'], publishes):
                    msg = "Rig task for asset {} is not valid. Valid tasks are: {}".format(
                        asset['code'], self.valid_rig_tasks
                    )
                    self.engine.logger.error(msg)
                    validation_errors.append(msg)
            elif asset['sg_asset_type'] in ['EnvLocation', 'EnvModule']:
                self.engine.logger.info('Assets {} is {}, validating gpu...'.format(asset['code'], asset['sg_asset_type']))
                # Validating GPU Caches...'
                if not self.validate_cache(asset['code'], publishes):
                    msg = 'GPU Cache for {} is not valid or missing.'.format(asset['code'])
                    self.engine.logger.error(msg)
                    validation_errors.append(msg)
            else:
                self.engine.logger.warning('Asset type ignored: {}, I dont know what to do!!!'.format(asset['code']))
                validation_errors.append('Asset type ignored: {}, I dont know what to do!!!'.format(asset['code']))

        return validation_errors

    def dependencies_validation(self, dependencies):

        validation_result = []

        for publish in dependencies:
            if not publish['validate']:
                msg = "Publish: {}, is not recognized as valid dependency.\n".format(publish['code'])
                self.engine.logger.error(msg)
                validation_result.append(msg)

        return validation_result

    def validate_rig_cache(self, asset_name, published_list, asset_maps):

        self.engine.logger.info('validating {} ({})**********************************************'.format(asset_name, len(published_list)))
        for publish in published_list:
            self.engine.logger.info(publish.get("code"))
            self.engine.logger.info(publish.get('published_file_type')['name'])
            if publish.get('published_file_type')['name'] in ['Shot lyt Alembic', 'Maya Shot lyt Alembic']:
                if publish["task.Task.step.Step.code"] == "Layout":
                    publish_namespace = publish.get("code").split("_")[5]
                    msg = "rig validating: {}, --> {}"
                    self.engine.logger.info(msg.format(publish_namespace, asset_name))

                    if publish_namespace in asset_maps[asset_name]:
                        self.engine.logger.info('VALIDATED!!!')

                        publish['validate'] = True
                        return True

        """
        for publish in published_list:
            self.engine.logger.info("rig validate: {}, --> {}".format(publish.get("code"), "_{}_".format(asset_name)))
            if "_{}_".format(asset_name) in publish.get("code"):
                if publish.get('published_file_type')['name'] in ['Shot lyt Alembic', 'Maya Shot lyt Alembic']:
                    if publish["task.Task.step.Step.code"] == "Layout":
                        publish['validate'] = True
                        return True
        """

        return False

    def validate_rig_assets(self, asset_list, published_files, names_map):
        validation_errors = []

        for asset in asset_list:
            if asset['sg_asset_type'] in ['Character', 'Prop', 'EnvProp', 'Vehicle']:
                if not self.validate_rig_cache(asset['code'], published_files, names_map):
                    msg = "Rig alembic cache downstream dependency not found for asset: {}".format(asset['code'])
                    self.engine.logger.error(msg)
                    validation_errors.append(msg)

        return validation_errors

    def validate_downstream(self, shot, dependencies_assets, assets, publishes, namespace_maps):

        validation_errors = []

        self.engine.logger.info('Validating downstream published files...')
        self.engine.logger.info(len(dependencies_assets))
        self.engine.logger.info(len(assets))
        self.engine.logger.info('assets: {}'.format(assets))
        self.engine.logger.info('dependencies_assets: {}'.format(dependencies_assets))

        self.engine.logger.info('Looping true dependencies...')
        for dependency in publishes:
            publish_type = dependency.get('published_file_type')['name']
            self.engine.logger.info(dependency.get("code"))
            self.engine.logger.info(dependency.get('published_file_type')['name'])

            # validate only alembic assets
            if publish_type in ['Shot lyt Alembic', 'Maya Shot lyt Alembic']:
                self.engine.logger.info('Validating alembic cache...')
                if dependency["task.Task.step.Step.code"] == "Layout":
                    publish_namespace = dependency.get("code").split("_")[5]
                    #Check if publish namespace is a breakdown tag name
                    for asset_name in namespace_maps:
                        msg = "rig validating: {}, --> {}"
                        self.engine.logger.info(msg.format(publish_namespace, asset_name))
                        if publish_namespace in namespace_maps[asset_name]:
                            asset_entity = [asset for asset in assets if asset['code'] == asset_name]
                            self.engine.logger.info('asset_entity: {}'.format(asset_entity))
                            if asset_entity:
                                # Get the first element of the list corresponding for the asset publish
                                asset_entity = asset_entity[0]
                                if asset_entity.get('sg_asset_type') in ['Character', 'Prop', 'EnvProp', 'Vehicle']:
                                    self.engine.logger.info('asset VALIDATED!!!')
                                    dependency['validate'] = True
                                    continue
                                else:
                                    self.engine.logger.info('ERROR: asset cache must come from : {}'.format(['Character', 'Prop', 'EnvProp', 'Vehicle']))

            # validate only Shot Camera CacheValidating upstreams publishes...
            elif publish_type in ['Shot Camera Cache']:
                self.engine.logger.info('Validating Camera cache...')
                if 'camera' in dependency.get('code'):
                    self.engine.logger.info('camera VALIDATED!!!')
                    dependency['validate'] = True
                    continue

            # validate only Video Review
            elif publish_type in ['Media Review']:
                self.engine.logger.info('Validating video review...')
                if shot['id'] == dependency['entity']['id']:
                    self.engine.logger.info('review VALIDATED!!!')
                    dependency['validate'] = True
                    continue
            else:
                self.engine.logger.info('ERROR is not a valid dependency: {}'.format(dependency.get("code")))

        self.engine.logger.info('--- Dependencies validation finished ---')


        return validation_errors

    def validate_dependencies(self, main_publish):

        errores = []

        # get the list of entities and names for breakdown
        asset_list, brakdown_asset_names, brakdown_asset_namespaces = self.get_asset_list(main_publish["entity.Shot.sg_breakdowns"])
        self.engine.logger.info('Asset names found in breakdown({}): {}'.format(len(brakdown_asset_names), brakdown_asset_names))

        if asset_list:
            upstream_publishes, assets_upstream = self.get_publishes(main_publish["upstream_published_files"])
            self.engine.logger.info('Asset names found in Upstreams({}):  {}'.format(len(assets_upstream), assets_upstream))


            self.engine.logger.info('Validating upstreams publishes...')
            # breakdown assets must be the same asset listed as upstream dependences
            if len(brakdown_asset_names) != len(assets_upstream):
                msg = "breakedown assets missmatch with upstream dependencies"
                self.engine.logger.error(msg)
                errores.append(msg)

            # Validate asset type with dependencies
            errores.extend(self.validate_upstreams(main_publish['entity'], asset_list, upstream_publishes))

            # Dependencies result
            errores.extend(self.dependencies_validation(upstream_publishes))

            # ----------------------------------------------------------------------------

            self.engine.logger.info('Validating downstream publishes...')
            downstream_result = self.get_publishes(main_publish["downstream_published_files"], upstream=False)
            # All downstream published files and all namespace names for alembic assets
            downstream_publishes, namespaces_downstream = downstream_result

            self.engine.logger.info('breakdown_namespaces_downstream: {}'.format(namespaces_downstream))

            self.engine.logger.info(':::: brakdown_asset_namespaces: {}'.format(brakdown_asset_namespaces))

            assets_downstream = []
            for asset_name in brakdown_asset_namespaces:
                for namespace in namespaces_downstream:
                    if namespace in brakdown_asset_namespaces[asset_name]:
                        assets_downstream.append(asset_name)


            self.engine.logger.info('breakdown asset names: {}'.format(assets_downstream))
            asset_list = []
            if assets_downstream:
                project_id = self.engine.context.project['id']
                asset_list = self.shotgun.find('Asset',
                                               [['code', 'in', assets_downstream], ['project.Project.id', 'is', project_id]],
                                               ['sg_asset_type', 'code', 'sg_status_list', 'sg_asset_type', 'project']
                                               )

            down_validation = self.validate_downstream(main_publish['entity'], assets_downstream, asset_list, downstream_publishes, brakdown_asset_namespaces)
            errores.extend(down_validation)

            # Dependencies result
            errores.extend(self.dependencies_validation(downstream_publishes))
        else:
            self.engine.logger.info('WARNING: There is no asset in breakdown list, dependency validation skiped!')

        return errores

    def clean_revision_tags(self, main_publish):

        current_tags = main_publish['tags']

        cleaned_tags = []
        if current_tags:
            # Remove any tag found
            for tag in current_tags:
                if tag['id'] not in [606, 607]:
                    cleaned_tags.append(tag)

        return cleaned_tags

    def update_revision_tag(self, main_publish, errors, tags):

        updated_tags = tags

        if errors:
            # Update as rejected
            updated_tags.append({'type': 'Tag', 'id': 607, 'name': 'splitter_delivery_rejected'})
            self.shotgun.update("PublishedFile", main_publish['id'], {'tags': updated_tags})
        else:
            # Update to deliver
            updated_tags.append({'type': 'Tag', 'id': 606, 'name': 'splitter_delivery_ready'})
            self.shotgun.update("PublishedFile", main_publish['id'], {'tags': updated_tags})

    def layout_breakdown_sanity(self):

        self.engine.logger.info('Starting breakdown sanity...')

        self.publishedfile = self.get_publishedfile()
        self.engine.logger.info('Main publish: {}'.format(self.publishedfile['code']))

        self.engine.logger.info('Validating dependencies for breakdown...')
        dependencies_result = self.validate_dependencies(self.publishedfile)

        self.engine.logger.info('Cleaning tags from main published file ...')
        cleaned_tags = self.clean_revision_tags(self.publishedfile)

        if not dependencies_result:
            self.engine.logger.info('Brakedown validation> TRUE')
            breakdown_status = True
        else:
            self.engine.logger.info('Brakedown validation> ERROR')
            breakdown_status = False

        # Update tag to splitter_delivery_ready
        self.update_revision_tag(self.publishedfile, breakdown_status, cleaned_tags)

        return dependencies_result


# ================================================================


class SequenceSplitter():

    def __init__(self, engine, tk, shotgun, transfersManager, shot_split=None, sequence_path=None, exc_in_batch=False):
        """
        """
        self.keyframesManager = KeyframesManager(engine, tk)
        self.sequence_shot_entities = {}
        self.context_by_shot_name = {}
        self.latest_shot_version_by_shot_name = {}
        self.shotgun = shotgun
        self.engine = engine
        self.tk = tk

        self.sequence_split_path = sequence_path
        self.shot_name_split = shot_split
        self.batch_split = exc_in_batch

        self.valid_rig_tasks = VALID_RIG_TASKS
        self.full_rig_task = FULL_RIG_TASK

        # rig swap requirements
        self.platform = sys.platform
        self.local_path = None
        if self.platform != "win32":
            raise Exception(
                "Only Windows platform is currently supported."
            )
        else:
            self.local_path = "local_path_windows"

        self.template_publish_assets = self.engine.get_template_by_name("maya_asset_publish")
        self.template_alembic_cache = self.engine.get_template_by_name("asset_alembic_cache")

        # Transfer manager instance
        self.transfersManager = transfersManager

        self.validate_current_session()

    def get_locked_number(self, publishedFile, shot_context):
        """
        This method seached for the locked version related to a breakdown entity if there is a
        locked publishedFile assigned.

        Returns version number for the locked published file in breakdown entity or None value.
        """

        # Get breakdown entity for context entity
        item_breakdown = self.engine.shotgun.find_one("CustomEntity30",
                                                      [['sg_shot', 'is', shot_context.entity],
                                                       ['code', 'is', publishedFile['entity']['name']]],

                                                      ['sg_locked_version.PublishedFile.version_number']
                                                      )
        self.engine.logger.info('item breakdown> {}'.format(item_breakdown))
        self.engine.logger.info('item breakdown> {}'.format(shot_context.entity))
        self.engine.logger.info('item breakdown> {}'.format(publishedFile['entity']['name']))

        if item_breakdown and item_breakdown.get('sg_locked_version.PublishedFile.version_number'):
            return item_breakdown.get('sg_locked_version.PublishedFile.version_number')

        return None

    def swap_camera_rig(self, cam_node, cam_reference):

        fields = ['entity', 'published_file_type', 'name', 'version_number', 'path']

        camera_path = cmds.referenceQuery(cam_reference, filename=True, withoutCopyNumber=True)
        self.engine.logger.info('camera_path: {}'.format(camera_path))
        publish = sgtk.util.find_publish(self.tk, [camera_path], fields=fields)
        current_publish = publish[camera_path]

        # Find latest aproved published file version number
        filters = [['entity', 'is', current_publish['entity']],
                   ['sg_status_list', 'is', 'apr'],
                   ['published_file_type', 'is', current_publish['published_file_type']],
                   ['name', 'is', current_publish['name']]]

        order = [{'field_name': 'version_number', 'direction': 'desc'}]

        self.engine.logger.info('field: {}'.format(filters))
        latest_camera = self.shotgun.find_one('PublishedFile', filters, fields, order=order)
        self.engine.logger.info('latest_camera: {}'.format(latest_camera))

        # Update camera reference if there is an aproved version update
        if current_publish['version_number'] != latest_camera['version_number']:
            new_path = latest_camera['path'][self.local_path]
            # swap path
            cmds.file(new_path, loadReference=cam_reference)
            self.engine.logger.info('camera changed from {}'.format(camera_path))
            self.engine.logger.info('to> {}'.format(new_path))

    def swap_caches(self, context, lockedversions):

        fields = ['entity', 'published_file_type', 'name', 'version_number', 'path', 'entity.Asset.sg_asset_type']

        for gpu_node in cmds.ls(l=True, type="gpuCache"):
            transform_node = cmds.listRelatives(gpu_node, p=True, f=True)[0]
            parent_node = cmds.listRelatives(transform_node, p=True, f=True)
            # Omit every nested node (has parent)
            if parent_node:
                continue

            # Search for the gpuPublishedFile
            current_path = cmds.getAttr("%s.cacheFileName" % gpu_node)

            self.engine.logger.info("Current GPU cache path: {}".format(current_path))
            self.engine.logger.info("Current lockedversions: {}".format(lockedversions))


            if current_path not in lockedversions:
                current_path = current_path.replace("/", os.path.sep)
                publish = sgtk.util.find_publish(self.tk, [current_path], fields=fields)
                publish = publish[current_path]
                self.engine.logger.info("Current GPU publish: {}".format(publish))

                if publish['entity.Asset.sg_asset_type'].lower() not in ["envlocation", "envmodule", "envprop"]:
                    msg = "Not alowed instance for {} type: {}, GPU instances are only valid for Environment or EnvModules"
                    raise ValueError(msg.format(publish['entity.Asset.sg_asset_type'], publish["name"]))

                locked_number = self.get_locked_number(publish, context)

                # Find latest aproved published file version number
                filters = [['entity', 'is', publish['entity']],
                           ['published_file_type', 'is', publish['published_file_type']]
                           ]

                if locked_number:
                    filters.append(['version_number', 'is', locked_number])
                else:
                    filters.append(['sg_status_list', 'is', 'apr'])

                order = [{'field_name': 'version_number', 'direction': 'desc'}]
                latest_gpu = self.shotgun.find_one('PublishedFile', filters, fields, order=order)

                self.engine.logger.info("Filters: {}".format(filters))
                self.engine.logger.info("Latest GPU cache path: {}".format(latest_gpu))

                # Update gpu cache if not in the same version
                if publish['version_number'] != latest_gpu['version_number']:
                    new_path = latest_gpu['path'][self.local_path]
                    # swap path
                    cmds.setAttr("%s.cacheFileName" % gpu_node, new_path, type="string")

    def swap_rigs_to_full(self):

        list_of_references = pm.listReferences(recursive=False)
        map_of_references = {}
        self.update_map_references(list_of_references, map_of_references)
        self.update_map_publishes(map_of_references, task=self.valid_rig_tasks)
        self.download_rigs(map_of_references, task=self.valid_rig_tasks)
        self.replace_rig_references(map_of_references, task=self.full_rig_task)

        # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def update_map_references(self, list_of_references, map_of_assets, ):

        for reference in list_of_references:

            filename, extension = os.path.splitext(reference.path)
            #   . . . . . . . . . . . . . . . . . . . . . .
            fields = None
            if extension == ".ma" or extension == ".mb":
                fields = self.template_publish_assets.get_fields(reference.path)
            else:
                raise Exception(
                    "File type not supported: {0}".format(reference.path)
                )
            #   . . . . . . . . . . . . . . . . . . . . . .
            asset_name = fields.get("Asset")
            asset_type = fields.get("sg_asset_type")
            step_code = fields.get("Step")
            #   . . . . . . . . . . . . . . . . . . . . . .
            if asset_type.lower() in ["character", "prop", "envprop", "envlocation", "envmodule"]:
                map_of_assets[reference.refNode.name()] = {
                    "asset_name": asset_name,
                    "reference": reference,
                    "type": asset_type,
                    "step": step_code,
                }
            else:
                continue

            #   . . . . . . . . . . . . . . . . . . . . . .
            entity_type = "Asset"

            filters = []

            filter_project = [
                "project", "is", self.engine.context.project
            ]

            filters.append(filter_project)

            filter_code = ["code", "is", fields["Asset"]]

            filters.append(filter_code)

            fields = ["code", "type", "id"]

            shotgun_entity = \
                self.engine.shotgun.find_one(
                    entity_type,
                    filters,
                    fields
                )

            map_of_assets[reference.refNode.name()].update({
                "entity": shotgun_entity
            })

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----


    def get_lockedVersionId(self, breakdown_name):

        # Get breakdown entity for context entity
        item_breakdown = self.engine.shotgun.find_one("CustomEntity30",
                                                      [['sg_shot', 'is', self.engine.context.entity],
                                                       ['code', 'is', breakdown_name]],

                                                      ['sg_locked_version.PublishedFile.id']
                                                      )

        if item_breakdown and item_breakdown.get('sg_locked_version.PublishedFile.id'):
            return item_breakdown.get('sg_locked_version.PublishedFile.id')

        return None


    def build_filters(self, sg_task, entity, context):

        filters = [['published_file_type.PublishedFileType.code', 'is', 'Maya Scene']]
        #
        filter_project = ["project", "is", context.project]
        filters.append(filter_project)
        #
        filter_entity = [
            "entity", "is", entity
        ]
        filters.append(filter_entity)
        #
        if isinstance(sg_task, str):
            filter_task = [
                "task.Task.content",
                "is",
                sg_task
            ]
        elif isinstance(sg_task, list):
            filter_task = [
                "task.Task.content",
                "in",
                sg_task
            ]
        filters.append(filter_task)
        #
        filter_aproved = ['sg_status_list', 'is', 'apr']
        filters.append(filter_aproved)

        return filters


    def get_sg_publishes(self, sg_entity, sg_filters):

        fields = [
            "id",
            "type",
            "code",
            "path_cache",
            "path",
            "sg_source_location"
        ]

        sorting = [
            {
                'column': 'created_at',
                'direction': 'desc'
            }
        ]

        return self.engine.shotgun.find(sg_entity, sg_filters, fields, sorting)[:1]

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----
    def update_map_publishes(self, map_of_assets, task, extra_filter=None):
        self.engine.logger.info("updating map publishes...")
        # Key is the reference node name, key was refered before as map_of_assets[key]["asset_name"]
        for key in map_of_assets.keys():

            entity = map_of_assets[key]["entity"]

            asset_key = map_of_assets[key]["asset_name"]
            locked_version_id = self.get_lockedVersionId(asset_key)
            if locked_version_id:
                self.engine.logger.info("Is locked version...")
                filters = [['id', 'is', locked_version_id]]

                list_of_published_assets = self.get_sg_publishes("PublishedFile", filters)
            else:
                self.engine.logger.info('Analizig Rig Full... {}'.format(task))
                filters = self.build_filters(task, entity, self.engine.context)
                self.engine.logger.info('filters... {}'.format(filters))
                list_of_published_assets = self.get_sg_publishes("PublishedFile", filters)
                self.engine.logger.info('len assets pubs#> {}'.format(len(list_of_published_assets)))

                # If no RigFull found search for RigBody
                if not list_of_published_assets:
                    self.engine.logger.info('Analizing Rig Body...')
                    filters = self.build_filters("BodyRig", entity, self.engine.context)
                    self.engine.logger.info('filters... {}'.format(filters))
                    list_of_published_assets = self.get_sg_publishes("PublishedFile", filters)
                    self.engine.logger.info('len assets pubs#> {}'.format(len(list_of_published_assets)))

                    if not list_of_published_assets:
                        self.engine.logger.error("Asset {} doesn't have a full nor body rig".format(asset_key))

            if not list_of_published_assets:
                continue

            if isinstance(task, str):
                map_of_assets[key].update({task: list_of_published_assets})
            elif isinstance(task, list):
                for t in task:
                    map_of_assets[key].update({t: list_of_published_assets})

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def download_rigs(self, map_of_assets, task):
        latest = 0

        # Key is the reference node name, key was refered before as map_of_assets[key]["asset_name"]

        for key in map_of_assets.keys():
            asset_key = map_of_assets[key]["asset_name"]
            if isinstance(task, str):
                if len(map_of_assets[key][task]) == 0:
                    pm.warning(
                        "Asset {} has no publishes in task {}".format(asset_key, task)
                    )
                    continue

                asset_filepath = map_of_assets[key][task][latest]["path"][self.local_path]

                if os.path.isfile(asset_filepath):
                    self.engine.logger.info(
                        "Published file {0} already exists in the local file system.".format(
                            asset_filepath
                        )
                    )
                else:
                    self.transfer_references(key, map_of_assets, task)
            elif isinstance(task, list):
                for t in task:
                    if len(map_of_assets[key][t]) == 0:
                        pm.warning(
                            "Asset {} has no publishes in task {}".format(asset_key, t)
                        )
                        continue

                    asset_filepath = map_of_assets[key][t][latest]["path"][self.local_path]

                    if os.path.isfile(asset_filepath):
                        self.engine.logger.info(
                            "Published file {0} already exists in the local file system.".format(
                                asset_filepath
                            )
                        )
                    else:
                        self.transfer_references(key, map_of_assets, t)


    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def transfer_references(self, key, map_of_assets, task):
        latest = 0
        asset_key = map_of_assets[key]["asset_name"]
        try:

            if not os.path.exists(map_of_assets[key][task][latest]["path"][self.local_path]):
                self.transfersManager.ensure_file_is_local(
                    map_of_assets[key][task][latest]["path"][self.local_path],
                    map_of_assets[key][task][latest]
                )

        except:
            pm.warning(
                "Unable to download all {0} ".format(asset_key) +
                "Assets and dependencies."
            )
            import traceback
            self.engine.logger.info(traceback.format_exc())

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def replace_rig_references(self, map_of_assets, task):

        latest = 0

        for key in map_of_assets.keys():
            asset_key = map_of_assets[key]["asset_name"]
            self.engine.logger.info("Key: {}, {}".format(asset_key, key))
            self.engine.logger.info("task: {}".format(task))
            if key in map_of_assets and task in map_of_assets[key]:
                self.engine.logger.info("map: {}".format(map_of_assets[key]))
                if map_of_assets[key][task]:
                    file_path =map_of_assets[key][task][latest]["path"][self.local_path]
                    if os.path.isfile(file_path):
                        compare_file_path = file_path.replace("\\", "/")
                        if map_of_assets[key]["reference"] != compare_file_path:
                            try:
                                map_of_assets[key]["reference"].replaceWith(file_path)
                                self.engine.logger.info('REPLACED RIG!!!! {} ----> {}'.format(map_of_assets[key]["reference"], file_path))
                            except:
                                self.engine.logger.info('ERROR REPLACING RIG: {}'.format(traceback.format_exc()))


    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def swap_audio_to_shot(self, shot_name, context):

        # Remove current sequence audio
        audio_result = cmds.ls(type='audio')
        #
        for audio_node in audio_result:
            self.engine.logger.info('Audio path to delete: {}'.format(cmds.sound(audio_node, f=True, q=True)))
            cmds.delete(audio_node)

        supported_audio_tasks = ["Editorial", "Animatic"]
        filters = [['entity', 'is', context.entity],
                   ['task.Task.content', 'in', supported_audio_tasks],
                   ['project', 'is', context.project],
                   ['published_file_type.PublishedFileType.code', 'is', 'Editorial Audio']]

        fields = ['version_number', 'path']
        order = [{'field_name': 'version_number', 'direction': 'desc'}]

        publish_audio = self.shotgun.find_one('PublishedFile', filters, fields, order)

        self.engine.logger.info('publish_audio> {}'.format(publish_audio))

        audio_path = ''
        if publish_audio and publish_audio.get('path'):
            audio_path = publish_audio['path'][self.local_path]

        if audio_path:
            self.engine.logger.info("audio path: {}".format(audio_path))
            # for all th required published files ensure they and their dependencies
            # exist locally on disk
            try:
                if not os.path.isfile(audio_path):
                    # self.transfersManager.ensure_file_is_local(path, sg_publish_data)
                    # self.transfersManager.ensure_local_dependencies(sg_publish_data)
                    self.transfersManager.ensure_file_is_local(audio_path, publish_audio)

            except:
                pm.warning(
                    "Unable to download {0} ".format(audio_path) +
                    "as Sound dependency."
                )
                self.engine.logger.info('AUDIO IS MISSING FROM SHOT!!!!! -----------')
                import traceback
                self.engine.logger.info(traceback.format_exc())


            # then replace audio to published audio path found

            # create the audio node
            name = "{}_audio".format(shot_name)
            audio = cmds.createNode('audio', name=name)

            # set its file path
            cmds.setAttr('{}.filename'.format(audio), audio_path, type='string')

            # set the offset based on the entity
            offset = None
            entity = context.entity

            self.engine.logger.info("Publish Entity for audio swap:\n{}".format(pf(entity)))

            filters = [['id', 'is', entity['id']]]
            shot = self.shotgun.find_one('Shot', filters, ['sg_cut_in'])
            if shot:
                offset = shot['sg_cut_in']
                # set audio ofsset
                if offset:
                    cmds.setAttr('{}.offset'.format(audio, offset))

            # To display sound in the time slider, you must specify
            # the sound node to display and turn display of sound "on."
            # First we need to get the name of the playback slider from
            # the global mel variable called gPlayBackSlider
            try:
                aPlayBackSliderPython = mel.eval('$tmpVar=$gPlayBackSlider')
                cmds.timeControl(aPlayBackSliderPython,
                                 edit=True,
                                 sound=audio,
                                 displaySound=True)

                self.engine.logger.info('Time control: {}'.format(cmds.timeControl(aPlayBackSliderPython, q=True)))
            except:
                self.engine.logger.info("Couldn't set the playback slider to display the audio node '{}'".format(audio))

        return audio_path

    def validate_current_session(self):
        """
        """

        self.engine.logger.info('Validating current session...')

        current_path = cmds.file(query=True, sceneName=True)
        if not current_path:
            if self.sequence_split_path is None:
                raise Exception("There is no path open found in current maya session.")

            self.engine.logger.info('Opening current session...')
            cmds.file(self.sequence_split_path, open=True, force=True)
            current_path = cmds.file(query=True, sceneName=True)

        self.engine.logger.info('Current session: {}'.format(self.sequence_split_path))

        if not current_path or not os.path.exists(current_path):
            message = "%s\nScene path doesn't exist or is not valid:"
            raise Exception(message % current_path)

        self.sequence_file_path = current_path

        self.engine.logger.info('Check if scene is a publish path ...')
        template = self.tk.templates.get("maya_sequence_publish")
        self.current_template_fields = template.validate_and_get_fields(self.sequence_file_path)
        if not self.current_template_fields:
            message = "The current scene path is not a valid Sequence Maya Publish file:\n%s"
            raise Exception(message % self.sequence_file_path)

        self.current_version_number = self.current_template_fields['version']
        self.current_publish_name = self.current_template_fields['name']

        self.tk.synchronize_filesystem_structure(full_sync=True)
        self.engine.logger.info('Build context from sequence path ...')
        self.context = self.tk.context_from_path(self.sequence_file_path)

    def get_top_references(self):
        """ Get the current TOP references objects in the scene
        """

        top_level_reference_nodes = []
        all_references = cmds.ls(objectsOnly=True, references=True)
        top_level_referenced_files = cmds.file(query=True, reference=True)
        for ref_node in all_references:
            ref_file = cmds.referenceQuery(ref_node, filename=True, withoutCopyNumber=True)
            if ref_file in top_level_referenced_files:
                top_level_reference_nodes.append(ref_node)

        return top_level_reference_nodes

    def bakeConstrains(self, startF, endF):
        """
        """

        # collect all constrainst in the scene
        all_constrains = cmds.ls(type="constraint")

        # keep only local (non referenced) constraints
        filtered_constraints = [
            _constraint for _constraint in all_constrains
                if not cmds.referenceQuery(_constraint, isNodeReferenced=True)
        ]

        control_constraint = []
        delete_constraint = []

        for _constraint in filtered_constraints:
            self.engine.logger.info(
                "{}\nWorking with constraint: {}\n".format(
                    "-" * 80, _constraint
                )
            )
            try:
                conections = cmds.listConnections(_constraint,
                                                  source=True,
                                                  scn=True,
                                                  type='transform')
                for node in conections:
                    # and cmds.attributeQuery('fbControl',node = node, exists = True):
                    if cmds.objectType(node) == 'transform':

                        list_source = cmds.listConnections(_constraint,
                                                           source=True,
                                                           destination=False,
                                                           scn=True,
                                                           type='transform')

                        destination = cmds.listConnections(_constraint,
                                                           source=False,
                                                           destination=True,
                                                           scn=True,
                                                           type='transform')
                        source = None
                        for each in list_source:
                            if not destination[0] == each:
                                source = each
                                break
                        # origin_name = source.split(':')[0]
                        if cmds.referenceQuery(node, isNodeReferenced=True):
                            origin_name = cmds.refereceQuery(
                                source, namespace=True, shortName=True
                            )
                        # destination_name = node.split(':')[0]
                        if cmds.referenceQuery(node, isNodeReferenced=True):
                            destination_name = cmds.refereceQuery(
                                node, namespace=True, shortName=True
                            )

                        if not destination[0] in control_constraint and \
                            not origin_name == destination_name:

                            self.engine.logger.info("From {} to {}".format(source, destination[0]))
                            control_constraint.append(destination[0])
                            delete_constraint.append(_constraint)
            except:
                self.engine.logger.info("Error: Maybe {0} is a NoneType?".format(_constraint))

        self.engine.logger.info(
            "\n{}\ncontrol_constraint:\n{}".format(
                "-" * 80, pf(control_constraint)
            )
        )
        self.engine.logger.info(
            "\n{}\ndelete_constraint:\n{}".format(
                "-" * 80, pf(delete_constraint)
            )
        )

        if len(control_constraint) > 0:
            cmds.bakeResults(control_constraint,
                             simulation=True,
                             t=(startF, endF),
                             oversamplingRate=1,
                             disableImplicitControl=True,
                             preserveOutsideKeys=True,
                             sparseAnimCurveBake=False,
                             removeBakedAttributeFromLayer=False,
                             removeBakedAnimFromLayer=False,
                             bakeOnOverrideLayer=False,
                             minimizeRotation=True,
                             controlPoints=False,
                             shape=True)

            cmds.delete(delete_constraint)

    def get_root_node(self, node_obj):
        """
        From a reference object collects the top root node name.
        returns: A string corresponding to the top root node name.
        """

        node_type = cmds.nodeType(node_obj)
        if node_type == 'reference':
            ref_nodes = cmds.referenceQuery(node_obj, nodes=True, dagPath=True)
            transform_list = cmds.ls(ref_nodes, type="transform", long=True)
            # get main parent of current reference's transform
            if len(transform_list) == 0:
                return None
            lastParent = transform_list[0]
        elif node_type == 'transform':
            try:
                children = cmds.listRelatives(node_obj, children=True)
                if children and cmds.nodeType(children[0]) == 'gpuCache':
                    lastParent = children[0]
            except:
                import traceback
                self.engine.logger.info(traceback.format_exc())

                return None
        else:
            return None
        #
        while True:
            newParent = cmds.listRelatives(lastParent, parent=True, fullPath=True)
            if newParent == None:
                break
            lastParent = newParent[0]

        return lastParent

    def get_node_data(self, node):
        node_type = cmds.nodeType(node)
        self.engine.logger.info('NODE TYPE? {}, {}'.format(node, node_type))
        if node_type == 'reference':
            node_file_path = cmds.referenceQuery(node, filename=True, withoutCopyNumber=True)
            node_namespace = cmds.referenceQuery(node, namespace=True).replace(':', '')
        elif node_type == 'transform':
            node_file_path = cmds.getAttr("%s.cacheFileName" % node)
            node_namespace = None
        else:
            node_file_path = None
            node_namespace = None

        self.engine.logger.info("node file path: {}, node namespace: {}".format(node_file_path, node_namespace))
        return node_file_path, node_namespace

    def clear_breakdown_data(self, entity):
        # clear current breakdown and shot assets linking
        filters = [['sg_shot', 'is', entity],
                   ['project', 'is', self.engine.context.project]]
        breakdown = self.shotgun.find('CustomEntity30', filters)
        for bd in breakdown:
            self.shotgun.delete('CustomEntity30', bd["id"])
        # clear asset data
        self.shotgun.update(entity['type'], entity['id'], {'assets': []})

    def create_breakdown(self, shot_name, requiered_nodes, locked_data={}):

        shot_entity = self.ensure_shot_entity(shot_name)
        assets = []

        self.engine.logger.info('Clearing old breakdown data.....')
        self.clear_breakdown_data(shot_entity)

        self.engine.logger.info('Creating breakdown.....')
        for asset_node in requiered_nodes:
            root_node = self.get_root_node(asset_node)
            if root_node:
                file_path, namespace = self.get_node_data(asset_node)
                self.engine.logger.info('{} ------------------> {}'.format(namespace, file_path))
                asset_entity = self.ensure_breakdown_entity(shot_entity, file_path, namespace, lock_data=locked_data)
                if asset_entity:
                    self.engine.logger.info('-----------adding asset_entity> : {}'.format(asset_entity))
                    assets.append(asset_entity)

        # finally just update the assets shot field
        data = {'assets': assets}
        self.engine.logger.info('update breakdown>... {}'.format(data))

        self.engine.logger.info('SG updt>>> {}'.format(self.shotgun.update('Shot', shot_entity['id'], data)))

    def ensure_breakdown_entity(self, shot_entity, dependency_path, namespace, lock_data=None):
        """
        """
        lockedData = {}
        if lock_data is not None:
            lockedData = lock_data.copy()

        asset_entity = None

        paths = [dependency_path]
        fields = ['entity', 'project']
        data = sgtk.util.find_publish(self.tk, paths, fields=fields)

        if data:
            publish = data[dependency_path]

            asset_entity = publish['entity']

            namespace = namespace or asset_entity['name']

            breakdown_data = {'project': publish['project'],
                              'code': namespace,
                              'sg_asset': asset_entity,
                              'sg_shot': shot_entity}

            filters = [['sg_asset', 'is', asset_entity],
                       ['sg_shot', 'is', shot_entity],
                       ['code', 'is', namespace],
                       ['project', 'is', publish['project']]]

            breakdown = self.shotgun.find_one('CustomEntity30', filters)

            if not breakdown:
                breakdown = self.shotgun.create('CustomEntity30', breakdown_data)

            # update breakdow if is a locked version.
            if dependency_path in lockedData:
                locked_update = {
                    'sg_locked_version': lockedData[dependency_path]['publish'],
                    'description': lockedData[dependency_path]['description']
                }
                self.engine.logger.info("UPDATE LOCKED DATA: {0}".format(locked_update))
                self.shotgun.update('CustomEntity30', breakdown["id"], locked_update)

        return asset_entity

    # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def ensure_shot_entity(self, shot_name):
        """
        """

        current = self.sequence_shot_entities.get(shot_name)

        if not current:
            filters = [['code', 'is', shot_name],
                       ['project', 'is', self.context.project],
                       ['sg_sequence', 'is', self.context.entity]]
            self.engine.logger.info('Shot filters: {}'.format(filters))
            shot_entity = self.shotgun.find_one('Shot', filters)

            self.sequence_shot_entities['shot_name'] = shot_entity

        return self.sequence_shot_entities['shot_name']

    def get_shot_context(self, shot_name):
        """
        """
        self.engine.logger.info('Get shot context.....')

        error = 'Shot {} is not under Sequence context,' \
                ' shot must be linked to sequence in shotgun'.format(shot_name)

        context = self.context_by_shot_name.get(shot_name)
        self.engine.logger.info('Context found for shot: {}'.format(context))
        entity = self.ensure_shot_entity(shot_name)
        self.engine.logger.info('Entity found for shot: {}'.format(entity))

        if not context and entity is not None:
            filters = [['content', 'is', 'Layout'], ['entity', 'is', entity]]
            task = self.shotgun.find_one('Task', filters, ['entity', 'project', 'step'])
            if task is None:
                self.engine.logger.info('ERROR: Layout task missing from shot.')
                return None, 'Layout task missing from shot.'

            pprint.pprint(task)

            # un register folders
            cmd = self.tk.get_command("unregister_folders")
            cmd.execute({"entity": task})

            # context needs to have folders on disk
            self.tk.create_filesystem_structure('Task', task['id'], self.engine.name)

            context = self.tk.context_from_entity('Task', task["id"])

            self.context_by_shot_name[shot_name] = context
            error = None

        return context, error

    def get_next_maya_shot_publish_version(self, shot_name):
        """define version based on existing publishes
           but mantaining sequence one as fallback
        """

        if not shot_name in self.latest_shot_version_by_shot_name:
            shot_context, error = self.get_shot_context(shot_name)

            filters = [['entity', 'is', shot_context.entity],
                       ['task', 'is', shot_context.task],
                       #['name', 'is', self.current_publish_name],
                       ['project', 'is', shot_context.project],
                       ['published_file_type.PublishedFileType.code', 'is', 'Maya Scene']]

            fields = ['version_number']
            order = [{'field_name': 'version_number', 'direction': 'desc'}]

            publish = self.shotgun.find_one(
                'PublishedFile', filters, fields, order)

            if publish and publish.get('version_number'):
                self.engine.logger.info('1--------------------> {}'.format(publish['version_number']))
                self.latest_shot_version_by_shot_name[shot_name] = publish['version_number'] + 1
            else:
                self.latest_shot_version_by_shot_name[shot_name] = self.current_version_number

        self.engine.logger.info('2--------------------> {}'.format(self.latest_shot_version_by_shot_name[shot_name]))
        return self.latest_shot_version_by_shot_name[shot_name]

    def validate_version_number(self, fields):
        """ Validate if a path for a published file exists in publish path with version number

        :return:
            True: No published file version found.
            False: Published file with version number found in filesystem.
        """

        publish_template = "maya_shot_publish"
        p_template = self.tk.templates.get(publish_template)
        publish_scene_path = p_template.apply_fields(fields)

        return not os.path.exists(publish_scene_path)

    def get_publish_scene_path(self, shot_name, template_name):
        """
        """

        shot_context, error = self.get_shot_context(shot_name)

        template = self.tk.templates.get(template_name)

        # context.as_template_fields need to have folders on disk
        template_fields = shot_context.as_template_fields(template)

        self.engine.logger.info('scene_path template: {}'.format(template_fields))

        sequence_fields = self.current_template_fields.copy()
        sequence_fields.update(template_fields)
        template_fields = sequence_fields

        version_number = self.get_next_maya_shot_publish_version(shot_name)
        self.engine.logger.info('Next version number from publishes: {} '.format(version_number))

        # add missing "name" and "version"
        template_fields['name'] = self.current_publish_name
        template_fields['version'] = version_number
        template_fields['Shot'] = shot_name

        self.engine.logger.info("template fields: {}".format(template_fields))

        while not self.validate_version_number(template_fields):
            version_number += 1
            self.engine.logger.info('Next version number for unregistered publishes: {}'.format(version_number))
            template_fields['version'] = version_number

        self.engine.logger.info('NEXT VERSION NUMBER SELECTED TO PUBLISH: {}'.format(self.latest_shot_version_by_shot_name[shot_name]))
        scene_path = template.apply_fields(template_fields)

        #using always the last version number in workarea too
        while os.path.exists(scene_path):
            version_number += 1
            template_fields['version'] = version_number
            scene_path = template.apply_fields(template_fields)
            if not os.path.exists(scene_path):
                break
        self.engine.logger.info('LAST VERSION NUMBER SELECTED FOR WORKAREA: {}'.format(self.latest_shot_version_by_shot_name[shot_name]))

        sgtk.util.filesystem.ensure_folder_exists(os.path.dirname(scene_path))

        return scene_path

    def publish_scene(self, shot_name):
        """
        """

        shot_context, error = self.get_shot_context(shot_name)

        work_file_path = self.get_publish_scene_path(
            shot_name, "maya_shot_work")
        publish_file_path = self.get_publish_scene_path(
            shot_name, "maya_shot_publish")

        shutil.copy(work_file_path, publish_file_path)

        publish_data = {
            "tk": self.tk,
            "context": shot_context,
            "path": publish_file_path,
            "name": self.current_publish_name,
            "version_number": self.get_next_maya_shot_publish_version(shot_name),
            "comment": 'Shot split from Sequence',
            "published_file_type": "Maya Scene",
            "task": shot_context.task,
            "created_by": {'type': 'HumanUser', 'id': 385},
        }

        sgtk.util.register_publish(**publish_data)

    def do_change_context(self, new_context):

        try:
            ctx_result, ctx_error = self.engine.change_context(new_context)
        except:
            # Fix shot context
            # Option 1: Forze values for context
            # Option 2: Script for fix context via tk
            self.engine.logger.info('!!!!!!!! {}'.format(new_context.to_dict()))

            current_file = pm.system.sceneName()
            self.engine.logger.info('current_file: {}'.format(current_file))
            # tk = sgtk.sgtk_from_path(current_file)
            tk = self.engine.sgtk
            self.engine.logger.info('tk: {}'.format(tk))
            self.engine.change_context(new_context)

    def all_tasks_generator(self, publish_tree):

        for item in publish_tree:
            for task in item.tasks:
                self.engine.logger.info('TASK: {}'.format(task))

    def run_shot_publish(self, shot_name, publish_name, camera_name):

        self.engine.logger.info('\nRUN SHOT PUBLISH ---------- :  ')
        # get the publish app instance from the engine's list of configured apps
        publish_app = self.engine.apps.get("tk-multi-publish2")
        shot_context, error = self.get_shot_context(shot_name)

        if not shot_context or not shot_context.entity or not shot_context.project:
            return 'context', 'Context not found, shotgun linking to current Sequence may be missing.'

        self.engine.logger.info('about to change context...')
        self.do_change_context(shot_context)
        self.engine.logger.info('context changed: {}'.format(self.engine.context))

        # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----
        self.sync_frame_range()

        # Enable color management
        self.enable_color_management()

        pm.saveFile(force=True, type='mayaAscii')

        self.engine.logger.info('camera_name: {}'.format(camera_name))
        # create a new publish manager instance
        manager = publish_app.create_publish_manager()

        self.engine.logger.info('Manager is loaded...')

        # now we can run the collector that is configured for this context
        current_session = manager.collect_session()

        self.engine.logger.info("sequence file path: {}".format(self.sequence_file_path))
        sequence_name = os.path.basename(self.sequence_file_path)

        for item in current_session:
            self.engine.logger.info('-------------------->> {}'.format(item.name))
            self.engine.logger.info(publish_name)
            # This is the actual publish session for the shot
            if item.name == publish_name:
                item.description = 'Splitted from sequence: {}'.format(sequence_name)
                item.properties["batch"] = True

            if item.name == "Shot Media Review Publish":
                item.properties["batch"] = camera_name
                self.engine.logger.info('BATCH: {}'.format(item.properties["batch"]))

        self.engine.logger.info('Session collected by manager')


        # validate the items to publish on own way to properly collect errors
        # this is basically the same as calling manager.validate() but cutom excpetion handling
        # https://developer.shotgridsoftware.com/tk-multi-publish2/_modules/tk_multi_publish2/api/manager.html#PublishManager.validate
        # Note that this is only necesary on python 2, since python 3 already
        # have a proper way to include tracebacks in the exceptions
        # https://peps.python.org/pep-3109/
        def customPublishValidation():

            # we'll use this to build a list of tasks that failed to validate
            failed_to_validate = []

            def task_cb(task):
                error = None
                # do the actual validation and send the status back to the generator
                # so that it can react to the results. This is used, for example, by
                # the UI's generator to update the display of the task as it is
                # being processed.
                try:
                    is_valid = task.validate()
                except Exception as e:
                    is_valid = False
                    error = e
                    if sys.version_info < (3,0):
                        setattr(error, '__traceback__', traceback.format_exc())

                # if the task didn't validate, add it to the list of tasks that
                # failed.
                if not is_valid:
                    failed_to_validate.append((task, error))

                return (is_valid, error)

            manager._process_tasks(None, task_cb)

            # execute the post validate method of the phase phase hook
            manager._post_phase_hook.post_validate(manager.tree,)

            return failed_to_validate

        tasks_failed_validation = customPublishValidation()

        self.engine.logger.info(
            'tasks_failed_validation: {}'.format(tasks_failed_validation)
        )

        if tasks_failed_validation:
            self.engine.logger.info('Errors found processing shot...')
            # extend information collected from the validate method
            # for each failed validation, we get a PublisherTask and an Exception
            # https://developer.shotgridsoftware.com/tk-multi-publish2/api.html#tk_multi_publish2.api.PublishManager.validate

            failed_validations = []

            for validation in tasks_failed_validation:
                task, error = validation

                detailed_task = {
                    'PublishTask': task,
                    'serialization': {
                        'plugin_name': task.plugin.name,
                        'plugin_path': task.plugin.path
                    }
                }
                detailed_error = error
                if isinstance(error, Exception):
                    detailed_error = {
                        'exception': error,
                        'traceback': error.__traceback__
                    }

                failed_validations.append((detailed_task, detailed_error))

            self.engine.logger.info(
                'failed_validations: {}'.format(
                    pprint.pformat(failed_validations)
                )
            )

            return 'validation', failed_validations

        self.engine.logger.info('*********************    PUBLISHING')
        publish_result = manager.publish()
        if publish_result:
            return 'publish', publish_result
        else:
            self.engine.logger.info('*********************    FINALIZING')
            finalize_result = manager.finalize()
            if finalize_result:
                return 'finalize', finalize_result

        self.engine.logger.info('*********************   run_shot_publish DONE!!!')

        return None, []

    def sync_frame_range(self):

        # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

        self.engine.logger.info('Setting frame range to shot...')
        # Set frame range for shot
        framerange_app = self.engine.apps.get("tk-multi-setframerange")
        (new_in, new_out) = framerange_app.get_frame_range_from_shotgun()
        framerange_app.set_frame_range(new_in, new_out)

        self.engine.logger.info("Frame range seted to: {}".format(new_in, new_out))

        # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def enable_color_management(self):

        # Enable Color Management
        color_management_enabled = cmds.colorManagementPrefs(q=True, cmEnabled=True)
        if color_management_enabled is False:
            cmds.colorManagementPrefs(e=True, cmEnabled=True)

        # ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ---- ----

    def get_all_assemblies(self):
        result = []

        ignored_assemblies = [
            "persp",
            "top",
            "front",
            "side"
        ]

        list_of_assemblies = pm.ls(assemblies=True)

        for assembly_ in list_of_assemblies:
            assembly_name = assembly_.name()
            if assembly_name not in ignored_assemblies:
                result.append(assembly_)

        return result

    def turn_visibility_on(self, list_of_objs):
        for obj in list_of_objs:
            try:
                obj.visibility.set(True)
            except:
                pass

    def extract_shot(self, shot_node_name):
        """destructuve process of demoved references and animation
        from a sequence file so that only those particualry requiered
        for an specific shot will remain in the scene
        """

        shot_node = pm.ls(shot_node_name, type="shot")[0]

        shot_name = shot_node.getShotName()

        self.engine.logger.info('Splitting started - extracting shot: {}'.format(shot_name))

        if not shot_node:
            message = "Can't find a shot node with its name set to : {}".format(shot_node_name)
            raise Exception(message)

        shot_context, error = self.get_shot_context(shot_name)
        if shot_context is None:
            return 'Context error', error

        #self.parent.logger.info("CurrentProgress: {}".format(45))

        split_string = shot_node.assets.get()

        # --------------------------------------------------------------------------------

        requiered_nodes = []

        if split_string is not None:
            # Remove possible empty assets to avoid errors by removing heading or
            # trailing ";", or replacing ";;" by ";"
            if split_string.startswith(";"):
                split_string = split_string[1:]
            if split_string.endswith(";"):
                split_string = split_string[:-1]
            split_string = split_string.replace(";;", ";")


            requiered_nodes.extend(split_string.split(";"))

        # --------------------------------------------------------------------------------

        # collect info from shot node
        camera_shape = pm.PyNode(shot_node.getCurrentCamera())
        if isinstance(camera_shape, pm.nt.Camera):
            camera_node = camera_shape.getParent()
        else:
            camera_node = camera_shape

        camera_name = camera_node.name()

        try:
            camera_reference = pm.referenceQuery(camera_shape, referenceNode=True)
            self.engine.logger.info('Camera Reference >> {}'.format(camera_reference))
        except:
            # raise Exception("Shot camera error: {}, assigned camera is not a reference.".format(camera_shape))
            return "Shot camera error", "{} assigned camera is not a reference.".format(camera_shape)


        # collect dependencies
        requiered_nodes.append(camera_reference)
        top_reference_nodes = pm.listReferences(recursive=False)

        # delete all the shot nodes different that current shot
        for shot_element in pm.ls(type='shot'):
            if shot_element != shot_node:
                pm.lockNode(shot_element, lock=False)
                pm.delete(shot_element)

        # delete all the gpu nodes if they are not required
        gpu_cache_nodes = pm.ls(type='gpuCache')
        for gpu_cache in gpu_cache_nodes:
            gpu_cache_transform = gpu_cache.getParent()
            # Take care only for the top root nodes, ignore nested
            if not gpu_cache_transform.getParent():
                if gpu_cache_transform.name() not in requiered_nodes:
                    pm.delete(gpu_cache_transform)

        # get first and last frame for the shot
        first_frame = shot_node.getStartTime()
        last_frame = shot_node.getEndTime()

        #self.parent.logger.info("CurrentProgress: {}".format(50))

        # bake animation from constrains before removing references
        self.bakeConstrains(first_frame, last_frame)

        #self.parent.logger.info("CurrentProgress: {}".format(55))

        # delete all the references if they are not required
        for reference_node in top_reference_nodes:
            if reference_node.refNode.name() not in requiered_nodes:
                reference_node.remove()

        #self.parent.logger.info("CurrentProgress: {}".format(60))

        # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

        self.keyframesManager \
            .limit_animation_curves(first_frame, last_frame)

        self.keyframesManager \
            .move_animation_with_cutItem_data(first_frame, shot_node)

        #self.parent.logger.info("CurrentProgress: {}".format(65))

        # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

        locked_versions = self.get_locked_versions(requiered_nodes)
        self.engine.logger.info('locked_versions: {}'.format(locked_versions))

        # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

        sequence = self.engine.shotgun.find_one(
            "Sequence",
            [['id', 'is', self.engine.context.entity['id']]],
            ['episode.Episode.code', 'code']
        )

        # ----------------------------------------------------------------------
        # Legacy FZS fix for specific episodes
        # if sequence['episode.Episode.code'] not in ['e105', 'e108', 'e112', 'e110']:
        #     # swap camera rig
        #     self.swap_camera_rig(camera_node, camera_reference)
        # else:
        #     self.engine.logger.info("\n\nNO CAMERA SWAP ------------- \n\n")
        # ----------------------------------------------------------------------

        # swap gpu caches to last published version
        self.swap_caches(shot_context, locked_versions)

        # swap assets from mid rigs to full rigs
        self.swap_rigs_to_full()

        # swap audio to current shot
        audio_result = self.swap_audio_to_shot(shot_name, shot_context)
        self.engine.logger.info("audio result after audio swap: {}".format(audio_result))

        if audio_result is None:
            self.engine.logger.info('AUDIO NOT SWAPED TO SHOT, NO AUDIO FOR SHOT FOUND!')
            # raise Exception("No audio for shot '{}' found.".format(shot_name))
            return "Audio error", "No audio for shot '{}' found.".format(shot_name)

        #self.parent.logger.info("CurrentProgress: {}".format(70))

        # Sanity after rig swap
        self.sanity_source()

        # Turn on visibility of assemblies (root nodes)
        list_of_assemblies = self.get_all_assemblies()
        self.turn_visibility_on(list_of_assemblies)

        # ready to save file
        shot_publish_scene_path = self.get_publish_scene_path(shot_name, "maya_shot_work")
        pm.renameFile(shot_publish_scene_path)
        pm.saveFile(force=True, type='mayaAscii')

        self.engine.logger.info('shot_publish_scene_path: {}'.format(shot_publish_scene_path))
        #print 2222, cmds.sound(cmds.ls(type='audio')[0], f=True, q=True)
        #print 'shot_publish_scene_path: ', shot_publish_scene_path['boom']

        # ----------------------------------------------------------------------
        # unnecessary lines???
        # aPlayBackSliderPython = mel.eval('$tmpVar=$gPlayBackSlider')
        # esto = cmds.timeControl(aPlayBackSliderPython, query=True, sound=True)


        # # Reopen shot file
        # pm.newFile(force=True)
        # pm.openFile(shot_publish_scene_path, force=True)
        # self.engine.logger.info('shot_publish_scene_path: {}'.format(shot_publish_scene_path))
        # ----------------------------------------------------------------------

        if not self.batch_split:
            self.huds_display()

        self.remove_unknown_plugins()

        #self.parent.logger.info("CurrentProgress: {}".format(75))

        try:
            # Enable color management
            self.enable_color_management()

            self.engine.logger.info('Creating Breakdown entities...')
            self.create_breakdown(shot_name, requiered_nodes, locked_versions)

            #self.parent.logger.info("CurrentProgress: {}".format(80))

            self.engine.logger.info("Starting publish process for shot...")
            shot_publish_name = os.path.basename(shot_publish_scene_path)

            publish_error, info = self.run_shot_publish(
                shot_name, shot_publish_name, camera_name
            )

            #self.parent.logger.info("CurrentProgress: {}".format(85))

            self.engine.logger.info(
                'publish_result: {} {}'.format(
                    publish_error, type(publish_error)
                )
            )
            self.engine.logger.info('result info: {} {}'.format(info, type(info)))

            for ex in info:
                self.engine.logger.info(ex)
                self.engine.logger.info(ex[0])
                self.engine.logger.info(ex[1])

            if publish_error is None:
                # Validate breakdown
                self.engine.logger.info('BREAKDOWN validation started ...')
                breakdown_validation = breakdownValidation(shot_name, self.sequence, self.shotgun, self.engine)
                result = breakdown_validation.layout_breakdown_sanity()

                self.engine.logger.info("breakedown validation result.. {}".format(result))
                if result:
                    self.engine.logger.info("breakedown validation result.. ")
                    for r in result:
                        self.engine.logger.info("Breakdown error: {}".format(r))

                    #return 'validation', result

        except:
            publish_error = 'execution'
            import traceback
            info = traceback.format_exc()

        return publish_error, info

    def build_locked_data(self, reference_path, publish_data, node):

        lock_data = {}

        publish = publish_data[reference_path]

        lock_enabled_attr_name = "lockedVersionEnabled"
        lock_description_attr_name = "lockedVersionDescription"
        self.engine.logger.info("Is locked enabled? {}".format(node))
        if cmds.attributeQuery(lock_enabled_attr_name, node=node, exists=True):
            lockedVersionEnabled = cmds.getAttr('{0}.{1}'.format(node, lock_enabled_attr_name))
            if lockedVersionEnabled:
                self.engine.logger.info("Yes, locked is enabled...")
                # Get locked data
                lockedVersionDescription = cmds.getAttr('{0}.{1}'.format(node, lock_description_attr_name))
                # Update locked data por currente reference path
                lock_data[reference_path] = {
                    'is_locked': lockedVersionEnabled,
                    'description': lockedVersionDescription,
                    'publish': publish,
                }
            else:
                self.engine.logger.info("No, locked is not enabled...")

        return lock_data

    def get_file_from_node(self, node):

        file_path = None
        node_type = cmds.nodeType(node)
        self.engine.logger.info("node_type: {}".format(node_type))
        if node_type == 'transform':
            # this one gets tricky, but we in theory now that its a gpuCache
            # Lets first get the children and verify its indeed a gpuCache
            children = cmds.listRelatives(node, children=True)
            if children and cmds.nodeType(children[0]) == 'gpuCache':
                child = children[0]
                file_path = cmds.getAttr("%s.cacheFileName" % child)
            else:
                # node not accepted, skiped
                self.engine.logger.info("Warning: Skiped not valid children node type {0}, {1}".format(node_type, children[0]))
        elif node_type == 'reference':
            file_path = cmds.referenceQuery(node, filename=True)
        else:
            # node not accepted, skiped
            self.engine.logger.info("Warning: Skiped not valida node type {0}, {1}".format(node_type, node))

        return file_path

    def get_locked_versions(self, requiered_nodes):


        self.engine.logger.info("Get locked version...")
        locked_data = {}
        for asset_node in requiered_nodes:
            self.engine.logger.info("-" * 80)
            self.engine.logger.info("asset_node: {}".format(asset_node))
            # Continue only for nodes with a valid file path as published file
            file_path = self.get_file_from_node(asset_node)
            self.engine.logger.info("file_path: {}".format(file_path))

            if not file_path:
                continue

            if file_path.endswith("}"):
                num_pattern = re.search(r"{\d+}", file_path).group(0)
                if num_pattern is not None:
                    file_path = file_path.replace(num_pattern, "")
            paths = [file_path]
            fields = ['entity', 'project']
            sg_data = sgtk.util.find_publish(self.tk, paths, fields=fields)
            if sg_data:
                self.engine.logger.info("Path is a valid published file...")
                # Get brakdown locked version if locked enabled
                root_node = self.get_root_node(asset_node)
                self.engine.logger.info("root_node: {}".format(root_node))
                locked_data.update(self.build_locked_data(file_path, sg_data, root_node))

            else:
                # The file referenced is not a valid publish
                self.engine.logger.info("Warning: The file referenced is not a valid publish {0}".format(file_path))

        return locked_data

    def remove_non_references(self):

        list_of_file_nodes = pm.ls(type="file")
        for file in list_of_file_nodes:

            # Agregar reference querty pa si la imagen es de una referencia
            try:
                pm.referenceQuery(file, filename=True)
            except:
                # print("File not from reference")
                pm.select(file, replace=True)
                imageName = pm.getAttr(".fileTextureName")
                imageNameFull = imageName.split('{')
                self.engine.logger.info('\t {}'.format(imageNameFull[0]))
                self.engine.logger.info('\tdelete: {}'.format(file))
                cmds.delete(str(file))

    def remove_unknown_nodes(self):

        unknownNodes = cmds.ls(type="unknown")
        unknownNodes += cmds.ls(type="unknownDag")
        for item in unknownNodes:
            if cmds.objExists(item):
                cmds.lockNode(item, lock=False)
                cmds.delete(item)

        pattern = re.compile(r'.*unknown.*', re.IGNORECASE)
        list_of_unknown_nodes = pm.ls(regex=pattern)
        if list_of_unknown_nodes:
            for each in list_of_unknown_nodes:
                node = pm.PyNode(each)
                pm.lockNode(node, lock=False)
                pm.general.delete(node)

    def remove_unknown_plugins(self):
        missing_plugins = pm.unknownPlugin(query=True, l=True)

        if missing_plugins:
            for plugin in missing_plugins:
                try:
                    pm.unknownPlugin(plugin, remove=True)
                except:
                    pm.warning("Unable to remove: {0}".format(plugin))

            self.engine.logger.info("Finished removing plugins")

    def list_of_not_connected_constraints(self):
        result = []
        list_of_constraints = pm.ls(type="constraint")

        for constraint in list_of_constraints:
            if len(constraint.listConnections()) == 0:
                result.append(constraint)

        return result

    def delete_not_connected_constraints(self):
        list_of_constraints = self.list_of_not_connected_constraints()

        for constraint in list_of_constraints:
            try:
                pm.delete(constraint)
            except:
                pm.warning("Unable to delete {0}.".format(
                    constraint.longName()))

    def huds_display2(self):
        self.engine.logger.info('ReSoLvInG HuDs dIsPlAy')
        set_of_required_huds = {
            'HUDFocalLength',
            'HUDShotCode',
            'HUDFocusDistance',
            'HUDFStop'
        }

        #
        for hud in set_of_required_huds:
            hud_exists = pm.headsUpDisplay(hud, exists=True)
            if hud_exists:
                hud_is_visible = pm.headsUpDisplay(
                    hud,
                    visible=True,
                    query=True
                )
                if hud_is_visible != True:
                    try:
                        pm.headsUpDisplay(hud, visible=True, edit=True)
                    except:
                        pm.warning(
                            "Unable to set visibility for {0}".format(hud)
                        )

    def huds_display(self):

        self.engine.logger.info('Huds display CHECK ')

        set_of_required_huds = {
            'HUDFocalLength',
            'HUDShotCode',
            'HUDFocusDistance',
            'HUDFStop'
        }

        result = []

        list_of_all_huds = pm.headsUpDisplay(listHeadsUpDisplays=True)

        for hud in list_of_all_huds:
            if pm.headsUpDisplay(hud, exists=True):
                pm.headsUpDisplay(hud, visible=False, edit=True)

        for hud in set_of_required_huds:
            if pm.headsUpDisplay(hud, exists=True):
                pm.headsUpDisplay(hud, visible=True, edit=True)

    def sanity_source(self):
        """
        """

        if not self.batch_split:
            self.huds_display()

        self.remove_non_references()

        self.remove_unknown_plugins()

        self.remove_unknown_nodes()

        self.delete_not_connected_constraints()

        cmds.unloadPlugin("AbcExport.mll")
        cmds.loadPlugin("AbcExport.mll")

    def collect_scene_shots(self):
        """
        Select the list of shots to be splitted, if a defined shot value is initalized
        the list will return only that shot, else every shot found will be returned
        :return:
        A list containing the shot nodes to process with split
        """

        self.engine.logger.info('Collecting shots from scene...')

        shot_list = []

        list_of_shots = pm.ls(type='shot')
        self.engine.logger.info('list_of_shots: {}'.format(list_of_shots))

        shot_list = list_of_shots

        # Select a custom selected shot or all shots
        if self.shot_name_split:
            shot_list = [shot for shot in shot_list if shot.getShotName() == self.shot_name_split]

        return shot_list

    def extract_shot_shots(self):
        """
        """

        self.engine.logger.info("Preparing shot extraction...")


        self.sanity_source()

        #self.parent.logger.info("CurrentProgress: {}".format(35))

        # Get current sequence entity related
        self.sequence = copy.deepcopy(self.engine.context.entity)

        list_of_shots = self.collect_scene_shots()


        publish_shot_errors = {}
        for shot in list_of_shots:
            #   . . . . . . . . . . . . . . . . . . . . . .
            shot_node_name = shot.name()
            shot_node = pm.ls(shot_node_name, type="shot")[0]
            shot_name = shot_node.getShotName()

            self.engine.logger.info('Extracting shot: {}'.format(shot_name))

            #if 'e118_lab060_0120a' in shot_name:
            shot_context, error = self.get_shot_context(shot_name)
            #self.parent.logger.info("CurrentProgress: {}".format(40))
            if shot_context is None:
                msg = ('Shot {} is not under Sequence context, shot must be '
                       'linked to sequence in shotgun').format(shot_name)
                publish_shot_errors[shot_name] = {'type': 'context', 'error': error}
            else:
                error_type, error_data = self.extract_shot(shot_node_name)
                if error_type is not None:
                    publish_shot_errors[shot_name] = {'type': error_type, 'error': error_data}

                # restore orgininal sequence file to
                if self.shot_name_split is None:

                    self.engine.logger.info('\nReopening ORIGINAL FILE ---------------------------')
                    #   . . . . . . . . . . . . . . . . . . . . . .
                    pm.newFile(force=True)

                    pm.openFile(self.sequence_file_path, force=True)
                    self.engine.logger.info('\nORIGINAL FILE RESTORED  ---------------------------')

                    # Enable color management
                    self.enable_color_management()


        self.engine.logger.info('\n\n----------------------------------------------------------')

        self.engine.logger.info('# Shots found: {}'.format(len(list_of_shots)))
        # self.engine.logger.info(len(list_of_shots))
        self.engine.logger.info('# Shots with errors: {}'.format(len(publish_shot_errors)))
        # self.engine.logger.info(len(publish_shot_errors))


        if publish_shot_errors:
            for shot in publish_shot_errors:
                self.engine.logger.info(shot)
                shot_errors = publish_shot_errors[shot]
                self.engine.logger.info(
                    'Error type: {}'.format(shot_errors['type'])
                )
                self.engine.logger.info(
                    'Error: {}'.format(pprint.pformat(shot_errors['error']))
                )

                if shot_errors['type'] in ['context', 'execution']:
                    self.engine.logger.info('\t\terror: {}'.format(shot_errors['error']))
                else:
                    for task, errors in shot_errors['error']:
                        self.engine.logger.info('Errors found in publish....')
                        self.engine.logger.info(
                            'ERROR - Detail:\n{0}\n{1}'.format(
                                pprint.pformat(task),
                                pprint.pformat(errors)
                            )
                        )

        return publish_shot_errors


HookBaseClass = sgtk.get_hook_baseclass()


class SequenceSplitterHook(HookBaseClass):

    def execute(self, entity_type, entities, other_params, **kwargs):

        # ensure metasync framework is available, but only load it once
        self.metasync = self.load_framework("mty-framework-metasync")
        self.parent.logger.info("metasync: {}".format(self.metasync))
        metasync_path = os.path.join(self.metasync.current_path(), "python")
        sys.path.append(metasync_path)

        shot_filter = other_params.get("filter_shot", None)
        split_file = other_params.get("file_path", None)
        batch_exec = other_params.get("batch_exec", False)
        process_in_farm = other_params.get("process_in_farm", False)
        if process_in_farm:
            self.parent.logger.info("Processing in farm...")
        else:
            self.parent.logger.info("Processing locally...")
        # It is important to have this variable regardless of where the split is processed,
        # otherwise the split will create another separate job instead of processing it
        # directly here in the spliter.
        os.environ["SG_PROCESSING_SECONDARY_OUTPUTS"] = "true"


        transfersManager = self.metasync.transfersManager

        splitter = SequenceSplitter(
            self.parent.engine,
            self.sgtk,
            self.parent.shotgun,
            transfersManager,
            shot_split=shot_filter,
            sequence_path=split_file,
            exc_in_batch=batch_exec
        )

        #self.parent.logger.info("CurrentProgress: {}".format(30))

        shot_errors = splitter.extract_shot_shots()

        # ----------------------------------------------------------------------
        # Temporarily replace the result of the hook, because apparently the
        # current result 'shot_errors' doesn't contain the required keys

        list_of_errors = []
        list_of_messages = []
        for shot in shot_errors:

            error = shot_errors[shot].get("type")
            if error:
                list_of_errors.append(error)

            message = shot_errors[shot].get("error")
            if message:
                list_of_messages.append(message)

        result = {
            "succes": [self.parent.context.entity],
            "errors": list_of_errors,
            "messages": list_of_messages
        # ----------------------------------------------------------------------

        }

        return result # Eventually should return 'shot_errors' with the right format

