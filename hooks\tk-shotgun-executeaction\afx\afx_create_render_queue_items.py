#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

"""
An app that loads a template specific to the project and sets all relevant
project settings found in SG:
    shot settings:
        - shot sg_cut_in (first shot frame)
        - shot sg_cut_out (last shot frame)
    project settings:
        - frame rate
        - resolution
        - working color space

"""

from tank import Hook
from tank import TankError
from tank.platform import Application
from tank.platform.qt import QtCore, QtGui
import tank

import os
import re
import pprint


class ProcessItemsHook(Hook):
    def execute(self, **kwargs):

        self.parent.engine.logger.info(
            "execute action create_render_queue_items start. {}".format("-" * 80)
        )

        result = self.create_render_queue_items()

        self.parent.engine.logger.info(
            "execute action create_render_queue_items end. {}".format("-" * 80)
        )

        return result

    def create_render_queue_items(self):

        result = {"succes": [1], "messages": [], "errors": []}

        adobe = self.parent.engine.adobe

        # get output folder (images)
        images_folder_path = self.get_output_folder()
        if not images_folder_path:
            msg = "Couldn't get the project path. Ensure it has been saved and try again"
            self.parent.engine.logger.info("error: {}".format(msg))
            self.show_message(msg, icon="Critical")
            # adobe.alert(msg)

            return result

        self.parent.engine.logger.info("images_folder_path: {}".format(images_folder_path))

        # first clear all existing items in render queue
        list_of_render_queue_items = self.collect_all_items_in_render_queue()
        self.remove_items(list_of_render_queue_items)

        # collect output compositions
        list_of_output_comps = []

        compositions = self.collect_all_compositions_in_project()
        if compositions:
            self.parent.engine.logger.debug(
                "compositions:\n{}".format(pprint.pformat(compositions))
            )
            list_of_output_comps = self.filter_output_compositions(compositions)
            # list_of_output_comps = list(set(list_of_output_comps))
            self.parent.engine.logger.info(
                "found {} output compositions".format(len(list_of_output_comps))
            )
        else:
            self.parent.engine.logger.error("Couldn't get any composition!")

        if list_of_output_comps:
            for comp in list_of_output_comps:
                rq_item = self.create_render_item(comp)
                if not rq_item:
                    continue
                self.set_setting_to_rq_item(comp, rq_item, images_folder_path)

        output_comps_num = len(list_of_output_comps)
        comp_suffix = "" if output_comps_num == 1 else "s"
        composition_names = [comp.name for comp in list_of_output_comps]
        msg = (
            "Finished creating render queue item{1} for {0} output composition{1}.\n\n{2}"
        ).format(output_comps_num, comp_suffix, pprint.pformat(composition_names))

        self.show_message(msg, icon="Information")
        # alert_box = adobe.alert(msg)

        return result

    def collect_all_compositions_in_project(self):
        compositions = []

        adobe = self.parent.engine.adobe

        project_items = adobe.app.project.items

        for i, item in enumerate(self.parent.engine.iter_collection(project_items)):
            if item.typeName == "Composition":
                compositions.append(item)

        return compositions

    def filter_output_compositions(self, list_of_comps):
        """
        Filters a list of comps to get only comps which name start with 'Output'
        It is NOT case sensitive so either 'Output' or 'output' should work.
        """

        output_comps = []

        for comp in list_of_comps:
            # Match comp name
            original_comp_name = str(comp.name)
            name_pattern = re.compile(r"(?P<output_tag>^[Oo]utput)")
            match_name = re.match(name_pattern, original_comp_name)

            # Match output comp comment (metadata)
            original_comp_comment = comp.comment
            output_comp_pattern = re.compile(
                (
                    r"(?P<head>.+)?"
                    r"(?P<AFX_comp>[afxAFX]{3} [Oo]ut(put)? [Cc]omp([ositne]+)?)"
                    r"(?P<tail>.+)?"
                )
            )
            match_output_comp = re.match(output_comp_pattern, original_comp_comment)

            if match_name and match_output_comp:
                self.parent.engine.logger.debug(
                    "match_name.groupdict():\n{}".format(
                        pprint.pformat(match_name.groupdict())
                    )
                )
                self.parent.engine.logger.debug(
                    "match_output_comp.groupdict():\n{}".format(
                        pprint.pformat(match_output_comp.groupdict())
                    )
                )
                if comp not in output_comps:
                    output_comps.append(comp)

        return output_comps

    def get_output_folder(self):
        """
        Tries to resolve the images folder to use it as base for the output
        paths. It defaults to the 'images' folder
        """

        adobe = self.parent.engine.adobe

        file_path = None
        file_obj = adobe.app.project.file
        if file_obj != None:
            file_path = file_obj.fsName

        if not file_path or "tk-shotgun-executeaction" in file_path:
            return None

        self.parent.engine.logger.info("file_path: {}".format(file_path))
        comp_root = os.path.dirname(os.path.dirname(file_path))
        images_folder_path = os.path.join(comp_root, "images")

        return images_folder_path

    def collect_all_items_in_render_queue(self):
        render_queue_items = []

        adobe = self.parent.engine.adobe

        for i, queue_item in enumerate(
            self.parent.engine.iter_collection(adobe.app.project.renderQueue.items)
        ):
            render_queue_items.append(queue_item)

        return render_queue_items

    def remove_items(self, list_of_items):
        for item in list_of_items:
            item.remove()

    def create_render_item(self, comp):
        """ Creates a new render queue item for the input comp"""

        adobe = self.parent.engine.adobe

        rq_item = adobe.app.project.renderQueue.items.add(comp)

        return rq_item

    def set_setting_to_rq_item(self, comp, rq_item, dest_folder):
        """ sets render settings to render queue item """

        adobe = self.parent.engine.adobe

        # Get settings from tk-aftereffects.yml
        out_module_setting = self.parent.get_setting("output_module")
        padding = self.parent.get_setting("padding")
        sequence_exension = self.parent.get_setting("sequence_extension")

        self.parent.engine.logger.info("out_module_setting: {}".format(out_module_setting))
        self.parent.engine.logger.info("padding: {}".format(padding))
        self.parent.engine.logger.info("sequence_exension: {}".format(sequence_exension))

        # Apply output module using the output module obtained from the settings
        output_module_obj = rq_item.outputModule(1)
        output_module_obj.applyTemplate(out_module_setting)

        # Build and apply output path
        sequence_name = "{0}_[{1}]{2}".format(
            comp.name, "#" * padding, sequence_exension
        )
        output_path = os.path.join(dest_folder, comp.name, sequence_name)
        self.parent.engine.logger.info("output_path: {}".format(output_path))
        output_module_obj.file = adobe.File(output_path)

        if not os.path.exists(os.path.dirname(output_path)):
            os.makedirs(os.path.dirname(output_path))

    def show_message(self, msg, icon=None):
        """
        Shows a message box with the input message
        """

        from sgtk.platform.qt import QtCore, QtGui

        icons_dict = {
            "NoIcon": QtGui.QMessageBox.NoIcon,
            "Question": QtGui.QMessageBox.Question,
            "Information": QtGui.QMessageBox.Information,
            "Warning": QtGui.QMessageBox.Warning,
            "Critical": QtGui.QMessageBox.Critical,
        }

        if not icon or icon not in icons_dict.keys():
            icon_selection = icons_dict["NoIcon"]
        else:
            icon_selection = icons_dict.get(icon, QtGui.QMessageBox.Information)

        msg_error = QtGui.QMessageBox()
        msg_error.setWindowTitle("Finished execution.")
        msg_error.setText(msg)
        msg_error.setIcon(icon_selection)
        msg_error.exec_()
