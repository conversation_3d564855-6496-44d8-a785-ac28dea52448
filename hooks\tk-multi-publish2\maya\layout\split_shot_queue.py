# -*- coding: utf-8 -*-
# Standard library:
import os
import json
import pprint
import subprocess
import inspect
from datetime import datetime
#   .   .   .   .   .   .   .   .   .   .   .
# Third party:
import sgtk
from pymel import core as pm
from maya import cmds
#   .   .   .   .   .   .   .   .   .   .   .
# Project:
# ================================================================

pp = pprint.pprint
pf = pprint.pformat


HookBaseClass = sgtk.get_hook_baseclass()


class MayaShotDataValidationPlugin(HookBaseClass):

    def __init__(self, parent):
        super(MayaShotDataValidationPlugin, self).__init__(parent)
        self.core_maya_tools = self.load_framework("mty-framework-coremayatools")
        self.composer = self.core_maya_tools.checks.composer


    @property
    def description(self):
        return """
            <p>
            This plugin handles the validation of the
            shot node data neccesary for publishing
            a sequence.
            </p>
        """

    @property
    def settings(self):

        # inherit the settings from the base publish plugin
        plugin_settings = super(MayaShotDataValidationPlugin, self).settings or {}

        # settings specific to this class
        review_publish_settings = {
            "Work Template": {
                "type": "template",
                "default": None,
                "description": "Template path for the current scene file. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },
            "Primary Publish Template": {
                "type": "template",
                "default": None,
                "description": "Template path for main scene publish. Should"
                               "correspond to a template defined in "
                               "templates.yml.",
            },

            "Valid Step Names": {
                "type": "list",
                "default": None,
                "description": "Step names allowed for the splitting process.",
            },
            "Valid Task Names": {
                "type": "list",
                "default": None,
                "description": "Valid task names for the splitting process.",
            }
        }

        # update the base settings
        plugin_settings.update(review_publish_settings)

        return plugin_settings


    # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

    @property
    def item_filters(self):
        return["maya.session.splits"]

    # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

    def accept(self, settings, item):

        # This plugin must only be acepted in the listed task
        valid_step_names = settings["Valid Step Names"].value
        valid_tasks_names = settings["Valid Task Names"].value

        step_name = self.parent.engine.context.step["name"].lower()
        task_name = self.parent.engine.context.task["name"].lower()

        if step_name in valid_step_names and task_name in valid_tasks_names:
            # enssure to have a shot list in the item properties
            if item.properties.get("shot_list"):
                return{
                    "accepted": True,
                    "enabled": True,
                    "visible": True,
                    "checked": False
                }
            else:
                self.logger.debug("Shots nodes not found on current scene.")
                return {
                    "accepted": False,
                    "enabled": False,
                    "visible": True,
                    "checked": False
                }
        else:
            self.logger.debug("Splitter Plugin not accepted for current task.")
            return {
                "accepted": False,
                "enabled": False,
                "visible": False,
                "checked": False
            }

    def list_of_errors(self, seq_cut_items, node_names):

        errors = []
        for cut_item in seq_cut_items:
            if cut_item['code'] not in node_names:
                errors.append(
                    'Shot not found in the current scene: {}'.format(cut_item['code'])
                )

        if len(seq_cut_items) != len(node_names):
            errors.append(
                (
                    "\nThe number of shots found in the current scene doesn't match "
                    "the number of cut items defined in the latest cut."
                )
            )

        return errors

    def validate(self, settings, item):

        state = {
            "errors_found": 0,
            "errors": {},
            "callbacks": {}
        }

        # fields = ['code']
        # filters = [
        #     ['sg_sequence.Sequence.code', 'is', self.parent.engine.context.entity['name']],
        #     ['sg_status_list', 'is_not', 'omt']
        # ]
        # sequence_shots = self.parent.engine.shotgun.find("Shot", filters, fields)

        latest_seq_cut = self.get_latest_cut()

        if not latest_seq_cut:
            self.composer.add(
                name="No latest cut found",
                list_of_errors=["No latest cut found"],
                state=state,
                action={"enabled": False}
            )
            return False

        seq_cut_items = self.get_cut_items(latest_seq_cut)

        self.parent.engine.logger.info(
            "Sequence latest Cut:\n{}".format(pf(latest_seq_cut))
        )
        self.parent.engine.logger.info(
            "Sequence CutItems:\n{}".format(pf(seq_cut_items))
        )

        names_from_nodes = [
            shot.getShotName()
            for shot in item.properties.get("shot_list", [])
        ]

        self.composer.add(
            name="Errors extracting shots",
            list_of_errors=self.list_of_errors(seq_cut_items, names_from_nodes),
            state=state,
            action={"enabled": False}
        )

        check_hooks = self.parent.get_setting("check_hooks")
        checks = self.parent.create_hook_instance(check_hooks)
        checks.generate_actions(state)

        return True



    def split_file_template(self):
        # TODO: Merge into a single code, preferably the main splitter hook
        data = inspect.cleandoc(
"""
import sys
import maya.standalone

maya.standalone.initialize(name="python")

# Import Toolkit so we can access to Toolkit specific features.
sys.path.append("{config_path}")

import sgtk
import maya.cmds as cmds
import pymel

# ManagerInit --------------------------------------------------------------------------

# Initialize the logger so we get output to our terminal.
sgtk.LogManager().initialize_custom_handler()

# Set debugging to true so that we get more verbose output, (should only be used for
# testing).
sgtk.LogManager().global_debug = True

# Authentication -----------------------------------------------------------------------

# Instantiate the authenticator object.
authenticator = sgtk.authentication.ShotgunAuthenticator()

# Authenticate via sg desktop
user = authenticator.get_user()

# Tells Toolkit which user to use for connecting to Shotgun. Note that this should
# always take place before creating an `Sgtk` instance.
sgtk.set_authenticated_user(user)

# Bootstrap init -----------------------------------------------------------------------

# create an instance of the ToolkitManager which we will use to set a bunch of settings
# before initiating the bootstrap.
mgr = sgtk.bootstrap.ToolkitManager()
mgr.plugin_id = "basic.shell"
mgr.do_shotgun_config_lookup = False
mgr.base_configuration = {config_descriptor}
mgr.pre_engine_start_callback = lambda ctx: ctx.sgtk.synchronize_filesystem_structure()

# Maya data ----------------------------------------------------------------------------

source_file_path = "{source_maya_file}"
shot_name = "{shot_name}"
split_entity = {entity_hash}
split_params = {{
    "filter_shot": shot_name,
    "file_path": source_file_path,
    "batch_exec": True
}}
split_task = {task_hash}

engine = mgr.bootstrap_engine("tk-maya", entity=split_task)
splitter = engine.apps.get("mty-executeaction-sequence-shot-splitter")

engine.logger.info("Starting Splitter execution...".rjust(120, "*"))
result = splitter.execute_hook(
    "action_hook",
    entity_type="Sequence",
    entities=[split_entity],
    other_params=split_params
)

if result.get("errors"):
    raise Exception("Error while Splitting.")

engine.logger.info("Finished Executing Splitter hook.".rjust(120, "*"))

"""
        )

        return data


    def create_split_file(self, shot_node, settings):

        work_template_name = settings["Work Template"].value
        work_template = self.parent.engine.get_template_by_name(work_template_name)
        scene_path = os.path.abspath(cmds.file(query=True, sn=True))
        fields = work_template.get_fields(scene_path)
        publish_template_name = settings["Primary Publish Template"].value
        publish_template = self.parent.engine.get_template_by_name(publish_template_name)
        splited_path = publish_template.apply_fields(fields)

        #splited_path = 'P:/FZS/PublishArea/shots/e115/e115_pnt040/sequence/Layout/maya/e115_pnt040_lyt_master.v004.ma'

        variables = {
            'config_path': sgtk.get_sgtk_module_path().replace('\\', '\\\\'),
            'source_maya_file': splited_path.replace('\\', '\\\\'),
            'shot_name': shot_node.getShotName(),
            'entity_hash': self.parent.engine.context.entity,
            'task_hash': self.parent.engine.context.task,
            'config_descriptor': self.parent.engine.sgtk.configuration_descriptor.get_dict(),
        }

        # Creates the file in current workarea special path
        dateTimeObj = datetime.now()
        timestampStr = dateTimeObj.strftime("%d%M%Y%H%M%S%f")
        filename_params = {'timestamp': timestampStr, 'shotname': shot_node.getShotName()}
        py_filename = '{timestamp}_split_{shotname}.py'.format(**filename_params)
        scene_dir_path = os.path.dirname(os.path.abspath(cmds.file(query=True, sn=True)))
        split_python_file = os.path.join(scene_dir_path, 'splitter', py_filename).replace('\\', '/')
        self.parent.logger.info('split_python_file: {}'.format(split_python_file))

        self.parent.engine.ensure_folder_exists(os.path.dirname(split_python_file))

        data = self.split_file_template()

        # drops split data to file
        with open(split_python_file, "w") as f:
            f.write(data.format(**variables))

        return split_python_file, fields["version"]

    def publish(self, settings, item):

        mayapy_path = os.path.join(os.getenv("MAYA_LOCATION"), "bin", "mayapy.exe").replace('\\', '/')

        for shot_node in item.properties["shot_list"]:

            # Generate split file
            split_python_file, version_number = self.create_split_file(shot_node, settings)

            # Create execution command


            cmd_data = {"interpreter_path": mayapy_path, 'python_file': split_python_file}
            # TODO: Merge into a single code, preferably the main splitter hook
            commands = inspect.cleandoc(
"""
set_progress(1)
import subprocess
logger.info("Starting splitter process...")
proc = subprocess.Popen(
    [\'{interpreter_path}\', \'{python_file}\'],
    shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, encoding="utf-8"
)
set_progress(10)
stdout_value,  stderr_value= proc.communicate()
set_progress(95)
if proc.returncode:
    logger.info(
        "\\n\\n****************** Splitter Execution Error ******************"
    )
    raise RuntimeError(stderr_value)
else:
    logger.info(
        "\\n\\n****************** Splitter Execution Success ******************"
    )
    logger.info(stdout_value)
    set_progress(100)

"""
            )
            self.parent.logger.info("Executing splitter...")

            if not os.environ.get("SG_PROCESSING_SECONDARY_OUTPUTS", False):
                # prepare queue interface
                queue = self.parent.engine.apps.get("mty-multi-queue")
                if not queue:
                    msg = "Multi Queue app not found. Engine available apps are:\n{}".format(
                        pf(self.parent.engine.apps)
                    )
                    self.parent.engine.logger.error(msg)
                    raise RuntimeError(msg)

                client_mod = queue.import_module("tk_multi_queueclient")
                client = client_mod.queue_client.Client()

                shot_node_name = shot_node.getShotName()

                #commands = '5+5'
                # submit job
                job_data = {
                    'commands': commands.format(**cmd_data),
                    'priority': 50,
                    'name': "Split shot {0}, from v{1}".format(
                        shot_node_name, str(version_number).zfill(3)
                    )
                }
                self.parent.logger.info(
                    'job_data:{}'.format(job_data)
                )
                self.parent.logger.info(
                    'result: {}'.format(client.submit_job(job_data))
                )
            else:
                self.parent.logger.info("exporting shot: {} on farm".format(shot_node.getShotName()))
                self.create_deadline_split_file(shot_node, settings)

                self.parent.logger.info("Finished splitting shot: {}".format(shot_node.getShotName()))
    def create_deadline_split_file(self, shot_node, settings):

        framework_deadline = self.parent.engine.custom_frameworks.get('mty-framework-deadline') or self.load_framework('mty-framework-deadline')

        work_template_name = settings["Work Template"].value
        work_template = self.parent.engine.get_template_by_name(work_template_name)
        scene_path = os.path.abspath(cmds.file(query=True, sn=True))
        fields = work_template.get_fields(scene_path)
        publish_template_name = settings["Primary Publish Template"].value
        publish_template = self.parent.engine.get_template_by_name(publish_template_name)
        splited_path = publish_template.apply_fields(fields)

        shot_name = shot_node.getShotName()
        entity_hash = self.parent.engine.context.entity
        source_maya_file = splited_path.replace('\\', '\\\\'),

        entity_type="Sequence"
        entities=[entity_hash]
        maya_version = self.parent.engine.execute_hook_expression(
                "{config}/tk-multi-publish2/maya/collector_alembic.py",
                "get_maya_version",
            )

        split_params = {
    "filter_shot": shot_name,
    "file_path": source_maya_file,
    "batch_exec": True,
    "process_in_farm": True
        }

        deadline_utils = framework_deadline.deadlineSubmitJobUtils
        config_data = deadline_utils.get_current_config_info(self.parent.engine)

        plugin_data = {
                "Action": "ExecuteHook",
                "HookExpression": "{config}/mty-framework-deadline/boostrap_secundary_outputs_maya.py",
                "MethodName": "process_split_on_farm",
                "HookKwargs":{
                    "entity_type": entity_type,
                    "entities": entities,
                    "other_params": split_params,
                    "config_info": config_data,
                    "maya_version": maya_version,
                },
            }
        jobs_values = {
            "OverrideJobFailureDetection": True,
            "FailureDetectionJobErrors": 1,
        }

        engine_name = self.parent.engine.name
        farm_group = self.get_farm_group_for_engine(engine_name)
        if farm_group:
            jobs_values["Group"] = farm_group

        jobID = framework_deadline.deadlineSubmitJobUtils.submitting_toolkit_job(
            self.parent.engine, plugin_data, info_data=jobs_values
        )

        self.parent.logger.info("JobID: {}".format(jobID))
        self.parent.logger.info("Finished splitting shot: {}".format(shot_name))

    def get_farm_group_for_engine(self, engine_name):
        """
        Get the custom group for the engine
        from overide value, if not exist return None
        """

        result = None
        valueoverrides = self.parent.engine.custom_frameworks.get(
        "mty-framework-valueoverrides"
        ) or self.load_framework("mty-framework-valueoverrides")

        # Get excluded tasks from value override in case it exists
        if valueoverrides:
            default_value_code = "mty.multi.deadline.select_group_for_engine"

            procress_grps = valueoverrides.get_value(
                default_value_code)

            self.parent.engine.logger.info("Overide Farm Group: {}".format(procress_grps))

            if procress_grps:
                dict_process_grps = json.loads(procress_grps)
                engine_grp = dict_process_grps.get(engine_name)
                if engine_grp:
                    result = engine_grp

        return result

    def finalize(self, settings, item):
        pass

    # ================================================================

    def get_latest_cut(self):
        shotgun = self.parent.engine.shotgun

        filters = [["entity", "is", self.parent.engine.context.entity]]
        fields = ["code", "revision_number", "id"]
        order = [{"field_name": "revision_number", "direction": "desc"}]

        latest_cut = shotgun.find_one("Cut", filters, fields, order=order)

        return latest_cut

    def get_cut_items(self, cut):
        shotgun = self.parent.engine.shotgun

        filters = [["cut", "is", cut]]
        fields = [
            "code",
            "cut",
            "cut_item_in",
            "cut_item_out",
            "edit_in",
            "edit_out",
            "cut_item_duration",
        ]

        list_of_cut_items = shotgun.find("CutItem", filters, fields)

        return list_of_cut_items

    def list_of_assets_link_errors(self, shot, list_of_assets):
        result = []
        list_of_shot_assets = shot.assets.get()

        if list_of_shot_assets == "" \
                or list_of_shot_assets is None:

            message = "{0} ".format(shot.getShotName()) + \
                "has no assets linked."

            result.append(message)

        else:
            list_of_shot_assets = list_of_shot_assets.split(";")

        for asset in list_of_shot_assets:
            if asset not in list_of_assets:

                message = \
                    "\"{0}\" asset linking error: \n\n".format(
                        shot.getShotName()
                    ) + \
                    "This could happen either because \n" + \
                    "the linked asset was renamed, \n" + \
                    "no longer exists, or it was replaced \n" + \
                    "by a new reference but the shot assets \n" + \
                    "attribute was not updated in the \n" + \
                    "shot linking process."

                result.append(message)

        return result

    # . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .

    def list_of_assets_in_session(self, gpuCache=True):
        result = []
        self.list_of_references = pm.listReferences()
        self.list_of_gpu_caches = pm.ls(type="gpuCache")

        for reference in self.list_of_references:
            result.append(reference.refNode)

        if gpuCache:
            for gpu_cache in self.list_of_gpu_caches:
                result.append(gpu_cache.getParent())

        return result
