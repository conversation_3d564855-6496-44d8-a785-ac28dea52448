########################################################################################
#
# Copyright (c) 2024 Mighty Animation Studio
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject your
# studio or personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio
#
########################################################################################

import os
import json
import pprint
import traceback

import sgtk
import bpy

from tank import Hook
from tank import TankError
from tank.platform.qt import QtCore, QtGui

class ProcessItemsHook(Hook):
    """
    Process items to load them into the scene
    """

    def load_item(self, item, update_progress):
        """
        The load item method is executed once for each defined item and its purpose is
        to process the loading of the item into the scene.

        The structure of each item is a dictionary with three keys:

        - "node": The name of the 'node' that is to be operated on. Most DCCs have
            a concept of a node, path, or some other way to address a particular
            object in the scene.
        - "type": The object type that this is. This is to
            know how to handle the object.
        - "path": Path on disk to the object to be loaded.
        """

        if item["type"] == "Shot Camera Alembic Cache":
            update_progress(
                {"progress": 30, "message": "Linking Shot Camera Alembic Cache..."}
            )

            camera_alembic_cache_path = item["path"]
            camera_alembic_cache_path = self.fix_path(camera_alembic_cache_path)
            namespace = item.get("other_params", {}).get("namespace", "")

            self.parent.logger.info(
                "Loading shot camera alembic cache: {}".format(
                    camera_alembic_cache_path
                )
            )

            try:
                # Ensure Alembic import addon is enabled
                if not "io_scene_alembic" in bpy.context.preferences.addons:
                    bpy.ops.preferences.addon_enable(module="io_scene_alembic")

                # Link alembic instead of importing
                with bpy.data.libraries.load(camera_alembic_cache_path, link=True) as (data_from, data_to):
                    data_to.objects = data_from.objects

                # Add the linked objects to the scene and create library overrides
                for obj in data_to.objects:
                    if obj is not None:
                        # Add to scene
                        bpy.context.scene.collection.objects.link(obj)

                        # Create library override if needed
                        if not obj.override_library:
                            bpy.ops.object.make_override_library(object=obj.name)

                        # Handle namespace
                        if namespace:
                            obj.name = f"{namespace}_{obj.name}"

            except Exception as e:
                msg = (
                    "Error linking shot camera alembic cache: {}\n{}".format(
                        camera_alembic_cache_path, e
                    )
                )
                self.parent.logger.error(msg)
                self.parent.logger.error(traceback.format_exc())
                self.show_message(msg, "ERROR")
                return

            self.parent.logger.info(
                "Successfully linked shot camera alembic cache: {}".format(
                    camera_alembic_cache_path
                )
            )

    def fix_path(self, path):
        """Replace all backward slashes with forward slashes."""
        path = path.replace("\\\\", "/")
        path = path.replace("\\", "/")
        return path

    def show_message(self, msg, severity="INFO"):
        """Shows a message box with the input message"""
        def draw(self, context):
            self.layout.label(text=msg)

        if severity == "ERROR":
            icon = 'ERROR'
        elif severity == "WARNING":
            icon = 'ERROR'
        else:
            icon = 'INFO'