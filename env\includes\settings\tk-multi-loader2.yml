# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

################################################################################

includes:
- ../app_locations.yml

################################################################################

# 3dsmax
settings.tk-multi-loader2.3dsmax:
  actions_hook: "{engine}/tk-multi-loader2/basic/scene_actions.py"
  action_mappings:
    3dsmax Scene: [import, reference]
    Alembic Cache: [import]
    Image: [texture_node]
    Rendered Image: [texture_node]
    Texture: [texture_node]
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
  - caption: Environments
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_main_parent, sg_environment_type, code]
  - caption: Shots
    entity_type: Shot
    filters:
    - [project, is, '{context.project}']
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
  - caption: My Tasks
    entity_type: Task
    filters:
    - [task_assignees, is, '{context.user}']
    - [project, is, '{context.project}']
    hierarchy: [entity, content]
  location: "@apps.tk-multi-loader2.location"

# flame
settings.tk-multi-loader2.flame:
  actions_hook: '{engine}/tk-multi-loader2/flame_loader_actions.py'
  action_mappings:
    Flame Batch File: [load_setup]
    Flame Quicktime: [load_clip]
    Flame Render: [load_clip]
    Flame OpenClip: [load_clip]
    Photoshop Image: [load_clip]
    Photoshop Proxy Image: [load_clip]
    Rendered Image: [load_clip]
    Image: [load_clip]
    Movie: [load_clip]
    Texture: [load_clip]
  entity_mappings:
    Shot: [load_batch]
    CutItem: [load_batch]
  location: "@apps.tk-multi-loader2.location"

# houdini
settings.tk-multi-loader2.houdini:
  action_mappings:
    Alembic Cache: [import]
    Houdini Scene: [merge]
    Image: [file_cop]
    Photoshop Image: [file_cop]
    Photoshop Proxy Image: [file_cop]
    Rendered Image: [file_cop]
    Texture: [file_cop]
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
  - caption: Environments
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_main_parent, sg_environment_type, code]
  - caption: Shots
    entity_type: Shot
    filters:
    - [project, is, '{context.project}']
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
  - caption: My Tasks
    entity_type: Task
    filters:
    - [task_assignees, is, '{context.user}']
    - [project, is, '{context.project}']
    hierarchy: [entity, content]
  publish_filters: [["sg_status_list", "is_not", null]]
  location: "@apps.tk-multi-loader2.location"

# mari
settings.tk-multi-loader2.mari:
  action_mappings:
    Alembic Cache: [geometry_import]
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
  - caption: Environments
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_main_parent, sg_environment_type, code]
  - caption: My Tasks
    entity_type: Task
    filters:
    - [task_assignees, is, '{context.user}']
    - [project, is, '{context.project}']
    hierarchy: [entity, content]
    publish_filters: []
  title_name: Import
  location: '@apps.tk-multi-loader2.location'

# maya
settings.tk-multi-loader2.maya:
  actions_hook: '{self}/tk-maya_actions.py:{config}/tk-multi-loader2/tk-maya_actions.py'
  action_mappings:
    Alembic Cache: [reference, import, reference_without_namespace, download_publish]
    Shot Camera Alembic: [reference, import, download_publish]
    Editorial Audio: [audio, download_publish]
    GPU Cache: [gpu_cache, download_publish]
    Image: [texture_node, image_plane, download_publish]
    Maya Scene: [reference, import, reference_without_namespace, reference_with_custom_namespace, download_publish]
    Photoshop Image: [texture_node, image_plane, download_publish]
    Photoshop Proxy Image: [texture_node, image_plane, download_publish]
    Rendered Image: [texture_node, image_plane, download_publish]
    Texture: [texture_node, image_plane, download_publish]
    UDIM Texture: [udim_texture_node, image_plane, download_publish]
    CustomShape Render Piece Library Frame: [library_sequence_texture_node]
    CustomShape Proxy Piece Library Frame: [library_sequence_texture_node]
    Shot Environment Overrides: [environment_overrides]
    Photoshop Layer Group Image: [texture_node, download_publish]
    Photoshop Layer Group Proxy Image: [texture_node, download_publish]

  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
  - caption: Environments
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_main_parent, sg_environment_type, code]
  # - caption: CustomShapes
  #   entity_type: Blendshape
  #   filters:
  #   - [project, is, "{context.project}"]
  #   hierarchy: [assets, sg_piece, code]
  - caption: Sequences
    entity_type: Sequence
    filters:
    - [project, is, "{context.project}"]
    hierarchy: [episode, code]
  - caption: Shots
    entity_type: Shot
    filters:
    - [project, is, '{context.project}']
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
  - caption: My Tasks
    entity_type: Task
    filters:
    - [task_assignees, is, "{context.user}"]
    - [project, is, "{context.project}"]
    hierarchy: [entity, content]
    publish_filters: [
    ["sg_status_list", "is_not", null],
    ["sg_status_list", "is", "apr"]
  ]

  filter_publishes_hook: '{config}/tk-multi-loader2/filter_publishes.py'
  #publish_filters: [["sg_status_list", "is_not", null], ["sg_status_list", "is", "apr"]] # In order to display only the approved
  location: "@apps.tk-multi-loader2.location"

# nuke
settings.tk-multi-loader2.nuke:
  action_mappings:
    Alembic Cache: [read_node]
    Flame Render: [read_node]
    Flame Quicktime: [read_node]
    Image: [read_node]
    Movie: [read_node]
    Nuke Script: [script_import]
    Photoshop Image: [read_node]
    Photoshop Proxy Image: [read_node]
    Rendered Image: [read_node]
    Texture: [read_node]
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
  - caption: Environments
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_main_parent, sg_environment_type, code]
  - caption: Shots
    entity_type: Shot
    filters:
    - [project, is, '{context.project}']
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
  - caption: My Tasks
    entity_type: Task
    filters:
    - [task_assignees, is, '{context.user}']
    - [project, is, '{context.project}']
    hierarchy: [entity, content]
  publish_filters: [
    ["sg_status_list", "is_not", null],
    ["sg_status_list", "is", "apr"]
  ]

  location: "@apps.tk-multi-loader2.location"

# nuke studio (actions hook)
settings.tk-multi-loader2.nukestudio.project:
  actions_hook: '{self}/tk-nuke_actions.py'
  action_mappings:
    Image: [clip_import]
    Movie: [clip_import]
    Rendered Image: [clip_import]
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
  - caption: Environments
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_main_parent, sg_environment_type, code]
  - caption: Shots
    entity_type: Shot
    filters:
    - [project, is, '{context.project}']
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
  - caption: My Tasks
    entity_type: Task
    filters:
    - [task_assignees, is, '{context.user}']
    - [project, is, '{context.project}']
    hierarchy: [entity, content]
  publish_filters: [
    ["sg_status_list", "is_not", null],
    ["sg_status_list", "is", "apr"]
  ]

  location: "@apps.tk-multi-loader2.location"

settings.tk-multi-loader2.nukestudio:
  actions_hook: '{self}/tk-nuke_actions.py'
  action_mappings:
    Alembic Cache: [read_node]
    Image: [read_node]
    Movie: [read_node]
    Nuke Script: [script_import]
    NukeStudio Project: [open_project]
    Photoshop Image: [read_node]
    Photoshop Proxy Image: [read_node]
    Rendered Image: [read_node]
    Texture: [read_node]
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
  - caption: Environments
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_main_parent, sg_environment_type, code]
  - caption: Shots
    entity_type: Shot
    filters:
    - [project, is, '{context.project}']
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
  - caption: My Tasks
    entity_type: Task
    filters:
    - [task_assignees, is, '{context.user}']
    - [project, is, '{context.project}']
    hierarchy: [entity, content]
  publish_filters: [
    ["sg_status_list", "is_not", null],
    ["sg_status_list", "is", "apr"]
  ]

  location: "@apps.tk-multi-loader2.location"

# photoshop
settings.tk-multi-loader2.photoshop:
  actions_hook: "{self}/tk-photoshopcc_actions.py:{config}/tk-multi-loader2/tk-multi-loader2_multi_transfer_actions.py:{config}/tk-multi-loader2/tk-photoshopcc_actions.py"
  action_mappings:
    Photoshop Image: [add_as_a_layer, open_file, download_publish]
    Photoshop Proxy Image: [add_as_a_layer, open_file, download_publish]
    Rendered Image: [add_as_a_layer, open_file, download_publish]
    Image: [add_as_a_layer, open_file, download_publish]
    Texture: [add_as_a_layer, open_file, download_publish]
    Photoshop Layer Group Image: [add_as_a_layer, open_file, download_publish]
    Photoshop Layer Group Proxy Image: [add_as_a_layer, open_file, download_publish]
    OBJ Mesh: [download_publish, add_as_a_layer]
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
  - caption: Environments
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_main_parent, sg_environment_type, code]
  - caption: Shots
    entity_type: Shot
    filters:
    - [project, is, '{context.project}']
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
  - caption: My Tasks
    entity_type: Task
    filters:
    - [task_assignees, is, '{context.user}']
    - [project, is, '{context.project}']
    hierarchy: [entity, content]
  publish_filters: [
    ["sg_status_list", "is_not", null],
    ["sg_status_list", "is", "apr"]
  ]

  location: "@apps.tk-multi-loader2.location"

# after effects
settings.tk-multi-loader2.aftereffects:
  action_mappings:
    # Removed default action 'add_to_comp' from all of the mappings since
    # it was crashing the loader interface: once a published file was selected,
    # it dissapeared from the loader interface.
    After Effects Project: [add_to_project, download_publish]
    # Maya Scene: [add_to_project, download_publish]
    Photoshop Image: [add_to_project, download_publish]
    Photoshop Proxy Image: [add_to_project, download_publish]
    Photoshop Layer Group Image: [add_to_project, download_publish]
    Photoshop Layer Group Proxy Image: [add_to_project, download_publish]
    Rendered Image: [add_to_project, download_publish]
    Harmony Shot Camera: [import_Harmony_camera, download_publish]
    Harmony Shot Tracker Peg: [import_Harmony_tracker_peg, download_publish]
    Media Review: [add_to_project, download_publish]
    Editorial Audio: [add_to_project, download_publish]
    Sequence Review: [add_to_project, download_publish]
    Illustrator Image: [add_to_project, download_publish]
    Layer Group Order: [Import_psd_output_layers_ordered, download_publish]
    # Image: [add_to_project, download_publish]
    # Texture: [add_to_project, download_publish]
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - ["sg_status_list", "is_not", "omt"]
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
  - caption: Environments
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - ["sg_status_list", "is_not", "omt"]
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_main_parent, sg_environment_type, code]
  - caption: Shots
    entity_type: Shot
    filters:
    - [project, is, '{context.project}']
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    - ["sg_status_list", "is_not", "na"]
    - ["sg_status_list", "is_not", "omt"]
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
  - caption: My Tasks
    entity_type: Task
    filters:
    - [task_assignees, is, '{context.user}']
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - ["sg_status_list", "is_not", "omt"]
    hierarchy: [entity, content]
  actions_hook: "{config}/tk-multi-loader2/tk-multi-loader2_multi_transfer_actions.py:{config}/tk-multi-loader2/tk-aftereffects_actions.py"
  publish_filters: [
    ["sg_status_list", "is_not", null],
    ["sg_status_list", "is", "apr"]
  ]
  location: "@apps.tk-multi-loader2.location"

# adobe premiere
settings.tk-multi-loader2.premiere:
  action_mappings:
    Premiere Project: [add_to_project, add_to_comp]
    After Effects Project: [add_to_project, add_to_comp]
    Maya Scene: [add_to_project, add_to_comp]
    Photoshop Image: [add_to_project, add_to_comp]
    Photoshop Proxy Image: [add_to_project, add_to_comp]
    Rendered Image: [add_to_project, add_to_comp]
    Image: [add_to_project, add_to_comp]
    Texture: [add_to_project, add_to_comp]
    Media Review: [add_to_project, add_to_comp]
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
  - caption: Environments
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_main_parent, sg_environment_type, code]
  - caption: Shots
    entity_type: Shot
    filters:
    - [project, is, '{context.project}']
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
  - caption: My Tasks
    entity_type: Task
    filters:
    - [task_assignees, is, '{context.user}']
    - [project, is, '{context.project}']
    hierarchy: [entity, content]
  actions_hook: "{engine}/tk-multi-loader2/basic/scene_actions.py"
  publish_filters: [
    ["sg_status_list", "is_not", null],
    ["sg_status_list", "is", "apr"]
  ]

  location: "@apps.tk-multi-loader2.location"

# motion builder
settings.tk-multi-loader2.motionbuilder:
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
  - caption: Environments
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_main_parent, sg_environment_type, code]
  - caption: Shots
    entity_type: Shot
    filters:
    - [project, is, '{context.project}']
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
  - caption: My Tasks
    entity_type: Task
    filters:
    - [task_assignees, is, '{context.user}']
    - [project, is, '{context.project}']
    hierarchy: [entity, content]
  location: "@apps.tk-multi-loader2.location"

# alias
settings.tk-multi-loader2.alias:
  action_mappings:
    Alias File: [import]
    Wref File: [reference]
    Igs File: [import]
    Stp File: [import]
    Stl File: [import]
    Jt File: [import]
    Catpart File: [import]
    Fbx File: [import]
    Image: [texture_node]
    Photoshop Image: [texture_node]
    Photoshop Proxy Image: [texture_node]
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
  - caption: Environments
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_main_parent, sg_environment_type, code]
  - caption: My Tasks
    entity_type: Task
    filters:
    - [task_assignees, is, '{context.user}']
    - [project, is, '{context.project}']
    hierarchy: [entity, content]
  actions_hook: "{engine}/tk-multi-loader2/basic/scene_actions.py"
  publish_filters: [["sg_status_list", "is_not", null]]
  location: "@apps.tk-multi-loader2.location"

# VRED
settings.tk-multi-loader2.vred:
  action_mappings:
    Alias File: [import]
    Igs File: [import]
    Stp File: [import]
    Stl File: [import]
    Jt File: [import]
    Catpart File: [import]
    Fbx File: [import]
    VRED Scene: [load]
    Osb File: [import]
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
  - caption: Environments
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_main_parent, sg_environment_type, code]
  - caption: My Tasks
    entity_type: Task
    filters:
    - [task_assignees, is, '{context.user}']
    - [project, is, '{context.project}']
    hierarchy: [entity, content]
  actions_hook: "{engine}/tk-multi-loader2/basic/scene_actions.py"
  publish_filters: [["sg_status_list", "is_not", null]]
  location: "@apps.tk-multi-loader2.location"

# harmony
settings.tk-multi-loader2.harmony:
  # actions_hook: '{config}/tk-multi-loader2/tk-harmony_actions.py'
  actions_hook: "{config}/tk-multi-loader2/tk-multi-loader2_multi_transfer_actions.py:{config}/tk-multi-loader2/tk-harmony_actions.py"
  action_mappings:
    # Alembic Cache: [download_publish, 3d, copy_path]
    # FBX File: [download_publish, 3d, copy_path]
    # OBJ File: [download_publish, 3d, copy_path]
    # 3DS File: [download_publish, 3d, copy_path]
    # OBS File: [download_publish, 3d, copy_path]
    # Movie File: [download_publish, movie, copy_path]
    Media Review:
      - download_publish
      - movie
      - copy_path
    # Image: [download_publish, drawing, copy_path]
    # Texture: [download_publish, drawing, copy_path]
    Rendered Image:
      - download_publish
      - image_sequence
      - copy_path
    Image Sequence Review:
      - download_publish
      - image_sequence
      - copy_path
    Photoshop Image:
      - download_publish
    #  - drawing
      - image_as_project_resolution
      - image_as_vector
      - copy_path
    Photoshop Proxy Image:
      - download_publish
    #  - drawing
      - image_as_project_resolution
      - image_as_vector
      - copy_path
    Photoshop Layer Group Image:
      - download_publish
    #  - drawing
      - image_as_project_resolution
      - image_as_vector
      - copy_path
    Photoshop Layer Group Proxy Image:
      - download_publish
    #  - drawing
      - image_as_project_resolution
      - image_as_vector
      - copy_path
    # WAV File: [download_publish, sound, copy_path]
    Editorial Audio: [download_publish, sound, copy_path]
    # Sequence Review: [download_publish,drawing_sequence]
    # Image Sequence Review: [download_publish,drawing_sequence,copy_path]
    # Sound File: [download_publish, sound, copy_path]
    # Audio File: [download_publish, sound, copy_path]
    Harmony TPL: [download_publish, tpl, copy_path]
    Harmony Shot Camera: [download_publish, camera, copy_path]
    FBX Mesh: [download_publish, 3d_mesh, copy_path]
    # OBJ Mesh: [download_publish, 3d_mesh, copy_path]
    Harmony Color Palette: [download_publish, color_palette, copy_path]
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - ["sg_status_list", "is_not", "omt"]
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
  - caption: Environments
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - ["sg_status_list", "is_not", "omt"]
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_main_parent, sg_environment_type, code]
  - caption: Shots
    entity_type: Shot
    filters:
    - [project, is, '{context.project}']
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    - ["sg_status_list", "is_not", "na"]
    - ["sg_status_list", "is_not", "omt"]
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
  - caption: My Tasks
    entity_type: Task
    filters:
    - [task_assignees, is, "{context.user}"]
    - [project, is, "{context.project}"]
    - ["sg_status_list", "is_not", "na"]
    - ["sg_status_list", "is_not", "omt"]
    hierarchy: [entity, content]
  publish_filters: [
    ["sg_status_list", "is_not", null],
    ["sg_status_list", "is", "apr"]
  ] # In order to display only the approved
  # Hook is not been used, using publish_filters setting instead
  # filter_publishes_hook: '{config}/tk-multi-loader2/filter_publishes.py'
  location: "@apps.tk-multi-loader2.location"

# blender
settings.tk-multi-loader2.blender:
  # actions_hook: '{engine}/tk-multi-loader2/tk-blender_actions.py:{config}/tk-multi-loader2/tk-blender_actions.py'
  actions_hook: '{engine}/tk-multi-loader2/tk-blender_actions.py:{config}/tk-multi-loader2/tk-multi-loader2_multi_transfer_actions.py:{config}/tk-multi-loader2/tk-blender_actions.py'
  action_mappings:
    Alembic Cache: [download_publish,copy_path, import]
    Shot Camera Alembic: [download_publish,copy_path, import, add_camera]
    Abc File: [download_publish,copy_path, import]
    Dae File: [download_publish,copy_path, import]
    Collada File: [download_publish,copy_path, import]
    Bvh File: [download_publish,copy_path, import]
    Motion Capture File: [download_publish,copy_path, import]
    Svg File: [download_publish,copy_path, import]
    Scalable Vector Graphics File: [download_publish,copy_path, import]
    Ply File: [download_publish,copy_path, import]
    Stanford File: [download_publish,copy_path, import]
    Stl File: [download_publish,copy_path, import]
    Glb File: [download_publish,copy_path, import]
    Gltf File: [download_publish,copy_path, import]
    Obj File: [download_publish,copy_path, import]
    Wavefront File: [download_publish,copy_path, import]
    X3d File: [download_publish,copy_path, import]
    Wrl File: [download_publish,copy_path, import]
    X3d Extensible 3D File: [download_publish,copy_path, import]
    Blender Project File: [download_publish,copy_path, link, append]
    Wav File: [download_publish,copy_path, asSequencerSound, add_audio]
    Sound File: [download_publish,copy_path, asSequencerSound, add_audio]
    Editorial Audio: [download_publish,copy_path, asSequencerSound, add_audio]
    Movie File: [download_publish,copy_path, asSequencerMovie, asCompositorNodeMovieClip]
    Media Review: [download_publish,copy_path, asSequencerMovie, asCompositorNodeMovieClip]
    Image File: [download_publish,copy_path, asSequencerImage, asCompositorNodeImage, add_texture, add_background_image]
    Photoshop Image: [download_publish,copy_path, asSequencerImage, asCompositorNodeImage, add_texture, add_background_image]
    Photoshop Proxy Image: [download_publish,copy_path, asSequencerImage, asCompositorNodeImage, add_texture, add_background_image]
    Rendered Image: [download_publish,copy_path, asSequencerImage, asCompositorNodeImage, add_texture, add_background_image]
    Texture File: [download_publish,copy_path, asSequencerImage, asCompositorNodeImage, add_texture, add_background_image]
    Sound: [download_publish,copy_path, asSequencerSound, add_audio]
    Movie: [download_publish,copy_path, asSequencerMovie, asCompositorNodeMovieClip]
    Image: [download_publish,copy_path, asSequencerImage, asCompositorNodeImage]
    Texture: [download_publish,copy_path, asSequencerImage, asCompositorNodeImage, add_texture, add_background_image]
    Fbx File: [download_publish,copy_path, import]
    Usd File: [download_publish,copy_path, import]
    3ds File: [download_publish,copy_path, import]
    Blend File: [download_publish,copy_path, import, link, append]
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - ["sg_status_list", "is_not", "omt"]
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
  - caption: Environments
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - ["sg_status_list", "is_not", "omt"]
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_main_parent, sg_environment_type, code]
  - caption: Shots
    entity_type: Shot
    filters:
    - [project, is, '{context.project}']
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    - ["sg_status_list", "is_not", "na"]
    - ["sg_status_list", "is_not", "omt"]
    hierarchy: [sg_sequence, code]
  - caption: My Tasks
    entity_type: Task
    filters:
    - [task_assignees, is, "{context.user}"]
    - [project, is, "{context.project}"]
    - ["sg_status_list", "is_not", "na"]
    - ["sg_status_list", "is_not", "omt"]
    hierarchy: [entity, content]
  publish_filters: [
    ["sg_status_list", "is_not", null],
    ["sg_status_list", "is", "apr"]
  ]
  location: "@apps.tk-multi-loader2.location"

# fusion
settings.tk-multi-loader2.fusion:
  #actions_hook: '{engine}/tk-multi-loader2/tk-fusion_actions.py'
  # actions_hook: '{engine}/tk-multi-loader2/tk-fusion_actions.py'
  actions_hook: "{config}/tk-multi-loader2/tk-multi-loader2_multi_transfer_actions.py:{engine}/tk-multi-loader2/tk-fusion_actions.py"
  action_mappings:
    Alembic Cache: [ensure_local, copy_path]
    Editorial Audio: [ensure_local, copy_path]
    Flame Render: [loader_node, ensure_local, copy_path]
    Flame Quicktime: [loader_node, ensure_local, copy_path]
    Image: [loader_node, ensure_local, copy_path]
    Maya Shot anm Alembic: [ensure_local, copy_path]
    Maya Shot lyt Alembic: [ensure_local, copy_path]
    Maya Shot Geo Alembic: [ensure_local, copy_path]
    Media Review: [loader_node, ensure_local, copy_path]
    Movie: [loader_node, ensure_local, copy_path]
    Photoshop Image: [loader_node, ensure_local, copy_path]
    Photoshop Proxy Image: [loader_node, ensure_local, copy_path]
    Rendered Image: [loader_node, ensure_local, copy_path]
    Shot Camera Alembic: [ensure_local, copy_path]
    Texture: [loader_node, ensure_local, copy_path]
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
  - caption: Environments
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_main_parent, sg_environment_type, code]
  - caption: Shots
    entity_type: Shot
    filters:
    - [project, is, '{context.project}']
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
  - caption: My Tasks
    entity_type: Task
    filters:
    - [task_assignees, is, '{context.user}']
    - [project, is, '{context.project}']
    hierarchy: [entity, content]
  publish_filters: [
    ["sg_status_list", "is_not", null],
    ["sg_status_list", "is", "apr"]
  ]

  location: "@apps.tk-multi-loader2.location"

  # krita
settings.tk-multi-loader2.krita:
  actions_hook: '{engine}/tk-multi-loader2/tk-krita_actions.py'
  action_mappings:
    Photoshop Image: [open_image, open_as_layer]
    Photoshop Proxy Image: [open_image, open_as_layer]
    Rendered Image: [open_image, open_as_layer]
    Image: [open_image, open_as_layer]
    Image Sequence: [import_animation_frames]
    Texture: [open_image, open_as_layer]
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
  - caption: Environments
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_main_parent, sg_environment_type, code]
  - caption: Shots
    entity_type: Shot
    filters:
    - [project, is, '{context.project}']
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
  - caption: My Tasks
    entity_type: Task
    filters:
    - [task_assignees, is, "{context.user}"]
    - [project, is, "{context.project}"]
    hierarchy: [entity, content]
  publish_filters: [
    ["sg_status_list", "is_not", null],
    ["sg_status_list", "is", "apr"]
  ]

  location: "@apps.tk-multi-loader2.location"

# substance painter
settings.tk-multi-loader2.substancepainter:
  actions_hook: '{engine}/tk-multi-loader2/tk-substancepainter_actions.py'
  action_mappings:
    Image: [environment, colorlut, alpha, texture]
    Texture: [environment, colorlut, alpha, texture]
    Rendered Image: [environment, colorlut, alpha, texture]
    Substance Material Preset: [preset]
    Sppr File: [preset]
    PopcornFX : [script]
    Pkfx File : [script]
    Shader: [shader]
    Glsl File: [shader]
    Substance Export Preset: [export]
    Spexp File: [export]
    Substance Smart Material: [smartmaterial]
    Spsm File: [smartmaterial]
    Substance File: [basematerial, alpha, texture, filter, procedural, generator]
    Sbsar File: [basematerial, alpha, texture, filter, procedural, generator]
    Substance Smart Mask: [smartmask]
    Spmsk File: [smartmask]
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
  - caption: Environments
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is, Environment]
    hierarchy: [sg_main_parent, sg_environment_type, code]
  - caption: My Tasks
    entity_type: Task
    filters:
    - [task_assignees, is, "{context.user}"]
    - [project, is, "{context.project}"]
    hierarchy: [entity, content]
  publish_filters: [
    ["sg_status_list", "is_not", null],
    ["sg_status_list", "is", "apr"]
  ]

  location: "@apps.tk-multi-loader2.location"

# standalone definition for Desktop
settings.tk-multi-loader2.standalone:
  actions_hook: "{config}/tk-multi-loader2/desktop_loader_actions.py"
  action_mappings:

    # After Effects
    After Effects Project: [download_publish]
    Premiere Project: [download_publish]
    Illustrator Image: [download_publish]

    # Harmony
    Harmony Project: [download_publish]
    Harmony Shot Camera: [download_publish]
    Harmony Shot Tracker Peg: [download_publish]
    Harmony TPL: [download_publish]
    Harmony Color Palette: [download_publish]

    # Photoshop
    Photoshop Image: [download_publish]
    Photoshop Proxy Image: [download_publish]
    Photoshop Layer Group Image: [download_publish]
    Photoshop Layer Group Proxy Image: [download_publish]

    # Maya
    Maya Scene: [download_publish]
    Alembic Cache: [download_publish]
    Shot Camera Alembic: [download_publish]
    Maya Shot Geo Alembic: [download_publish]
    GPU Cache: [download_publish]
    FBX Mesh: [download_publish]
    OBJ Mesh: [download_publish]

    # Common Media
    UDIM Image: [download_publish]
    Media Review: [download_publish]
    Sequence Review: [download_publish]
    Rendered Image: [download_publish]
    Image: [download_publish]
    Movie: [download_publish]
    Editorial Audio: [download_publish]
    Image Sequence: [download_publish]
    Texture: [download_publish]
  publish_filters: [
    ["sg_status_list", "is_not", null],
    ["sg_status_list", "is", "apr"]
  ]
  entities:
  - caption: Assets
    entity_type: Asset
    filters:
    - [project, is, '{context.project}']
    - ["sg_status_list", "is_not", "na"]
    - [sg_asset_type, is_not, Environment]
    hierarchy: [sg_asset_type, code]
  - caption: Shots
    entity_type: Shot
    filters:
    - [project, is, '{context.project}']
    - [sg_sequence.Sequence.episode.Episode.sg_status_list, 'is_not', 'fin']
    hierarchy: [sg_sequence.Sequence.episode, sg_sequence, code]
  - caption: My Tasks
    entity_type: Task
    filters:
    - [task_assignees, is, '{context.user}']
    - [project, is, '{context.project}']
    hierarchy: [entity, content]
  location: "@apps.tk-multi-loader2.location"
