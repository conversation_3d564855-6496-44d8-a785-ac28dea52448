# Copyright (c) 2017 Shotgun Software Inc.
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided "AS IS" and subject to the Shotgun Pipeline Toolkit
# Source Code License included in this distribution package. See LICENSE.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the Shotgun Pipeline Toolkit Source Code License. All rights
# not expressly granted therein are reserved by Shotgun Software Inc.

################################################################################

includes:
- ../app_locations.yml

################################################################################

# nuke studio
settings.tk-multi-breakdown.nukestudio:
  hook_get_version_number: '{config}/tk-multi-breakdown/get_publish_version_number.py'
  hook_scene_operations: '{self}/tk-nuke_scene_operations.py:{config}/tk-multi-breakdown/tk-multi_scene_transfers.py'
  location: '@apps.tk-multi-breakdown.location'
# alias
settings.tk-multi-breakdown.alias:
  hook_get_version_number: '{config}/tk-multi-breakdown/get_publish_version_number.py'
  hook_scene_operations: '{engine}/tk-multi-breakdown/basic/scene_operations.py:{config}/tk-multi-breakdown/tk-multi_scene_transfers.py'
  location: '@apps.tk-multi-breakdown.location'
# VRED
settings.tk-multi-breakdown.vred:
  hook_get_version_number: '{config}/tk-multi-breakdown/get_publish_version_number.py'
  hook_scene_operations: '{engine}/tk-multi-breakdown/basic/scene_operations.py:{config}/tk-multi-breakdown/tk-multi_scene_transfers.py'
  location: '@apps.tk-multi-breakdown.location'
# Maya
settings.tk-multi-breakdown.maya:
  hook_get_version_number: '{config}/tk-multi-breakdown/get_publish_version_number.py'
  hook_scene_operations: '{self}/tk-maya_scene_operations.py:{config}/tk-multi-breakdown/maya/scene_operations_extended.py:{config}/tk-multi-breakdown/tk-multi_scene_transfers.py'
  location: '@apps.tk-multi-breakdown.location'
# Harmony
settings.tk-multi-breakdown.harmony:
  hook_get_version_number: '{config}/tk-multi-breakdown/get_publish_version_number.py'
  hook_scene_operations: '{engine}/tk-multi-breakdown/tk-harmony_scene_operations.py:{config}/tk-multi-breakdown/tk-multi_scene_transfers.py'
  location: '@apps.tk-multi-breakdown.location'
# ---- Blender
settings.tk-multi-breakdown.blender:
  hook_scene_operations: '{engine}/tk-multi-breakdown/tk-blender_scene_operations.py'
  location: "@apps.tk-multi-breakdown.location"
# Fusion
settings.tk-multi-breakdown.fusion:
  hook_get_version_number: '{config}/tk-multi-breakdown/get_publish_version_number.py'
  hook_scene_operations: '{engine}/tk-multi-breakdown/tk-fusion_scene_operations.py:{config}/tk-multi-breakdown/tk-multi_scene_transfers.py'
  #hook_scene_operations: '{engine}/tk-multi-breakdown/tk-fusion_scene_operations.py:{config}/tk-multi-breakdown/fusion/scene_operations_extended.py:{config}/tk-multi-breakdown/tk-multi_scene_transfers.py'
  location: '@apps.tk-multi-breakdown.location'
# ---- Krita
settings.tk-multi-breakdown.krita:
  hook_get_version_number: '{config}/tk-multi-breakdown/get_publish_version_number.py'
  hook_scene_operations: '{engine}/tk-multi-breakdown/tk-krita_scene_operations.py:{config}/tk-multi-breakdown/tk-multi_scene_transfers.py'
  location: '@apps.tk-multi-breakdown.location'
frameworks:
  tk-framework-widget_v1.x.x:
    location:
      type: app_store
      name: tk-framework-widget
      version: v1.2.1
# ---- Substancepainter
settings.tk-multi-breakdown.substancepainter:
  hook_scene_operations: '{engine}/tk-multi-breakdown/tk-substancepainter_scene_operations.py:{config}/tk-multi-breakdown/tk-multi_scene_transfers.py'
  location: '@apps.tk-multi-breakdown.location'
