"use strict";


var META_SHOTGUN_PATH = "meta.shotgun.path";

function setNodeMetadata(nodeName, attrName, value)
{
    var visualAttrName = attrName;
    var idx = attrName.lastIndexOf(".");
    if (idx >= 0)
    {
      visualAttrName = attrName.substr(idx + 1);
    }

    var attr = node.getAttr(nodeName, 1.0, attrName);
    if(attr.keyword() == "")
    {
        if (node.createDynamicAttr(nodeName, "STRING", attrName, visualAttrName, false))
        {
          attr = node.getAttr(nodeName, 1.0, attrName);
        }

        if (attr.keyword() != "")
        {
            node.setTextAttr(nodeName, attrName, 1.0, value || visualAttrName);
        }
    }
    else
    {
        node.setTextAttr(nodeName, attrName, 1.0, value || visualAttrName);
    }
};

function custom_basename( filename )
{
    var pos = filename.lastIndexOf( ".");
    if( pos >= 0 )
        filename = filename.substr(0,pos);
    var name = filename.split("/");
    if( name.length > 0 )
        name = name[ name.length-1];
    return  name;
}


function custom_getUniqueColumnName( column_prefix )
{
    var suffix = 0;
    // finds if unique name for a column
    var column_name = column_prefix;
    while(suffix < 2000)
    {
            if(!column.type(column_name))
                    break;

            suffix = suffix + 1;
            column_name = column_prefix + "_" + suffix;
    }
    return column_name;
}


function custom_copyFile( srcFilename, dstFilename )
{
    var srcFile = new PermanentFile(srcFilename);
    var dstFile = new PermanentFile(dstFilename);
    srcFile.copy(dstFile);
}


function custom_dropFileInNewElement(
    root, filename, transparency, alignmentRule, vectorFormat, extension, scale_multiplier
)
{
    include("harmony_utility_functions.js");
    // var vectorFormat = null;
    // var extension = null;

    var pos = filename.lastIndexOf( "." );
    if( pos < 0 )
        return null;

    extension = filename.substr(pos+1).toLowerCase();

    if( extension == "jpeg" )
        extension = "jpg";
    if(  extension == "tvg" )
    {
        vectorFormat = "TVG"
        extension ="SCAN"; // element.add() will use this.
    }

    var name = custom_basename(filename);
    var elemId = element.add(name, "BW", scene.numberOfUnitsZ(), extension.toUpperCase(), vectorFormat);
    if ( elemId == -1 )
    {
        // hum, unknown file type most likely -- let's skip it.
        return null; // no read to add.
    }

    var uniqueColumnName = custom_getUniqueColumnName(name);
    column.add(uniqueColumnName , "DRAWING");
    column.setElementIdOfDrawing( uniqueColumnName, elemId );

    var good_position = get_good_scene_position(0, 100);
    var xPos = good_position.x;
    var yPos = good_position.y;

    var read = node.add(root, name, "READ", xPos, yPos, 0);

    // Add SG metadata (path)
    setNodeMetadata(read, META_SHOTGUN_PATH, filename)

    var transparencyAttr = node.getAttr(read, frame.current(), "READ_TRANSPARENCY");
    var transparencyAttr = node.getAttr(read, frame.current(), "READ_TRANSPARENCY");
    var opacityAttr = node.getAttr(read, frame.current(), "OPACITY");
    transparencyAttr.setValue(true);
    opacityAttr.setValue(transparency);

    var image_scale_x = node.getAttr(read, frame.current(), "scale.x");
    var image_scale_y = node.getAttr(read, frame.current(), "scale.y");
    image_scale_x.setValue(scale_multiplier);
    image_scale_y.setValue(scale_multiplier);

    var alignmentAttr = node.getAttr(read, frame.current(), "ALIGNMENT_RULE");
    alignmentAttr.setValue(alignmentRule);

    var transparencyModeAttr = node.getAttr(read, frame.current(), "applyMatteToColor");
    if (extension == "png")
        transparencyModeAttr.setValue(PNGTransparencyMode);
    if (extension == "tga")
        transparencyModeAttr.setValue(TGATransparencyMode);
    if (extension == "sgi")
        transparencyModeAttr.setValue(SGITransparencyMode);
    if (extension == "psd")
        transparencyModeAttr.setValue(FlatPSDTransparencyMode);

    node.linkAttr(read, "DRAWING.ELEMENT", uniqueColumnName);

    var timing = "1"; // we're creating drawing name '1'

    Drawing.create(elemId, timing, true); // create a drawing drawing, 'true' indicate that the file exists.
    var drawingFilePath = Drawing.filename(elemId, timing);   // get the actual path, in tmp folder.
    custom_copyFile( filename, drawingFilePath );

    //set exposure of all frames.
    var nframes = frame.numberOf();
    for( var i =1; i <= nframes; ++i)
    {
        column.setEntry(uniqueColumnName, 1, i, timing );
    }

    return read; // name of the new drawing layer.
}


function custom_import_image(
    filename, parent, transparency, alignmentRule,
    vectorFormat, extension, scale_multiplier
)
{
    if (parent === undefined)
        parent = node.root();

    // var transparency = null;
    // var alignmentRule = null;
    var read_node = custom_dropFileInNewElement(
        parent, filename, transparency, alignmentRule,
        vectorFormat, extension, scale_multiplier
    );
    MessageLog.trace("Created node: " + read_node);
    return read_node;
};


/*
Used to import resources into the scene.
Note that most of these methods are extracted from the example scripts.
*/

// var PNGTransparencyMode = 0; // Premultiplied wih Black
// var TGATransparencyMode = 0; // Premultiplied wih Black
// var SGITransparencyMode = 0; // Premultiplied wih Black
// var LayeredPSDTransparencyMode = 1; // Straight
// var FlatPSDTransparencyMode = 2; // Premultiplied wih White

var PNGTransparencyMode = 1; // Straight
var TGATransparencyMode = 1; // Straight
var SGITransparencyMode = 1; // Straight
var LayeredPSDTransparencyMode = 1; // Straight
var FlatPSDTransparencyMode = 1; // Straight


/*
transparency: BOOL
alignmentRule: INT or STR
    0: Left
    1: Right
    2: Top
    3: Bottom
    4: Center Fit
    5: Center Fill
    6: Center LR
    7: Center TB
    8: Stretch
    9: As Is
    10: Center First Page
vectorFormat: STR
    null
    "TVG"
extension: STR
    null
    "SCAN"
*/

/*
// var filename = "P:/HPTL/WorkArea/shots/HPTL999/HPTL999_A/shots/HPTL999_A_0010/Setup/Assembly/harmony/scenes/elements/bg0002_dsn_Render_master_master_v005/bg0002_dsn_Render_master_master_v005-1.png";
var filename = "C:/Users/<USER>/Desktop/tmp/bg0002_dsn_Render_master_master_v005.tvg";
var parent = "Top";
var transparency = true;
var alignmentRule = "As Is";
var vectorFormat = "TVG";
var extension = "SCAN";
var scale_multiplier = 8.32031;

custom_import_image(
    filename, parent, transparency, alignmentRule, vectorFormat, extension, scale_multiplier
);
*/
