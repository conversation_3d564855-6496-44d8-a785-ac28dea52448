submissionDir = callDeadlineCommand(["-GetRepositoryPath", "submission/Harmony/Main"])
MessageLog.trace("submissionDir: " + submissionDir);
scriptPath = trim(submissionDir) + "/SubmitHarmonyToDeadline.js";
// scriptPath = submissionDir + "/SubmitHarmonyToDeadline.js";
MessageLog.trace("scriptPath: " + scriptPath);

include( scriptPath );

function callDeadlineCommand( args ) {
	var commandLine = "";
	var deadlineBin = "";

	deadlineBin = System.getenv( "DEADLINE_PATH" )
	// MessageLog.trace("deadlineBin before trim: " + deadlineBin);
	// ensure we use forward slashes only
	deadlineBin = trim(deadlineBin);
	// MessageLog.trace("deadlineBin after trim: " + deadlineBin);
	if( ( deadlineBin === null || deadlineBin == "" ) && about.isMacArch() ) {
		var file = new File( "/Users/<USER>/Thinkbox/DEADLINE_PATH" );
		file.open(FileAccess.ReadOnly);
		deadlineBin = file.read();
		file.close();
	}

	if( deadlineBin === null || deadlineBin == "" ) {
		commandLine = "deadlinecommand";
	} else {
		// MessageLog.trace("deadlineBin after ifs: " + deadlineBin);
		// deadlineBin = trim(deadlineBin);
		commandLine = deadlineBin + "/deadlinecommand";
	}

	MessageLog.trace("commandLine: " + commandLine);
	/*
	commandArgs = [];
	commandArgIndex = 0;
	commandArgs[commandArgIndex++] = commandLine;
	for( arg in args)
	{
		commandArgs[commandArgIndex++] = args[arg];
	}
	MessageLog.trace("commandArgs: " + commandArgs);
	MessageLog.trace("about to execute subprocess");
	var status = Process.execute(commandArgs);
	MessageLog.trace("status: " + status);
	var mOut = Process.stdout;
	MessageLog.trace("mOut: " + mOut);
	*/
	commandArgs = [];
	commandArgIndex = 0;
	for( arg in args) {
		commandArgs[commandArgIndex++] = args[arg];
	}
	MessageLog.trace("commandArgs:\n" + JSON.stringify(commandArgs, null, 2));

	var qpro = new QProcess();
	qpro.start(commandLine, commandArgs);
	qpro.waitForFinished(-1);

	var mOut = new QTextStream(qpro.readAllStandardOutput()).readAll();
	MessageLog.trace("mOut: " + mOut);

	// var result = trim(mOut);
	var result = mOut;
	return result;
}

function trim(string_to_trim) {
	// MessageLog.trace("string_to_trim: " + string_to_trim);
	string_to_trim = string_to_trim.replace(/\\/g, "/");
	// MessageLog.trace("fixed string_to_trim: " + string_to_trim);
	return string_to_trim
		.replace(/\n/g, "")
		.replace(/\r/g, "")
		.replace(/^\s+/, "")
		.replace(/\s+$/, "");
}

function SubmitToDeadline() {
	if (typeof InnerSubmitToDeadline === 'undefined') {
		MessageBox.information( "Failed to import Deadline" );
	} else {
		// MessageLog.trace("about to call InnerSubmitToDeadline");
		InnerSubmitToDeadline( submissionDir );
	}
}
