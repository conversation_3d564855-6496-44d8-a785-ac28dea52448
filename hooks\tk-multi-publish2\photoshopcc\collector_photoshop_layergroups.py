#####################################################################################
#
# Copyright (c) 2020 Mighty Animation Studio and Metacube Technology Entertainment
#
# CONFIDENTIAL AND PROPRIETARY
#
# This work is provided as part of a colaboration and subject to the
# Mighty - Metacube Pipeline Code Agreement and your personal work agreement.
# By accessing, using, copying or modifying this work you indicate your
# agreement to the the specific terms of your contract. All rights
# not expressly granted therein are reserved by Mighty Animation Studio and
# Metacube Technology entertainment.
#
#####################################################################################

# import mimetypes
import os
import re
import json
import pprint
import unicodedata

import sgtk

HookBaseClass = sgtk.get_hook_baseclass()


class LayerGroupsCollector(HookBaseClass):
    """ """

    @property
    def settings(self):
        # grab any base class settings
        collector_settings = super(LayerGroupsCollector, self).settings or {}

        photoshop_settings = {
            "Primary Publish Template": {
                "type": "template",
                "default": None,
                "description": "A work file template required by this collector.",
            },
            "Work Template": {
                "type": "template",
                "default": None,
                "description": "A work file template required by this collector.",
            },
            "Publish Layer Group Image Template": {
                "type": "template",
                "default": None,
                "description": "A publish file template required by layergroups_image_item.",
            },
            "Publish Layer Group Proxy Image Template": {
                "type": "template",
                "default": None,
                "description": "A work file template required by layergroups_proxy_image_item.",
            },
            "Publish Image Review Template": {
                "type": "template",
                "default": None,
                "description": "A publish file template required by layergroups_image_item.",
            },
            "Publish Video Template": {
                "type": "template",
                "default": None,
                "description": "A work file template required by layergroups_proxy_image_item.",
            },
            "Image File Type": {
                "type": "string",
                "default": None,
                "description": "Published file type for group layer images",
            },
            "Proxy Image File Type": {
                "type": "string",
                "default": None,
                "description": "Published file type for group layer proxy images",
            },
            "Image Sequence Review Type": {
                "type": "string",
                "default": None,
                "description": "Published file type for group layer images",
            },
            "Video File Type": {
                "type": "string",
                "default": None,
                "description": "Published file type for group layer video (media review)",
            },
            "Publish Photoshop Proxy Template": {
                "type": "template",
                "default": None,
                "description": "A publish file template required by ps_proxy_image_item.",
            },
        }

        # update the base settings with these settings
        collector_settings.update(photoshop_settings)

        return collector_settings

    def process_current_session(self, settings, parent_item):
        """
        Analyzes the current scene open in a DCC and parents a subtree of items
        under the parent_item passed in.

        :param dict settings: Configured settings for this collector
        :param parent_item: Root item instance
        """

        # let the parent collector do its own job
        super(LayerGroupsCollector, self).process_current_session(settings, parent_item)


        self._collect_layergroups(parent_item, settings)

    def _collect_layergroups(self, item, settings):
        """
        Creates just one item for all the output layer group in the document root.
        Only layer groups which start with [Oo]utput[_- ] are taken in account.

        """

        engine = self.parent.engine
        photoshop = engine.adobe

        # ------------------------------------------------------------------------------
        # Get templates and file types from settings
        primary_publish_template = settings.get("Primary Publish Template").value
        primary_work_template = settings.get("Work Template").value
        layer_group_image_pub_template = settings.get(
            "Publish Layer Group Image Template"
        ).value
        layer_group_proxy_image_pub_template = settings.get(
            "Publish Layer Group Proxy Image Template"
        ).value
        layer_group_image_seq_pub_template = settings.get(
            "Publish Image Review Template"
        ).value
        video_pub_template = settings.get("Publish Video Template").value

        layer_group_image_file_type = settings.get("Image File Type").value
        layer_group_proxy_image_file_type = settings.get("Proxy Image File Type").value
        layer_group_image_seq_file_type = settings.get(
            "Image Sequence Review Type"
        ).value
        layer_group_video_file_type = settings.get("Video File Type").value
        ps_proxy_image_pub_template = settings.get(
            "Publish Photoshop Proxy Template"
        ).value

        # ------------------------------------------------------------------------------
        # Load value overrides framework
        valueoverrides = self.parent.engine.custom_frameworks.get(
            "mty-framework-valueoverrides"
        ) or self.load_framework("mty-framework-valueoverrides")

        secondary_outputs_on_local = False
        if valueoverrides:
            value_code = (
                "mty.engine.photoshop.multi-publish.process_secondary_outputs_on_local"
            )
            value_secondary_outputs_on_local = valueoverrides.get_value(value_code)
            self.parent.engine.logger.info(
                "photoshop.multi-publish.process_secondary_outputs_on_local: {}".format(
                value_secondary_outputs_on_local
                )
            )
            dict_secondary_outputs_on_local = json.loads(value_secondary_outputs_on_local)
            secondary_outputs_on_local = dict_secondary_outputs_on_local.get(
                "photoshop.layergroups", {}
            ).get(
                "value", True
            )


        self.parent.engine.logger.info(
            "secondary_outputs_on_local [photoshop.layergroups]: {}".format(secondary_outputs_on_local)
        )
        # default regex pattern
        # image_export_regex = re.compile(
        #     r"(?P<output_flag>[Oo]utput[_\- ])"
        #     r"(?P<name>.+)"
        # )
        image_export_regex = re.compile(
            r"(?P<output_flag>[Oo]utput[_\- :.,]+)"
            r"(?P<name>(?P<order>[0-9]{1,3})?.+)"
        )
        # self.parent.engine.logger.info(
        #     "default image_export_regex pattern: {}".format(image_export_regex)
        # )

        # Get regex pattern value override in case it exists
        if valueoverrides:
            # self.parent.engine.logger.info("CoreOverrides: {}".format(CoreOverrides))
            value_code = "mty.engine.photoshop.task.image_export.regex"
            data = valueoverrides.get_value(value_code)
            # self.parent.engine.logger.info("data: {}".format(data))
            if data:
                image_export_regex = json.loads(data)
                # self.parent.engine.logger.info(
                #     "override image_export_regex pattern: {}".format(image_export_regex)
                # )

        pattern = image_export_regex
        # self.parent.engine.logger.info(
        #     "image_export_regex pattern to be used: {}".format(pattern)
        # )

        # ------------------------------------------------------------------------------
        # Get document data
        PS_document = photoshop.app.activeDocument
        PS_document_path = PS_document.fullName.fsName or None

        # Get layers data
        layer_sets = PS_document.layerSets
        all_layers = PS_document.layers

        self.parent.engine.logger.info(
            "all_layers:\n{}".format(pprint.pformat(list(all_layers)))
        )
        self.parent.engine.logger.info(
            "layer_sets:\n{}".format(pprint.pformat(list(layer_sets)))
        )

        # the previous layer set flattens name is here just for compatibility reasons,
        # Could be removed later.
        previous_layer_set_flattens_name = "Output_flaten_group_DONOTDELETE"
        layer_set_flattens_name = "DO_NOT_DELETE_Output_flatten_group"
        # store the layer_set_flattens_name in the publisher item for later use in the
        # publish plugins
        item.properties["layer_set_flattens_name"] = layer_set_flattens_name

        # delete if exist output flatten layer group, including the previous name
        self.parent.engine.logger.info(
            "layer_sets number: {}".format(len(list(layer_sets)))
        )
        for layer_set in list(layer_sets):
            self.parent.engine.logger.info("-" * 40)
            self.parent.engine.logger.info(
                "working with layer: {}".format(layer_set.name)
            )
            if (
                layer_set.name == layer_set_flattens_name
                or layer_set.name == previous_layer_set_flattens_name
            ):
                self.parent.engine.logger.info("Found matching flatten layer")
                layer_set.allLocked = False
                layer_set.remove()
            else:
                self.parent.engine.logger.info("Not matching flatten layer, skipping...")
            self.parent.engine.logger.info(
                "layer_sets number during loop: {}".format(len(list(layer_sets)))
            )

        self.parent.engine.logger.info(
            "layer_sets number after loop: {}".format(len(list(layer_sets)))
        )
        # get the layers sets again to get the updated existing ones
        layer_sets = PS_document.layerSets

        self.parent.engine.logger.info("About to filter_output_layer_groups...")
        output_layer_sets = self.filter_output_layer_groups(layer_sets, pattern)
        self.parent.engine.logger.info("FInished filtering_output_layer_groups...")

        self.parent.engine.logger.info("About to add new layer set...")
        flatten_layer_set = layer_sets.add()
        flatten_layer_set.name = layer_set_flattens_name
        flatten_layer_set.visible = False

        # ------------------------------------------------------------------------------

        # Create master output layer group. This will be a flattened version of the
        # psd file, so even if there are no output layer groups, at least one output
        # layer group will be generated. This layer will always have the index 1
        # used later to create the sequence review and the media review publishes.


        # if not output_layer_sets:
        #     output_layer_sets = [master_output_layer_data]
        # else:
        #     output_layer_sets.insert(0, master_output_layer_data)

        self.parent.engine.logger.info(
            "output_layer_sets:\n{}".format(pprint.pformat(output_layer_sets))
        )


        # Create master output layer group. This will be a flattened version of the
        # psd file, so even if there are no output layer groups, at least one output
        # layer group will be generated. This layer will always have the index 1
        # used later to create the sequence review and the media review publishes.
        # layergroups_image_item.properties["layer_group_data"].update(
        #     master_output_layer_data
        # )
        layer_group_data = {}


        # Any output layer group will be added to the list in this section. As the
        # master layer (flattened version of the psd file) is generated automatically
        # in the previous section of the code, all additional groups will start from
        # index 2

        output_guides_regex = (
            r"(?P<output_flag>[Oo]utput)"
            r"(?P<name>(?P<separator>[_\- ]*)[Gg]uide[s]?(.+)?)$"
        )

        # define fallback pattern for layer group name and order
        fallback_pattern = re.compile(r"(?P<order>\d+)(?P<name_tail>.+)$")

        wrongly_named_layer_groups = []
        self.parent.engine.logger.info("About to loop over output_layer_sets...")
        for layer_set in output_layer_sets:
            # --------------------------------------------------------------------------
            # self.parent.engine.logger.info(
            #     "layer_set name:\n{}".format(pprint.pformat(layer_set.name))
            # )
            self.parent.engine.logger.info("-" * 80)
            match = re.match(pattern, layer_set.name)
            if match:
                self.parent.engine.logger.info(
                    "main pattern match groups: {}".format(match.groupdict())
                )
                layerset_name = match.groupdict().get("name")
                self.parent.engine.logger.debug(
                    "layerset_name: {}".format(layerset_name)
                )

                # check if the name is only a number
                if layerset_name.isdigit():
                    self.parent.engine.logger.warning(
                        "Found a wrongly named layer group: {}".format(layer_set.name)
                    )
                    wrongly_named_layer_groups.append(layer_set.name)
                    continue

                self.parent.engine.logger.info("About to use reformat method...")
                layerset_name = self.reformat_layerset_name(layerset_name)
                layerset_order = match.groupdict().get("order", None)
                if layerset_order:
                    self.parent.engine.logger.info(
                        "About to build the new layerset name. Current name: {}".format(
                            layerset_name
                        )
                    )
                    layerset_basename = layerset_name.replace(layerset_order, "")
                    layerset_order = layerset_order.zfill(3)
                    layerset_name = "{}{}".format(layerset_order, layerset_basename)
                    self.parent.engine.logger.info(
                        "New layerset name: {}".format(layerset_name)
                    )
                # ensure that the layer actually doesn't have an "order" number
                # in its name.
                if not layerset_order:
                    self.parent.engine.logger.warning(
                        "Couldn't find order number with the main regex pattern. "
                        "Using fallback pattern..."
                    )
                    # fallback in case the layer actually has an order number but the
                    # format of its name is not correct
                    fallback_match = re.match(fallback_pattern, layerset_name)
                    self.parent.engine.logger.debug(
                        "main match fallback_match: {}".format(fallback_match)
                    )

                    if fallback_match:
                        self.parent.engine.logger.info(
                            "Found fallback_match: {}".format(fallback_match.groupdict())
                        )
                        layerset_name = fallback_match.groupdict().get(
                            "name_tail", layerset_name
                        )

                        # check if the name is only a number
                        if layerset_name.isdigit():
                            self.parent.engine.logger.warning(
                                "Found a wrongly named layer group: {}".format(layer_set.name)
                            )
                            wrongly_named_layer_groups.append(layer_set.name)
                            continue

                        self.parent.engine.logger.info(
                            "About to use reformat method..."
                        )
                        layerset_name = self.reformat_layerset_name(layerset_name)

                        match_order = fallback_match.groupdict().get("order", None)
                        if match_order:
                            layerset_basename = layerset_name.replace(match_order, "")
                            layerset_order = match_order.zfill(3)
                            layerset_name = "{}{}".format(match_order, layerset_basename)
                        else:
                            layerset_order = "998"
                            layerset_name = "{}{}".format(layerset_order, layerset_name)

                        # finally, add the order number as a prefix of the reformatted
                        # name
                        layerset_name = "{}{}".format(layerset_order, layerset_name)
            else:
                self.parent.engine.logger.info(
                    "Couldn't match the main regex, trying the fall back regex..."
                )
                self.parent.engine.logger.info("About to use reformat method...")
                # fallback in case the main match fails
                layerset_name = self.reformat_layerset_name(layer_set.name)
                self.parent.engine.logger.debug(
                    "layerset_name: {}".format(layerset_name)
                )

                # check if the name is only a number
                if layerset_name.isdigit():
                    wrongly_named_layer_groups.append(layer_set.name)
                    continue

                layerset_order = "998"
                layerset_name = "{}{}".format(layerset_order, layerset_name)

                self.parent.engine.logger.debug(
                    "fallback layerset_name reformat: {}".format(layerset_name)
                )

                # last fallback in case the layer actually has an order number but the
                # format of its name is not correct
                fallback_match = re.match(fallback_pattern, layerset_name)
                self.parent.engine.logger.debug(
                    "fallback fallback_match: {}".format(fallback_match)
                )

                if fallback_match:
                    layerset_name = fallback_match.groupdict().get(
                        "name_tail", layerset_name
                    )

                    # check if the name is only a number
                    if layerset_name.isdigit():
                        wrongly_named_layer_groups.append(layer_set.name)
                        continue

                    match_order = fallback_match.groupdict().get("order", None)
                    self.parent.engine.logger.debug(
                        "fallback match_order: {}".format(match_order)
                    )
                    if match_order:
                        layerset_basename = layerset_name.replace(match_order, "")
                        layerset_order = match_order.zfill(3)
                        layerset_name = "{}{}".format(match_order, layerset_basename)
                    else:
                        layerset_order = "998"
                        layerset_name = "{}{}".format(layerset_order, layerset_name)


            # Handle the guides output layer
            match_guides = re.match(output_guides_regex, layer_set.name)
            if match_guides:
                layerset_order = "999"

            self.parent.engine.logger.info("layerset_order: {}".format(layerset_order))

            # Update layer_group_data dict with each output layer data. Only
            # add the layer group dict if both, layerset_name and layerset_order
            # exist, otherwise the layer is ignored.
            if layerset_name and layerset_order:
                # duplicate the layer set and move it to the end of the layer set
                duplicate_layer_set = layer_set.duplicate()
                flatten_layer_name = layer_set.name + "_flatten"
                duplicate_layer_set.name = flatten_layer_name
                flatten_layer = duplicate_layer_set.merge()
                flatten_layer = flatten_layer.move(
                    flatten_layer_set, photoshop.ElementPlacement.INSIDE
                )
                self.parent.engine.logger.info(
                    "layerset_order_master: {} LEN {}".format(
                        (list(flatten_layer_set.layers)),
                        len(list(flatten_layer_set.layers))
                    )
                )
                if len(list(flatten_layer_set.layers)) > 1:
                    flatten_layer_set.layers[0].move(
                        flatten_layer_set.layers[(len(list(flatten_layer_set.layers))-1)],
                        photoshop.ElementPlacement.PLACEAFTER
                    )

                layer_group_data.update(
                    {
                        layer_set.name: {
                            "layer_group": flatten_layer_set.name,
                            "original_name": flatten_layer_name,
                            "reformat_name": layerset_name,
                            "type": "Layer Group Image",
                            "index": None,
                            "order": layerset_order,
                        }
                    }
                )


        self.parent.engine.logger.debug(
            "layer_group_data after output_layer_sets iteration:\n{}".format(
                pprint.pformat(layer_group_data)
            )
        )

        master_layer_flatten = None
        if layer_group_data:
             # master
            duplica_master_layer_flatten = flatten_layer_set.duplicate()
            # delete guides when creating the master flatten
            for layer in duplica_master_layer_flatten.layers:
                match_guides = re.match(output_guides_regex, layer.name)
                if match_guides:
                    layer.remove()
                master_layer_flatten = duplica_master_layer_flatten.merge()
                master_layer_flatten.name = "output_masterlayer_flatten"

        else:
            # if the psd dont have any layer output, create the master flatten
            # of all layers

            # uplicate the document
            merged_doc = PS_document.duplicate("Fusion Temporal")

            # Flatten the document
            merged_doc.flatten()

            # get the first layer and rename
            merge_master_layer_flatten = merged_doc.artLayers[0]
            merge_master_layer_flatten.name = "output_masterlayer_flatten"

            # Duplicate the layer on original document
            master_layer_flatten = merge_master_layer_flatten.duplicate(PS_document)

            # close the fusion document
            merged_doc.close(photoshop.SaveOptions.DONOTSAVECHANGES)

        if master_layer_flatten:
            # merge layer and colocate in flatten layer set

            master_layer_flatten.move(
                flatten_layer_set, photoshop.ElementPlacement.INSIDE
            )
            flatten_layer_set.visible = False
            flatten_layer_set.allLocked = True

            master_output_layer_data = {
                "master": {
                    "layer_group": layer_set_flattens_name,
                    "original_name": "output_masterlayer_flatten",
                    "reformat_name": "master",
                    "type": "Layer Group Image",
                    "index": 1,
                    "order": "000",
                }
            }

            layer_group_data.update(master_output_layer_data)

        count_bakes_layers = flatten_layer_set.layers.length
        self.parent.engine.logger.info(
            "flatten_layer_set.layers.length: {}".format(count_bakes_layers)
        )

        if count_bakes_layers:
            # --------------------------------------------------------------------------
            # Create one image item for all of the output layer groups.  The data of
            # layer will be added to a "layer_group_data" dictionary

            layergroups_image_item = item.create_item(
                "photoshop.layergroups",
                "Photoshop Layer Group Images",
                "Layer Group Images",
            )

            # get the icon path to display for this item
            icon_path = os.path.join(
                self.disk_location, os.pardir, "icons", "image_publish.png"
            )
            layergroups_image_item.set_icon_from_path(icon_path)
            layergroups_image_item.properties["layer_group_data"] = {}

        if wrongly_named_layer_groups:
            self.parent.engine.logger.error(
                "Found wrongly named layer groups:\n{}".format(
                    pprint.pformat(wrongly_named_layer_groups)
                )
            )
            layergroups_image_item.properties["wrongly_named_layer_groups"] = wrongly_named_layer_groups


        if layer_group_data:
            sorted_list = sorted(layer_group_data.items(), key=lambda x: x[1]["order"])
            sorted_layer_group_data = dict(sorted_list)
        else:
            sorted_layer_group_data = {}

        self.parent.engine.logger.debug(
            "sorted_layer_group_data:\n{}".format(
                pprint.pformat(sorted_layer_group_data)
            )
        )

        # for k in sorted_layer_group_data.keys():
        #     self.parent.engine.logger.info(k)

        if not sorted_layer_group_data:
            self.parent.engine.logger.warning("No output layer groups found")
            return False

        i = 2
        for layer in sorted_layer_group_data.keys():
            if sorted_layer_group_data[layer]["reformat_name"] == "master":
                continue
            sorted_layer_group_data[layer]["index"] = i
            i += 1

        self.parent.engine.logger.debug(
            "sorted_layer_group_data:\n{}".format(
                pprint.pformat(sorted_layer_group_data)
            )
        )
        # get all the layers' visibility current state. It is useful later when we
        # export each layer as an individual file.
        all_layers_after_flatten = PS_document.layers
        all_layers_visibility_state = self.get_layers_current_state(
            all_layers_after_flatten
            )
        # set the all_layers_visibility_state and all_output_layer_groups
        # property in the root_item, used in publish_layer_group and
        # publish_layer_group_proxy

        root_item = self.get_root_item(item)
        root_item.properties["all_output_layer_groups"] = sorted_layer_group_data
        root_item.properties[
                "all_layers_visibility_state"
            ] = all_layers_visibility_state

        # self.parent.engine.logger.info(
        #     "all_layers_visibility_state:\n{}".format(
        #         pprint.pformat(all_layers_visibility_state)
        #     )
        # )


        # for l in sorted_layer_group_data.keys():
        #     self.parent.engine.logger.info(
        #         "sorted_layer_group_data[{}]:\n{}".format(
        #             l, pprint.pformat(sorted_layer_group_data[l])
        #         )
        #     )

        layergroups_image_item.properties["layer_group_data"].update(
            sorted_layer_group_data
        )

        # Create the dictionary that will be used in all sibling hooks. This
        # contains all of the relevant templates and file types, as well as
        # basic date of the PS document.
        if not layergroups_image_item.properties.get(
            "templates_and_file_types"
        ):
            layergroups_image_item.properties["templates_and_file_types"] = {
                "primary_publish_template_name": primary_publish_template,
                "primary_publish_template": engine.get_template_by_name(
                    primary_publish_template
                ),
                "primary_work_template_name": primary_work_template,
                "primary_work_template": engine.get_template_by_name(
                    primary_work_template
                ),
                "image_publish_template_name": layer_group_image_pub_template,
                "image_publish_template": engine.get_template_by_name(
                    layer_group_image_pub_template
                ),
                "image_file_type": layer_group_image_file_type,
                "proxy_image_publish_template_name": layer_group_proxy_image_pub_template,
                "proxy_image_publish_template": engine.get_template_by_name(
                    layer_group_proxy_image_pub_template
                ),
                "proxy_image_file_type": layer_group_proxy_image_file_type,
                "image_seq_publish_template_name": layer_group_image_seq_pub_template,
                "image_seq_publish_template": engine.get_template_by_name(
                    layer_group_image_seq_pub_template
                ),
                "image_seq_file_type": layer_group_image_seq_file_type,
                "video_publish_template_name": video_pub_template,
                "video_publish_template": engine.get_template_by_name(
                    video_pub_template
                ),
                "video_file_type": layer_group_video_file_type,
                "PS_document_path": PS_document_path,
                "PS_document": PS_document,
                "publish_photoshop_proxy_template_name": ps_proxy_image_pub_template,
                "publish_photoshop_proxy_template": engine.get_template_by_name(
                    ps_proxy_image_pub_template
                ),
            }

            # Save relevant stuff to the root item so it can be accessed by the sibling hooks.

            self.parent.engine.logger.info(
                "root_item.properties[all_output_layer_groups]:\n{}".format(
                    pprint.pformat(root_item.properties["all_output_layer_groups"])
                )
            )
            self.parent.engine.logger.info(
                "root_item.properties[all_layers_visibility_state]:\n{}".format(
                    pprint.pformat(root_item.properties["all_layers_visibility_state"])
                )
            )
        if not secondary_outputs_on_local:
            layergroups_image_item.properties["output_to_farm"] = True

            second_outputs_to_farm = root_item.properties.get(
                "secondary_outputs_to_farm", {}
            )

            dit_to_primary ={
                "photoshop.layergroups":{
                    "layer_group_data" : sorted_layer_group_data,
                    "templates_and_file_types": {
                        "primary_publish_template_name": primary_publish_template,
                        "primary_work_template_name": primary_work_template,
                        "image_publish_template_name": layer_group_image_pub_template,
                        "image_file_type": layer_group_image_file_type,
                        "proxy_image_publish_template_name": layer_group_proxy_image_pub_template,
                        "proxy_image_file_type": layer_group_proxy_image_file_type,
                        "image_seq_publish_template_name": layer_group_image_seq_pub_template,
                        "image_seq_file_type": layer_group_image_seq_file_type,
                        "video_publish_template_name": video_pub_template,
                        "video_file_type": layer_group_video_file_type,
                        "publish_photoshop_proxy_template_name": ps_proxy_image_pub_template,
                    }
                }
            }

            if wrongly_named_layer_groups:
                dit_to_primary["photoshop.layergroups"]["wrongly_named_layer_groups"] = wrongly_named_layer_groups

            second_outputs_to_farm.update(dit_to_primary)

            root_item.properties["secondary_outputs_to_farm"] = second_outputs_to_farm

    def filter_output_layer_groups(self, layer_sets, pattern):
        """
        Filter layer sets if their names start with [Oo]utput[_- ]
        """
        result = []

        for layer_set in layer_sets:
            if not layer_set.visible:
                continue
            sublayers = list(layer_set.layers)
            match = re.match(pattern, layer_set.name)
            if match and sublayers:
                self.parent.engine.logger.info("layer_set.name: {}".format(layer_set.name))
                self.parent.engine.logger.info(match.groupdict())

                result.append(layer_set)

        return result

    def get_layers_current_state(self, all_layers):
        layers_current_state = {}

        i = 0
        for layer in all_layers:
            layers_current_state[layer.name] = {
                "visible": layer.visible,
                "layer_type": layer.typename,
                "index": i,
                "is_locked": layer.allLocked,
                "layer_obj": layer,
            }
            if layer.typename == "ArtLayer":
                layers_current_state[layer.name].update(
                    {"is_background": layer.isBackgroundLayer}
                )
            else:
                layers_current_state[layer.name].update({"is_background": False})
            i += 1

        return layers_current_state

    def reformat_layerset_name(self, name):
        """
        Split name with [_- ], capitalize each part, and finally join those parts.
        The intention is to reformat the original name using camel case, so
        'example layer_name-with spaces' becomes 'ExampleLayerNameWithSpaces'
        """

        def replace_non_ascii(text):
            """Replaces non-ASCII characters with their closest ASCII equivalents."""
            nfkd_form = unicodedata.normalize('NFKD', text)
            only_ascii = nfkd_form.encode('ASCII', 'ignore').decode('ASCII')
            return only_ascii

        self.parent.engine.logger.info("Starting name reformatting...")
        self.parent.engine.logger.info("input name: {}".format(name))

        name = name.strip()
        self.parent.engine.logger.info("name after strip: {}".format(name))

        name_split = re.split(r" |_|-", name)
        self.parent.engine.logger.info("name_split: {}".format(name_split))

        # replace all forward or backward slashes with hyphens using regex
        name_split = [re.sub(r"[/\\]", "-", name) for name in name_split]
        self.parent.engine.logger.info(
            "name_split after slashes substitution: {}".format(name_split)
        )

        # clean string
        name_split = [replace_non_ascii(name) for name in name_split if name]
        self.parent.engine.logger.info(
            "name_split after string cleanup: {}".format(name_split)
        )

        list_of_capitalized_words = []
        for word in name_split:
            if word:
                if word[0].isalpha():
                    try:
                        word = "{}{}".format(word[0].upper(), word[1:])
                    except:
                        pass
                list_of_capitalized_words.append(word)

        if list_of_capitalized_words:
            capitalized_name = "".join(list_of_capitalized_words)
            # remove all remaining spaces (if any)
            capitalized_name = re.sub("\s+", "", capitalized_name)
            return capitalized_name
        else:
            # remove all remaining spaces (if any)
            name = re.sub("\s+", "", name)
            return name

    def get_root_item(self, item):
        if not item.is_root:
            root = self.get_root_item(item.parent)
        else:
            root = item

        return root
